<?php
/**
 * Local PHPStan test script
 * 
 * This script tests PHPStan functionality locally to debug issues
 * Run with: php test-phpstan-local.php
 */

// Include the shared PHPStan utilities
require_once '.github/scripts/php/phpstan_shared.php';

echo "=== PHPStan Local Test ===\n";

// Test file with known issues
$testFile = 'application/controllers/ComplaintController.php';

if (!file_exists($testFile)) {
    echo "ERROR: Test file not found: $testFile\n";
    exit(1);
}

echo "Testing file: $testFile\n";
echo "File size: " . filesize($testFile) . " bytes\n";

// Check if PHPStan is available
$phpstanVersion = shell_exec('phpstan --version 2>/dev/null');
if (!$phpstanVersion) {
    echo "ERROR: PHPStan not found. Please install it first:\n";
    echo "composer global require phpstan/phpstan:^1.0\n";
    exit(1);
}

echo "PHPStan version: " . trim($phpstanVersion) . "\n";

// Run PHPStan analysis
echo "\n=== Running PHPStan Analysis ===\n";
$errors = runPhpstan([$testFile], 'test');

echo "\n=== Results ===\n";
echo "Total errors found: " . count($errors) . "\n";

if (count($errors) > 0) {
    echo "\nErrors:\n";
    foreach ($errors as $i => $error) {
        echo ($i + 1) . ". $error\n";
    }
} else {
    echo "No errors found.\n";
}

// Test specific patterns that should be caught
echo "\n=== Pattern Analysis ===\n";

$content = file_get_contents($testFile);

// Check for PHP 7+ syntax
$php7Patterns = [
    '/\?\?/' => 'Null coalescing operator (??)',
    '/<=>/' => 'Spaceship operator (<=>)',
    '/\?\s*:/' => 'Ternary shorthand (?:)',
];

foreach ($php7Patterns as $pattern => $description) {
    if (preg_match($pattern, $content)) {
        echo "✓ Found: $description\n";
    }
}

// Check for undefined function calls
if (preg_match('/nonExistentFunction/', $content)) {
    echo "✓ Found: Call to nonExistentFunction()\n";
}

// Check for complexity indicators
$complexityIndicators = [
    '/if\s*\(.*if\s*\(.*if\s*\(/' => 'Deeply nested if statements',
    '/testBrokenFunction/' => 'Test function with intentional issues',
];

foreach ($complexityIndicators as $pattern => $description) {
    if (preg_match($pattern, $content)) {
        echo "✓ Found: $description\n";
    }
}

echo "\n=== Configuration Test ===\n";

// Test PHPStan configuration
$configFile = createPhpstanConfig();
echo "Generated config file: $configFile\n";
echo "Config content:\n";
echo file_get_contents($configFile);
unlink($configFile);

echo "\n=== Manual PHPStan Command Test ===\n";

// Create a temporary config and run PHPStan manually
$configFile = createPhpstanConfig();
$cmd = "phpstan analyse --configuration=" . escapeshellarg($configFile) . " --error-format=json --no-progress " . escapeshellarg($testFile);
echo "Command: $cmd\n";

$output = shell_exec($cmd . " 2>&1");
echo "Raw output:\n";
echo $output;

unlink($configFile);

echo "\n=== Test Complete ===\n";
