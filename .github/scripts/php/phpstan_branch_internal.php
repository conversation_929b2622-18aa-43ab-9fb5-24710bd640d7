<?php
/**
 * PHPStan-based branch internal breaking changes detector
 * 
 * Compares PHPStan analysis results between consecutive commits in the same branch
 * to detect regressions where later commits break earlier commits.
 * Also performs PHP 5.5 compatibility checks.
 *
 * Usage: php phpstan_branch_internal.php <base_ref> <file1> [<file2> ...]
 */

if ($argc < 3) {
    fwrite(STDERR, "Usage: php phpstan_branch_internal.php <base_ref> <file...>\n");
    exit(2);
}

$baseRef = $argv[1];  // Typically HEAD~1
$files = array_slice($argv, 2);

// ---- Load shared PHPStan utilities ----
require_once __DIR__ . '/phpstan_shared.php';

// Create reports directory
if (!is_dir('reports')) {
    mkdir('reports', 0755, true);
}

$totalIssues = 0;
$reportLines = [];

echo "Running PHPStan branch internal breaking changes check...\n";
echo "Base reference: $baseRef (previous commit)\n";
echo "Current reference: HEAD (current commit)\n";
echo "Files to check: " . implode(', ', $files) . "\n\n";

// Create temporary directory for base version analysis
$tempDir = sys_get_temp_dir() . '/phpstan_branch_internal_' . uniqid();
mkdir($tempDir, 0755, true);

try {
    // Determine base ref (handle both local and origin refs)
    $testCmd = 'git rev-parse --verify --quiet "origin/' . $baseRef . '"';
    $resolvedBaseRef = trim(shell_exec($testCmd)) ? 'origin/'.$baseRef : $baseRef;

    // Also try without origin prefix if base_ref doesn't look like a branch name
    if (strpos($baseRef, 'HEAD') !== false || preg_match('/^[0-9a-f]{7,40}$/i', $baseRef)) {
        // It's a commit SHA or HEAD reference, use as-is
        $resolvedBaseRef = $baseRef;
    }

    echo "DEBUG: Base ref resolution:\n";
    echo "  Input base_ref: $baseRef\n";
    echo "  Resolved base_ref: $resolvedBaseRef\n\n";

    foreach ($files as $path) {
        echo "Analyzing: $path\n";

        $headExists = is_file($path);
        $gitShowCmd = 'git show ' . escapeshellarg($resolvedBaseRef . ':' . $path) . ' 2>&1';
        echo "  DEBUG: Git show command: $gitShowCmd\n";

        $baseContent = shell_exec($gitShowCmd);
        $contentLength = strlen($baseContent !== null ? $baseContent : '');
        echo "  DEBUG: Base content length: $contentLength bytes\n";

        if ($contentLength > 0 && strpos($baseContent, 'fatal:') === false) {
            echo "  DEBUG: File exists in base commit\n";
        } else {
            echo "  DEBUG: File does NOT exist in base commit (new file or error)\n";
            if ($baseContent) {
                echo "  DEBUG: Git error: " . substr($baseContent, 0, 200) . "\n";
            }
        }

        if ($baseContent === null || $baseContent === '' || strpos($baseContent, 'fatal:') !== false) {
            // File didn't exist in base commit, check if it exists now
            if ($headExists) {
                echo "  → New file since $baseRef, checking for basic errors...\n";
                $reportLines[] = "ANALYSIS: $path (NEW FILE)";
                $reportLines[] = "Status: File does not exist in base commit ($baseRef)";
                $reportLines[] = "Action: Running PHPStan analysis on current version";
                $reportLines[] = "";
                
                $headErrors = runPhpstan([$path], 'head');
                if (!empty($headErrors)) {
                    $reportLines[] = "❌ ERRORS in new file $path:";
                    foreach ($headErrors as $error) {
                        $reportLines[] = "  - " . $error;
                        $totalIssues++;
                    }
                } else {
                    $reportLines[] = "✅ No PHPStan errors found in new file";
                }
                $reportLines[] = str_repeat("=", 60);
                $reportLines[] = "";
            }
            continue;
        }
        
        if (!$headExists) {
            // File was deleted - note this but don't count as regression
            $reportLines[] = "ANALYSIS: $path (DELETED FILE)";
            $reportLines[] = "Status: File was deleted in current commit";
            $reportLines[] = str_repeat("=", 60);
            $reportLines[] = "";
            continue;
        }
        
        // File exists in both versions - run detailed comparison
        $reportLines[] = "ANALYSIS: $path (MODIFIED FILE)";
        $reportLines[] = "Status: File exists in both base ($baseRef) and current (HEAD)";
        $reportLines[] = "Action: Running comparative PHPStan analysis";
        $reportLines[] = "";
        
        // Create temporary file for base version
        $baseTempFile = $tempDir . '/' . basename($path, '.php') . '_base.php';
        file_put_contents($baseTempFile, $baseContent);
        
        // Run PHPStan on both versions
        echo "  → Analyzing base version...\n";
        $baseErrors = runPhpstan([$baseTempFile], 'base');
        
        echo "  → Analyzing current version...\n";
        $headErrors = runPhpstan([$path], 'head');
        
        // Report analysis results
        $reportLines[] = "Base version analysis: " . count($baseErrors) . " PHPStan errors";
        $reportLines[] = "Current version analysis: " . count($headErrors) . " PHPStan errors";
        $reportLines[] = "";
        
        // Check for regressions
        $regressions = findRegressions($baseErrors, $headErrors, $path, basename($baseTempFile));
        
        // Add regressions to total count
        $totalIssues += count($regressions);
        
        // Add regressions to report
        if (!empty($regressions)) {
            $reportLines[] = "❌ REGRESSIONS in $path:";
            foreach ($regressions as $error) {
                $reportLines[] = "  - " . $error;
            }
            echo "  → ❌ Found " . count($regressions) . " regression(s)\n";
        } else {
            echo "  → ✅ No regressions detected\n";
        }
        
        // Also check for improvements (errors that were fixed)
        $improvements = findImprovements($baseErrors, $headErrors, $path, $baseTempFile);
        if (!empty($improvements)) {
            $reportLines[] = "";
            $reportLines[] = "✅ IMPROVEMENTS in $path (errors fixed):";
            foreach ($improvements as $error) {
                $reportLines[] = "  + " . $error;
            }
            echo "  → ✅ Fixed " . count($improvements) . " error(s)\n";
        }
        
        $reportLines[] = str_repeat("=", 60);
        $reportLines[] = "";
        
        // Clean up temp file
        unlink($baseTempFile);
    }
    
} finally {
    // Clean up temp directory
    if (is_dir($tempDir)) {
        array_map('unlink', glob("$tempDir/*"));
        rmdir($tempDir);
    }
}

// Create comprehensive report header
$reportHeader = [
    "PHP Branch Internal Breaking Changes Analysis Report",
    str_repeat("=", 50),
    "Generated: " . date('Y-m-d H:i:s'),
    "Base reference: $baseRef",
    "PHPStan version: " . (shell_exec('phpstan --version 2>/dev/null') ?: 'Unknown'),
    "Files analyzed: " . count($files),
    "",
    "SUMMARY:",
    "========",
    "Total regressions found: $totalIssues",
    ""
];

if ($totalIssues > 0) {
    $reportHeader[] = "❌ ANALYSIS RESULT: REGRESSIONS DETECTED";
    $reportHeader[] = "ACTION REQUIRED: Review and fix the regressions listed below";
} else {
    $reportHeader[] = "✅ ANALYSIS RESULT: NO REGRESSIONS DETECTED"; 
    $reportHeader[] = "Your latest commit doesn't introduce any new errors";
}

$reportHeader[] = "";
$reportHeader[] = "DETAILED ANALYSIS:";
$reportHeader[] = str_repeat("=", 18);
$reportHeader[] = "";

// Combine header with detailed analysis
$fullReport = array_merge($reportHeader, $reportLines);

// Add footer with next steps
$reportFooter = [
    "",
    "NEXT STEPS:",
    "===========",
];

if ($totalIssues > 0) {
    $reportFooter[] = "1. Review each regression listed above";
    $reportFooter[] = "2. Fix the new errors introduced in your latest commit";
    $reportFooter[] = "3. Test your changes locally with PHPStan";
    $reportFooter[] = "4. Push again to re-run this analysis";
    $reportFooter[] = "";
    $reportFooter[] = "TIP: Focus on new errors in your latest commit.";
    $reportFooter[] = "These are regressions compared to your previous commit.";
} else {
    $reportFooter[] = "1. Your latest commit looks good from a regression perspective";
    $reportFooter[] = "2. Continue with your development";
    $reportFooter[] = "3. Proceed with code review and merge";
}

$fullReport = array_merge($fullReport, $reportFooter);

// Write report
$reportContent = implode("\n", $fullReport);
file_put_contents('reports/phpstan-branch-internal.txt', $reportContent);

// Also create JSON report for machine consumption
$jsonReport = [
    'base_ref' => $baseRef,
    'head_ref' => 'HEAD',
    'total_regressions' => $totalIssues,
    'files_checked' => $files,
    'timestamp' => date('Y-m-d H:i:s'),
    'analysis_type' => 'branch_internal'
];
file_put_contents('reports/phpstan-branch-internal.json', json_encode($jsonReport, JSON_PRETTY_PRINT));

if ($totalIssues > 0) {
    echo "\n" . $reportContent . "\n";
    echo "\nFound $totalIssues regression(s) in your latest commit.\n";
    echo "Your changes introduced new PHPStan errors that weren't present in the previous commit.\n";
    exit(1);
}

echo "\nNo branch internal breaking changes detected.\n";
echo "Your latest commit doesn't introduce any regressions.\n";
exit(0);

// ---- Helper functions ----

/**
 * Find regressions: errors in HEAD that weren't in base
 */
function findRegressions($baseErrors, $headErrors, $currentFile, $baseFile) {
    $regressions = [];
    
    // Normalize errors to ignore file path differences
    $normalizedBaseErrors = array_map('normalizeError', $baseErrors);
    
    foreach ($headErrors as $headError) {
        $normalizedHeadError = normalizeError($headError);
        if (!in_array($normalizedHeadError, $normalizedBaseErrors)) {
            $regressions[] = $headError;
        }
    }
    
    return $regressions;
}

/**
 * Find improvements: errors in base that are fixed in HEAD
 */
function findImprovements($baseErrors, $headErrors, $currentFile, $baseFile) {
    $improvements = [];
    
    // Normalize errors to ignore file path differences
    $normalizedHeadErrors = array_map('normalizeError', $headErrors);
    
    foreach ($baseErrors as $baseError) {
        $normalizedBaseError = normalizeError($baseError);
        if (!in_array($normalizedBaseError, $normalizedHeadErrors)) {
            $improvements[] = $baseError;
        }
    }
    
    return $improvements;
}

/**
 * Normalize error message to ignore file path differences
 */
function normalizeError($error) {
    // Remove specific file paths and focus on line numbers and error content
    $error = preg_replace('/\/[^:]+\.php:/', '', $error);
    $error = preg_replace('/File:.*/', '', $error);
    return trim($error);
}