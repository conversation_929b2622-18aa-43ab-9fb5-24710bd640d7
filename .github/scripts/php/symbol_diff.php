<?php
/**
 * Simple symbol integrity checker for PHP 5.5-era code.
 * 
 * Compares base and head versions of each changed file and reports
 * removed classes, functions, methods, and class properties.
 *
 * Usage: php symbol_diff.php <base_ref> <file1> [<file2> ...]
 */

if ($argc < 3) {
    fwrite(STDERR, "Usage: php symbol_diff.php <base_ref> <file...>\n");
    exit(2);
}

$baseRef = $argv[1];
$files = array_slice($argv, 2);

$totalIssues = 0;
$reportLines = [];

foreach ($files as $path) {
    $headExists = is_file($path);
    $baseBlob = trim(shell_exec('git rev-parse --verify --quiet "origin/' . escapeshellarg($baseRef) . '"')) ? 'origin/'.$baseRef : $baseRef;
    $baseContent = @shell_exec('git show '.escapeshellarg($baseBlob.':'.$path).' 2>/dev/null');
    if ($baseContent === null || $baseContent === '') {
        // New file or not present in base; nothing to compare for removals
        continue;
    }
    if (!$headExists) {
        // File was deleted; everything was removed, flag high-level notice
        $reportLines[] = "REMOVED file: $path (all symbols)";
        $totalIssues++;
        continue;
    }

    $removed = array_diff_symbols(parse_symbols($baseContent), parse_symbols(file_get_contents($path)));
    foreach ($removed as $kind => $names) {
        foreach ($names as $name) {
            $reportLines[] = sprintf('REMOVED %s: %s (in %s)', $kind, $name, $path);
            $totalIssues++;
        }
    }
}

if ($totalIssues > 0) {
    echo implode("\n", $reportLines), "\n";
    exit(1);
}
echo "No removed symbols detected.\n";
exit(0);

// ---- helpers ----

function parse_symbols($code) {
    $tokens = token_get_all($code);
    $ns = '';
    $symbols = [
        'class' => [],
        'function' => [],
        'method' => [],
        'property' => [],
    ];

    $i = 0; $count = count($tokens);
    $classStack = []; // stack of fully-qualified class names
    while ($i < $count) {
        $t = $tokens[$i];

        // namespace Foo\Bar;
        if (is_array($t) && $t[0] === T_NAMESPACE) {
            $i++;
            $ns = read_namespace($tokens, $i);
            continue;
        }

        // class/interface/trait
        if (is_array($t) && in_array($t[0], [T_CLASS, T_INTERFACE, T_TRAIT], true)) {
            // skip "class" tokens if they're anonymous classes (PHP 7+), not applicable to PHP 5.5
            $name = read_identifier($tokens, ++$i);
            if ($name !== null) {
                $fqcn = ltrim($ns.'\\'.$name, '\\');
                $symbols['class'][] = $fqcn;
                $classStack[] = $fqcn;
            }
            // advance to next '{'
            while ($i < $count && (!is_string($tokens[$i]) || $tokens[$i] !== '{')) $i++;
            $i++; // consume '{'
            // Walk until matching '}', collecting methods/properties
            $depth = 1;
            $propBuffer = []; $visibilitySeen = false;
            while ($i < $count && $depth > 0) {
                $tk = $tokens[$i];

                if (is_string($tk)) {
                    if ($tk === '{') $depth++;
                    if ($tk === '}') $depth--;
                    $i++; continue;
                }

                // Visibility or 'var' starts a property declaration context
                if (in_array($tk[0], [T_PUBLIC, T_PROTECTED, T_PRIVATE, T_VAR], true)) {
                    $visibilitySeen = true; $i++; continue;
                }

                // Collect property variables after visibility/var, up to ';'
                if ($visibilitySeen && $tk[0] === T_VARIABLE) {
                    $propBuffer[] = $tk[1]; $i++; continue;
                }
                if ($visibilitySeen && is_string($tk) && $tk === ';') {
                    foreach ($propBuffer as $pv) {
                        $symbols['property'][] = end($classStack).'::'.$pv;
                    }
                    $propBuffer = []; $visibilitySeen = false;
                    $i++; continue;
                }

                // Methods
                if ($tk[0] === T_FUNCTION) {
                    // Skip possible & reference
                    $name = read_identifier($tokens, ++$i);
                    if ($name !== null) {
                        $symbols['method'][] = end($classStack).'::'.$name;
                    }
                    continue;
                }

                $i++;
            }
            array_pop($classStack);
            continue;
        }

        // global function
        if (is_array($t) && $t[0] === T_FUNCTION) {
            $name = read_identifier($tokens, ++$i);
            if ($name !== null) {
                $symbols['function'][] = ltrim($ns.'\\'.$name, '\\');
            }
            continue;
        }

        $i++;
    }

    // Deduplicate
    foreach ($symbols as $k => $arr) {
        $symbols[$k] = array_values(array_unique($arr));
    }
    return $symbols;
}

function read_namespace(&$tokens, &$i) {
    $parts = [];
    $count = count($tokens);
    // Collect T_STRING and T_NS_SEPARATOR until ';' or '{'
    while ($i < $count) {
        $t = $tokens[$i];
        if (is_array($t) && ($t[0] === T_STRING || $t[0] === T_NS_SEPARATOR)) {
            $parts[] = $t[1]; $i++; continue;
        }
        if (is_string($t) && ($t === ';' || $t === '{')) {
            // stop at end of namespace decl
            break;
        }
        if (is_array($t) && $t[0] === T_WHITESPACE) { $i++; continue; }
        break;
    }
    return implode('', $parts);
}

function read_identifier(&$tokens, &$i) {
    $count = count($tokens);
    // Skip whitespace and '&'
    while ($i < $count && (
        (is_array($tokens[$i]) && $tokens[$i][0] === T_WHITESPACE)
        || (is_string($tokens[$i]) && $tokens[$i] === '&')
    )) { $i++; }
    if ($i < $count && is_array($tokens[$i]) && $tokens[$i][0] === T_STRING) {
        return $tokens[$i++][1];
    }
    return null; // anonymous or unreachable
}

function array_diff_symbols($base, $head) {
    $out = [];
    foreach ($base as $kind => $items) {
        $removed = array_values(array_diff($items, isset($head[$kind]) ? $head[$kind] : []));
        if ($removed) $out[$kind] = $removed;
    }
    return $out;
}