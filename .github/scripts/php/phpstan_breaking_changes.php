<?php
/**
 * PHPStan-based breaking changes detector
 * 
 * Compares PHPStan analysis results between base and head commits
 * to detect breaking changes like missing interfaces, classes, methods, etc.
 * Also performs PHP 5.5 compatibility checks.
 *
 * Usage: php phpstan_breaking_changes.php <base_ref> <file1> [<file2> ...]
 */

if ($argc < 3) {
    fwrite(STDERR, "Usage: php phpstan_breaking_changes.php <base_ref> <file...>\n");
    exit(2);
}

$baseRef = $argv[1];
$files = array_slice($argv, 2);

// ---- Load shared PHPStan utilities ----
require_once __DIR__ . '/phpstan_shared.php';

// Create reports directory
if (!is_dir('reports')) {
    mkdir('reports', 0755, true);
}

$totalIssues = 0;
$reportLines = [];

echo "Running PHPStan breaking changes check...\n";
echo "Base reference: $baseRef\n";
echo "Target compatibility: PHP 5.5.9\n";
echo "PHPStan level: 1\n";
echo "Files to check: " . implode(', ', $files) . "\n\n";

// Create temporary directory for base version analysis
$tempDir = sys_get_temp_dir() . '/phpstan_breaking_changes_' . uniqid();
mkdir($tempDir, 0755, true);

try {
    // Determine base ref (handle both local and origin refs)
    $testCmd = 'git rev-parse --verify --quiet "origin/' . $baseRef . '"';
    $baseBlob = trim(shell_exec($testCmd)) ? 'origin/'.$baseRef : $baseRef;

    echo "DEBUG: Base ref resolution:\n";
    echo "  Input base_ref: $baseRef\n";
    echo "  Resolved baseBlob: $baseBlob\n\n";

    foreach ($files as $path) {
        echo "Analyzing: $path\n";

        $headExists = is_file($path);
        $gitShowCmd = 'git show ' . escapeshellarg($baseBlob . ':' . $path) . ' 2>&1';
        echo "  DEBUG: Git show command: $gitShowCmd\n";

        $baseContent = shell_exec($gitShowCmd);
        $contentLength = strlen($baseContent !== null ? $baseContent : '');
        echo "  DEBUG: Base content length: $contentLength bytes\n";

        if ($contentLength > 0 && strpos($baseContent, 'fatal:') === false) {
            echo "  DEBUG: File exists in base branch\n";
        } else {
            echo "  DEBUG: File does NOT exist in base branch (new file or error)\n";
            if ($baseContent) {
                echo "  DEBUG: Git error: " . substr($baseContent, 0, 200) . "\n";
            }
        }

        if ($baseContent === null || $baseContent === '' || strpos($baseContent, 'fatal:') !== false) {
            // New file - run PHPStan on current version only to check for obvious errors
            if ($headExists) {
                echo "  → New file, checking for basic errors...\n";
                $reportLines[] = "ANALYSIS: $path (NEW FILE)";
                $reportLines[] = "Status: File does not exist in base branch ($baseRef)";
                $reportLines[] = "Action: Running PHPStan analysis on current version";
                $reportLines[] = "";
                
                $headErrors = runPhpstan([$path], 'head');
                if (!empty($headErrors)) {
                    $reportLines[] = "❌ NEW FILE ERRORS in $path:";
                    foreach ($headErrors as $error) {
                        $reportLines[] = "  - " . $error;
                        $totalIssues++;
                    }
                } else {
                    $reportLines[] = "✅ No PHPStan errors found in new file";
                }
                $reportLines[] = str_repeat("=", 60);
                $reportLines[] = "";
            }
            continue;
        }
        
        if (!$headExists) {
            // File was deleted - major breaking change
            $reportLines[] = "ANALYSIS: $path (DELETED FILE)";
            $reportLines[] = "❌ BREAKING CHANGE: File was deleted - all symbols removed";
            $reportLines[] = "Impact: Any code depending on this file will break";
            $reportLines[] = str_repeat("=", 60);
            $reportLines[] = "";
            $totalIssues++;
            continue;
        }
        
        // File exists in both versions - run detailed comparison
        $reportLines[] = "ANALYSIS: $path (MODIFIED FILE)";
        $reportLines[] = "Status: File exists in both base ($baseRef) and current (HEAD)";
        $reportLines[] = "Action: Running comparative PHPStan analysis";
        $reportLines[] = "";
        
        // Create temporary file for base version
        $baseTempFile = $tempDir . '/' . basename($path, '.php') . '_base.php';
        file_put_contents($baseTempFile, $baseContent);
        
        // Run PHPStan on both versions
        echo "  → Analyzing base version...\n";
        $baseErrors = runPhpstan([$baseTempFile], 'base');
        
        echo "  → Analyzing current version...\n";
        $headErrors = runPhpstan([$path], 'head');
        
        // Report analysis results
        $reportLines[] = "Base version analysis: " . count($baseErrors) . " PHPStan errors";
        $reportLines[] = "Current version analysis: " . count($headErrors) . " PHPStan errors";
        $reportLines[] = "";
        
        // Find new errors (breaking changes)
        $newErrors = findNewErrors($baseErrors, $headErrors, $path, basename($baseTempFile));
        
        if (!empty($newErrors)) {
            $reportLines[] = "❌ BREAKING CHANGES DETECTED:";
            foreach ($newErrors as $error) {
                $reportLines[] = "  - " . $error;
                $totalIssues++;
            }
        } else {
            $reportLines[] = "✅ No breaking changes detected";
        }
        
        // Show improvements (errors that were fixed)
        $fixedErrors = array_diff($baseErrors, $headErrors);
        if (!empty($fixedErrors)) {
            $reportLines[] = "";
            $reportLines[] = "✅ IMPROVEMENTS (errors fixed):";
            foreach (array_slice($fixedErrors, 0, 5) as $error) {
                $reportLines[] = "  + " . $error;
            }
            if (count($fixedErrors) > 5) {
                $reportLines[] = "  + ... and " . (count($fixedErrors) - 5) . " more";
            }
        }
        
        $reportLines[] = str_repeat("=", 60);
        $reportLines[] = "";
        
        // Clean up temp file
        unlink($baseTempFile);
    }
    
} finally {
    // Clean up temp directory
    if (is_dir($tempDir)) {
        array_map('unlink', glob("$tempDir/*"));
        rmdir($tempDir);
    }
}

// Create comprehensive report header
$reportHeader = [
    "PHP Breaking Changes Analysis Report",
    str_repeat("=", 40),
    "Generated: " . date('Y-m-d H:i:s'),
    "Base reference: $baseRef",
    "PHPStan version: " . (shell_exec('phpstan --version 2>/dev/null') ?: 'Unknown'),
    "Target compatibility: PHP 5.5.9",
    "Files analyzed: " . count($files),
    "",
    "SUMMARY:",
    "========",
    "Total issues found: $totalIssues",
    ""
];

if ($totalIssues > 0) {
    $reportHeader[] = "❌ ANALYSIS RESULT: BREAKING CHANGES DETECTED";
    $reportHeader[] = "ACTION REQUIRED: Review and fix the issues listed below";
} else {
    $reportHeader[] = "✅ ANALYSIS RESULT: NO BREAKING CHANGES DETECTED"; 
    $reportHeader[] = "Your changes are compatible with the base branch";
}

$reportHeader[] = "";
$reportHeader[] = "DETAILED ANALYSIS:";
$reportHeader[] = str_repeat("=", 18);
$reportHeader[] = "";

// Combine header with detailed analysis
$fullReport = array_merge($reportHeader, $reportLines);

// Add footer with next steps
$reportFooter = [
    "",
    "NEXT STEPS:",
    "===========",
];

if ($totalIssues > 0) {
    $reportFooter[] = "1. Review each breaking change listed above";
    $reportFooter[] = "2. Fix code compatibility issues";
    $reportFooter[] = "3. Test changes with PHP 5.5.9 if possible";
    $reportFooter[] = "4. Re-run this analysis to verify fixes";
    $reportFooter[] = "";
    $reportFooter[] = "TIP: Focus on new errors introduced in your changes.";
    $reportFooter[] = "Existing errors in the base branch are not your responsibility.";
} else {
    $reportFooter[] = "1. Your changes look good from a compatibility perspective";
    $reportFooter[] = "2. Consider running additional tests";
    $reportFooter[] = "3. Proceed with code review and merge";
}

$fullReport = array_merge($fullReport, $reportFooter);

// Write report
$reportContent = implode("\n", $fullReport);
file_put_contents('reports/phpstan-breaking-changes.txt', $reportContent);

if ($totalIssues > 0) {
    echo "\n" . $reportContent . "\n";
    echo "\nFound $totalIssues breaking changes.\n";
    exit(1);
}

echo "\nNo breaking changes detected.\n";
exit(0);

// ---- Helper functions ----

/**
 * Find new errors that indicate breaking changes
 */
function findNewErrors($baseErrors, $headErrors, $currentFile, $baseFile) {
    $newErrors = [];
    
    // Normalize file references in errors
    $normalizedHeadErrors = array_map(function($error) use ($currentFile) {
        return str_replace(basename($currentFile), 'FILE', $error);
    }, $headErrors);
    
    $normalizedBaseErrors = array_map(function($error) use ($baseFile) {
        return str_replace($baseFile, 'FILE', $error);
    }, $baseErrors);
    
    // Find errors that exist in head but not in base
    foreach ($normalizedHeadErrors as $index => $normalizedError) {
        if (!in_array($normalizedError, $normalizedBaseErrors)) {
            // This is a new error - potential breaking change
            $originalError = $headErrors[$index];
            
            // Filter for breaking change indicators
            if (isBreakingChangeError($originalError)) {
                $newErrors[] = $originalError;
            }
        }
    }
    
    return $newErrors;
}

/**
 * Determine if an error indicates a breaking change
 */
function isBreakingChangeError($error) {
    $breakingPatterns = [
        '/Interface .+ not found/',
        '/Class .+ not found/',
        '/Function .+ not found/',
        '/Method .+ not found/',
        '/Property .+ not found/',
        '/Constant .+ not found/',
        '/implements unknown interface/',
        '/extends unknown class/',
        '/Call to undefined/',
        '/Access to undefined/',
        '/Unknown type/',
    ];
    
    foreach ($breakingPatterns as $pattern) {
        if (preg_match($pattern, $error)) {
            return true;
        }
    }
    
    return false;
}