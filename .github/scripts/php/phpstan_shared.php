<?php
/**
 * Shared PHPStan utilities for GitHub Actions workflows
 *
 * This file contains common PHPStan functions used by multiple scripts
 * to avoid code duplication and maintain consistency.
 *
 * Used by:
 * - phpstan_breaking_changes.php
 * - phpstan_branch_internal.php
 */

/**
 * Run PHPStan analysis on given files
 *
 * @param array $files List of file paths to analyze
 * @param string $context Context identifier for debugging (e.g., 'base', 'head')
 * @return array Array of error messages found by PHPStan
 */
function runPhpstan($files, $context) {
    $filesArg = implode(' ', array_map('escapeshellarg', $files));
    $configFile = createPhpstanConfig();

    // Debug: Show the command being executed
    $cmd = "phpstan analyse --configuration=" . escapeshellarg($configFile) . " --error-format=json --no-progress $filesArg";
    echo "DEBUG [$context]: Running PHPStan command: $cmd\n";

    // Run PHPStan with JSON output, but capture stderr too
    $output = shell_exec($cmd . " 2>&1");
    echo "DEBUG [$context]: PHPStan raw output length: " . strlen($output ?: '') . " bytes\n";

    // Show first 500 chars of output for debugging
    if ($output) {
        echo "DEBUG [$context]: PHPStan output preview: " . substr($output, 0, 500) . "\n";
    }

    unlink($configFile);

    if (!$output) {
        echo "DEBUG [$context]: No output from PHPStan\n";
        return [];
    }

    $result = json_decode($output, true);
    if (!$result) {
        echo "DEBUG [$context]: Failed to decode JSON output\n";
        echo "DEBUG [$context]: Raw output: $output\n";
        return [];
    }

    if (!isset($result['files'])) {
        echo "DEBUG [$context]: No 'files' key in PHPStan result\n";
        echo "DEBUG [$context]: Available keys: " . implode(', ', array_keys($result)) . "\n";
        return [];
    }

    $errors = [];
    foreach ($result['files'] as $file => $fileData) {
        if (isset($fileData['messages'])) {
            echo "DEBUG [$context]: Found " . count($fileData['messages']) . " messages in $file\n";
            foreach ($fileData['messages'] as $message) {
                $errors[] = sprintf(
                    "Line %d: %s",
                    isset($message['line']) ? $message['line'] : 0,
                    isset($message['message']) ? $message['message'] : 'Unknown error'
                );
            }
        }
    }

    echo "DEBUG [$context]: Total errors found: " . count($errors) . "\n";
    return $errors;
}

/**
 * Create temporary PHPStan configuration for PHP 5.5/5.6 compatibility analysis
 *
 * @return string Path to the temporary configuration file
 */
function createPhpstanConfig() {
    $configFile = tempnam(sys_get_temp_dir(), 'phpstan_config_');
    file_put_contents($configFile, "parameters:\n" .
        "  level: 8\n" .  // Increased from 5 to 8 for stricter analysis
        "  phpVersion: 50600\n" .  // Changed to PHP 5.6.0 to match your composer.json
        "  checkUndefinedClasses: true\n" .
        "  checkUndefinedMethods: true\n" .
        "  checkUndefinedProperties: true\n" .
        "  checkUndefinedFunctions: true\n" .
        "  checkMissingIterableValueType: false\n" .
        "  checkGenericClassInNonGenericObjectType: false\n" .
        "  reportUnmatchedIgnoredErrors: false\n" .
        "  treatPhpDocTypesAsCertain: false\n" .  // Additional strictness
        "  checkTooWideReturnTypesInProtectedAndPublicMethods: true\n" .
        "  checkMissingCallableSignature: true\n"
    );

    return $configFile;
}
