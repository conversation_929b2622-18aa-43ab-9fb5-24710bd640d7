#!/usr/bin/env python3

"""
PHPMD Report Generator - Enhanced 2025 Edition

Analyzes PHPMD XML reports for base and head commits and generates
comprehensive complexity analysis with modern thresholds, technical debt scoring,
maintainability index, and advanced GitHub Actions annotations.

Key Features:
- Modern 2025 complexity thresholds (CC≥10 error, CC≥7 warning)
- Technical debt scoring with weighted complexity impact
- Maintainability index calculation (industry standard)
- Method length analysis and refactoring recommendations
"""

import xml.etree.ElementTree as ET
import json
import sys
import os
import math
from collections import defaultdict
import re

def last_int(s: str) -> int:
    """Extract the last integer from a string, return 0 if none found."""
    matches = re.findall(r'\d+', s or '')
    return int(matches[-1]) if matches else 0

def extract_names(rule: str, text: str):
    """Extract method/class names from PHPMD violation text."""
    method = ''
    cls = ''

    if rule in ('CyclomaticComplexity', 'NPathComplexity'):
        # e.g. "The cyclomatic complexity for method foo() is 13."
        # e.g. "The NPath complexity of method bar() is 256."
        method_match = re.search(r'method\s+([A-Za-z0-9_\\:]+)\s*\(\)', text, flags=re.I)
        if method_match:
            method = method_match.group(1)
    elif rule == 'ExcessiveClassComplexity':
        # e.g. "The class Foo has a WMC of 31."
        # e.g. "Weighted Method Count for class Bar is 42."
        class_match = re.search(r'class\s+([A-Za-z0-9_\\:]+)', text, flags=re.I)
        if class_match:
            cls = class_match.group(1)

    return method, cls

def parse_phpmd_xml(filepath):
    metrics = {
        'functions': {},  # function_name -> {'cc': X, 'npath': Y, 'file': Z, 'line': N}
        'classes': {},    # class_name -> {'wmc': X, 'file': Z, 'line': N}
        'files': {}       # filename -> {'total_cc': X}
    }

    if not os.path.exists(filepath):
        return metrics

    try:
        tree = ET.parse(filepath)
        root = tree.getroot()

        for file_elem in root.findall('file'):
            filename = file_elem.get('name', '')
            file_total_cc = 0

            for violation in file_elem.findall('violation'):
                try:
                    rule = violation.get('rule', '')
                    beginline = int(violation.get('beginline', 0) or 0)
                    violation_text = (violation.text or '').strip()

                    # Extract value from violation message text
                    value = last_int(violation_text)

                    # Extract method/class names from violation message text
                    method_name, class_name = extract_names(rule, violation_text)

                    if rule == 'CyclomaticComplexity' and value > 0 and method_name:
                      func_key = f"{filename}::{method_name}"
                      metrics['functions'][func_key] = {
                          'cc': value,
                          'npath': metrics['functions'].get(func_key, {}).get('npath', 0),
                          'file': filename,
                          'line': beginline
                      }
                      file_total_cc += value

                    elif rule == 'NPathComplexity' and value > 0 and method_name:
                        func_key = f"{filename}::{method_name}"
                        if func_key not in metrics['functions']:
                            metrics['functions'][func_key] = {'cc': 0, 'file': filename, 'line': beginline}
                        metrics['functions'][func_key]['npath'] = value

                    elif rule == 'ExcessiveClassComplexity' and value > 0 and class_name:
                        class_key = f"{filename}::{class_name}"
                        metrics['classes'][class_key] = {
                            'wmc': value,
                            'file': filename,
                            'line': beginline
                        }
                except (ValueError, AttributeError) as e:
                    # Skip malformed violation entries, but log them for debugging
                    print(f"Warning: Skipping malformed violation in {filename}: {e}")
                    continue

            if file_total_cc > 0:
                metrics['files'][filename] = {'total_cc': file_total_cc}

    except (ET.ParseError, FileNotFoundError) as e:
        print(f"Warning: Could not parse {filepath}: {e}")

    return metrics

def calculate_technical_debt_score(metrics):
    """Calculate weighted technical debt score based on complexity metrics."""
    score = 0
    debt_items = []

    for func_key, data in metrics['functions'].items():
        cc = data['cc']
        npath = data.get('npath', 0)
        method_name = func_key.split('::')[-1]

        # Weighted scoring (CC has higher impact than NPath)
        func_debt = 0
        if cc >= 15:
            func_debt += cc * 5  # Very high impact
            debt_items.append(f"🔴 {method_name}: CC={cc} (critical)")
        elif cc >= 10:
            func_debt += cc * 3  # High impact
            debt_items.append(f"🟠 {method_name}: CC={cc} (high)")
        elif cc >= 7:
            func_debt += cc * 2  # Medium impact
            debt_items.append(f"🟡 {method_name}: CC={cc} (moderate)")
        elif cc >= 5:
            func_debt += cc * 1  # Low impact

        # NPath contribution (secondary factor)
        if npath >= 200:
            func_debt += npath // 20
        elif npath >= 100:
            func_debt += npath // 40

        score += func_debt

    return score, debt_items

def calculate_maintainability_index(cc, lines_of_code, halstead_volume=100):
    """Calculate industry-standard maintainability index (0-100 scale)."""
    if lines_of_code == 0:
        return 100

    # Industry standard formula
    mi = (171 - 5.2 * math.log(halstead_volume) -
          0.23 * cc - 16.2 * math.log(lines_of_code)) * 100 / 171
    return max(0, min(100, mi))

def analyze_method_length(violation_text, rule):
    """Extract method length from PHPMD violation text."""
    if rule == 'ExcessiveMethodLength':
        # e.g. "The method foo() has 45 lines of code."
        match = re.search(r'has\s+(\d+)\s+lines', violation_text, flags=re.I)
        if match:
            return int(match.group(1))
    return 0

def generate_refactoring_suggestions(metrics):
    """Generate actionable refactoring suggestions based on complexity analysis."""
    suggestions = []

    for func_key, data in metrics['functions'].items():
        cc = data['cc']
        npath = data.get('npath', 0)
        method_name = func_key.split('::')[-1]

        if cc >= 15:
            suggestions.append(f"🚨 **{method_name}**: Critical refactoring needed (CC={cc}). Consider breaking into smaller methods.")
        elif cc >= 10:
            suggestions.append(f"⚠️ **{method_name}**: Refactoring recommended (CC={cc}). Look for conditional logic to extract.")
        elif cc >= 7 and npath >= 100:
            suggestions.append(f"💡 **{method_name}**: Consider simplification (CC={cc}, NPath={npath}). Reduce nested conditions.")

    return suggestions

def generate_report():
    """Generate complexity analysis report from PHPMD XML files."""
    print("DEBUG: Starting complexity analysis report generation...")

    # Check if report files exist
    base_exists = os.path.exists('reports/base.xml')
    head_exists = os.path.exists('reports/head.xml')
    print(f"DEBUG: base.xml exists: {base_exists}")
    print(f"DEBUG: head.xml exists: {head_exists}")

    # Parse both reports
    base_metrics = parse_phpmd_xml('reports/base.xml')
    head_metrics = parse_phpmd_xml('reports/head.xml')

    print(f"DEBUG: Base metrics - functions: {len(base_metrics['functions'])}, classes: {len(base_metrics['classes'])}")
    print(f"DEBUG: Head metrics - functions: {len(head_metrics['functions'])}, classes: {len(head_metrics['classes'])}")

    # Calculate deltas and advanced metrics
    results = {
        'added_delta_cc': 0,
        'max_cc_after': 0,
        'max_npath_after': 0,
        'cc_violations': 0,           # CC≥10 (hard errors)
        'cc_warnings': 0,             # CC≥7 (warnings)
        'npath_violations': 0,        # NPath≥100
        'method_length_violations': 0, # Method length≥30
        'worst_delta_complexity': 0,
        'large_jumps': [],
        'top_cc': [],
        'top_npath_delta': [],
        'annotations': [],
        'technical_debt_score': 0,
        'maintainability_index': 0,
        'refactoring_suggestions': []
    }

    # Process functions
    for func_key, head_data in head_metrics['functions'].items():
        cc_after = head_data['cc']
        npath_after = head_data.get('npath', 0)
        filename = head_data['file']
        line = head_data['line']

        # Calculate deltas
        base_data = base_metrics['functions'].get(func_key, {'cc': 0, 'npath': 0})
        cc_delta = cc_after - base_data['cc']
        npath_delta = npath_after - base_data['npath']

        # Track maximums
        results['max_cc_after'] = max(results['max_cc_after'], cc_after)
        results['max_npath_after'] = max(results['max_npath_after'], npath_after)

        # Modern 2025 complexity thresholds
        if cc_after >= 10:
            results['cc_violations'] += 1
            results['annotations'].append({
                'level': 'error',
                'file': filename,
                'line': line,
                'message': f"Cyclomatic Complexity {cc_after} exceeds modern limit (10)"
            })
        elif cc_after >= 7:
            results['cc_warnings'] += 1
            results['annotations'].append({
                'level': 'warning',
                'file': filename,
                'line': line,
                'message': f"Cyclomatic Complexity {cc_after} approaching limit (7+)"
            })

        # Tightened NPath thresholds
        if npath_after >= 100:
            results['npath_violations'] += 1
            results['annotations'].append({
                'level': 'warning',
                'file': filename,
                'line': line,
                'message': f"NPath Complexity {npath_after} exceeds recommended limit (100)"
            })

        # Delta NPath warnings (tightened)
        if npath_delta >= 30:
            results['annotations'].append({
                'level': 'warning',
                'file': filename,
                'line': line,
                'message': f"NPath Complexity increased by {npath_delta} (threshold: +30)"
            })

        # Delta CC warnings (more sensitive)
        if cc_delta >= 2:
            results['annotations'].append({
                'level': 'warning',
                'file': filename,
                'line': line,
                'message': f"Cyclomatic Complexity increased by {cc_delta} - consider refactoring"
            })

        # Add to Added ΔCC if positive
        if cc_delta > 0:
            results['added_delta_cc'] += cc_delta

        # Store for top lists
        results['top_cc'].append((func_key, cc_after, cc_delta))
        if npath_delta > 0:
            results['top_npath_delta'].append((func_key, npath_after, npath_delta))

    # Process classes for WMC deltas with large jump detection
    for class_key, head_data in head_metrics['classes'].items():
        wmc_after = head_data['wmc']
        base_wmc = base_metrics['classes'].get(class_key, {'wmc': 0})['wmc']
        wmc_delta = wmc_after - base_wmc
        results['worst_delta_complexity'] = max(results['worst_delta_complexity'], wmc_delta)

        # Large jump detection for classes
        if wmc_delta >= 20:
            results['large_jumps'].append(f"Class WMC jump: {class_key.split('::')[-1]} (+{wmc_delta})")
        elif wmc_delta >= 10:
            results['large_jumps'].append(f"Class WMC increase: {class_key.split('::')[-1]} (+{wmc_delta})")

    # Process files for total CC deltas with large jump detection
    for filename, head_data in head_metrics['files'].items():
        total_cc_after = head_data['total_cc']
        base_total_cc = base_metrics['files'].get(filename, {'total_cc': 0})['total_cc']
        cc_delta = total_cc_after - base_total_cc
        results['worst_delta_complexity'] = max(results['worst_delta_complexity'], cc_delta)

        # Large jump detection for files
        if cc_delta >= 20:
            results['large_jumps'].append(f"File ΣCC jump: {os.path.basename(filename)} (+{cc_delta})")
        elif cc_delta >= 10:
            results['large_jumps'].append(f"File ΣCC increase: {os.path.basename(filename)} (+{cc_delta})")

    # Sort and limit top lists
    results['top_cc'].sort(key=lambda x: x[1], reverse=True)
    results['top_cc'] = results['top_cc'][:5]

    results['top_npath_delta'].sort(key=lambda x: x[2], reverse=True)
    results['top_npath_delta'] = results['top_npath_delta'][:5]

    # Calculate advanced metrics
    debt_score, debt_items = calculate_technical_debt_score(head_metrics)
    results['technical_debt_score'] = debt_score

    # Calculate average maintainability index
    total_cc = sum(data['cc'] for data in head_metrics['functions'].values())
    total_functions = len(head_metrics['functions'])
    avg_cc = total_cc / total_functions if total_functions > 0 else 0
    results['maintainability_index'] = calculate_maintainability_index(avg_cc, 100)  # Approximate LOC

    # Generate refactoring suggestions
    results['refactoring_suggestions'] = generate_refactoring_suggestions(head_metrics)

    # Generate annotations
    for annotation in results['annotations']:
        print(f"::{annotation['level']} file={annotation['file']},line={annotation['line']}::{annotation['message']}")

    # Generate enhanced summary
    debt_level = "🟢 LOW" if debt_score < 50 else "🟡 MODERATE" if debt_score < 150 else "🔴 HIGH"
    mi_level = "🟢 EXCELLENT" if results['maintainability_index'] >= 80 else "🟡 GOOD" if results['maintainability_index'] >= 60 else "🔴 NEEDS WORK"

    summary = f"""# PHP Complexity Analysis Report - Enhanced 2025 Edition

## 🎯 Quality Scorecard
- **Added ΔCC**: {results['added_delta_cc']}
- **Max CC (after)**: {results['max_cc_after']}
- **Max NPath (after)**: {results['max_npath_after']}
- **CC ≥10 violations** (errors): {results['cc_violations']}
- **CC ≥7 warnings**: {results['cc_warnings']}
- **NPath ≥100 violations**: {results['npath_violations']}
- **Technical Debt Score**: {debt_score} ({debt_level})
- **Maintainability Index**: {results['maintainability_index']:.1f}/100 ({mi_level})
- **Worst Δ(ΣCC/WMC)**: {results['worst_delta_complexity']}

## Top 5 Highest Cyclomatic Complexity
"""

    if results['top_cc']:
        for func_key, cc_after, cc_delta in results['top_cc']:
            func_name = func_key.split('::')[-1]
            summary += f"- **{func_name}**: {cc_after} (Δ{cc_delta:+d})\n"
    else:
        summary += "- No functions found\n"

    summary += "\n## Top 5 Largest NPath Increases\n"

    if results['top_npath_delta']:
        for func_key, npath_after, npath_delta in results['top_npath_delta']:
            func_name = func_key.split('::')[-1]
            summary += f"- **{func_name}**: {npath_after} (Δ{npath_delta:+d})\n"
    else:
        summary += "- No significant NPath increases\n"

    # Add large jumps section
    if results['large_jumps']:
        summary += "\n## Large Complexity Jumps (Investigation Recommended)\n"
        for jump in results['large_jumps'][:5]:  # Limit to top 5
            summary += f"- {jump}\n"

    # Add refactoring suggestions
    if results['refactoring_suggestions']:
        summary += "\n## 🔧 Refactoring Recommendations\n"
        for suggestion in results['refactoring_suggestions'][:10]:  # Limit to top 10
            summary += f"- {suggestion}\n"

    # Add technical debt breakdown
    if debt_items:
        summary += f"\n## 📊 Technical Debt Breakdown\n"
        for item in debt_items[:10]:  # Show top 10 debt contributors
            summary += f"- {item}\n"

    # Write summary to GitHub
    github_step_summary = os.environ.get('GITHUB_STEP_SUMMARY', '/dev/stdout')
    with open(github_step_summary, 'w') as f:
        f.write(summary)

    # Save full metrics
    os.makedirs('reports', exist_ok=True)
    with open('reports/complexity.json', 'w') as f:
        json.dump({
            'base_metrics': base_metrics,
            'head_metrics': head_metrics,
            'results': results
        }, f, indent=2)

    # Exit with error if hard failures (modern CC≥10 threshold)
    if results['cc_violations'] > 0:
        print(f"\n{'='*60}")
        print(f"❌ COMPLEXITY CHECK FAILED")
        print(f"{'='*60}")
        print(f"Found {results['cc_violations']} function(s) with CC ≥10 (error threshold)")
        print(f"See annotations above for details")
        print(f"{'='*60}\n")
        sys.exit(1)
    else:
        print(f"\n{'='*60}")
        print("✅ COMPLEXITY CHECK PASSED")
        print(f"{'='*60}")
        print("No functions exceed CC ≥10 threshold")
        warnings = results['cc_warnings'] + results['npath_violations']
        if warnings > 0 or results['added_delta_cc'] > 0:
            print(f"INFO: {warnings} complexity warnings, Added ΔCC: {results['added_delta_cc']}")
            print(f"Technical Debt Score: {debt_score}, Maintainability Index: {results['maintainability_index']:.1f}")
        print(f"{'='*60}\n")

if __name__ == '__main__':
    generate_report()
