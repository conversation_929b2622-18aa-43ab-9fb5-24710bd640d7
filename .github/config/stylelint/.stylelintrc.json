{"extends": ["stylelint-config-standard"], "plugins": ["stylelint-order", "@stylistic/stylelint-plugin"], "rules": {"stylistic/indentation": 4, "stylistic/string-quotes": "double", "color-hex-length": "short", "stylistic/color-hex-case": "lower", "stylistic/selector-attribute-quotes": "always", "selector-pseudo-element-colon-notation": "double", "stylistic/declaration-block-trailing-semicolon": "always", "stylistic/declaration-colon-space-before": "never", "stylistic/declaration-colon-space-after": "always", "stylistic/number-leading-zero": "always", "function-url-quotes": "always", "font-weight-notation": "numeric", "comment-empty-line-before": "always", "rule-empty-line-before": "always-multi-line", "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}], "property-no-vendor-prefix": null, "value-no-vendor-prefix": null, "selector-no-vendor-prefix": null, "media-feature-name-no-vendor-prefix": null, "order/order": ["custom-properties", "dollar-variables", "at-variables", "declarations", "rules", "at-rules"], "order/properties-order": [{"properties": ["position", "top", "right", "bottom", "left", "z-index"]}, {"properties": ["display", "flex", "flex-direction", "flex-wrap", "flex-flow", "flex-grow", "flex-shrink", "flex-basis", "grid", "grid-area", "grid-template", "grid-template-rows", "grid-template-columns", "grid-row", "grid-column", "grid-auto-rows", "grid-auto-columns", "grid-auto-flow", "gap", "row-gap", "column-gap", "align-content", "align-items", "align-self", "justify-content", "justify-items", "justify-self", "order"]}, {"properties": ["float", "clear"]}, {"properties": ["box-sizing", "width", "min-width", "max-width", "height", "min-height", "max-height"]}, {"properties": ["margin", "margin-top", "margin-right", "margin-bottom", "margin-left"]}, {"properties": ["padding", "padding-top", "padding-right", "padding-bottom", "padding-left"]}, {"properties": ["overflow", "overflow-x", "overflow-y"]}, {"properties": ["clip", "visibility"]}, {"properties": ["color", "font", "font-family", "font-size", "font-style", "font-weight", "line-height", "letter-spacing", "text-align", "text-decoration", "text-transform", "white-space", "word-break", "word-wrap"]}, {"properties": ["background", "background-color", "background-image", "background-position", "background-repeat", "background-size"]}, {"properties": ["border", "border-width", "border-style", "border-color", "border-radius"]}, {"properties": ["outline", "outline-offset"]}, {"properties": ["opacity"]}, {"properties": ["transform", "transform-origin"]}, {"properties": ["transition", "transition-delay", "transition-duration", "transition-property", "transition-timing-function"]}, {"properties": ["animation", "animation-delay", "animation-duration", "animation-iteration-count", "animation-name", "animation-play-state", "animation-timing-function"]}]}, "ignoreFiles": ["vendor/**/*.css", "node_modules/**/*.css", "dist/**/*.css", "build/**/*.css", "**/*.min.css"]}