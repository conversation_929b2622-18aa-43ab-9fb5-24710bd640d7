<?xml version="1.0"?>
<ruleset name="OmniBoxx Comprehensive Code Quality">

  <description>Comprehensive PHP code quality analysis for OmniBoxx</description>

  <!-- ========================================== -->
  <!-- CORE COMPLEXITY RULES -->
  <!-- ========================================== -->

  <!-- Cyclomatic Complexity -->
  <rule ref="rulesets/codesize.xml/CyclomaticComplexity">
    <properties>
      <property name="reportLevel" value="5"/>
    </properties>
  </rule>

  <!-- NPath Complexity -->
  <rule ref="rulesets/codesize.xml/NPathComplexity">
    <properties>
      <property name="minimum" value="50"/>
    </properties>
  </rule>

  <!-- Excessive Class Complexity -->
  <rule ref="rulesets/codesize.xml/ExcessiveClassComplexity">
    <properties>
      <property name="maximum" value="50"/>
    </properties>
  </rule>

  <!-- Method Length Limit -->
  <rule ref="rulesets/codesize.xml/ExcessiveMethodLength">
    <properties>
      <property name="minimum" value="25"/>
    </properties>
  </rule>

  <!-- Class Length Limit -->
  <rule ref="rulesets/codesize.xml/ExcessiveClassLength">
    <properties>
      <property name="minimum" value="500"/>
    </properties>
  </rule>

  <!-- Parameter Count Limit -->
  <rule ref="rulesets/codesize.xml/ExcessiveParameterList">
    <properties>
      <property name="minimum" value="6"/>
    </properties>
  </rule>

  <!-- Public Method Count -->
  <rule ref="rulesets/codesize.xml/ExcessivePublicCount">
    <properties>
      <property name="minimum" value="20"/>
    </properties>
  </rule>

  <!-- Too Many Fields -->
  <rule ref="rulesets/codesize.xml/TooManyFields">
    <properties>
      <property name="maxfields" value="15"/>
    </properties>
  </rule>

  <!-- Too Many Methods -->
  <rule ref="rulesets/codesize.xml/TooManyMethods">
    <properties>
      <property name="maxmethods" value="25"/>
    </properties>
  </rule>

  <!-- ========================================== -->
  <!-- CLEAN CODE RULES -->
  <!-- ========================================== -->

  <!-- Boolean Flag Arguments (Code Smell) -->
  <rule ref="rulesets/cleancode.xml/BooleanArgumentFlag" />

  <!-- Else Expression (Prefer Early Returns) -->
  <rule ref="rulesets/cleancode.xml/ElseExpression" />

  <!-- Static Access (With Laravel/Real Estate Exceptions) -->
  <rule ref="rulesets/cleancode.xml/StaticAccess">
    <properties>
      <property name="exceptions" value="Route,Config,DB,Log,Auth,Cache,Session,Request,Response,Validator,Hash,Crypt,Storage,Mail,Queue,Event,Notification,Artisan,Schema,Carbon,Str"/>
    </properties>
  </rule>

  <!-- ========================================== -->
  <!-- DESIGN QUALITY RULES -->
  <!-- ========================================== -->

  <!-- Dangerous Constructs -->
  <rule ref="rulesets/design.xml/ExitExpression" />
  <rule ref="rulesets/design.xml/EvalExpression" />
  <rule ref="rulesets/design.xml/GotoStatement" />

  <!-- Inheritance Depth -->
  <rule ref="rulesets/design.xml/DepthOfInheritance">
    <properties>
      <property name="minimum" value="4"/>
    </properties>
  </rule>

  <!-- Class Coupling -->
  <rule ref="rulesets/design.xml/CouplingBetweenObjects">
    <properties>
      <property name="minimum" value="8"/>
    </properties>
  </rule>

  <!-- Number of Children -->
  <rule ref="rulesets/design.xml/NumberOfChildren">
    <properties>
      <property name="minimum" value="10"/>
    </properties>
  </rule>

  <!-- ========================================== -->
  <!-- UNUSED CODE DETECTION -->
  <!-- ========================================== -->

  <!-- Unused Private Fields -->
  <rule ref="rulesets/unusedcode.xml/UnusedPrivateField" />

  <!-- Unused Local Variables -->
  <rule ref="rulesets/unusedcode.xml/UnusedLocalVariable" />

  <!-- Unused Private Methods -->
  <rule ref="rulesets/unusedcode.xml/UnusedPrivateMethod" />

  <!-- Unused Formal Parameters -->
  <rule ref="rulesets/unusedcode.xml/UnusedFormalParameter" />

  <!-- ========================================== -->
  <!-- NAMING CONVENTIONS -->
  <!-- ========================================== -->

  <!-- Short Variable Names -->
  <rule ref="rulesets/naming.xml/ShortVariable">
    <properties>
      <property name="minimum" value="2"/>
      <property name="exceptions" value="id,db,i,j,k,x,y,z"/>
    </properties>
  </rule>

  <!-- Long Variable Names -->
  <rule ref="rulesets/naming.xml/LongVariable">
    <properties>
      <property name="maximum" value="25"/>
    </properties>
  </rule>

  <!-- Short Method Names -->
  <rule ref="rulesets/naming.xml/ShortMethodName">
    <properties>
      <property name="minimum" value="3"/>
      <property name="exceptions" value="up,go,on,run,get,set,has,is"/>
    </properties>
  </rule>

  <!-- Constructor Naming -->
  <rule ref="rulesets/naming.xml/ConstructorWithNameAsEnclosingClass" />

  <!-- Constant Naming -->
  <rule ref="rulesets/naming.xml/ConstantNamingConventions" />

  <!-- Boolean Method Naming -->
  <rule ref="rulesets/naming.xml/BooleanGetMethodName" />

</ruleset>
