name: Dry-run merge conflict check

on:
  workflow_call:

permissions:
  contents: read

concurrency:
  group: merge-check-${{ github.ref }}
  cancel-in-progress: true

jobs:
  dry-merge:
    name: Dry-run merge to develop + support/*
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Checkout (full history)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch all branches
        run: |
          git fetch --prune origin '+refs/heads/*:refs/remotes/origin/*'
          git branch -a

      - name: Determine source
        id: src
        env:
          EVENT_NAME: ${{ github.event_name }}
        run: |
          if [ "$EVENT_NAME" = "pull_request" ]; then
            echo "ref=${{ github.event.pull_request.head.ref }}" >> $GITHUB_OUTPUT
            echo "sha=${{ github.event.pull_request.head.sha }}" >> $GITHUB_OUTPUT
          else
            echo "ref=${{ github.ref_name }}" >> $GITHUB_OUTPUT
            echo "sha=${{ github.sha }}" >> $GITHUB_OUTPUT
          fi
          echo "develop_ref=origin/develop" >> $GITHUB_OUTPUT

      - name: Configure git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Step 1 — feature → develop (simulate)
        id: merge_develop
        run: |
          set -e
          # Start from develop
          git switch -c tmp-merged "${{ steps.src.outputs.develop_ref }}"
          
          # Try real merge (local). Fails on conflicts.
          set +e
          git merge --no-ff --no-edit "${{ steps.src.outputs.sha }}"
          rc=$?
          set -e
          
          if [ $rc -ne 0 ]; then
            echo "CONFLICTS=true" >> $GITHUB_OUTPUT
            echo "Conflicts when merging feature (${{ steps.src.outputs.ref }}) to develop"
            git ls-files -u | cut -f2 | sort -u | tee /tmp/develop_conflicts.txt
            # Don't exit here - let summary run first
          else
            echo "CONFLICTS=false" >> $GITHUB_OUTPUT
          fi

      - name: Collect support/* branches
        id: supports
        if: steps.merge_develop.outputs.CONFLICTS == 'false'
        run: |
          mapfile -t BRANCHES < <(git for-each-ref --format='%(refname:short)' 'refs/remotes/origin/support/*' | sort)
          if [ ${#BRANCHES[@]} -eq 0 ]; then
            echo "none=true" >> $GITHUB_OUTPUT
            echo "No support/* branches found."
          else
            printf "%s\n" "${BRANCHES[@]}" > /tmp/support_list.txt
            echo "none=false" >> $GITHUB_OUTPUT
          fi

      - name: Step 2 — develop+feature → each support/*
        if: steps.supports.outputs.none == 'false'
        run: |
          STATUS=0
          echo "Dry-run merge develop(+feature) → support/*"
          echo ""

          while read -r REMOTE; do
            # REMOTE is e.g.: origin/support/client-a
            BRANCH="${REMOTE#origin/}"            # support/client-a
            TMP="tmp-check-${BRANCH//\//-}"       # tmp-check-support-client-a

            git switch -c "$TMP" "$REMOTE" >/dev/null 2>&1

            set +e
            git merge --no-commit --no-ff tmp-merged >/dev/null 2>&1
            rc=$?
            set -e

            if [ $rc -ne 0 ]; then
              echo "$BRANCH — CONFLICT"
              git diff --name-only --diff-filter=U | sort -u | tee "/tmp/${TMP}_conflicts.txt"
              git merge --abort || true
              STATUS=1
            else
              # Clean up and continue
              git reset --hard HEAD >/dev/null 2>&1
              echo "$BRANCH — OK (no conflicts)"
            fi

            # Return to tmp-merged for next branch
            git switch tmp-merged >/dev/null 2>&1
          done < /tmp/support_list.txt

          # Don't exit here - let summary run first
          echo "exit_status=$STATUS" >> $GITHUB_OUTPUT

      - name: Count conflicts and prepare summary data
        if: always()
        id: summary_data
        continue-on-error: true
        run: |
          # Count total conflicts across all support branches
          TOTAL_CONFLICTS=0
          FAILED_BRANCHES=""
          if [ "${{ steps.supports.outputs.none }}" = "false" ] && [ -f /tmp/support_list.txt ]; then
            while read -r REMOTE; do
              BRANCH="${REMOTE#origin/}"
              TMP="tmp-check-${BRANCH//\//-}"
              if [ -f "/tmp/${TMP}_conflicts.txt" ]; then
                CONFLICT_COUNT=$(wc -l < "/tmp/${TMP}_conflicts.txt" 2>/dev/null || echo "0")
                if [ "$CONFLICT_COUNT" -gt 0 ]; then
                  TOTAL_CONFLICTS=$((TOTAL_CONFLICTS + CONFLICT_COUNT))
                  FAILED_BRANCHES="$FAILED_BRANCHES $BRANCH"
                fi
              fi
            done < /tmp/support_list.txt
          fi

          echo "total_conflicts=$TOTAL_CONFLICTS" >> $GITHUB_OUTPUT
          echo "failed_branches=$FAILED_BRANCHES" >> $GITHUB_OUTPUT

          # Determine overall result
          if [ "${{ steps.merge_develop.outputs.CONFLICTS }}" = "true" ]; then
            echo "result=FAILED" >> $GITHUB_OUTPUT
            echo "details=DEVELOP MERGE CONFLICTS: Cannot merge into develop due to conflicts. Please resolve conflicts with develop branch first." >> $GITHUB_OUTPUT
          elif [ "$TOTAL_CONFLICTS" -gt 0 ]; then
            echo "result=FAILED" >> $GITHUB_OUTPUT
            echo "details=SUPPORT BRANCH CONFLICTS: Conflicting branches: $FAILED_BRANCHES. This PR will conflict when merged through the full pipeline." >> $GITHUB_OUTPUT
          else
            echo "result=PASSED" >> $GITHUB_OUTPUT
            echo "details=NO CONFLICTS: PR can merge cleanly through entire pipeline. All support branches are compatible." >> $GITHUB_OUTPUT
          fi

      - name: Generate summary
        if: always()
        uses: ./.github/actions/workflow-summary
        with:
          workflow_name: 'Dry-run Merge Conflicts'
          files_checked: 'N/A (Branch analysis)'
          result: ${{ steps.summary_data.outputs.result }}
          additional_info: |
            PR Branch: ${{ steps.src.outputs.ref }}
            Base Branch: develop
            Develop Merge: ${{ steps.merge_develop.outputs.CONFLICTS == 'false' && 'SUCCESS' || 'FAILED' }}
            Support Branches Found: ${{ steps.supports.outputs.none == 'false' && 'YES' || 'NO' }}
            Total Conflicting Files: ${{ steps.summary_data.outputs.total_conflicts }}
          details: ${{ steps.summary_data.outputs.details }}

      - name: Fail job if conflicts detected
        if: steps.summary_data.outputs.result == 'FAILED'
        run: |
          echo "::error::Merge conflicts detected in dry-run analysis"
          exit 1