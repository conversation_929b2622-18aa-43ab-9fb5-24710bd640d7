name: CSS Lint

on:
  workflow_call:

permissions:
  contents: read

jobs:
  lint:
    name: CSS Lint
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup CSS linting tools
        uses: ./.github/actions/css/setup-stylelint

      - name: Get changed CSS files
        id: changed
        uses: ./.github/actions/changed-files
        with:
          base: develop
          head: HEAD
          globs: '*.css *.scss *.sass'

      - name: Ski<PERSON> if no CSS changes
        if: steps.changed.outputs.count == '0'
        run: echo "::notice::No CSS files changed. Skipping CSS lint checks."

      - name: Setup Stylelint configuration
        if: steps.changed.outputs.count != '0'
        run: |
          echo "Using static Stylelint configuration"
          cp ./.github/config/stylelint/.stylelintrc.json .stylelintrc.json


      - name: Run Stylelint checks
        if: steps.changed.outputs.count != '0'
        id: stylelint
        uses: ./.github/actions/css/stylelint-run
        with:
          files: ${{ steps.changed.outputs.files }}

      - name: Run browser compatibility check
        if: steps.changed.outputs.count != '0'
        id: compat
        uses: ./.github/actions/css/browser-compat
        with:
          files: ${{ steps.changed.outputs.files }}

      - name: Generate summary
        if: always()
        uses: ./.github/actions/workflow-summary
        with:
          workflow_name: 'CSS Lint'
          files_checked: ${{ steps.changed.outputs.count }}
          result: ${{ steps.changed.outputs.count == '0' && 'SKIPPED' || (steps.stylelint.outputs.violations == 'true' && 'FAILED' || 'PASSED') }}
          additional_info: |
            Stylelint violations: ${{ steps.stylelint.outputs.violations == 'true' && 'Found' || 'None' }}
            Browser compatibility: Checked
          details: ${{ steps.changed.outputs.count == '0' && 'No CSS files changed' || (steps.stylelint.outputs.violations == 'true' && 'Stylelint violations found - please fix before merging' || 'All CSS checks passed') }}

      - name: Fail on Stylelint violations
        if: steps.stylelint.outputs.violations == 'true'
        run: |
          echo "::error::CSS linting failed due to Stylelint violations"
          echo "::error::Please fix the violations above before merging"
          echo "::notice::Browser compatibility warnings are informational only"
          exit 1