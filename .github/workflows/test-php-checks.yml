name: Test PHP Checks (Manual Trigger)

on:
  workflow_dispatch:
    inputs:
      test_file:
        description: 'File to test (e.g., application/controllers/ComplaintController.php)'
        required: true
        default: 'application/controllers/ComplaintController.php'
      check_type:
        description: 'Type of check to run'
        required: true
        type: choice
        options:
          - 'all'
          - 'lint'
          - 'breaking-changes'
          - 'branch-internal'
          - 'complexity'

permissions:
  contents: read

jobs:
  test-lint:
    name: Test PHP Lint
    runs-on: ubuntu-latest
    if: ${{ inputs.check_type == 'all' || inputs.check_type == 'lint' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: composer

      - name: Test PHP syntax
        run: |
          echo "Testing PHP syntax on: ${{ inputs.test_file }}"
          if [ -f "${{ inputs.test_file }}" ]; then
            php -l "${{ inputs.test_file }}"
          else
            echo "::error::File not found: ${{ inputs.test_file }}"
            exit 1
          fi

      - name: Install PHPCS and PHPCompatibility
        uses: ./.github/actions/php/install-phpcs-compat

      - name: Run PHPCS compatibility check
        run: |
          COMPOSER_HOME=$(composer global config home)
          PHPCS_BIN="$COMPOSER_HOME/vendor/bin/phpcs"
          
          echo "Running PHPCS compatibility check on: ${{ inputs.test_file }}"
          $PHPCS_BIN --standard=PHPCompatibility --runtime-set testVersion 5.6 "${{ inputs.test_file }}" || true

  test-breaking-changes:
    name: Test Breaking Changes
    runs-on: ubuntu-latest
    if: ${{ inputs.check_type == 'all' || inputs.check_type == 'breaking-changes' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: composer

      - name: Test PHPStan breaking changes
        uses: ./.github/actions/php/phpstan-breaking-changes
        with:
          files: ${{ inputs.test_file }}
          base_ref: 'develop'

  test-branch-internal:
    name: Test Branch Internal
    runs-on: ubuntu-latest
    if: ${{ inputs.check_type == 'all' || inputs.check_type == 'branch-internal' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: composer

      - name: Test PHPStan branch internal
        uses: ./.github/actions/php/phpstan-branch-internal
        with:
          files: ${{ inputs.test_file }}
          base_ref: 'HEAD~1'

  test-complexity:
    name: Test Complexity
    runs-on: ubuntu-latest
    if: ${{ inputs.check_type == 'all' || inputs.check_type == 'complexity' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: 'none'

      - name: Install PHPMD
        uses: ./.github/actions/php/phpmd-install
        with:
          version: latest

      - name: Create reports directory
        run: mkdir -p reports

      - name: Run PHPMD on test file
        run: |
          echo "Running PHPMD on: ${{ inputs.test_file }}"
          
          # Run PHPMD on base version (empty for new analysis)
          echo '<?xml version="1.0"?><pmd version="2.0.0" timestamp="2025-01-01T00:00:00+00:00"><file name="empty"></file></pmd>' > reports/base.xml
          
          # Run PHPMD on current version
          if [ -f "${{ inputs.test_file }}" ]; then
            ~/.composer/vendor/bin/phpmd "${{ inputs.test_file }}" xml ./.github/config/phpmd/ruleset.xml > reports/head.xml || true
            echo "PHPMD XML output:"
            cat reports/head.xml
          else
            echo "::error::File not found: ${{ inputs.test_file }}"
            exit 1
          fi

      - name: Generate complexity report
        run: |
          echo "Generating complexity report..."
          python3 ./.github/scripts/php/phpmd-report.py

      - name: Upload reports
        uses: actions/upload-artifact@v4
        with:
          name: test-complexity-reports
          path: reports/
          retention-days: 7
