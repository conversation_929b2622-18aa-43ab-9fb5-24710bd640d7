name: PHP Complexity

on:
  workflow_call:

permissions:
  contents: read

# For heavy complexity analysis, cancel older runs when new commits arrive
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  complexity:
    name: PHP Complexity Analysis
    runs-on: ubuntu-latest

    steps:
      - name: Checkout HEAD
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: 'none'

      - name: Install PHPMD (Latest Version)
        uses: ./.github/actions/php/phpmd-install
        with:
          version: latest

      - name: Get changed PHP files
        id: changed
        uses: ./.github/actions/changed-files
        with:
          base: develop
          head: HEAD
          globs: '*.php'

      - name: Skip if no PHP changes
        if: steps.changed.outputs.count == '0'
        run: echo "No PHP files changed. Skipping complexity analysis."

      - name: Determine base SHA
        if: steps.changed.outputs.count != '0'
        id: base-sha
        run: |
          # Try to get the base SHA from the event, fallback to develop
          if [ -n "${{ github.event.before }}" ] && [ "${{ github.event.before }}" != "0000000000000000000000000000000000000000" ]; then
            BASE_SHA="${{ github.event.before }}"
            echo "Using event base SHA: $BASE_SHA"
          else
            # Fallback to develop branch
            git fetch origin develop:develop || true
            BASE_SHA=$(git rev-parse origin/develop)
            echo "Using develop branch SHA: $BASE_SHA"
          fi
          echo "base_sha=$BASE_SHA" >> "$GITHUB_OUTPUT"

      - name: Run PHPMD Analysis
        if: steps.changed.outputs.count != '0'
        uses: ./.github/actions/php/phpmd-run
        with:
          files: ${{ steps.changed.outputs.files }}
          base_sha: ${{ steps.base-sha.outputs.base_sha }}
          head_sha: ${{ github.sha }}
          ruleset: './.github/config/phpmd/ruleset.xml'

      - name: Generate Complexity Report
        if: steps.changed.outputs.count != '0'
        run: |
          mkdir -p reports
          echo "Running complexity analysis..."
          python3 ./.github/scripts/php/phpmd-report.py

      - name: Upload reports
        if: steps.changed.outputs.count != '0'
        uses: actions/upload-artifact@v4
        with:
          name: complexity-reports
          path: reports/
          retention-days: 7

      - name: Generate summary
        if: always()
        uses: ./.github/actions/workflow-summary
        with:
          workflow_name: 'PHP Complexity Analysis'
          files_checked: ${{ steps.changed.outputs.count }}
          result: ${{ steps.changed.outputs.count == '0' && 'SKIPPED' || (job.status == 'success' && 'PASSED' || 'FAILED') }}
          additional_info: |
            PHPMD version: Latest (cached)
            Ruleset coverage: 24+ comprehensive rules
            Thresholds: CC≥10 error, CC≥7 warning, NPath≥100
            Advanced metrics: Technical debt scoring, maintainability index
            Tooling: PHP 8.1 (targeting PHP 5.6 compatibility)
          details: ${{ steps.changed.outputs.count == '0' && 'No PHP files to analyze' || (job.status == 'success' && 'Complexity analysis completed' || 'Complexity analysis failed - check for CC≥10 violations or configuration issues') }}
