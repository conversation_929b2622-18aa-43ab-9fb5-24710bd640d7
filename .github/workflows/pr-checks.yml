name: PR Checks

on:
  pull_request:
    branches:
      - develop
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  php_lint:
    name: PHP Lint
    uses: ./.github/workflows/_php-quality-checks.yml
    with:
      check_mode: 'lint'
      base_ref: 'develop'

  php_breaking_changes:
    name: PHP Breaking Changes
    uses: ./.github/workflows/_php-quality-checks.yml
    with:
      check_mode: 'breaking-changes'
      base_ref: 'develop'

  php_branch_internal_breaking_changes:
    name: PHP Branch Internal Breaking Changes
    uses: ./.github/workflows/_php-quality-checks.yml
    with:
      check_mode: 'branch-internal'
      base_ref: 'develop'

  php_complexity:
    name: PHP Complexity
    uses: ./.github/workflows/_php-complexity.yml

  conflicts:
    name: Support Branch Conflicts
    uses: ./.github/workflows/_support-branch-conflicts.yml

  dry_merge_conflicts:
    name: Dry-run Merge Conflicts
    uses: ./.github/workflows/_dry-merge-conflicts.yml
