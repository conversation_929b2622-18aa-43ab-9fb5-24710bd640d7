name: PHP Quality Checks

on:
  workflow_call:
    inputs:
      check_mode:
        description: 'Type of checks to run: lint, breaking-changes, branch-internal'
        required: true
        type: string

      base_ref:
        description: 'Base reference for comparison'
        required: false
        type: string
        default: 'develop'

permissions:
  contents: read

jobs:
  quality-checks:
    name: PHP Quality Analysis
    runs-on: ubuntu-latest
    env:
      COMPOSER_NO_INTERACTION: "1"
      COMPOSER_ALLOW_SUPERUSER: "1"

    steps:
      # INPUT VALIDATION
      - name: Validate inputs
        run: |
          VALID_MODES="lint breaking-changes branch-internal"
          if ! echo "$VALID_MODES" | grep -qw "${{ inputs.check_mode }}"; then
            echo "::error::Invalid check_mode '${{ inputs.check_mode }}'. Must be one of: $VALID_MODES"
            exit 1
          fi
          echo "✅ Input validation passed: check_mode='${{ inputs.check_mode }}'"

      # SETUP
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup PHP 8.1
        uses: ./.github/actions/php/setup-php
        with:
          version: '8.1'
          tools: composer

      # FILE DETECTION
      - name: Get changed PHP files
        id: changed
        uses: ./.github/actions/changed-files
        with:
          base: ${{ inputs.base_ref }}
          head: HEAD
          globs: '*.php'

      - name: Create reports directory
        run: mkdir -p reports

      - name: Skip if no PHP changes
        if: steps.changed.outputs.count == '0'
        run: |
          echo "::notice::No PHP files changed. Skipping PHP quality checks."
          # Create a skip report
          cat > reports/skip-report.txt <<EOF
          PHP Quality Check Report - ${{ inputs.check_mode }}
          ========================================
          Status: SKIPPED
          Reason: No PHP files changed
          Base reference: ${{ inputs.base_ref }}
          Check mode: ${{ inputs.check_mode }}
          Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          ========================================
          EOF

      # MODE: LINT - Code style and compatibility checks
      - name: Run PHP syntax check
        id: syntax_check
        if: steps.changed.outputs.count != '0' && inputs.check_mode == 'lint'
        continue-on-error: true
        run: |
          echo "=========================================="
          echo "Running fast PHP syntax check (php -l)..."
          echo "=========================================="

          # Initialize report
          REPORT_FILE="reports/syntax-check.txt"
          echo "PHP Syntax Check Report" > "$REPORT_FILE"
          echo "========================================" >> "$REPORT_FILE"
          echo "Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> "$REPORT_FILE"
          echo "Files checked: ${{ steps.changed.outputs.count }}" >> "$REPORT_FILE"
          echo "" >> "$REPORT_FILE"

          SYNTAX_ERRORS=0
          CHECKED_FILES=0
          for file in ${{ steps.changed.outputs.files }}; do
            CHECKED_FILES=$((CHECKED_FILES + 1))
            echo "Checking syntax: $file"
            echo "[$CHECKED_FILES] Checking: $file" >> "$REPORT_FILE"

            if php -l "$file" > /tmp/php_lint_output.txt 2>&1; then
              echo "  ✓ Syntax OK"
              echo "    Result: ✓ PASS" >> "$REPORT_FILE"
            else
              echo "  ✗ Syntax Error"
              echo "    Result: ✗ FAIL" >> "$REPORT_FILE"
              echo "    Error details:" >> "$REPORT_FILE"
              cat /tmp/php_lint_output.txt | sed 's/^/      /' >> "$REPORT_FILE"
              php -l "$file"
              echo "::error file=$file::PHP syntax error detected"
              SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
            fi
            echo "" >> "$REPORT_FILE"
          done

          # Summary
          echo "========================================" >> "$REPORT_FILE"
          echo "Summary:" >> "$REPORT_FILE"
          echo "  Total files: $CHECKED_FILES" >> "$REPORT_FILE"
          echo "  Passed: $((CHECKED_FILES - SYNTAX_ERRORS))" >> "$REPORT_FILE"
          echo "  Failed: $SYNTAX_ERRORS" >> "$REPORT_FILE"
          echo "========================================" >> "$REPORT_FILE"

          if [ $SYNTAX_ERRORS -gt 0 ]; then
            echo "::error::Found $SYNTAX_ERRORS file(s) with PHP syntax errors"
            exit 1
          fi

          echo "=========================================="
          echo "✓ All files passed syntax check"
          echo "=========================================="

      - name: Install PHPCS and PHPCompatibility
        if: steps.changed.outputs.count != '0' && inputs.check_mode == 'lint'
        uses: ./.github/actions/php/install-phpcs-compat

      - name: Run PHP linting (style + compatibility)
        id: phpcs
        if: steps.changed.outputs.count != '0' && inputs.check_mode == 'lint'
        uses: ./.github/actions/php/phpcs-diff
        with:
          files: ${{ steps.changed.outputs.files }}

      # MODE: BREAKING-CHANGES - PHPStan breaking changes analysis
      - name: Run PHPStan breaking changes analysis
        if: steps.changed.outputs.count != '0' && inputs.check_mode == 'breaking-changes'
        id: phpstan
        uses: ./.github/actions/php/phpstan-breaking-changes
        with:
          files: ${{ steps.changed.outputs.files }}
          base_ref: ${{ inputs.base_ref }}

      - name: Fail if breaking changes detected
        if: inputs.check_mode == 'breaking-changes' && steps.phpstan.outputs.breaking_changes == '1'
        run: |
          echo "::error title=PHP Breaking Changes::Breaking changes detected that could break existing code."
          exit 1

      # MODE: BRANCH-INTERNAL - PHPStan branch internal analysis
      - name: Run PHPStan branch internal analysis
        if: steps.changed.outputs.count != '0' && inputs.check_mode == 'branch-internal'
        id: branch-internal
        uses: ./.github/actions/php/phpstan-branch-internal
        with:
          files: ${{ steps.changed.outputs.files }}
          base_ref: ${{ inputs.base_ref }}

      - name: Fail if branch internal errors detected
        if: inputs.check_mode == 'branch-internal' && steps.branch-internal.outputs.breaking_changes == '1'
        run: |
          echo "::error title=PHP Branch Internal Errors::Your changes introduced errors within the PR."
          exit 1

      # REPORTING
      - name: Upload quality reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: php-quality-reports-${{ inputs.check_mode }}
          path: reports/
          retention-days: 7
          if-no-files-found: ignore

      # SUMMARY
      - name: Generate summary
        if: always()
        uses: ./.github/actions/workflow-summary
        with:
          workflow_name: 'PHP Quality Checks (${{ inputs.check_mode }})'
          files_checked: ${{ steps.changed.outputs.count }}
          result: ${{ job.status == 'success' && 'PASSED' || 'FAILED' }}
          additional_info: |
            Check Mode: ${{ inputs.check_mode }}
            Base reference: ${{ inputs.base_ref }}
          details: ${{ steps.changed.outputs.count == '0' && 'No PHP changes' || (job.status == 'success' && 'All checks passed' || 'Checks failed - see reports') }}
