name: Support Branch Conflicts

on:
  workflow_call:

permissions:
  contents: read

jobs:
  check-conflicts:
    name: Check PR Conflicts with Support Branches
    runs-on: ubuntu-latest

    steps:
      - name: Checkout PR branch and base branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git user for simulation
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Get changed files in PR
        id: changed
        uses: ./.github/actions/changed-files
        with:
          base: ${{ github.base_ref }}
          head: ${{ github.head_ref }}
          globs: '*'

      - name: Define support branches to check
        id: support_branches
        run: |
          # Define all support branches to check
          BRANCHES=(
            "support/upgrade_AND_OM-2149_twinfield_transaction_import_date_improvement"
            "support/acl_editor_and_upgrade"
            "support/acl_editor_and_OM-2149_twinfield_transaction_import_date_improvement"
            "support/no_vat_exchange_on_third_party_corporation_invoices_AND_OM-2149_twinfield_transaction_import_date_improvement"
            "support/phoenix_layout"
            "support/riva_email_complaint_to_reporter"
            "support/VAT_reversed"
            "support/worker_queue_test_suite"
            "support/invoice_custom_substract_and_article_fixes"
            "support/no_vat_exchange_on_third_party_corporation_invoices_AND_OM-2149_twinfield_transaction_import_date_improvement_AND_investor_provision_due_period_shift"
            "support/investor_provision_due_period_shift"
            "support/demo_testing_branche"
            "support/bankaccount_closing_account_and_indexation_improvements"
            "support/bankaccount_closing_account_twinfield_no_vat_combination_branch"
            "support/bankaccount_closing_account_twinfield_no_vat_combination_branch_and_acl"
            "support/HBSoftware_api_2_testing"
            "support/1_3_2025-technical-mapping-for-everybody"
            "support/omnibox_UI_changes_show-view"
            "support/upgrade-twinfield-transaction-and-demo-vat-reversed"
            "support/mt940_use_highest_date_249509"
            "support/demo_testing_branche_AND_vat-reversed"
            "support/demo_test_vat-reversed_timeslot_assign_supportmail"
          )

          printf "%s\n" "${BRANCHES[@]}" > /tmp/support_branches.txt
          echo "total=${#BRANCHES[@]}" >> $GITHUB_OUTPUT

      - name: Check conflicts with all support branches
        id: check_all
        run: |
          TOTAL_CHECKED=0
          TOTAL_CONFLICTS=0
          CONFLICT_BRANCHES=""
          ERROR_BRANCHES=""
          SUCCESS_BRANCHES=""

          echo "Starting conflict checks for all support branches..."
          echo ""

          while IFS= read -r BRANCH; do
            TOTAL_CHECKED=$((TOTAL_CHECKED + 1))
            echo "[$TOTAL_CHECKED] Checking $BRANCH..."

            # Use git merge-tree to simulate 3-way merge
            set +e
            MERGE_RESULT=$(git merge-tree ${{ github.base_ref }} ${{ github.head_ref }} origin/$BRANCH 2>&1)
            MERGE_EXIT_CODE=$?
            set -e

            if [ $MERGE_EXIT_CODE -ne 0 ]; then
              # Branch might not exist or other error
              if echo "$MERGE_RESULT" | grep -q "not a tree"; then
                echo "  ⚠️  Branch does not exist"
                ERROR_BRANCHES="$ERROR_BRANCHES $BRANCH"
              else
                echo "  ❌ Error checking branch"
                ERROR_BRANCHES="$ERROR_BRANCHES $BRANCH"
              fi
            elif [ -n "$MERGE_RESULT" ]; then
              # Conflicts detected
              echo "  ❌ CONFLICTS detected"
              TOTAL_CONFLICTS=$((TOTAL_CONFLICTS + 1))
              CONFLICT_BRANCHES="$CONFLICT_BRANCHES $BRANCH"
            else
              # No conflicts
              echo "  ✅ No conflicts"
              SUCCESS_BRANCHES="$SUCCESS_BRANCHES $BRANCH"
            fi
          done < /tmp/support_branches.txt

          echo ""
          echo "Summary of conflict checks:"
          echo "Total branches checked: $TOTAL_CHECKED"
          echo "Branches with conflicts: $TOTAL_CONFLICTS"
          echo "Branches with errors: $(echo $ERROR_BRANCHES | wc -w)"
          echo "Compatible branches: $(echo $SUCCESS_BRANCHES | wc -w)"

          # Calculate compatible branches
          COMPATIBLE_BRANCHES=$((TOTAL_CHECKED - TOTAL_CONFLICTS))

          # Prepare summary details
          if [ "$TOTAL_CONFLICTS" -eq 0 ]; then
            SUMMARY_DETAILS="All support branches are compatible with this PR"
          else
            SUMMARY_DETAILS="CONFLICTS DETECTED:\n\nBranches with conflicts:$CONFLICT_BRANCHES\n\nThis PR has conflicts with $TOTAL_CONFLICTS support branches.\nManual resolution may be required when merging to these branches."
          fi

          # Set outputs
          echo "total_checked=$TOTAL_CHECKED" >> $GITHUB_OUTPUT
          echo "total_conflicts=$TOTAL_CONFLICTS" >> $GITHUB_OUTPUT
          echo "compatible_branches=$COMPATIBLE_BRANCHES" >> $GITHUB_OUTPUT
          echo "conflict_branches=$CONFLICT_BRANCHES" >> $GITHUB_OUTPUT
          echo "error_branches=$ERROR_BRANCHES" >> $GITHUB_OUTPUT
          echo "success_branches=$SUCCESS_BRANCHES" >> $GITHUB_OUTPUT

          # Use multiline output for details
          echo "summary_details<<EOF" >> $GITHUB_OUTPUT
          echo "$SUMMARY_DETAILS" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Generate summary
        if: always()
        uses: ./.github/actions/workflow-summary
        with:
          workflow_name: 'Support Branch Conflicts'
          files_checked: ${{ steps.changed.outputs.count }}
          result: ${{ steps.check_all.outputs.total_conflicts == '0' && 'PASSED' || 'FAILED' }}
          additional_info: |
            PR Branch: ${{ github.head_ref }}
            Base Branch: ${{ github.base_ref }}
            Total support branches: ${{ steps.support_branches.outputs.total }}
            Branches checked: ${{ steps.check_all.outputs.total_checked }}
            Conflicting branches: ${{ steps.check_all.outputs.total_conflicts }}
            Compatible branches: ${{ steps.check_all.outputs.compatible_branches }}
          details: ${{ steps.check_all.outputs.summary_details }}

      - name: Fail job if conflicts detected
        if: steps.check_all.outputs.total_conflicts != '0'
        run: |
          echo "::error::Merge conflicts detected with ${{ steps.check_all.outputs.total_conflicts }} support branches"
          echo "::error::Conflicting branches: ${{ steps.check_all.outputs.conflict_branches }}"
          echo "::error::Please review the conflicts above and resolve before merging"
          exit 1