<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$this->addSubForm($general, 'general_form');

	$general->addElement('text', 'name', [
		'label' => 'Naam',
		'validators' => [],
	]);

	$general->addElement('select', 'format', [
		'label' => 'Formaat',
		'multiOptions' => ObjectPropertyTypes::getFormatLabels(),
	]);

	$general->addElement('text', 'code', [
		'label' => 'Code',
		'validators' => [],
	]);

	$general->addElement('text', 'order', [
		'label' => 'Volgorde',
		'validators' => [],
	]);


//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Object eigenschap opslaan'
]);

?>
