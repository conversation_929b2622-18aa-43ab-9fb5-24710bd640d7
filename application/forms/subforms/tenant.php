<?

// VERVANGEN DOOR FUNCTIE AANROEPEN
switch ($i) {
    case 2:
        $partnername =  'partner';
        break;
    case 3:
        $partnername =  'partner_2';
        $partner;
        break;
    case 4:
        $partnername =  'partner_3';
        break;
}

${"partner" . $i} = new Form_SubForm($this);
${"partner" . $i}->setAttrib('title', $i.'e ' .ucfirst(translate()->_('occupant')));
$this->addSubForm(${"partner" . $i}, 'partner_form'.$i);

// id
${"partner" . $i}->addElement('hidden', $partnername, array(
));

// geslacht
${"partner" . $i}->addElement('select', $partnername.'_gender', array(
// NOTE: make sure to add all fields of the partner_subform which do not have an empty default value
// to the $excludeFromPartnerIsEmptyCheck array inside Users->partnerSubformContainsData()
    'label' => ucfirst(translate()->_('gender')),
    'multiOptions'	=> array(
        'female' => ucfirst(translate()->_('gender_female')),
        'male' => ucfirst(translate()->_('gender_male')),
        'unknown' => ucfirst(translate()->_('unknown')),
        'none' => ucfirst(translate()->_('none'))
    ),
    'registerInArrayValidator' => false,
));


// voorletters
${"partner" . $i}->addElement('text', $partnername.'_initials', array(
    'label' => ucfirst(translate()->_('initials')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	100,
            )
        )
    )
));

// voornaam
${"partner" . $i}->addElement('text', $partnername.'_firstname' , array(
    'label' => ucfirst(translate()->_('firstname')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	100,
            )
        )
    )
));

// tussenvoegsel
${"partner" . $i}->addElement('text', $partnername.'_middlename' , array(
    'label' => ucfirst(translate()->_('middlename')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	20,
            )
        )
    )
));

// achternaam
${"partner" . $i}->addElement('text', $partnername.'_name' , array(
    'label' => ucfirst(translate()->_('surname')),
    'attribs' => array(
        'force_column_break' => true,
    ),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	100,
            )
        )
    )
));

// BSN
${"partner" . $i}->addElement('select', $partnername.'_identication_type', [
// NOTE: make sure to add all fields of the partner_subform which do not have an empty default value
// to the $excludeFromPartnerIsEmptyCheck array inside Users->partnerSubformContainsData()
    'label' => ucfirst(translate()->_('identification_type')),
    'multiOptions'	=> Users::$identificationTypeLabels,
]);

// BSN
${"partner" . $i}->addElement('text', $partnername.'_BSN' , [
    'label' => ucfirst(translate()->_('social_security_identifier')),
    'validators' => [
        [
            'validator'	=> 'stringLength',
            'options'	=> [
                'max' =>	20,
            ]
        ]
    ]
]);

${"partner" . $i}->addElement('text', $partnername.'_identication_valid_till' , [
    'label' => ucfirst(translate()->_('identification_valid_till')),
    'attribs' => [
        'class' => 'DatePicker',
    ],
    'validators' => [
        [
            'validator'	=> 'Date',
            'options'	=> [
                'locale' => 'nl',
            ]
        ]
    ]
]);


// geboortedatum
${"partner" . $i}->addElement('text', $partnername.'_bdate' , array(
    'label' => ucfirst(translate()->_('birth')),
    'attribs' => array(
        'class' => 'DatePicker',
        'datepicker_options' => json_encode(array(
            'yearRange' => date('Y') - 1900,
            'yearStart' =>  1900
        ))
    ),
    'validators' => array(
        array(
            'validator'	=> 'Date',
            'options'	=> array(
                'locale' => 'nl',
            )
        )
    )
));

// birthplace
${"partner" . $i}->addElement('text', $partnername.'_birthplace' , [
    'label' => ucfirst(translate()->_('birthplace')),
] );


// email
${"partner" . $i}->addElement('text', $partnername.'_email_address' , array(
    'label' => translate()->_('email'),
    'validators' =>  array('EmailAddress'),
));

if(Settings::get('software_country') === 'be') {
    ${"partner" . $i}->addElement('text', $partnername.'_email_parent' , [
        'label' => translate()->_('email_parent'),
        'validators' => ['EmailAddress'],
    ]);
}

${"partner" . $i}->addElement('text', $partnername.'_phone_secondary' , array(
    'label' => ucfirst(translate()->_('mobile')),
    'validators' => array( 'PhoneNumber' ),
));

// telefoonnummers
${"partner" . $i}->addElement('text', $partnername.'_phone_primary' , array(
    'label' => ucfirst(translate()->_('phone')),
    'validators' => array( 'PhoneNumber' ),
));



${"partner" . $i}->addElement('text', $partnername.'_phone_emergency' , array(
    'label' => ucfirst(translate()->_('emergency_contactphone')),
    'validators' => array( 'PhoneNumber' ),
    'attribs' => array(
        'force_column_break' => true,
    ),
));






${"partner" . $i}->addElement('text', $partnername.'_free_field_1' , array(
    'label' => ucfirst(translate()->_('free_field_1')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	255,
            )
        )
    )
));

${"partner" . $i}->addElement('text', $partnername.'_free_field_2' , array(
    'label' => ucfirst(translate()->_('free_field_2')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	255,
            )
        )
    )
));

// partner language
${"partner" . $i}->addElement('select', $partnername.'_language' , [
// NOTE: make sure to add all fields of the partner_subform which do not have an empty default value
// to the $excludeFromPartnerIsEmptyCheck array inside Users->partnerSubformContainsData()
    'label' => 'Taal',
    'multiOptions' => $languageOptions,
]);





// bedrijfsnaam
${"partner" . $i}->addElement('text', $partnername.'_corporation' , array(
    'label' => ucfirst(translate()->_('company')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	255,
            )
        )
    )
));


    ${"partner" . $i}->addElement('select', $partnername.'_source_of_income' , [
// NOTE: make sure to add all fields of the partner_subform which do not have an empty default value
// to the $excludeFromPartnerIsEmptyCheck array inside Users->partnerSubformContainsData()
        'label' => ucfirst(translate()->_('source_of_income')),
        'multiOptions' => [
            '' => 'onbekend',
            'Loondienst (onbepaald)' => 'Loondienst (onbepaald)',
            'Loondienst (tijdelijk)' => 'Loondienst (tijdelijk)',
            'pensioen' => 'Pensioen',
            'uitkering' => 'Uitkering',
            'ondernemer' => 'Ondernemer / ZZP',
            'uitzendkracht' => 'Uitzendkracht',
        ]
    ]);

// Bruto inkomen
    ${"partner" . $i}->addElement('text', $partnername.'_income_amount' , array(
        'label' => ucfirst(translate()->_('income_amount')),
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	255,
                )
            )
        )
    ));

    ${"partner" . $i}->addElement('select', $partnername.'_living_situation' , [
// NOTE: make sure to add all fields of the partner_subform which do not have an empty default value
// to the $excludeFromPartnerIsEmptyCheck array inside Users->partnerSubformContainsData()
        'label' => ucfirst(translate()->_('living_situation')),
        'multiOptions' => [
            '' => 'onbekend',
            'Koopwoning (verkocht)' => 'Koopwoning (verkocht)',
            'Koopwoning (niet verkocht)' => 'Koopwoning (niet verkocht)',
            'koopwoning' => 'Koopwoning',
            'huurwoning' => 'Huurwoning',
            'thuiswonend' => 'Thuiswonend'
        ]
    ]);

if($this->getOwner()->userid > 0)
${"partner" . $i}->addElement('html', $partnername.'_unlink', array(
'label' => 'Loskoppelen',
'validators' => [],
'html' => '<input id="partner_unlink_'.$i.'" type="button" value=" '.$i.'e bewoner loskoppelen"></input>'
));

?>
