<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Instellingen '). ucfirst(Settings::get('financial_export_system')));
    $this->addSubForm($first, 'first');



    if (Settings::get('financial_export_system') === 'exactonline') {
        include 'exactonline/settings.php';
    } elseif (Settings::get('financial_export_system') === 'twinfield') {
        include 'twinfield/settings.php';
    } elseif (Settings::get('financial_export_system') === 'yuki') {
        include 'yuki/settings.php';
    } elseif (Settings::get('financial_export_system') === 'afas') {
        include 'afas/settings.php';
    } elseif (Settings::get('financial_export_system') === 'multivers') {
        include 'multivers/settings.php';
    } elseif (Settings::get('financial_export_system') === 'hb') {
        include 'hb/settings.php';
    }

    
    $first->addElement('checkbox', 'user_olddebtorcode_as_identifier', [
        'label' => 'Huurder financiele uitwisseling id als klantnummer gebruiken',
    ]);



if (FinancialExportSystems::projectRelationCodesAreAllowed()) {
	$first->addElement('checkbox', 'financial_export_system_project_relation_code_enabled', [
		'label' => 'Project- en relatiecodes ingeschakeld',
		'attribs' => [
			'title' => 'Maakt het mogelijk om op projecten een projectcode en een relatiecode in te voeren '
				. 'en beiden op een object te overschrijven. De uiteindelijke codes worden (indien ingevuld) gebruikt '
				. 'in de export naar het financiële systeem.',
			'class' => 'forceAutoHint',
		]
	]);
}

	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
