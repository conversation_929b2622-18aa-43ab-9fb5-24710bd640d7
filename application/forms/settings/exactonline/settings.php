<?php

if (loginManager::data()->rights === 'admin') {
    $first->addElement('checkbox', 'exact_online_attach_invoice_pdf', [
        'label' => 'Verkoop/Credit factuur mee sturen tijdens factuur uitwisseling',
        'attribs' => [
            'title' => 'Dit zorgt er voor dat er na het uitwisselen van de factuur run, de onderliggende PFD\'s <PERSON><PERSON> voor één er achteraan verstuurd worden. ',
            'class' => 'forceAutoHint'
        ]
    ]);
}



    $first->addElement('checkbox', 'financial_export_exactonline_invoices_using_api', [
        'label' => 'Exact Online live uitwisseling van facturen',
        'attribs' => [
            'title' => 'Live uitwisseling moet in afstemming met de klant ingeschakeld worden, 
            	deze setting kan worden verwijderd als alle klanten gemigreerd zijn naar live uitwisseling.<br><br>
            	Let op: om dit te laten werken moet Omniboxx ook toegang hebben gekregen tot Exact. Ga hiervoor naar 
            	"Instellingen > Koppelingen > Financieel systeem" en volg de instructies.',
            'class' => 'forceAutoHint'
        ]
    ]);


    $allowed_variables = [
        'firstname' => 'Voornaam',
        'initials' => 'Initialen',
        'name' => 'Achternaam',
    ];

    $variables_string = '';

    foreach($allowed_variables as $allowed_variable_name => $allowed_variable_value)
        $variables_string .= '<b>[' . $allowed_variable_name . ']</b>: ' . $allowed_variable_value . '<br />';

    $first->addElement('text', 'financial_export_private_debtor_name', [
        'label' => 'Aangepaste particuliere debiteurnaam',
        'attribs' => [
            'placeholder' => '[firstname] [middlename] [name]',
            'title' => 'Samenstelling van de naam van de particuliere debiteur bij de financiele koppeling.'
				. "<br /><br />Variabelen: <br /> $variables_string"
				. "<br /><br /><b>Voorbeeld</b>:<br />'[firstname] [middlename] [name]' wordt weergegeven als 'Arie de Boer'",
            'class' => 'forceAutoHint',
        ]
    ]);

	$first->addElement('text', 'financial_export_exactonline_account_code_offset', [
		'label' => 'Debiteur nummer ophoging',
		'attribs' => [
			'title' => 'Hier kan een numerieke waarde ingevuld worden. Bij de uitwisseling naar Exact zal deze waarde
			 bij de debiteurcode van de debiteur opgeteld worden, tenzij deze debiteurcode overschreven is d.m.v. het 
			 "deb. code financieel" veld. Dit kan gebruikt worden om te voorkomen dat Omniboxx de gegevens van (buiten 
			 Omniboxx om aangemaakte) debiteuren in Exact overschrijft door toevallig hetzelfde debiteurnummer te 
			 genereren.',
			'class' => 'forceAutoHint',
		]
	]);

    $first->addElement('checkbox', 'financial_export_exactonline_easy_matching_descriptions', [
        'label' => 'Exact Online betere match omschrijvingen',

		'attribs' => [
			'title' => 'Deze instelling veranderd de inhoud van een aantal velden in de uitvoer naar Exact. 
				Deze zijn er initieel op gericht het matchen van betalingen makkelijker te maken, vandaar de naam.
				De wijzigingen zijn als volgt:<br><br>

				1/ Het "Uw Ref" veld gaat het Omniboxx factuurnummer bevatten ipv leeg zijn.<br><br>
				
				2/ Het "Omschrijving" veld van de factuurregels wordt gebaseerd op de omschrijving in Omniboxx, wat 
				meestal neerkomt op het component (zoals Huur).<br><br>
				
				3/ Het "Omschrijving" veld van de factuur wordt gebaseerd op de factuur omschrijving, vaak iets als 
				"Huur september".<br><br>
				
				4/ Het inkoopdagboek en verkoopdagboek krijgen de namen \'Inkoopdagboek\' en \'Verkoopdagboek\' ipv de 
				factuurnaam van de laatste factuur die geïmporteerd hebt.<br><br>
				',
			'class' => 'forceAutoHint',
		]
    ]);


$first->addElement('checkbox', 'financial_export_exactonline_naw_info', [
    'label' => 'Exporteer adres, telefoon en email mee in de koppeling',
]);

	$first->addElement('checkbox', 'exact_online_dd_mandates_enabled', [
		'label' => 'Automatische incasso en betalingscondities meesturen',
		'attribs' => [
			'title' => 'Stuur naar Exact welke van de twee hieronder staande betalingscondities van toepassing '
				.'is op een bepaalde factuur (afhankelijk van de "Type betaling" instelling bij de relatie waar '
				.'de factuur voor is).',
			'class' => 'forceAutoHint',
		]
	]);

	$first->addElement('text', 'exact_online_dd_mandates_payment_condition_code_sales_dd', [
		'label' => 'Betalingsconditie (automatische) incasso',
        'title' => 'Dit is de instelling in Exact online voor de automatische incasso (meestal IN).',
        'class' => 'forceAutoHint'
	]);

	$first->addElement('text', 'exact_online_dd_mandates_payment_condition_code_sales_cash', [
		'label' => 'Betalingsconditie handmatige betaling/"Op krediet"',
        'title' => 'Dit is de instelling in Exact online voor de handmatig / betalen op krediet.',
        'class' => 'forceAutoHint'
	]);

    $first->addElement('checkbox', 'financial_tax_seven_percent_rate_enabled', [
        'label' => '7% BTW tarief (Duitsland)',
        'attribs' => [
            'class' => 'forceAutoHint',
        ]
    ]);

    $first->addElement('text', 'financial_export_sales_vat_seven_percent', [
        'label' => 'BTW 7% (Duitsland)',
        'attribs' => [
            'title' => 'Grootboekrekening van BTW 7% verkoop in de boekhouding (bv 1510)',
            'class' => 'forceAutoHint'
        ]
    ]);
    $first->addElement('text', 'financial_export_sales_vat_seven_percent_code', [
        'label' => 'BTW Code 7% (Duitsland) (excl)',
        'attribs' => [
            'title' => 'Code van BTW 7% verkoop in de boekhouding (bv 03)',
            'class' => 'forceAutoHint'
        ]
    ]);

    $first->addElement('checkbox', 'financial_oauth_manager', [
        'label' => 'Exact online oauth manager (beta)',
        'attribs' => [
            'title' => 'Exact online oauth manager',
            'class' => 'forceAutoHint'
        ]
    ]);
