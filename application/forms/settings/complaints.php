<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $complaint = new Form_SubForm($this);
    $complaint->setAttrib('title', 'Algemene instellingen');
    $this->addSubForm($complaint, 'complaint');

    $email = new EmailAccounts();
    
    $complaint->addElement('text', 'complaint_added_email', array(
        'label' => 'E-mail bij nieuwe melding sturen naar',
        'title' => 'Indien nieuwe melding wordt toegevoegd een mail sturen naar dit mailadres',
        'class' => 'forceAutoHint',
    ));

    $complaint->addElement('checkbox', 'components_on_complaints', [
        'label' => 'Componenten bij meldingen',
        'title' => 'Meldingen in bulk van status kunnen veranderen',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'complaint_bulk_actions', [
        'label' => 'Meldingen dashboard, bulk-acties',
        'title' => 'Meldingen in bulk van status kunnen veranderen',
        'class' => 'forceAutoHint',
     ]);

    $complaint->addElement('checkbox', 'support_complaint_projects', [
        'label' => 'Werken activeren',
        'title' => 'Hiermee kunnen meerdere meldingen onder 1 project vallen -> Indien werken ook opdrachtwaarden activeren',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'support_complaint_budgets', [
        'label' => 'Opdrachtwaarden activeren',
        'title' => 'Met deze setting kan per melding een opdrachtwaarde ingevoerd worden. Dit wordt in de mail als maximaal budget naar de leveranciers vermeldt.
                    Indien werken geactiveerd zijn telt deze waarde ook mee in voortgang van het budget van de werken.',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'support_complaint_autorisation', [
        'label' => 'Autorisaties activeren',
        'title' => 'Autorisaties per categorie en bedrag ingeven en autorisatie pagina voor meldingen definitief maken',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'support_complaint_update_appointment_text', [
        'label' => 'Tekst van een afspraak bij een storing aanpassen achteraf',
        'title' => 'Hiermee kan achteraf de tekst bij een afspraak aangepast worden. Normaliter uit omdat de tekst anders kan afwijken met wat naar de leverancier is gemaild',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'support_complaint_reassign_contact', [
        'label' => 'Servicepartij aanpassen toestaan',
        'title' => 'Toestaan om achteraf de servicepartij aan te passen, let op; vooralsnog worden hierbij deadlines e.d. niet vernieuwd voor de nieuwe servicepartij',
        'class' => 'forceAutoHint',
    ]);

    $complaint->addElement('checkbox', 'complaint_create_invoice_when_done', [
        'label' => 'Factuur maken bij afhandelen',
        'title' => 'Let op; vooralsnog enkel toegepast bij het bulk-bewerken in het "Storingen & meldingen" dashboard.',
        'class' => 'forceAutoHint',
    ] );

    $complaint->addElement('text', 'complaint_dashboard_limit_months', [
        'label' => 'Aantal maanden terug dat het dahsboard storingen laat zien',
        'title' => 'V.b. indien 6 wordt ingevuld toont dit overzicht meldingen die maximaal 6 maanden geleden zijn aangemaakt. Dit veld leeglaten indien u geen beperking wil. Let op: wanneer u meldingen niet netjes afhandeld kan het dashboard traag worden door de vele oude meldingen.',
        'class' => 'forceAutoHint',
        'validators' => [
            [
                'greaterThan',
                false,
                [0],
            ],
        ],
    ] );

    $complainttimes = new Form_SubForm($this);
    $complainttimes->setAttrib('title', 'Response tijden');
    $this->addSubForm($complainttimes, 'complaintimes');


    $complainttimes->addElement('text', 'complaint_responsetime_malfunction', [
        'label' => 'Reactietijd storing (uren)',
        'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_responsetime_malfunction'],
    ] );

    $complainttimes->addElement('text', 'complaint_responsetime_urgent_malfunction', [
        'label' => 'Reactietijd urgente storing (uren)',
        'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_responsetime_urgent_malfunction'],
    ] );

    $complainttimes->addElement('text', 'complaint_responsetime_complaint', [
        'label' => 'Reactietijd melding/klacht (uren)',
        'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_responsetime_complaint'],
    ] );

    $complainttimes->addElement('checkbox', 'complaint_use_assigntime', [
        'label' => 'Toewijstijd meldingen in gebruik',
        'title' => 'Maak gebruik van de onderstaande "Toewijstijd" instelling, zie daar voor toelichting.',
        'class' => 'forceAutoHint',
    ] );

    $complainttimes->addElement( 'text', 'complaint_assigntime_complaint', [
        'label' => 'Toewijstijd melding (uren)',
        'title' => 'Standaard waarde voor de instelling die er voor zorgt dat bij nieuwe, niet
				toegewezen meldingen een deadline aangemaakt wordt die verwijderd wordt als de melding toegewezen wordt.',
        'class' => 'forceAutoHint',
        'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_assigntime_complaint'],
    ] );

    $serviceparties = new Form_SubForm($this);
    $serviceparties->setAttrib('title', 'Storingdienst portal');
    $this->addSubForm($serviceparties, 'serviceparties');


    $serviceparties->addElement('checkbox', 'serviceparty_disable_assign_technical_party', [
        'label' => 'Storingsdienst mag melding NIET naar een servicepartij uitzetten',
        'title' => 'Hiermee verdwijnt de optie in de portal van de storingdienst om een servicepartij aan een melding te koppelen.',
        'class' => 'forceAutoHint',
    ] );


    $technicalparties = new Form_SubForm($this);
    $technicalparties->setAttrib('title', 'Technische partijen portal');
    $this->addSubForm($technicalparties, 'technicalparties');


    $technicalparties->addElement('checkbox', 'technicalparty_enable_edit_inform_note', [
        'label' => 'Technische partij mag Toelichting voor servicepartij bewerken op de portal',
        'class' => 'forceAutoHint',
    ] );


    $complaintmailgeneral = new Form_SubForm($this);
    $complaintmailgeneral->setAttrib('title', 'E-mail algemeen');
    $this->addSubForm($complaintmailgeneral, 'complaintmailgeneral');

    $complaintmailgeneral->addElement('select', 'complaints_mail_account', array(
        'label' => 'E-mail account voor verzending van klachten en storing mails',
        'multioptions' 	=> $email->getForForm(),
        'attribs' => array ('style' => 'width: 300px'),
        'title' => 'Stel een afwijkend mail account in voor verzending van klachten en storingen. Deze overrulled andere instellingen in Omniboxx ingesteld als standaard of project',
        'class' => 'forceAutoHint',
    ));


    $complaintmailgeneral->addElement('checkbox', 'complaints_mail_share_technical_party_contact_data_open_status', array(
        'label' => 'Deel bij open status al de contactgegevens van de technische partij',
        'attribs' => array ('style' => 'width: 300px'),
        'title' => 'Het wordt de taak van de huurder om contact op te nemen met de technische partij in plaats van andersom. In de mail naar de huurder komt het verzoek contact op te nemen met de technische partij. In de mail naar de technische partij wordt alsnog de gegevens gedeeld van de huurder het verzoek contact op te nemen.',
        'class' => 'forceAutoHint',
    ));


    $complaintmailgeneral->addElement('checkbox', 'complaint_send_mail_callcenter', [
        'label' => 'Indien melding wordt aangemaakt door Storingsdienst ook cc sturen naar mail adres van Storingsdienst ',
    ]);





    $complaintmailtenant = new Form_SubForm($this);
    $complaintmailtenant->setAttrib('title', 'E-mail huurder');
    $this->addSubForm($complaintmailtenant, 'complaintmailtenant');

    $complaintmailtenant->addElement('checkbox', 'complaint_add_employee_info_to_mail', [
        'label' => 'Voeg medewerker info toe aan standaard mail',
        'title' => 'Zet de naam, het telefoonnummer en de aanwezigheid gegevens van de interne medewerker
							die aan een melding gekoppeld is in de e-mails die een klant informeren over de status van een
							melding. Deze instelling kan per project overschreven worden. Als u de persoonlijke pagina
							module gebruikt kunnen klanten deze gegevens daar ook inzien voor hun openstaande meldingen.',
        'class' => 'forceAutoHint',
    ] );

    $complaintmailtenant->addElement('checkbox', 'complaint_hide_phone_option_tenant', [
        'label' => 'Verberg telefoonnummer in mails naar huurder. ',
    ]);

    $complaintmailtenant->addElement('text', 'complaint_general_phone', [
        'label' => 'Algemeen telefoonnummer in mails naar huurder voor meldingen. ',
    ]);


    $complaintmailtenant->addElement('checkbox', 'inform_solution_tenant', [
        'label' => 'De oplossing van de melding ook in de mail naar de huurder meesturen.'
    ]);


    $complaintmailtenant->addElement('textarea', 'extra_info_tenant_mail_support_nl', array(
        'label' => 'Standaard tekst toegevoegd aan mail bij melding aan huurder (NL)',
        'rows' => 7,
        'cols' => 35,
        'title' => 'Deze tekst wordt toegevoegd aan de standaard Omniboxx mail aan huurder.',
        'class' => 'forceAutoHint',
    ));

    $complaintmailtenant->addElement('textarea', 'extra_info_tenant_mail_support_en', array(
        'label' => 'Standaard tekst toegevoegd aan mail bij melding aan huurder (EN)',
        'rows' => 7,
        'cols' => 35,
        'title' => 'Deze tekst wordt toegevoegd aan de standaard Omniboxx mail aan huurder.',
        'class' => 'forceAutoHint',
    ));




    $complaintmail = new Form_SubForm($this);
    $complaintmail->setAttrib('title', 'E-mail technische partij');
    $this->addSubForm($complaintmail, 'complaintmail');



    $complaintmail->addElement('checkbox', 'complaint_add_corporation_info_to_mail', array(
        'label' => 'Voeg BV info toe aan standaard mail',
        'title' => 'Voeg de gegevens van de juridische entiteit toe aan de mail voor technische partij t.b.v. facturatie',
        'class' => 'forceAutoHint',

    ));

    $complaintmail->addElement('checkbox', 'complaint_add_investor_info_to_mail', array(
        'label' => 'Als een belegger bekend is voeg belegger naam en  p/a info toe aan standaard mail',
        'title' => 'Voeg de naam van de belegger toe aan de mail voor technische partij t.b.v. facturatie',
        'class' => 'forceAutoHint',
    ));



    $complaintmail->addElement('checkbox', 'complaint_add_component_ledger_to_mail', array(
        'label' => 'Voeg grootboek toe aan standaard mail',
        'title' => 'Voegt het grootboek van het component toe aan de mail voor de technische partij. <b>Werkt alleen als de setting "Componenten bij meldingen" aanstaat</b>',
        'class' => 'forceAutoHint',

    ));

    $complaintmail->addElement('checkbox', 'complaint_add_component_code_to_mail', array(
        'label' => 'Voeg code van het component toe aan standaard mail',
        'title' => 'Voegt het code van het component toe aan de mail voor de technische partij. <b>Werkt alleen als de setting "Componenten bij meldingen" aanstaat</b>',
        'class' => 'forceAutoHint',
    ));

    $complaintmail->addElement('checkbox', 'complaint_add_costcenter_to_mail', array(
        'label' => 'Voeg kostenplaats info toe aan standaard mail',
        'title' => 'Voegt de kosten plaats van project of object toe aan de mail voor technische partij t.b.v. facturatie',
        'class' => 'forceAutoHint',

    ));

    $complaintmail->addElement('checkbox', 'complaint_dont_send_mail', [
        'label' => 'Geen mail sturen naar leverancier bij meldingen',
        'title' => 'Standaard gaat een mail naar de leverancier met opdrachtbon bij een melding. Wilt u dit niet, selecteer deze optie',
        'class' => 'forceAutoHint',
    ]);

    $complaintmail->addElement('checkbox', 'complaint_send_mail_callcenter', [
        'label' => 'Indien melding wordt aangemaakt door Storingsdienst ook cc sturen naar mail adres van Storingsdienst ',
    ]);


    $complaintmail->addElement('checkbox', 'complaint_hide_phone_option_service_party', [
        'label' => 'Verberg telefoonnummer in mails naar technische partijen. ',
    ]);

    $complaintmail->addElement('checkbox', 'option_disable_automatic_appointment', [
        'label' => 'Standaard geen afspraak link in melding mail',
        'title' => 'Standaard zit een link voor het maken van een afsraak in de mail naar de technische partij, '
            . 'wilt u dit per melding zelf bepalen selecteer deze optie',
        'class' => 'forceAutoHint',
    ]);

    $complaintmail->addElement('checkbox', 'option_disable_all_buttons', [
        'label' => 'Standaard geen enkele button/link in melding mail',
        'title' => 'Standaard zitten er 3 knopppen in de mail in de mail naar de technische partij, '
            . 'wilt u standaard alle buttons uitschakelen selecteer deze optie',
        'class' => 'forceAutoHint',
    ]);

    $complaintmail->addElement('text', 'complaint_add_digital_mailaddress', [
        'label' => 'Digitale mailbox voor inkoopfacturen',
        'title' => 'Het mailadres waarna leveranciers de factuur kunnen mailen',
        'validators' =>  array('EmailAddress'),
        'class' => 'forceAutoHint',
    ] );

    $complaintmail->addElement('textarea', 'extra_info_external_support', array(
        'label' => 'Standaard tekst toegevoegd aan mail bij opdracht aan technische partijen',
        'rows' => 7,
        'cols' => 35,
        'title' => 'Deze tekst wordt toegevoegd aan de standaard Omniboxx mail aan technische partijen. Hier kunt u bijvoordeeld een standaard mandaat bedrag of andere standaard wensen bij de uitvoering toevoegen',
        'class' => 'forceAutoHint',
    ));

    $this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
