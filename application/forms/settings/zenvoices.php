<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');
global $config;

$general->addElement('checkbox', 'zenvoices_module', array(
    'label' => 'Zenvoices Module',
    'attribs' => [
        'title' => 'Hier me zet je de Zenvoices module aan om bijlagens te downloaden',
        'class' => 'forceAutoHint'
    ]
));


$general->addElement('text', 'zenvoices_tenant', array(
    'label' => 'Zenvoices Omgevingsnaam',
    'style' => 'width:330px',
    'attribs' => [
        'title' => '',
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('text', 'zenvoices_username', array(
    'label' => 'Zenvoices Gebruikersnaam of e-mailadres',
    'style' => 'width:330px',
    'attribs' => [
        'title' => '',
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('password', 'zenvoices_password', array(
    'label' => 'Zenvoices Wachtwoord',
    'renderPassword' => true,
    'style' => 'width:330px',
    'attribs' => [
        'title' => '',
        'class' => 'forceAutoHint'
    ]
));


$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
