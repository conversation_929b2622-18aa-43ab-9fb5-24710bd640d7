<?php

$first->addElement('text', 'financial_export_yuki_accesskey', [
    'label' => 'Yuki API-sleutel',
    'attribs' => [
        'title' => 'Voer hier de geheime sleutel in die Omniboxx toegang geeft tot uw Yuki omgeving. Hoe u deze aanmaakt is te vinden in de Yuki documentatie onder "Webservice toegangscode".',
        'class' => 'forceAutoHint',
    ],
] );

$first->addElement('checkbox', 'financial_export_send_management_fee_rows_only', [
    'label'   => 'Alleen "'.translate()->_('management_fee').'" regels doorsturen',
    'attribs' => [
        'title' => 'Stuur, van beheerprovisie facturen, alleen de factuurregels genaamt "'.translate()->_('management_fee').'" door via de financiele koppeling. Alleen van toepassing op de Yuki koppeling.',
        'class' => 'forceAutoHint',
    ],
]);


$first->addElement('checkbox', 'booking_period_equals_expire_date', [
    'label' => 'Boekperiode gelijk aan vervaldatum',
    'attribs' => [
        'title' => 'Hiermee wordt alleen de boekingperiode voor financiele koppelingen gelijkgesteld aan '
            . 'de periode van de vervaldatum. De factuurdatum blijft gelijk aan de dag van verzending.',
        'class' => 'forceAutoHint',
    ]
]);
