<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Logo briefpapier'));
    $this->addSubForm($first, 'first');

    $uploadedletterhead = false;
    if (Settings::get('upload_letterhead')) {
        $images = new Images();
        $uploadedletterhead = $images->get(
            Settings::get('upload_letterhead')
        );
    }

    $first->addElement('text', 'current_uploaded_letterhead', array(
        'label' => 'Huidig briefpapier',
        'value' => $uploadedletterhead ? $uploadedletterhead['filename'] : false
    ));


    $first->addElement('text', 'upload_letterhead', array(
        'label' => 'Logo briefpapier instellen',
        'attribs' => array(
            'class' => 'imageUpload',
            'image-upload-type' => 'stationary_logo'
        )
    ));

    $first->addElement('text', 'invoice_footer_color', [
        'label' => 'Kleur lijn footer factuur',
        'attribs' => [
            'title' => 'Let op: moet ingevoerd worden als een HEX kleurcode, beginnend met een #',
            'class' => 'forceAutoHint',
        ],
        'validators' => [
            [
                'regex',
                false,
                'options' => [
                    'pattern' => '/^\#[a-zA-Z0-9]{6}$/'
                ]
            ]
        ]
    ]);


	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
