<?php

$first->addElement('text', 'financial_export_system_multivers_client_id', [
    'label' => 'Client ID',
    'attribs' => [
        'title' => 'Deze is te verkrijgen wanneer er aan de kant van Multivers een api applicatie aangemaakt wordt. Bekijk de Multivers handleiding voor instructies.',
        'class' => 'forceAutoHint',
    ]
]);

$first->addElement('text', 'financial_export_system_multivers_client_secret', [
    'label' => 'Client Secret',
    'attribs' => [
        'title' => 'Deze is te verkrijgen wanneer er aan de kant van Multivers een api applicatie aangemaakt wordt. Bekijk de Multivers handleiding voor instructies.',
        'class' => 'forceAutoHint',
    ]
]);

$first->addElement('text', 'financial_export_payment_condition', [
    'label' => 'Betalingsconditie',
    'attribs' => [
        'title' => 'Tot op heden enkel voor Multivers koppeling, verwacht een nummerieke waarde van de betalings conditie instelling',
        'class' => 'forceAutoHint',
    ]
]);
