<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');
global $config;

$general->addElement('text', 'days_between_invoice_and_expiredate_on_purchase_invoice', array(
    'label' => 'Dagen vanaf factuurdatum voor invullen vervaldatum',
    'value' => 30,
    'validators' =>
        [
            ['validator' => 'digits'],
            ['validator' => 'stringLength', 'options'	=> ['min' => 1, 'max' => 2]],
            [
            'validator' => 'Between',
                'options' => [
                    'min' => 1,
                    'max' => 99,
                ]
            ]
        ],
    'attribs' => [
        'title' => 'Vul hier een getal groter dan 1 in om tussen factuurdatum en vervaldatum automatisch een berekening te laten uitvoeren. Wordt dit niet ingevoerd dan wordt standaard 30 dagen gehanteerd',
        'class' => 'forceAutoHint'
    ]

));

if (Settings::get('for_third_party')) {
    $general->addElement('checkbox', 'default_dont_send_invoice_attachment_on_investor_report', array(
        'label' => 'Standaard bijlage niet meesturen met de beleggerafrekening',

    ));
}

$general->addElement('checkbox', 'default_make_payment_on_adding_purchase', array(
    'label' => 'Standaard overboekingsopdracht maken bij inboeken inkoopfactuur',

));


$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
