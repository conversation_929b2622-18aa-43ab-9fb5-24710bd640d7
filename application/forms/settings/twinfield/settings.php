<?php /** @noinspection PhpUnhandledExceptionInspection */


$first->addElement('text', 'financial_export_twinfield_last_import_date', [
    'label' => 'Twinfield laatste datum waarop betalingen zijn ingelezen',
    'attribs' => [
        'title' => 'Formaat = yyyy-mm-dd, wordt door de code zelf aangepast bij het inlezen.',
        'class' => 'forceAutoHint',
    ]
]);

$first->addElement('checkbox', 'financial_export_twinfield_exchange_address_data', [
    'label' => 'Wissel adres/telefoon/email gegevens uit',
    'attribs' => [
        'title' => 'Als tijdens het uitwisselen van een factuur de relatie gegevens aan Twinfield doorgegeven worden, '
            . 'geef dan ook (factuur) NAW, telefoon en e-mail gegevens door (voor zover aanwezig).<br/>' .
            'Let op; Twinfield accepteert deze aanvullende gegevens enkel wanneer er bij de huurder in Omniboxx ' .
            'bij de facturatie voorkeuren een factuuradres ingesteld is.'
        ,
        'class' => 'forceAutoHint',
    ]
]);


$first->addElement('checkbox', 'financial_export_disable_period', [
    'label' => 'Periode niet meegeven in uitwisseling',
    'attribs' => [
        'title' => 'Met deze instelling wordt de periode gebaseerd op de datum van de factuur en niet door de'
            . ' meegegeven periode. Bv in het geval van gebroken boekjaar is deze setting nodig om de uitwisseling'
            . ' te laten werken',
        'class' => 'forceAutoHint',
    ]
]);

$allowed_variables = [
    'short_period_name' => 'De afgekorte periode van de factuur (bijvoorbeeld Q3 ' . date('Y') . ')',
    'period_name' => 'De periode van de factuur',
    'name' => 'De volledige naam van de debiteur',
    'address_specification' => 'De nummer specificatie van het object'
];

$variables_string = '';
foreach ($allowed_variables as $allowed_variable_name => $allowed_variable_value) {
    $variables_string .= '<b>[' . $allowed_variable_name . ']</b>: ' . $allowed_variable_value . '<br />';
}

$first->addElement('text', 'financial_export_row_description', [
    'label' => 'Aangepaste omschrijving boekingsregels',
    'attribs' => [
        'title' => 'Samenstelling van de omschrijving van de boekingsregels bij de financiele koppeling. '
            . '(tot op heden; enkel Twinfield)<br /><br />Variabelen: <br />' . $variables_string . '<br /><br /><b>'
            . 'Voorbeeld</b>:<br />\'[name] - [period_name]\' wordt weergegeven als '
            . '\'Dhr. A. de Boer - Factuur augustus 2016\'',
        'class' => 'forceAutoHint',
    ]
]);

$first->addElement('checkbox', 'booking_period_equals_invoice_date', [
    'label' => 'Boekperiode gelijk aan factuurdatum',
    'attribs' => [
        'title' => 'Hiermee wordt alleen de boekingperiode voor Twinfield gelijkgesteld aan '
            . 'de factuurdatum. De factuurdatum blijft gelijk aan de dag van verzending. Is dit alleen voor commercieel vastgoed wenselijk, zet dan deze instelling uit en onderstaande setting aan.',
        'class' => 'forceAutoHint',
    ]
]);

$first->addElement('checkbox', 'twinfield_invoice_date_equals_expire_date', [
    'label' => 'Factuurdatum gelijk aan vervaldatum',
    'attribs' => [
        'title' => 'Hiermee wordt  de factuurdatum in Twinfield boeking gelijkgesteld aan '
            . 'de Vervaldatum. De factuurdatum op de factuur blijft gelijk aan de dag van verzending. Als deze instelling aan staat hoeft onderstaande instelling niet aan.',
        'class' => 'forceAutoHint',
    ]
]);


$first->addElement('checkbox', 'invoice_date_equals_expire_date_only_for_residential', [
    'label' => 'Factuurdatum gelijk aan vervaldatum alleen voor residentieel vastgoed',
    'attribs' => [
        'title' => 'Hiermee wordt  de factuurdatum in Twinfield boeking gelijkgesteld aan '
            . 'de Vervaldatum. De factuurdatum op de factuur blijft gelijk aan de dag van verzending.',
        'class' => 'forceAutoHint',
    ]
]);



$first->addElement('checkbox', 'booking_period_equals_expire_date_only_for_residential', [
    'label' => 'Boekperiode gelijk aan factuurdatum alleen voor commercieel vastgoed',
    'attribs' => [
        'title' => 'Hiermee wordt de boekingperiode voor financiele koppeling gelijkgesteld aan '
            . 'de periode van de factuurdatum voor commercieel vastgoed.',
        'class' => 'forceAutoHint',
    ]
]);
