<?php

$first->addElement('text', 'financial_export_hb_base_uri', [
    'label' => 'Navision Base URL',

]);

$first->addElement('text', 'financial_export_hb_base_path', [
    'label' => 'Navision Base Path',

]);

$first->addElement('text', 'financial_export_hb_auth_user', [
    'label' => 'Navision Username',

]);

$first->addElement('text', 'financial_export_hb_auth_password', [
    'label' => 'Navision Password',

]);

$first->addElement('checkbox', 'cronjob_send_navision_contacts', [
    'label' => 'Stuur dagelijks debiteuren automatisch naar Navision'
]);
