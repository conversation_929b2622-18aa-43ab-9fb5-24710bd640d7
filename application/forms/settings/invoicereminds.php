<?php
$usersModel = new Users();
$internal_users = $usersModel->getInternalUsersShort();

$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $ca = new Form_SubForm($this);
    $ca->setAttrib('title', 'Herinneringen & Aanmaningen');
    $this->addSubForm($ca, 'ca');


    $ca->addElement('checkbox', 'reminder_grouped_user', array(
        'label' => 'Gegroepeerd per ontvanger',
        'attribs' => array(
            'title' => 'Ipv 1 herinnering per openstaande post worden deze gegroepeerd per ontvanger',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $ca->addElement('checkbox', 'reminder_other_unpayed_with_single_penalty', [
        'label' => 'Zin over overige openstaande facturen weergeven bij enkele herinnering',
        'attribs' => array(
            'title' => 'Indien niet voor gegroepeerd wordt gekozen kan met deze instelling wel de overige openstaande posten qua totaalbedrag getoond worden.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ]);

    $ca->addElement('checkbox', 'hide_contact_for_complaint_in_reminds_mails', array(

        'label' => 'Verberg contactgegevens in geval van storing in herinneringen en aanmaningen',
        'attribs' => array(
            'title' => 'In de herinneringen is geen contactgegevens delen voor contact in geval van een storing.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));


    $ca->addElement('checkbox', 'hide_phone_in_reminds_mails', array(

        'label' => 'Verberg telefoonnummer in herinneringen en aanmaningen',
        'attribs' => array(
            'title' => 'In de herinneringen is alleen nog maar het mail adres zichtbaar voor het opnemen van contact.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $ca->addElement('checkbox', 'hide_expire_date_reminders', array(
        'label' => 'Verberg vervaldatum in herinneringen'
    ));


    $ca->addElement('checkbox', 'reminder_disable_notax_notice', array(
        'label' => 'Waarschuwing BTW-plichtig uitschakelen'
    ));

    $ca->addElement('checkbox', 'reminder_level_99_disabled', [
        'label' => 'Niveau incassobureau uitschakelen',
        'attribs' => [
            'title' => 'Deze instelling zorgt ervoor dat er geen incassodossiers zullen worden gemaakt.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $ca->addElement('checkbox', 'reminder_nocopy_invoice', array(
        'label' => 'Factuur niet meezenden met herinneringen',
    ));

    $ca->addElement('checkbox', 'reminder_no_extra_post_copy', array(
        'label' => 'Aanmaning per E-mail, geen poststuk',
        'attribs' => [
            'title' => 'Wanneer een aanmaning verzonden kan worden via E-mail wordt normaal ook nog een poststuk aangeboden, met deze optie kan dat uitgeschakeld worden zodat de aanmaning indien mogelijk enkel per E-mail verstuurd wordt',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ]
    ));


    $remind_commercial_penalty = new Form_SubForm($this);
    $remind_commercial_penalty->setAttrib('title', 'Commerciele boetes bij herinneringen & aanmaningen');
    $this->addSubForm($remind_commercial_penalty, 'remind_commercial_penalty');

    $remind_commercial_penalty->addElement('select', 'remind_commercial_penalty_method', [
        'label' => 'Methode',
        'multiOptions' => [
            'commercial' => 'Commerciele methode',
            'private' => 'Particuliere methode'
        ],
    ]);

    $remind_commercial_penalty->addElement('select', 'remind_commercial_penalty_increase', [
        'label' => 'Boete verhoging',
        'multiOptions' => [
            'invoice' => 'Vast bedrag per factuur',
            'per_reminder' => 'Ophogend per factuur per periode'
        ],
    ]);

    $remind_commercial_penalty->addElement('text', 'remind_commercial_penalty_interest_rate', [
        'label' => 'Rente percentage'
    ]);

    $remind_commercial_penalty->addElement('text', 'remind_commercial_penalty_amount', [
        'label' => 'Minimum boete bedrag'
    ]);

    $cadays = new Form_SubForm($this);
    $cadays->setAttrib('title', 'Niveaus en dagen');
    $this->addSubForm($cadays, 'cadays');


    $cadays->addElement('checkbox', 'reminder_inform_enabled', array(

        'label' => 'Niveau 0 ingeschakeld',
        'attribs' => array(
            'title' => 'Voor-aankondiging voor de herinnering versturen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $cadays->addElement('text', 'reminder_inform_days', array(
        'label' => 'Dagen niveau 0',
        'attribs' => array(
            'title' => 'Aantal dagen tussen de voor-aankondiging en de herinnering. Deze termijn wordt ook weergegeven als termijn in de tekst.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));



    $cadays->addElement('text', 'reminder_toformalnotice_days', array(
        'label' => 'Dagen niveau 1',
        'attribs' => array(
            'title' => 'Aantal dagen tussen de herinnering en het aanmaken van de aanmaning. Deze termijn wordt ook weergegeven als termijn in de herinnering tekst.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));


    $cadays->addElement('text', 'label_reminder', array(
        'label' => 'Naam niveau 1',
    ));

    $cadays->addElement('checkbox', 'reminder_level_one_repeat_forever', [
        'label' => 'Niveau 1 oneindig herhalen',
        'attribs' => [
            'title' => 'Met deze instelling ingeschakeld, in combinatie met de instellingen "Niveau 2 uitschakelen" en "Niveau incassobureau uitschakelen", kan ervoor zorgen dat er oneindig herinneringen kan worden verstuurd. Let op; dit is een maatwerk oplossing en zeer waarschijnlijk niet toepasbaar bij andere klanten.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cadays->addElement('text', 'formalnotice_tocollectionagency_days', array(
        'label' => 'Dagen niveau 2',
        'attribs' => array(
            'title' => 'Aantal dagen tussen de aanmaning en het aanmaken van het incasso dossier. Op de aanmaning wordt deze betaaltermijn ook weergegeven.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $cadays->addElement('checkbox', 'reminder_level_two_disabled', [
        'label' => 'Niveau 2 uitschakelen',
        'attribs' => [
            'title' => 'Deze instelling zorgt ervoor dat er geen aanmaningen (dus; herinnering niveau 2) zullen worden gemaakt. Let op; dit is een maatwerk oplossing en zeer waarschijnlijk niet toepasbaar bij andere klanten.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cadays->addElement('text', 'label_formalnotice', array(
        'label' => 'Naam niveau 2',
    ));

    $cadays->addElement('checkbox', 'collection_agency_inform_enabled', array(
        'label' => 'Niveau 3 ingeschakeld',
    ));

    $cadays->addElement('text', 'collection_agency_inform_days', array(
        'label' => 'Dagen niveau 3',
        'attribs' => array(
            'title' => 'Enkel voor klanten met een derde niveau',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $cadays->addElement('text', 'label_collection_agency_inform', array(
        'label' => 'Naam niveau 3',
    ));



    $cadays->addElement('text', 'tocollectionagency_days', array(
        'label' => 'Aantal dagen naar incassobureau',
        'attribs' => array(
            'title' => 'Deze setting doet op dit moment niks en zou waarschijnlijk kunnen verdwijnen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));











    $this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
