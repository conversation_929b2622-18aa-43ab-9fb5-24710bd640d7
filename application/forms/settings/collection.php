<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {



    $collection = new Form_SubForm($this);
    $collection->setAttrib('title', 'Incasso');
    $this->addSubForm($collection, 'collection');


    $collection->addElement('checkbox', 'collection_no_group_by_project', array(
        'label' => 'Incasso per J.E.',
        'attribs' => [
            'title' => 'Incasso opdrachten niet groeperen per project, maar per juridische entiteit',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));


    $collection->addElement('checkbox', 'financial_export_group_collection', array(
        'label' => 'Gegroepeerde automatische incasso boeking',
        'attribs' => [
            'title' => 'Hiermee wordt incasso batch als 1 bedrag geincasseerd',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    $collection->addElement('checkbox', 'financial_export_group_collection_payment', array(
        'label' => 'Gegroepeerde overboeking',
        'attribs' => [
            'title' => 'Deze instelling groepeert alle transacties in een SEPA bestand tot 1 batch. Dit is meestal'
                .' NIET wenselijk, maar bij sommige banken de enige manier om de transacties (werkbaar) in te lezen.<br><br>'
                .' LET OP: deze instelling alleen inschakelen in overleg met Omniboxx klantenservice. Test het'
                .' effect onmiddellijk met een SEPA bestand en schakel de instelling weer uit als het bestand niet'
                .' door de bank geaccepteerd wordt.',
            'class' => 'forceAutoHint',

        ]
    ));

    $collection->addElement('text', 'clieop_month_day', array(
        'label' => 'Dag van de maand clieop incasso',
        'attribs' => [
            'title' => 'Deze dag wordt op de facturen getoond waarop de incasso zal plaatsvinden. (Nummer bv. 26 betekend dat de incasso op de 26e zal plaatsvinden). 
            <br/><br/>
            Indien deze setting leeg is, dan zal de zin "Bovenstaand bedrag wordt rond de vervaldatum afgeschreven" gebruikt worden in plaats van een specifieke datum te tonen. ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    $collection->addElement('checkbox', 'hide_clieop_month_day', array(
        'label' => 'Verberg de maand clieop incasso op de factuur',
        'attribs' => [
            'title' => 'Hiermee wordt de maand van de incasso niet getoond op de facturen, alleen de dag van de incasso',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    if (Settings::get('financial_export_system') === 'exactonline') {

        $collection->addElement('checkbox', 'use_iban_as_mandate_reference', array(
            'label' => 'Gebruik het iban van de huurder als mandaat referentie ipv het Omninboxx klantnummer',
            'attribs' => [
                'title' => 'Gebruik het iban van de huurder als mandaat referentie ipv het Omninboxx klantnummer',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ],
        ));

    }




	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
