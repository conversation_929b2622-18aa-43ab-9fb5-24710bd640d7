<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $indexation = new Form_SubForm($this);
    $indexation->setAttrib('title', 'Huurverhogingen particulier');
    $this->addSubForm($indexation, 'indexation');


    

    $indexation->addElement('checkbox', 'indexation_post_only', [
        'label' => 'Enkel per post',
        'attribs' => [
            'title' => 'Met deze instelling zullen huurverhogingen niet per E-mail verstuurd worden maar enkel klaar worden gezet in postcorrespondentie',
            'class' => 'forceAutoHint',
        ]
    ]);

    $indexation->addElement('checkbox', 'indexation_lifting_period_restriction', [
        'label' => 'Periode restrictie opheffen',
        'attribs' => [
            'title' => 'Met deze instelling zullen sociale huurverhogingen ook later dan 2 maanden vantevoren of in het verleden gemaakt kunnen worden.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $indexation->addElement('checkbox', 'indexation_address_always_object_address', [
        'label' => 'Adres indexatiebrief altijd op objectadres ',
        'attribs' => [
            'title' => 'Met deze instelling zal het adres op de huurverhoging altijd het objectadres zijn ook al is het factuuradres ingevuld',
            'class' => 'forceAutoHint',
        ]
    ]);




    $indexationcommercial = new Form_SubForm($this);
    $indexationcommercial->setAttrib('title', 'Indexaties CBS');
    $this->addSubForm($indexationcommercial, 'indexationcommercial');


    $indexationcommercial->addElement('checkbox', 'invoice_indexation_commercial_no_rounding', array(
        'label' => 'Comm. indexatie afronding uitschakelen',
        'attribs' => [
            'title' => 'Bij berekening van een commerciële indexatie wordt afgerond op 3 cijfers achter de komma. Dit is conform CSB berekening. Wilt u deze afronding uitzetten en op de hele breuk de inexatie berekenen zet dan deze instelling aan. Let op: hiermee komt de berekening niet meer overeen met de officiele berekening van het CBS',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));


    $indexationcommercial->addElement('checkbox', 'invoice_indexation_commercial_apply_to_future_versions', [
        'label' => 'Comm. indexatie toepassen op toekomstige versies van bedragen',
        'attribs' => [
            'title' => 'Bij berekening van een commerciële indexatie tevens de uitkomst van de indexatie toepassen op alle toekomstige versies van bedragen zoals ingesteld bij het object.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ]);


    $indexationcommercial->addElement('checkbox', 'invoice_enable_previous_period_indexation', array(
        'label' => 'Comm. indexatie uit voorgaande periode',
        'attribs' => [
            'title' => 'Bij berekening van een commerciële indexatie tevens de voorgaande periode corrigeren indien toen geen cijfers ingevoerd of bekend waren. kan max. 1 periode terug (maand/kwartaal/jaar)',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));



    $this->addElement('submit', 'aanmaken', [
        'label' => 'Opslaan'
    ]);



} catch (Zend_Form_Exception $e) {
	throw $e;
}
