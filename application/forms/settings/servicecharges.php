<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $service = new Form_SubForm($this);
    $service->setAttrib('title', 'Servicekosten');
    $this->addSubForm($service, 'service');



$service->addElement('checkbox', 'modules_service_charges_administration_charges_enabled', array(
    'label' => 'Eindafrekeningen administratiekosten inschakelen',
    'attribs' => array(
        'title' => 'Omniboxx berekend zelf administratiekosten over de kosten in de servicekostenafrekening. Vul hieronder het percentage in voor deze berekening.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    )
));
$service->addElement('number', 'modules_service_charges_administration_charges_percentage', array(
    'label' => 'Eindafrekeningen administratiekosten percentage',
    'attribs' => array(
        'title' => 'Percentage administratie over van de werkelijke kosten',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -20, 'y': 0}",
        'decimals' => 2,
        'step' => '.01',
    )
));

    $service->addElement('number', 'modules_service_charges_administration_charges_minumum', array(
        'label' => 'Min. bedrag adm. kosten',
        'attribs' => array(
            'title' => 'In centen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -20, 'y': 0}",
            'decimals' => 2,
            'step' => '.01',
        )
    ));

    $service->addElement('number', 'modules_service_charges_administration_charges_maximum', array(
        'label' => 'Max. bedrag adm. kosten',
        'attribs' => array(
            'title' => 'In centen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -20, 'y': 0}",
            'decimals' => 2,
            'step' => '.01',
        )
    ));


    $service->addElement('text', 'modules_service_charges_administration_charges_component_id', array(
        'label' => 'Eindafrekeningen administratiekosten component id',
        'attribs' => array(
            'title' => 'Het component ID van het kosten component waar de administratiekosten op geboekt moeten worden,',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -20, 'y': 0}"
        )
    ));

$service->addElement('text', 'modules_service_charges_administration_ledger', array(
    'label' => 'Grootboek voor gladlopen afrekening servicekosten',
    'attribs' => array(
        'title' => 'Grootboek voor gladlopen afrekening servicekosten',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -20, 'y': 0}"
    )
));

$service->addElement('text', 'modules_service_charges_add_advance_months', array(
    'label' => 'Eindafrekeningen verhoog voorshot met X maanden',
    'validators' => [
        [
            'validator'	=> 'Int',
        ],
        [
            'validator'	=> 'stringLength',
            'options'	=> [
                'min' =>	1,
                'max' =>	11,
            ]
        ],
    ],
    'attribs' => array(
        'title' => 'Indien een gebroken jaar is gefactureerd kunt u hier een getal invullen waarmee het aantal maanden qua voorschot verhoogd wordt. Let op. Bedrag wordt gebaseerd op totaal van het jaar',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -20, 'y': 0}"
    )
));

/*
    $service->addElement('checkbox', 'modules_service_charges_cost_entry_ex_vat', array(
        'label' => 'Kosten handmatig invoeren op basis van excl. btw bedragen.',
        'attribs' => array(
            'title' => 'Omniboxx berekend zelf administratiekosten over de kosten in de servicekostenafrekening. Vul hieronder het percentage in voor deze berekening.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));
*/






    $this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
