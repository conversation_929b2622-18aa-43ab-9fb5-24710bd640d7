<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');
global $config;

$general->addElement('checkbox', 'wonen31_module', array(
    'label' => 'Wonen31 Module',
    'attribs' => [
        'title' => 'Hier me zet je de wonen31 module aan',
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('checkbox', 'wonen31_cronjobs', array(
    'label' => 'Wonen31 uitwissel cronjobs/achtergrond worker',
    'attribs' => [
        'title' => 'Hier me word er op de achtergrond de data tussen Omniboxx en Wonen31 uitwisselt',
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('checkbox', 'wonen31_easy_mode', array(
    'label' => 'Wonen31 easy mode',
    'attribs' => [
        'title' => "Door de 'Easy Mode' te activeren, hoef je enkel de website aan te vinken bij objectpublicatie, waarna het automatisch ook naar Wonen31 verstuurd wordt.",
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('checkbox', 'wonen31_email', array(
    'label' => 'Verstuur een e-mail dat het aanbod op Wonen31 staat',
    'attribs' => [
        'title' => 'Verstuur een e-mail dat het woningaanbod op Wonen31 staat. Deze e-mail wordt verzonden vanuit no-reply@ naar huurders en mensen die zich in het verleden hebben aangemeld voor een woning.',
        'class' => 'forceAutoHint'
    ]
));


$general->addElement('text', 'wonen31_api_key', array(
    'label' => 'Wonen31 API key',
    'style' => 'width:330px',
    'attribs' => [
        'title' => 'Hier stel je de API key in die te vinden is op https://desk.wonen31.nl',
        'class' => 'forceAutoHint'
    ]
));




$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
