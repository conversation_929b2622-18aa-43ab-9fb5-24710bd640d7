<?php

$first->addElement('text', 'financial_export_afas_customer_id', [
    'label' => 'AFAS Klantnummer',
]);

$first->addElement('text', 'financial_export_afas_app_token', [
    'label' => 'AFAS App token',
]);


$first->addElement('text', 'financial_export_afas_environment', [
    'label' => 'AFAS omgeving',
    'attribs' => [
        'title' => 'Voor productie moet deze instelling leeg zijn, is bedoeld voor wanneer er in AFAS bijvoorbeeld een testomgeving aangemaakt is. Dit zorgt ervoor dat de API verbind met de testomgeving ipv productie. De waarde hiervan zou bijvoorbeeld "test" kunnen zijn waardoor de API verbind met "Https://xxxxx.rest<b>test</b>.afas.online/profitrestservices" ipv "Https://xxxxx.rest.afas.online/profitrestservices"',
        'class' => 'forceAutoHint'
    ]
]);


$first->addElement('checkbox', 'financial_export_afas_cost_center', [
    'label' => 'AFAS kostenplaats/kostendrager',
    'attribs' => [
        'title' => 'Met deze instelling ingeschakeld zal Omniboxx kostenplaatsen en kostendragers aanmaken in AFAS bij het aanmaken of bewerken van een object',
        'class' => 'forceAutoHint'
    ]
]);

$first->addElement('checkbox', 'financial_export_afas_user_number_as_debtor_code', [
    'label' => 'AFAS -> Omniboxx klantnummer als debiteurnummer',
]);

$first->addElement('checkbox', 'financial_export_afas_empty_fiscalperiod_and_fiscalyear', [
    'label' => 'Laat boekjaar en boekperiode leeg in de uitwisseling',
    'attribs' => [
        'title' => 'Als klanten met een gebroken boekjaar werken moet deze setting aanstaan, de periode en jaar van de boeking worden leeg gelaten en als het goed is door AFAS zelf gevuld',
        'class' => 'forceAutoHint'
    ]
]);


$first->addElement('checkbox', 'financial_export_afas_invoice_identifier_as_invoice_id', [
    'label' => 'AFAS -> Omniboxx factuurnummer zonder punten (ipv factuur id) als AFAS factuurnummer',
    'attribs' => [
        'title' => 'Dus factuur: "0201.F' . date('y') . '.0005" wordt bijvoorbeeld: "0201F' . date('y') . '0005" in AFAS',
        'class' => 'forceAutoHint'
    ]
]);

$first->addElement('checkbox', 'financial_export_afas_house_number_suffix', [
    'label' => 'Gebruik bouwnummer als huisnummer toevoeging',
    'attribs' => [
        'title' => 'Bij het adres van een object is een een vrij in te vullen veld "bouwnummer" Dit veld kan gebruikt worden als huisnummer toevoeging',
        'class' => 'forceAutoHint'
    ]
]);

$first->addElement('text', 'financial_export_afas_sync_timestamp', [
    'label' => 'AFAS Sync timestamp',
    'ignored' => true,
    'disabled' => true,
]);

$first->addElement('text', 'financial_export_afas_debtor_profile_id', [
    'label' => 'AFAS debtor profile id',
]);

$first->addElement('text', 'financial_export_system_closed_date', array(
    'label' => 'Financiele koppeling periode afsluiting',
    'attribs' => [
        'title' => 'Afgesloten periode datum invoeren als:<br />dd-mm-yyyy' . (Settings::get('financial_export_system') != 'afas' ? '<br /><br />Tot nu toe enkel toegepast voor handmatige facturen voor de AFAS uitwisseling.' : ''),
        'class' => 'forceAutoHint'
    ]
));
