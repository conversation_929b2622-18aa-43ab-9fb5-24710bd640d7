<?php


$this->setAction('')
    ->set<PERSON><PERSON>od('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $general = new Form_SubForm($this);
    $general->setAttrib('title', 'Email inhoud beleggerafrekening');
    $this->addSubForm($general, 'general');


    $general->addElement('textarea', 'mail_investor_provision_nl', array(
        'label' => 'Standaard tekst mail beleggerafrekening belegger (NL)',
        'rows' => 7,
        'cols' => 35,
        'title' => 'De aanhef en alsuiting is standaard. Met deze optie kunt u de standaard tussenliggende tekst in de mail aanpassen. Standaard is de tekst "Bijgevoegd vindt u uw huurafrekening."',
        'class' => 'forceAutoHint',
    ));


    $general->addElement('textarea', 'mail_investor_provision_en', array(
        'label' => 'Standaard tekst mail beleggerafrekening belegger (EN)',
        'rows' => 7,
        'cols' => 35,
        'title' => 'De aanhef en alsuiting is standaard. Met deze optie kunt u de standaard tussenliggende tekst in de mail aanpassen. Standaard is de tekst "Attached you will find your rent settlement. You can open this with a pdf reader.."',
        'class' => 'forceAutoHint',
    ));



    $this->addElement('submit', 'aanmaken', [
        'label' => 'Opslaan'
    ]);

} catch (Zend_Form_Exception $e) {
    throw $e;
}
