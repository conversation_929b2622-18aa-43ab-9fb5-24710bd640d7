<?php
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $mailingsettings = new Form_SubForm($this);
    $mailingsettings->setAttrib('title', 'Algemeen');
    $this->addSubForm($mailingsettings, 'mailingsettings');


    $email = new EmailAccounts();


    $mailingsettings->addElement('select', 'no_reply_mail_address_not_drawn', array(
        'label' => 'No reply mail adres voor uitloot mails',
        'multioptions' 	=> $email->getForForm(),
        'attribs' => [
            'title' => 'indien hier een e-mail adres is ingevoerd dan wordt dit mail adres bij alle niet ingeloot mails gehanteerd',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));


    $mailingsettings->addElement('checkbox', 'rental_deal_request_documents_after_website_viewing', array(
        'label' => 'Bij bezichting overslaan vanuit website aanvragen document opvraag mail versturen',
        'attribs' => [
            'title' => 'Bij bezichting overslaan vanuit website aanvragen document opvraag mail versturen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));


    $mailingsettings->addElement('checkbox', 'rental_deal_create_uo_concept_after_website_viewing', array(
        'label' => 'Bij bezichting overslaan vanuit website aanvragen concept koppeling maken',
        'attribs' => [
            'title' => 'Bij bezichting overslaan vanuit website aanvragen concept koppeling maken',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    $portalsettings = new Form_SubForm($this);
    $portalsettings->setAttrib('title', 'Portal');
    $this->addSubForm($portalsettings, 'portalsettings');


    $portalsettings->addElement('checkbox', 'hide_income_and_living_fields', array(
        'label' => 'Verberg inkomen en huidige woonsituatie op portal',
        'attribs' => [
            'title' => 'Verberg inkomen en huidige woonsituatie op portal',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    $portalsettings->addElement('checkbox', 'hide_current_adddress', [
        'label' => 'Verberg huidig adres op de portal',
        'attribs' => [
            'title' => 'Verberg huidig adres op de portal',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ]
    ]);
    $portalsettings->addElement('textarea', 'avg_explanation_document_upload', [
        'label' => 'Privacy toelichting bij documenten uploaden op de portal',

    ]);



    $mailingsettingsmail = new Form_SubForm($this);
    $mailingsettingsmail->setAttrib('title', 'Match mail instellingen');
    $this->addSubForm($mailingsettingsmail, 'mailingsettingsmail');

    $mailingsettingsmail->addElement('checkbox', 'payed_account_needed_mailing', array(
        'label' => 'Betaald account vereist',
        'attribs' => [
            'title' => 'Betaald account is nodig voor ontvangen mailing & instellen zoekprofiel op de portal',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    if (Settings::get('modules_user_project')):
    $mailingsettingsmail->addElement('checkbox', 'mailing_from__all_branch_locations', array(
        'label' => 'Aanbod van alle vestigingen mailen',
        'attribs' => [
            'title' => 'Indien een kandidaat zich heeft aangemeldt bij 1 vestiging, krijgt hij toch aanbod van projecten onder alle vestigingen gemaild',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));
    endif;

    $mailingsettingsmail->addElement('checkbox', 'filter_matches_on_object_type', array(
        'label' => 'Op objecttype filteren in Matchmails',
        'attribs' => [
            'title' => 'Indien deze optie gekozen is, maar op het object de objecttypes niet ingevoerd zijn beperkt dit de zoekresultaten',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    $mailingsettingsmail->addElement('numberInput', 'payed_account_initial_price', [
        'label' => 'Betaald account initiële prijs',
        'attribs' => [
            'title' => 'Prijs voor het initieel activeren van een betaald account, nodig voor iDeal betalingen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ]
    ]);

    $mailingsettingsmail->addElement('numberInput', 'payed_account_extension_price', [
        'label' => 'Betaald account extensie-prijs',
        'attribs' => [
            'title' => 'Prijs voor het verlengen van een betaald account met 1 jaar, nodig voor iDeal betalingen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ]
    ]);

    $mediapartners = new Form_SubForm($this);
    $mediapartners->setAttrib('title', 'Media partners');
    $this->addSubForm($mediapartners, 'mediapartners');

    $mediapartners->addElement('checkbox', 'soomedia_activated', array(
        'label' => 'SooMedia',
        'attribs' => [
            'title' => 'Media bestellingen plaatsen bij SooMedia',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    if (Settings::get('soomedia_activated')):

        $mediapartners->addElement('textarea', 'soomedia_products', array(
            'label' => 'SooMedia producten',
            'attribs' => [
                'title' => 'Welke producten kunnen we bestellen (gescheiden door enter)',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
                'rows' => 15,
                'cols' => 35,
            ],
        ));

        $mediapartners->addElement('text', 'soomedia_mailaddress', array(
            'label' => 'SooMedia mailadres voor bestellingen',
            'validators' => array('emailAddress'),
            'attribs' => [
                'title' => 'Mailadres voor bestellingen SooMedia',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ],
        ));

    endif;


    $mediapartners->addElement('checkbox', 'redstone_activated', array(
        'label' => 'Redstone',
        'attribs' => [
            'title' => 'Media bestellingen plaatsen bij Redstone',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ],
    ));

    if (Settings::get('redstone_activated')):

        $mediapartners->addElement('textarea', 'redstone_products', array(
            'label' => 'Redstone producten',
            'attribs' => [
                'title' => 'Welke producten kunnen we bestellen (gescheiden door enter)',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
                'rows' => 15,
                'cols' => 35,
            ],
        ));

        $mediapartners->addElement('text', 'redstone_mailaddress', array(
            'label' => 'Redstone mailadres voor bestellingen',
            'validators' => array('emailAddress'),
            'attribs' => [
                'title' => 'Mailadres voor bestellingen Redstone',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ],
        ));

    endif;

    $this->addElement('submit', 'aanmaken', [
        'label' => 'Opslaan'
    ]);

} catch (Zend_Form_Exception $e) {
    throw $e;
}
