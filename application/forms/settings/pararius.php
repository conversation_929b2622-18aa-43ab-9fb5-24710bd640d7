<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');
global $config;

$general->addElement('text', 'modules_rental_pararius_id', array(
    'label' => 'Pararius agent id',
    'attribs' => [
        'title' => 'De agent id van de Pararius installatie',
        'class' => 'forceAutoHint'
    ]
));

$general->addElement('checkbox', 'modules_rental_pararius_export', [
    'label' => 'Maak de Pararius v2 XML beschikbaar',
    'attribs' => [
        'title' => 'Hiermee activeer je de Omniboxx pararius V2 XML. Doe dit alleen als de objecten op basis van de handleiding zijn nagelopen op de nieuwe eisen vanuit Pararius',
        'class' => 'forceAutoHint'
    ]
]);



$general->addElement('text', 'modules_rental_pararius_url', array(
    'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'pararius/export/',
    'label' => 'URL aanroep voor Pararius',
    'ignored' => true,
    'disabled' => true,
    'style' => 'width:330px',
    'attribs' => [
        'title' => 'Aan Pararius moet een url doorgegeven worden waar ze de gegevens vandaan kunnen kunnen halen, dat is deze url',
        'class' => 'forceAutoHint'
    ]
));




$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
