<?php


$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $general = new Form_SubForm($this);
    $general->setAttrib('title', 'Facturen algemene instellingen');
    $this->addSubForm($general, 'general');

    $general->addElement('checkbox', 'invoice_run_dont_update_expire_date', [
        'label' => 'Factuurruns vervaldatum vastzetten',
        'attribs' => [
            'title' => 'Hiermee kunt u ervoor zorgen dat de vervaldatum niet bijgewerkt wordt bij het hergenereren' .
                'van factuurruns',
            'class' => 'forceAutoHint',
        ]
    ]);

    $general->addElement('checkbox', 'invoice_date_equals_expire_date', [
        'label' => 'Factuurdatum gelijk aan vervaldatum',
        'attribs' => [
            'title' => 'Hiermee wordt de factuurdatum op de factuur gelijk aan de vervaldatum. Voor financiele koppelingen heeft dit tot gevolgd dat dit de factuurdatum in het boekhoudpakket wordt. Tenzij dit bij de specifieke instellingen van het financiele systeem anders wordt overschreven',
            'class' => 'forceAutoHint',
        ]
    ]);


    if (Settings::get('general_company_shortname') === 'debazaar') {
        $general->addElement('checkbox', 'invoice_enable_covid_discount', [
            'label' => 'Covid korting',
            'attribs' => [
                'title' => 'Hiermee kan de maatwerk covid korting ingeschakeld worden voor de Bazaar',
                'class' => 'forceAutoHint',
            ]
        ]);

        $general->addElement('text', 'invoice_enable_covid_discount_percentage', [
            'label' => 'Covid korting %',
            'attribs' => [
                'title' => 'Hiermee kan de maatwerk covid korting % ingegeven worden voor de Bazaar. Indien leeg dan standaard 50%',
                'class' => 'forceAutoHint',
            ]
        ]);

        $general->addElement('text', 'invoice_covid_unset_component_list', [
            'label' => 'Covid korting -> componenten uitzonderen van facturatie',
            'attribs' => [
                'title' => 'Hiermee kan als maatwerk bepaalde componenten geheel uitgeschakeld worden bij de facturatie, component ids opgeven met een komma ertussen.',
                'class' => 'forceAutoHint',
            ]
        ]);
    }

    $internal_users = db()->fetchPairs(db()->select()->from('users', array('id', 'CONCAT(initials, middlename, " ", name)'))->where('internal > ?', 0));

    $invoice = new Form_SubForm($this);
    $invoice->setAttrib('title', 'Facturen Verzending');
    $this->addSubForm($invoice, 'invoice');

    $invoice->addElement('checkbox', 'invoice_no_email', array(
        'label' => 'Facturen niet per E-mail',
        'attribs' => [
            'title' => 'Hiermee kunt u de algehele email verzending van facturen per mail uitschaken',
            'class' => 'forceAutoHint',
        ]
    ));

    $invoice->addElement('checkbox', 'invoice_add_address_to_subject', array(
        'label' => 'Voeg objectadres aan onderwerpregel van factuurmail toe',
        'attribs' => [
            'title' => 'Hiermee wordt het objectadres aan de onderwerpregel van de factuur toegevoegd',
            'class' => 'forceAutoHint',
        ]
    ));

    $invoice->addElement('checkbox', 'invoice_no_post', array(
        'label' => 'Facturen niet per post',
        'attribs' => [
            'title' => 'Hiermee kunt u de algehele post verzending van facturen per mail uitschaken',
            'class' => 'forceAutoHint',
        ]
    ));


    $invoice->addElement('checkbox', 'invoice_create_purchase_from_email_attachment', [
        'label' => 'Inkoopfacturen maken van E-mail bijlage',
        'attribs' => [
            'title' => 'ALPHA functionaliteit, niet inschakelen',
            'class' => 'forceAutoHint',
        ]
    ]);

    if(Settings::get('software_country') == 'be') {
        $invoice->addElement('checkbox', 'invoice_include_structured_message',[
            'label' => 'Gestructureerde mededeling inschakelen',
            'attribs' => [
                'title' => 'Aanzetten indien u gebruik maakt van de gestructureerde mededeling ',
                'class' => 'forceAutoHint',
            ]
        ]);
    }

    $invoice->addElement('checkbox', 'invoice_custom_post_add_stationary', array(
        'label' => 'Briefpapier bij postverzending handmatige facturen',
        'attribs' => [
            'title' => 'Hierbij wordt het logo op briefpapier bij handmatige postfactuur toegevoegd zodat dit via kleurenafdruk verstuurd kan worden ipv met voorgedrukt briefpapier',
            'class' => 'forceAutoHint',
        ]
    ));

    $invoice->addElement('checkbox', 'invoice_post_disable_notification', array(
        'label' => 'Notificatie voor poststukken uitschakelen',
        'attribs' => [
            'title' => 'Hiermee schakelt u de mails naar mailbox uit dat er facturen klaarstaan per post ter verzending',
            'class' => 'forceAutoHint',
        ]
    ));

    $invoice->addElement('checkbox', 'invoice_disable_grouped_rates', array(
        'label' => 'Toon alle componenten in regels op de factuur',
        'attribs' => [
            'title' => 'Indien deze optie aan staat worden op prolongatie runs de regels getoond conform de componenten op de huurder kaart. Indien deze uit staat wordt dit: "Huursom volgens contract"',
            'class' => 'forceAutoHint',
        ]
    ));

    $invoice->addElement('checkbox', 'overide_default_expire_day', [
        'label' => 'Wijzig standaard vervaldatum per 1e van de maand',
        'attribs' => [
            'title' => 'Indien deze setting aan wordt gezet dient voor werking ook de setting "Nieuwe standaard vervaldatum" ingevuld worden',
            'class' => 'forceAutoHint',
        ]
    ]);

    $invoice->addElement('text', 'overide_default_expire_day_new_day', [
        'label' => 'Aantal dagen optellen bij vervaldatum',
        'attribs' => [
            'title' => 'Vul hier het aantal dagen in ten opzichte van de standaard vervaldatum die opgeteld moeten worden.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $invoice->addElement('checkbox', 'overide_default_expire_day_subtract', [
        'label' => 'In plaats van optellen, het aantal dagen aftrekken',
        'attribs' => [
            'title' => 'De dagen worden dan afgetrokken van de vervaldatum in plaats van opgeteld',
            'class' => 'forceAutoHint',
        ]
    ]);


    $invoicefooter = new Form_SubForm($this);
    $invoicefooter->setAttrib('title', 'Footer opties');
    $this->addSubForm($invoicefooter, 'invoicefooter');

    $invoicefooter->addElement('checkbox', 'invoice_hide_footer', array(
        'label' => 'Footer niet op factuur tonen',
    ));

    $invoicefooter->addElement('checkbox', 'hide_footerblock_print_layout', array(
        'label' => 'Verberg footer op blanco briefpapier',
    ));

    $invoicefooter->addElement('checkbox', 'invoice_manage_corporation_footer', array(
        'label' => 'Beheer J.E. in footer weergeven',
        'attribs' => array(
            'title' => 'Met deze setting komt de J.E. Beheer naam in de footer te staan ipv J.E. project, enkel voor facturen voor huurders.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));




    $invoicelayout = new Form_SubForm($this);
    $invoicelayout->setAttrib('title', 'Facturen aanhef opties');
    $this->addSubForm($invoicelayout, 'invoicelayout');

    $invoicelayout->addElement('checkbox', 'invoice_notify_partner', array(
        'label' => 'Partner tonen op factuur',
        'attribs' => array(
            'title' => 'Naam van 2e bewoner ook op de huurfactuur tonen',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        )
    ));

    $invoicelayout->addElement('checkbox', 'invoice_hide_email', array(
        'label' => 'E-mail adres niet op factuur tonen',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_hide_title', array(
        'label' => 'Titel niet op factuur tonen',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_shown_bankname', array(
        'label' => 'Vervang label "IBAN" door naam van bank op de factuur ',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_hide_period', array(
        'label' => 'Periode niet op factuur tonen',
    ));

    $invoicelayout->addElement('textarea', 'invoice_sale_conditions_nl', [
        'label' => 'Verkoopvoorwaarden vermelding (NL)',
        'style' => 'width:320px',
        'expandable' => true
    ]);

    $invoicelayout->addElement('textarea', 'invoice_sale_conditions_en', [
        'label' => 'Verkoopvoorwaarden vermelding (EN)',
        'style' => 'width:320px',
        'expandable' => true
    ]);

    $invoicelayout->addElement('textarea', 'invoice_sale_conditions_fr', [
        'label' => 'Verkoopvoorwaarden vermelding (FR)',
        'style' => 'width:320px',
        'expandable' => true
    ]);

    $invoicelayout->addElement('textarea', 'invoice_add_extra_remark', array(
        'label' => 'Opmerking op iedere factuur',
        'style' => 'width:320px',
        'expandable' => true
    ));

    $invoicelayout->addElement('checkbox', 'invoice_hide_payment_method', array(
        'label' => 'Betaalwijze niet op de factuur tonen',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_show_build_on_invoice', array(
        'label' => 'Toon nummerspecificatie op factuur',
    ));

    $invoicelayout->addElement('text', 'build_label', array(
        'label' => 'Label nummerspecificatie',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_show_build_on_invoice_specific', array(
        'label' => 'Toon nummerspecificatie op afzonderlijke regel op factuur',
    ));

    $invoicelayout->addElement('checkbox', 'invoice_show_collection_total_on_summary', array(
        'label' => 'Toon totaal automatische incasso op invoice summary',
    ));
    $invoicelayout->addElement('checkbox', 'invoice_hide_collection_indicator', array(
        'label' => 'Verstop de "Automatisch incasso ja/nee" regel op facturen',
    ));


    $invoicecustom = new Form_SubForm($this);
    $invoicecustom->setAttrib('title', 'Handmatige facturen');
    $this->addSubForm($invoicecustom, 'invoicecustom');

    $invoicecustom->addElement('select', 'default_custom_invoice_payment_conditions', [
        'label' => 'Standaard betaalconditie handmatige factuur (aantal dagen)',
        'multioptions' 	=> [
            '30' => '30 dagen',
            '14' => '14 dagen'
        ]
    ]);


    $invoicecustom->addElement('checkbox', 'invoice_custom_corporation_bankaccount', [
        'label' => 'Bankrekening juridische entiteit aanpasbaar',
        'attribs' => [
            'title' => 'Mogelijkheid om te kiezen welke bankrekening er gebruikt wordt' .
                ' bij het maken van een handmatige factuur',
            'class' => 'forceAutoHint',
        ]
    ]);

    $invoicecustom->addElement('textarea', 'invoice_description_first_invoice', [
        'label' => 'Verkoopvoorwaarden vermelding (NL)',
        'style' => 'width:320px',
        'expandable' => true
    ]);

    $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

    $invoicecustom->addElement('textarea', 'invoice_description_first_invoice', [
        'label' => 'Omschrijving 1e nota op handmatige factuur NL',
        'style' => 'width:320px',
        'placeholder' => 'Hierbij ' . $pronoun . ' de factuur voor huur en (eventuele) borg',
        'expandable' => true
    ]);

    $invoicecustom->addElement('textarea', 'invoice_description_first_invoice_en', [
        'label' => 'Omschrijving 1e nota op handmatige factuur EN',
        'style' => 'width:320px',
        'placeholder' => 'This document contains your invoice for rent and deposit',
        'expandable' => true
    ]);

    $invoicecustom->addElement('textarea', 'invoice_description_last_invoice', [
        'label' => 'Omschrijving laatste nota op handmatige factuur',
        'style' => 'width:320px',
        'placeholder' => 'Hierbij ' . $pronoun . ' de laatste factuur voor verrekening van de huur en (eventuele) borg',
        'expandable' => true
    ]);

    $invoicecustom->addElement('textarea', 'invoice_description_last_invoice_en', [
        'label' => 'Omschrijving laatste nota op handmatige factuur',
        'style' => 'width:320px',
        'placeholder' => 'This document contains your invoice for rent and deposit',
        'expandable' => true
    ]);

    $this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
