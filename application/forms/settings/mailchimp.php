<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');

$general->addElement('text', 'mail_chimp_api_key', [
    'label' => 'MailChimp Api key:',
    'attribs' => [
        'title' => 'U dient zelf in MailChimp een api key aan te maken. Hoe u dit moet doen kunt u op de support pagina van MailChimp vinden.',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact = new Form_SubForm($this);
$contact->setAttrib('title', 'Contact gegevens');
$this->addSubForm($contact, 'contact');

$contact->addElement('text', 'mail_chimp_contact_company', [
    'label' => 'Bedrijf',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact->addElement('text', 'mail_chimp_contact_address1', [
    'label' => 'Adres',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact->addElement('text', 'mail_chimp_contact_city', [
    'label' => 'Stad',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact->addElement('text', 'mail_chimp_contact_state', [
    'label' => 'Provincie',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact->addElement('text', 'mail_chimp_contact_zip', [
    'label' => 'Postcode',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$contact->addElement('text', 'mail_chimp_contact_country', [
    'label' => 'Land',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);


$campaignDefaults = new Form_SubForm($this);
$campaignDefaults->setAttrib('title', 'Standaard gegevens');
$this->addSubForm($campaignDefaults, 'campaign_defaults');

$campaignDefaults->addElement('text', 'mail_chimp_campaign_defaults_from_name', [
    'label' => 'Van Naam',
    'attribs' => [
        'title' => 'Naam van de afzender. Het is mogelijk om dit per email groep te overschrijven.',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$campaignDefaults->addElement('text', 'mail_chimp_campaign_defaults_from_email', [
    'label' => 'Van Email',
    'attribs' => [
        'title' => 'Dit e-mailadres wordt standaard gebruikt. Het is mogelijk om dit per email groep te overschrijven.',
        'class' => 'forceAutoHint'
    ],
    'required' => true
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
