<?php

$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Algemene instellingen (voor alle huurders)'));
    $this->addSubForm($first, 'first');

    $first->addElement('checkbox', 'tenantportal_lock_changeprofile' , array(
        'label' => 'Lock velden op persoonlijke pagina',
        'attribs' => [
            'title' => 'De huurder kan bij gegevens wijzigen alleen zijn wachtwoord wijzigen. Andere gegevens (email, telefoon en IBAN) zijn gelockt.',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('checkbox', 'tenantportal_lock_changemail' , array(
        'label' => 'Geen mail sturen bij wijziging gegevens op de persoonlijke pagina',
        'attribs' => [
            'title' => 'Bij gegevens wijzigen wordt normaliter een mail gestuurd, deze optie aanvinken zet deze mail uit.',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('text', 'tenantportal_date_show_invoices' , array(
        'label' => '"Datum vanaf" voor tonen facturen.',
        'attribs' => [
            'title' => '"Datum vanaf" voor tonen facturen.',
            'class' => 'DatePicker',]
    ));



	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
