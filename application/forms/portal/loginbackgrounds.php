<?php

$this->setAction('')
    ->set<PERSON>ethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Achtergrondafbeeldingen'));
    $this->addSubForm($first, 'first');


    $first->addElement('text', 'login_background_images' , array(
        'label' => 'Pad naar fotos gescheiden door een komma. Pad start met media/assets/img/bg/naamklant/naamafbeelding.jpg(Alleen in te stellen door een developer van Omniboxx)',
        'value' => '',
        'style' => 'width:730px'
    ));


	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
