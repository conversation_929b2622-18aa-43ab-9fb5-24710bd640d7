<?php

$this->setAction('')
    ->setMethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

$uploadedletterhead = false;
if (Settings::get('tenant_portal_logo')) {
    $images = new Images();
    $uploadedletterhead = $images->get(
        Settings::get('tenant_portal_logo')
    );
}


try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Algemene instellingen (voor alle portals)'));
    $this->addSubForm($first, 'first');


    $first->addElement('text', 'colorcode_topbar' , array(
        'label' => 'Achtergrondkleur bovenbalk',
        'value' => '#707070',
    ));

    $first->addElement('text', 'colorcode_tabs' , array(
        'label' => 'Achtergrondkleur tabs',
        'value' => '#707070',
    ));

    $first->addElement('text', 'colorcode_tabs_link' , array(
        'label' => 'Kleurcode link tabs',
        'value' => '#707070',
    ));

    $first->addElement('text', 'colorcode_tabs_link_hover_active' , array(
        'label' => 'Kleurcode link tabs hover en actief',
        'value' => '#707070',
    ));


    $first->addElement('text', 'colorcode_middlebar' , array(
        'label' => 'Achtergrondkleur middenbalk',
        'value' => '#707070',
    ));
    $first->addElement('text', 'font_colorcode_middlebar' , array(
        'label' => 'Kleur text middenbalk',
        'value' => '#ffffff',
    ));



    $first->addElement('text', 'colorcode_footer' , array(
        'label' => 'Kleur footer',
        'value' => '#707070',
    ));
    $first->addElement('text', 'font_colorcode_footer' , array(
        'label' => 'Kleur text footer',
        'value' => '#ffffff',
    ));


    $first->addElement('text', 'colorcode_links' , array(
        'label' => 'Kleurcode links',
        'value' => '#707070',
    ));

    $first->addElement('text', 'colorcode_links_hover' , array(
        'label' => 'Kleurcode links hover',
        'value' => '#707070',
    ));




    $first->addElement('text', 'tenant_portal_logo', array(
        'label' => 'Logo portal instellen',
        'attribs' => array(
            'class' => 'imageUpload',
            'image-upload-type' => 'portal_logo'
        )
    ));

    $first->addElement('hidden', 'current_uploaded_letterhead', array(
        'label' => 'Huidig logo',
        'value' => $uploadedletterhead ? $uploadedletterhead['filename'] : false
    ));





	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
