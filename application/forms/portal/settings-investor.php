<?php

$this->setAction('')
    ->set<PERSON>ethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst('Algemene instellingen (voor alle beleggers)'));
    $this->addSubForm($first, 'first');

    $first->addElement('checkbox', 'brokerportal_contact_email', array(
        'label' => 'Toon e-mail adres',
    ));

    $first->addElement('checkbox', 'brokerportal_contact_phone' , array(
        'label' => 'Toon telefoonnummer',
    ));

    $first->addElement('checkbox', 'brokerportal_hide_payment_problems' , array(
        'label' => 'Verberg (huur) achterstanden',
    ));

    $first->addElement('checkbox', 'brokerportal_hide_tenant_object_details' , array(
        'label' => 'Geen doorlink op huurder en object op in beheer overzicht',
    ));

    $first->addElement('checkbox', 'brokerportal_show_account_manager' , array(
        'label' => 'Toon contactgegevens Portefeuille / Accountmanager belegger',
        'attribs' => [
            'title' => 'De portefeuillemanager kunt u bij de belegger instellen',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('checkbox', 'brokerportal_show_emails' , array(
        'label' => 'Toon mailbox belegger',
    ));
/* temp disabled to prevent customers enabling this during the testing fase 
    $first->addElement('checkbox', 'brokerportal_show_year_overview' , array(
        'label' => 'Toon jaaroverzicht op portal',
    ));
*/    
    $first->addElement('checkbox', 'brokerportal_show_projects_contactpage' , array(
        'label' => 'Voeg project keuze toe bij contactformulier',
        'attribs' => [
            'title' => 'De belegger kan bij zijn vraag een keuze maken uit de projecten in Omniboxx waar bij als belegger objecten in heeft',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('checkbox', 'meter_usage_broker_portal' , array(
        'label' => 'Toon meterstanden op portal',
        'attribs' => [
            'title' => 'Alleen aanzetten indien u meternummers en meterstanden bijhoud',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('checkbox', 'brokerportal_show_projects_contactpage' , array(
        'label' => 'Voeg project keuze toe bij contactformulier belegger portal',
        'attribs' => [
            'title' => 'Alleen aanzetten indien u meternummers en meterstanden bijhoud',
            'class' => 'forceAutoHint',]
    ));

    $first->addElement('checkbox', 'brokerportal_about_contactpage' , array(
        'label' => 'Voeg een drop down keuze met onderwerpen toe bij contactformulier.',
        'attribs' => [
            'title' => 'De onderwerpen en mailadressen per onderwerp kunnen ingesteld worden onder Instellingen -> Portal -> Contactcategorieen',
            'class' => 'forceAutoHint',]
    ));


    $first->addElement('checkbox', 'brokerportal_show_only_rent' , array(
        'label' => 'Toon alleen netto huurbedragen',
        'attribs' => [
            'title' => 'Toon alleen optellingen waarvan in Omniboxx bij het component is aangegeven dat dit huur betreft',
            'class' => 'forceAutoHint',]
    ));

	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
