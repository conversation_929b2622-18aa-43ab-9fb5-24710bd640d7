<?

$corpLib = new Corporation();
$invModel = new Investors();

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');

$investorCorporation = new Form_SubForm($this);
$investorCorporation->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($investorCorporation, 'investor_corporation_form');

	$investorOptions = $invModel->getNamesById();
	natcasesort($investorOptions);
	$investorCorporation->addElement('select', 'investor', [
		'label' => 'Belegger',
		'multiOptions' => $investorOptions,
	]);

	$corporationOptions = $corpLib->getSimpleList();
	natcasesort($corporationOptions);
	$investorCorporation->addElement('select', 'corporation', [
		'label' => 'Juridische entiteit',
		'multiOptions' => $corporationOptions,
	]);

//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Opslaan'
]);

?>
