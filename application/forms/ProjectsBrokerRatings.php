<?	

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'ProjectsBrokerRatings')
			->setAttrib('class', 'ProjectsBrokerRatings form');

	$general = new Form_SubForm($this);
	$general->setAttrib('title', 'Algemeen');
	$this->addSubForm($general, 'general');

		$projects = array();
		foreach(Project::getMenuList() as $project_id => $project)
			$projects[$project_id] = $project['name'];
			
		$general->addElement('select', 'project_id', array(
			'label' => 'Project',
			'required' => true,
			'multiOptions' => $projects
			
		));

		$pbr_model = new ProjectsBrokerRatings();
			
		$general->addElement('select', 'type', array(
			'label' => 'Type',
			'required' => true,
			'multiOptions' => $pbr_model->types
		));

			
		$general->addElement('text', 'year', array(
			'label' => 'Jaar',
			'required' => true,
			'value' => date('Y')
		));

		$general->addElement('text', 'rating', array(
			'label' => 'Rating',
			'required' => true,
			'placeholder' => 'Waarde tussen 0 en 10',
			'attribs' => [
				'title' => 'Voor geschillen hier het aantal geschillen binnen dit project invoeren, '
					.'in het rapport wordt het totaal van alle projecten weergegeven.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -20, 'y': 0}",
			]
		));

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Rating opslaan'
	));

?>