<?
$model = new ObjectsAlert();


//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general_form');

	$general->addElement('select', 'status', [
		'label' => 'Status',
		'multiOptions' => $model->statuses,
	]);


//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Adres opslaan'
]);

?>
