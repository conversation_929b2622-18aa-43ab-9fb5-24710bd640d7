<?	

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'Objectusers')
			->setAttrib('class', 'Objectusers form');

	$date = new Form_SubForm($this);
	$date->setAttrib('title', 'Datum instellingen');
	$this->addSubForm($date, 'date');


        $date->addElement('checkbox', 'finaldate', array(
            'label' => 'Datum definitief',
        ));

		$date->addElement('text', 'from', array(
			'label' => 'Begindatum',
			'required' => true,
			'attribs' => array('noAutoDatepicker' => 'true')
		));

		$date->addElement('checkbox', 'till_enabled', array(
			'label' => 'Einddatum bekend',
		));

		$date->addElement('text', 'till', array(
			'label' => 'Einddatum',
		));


	$users = new Form_SubForm($this);
	$users->setAttrib('title', 'Gebruikers');
	$this->addSubForm($users, 'users');

		$users->addElement('checkbox', 'empty', array(
			'label' => 'Leegstand',
			'value' => true,
			'disabled' => true
		));

		$users->addElement('text', 'normal', array(
			'label' => 'Contractant',
			'disabled' => true
		));

		$users->addElement('text', 'invoice', array(
			'label' => 'Facturatie',
			'disabled' => true
		));

		$users->addElement('text', 'emergency', array(
			'label' => 'Noodgevallen',
			'disabled' => true
		));		

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Koppeling opslaan'
	));

?>