<?
	//general form options
	$this->setMethod('post')
	 ->setAttrib('class', 'complaintadd form');

	$about_select = db()->select()
        ->from('investor_contact_categories', ['id', 'name_'.loginManager::data()->language.''])
        ->where('deleted = ?', false)
        ->order('order ASC')
        ->order('id ASC');

	$about_options = db()->fetchPairs($about_select);


    array_add_at_pos($about_options, ['' => ucfirst(translate()->_('choose'))], 0);

    $this->addElement('select', 'about', [
        'label' => (translate()->_('about')),
        'multiOptions' => $about_options,
    ]);

	//title
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Onderwerp:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	Settings::get('brokerportal_about_contactpage') ? false : true
	));

    $investorId = loginManager::data()->info['investor_company_id'];
    $investorModel = new \Investors();
    $investorProjects = $investorModel->getProjects($investorId);

    foreach ($investorProjects as $project)
        $projects[$project['name']] = $project['name'];

    $this->addElement('select', 'project', [
        'label' => 'Project.',
        'required' 		=>	Settings::get('brokerportal_show_projects_contactpage') ? true : false,
        'multiOptions'	=> (array) $projects,
    ]);

    //description
    $this->addElement('textarea', 'description', array(
        'label' 		=>  (translate()->_('your_question')),
        'validators' 	=> 	array(),
        'rows'			=> 	8,
        'required' 		=>	true
    ));
	
	//name default invoeren
	$this->addElement('text', 'name', array(
		'label' 		=> 	(translate()->_('name')),
		'validators' 	=> 	array(),
        'value'         =>  loginManager::data()->info['rendered_name'],
		'size' 			=> 	60,
		'required' 		=>	true
	)); 

	//phone default invoeren
	$this->addElement('text', 'phone', array(
		'label' 		=> 	'Uw directe telefoonnummer:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	false
	)); 
	



	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => (translate()->_('send'))
	));
