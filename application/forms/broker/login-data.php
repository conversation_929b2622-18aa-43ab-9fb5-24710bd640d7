<?php /** @noinspection PhpUndefinedMethodInspection */

$this->setMethod('post')
	->setAttrib('class', 'form');

$this->addElement('text', 'username', [
	'label' => translate()->_('username'),
	'placeholder' => translate()->_('username'),
	'validators' => [
		[
			'validator' => 'stringLength',
			'options' => [5, 50]
		]
	],
]);

$passwordFieldOptions = [
	'label' => translate()->_('password'),
	'placeholder' => translate()->_('password'),
	'renderPassword' => false,
	'validators' => [
		[
			'validator' => 'stringLength',
			'options' => [5, 30]
		]
	],
];

$this->addElement('password', 'password', $passwordFieldOptions);

$this->addElement('password', 'password_confirm', array_merge($passwordFieldOptions, [
	'label' => translate()->_('password_repeat'),
	'placeholder' => translate()->_('password_repeat'),
]));

$this->addElement('hidden', 'user_id', [
]);

$this->addButtons(false);
$this->addElement('submit', 'submit', [
	'label' => translate()->_('save')
]);
