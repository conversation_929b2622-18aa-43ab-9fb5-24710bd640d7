<?
$addressModel = new Address();
$params = $this->getOwner()->getAllUrlParams();

$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general_form');

$general->addElement('hidden', 'object_spaces_type', [
	'value' => $params['object_spaces_type']
]);

$general->addElement('text', 'title_nl', [
	'label' => 'Naam (Nederlandstalig)',
]);

$general->addElement('text', 'title_en', [
	'label' => 'Naam (Engelstalig)',
]);

if (Settings::get('software_french_enabled')) {
	$general->addElement('text', 'title_fr', [
		'label' => 'Naam (Franstalig)',
	]);
}

$types =  db()->fetchPairs(db()->select()->from(
    ['it'=>'inspection_types'],
    ['id','description_nl']
)->order('description_nl')->where('archived = ?', 0));

$general->addElement('multiselect', 'inspection_type', [
    'label' => 'Inspectie type beperken',
    'multioptions' => $types,
    'attribs' => [
        'title' => 'Ruimte enkel beschikbaar maken voor een bepaald types inspectie. Deselecteer alle opties om de ruimte beschikbaar te maken voor alle type inspecties',
        'class' => 'forceAutoHint'
    ],
]);

$this->addElement('submit', 'aanmaken', [
	'label' => 'Onderdeel opslaan'
]);

