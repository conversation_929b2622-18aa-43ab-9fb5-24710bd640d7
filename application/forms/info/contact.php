<?
	//general form options
	$this->setMethod('post')
	 ->setAttrib('class', 'complaintadd form');

	
	
	//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Onderwerp:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	true
	));
	
	//name
	$this->addElement('text', 'name', array(
		'label' 		=> 	'Uw naam:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	true,
        'value'         => loginManager::data()->name
	));

//name
$this->addElement('text', 'email', array(
    'label' 		=> 	'Uw email:',
    'validators' 	=> 	array(),
    'size' 			=> 	60,
    'required' 		=>	true,
    'value'         => loginManager::data()->info['email']
));


//name
$this->addElement('text', 'url', array(
    'label' 		=> 	'Url pagina Omniboxx (optioneel):',
    'validators' 	=> 	array(),
    'size' 			=> 	60
  ));

//name
	$this->addElement('text', 'phone', array(
		'label' 		=> 	'Uw directe telefoonnummer:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	true,
        'value'         => loginManager::data()->info['phone_user']
	)); 
	
	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	'Omschrijving vraag',
		'validators' 	=> 	array(),
		'rows'			=> 	8,
		'required' 		=>	true
	));


	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Versturen aanvraag support'
	));
