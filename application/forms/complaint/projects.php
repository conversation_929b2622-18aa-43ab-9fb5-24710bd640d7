<?


//general form options
$this->setAction($action)
    ->setMethod('post')
    ->setAttrib('id', 'complaintadd')
    ->setAttrib('class', 'complaintadd form');



//name
$this->addElement('text', 'title', array(
    'label' 		=> 	'Titel',
    'validators' 	=> 	array(),
    'required' 		=>	true
));

$this->addElement('text', 'budget', array(
    'label' 		=> 	'Budget',
    'validators' 	=> 	array(),
    'required' 		=>	true
));


//description
$this->addElement('textarea', 'description', array(
    'label' 		=> 	'Omschrijving',
    'validators' 	=> 	array(),
    'rows'			=> 	8,
    'cols'			=> 	50,
    'required' 		=>	true
));


//submit
$this->addButtons(false);
$this->addElement('submit', 'submit_button', array(
    'label' => 'Versturen'
));
