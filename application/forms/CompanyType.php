<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$companyType = new Form_SubForm($this);
$companyType->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($companyType, 'company_type_form');

	$companyType->addElement('text', 'name', array(
		'label' => 'Naam',
		'required' => true,
	));

	$companyType->addElement('checkbox', 'commercial_user', array(
		'label' => 'Commerciele gebruiker',
		'attribs' => array(
			'title' => 'Dit soort bedrijf is een commerciele gebruiker.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	$companyType->addElement('checkbox', 'technical_contact', array(
		'label' => 'Storing partij',
		'attribs' => array(
			'title' => 'Met dit soort bedrijf kan contact opgenomen worden in geval van storingen.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	$companyType->addElement('textarea', 'description', array(
		'label' => 'Beschrijving',
		'cols' => '50',
		'rows' => '3',
	));


//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Bedrijfs type opslaan'
));

?>