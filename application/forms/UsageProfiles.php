<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'usageprofile')
			->setAttrib('class', 'usageprofile profile form');

	/**
	 * General usage profile data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		// profile name
		$general->addElement('text', 'name', array(
			'label' 		=> 	'Naam profiel',
			'validators' 	=> 	array()
		));

		// profile date
		$general->addElement('text', 'date', array(
			'label' 		=> 	'Datum',
			'validators' 	=> 	array()
		));

		// profile type
		$general->addElement('select', 'type', array(
			'label' 		=> 	'Type',
			'validators' 	=> 	array(),
			'multiOptions' => array('commercial' => 'Commercieel', 'private' => 'Particulier')
		));
	
	//submit
	$this->addElement('submit', 'usageprofile', array(
		'label' => 'Opslaan'
	));