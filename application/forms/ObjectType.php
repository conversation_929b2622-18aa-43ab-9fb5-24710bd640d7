<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$objectType = new Form_SubForm($this);
$objectType->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($objectType, 'object_type_form');

	$objectType->addElement('text', 'name', array(
		'label' => 'Naam',
		'required' => true,
	));

	if(Settings::get('modules_rental'))
		$objectType->addElement('text', 'name_en', array(
			'label' => 'Naam engels',
			'required' => false,
            ));

$OBT = new ObjectTypeDefinitions();
$objectDefinitions = $OBT->getSimpleList();

    $objectType->addElement('select', 'definition_id', array(
        'label' => 'Type vastgoed',
        'multiOptions' => ['' => 'Maak een keuze'] + $objectDefinitions,
        'required' => true,
    ));


//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Object type opslaan'
));

?>
