<?
$addressModel = new Address();
$params = $this->getOwner()->getAllUrlParams();

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$address = new Form_SubForm($this);
$address->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($address, 'address_form');

	$address->addElement('text', 'address', [
		'label' => 'Straat',
		'validators' => [
			[
				'validator'	=> 'stringLength',
				'options'	=> [
					'max' =>	255,
				]
			]
		],
	]);

	$address->addElement('text', 'number', [
		'label' => 'Huisnummer',
		'validators' => [
			[
				'validator'	=> 'stringLength',
				'options'	=> [
					'max' =>	20,
				]
			]
		],
	]);

	$address->addElement('text', 'zipcode', [
		'label' => 'Postcode',
		'filters' => [
			'StringTrim',
		],
		'validators' => [
			[
				'validator'	=> 'stringLength',
				'options'	=> [
					'max' =>	50,
				]
			],
			'postcode' => [
				'validator'	=> 'PostCode',
				'options'	=> [
					'format'	=> '\d{4}[ ]?[a-zA-Z]{2}'
				]
			]
		],
	]);


	if(isset($_POST))
		if(isset($_POST['address_form']['country']))
			if(
			    $_POST['address_form']['country'] !== '' &&
                ($locale = $addressModel->getCountryLocale($_POST['address_form']['country']))
            ) {
				if ($locale != 'NL') {
					try {
						$address->zipcode->getValidator('postcode')->setLocale($locale);
					} catch (Zend_Validate_Exception $e) {
						$address->zipcode->clearValidators();
					}
				}
			} else {
				$address->zipcode->clearValidators();
			}

	$address->addElement('text', 'city', [
		'label' => 'Stad',
		'validators' => [
			[
				'validator'	=> 'stringLength',
				'options'	=> [
					'max' =>	50,
				]
			]
		],
	]);

	$address->addElement('select', 'country', [
		'label' => 'Land',
		'multiOptions' => Address::getCountries(),
	]);

	$address->addElement('select', 'address_type', [
		'label' => 'Type adres',
		'multiOptions' => Address::getAddressTypesForSelect(),
		'value' => Settings::get('general_company_shortname') == 'debazaar' ? 'correspondence' : false
	]);


$link = new Form_SubForm($this);
$link->setAttrib('style', 'display: none;');
$this->addSubForm($link, 'link_form');


	$link->addElement('hidden', 'type', [
		'value' => $params['type'],
	]);

	$link->addElement('hidden', 'type_id', [
		'value' => $params['type_id'],
	]);


//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Adres opslaan'
]);

?>
