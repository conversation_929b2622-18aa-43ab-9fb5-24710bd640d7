<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'arrangement')
			->setAttrib('class', 'arrangement form');

	/**
	 * General
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		
	//date
		$general->addElement('text', 'arrangementdate', array(
			'label' => 'Datum regeling',
			'value' => date('d-m-Y'),
			'validators' => array()
		));

		$general->addElement('text', 'name', array(
			'label' => 'Naam',
			'validators' => array(),
		));

		$general->addElement('checkbox', 'disabled', array(
			'label' => 'Uitgeschakeld',
			'validators' => array(),
		));
	/**
	 * Select user
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//search
		$user->addElement('text', 'search', array(
			'label' => 'Zoeken',
			'validators' => array(),
		));

		//search
		$user->addElement('hidden', 'id', array(
			'label' => '',
			'validators' => array(),
		));

		//gender
		$user->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
			'disabled'		=>	true,
			'ignore'		=>	true
		));

		// initials
		$user->addElement('text', 'initials', array(
			'label' 		=> 	'Initialen',
			'validators' => array(
			),
			'disabled'		=>	true,
			'ignore'		=>	true
		));

		//firstname
		$user->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array()
			),
			'disabled'		=>	true,
			'ignore'		=>	true
		));

		//middlename
		$user->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array()
			),
			'disabled'		=>	true,
			'ignore'		=>	true
		));

		//name
		$user->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array()
			),
			'disabled'		=>	true,
			'ignore'		=>	true
		));	
		
	/**
	 * Invoices
	 */
	$invoices = new Form_SubForm($this);
	$this->addSubForm($invoices, 'invoices');

		//list
		$invoices->addElement('multiselect', 'list', array(
			'label' => 'Facturen',
			'validators' => array(),
			'multiOptions' => array(
				false => 'Geen openstaande facturen gevonden'
			)
		));

		
	/**
	 * frequence
	 */
	$frequence = new Form_SubForm($this);
	$this->addSubForm($frequence, 'frequence');

		//total
		$frequence->addElement('text', 'total_preview', array(
			'label' => 'Totaal openstaand bedrag',
			'validators' => array(),
			'value' =>	0,
			'disabled' => true,
			'ignore' => true
		));
		
		//total
		$frequence->addElement('text', 'total', array(
			'label' => 'Totaal betalingsregeling bedrag',
			'validators' => array(),
			'value' =>	0,
		));

		$frequence->addElement('text', 'number', array(
			'label' => 'Aantal periodes',
			'validators' => array(),
			'value' => 1,
		));	

		$frequence->addElement('text', 'period', array(
			'label' => 'Bedrag per periode',
			'validators' => array(),
			'disabled' => true
		));	
		
	$this->addElement('submit', 'save', array(
		'label' => 'Opslaan',
		'validators' => array(),
	));			