<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'projectTypes')
			->setAttrib('class', 'projectTypes form');
				
	$general = new Form_SubForm($this);
	$general->setAttrib('title', 'Algemeen');
	
	$this->addSubForm($general, 'general');
	
		$general->addElement('select', 'type', array(
			'label' => 'Type opdracht',
			'required' => true,
			'multiOptions' => array('credit' => 'Incasso', 'debet' => 'Betaling')
		));
					
		$general->addElement('checkbox', 'test', array(
			'label' => 'Test modus',
		));
	
		$general->addElement('text', 'process_date', array(
			'label' => 'Uitvoerdatum',
			'required' => true,
			'attribs' => array('class' => 'DatePicker')
		));

		$general->addElement('select', 'sepa_status', array(
			'label' => 'SEPA incasso status',
			'multiOptions' => array(
				'FRST' => 'Eerste incasso',
				'RCUR' => 'Wederkerende incasso',
				'FNAL' => 'Laatste incasso',
				'OOFF' => 'Eenmalige incasso'
			),
			'attribs' => array(
				'title' => 'Mogelijke sequentie opties ten behoeve van het genereren van een SEPA-incasso bestand.<br /><br /> Standaard staat deze waarde op "Eerste incasso" en bij het afronden van de eerste incasso wordt deze waarde bij de gebruiker automatisch op "Wederkerende incasso" gezet.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

$corporation = new Form_SubForm($this);
	$corporation->setAttrib('title', 'Afzender');
	
	$this->addSubForm($corporation, 'corporation');
		
		$corporation->addElement('select', 'corporation', array(
			'label' => 'B.V.',
			'required' => true,
			'multiOptions' => db()->fetchPairs(db()->select()->from(array('c' => 'corporations'), array('id', 'name'))->order('name'))
		));
		
		$corporation->addElement('text', 'principal', array(
			'label' => 'Principal name',
			'required' => true,
		));
		
		$corporation->addElement('text', 'kvk', array(
			'label' => 'KvK',
			'required' => false,
		));

		if(Settings::get('software_type') != 'energy'){
			$corporation->addElement('text', 'kvk_subcode', array(
				'label' => 'KvK vestigingsnummer',
				'required' => false,
			));
		}

		$corporation->addElement('text', 'creditor_id', array(
			'label' => 'Incassant ID',
			'attribs' => [
				'style' => 'width: 170px;',
				'title' => 'Op uw incasso contract vindt u uw Incassant-ID, de bank bepaald dit aan de hand van uw KvK nummer.<br /><br />Als er bij deze betalingsopdracht een KvK nummer ingevoerd is zullen wij na het opslaan het Incassant-ID automatisch berekenen, echter dient dit door u gecontroleerd te worden met uw incasso contract.<br /><br />Wanneer het Incassant-ID niet correct is ingevoerd zal uw incasso batch hierop afgekeurd worden.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			]
		));

		$corporation->addElement('text', 'iban', array(
			'label' => 'IBAN',
			'required' => false,
		));

		$corporation->addElement('text', 'bic', array(
			'label' => 'BIC',
			'required' => false,
		));

		$corporation->addElement('text', 'description', array(
			'label' => 'Fixed description',
			'required' => true,
		));
		
		$corporation->addElement('text', 'identification', array(
			'label' => 'Identificatie',
			'required' => true,
		));		
/*
	$transactions = new Form_SubForm($this);
	$transactions->setAttrib('title', 'Opdrachten');
	
	$this->addSubForm($transactions, 'transactions');
			
		$transactions->addElement('text', 'bankaccount', array(
			'label' => 'Bankrekening',
			'required' => true,
		));			
		
		$transactions->addElement('text', 'amount', array(
			'label' => 'Bedrag',
			'required' => true,
		));
		
		$transactions->addElement('text', 'name', array(
			'label' => 'Naam',
			'required' => true,
		));	
		
		$transactions->addElement('text', 'city', array(
			'label' => 'Plaats',
			'required' => true,
		));	
		
		$transactions->addElement('text', 'description', array(
			'label' => 'Omschrijving',
			'required' => true,
		));		
			*/
	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Opslaan'
	));

?>