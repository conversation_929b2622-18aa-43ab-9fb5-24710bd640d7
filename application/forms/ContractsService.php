<?

$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
	$this->getOwner()->contractId: null;

$contractsModel = new Contracts();
$contractServiceModel = new ContractsService();
$contract = $contractId? $contractsModel->getById($contractId): null;
$contractService = $contract && intval($contract->fields) > 0? $contractServiceModel->getById($contract->fields): null;

$this->getOwner()->view->contractType = 'ContractsService';

//general form options
$this->setAction('contract/edit/'.(isset($contractId)? 'id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form')
        ->setAttrib('id', 'contractServiceForm');

$usersModel = new Users();
$intUsers = $usersModel->getInternalUsers();

foreach ($intUsers as $user)
	$internalUsers[$user['id']] = User::buildname($user);

$projectLib = new Project();
$projs = (array) $projectLib->getList();
foreach ($projs as $project)
	$projects[$project['id']] = $project['name'];


$objectgroupLib = new Objectgroups();
$objectgroups = (array) $objectgroupLib->getProjectList();
//p($objectgroups,'die');
    foreach ($objectgroups as $objectgroup) {

        $objectgroupsselected[$objectgroup['id']] = $objectgroup['description']. ' ('.$objectgroup['projectname'].')';
    }


$contractForm = new Form_SubForm($this);
$contractForm->setAttrib('title', 'Servicecontract');
$this->addSubForm($contractForm, 'contract_form');

	$contractForm->addElement('hidden', 'supplier', array(
	));

	$contractForm->addElement('text', 'supplier_buildname', array(
		'label' => 'Leverancier',
		'autocomplete' => 'off',
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Zoek op bedrijfsnaam.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'assign/search/',
			'autocompleter_options' => Zend_Json::encode([
				'hiddenElementId' => 'contract_form-supplier',
				'postData' => [
					'forComplaint' 	=> false,
					'outputJson'	=> true,
					'limit'			=> '10',
					'all'			=> true,
				]
			], false, ['enableJsonExprFinder' => true])
		),
		'validators' => [
			'ComboboxRequired' => [
				'validator'	=> 'ComboboxRequired',
				'options'	=> [
					'field' => $contractForm->getElement('supplier')
				]
			],
		],
	));

	$contractForm->addElement('text', 'identifier', [
		'label' => 'Contractnummer',
		'required' 		=>	true,
	]);

$contractForm->addElement('textarea', 'description', [
    'label' => 'Omschrijving',
    'expandable' => true
]);

$contractForm->addElement('select', 'type', [
    'label' => 'Koppelen aan',
    'multiOptions' => [
        '' => 'Maak een keuze',
        'project' => 'Project',
        'objectgroup' => 'Objectgroep',
        'object' => 'Enkel object',
        'multi_object' => 'Meerdere objecten'
    ],
    'required' => true
]);

$contractForm->addElement('select', 'project', [
		'label' => 'Project',
		'required' 		=>	false,
        'multiOptions' => [0 => 'Maak een keuze'] + $projects,

	]);

    $contractForm->addElement('select', 'objectgroup', [
        'label' => 'Objectgroep',
        'required' 		=>	false,
        'attribs' => array(
            'style' => 'width: 300px; height: 20px;',
        ),
        'multiOptions' => [0 => 'Maak een keuze'] + $objectgroupsselected,
    ]);


    $contractForm->addElement('text', 'object_address', [
        'label' => 'Object',
        'required' => false,
        'autocomplete' => 'off',
        'attribs' => [
            'title' => 'Zoeken op adres',
            'style' => 'width: 300px; height: 20px;',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
            'placeholder' => '',
            'autocompleter' => 'object/find-active-objects/limit/10/',
            'autocompleter_options' => Zend_Json::encode([
                'hiddenElementId' => 'contract_form-object'
            ], false, ['enableJsonExprFinder' => true])
        ],
        'validators' => [
            [
                'validator' => 'autoCompleterInputValidator',
                'options' => ['objects', 'object']
            ]
        ],
    ]);

    $contractForm->addElement('hidden', 'object', [
        'label' => '',
        'required' => false,
    ]);

$contractForm->addElement('text', 'multi_object_value', ['hidden' => true]);

	$contractForm->addElement('select', 'responsible_commercial', [
		'label' => 'Commercieel verantw.',
		'required' 		=>	true,
		'multiOptions'	=> (array) $internalUsers,
	]);

	$contractForm->addElement('select', 'responsible_financial', [
		'label' => 'Financieel verantw.',
		'required' 		=>	true,
		'multiOptions'	=> (array) $internalUsers,
	]);

	$contractForm->addElement('select', 'responsible_technical', [
		'label' => 'Technisch verantw.',
		'required' 		=>	true,
		'multiOptions'	=> (array) $internalUsers,
	]);

	$contractForm->addElement('checkbox', 'block', [
		'label' => 'Blokkeren',
		'value' => false,
		'attribs' => [
			'title' => 'Als dit aangevinkt is wordt dit contract niet opgenomen in verschillende overzichten.',
			'class' => 'forceAutoHint',
		],
	]);


$multiObjectSubForm = new Form_SubForm($this);
$multiObjectSubForm->setAttrib('title', 'Meerdere object selectie');
$multiObjectSubForm->setAttrib('id', 'multiObjectSubForm');
$this->addSubForm($multiObjectSubForm, 'multi_object_sub_form');

$multiObjectSubForm->addElement('text', 'multi_object_container', ['hidden' => true]);

$datesForm = new Form_SubForm($this);
$datesForm->setAttrib('title', '&nbsp;');
$this->addSubForm($datesForm, 'dates_form');

	$groupStructure = ContractGroups::getStructure();

	$groups = [];
	foreach ($groupStructure as $group)
		$groups[$group['id']] = $group['name'];

	$datesForm->addElement('select', 'group', [
		'label' => 'Hoofdgroep',
		'required' 		=>	true,
		'multiOptions'	=> $groups,
	]);

	$datesForm->addElement('select', 'subgroup', [
		'label' 					=> 'Subgroep',
		'registerInArrayValidator' 	=> false,
		'multiOptions'				=> [],
	]);

	$datesForm->addElement('text', 'payment_frequency', [
		'label' => 'Betalingsfrequentie (mnd)',
		'validators' => [
			[
				'validator'	=> 'Int',
			],
			[
				'validator'	=> 'GreaterThan',
				'options'	=> [
					'min' => 0,
				]
			],
		],
	]);

	$datesForm->addElement('text', 'from', [
		'label' => 'Begindatum',
		'required' 		=>	true,
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$datesForm->addElement('text', 'till', [
		'label' => 'Einddatum',
		'required' 		=>	true,
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$datesForm->addElement('checkbox', 'extend_tacitly', [
		'label' => 'Stilzwijgend verlengen',
		'value' => true,
	]);

	$datesForm->addElement('text', 'period_formula', [
		'label' => 'Verlengformule',
		'required' 		=>	false,
	]);

	$datesForm->addElement('hidden', 'period_formula_explain', [
	]);

	$datesForm->addElement('text', 'notice_period', [
		'label' => 'Opzegtermijn (mnd)',
		'validators' => [
			[
				'validator'	=> 'Callback',
				'options' => [
					'callback' => function($value, $allValues) {
						return ( $allValues['extend_tacitly'] === '0' || intval($value) > 0 );
					}
				],
			],
		],
	]);

	// this is used (via javascript) to set the subgroup field to the
	// original set value if the user switches to an other main group and back.
	$datesForm->addElement('hidden', 'original_subgroup', [
		'disabled' => 'disabled',
	]);

//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
