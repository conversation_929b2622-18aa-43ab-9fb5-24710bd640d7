<?
 //general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$Objectgroups = new Form_SubForm($this);
$Objectgroups->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($Objectgroups, 'objectgroups_form');

$Objectgroups->addElement('text', 'description', array(
		'label' => 'Naam',
	));

$Objectgroups->addElement('text', 'tiara_id', array(
    'label' => 'Tiara ID',
));


//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Categorie opslaan'
));



?>
