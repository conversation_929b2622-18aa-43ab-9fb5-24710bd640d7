<?

//general form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('id', 'complaintadd')
    ->setAttrib('class', 'complaintadd form');


$this->addElement('hidden', 'type', array());
$this->addElement('hidden', 'mapTo', array());

$this->addElement('text', 'object_search', array(
    'label' 		=> 	'Koppelen',
    'validators' 	=> 	array(),
    'autocomplete' => 'off',
    'attribs' => array(
        'title' => 'Zoek op adres van het object',
        'autocompleter' => 'object/add-direct-search-results/',
        'autocompleter_options' => Zend_Json::encode(array(
            'hiddenElementId' => 'mapTo'
        ))
    )
));


$db = Zend_Db_Table::getDefaultAdapter();

$options = $db->fetchPairs(
    $db->select()->from(array('p'=>'projects'),
        array('id','name')) ->order('p.name')
);

 $result['items'][0] =  "Maak je keuze" ;

foreach ($options as $key => $value) {
    $result['items'][$key] = $value;
}



$this->addElement('select', 'projectTo', array(
    'label' 		=> 'Project',
    'multiOptions'	=> $result['items'],
    'required' 		=>	true
));

//catagoryTitle
$this->addElement('select', 'objectgroupTo', array(
    'label' 		=> 'Objectgroep',
    'RegisterInArrayValidator' => false

));


$this->addButtons(false);
$this->addElement('submit', 'submitButton', array(
    'label' => 'Object verplaatsen '
));



