<?

	//general form options
	$this->setAction('photo/add/')
			->setMethod('post')
			->setAttrib('id', 'photoadd')
			->setAttrib('class', 'photoadd form');
	

	//category
	// the list of categories is filled by the PhotoController.addAction()
	$this->addElement('select', 'category', array(
		'label' 		=> 'Categorie',
		'multiOptions'	=> array(),
		'registerInArrayValidator' => false,
		'required' 		=>	true
	));
	
	//categoryTitle
	$this->addElement('select', 'categoryTitle', array(
		'label' 		=> 'Bestand',
		'RegisterInArrayValidator' => false,
		'disabled'		=>	true,
		'required' 		=>	true
	));


	//name
	$this->addElement('hidden', 'photo', array(
		'label' 		=> 	'Foto\'s',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//note
	$this->addElement('text', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));
					
	//submit
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
