<?
	//general form options
	$this
			->setMethod('post')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintexternal form');

 
	 //date
	$this->addElement('text', 'date', array(
		'label' => 'Datum aanmelding',
		'disabled' => true,
		'ignore' => true,
	));
	
	//deadline
	$this->addElement('text', 'status_date', array(
		'label' => 'Datum gepland servicebezoek',
		'filters' => array('StringTrim'),
		'validators' => array(
			'NotEmpty',
			array('DateAfter',false,
				array(
					'min' => date('d-m-Y H:i:s'),
				)
			)
		),
		'class' => 'DatePicker',
		'required' 		=>	true
	));

	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	'Toelichting',
		'validators' 	=> 	array(),
		'rows'			=> 	8,
		'required' 		=>	true
	));
	 
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Versturen'
	));
