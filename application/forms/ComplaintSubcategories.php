<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'complaintSubcategory')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'form');


	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		// retrieve the list of categories
		$ccs = (new ComplaintCategories())->getList();
		foreach ($ccs as $cc) {
			$categories[$cc['id']] = $cc['name'];
		}

		$general->addElement('select', 'category', array(
			'label' => 'Categorie',
			'required' => true,
			'multiOptions' => $categories,
			'value' => $this->getOwner()->getParam('catid')
		));

		$general->addElement('text', 'name', array(
			'label' => 'Omschrijving',
			'required' => true,
		));

        $general->addElement('text', 'name_en', array(
            'label' => 'Omschrijving Engels',

        ));
if (Settings::get('software_french_enabled')) {
        $general->addElement('text', 'name_fr', array(
            'label' => 'Omschrijving Frans',

        ));
}

if(Settings::get('complaint_create_invoice_when_done')) {
        $general->addElement('select', 'invoice_recipient', [
            'label' => 'Ontvanger facturatie',
            'multiOptions' => [
                'tenant' => 'Huurder',
                'investor' => 'Eigenaar'
            ],
        ]);
}

        $general->addElement('textarea', 'attentionfield', array(
            'label' => 'Hints getoond bij kiezen categorie door huurder',
            'rows' => 12,
            'cols' => 35
        ));

        $general->addElement('textarea', 'attentionfield_en', array(
            'label' => 'Hints getoond bij kiezen categorie door huurder (Engels)',
            'rows' => 12,
            'cols' => 35
        ));
        if (Settings::get('software_french_enabled')) {
            $general->addElement('textarea', 'attentionfield_fr', array(
                'label' => 'Hints getoond bij kiezen categorie door huurder (Frans)',
                'rows' => 12,
                'cols' => 35
            ));
        }

		$general->addElement('select', 'default_component_id', [
			'label' => 'Standaard component',
			'multiOptions' => $this->getOwner()->components,
		]);

		$general->addElement('hidden', 'subcategory_id', [
			'value' => $this->getOwner()->subcategoryId
		]);

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Categorie opslaan'
	));
?>
