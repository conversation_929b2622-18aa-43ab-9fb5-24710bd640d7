<?

$this
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');

$file_upload = new Form_SubForm($this);
$file_upload->setAttrib('title', 'File upload');
$this->addSubForm($file_upload, 'file_upload');

    $corpLib = new Corporation();
    $corporationOptions = $corpLib->getSimpleList();
    natcasesort($corporationOptions);

    $file_upload->addElement('select', 'corporation', [
        'label' => 'Juridische entiteit',
        'multiOptions' => $corporationOptions,
        'style' => 'width:280px;',
        'required' => true
    ]);

    $file_upload->addElement('select', 'type', [
        'label' => 'Type bestand',
        'multiOptions' => [
            '' => 'Kies een type bestand',
            'normal' => 'Financieel bestand',
            'project' => 'Financieel begroting bestand'
        ],
        'style' => 'width:280px;',
        'required' => true
    ]);

    $file_upload->addElement('file', 'file', [
        'label' => 'Bestand toevoegen',
        'required' => true,
        'style' => 'width:280px;'
    ]);


//submit
$this->addElement('submit', 'submit_button', [
    'label' => 'Opslaan'
]);

?>
