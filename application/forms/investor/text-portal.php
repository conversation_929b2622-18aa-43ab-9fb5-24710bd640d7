<?php

$this->setAction('')
    ->set<PERSON>ethod('post')
     ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'corporation form');

try {

    $first = new Form_SubForm($this);
    $this->addSubForm($first, 'first');

    $first->addElement('textarea', 'welcometext_investor_nl', array(
        'label' => 'Welkomsttekst Belegger (NL)',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'inmanagementtext_investor_nl' , array(
        'label' => 'Text overzicht vhe in beheer (NL)',
        'rows' => 10,
        'cols' => 80,
    ));

    $first->addElement('textarea', 'documentstext_investor_nl' , array(
        'label' => 'Text documenten (NL)',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'contacttext_investor_nl' , array(
        'label' => 'Text contactformulier (NL) ',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'yourinfotext_investor_nl' , array(
        'label' => 'Text uw gegevens (NL)',
        'rows' => 10,
        'cols' => 80,

    ));

	$first->addElement('textarea', 'yourinfotext_userpass_change_investor_nl' , [
		'label' => 'Text uw gegevens, gebruikersnaam+wachtwoord veranderen (NL)',
		'rows' => 10,
		'cols' => 80,
	]);

    $first->addElement('textarea', 'welcometext_investor_en', array(
        'label' => 'Welkomsttekst Belegger (EN)',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'inmanagementtext_investor_en' , array(
        'label' => 'Text overzicht vhe in beheer (EN)',
        'rows' => 10,
        'cols' => 80,
    ));

    $first->addElement('textarea', 'documentstext_investor_en' , array(
        'label' => 'Text documenten (EN)',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'contacttext_investor_en' , array(
        'label' => 'Text contactformulier (EN) ',
        'rows' => 10,
        'cols' => 80,

    ));

    $first->addElement('textarea', 'yourinfotext_investor_en' , array(
        'label' => 'Text uw gegevens (EN)',
        'rows' => 10,
        'cols' => 80,

    ));

	$first->addElement('textarea', 'yourinfotext_userpass_change_investor_en' , [
		'label' => 'Text uw gegevens, gebruikersnaam+wachtwoord veranderen (EN)',
		'rows' => 10,
		'cols' => 80,
	]);


    if(Settings::get('software_french_enabled')) {

        $first->addElement('textarea', 'welcometext_investor_fr', array(
            'label' => 'Welkomsttekst Belegger (FR)',
            'rows' => 10,
            'cols' => 80,

        ));

        $first->addElement('textarea', 'inmanagementtext_investor_fr' , array(
            'label' => 'Text overzicht vhe in beheer (FR)',
            'rows' => 10,
            'cols' => 80,
        ));

        $first->addElement('textarea', 'documentstext_investor_fr' , array(
            'label' => 'Text documenten (FR)',
            'rows' => 10,
            'cols' => 80,

        ));

        $first->addElement('textarea', 'contacttext_investor_fr' , array(
            'label' => 'Text contactformulier (FR)',
            'rows' => 10,
            'cols' => 80,

        ));

        $first->addElement('textarea', 'yourinfotext_investor_fr' , array(
            'label' => 'Text uw gegevens (FR)',
            'rows' => 10,
            'cols' => 80,

        ));

		$first->addElement('textarea', 'yourinfotext_userpass_change_investor_fr' , [
			'label' => 'Text uw gegevens, gebruikersnaam+wachtwoord veranderen (FR)',
			'rows' => 10,
			'cols' => 80,
		]);




    }

	$this->addElement('submit', 'aanmaken', [
		'label' => 'Opslaan'
	]);

} catch (Zend_Form_Exception $e) {
	throw $e;
}
