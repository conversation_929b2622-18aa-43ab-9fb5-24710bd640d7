<?php
    $internalUsers = (new Users())->getInternalUsersOptions();
    unset($internalUsers[568]);

	$internal_ids = array_keys(array_reverse($internalUsers, true));

	if((!Settings::get('ca_responsible_employee') || Settings::get('ca_responsible_employee') == 568) && count($internal_ids) > 0)
		Settings::set('ca_responsible_employee', $internal_ids[0]);


	//general form options
	$this->setAction('reminds/ca-settings/')
			->setMethod('post')
			->setAttrib('id', 'casettings')
			->setAttrib('class', 'casettings form');


	$settings = new Form_SubForm($this);
	$settings->setAttrib('title', 'Instellingen');
	$this->addSubForm($settings, 'settings');
	
		$settings->addElement('select', 'ca_responsible_employee', array(
			'label' => 'Contactpersoon incassobureau',
			'multiOptions' => $internalUsers,
			'RegisterInArrayValidator' => false,
			'attribs' => array(
				'title' => 'Gebruiker binnen het systeem welke op het incasso dossier weergegeven wordt als contactpersoon.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -7, 'y': 0}",
				'maxlength' => 11
			),
			'validators' => array()
		));
