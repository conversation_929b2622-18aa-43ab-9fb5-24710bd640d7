<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'rental-saved')
			->setAttrib('class', 'rental-saved form');

	$rentalSaved = new Form_SubForm($this);
	$rentalSaved->setAttrib('title', 'Huurbedrag');
	$this->addSubForm($rentalSaved, 'rentalSaved');
		
		$rentalSaved->addElement('text', 'amount', array(
			'label' => 'Bedrag'
		));
			//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Overschreven bedrag opslaan'
	));

?>