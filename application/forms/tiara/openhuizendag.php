<?php

function getHours()
{
    return [
        '01' => '01',
        '02' => '02',
        '03' => '03',
        '04' => '04',
        '05' => '05',
        '06' => '06',
        '07' => '07',
        '08' => '08',
        '09' => '09',
        '10' => '10',
        '11' => '11',
        '12' => '12',
        '13' => '13',
        '14' => '14',
        '15' => '15',
        '16' => '16',
        '17' => '17',
        '18' => '18',
        '19' => '19',
        '20' => '20',
        '21' => '21',
        '22' => '22',
        '23' => '23',
        '00' => '00'
    ];
}

function getMinutes()
{
    return [
        '00' => '00',
        '15' => '15',
        '30' => '30',
        '45' => '45'
    ];
}

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$openhuizendag = new Form_SubForm($this);
$openhuizendag->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($openhuizendag, 'openhuizendag');

$openhuizendag->addElement('text', 'date', [
    'label' => 'Datum',
    'class' => 'DatePicker'
]);

$openhuizendag->addElement('select', 'start_hour', [
    'label' => 'Start tijd',
    'value' => '12',
    'multiOptions' => getHours()
]);

$openhuizendag->addElement('select', 'start_minute', [
    'label' => '',
    'multiOptions' => getMinutes()
]);

$openhuizendag->addElement('select', 'end_hour', [
    'label' => 'Eind tijd',
    'value' => '13',
    'multiOptions' => getHours()
]);

$openhuizendag->addElement('select', 'end_minute', [
    'label' => '',
    'multiOptions' => getMinutes()
]);

$this->addElement('submit', 'aanmaken', ['label' => 'Aanmelden']);
