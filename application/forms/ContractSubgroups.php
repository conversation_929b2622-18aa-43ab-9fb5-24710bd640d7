<?

$contractGroupId = isset($this->getOwner()->contractGroupId) && intval($this->getOwner()->contractGroupId) > 0?
	$this->getOwner()->contractGroupId: null;

$contractSubgroupId = isset($this->getOwner()->contractSubgroupId) && intval($this->getOwner()->contractSubgroupId) > 0?
	$this->getOwner()->contractSubgroupId: null;


//general form options
$this->setAction('contract-subgroup/edit/id/'.$contractSubgroupId.'/contract-group-id/'.$contractGroupId)
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$contractSubgroupForm = new Form_SubForm($this);
$this->addSubForm($contractSubgroupForm, 'type_form');

	$contractSubgroupForm->addElement('text', 'name', [
		'label' => 'Naam',
		'required' => true,
	]);

	$contractSubgroupForm->addElement('hidden', 'group', [
		'required' => true,
		'value' => $contractGroupId,
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
