<?

	$collection = $this->getOwner()->collection;

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'projectTypes')
			->setAttrib('class', 'projectTypes form');
				

	$user = new Form_SubForm($this);
	$user->setAttrib('title', 'Begunstigde');
	
	$this->addSubForm($user, 'user');
			
		$user->addElement('text', 'name', array(
			'label' => 'Naam',
			'required' => true,
		));

		$user->addElement('text', 'iban', array(
			'label' => 'IBAN',
			'required' => false,
		));

		$user->addElement('text', 'bic', array(
			'label' => 'BIC',
			'required' => false,
		));		
		
		$user->addElement('text', 'city', array(
			'label' => 'Plaats'
		));		

	$details = new Form_SubForm($this);
	$details->setAttrib('title', 'Details');
	
	$this->addSubForm($details, 'details');

		$financial_export_group_collection = false;// Settings::get('financial_export_group_collection') && $collection['invoice_run_id'];

		$amount_attribs = array();

		if($financial_export_group_collection)
			$amount_attribs = array(
				'title' => 'Aanpassen van het bedrag is uitgeschakeld om de match van het bedrag bij de koppeling naar het financiele pakket te waarborgen',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 10, 'y': 0}"
			);

		$collection_attribs = [
			'label' => 'Bedrag',
			'required' => true,
			'ignore' => $financial_export_group_collection,
			'attribs' => $amount_attribs
		];

		if($financial_export_group_collection)
			$collection_attribs['readonly'] = true;

		$details->addElement('text', 'amount', $collection_attribs);		
		
		$details->addElement('textarea', 'description', array(
			'label' => 'Omschrijving',
			'required' => true,
			'attribs' => array(
				'style' => 'width: 300px; height: 40px;'
			)
		));		
		
	if($collection->type == 'credit'){
		$mandate = new Form_SubForm($this);
		$mandate->setAttrib('title', 'Machtiging');

		$this->addSubForm($mandate, 'mandate');

			$mandate->addElement('textarea', 'id', array(
				'label' => 'Machtigingskenmerk',
				'required' => false,
				'attribs' => array(
					'style' => 'width: 300px; height: 40px;',
					'placeholder' => (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
					'title' => 'Machtigingskenmerk voor incasso. De standaard waarde die hiervoor gebruikt wordt is het ' . (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': 10, 'y': 0}"
				)
			));

			$mandate->addElement('text', 'date', array(
				'label' => 'Machtigingsdatum',
				'required' => false,
				'placeholder' => '01-11-2009',
				'attribs' => array(
					'class' => 'DatePicker',
					'title' => 'Datum van ondertekening van de machtiging. Bij bestaande machtigingen is deze datum altijd 1 november 2009, de standaard datum voor SEPA incasso.',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': 2, 'y': -3}"
				)
			));
	}

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Opslaan'
	));

?>