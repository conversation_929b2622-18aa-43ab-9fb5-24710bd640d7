
<?
 //general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form')
    	->setAttrib('fullwidth', true);


$SupportComplaintsProjects = new Form_SubForm($this);
$SupportComplaintsProjects->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($SupportComplaintsProjects, 'support_complaints_projects_form');


$projectLib = new Project();
$projs = (array) $projectLib->getList();
foreach ($projs as $project)
    $projects[$project['id']] = $project['name'];

$SupportComplaintsProjects->addElement('select', 'project', [
    'label' => 'Project',
    'required' 		=>	true,
    'multiOptions'	=> (array) $projects,
]);


//name
$SupportComplaintsProjects->addElement('text', 'title', array(
    'label' 		=> 	'Titel',
    'validators' 	=> 	[],
    'required' 		=>	true
));

$SupportComplaintsProjects->addElement('text', 'budget', array(
    'label' 		=> 	'Budget',
    'validators' 	=> 	[],
    'required' 		=>	true
));


//description
$SupportComplaintsProjects->addElement('textarea', 'description', array(
    'label' 		=> 	'Omschrijving',
    'validators' 	=> 	[],
    'rows'			=> 	8,
    'cols'			=> 	50,
    'required' 		=>	true
));

$SupportComplaintsProjects->addElement('checkbox', 'completed', array(
    'label' => 'Werk afgerond?',
    'attribs' => array(
        'title' => 'Werk is afgerond',
        'class' => 'forceAutoHint'
    )
));

$SupportComplaintsProjects->addElement('hidden', 'user_id', [
    'value' => loginManager::data()->id
]);



//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Werk opslaan'
));



?>

