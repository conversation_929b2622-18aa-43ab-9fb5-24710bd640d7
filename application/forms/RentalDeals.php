<?
//general form options
$this->setAction('rental-deals/edit/')
	->setMethod('post')
	->setAttrib('id', 'rental_deals')
	->setAttrib('prevent_enter_submit', '1')
	->setAttrib('class', 'rental_deals form')
    ->setAttrib('fullwidth', true);

$rentalDeals = new Form_SubForm($this);
$rentalDeals->setAttrib('title', 'Deal toevoegen');
$this->addSubForm($rentalDeals, 'rental_deals');

if(Settings::get('modules_rental_contract_deals')) {
	$rentalDeals->addElement('hidden', 'contract_id', array(
		'value' => $this->getOwner()->getParam('contract_id')
	));

	$rentalDeals->addElement('text', 'contract', array(
		'label' => 'Contract',
		'required' => true,
		'autocomplete' => 'off',
		'value' => $address ?: '',
		'attribs' => array(
			'title' => 'Zoeken op contractnummer',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'contract/find/limit/10/',
			'autocompleter_options' => Zend_Json::encode([
				'hiddenElementId' => 'rental_deals-contract_id'
			], false, array('enableJsonExprFinder' => true))
		)
	));
} else {

    $rentalDeals->addElement('hidden', 'user_id', array(
        'value' => $this->getOwner()->getParam('user_id')
    ));

    if($this->getOwner()->getParam('user_id') > 0)
        $user = db()->fetchOne(db()->select()->from('users', ['rendered_name'])->where('id = ?', $this->getOwner()->getParam('user_id')));

    $rentalDeals->addElement('text', 'user', array(
        'label' => 'Kandidaat',
        'autocomplete' => 'off',
        'required' 		=>	true,
        'value' => $user ?: '',
        'attribs' => [
            'style' => 'width: 600px; height: 20px;',
            'title' => 'Zoek op naam- of bedrijfsnaam.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
            'placeholder' => '',
            'autocompleter' => 'contract/find-registrants/1/',
            'autocompleter_options' => Zend_Json::encode([
                'hiddenElementId' => 'rental_deals-user_id'
            ], false, ['enableJsonExprFinder' => true])
        ],
        'validators' => [
            [
                'validator' => 'autoCompleterInputValidator',
                'options' => ['users', 'user_id']
            ]
        ]
    ));



	$rentalDeals->addElement('hidden', 'object_id', array(
		'value' => $this->getOwner()->getParam('object_id')
	));

	if ($this->getOwner()->getParam('object_id') > 0)
		$address = db()->fetchOne(db()->select()->from('objects', ['rendered_address'])->where('id = ?', $this->getOwner()->getParam('object_id')));

	$rentalDeals->addElement('text', 'object', array(
		'label' => 'Object',
		'required' => true,
		'autocomplete' => 'off',
		'value' => $address ?: '',
		'attribs' => array(
			'title' => 'Zoeken op adres',
            'style' => 'width: 600px; height: 20px;',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'object/find-active-objects/limit/10/',
			'autocompleter_options' => Zend_Json::encode([
				'hiddenElementId' => 'rental_deals-object_id'


			], false, array('enableJsonExprFinder' => true))
		),
        'validators' => [
            [
                'validator' => 'autoCompleterInputValidator',
                'options' => ['objects', 'object_id']
            ]
        ],
	));



}

$usersModel = new Users();
$intUsers = $usersModel->getInternalRentalUsers();

foreach ($intUsers as $user)
	if(empty($user['enddate']))
		$internalUsers[$user['id']] = User::buildname($user);

    if(count($internalUsers) <= 0){
        die('<br /><br /><br />Kan geen deals aanmaken, er zijn geen medewerkers van afdeling "verhuur" aangemaakt');
    }


if(!Settings::get('software_only_rental')) {


    $rentalDeals->addElement('select', 'created_user_id', array(
        'label' => 'Medewerker',
        'required' => true,
        'value' => loginManager::data()->id,
        'multiOptions' => $internalUsers
    ));
}
    $allowed_projects = loginManager::allowedProjects();
    $project_default_template_id = false;
    if (count($allowed_projects) > 0 && $allowed_projects !== false) {
        $select = db()->select()
            ->from('projects', ['rental_deals_default_template'])
            ->where('id = ?', $allowed_projects[0]);

        $project_default_template_id = db()->fetchOne($select);
    }

    $select = db()->select()
        ->from('rental_deals_status_templates', ['id', 'name'])
        ->where('finalized = ?', true)
        ->where('concept = ?', false)
        ->where('archived = ?', false)
        ->order(['default DESC', 'name ASC']);

    if ($allowed_projects = loginManager::allowedProjects()) {
        $select->where('`projects` IS NULL OR CONCAT(",", `projects`, ",") REGEXP ",(' . implode('|', $allowed_projects) . '),"');
    }

    $rentalDeals->addElement('select', 'status_template_id', [
        'label' => 'Workflow',
        'validators' => [],
        'multiOptions' => db()->fetchPairs($select),
        'value' => $project_default_template_id > 0 ? $project_default_template_id : false
    ]);

if (is_null($this->getOwner()->getParam('id'))) {
    $rentalDeals->addElement('select', 'silent_forward_to_status_type_id', [
        'label' => 'Direct naar status',
        'validators' => [],
        'registerInArrayValidator' => false,
        'attribs' => [
            'title' => 'Met deze optie kan een deal direct in een bepaalde status worden geplaatst, zonder dat er E-mail berichten verstuurd worden aan de kandidaat.',
            'class' => 'forceAutoHint',
        ]
    ]);
}
$rentalDeals->addElement( 'select', 'profile_completion', [
    'label'        => ucfirst(translate()->_('profile_completion')),
    'multiOptions' => [
        '' => 'Zoals nu bij kandidaat ingesteld',
        '9' => 'Account aangemaakt/inschrijving ontvangen',
        '1' => 'Ingeloot/toegewezen',
        '2' => 'Wachten op aanlevering dossier',
        '3' => 'Dossier compleet, wachten op goedkeuring dossier',
        '0' => 'Dossier goedgekeurd (mag ondertekenen of heeft ondertekend)',
    ],
    'attribs'      => [
        'title'   => 'Hier kan je aangeven welke status van verhuurflow de kandidaat zich bevindt.',
        'class'   => 'forceAutoHint',
    ],
] );

$rentalDeals->addElement('checkbox', 'force', array(
	'attribs' => ['style' => 'display:none;']
));

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Deal toevoegen'
));
