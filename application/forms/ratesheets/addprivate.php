<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'rate')
			->setAttrib('class', 'rate form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//project
		$general->addElement('text', 'project', array(
			'label' => 'Project',
			'validators' => array(
			),
		));

		//date
		$general->addElement('text', 'date', array(
			'label' => 'Datum',
			'validators' => array(
			),
		));

		// title
		$general->addElement('text', 'title', array(
			'label' => 'Naam tariefblad',
			'validators' => array(
			),
		));

		//
		$general->addElement('text', 'emptyrate', array(
			'label' => 'Percentage bij leegstand',
			'validators' => array(
			),
		));

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => 'Tariefblad toevoegen'
	));

?>