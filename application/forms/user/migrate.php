<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'migrate')
			->setAttrib('class', 'migrate form');

	$this->addElement('text', 'end', array(
		'label' 		=> 	'Einddatum',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

/*
	$this->addElement('text', 'meterend', array(
		'label' 		=> 	'Eindstand',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));
*/

	/**
	 * user address data
	 */
	$userAddress = new Form_SubForm($this);
	$this->addSubForm($userAddress, 'userAddress');

		//address
		$userAddress->addElement('text', 'address', array(
			'label' 		=> 	'Adres',
			'validators' 	=> 	array(),
			'required' 		=>	true
		));

		//number
		$userAddress->addElement('text', 'number', array(
			'label' 		=> 	'Huisnummer',
			'validators' 	=> 	array(),
			'required' 		=>	true
		));

		//zipcode
		$userAddress->addElement('text', 'zip', array(
			'label' 		=> 	'Postcode',
			'validators' 	=> 	array(
				array(
					'regex',
					false,
					'options' => array(
						'pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/'
					)
				)
			),
			'required' 		=>	true
		));

		//city
		$userAddress->addElement('text', 'city', array(
			'label' 		=> 	'Woonplaats',
			'validators' 	=> 	array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			),
			'required' 		=>	true
		));

	/**
	 * next tenant
	 */
	$nextTenant = new Form_SubForm($this);
	$this->addSubForm($nextTenant, 'nextTenant');

		$nextTenant->addElement('text', 'name', array(
			'label' 		=> 	'Naam',
			'validators' 	=> 	array(),
			'required' 		=>	true
		));

		$nextTenant->addElement('text', 'phone', array(
			'label' 		=> 	'Telefoonnummer',
			'validators' 	=> 	array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 13)
				)
			),
			'required' 		=>	true
		));

		$nextTenant->addElement('text', 'email', array(
			'label' 		=> 	'E-mail',
			'validators' 	=> 	array('emailAddress')
		));

		$nextTenant->addElement('text', 'residents', array(
			'label' 		=> 	'Aantal personen',
			'validators' 	=> 	array(
				array(
					'validator' => 'Digits'
				)
			)
		));

	//submit
	$this->addElement('submit', 'submit', array(
		'label' => 'Versturen'
	));