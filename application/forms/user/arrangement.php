<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'arrangement')
			->setAttrib('class', 'arrangement form');

	/**
	 * General arrangement data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//period
		$general->addElement('text', 'period', array(
			'label' => 'Verdelen over aantal maanden',
			'validators' => array(
			),
		));

		//payed
		$general->addElement('text', 'payed', array(
			'label' => 'Aantal keren betaald',
			'validators' => array(
			),
		));

/*		//monthly
		$general->addElement('text', 'amount', array(
			'label' => 'Maandelijks bedrag',
			'validators' => array(),
		));*/

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => 'Opslaan'
	));