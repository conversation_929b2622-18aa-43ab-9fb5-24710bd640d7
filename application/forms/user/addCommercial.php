<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'useraddCommercial')
			->setAttrib('class', 'useradd commercial form');
	
	$legal = new Form_SubForm($this);
	$this->addSubForm($legal, 'legal');

		for ($i=0; $i < 3; $i++) { 
			//gender
			$legal->addElement('select', 'gender_' . $i, array(
				'label' 		=> 	'Geslacht',
				'multioptions' 	=> array(
										'male' => 'Man',
										'female' => 'Vrouw',
										'unknown' => 'Onbekend'
									),
				'validators' 	=> 	array(),
			));

			// initials
			$legal->addElement('text', 'initials_' . $i, array(
				'label' 		=> 	'Voorletters',
				'validators' 	=> 	array(),
			));


				//firstname
			$legal->addElement('text', 'firstname_' . $i, array(
				'label' => 'Voornaam',
				'validators' 	=> 	array(),
			));

			//middlename
			$legal->addElement('text', 'middlename_' . $i, array(
				'label' => 'Tussenvoegsel',
				'validators' 	=> 	array(),
			));

			//name
			$legal->addElement('text', 'name_' . $i, array(
				'label' => 'Achternaam',
				'validators' 	=> 	array(),
			));
		}



		/**
	 * name data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//gender
		$general->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$general->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' 	=> 	array(),
		));


			//firstname
		$general->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' 	=> 	array(),
		));

		//middlename
		$general->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' 	=> 	array(),
		));

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' 	=> 	array(),
		));
		
		//email
		$general->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress')
		));
		
		// telephone 1
		$general->addElement('text', 'phone1', array(
			'label' 		=> 	'Telefoon nummer',
			'validators' 	=> 	array(),
		));		
		
		// telephone 2
		$general->addElement('text', 'phone2', array(
			'label' 		=> 	'Alternatief telefoon nummer',
			'validators' 	=> 	array(),
		));
		

		// corporation
		$general->addElement('text', 'company', array(
			'label' 		=> 	'Bedrijfsnaam',
			'validators' 	=> 	array(),
		));
		
		// chain
		$general->addElement('text', 'chain', array(
			'label'         => 'Keten',
			'validators'    => array(),
		));

		// kvk
		$general->addElement('text', 'kvk', array(
			'label' 		=> 	'KvK-nummer',
			'validators' 	=> 	array(),
		));

		// kvk
		$general->addElement('text', 'tax', array(
			'label' 		=> 	'BTW-nummer (zonder NL)',
			'validators' 	=> 	array(),
		));

		//City
		$general->addElement('text', 'statutory', array(
			'label' => 'Statutair gevestigd te',
			'validators' 	=> 	array(),

		));

		// comment
		$this->addElement('textarea', 'comment', array(
			'label' 		=> 	false,
			'validators' 	=> 	array(),
		));	

		//SMS benaderen
		$general->addElement('checkbox', 'sms', array(
			'label' => 'Informeren per sms?',
			'validators' => array(),
		));
		//olddebtorcode
		$general->addElement('text', 'olddebtorcode', array(
			'label' 		=> 	'Deb. code financieel',
			'validators' 	=> 	array(),
		));	



		//first
		$general->addElement('select', 'first', array(
			'label' 		=> 	'Contracteren afgerond',
			'multioptions' 	=> array('1' => 'Nee', '0' => 'Ja'),
			'validators' 	=> 	array(),
		));	
	/**
	 * invoiceing data
	 */
	$invoiceing = new Form_SubForm($this);
	$this->addSubForm($invoiceing, 'invoiceing');
		
		//userselect
		$invoiceing->addElement('select', 'userselect', array(
			'label' 		=> 	'Ter attentie van',
			'multioptions' 	=> array(
									'contractant' => 'Contractant',
									'invoice' => 'Contactpersoon facturatie'
								),
			'validators' 	=> 	array(),
		));

		//gender
		$invoiceing->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$invoiceing->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' 	=> 	array(),
		));


			//firstname
		$invoiceing->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' 	=> 	array(),
		));

		//middlename
		$invoiceing->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' 	=> 	array(),
		));

		//name
		$invoiceing->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' 	=> 	array(),
		));

		//Phone home
		$invoiceing->addElement('text', 'phone', array(
			'label' => 'Telefoon',
			'validators' 	=> 	array(),
		));



		//email
		$invoiceing->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress')
		));
		
		// tav
		$invoiceing->addElement('text', 'tav', array(
			'label' 		=> 	'Ter attentie van',
			'validators' 	=> 	array(),
		));

		// tav
		$invoiceing->addElement('text', 'department', array(
			'label' 		=> 	'Afdeling',
			'validators' 	=> 	array(),
		));

		// mention
		$invoiceing->addElement('text', 'mention', array(
			'label' 		=> 	'Onder vermelding van',
			'validators' 	=> 	array(),
		));


		//address
		$invoiceing->addElement('text', 'address', array(
			'label' => 'Straatnaam',
			'validators' => array()
		));

		//number
		$invoiceing->addElement('text', 'number', array(
			'label' => 'Huisnummer',
			'validators' => array()
		));

		//zipcode
		$invoiceing->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => array()
		));

		//City
		$invoiceing->addElement('text', 'city', array(
			'label' => 'Vestigingsplaats',
			'validators' 	=> 	array(),

		));

		$invoiceing->addElement('checkbox', 'overrule', array(
			'label' => 'Adressering overschrijven:',
			'validators' 	=> 	array(),

		));

		$invoiceing->addElement('hidden', 'overrule_text', array(
			'label' => '',
			'validators' 	=> 	array(),

		));


	/**
	 * emergency contact data
	 */
	$emergency = new Form_SubForm($this);
	$this->addSubForm($emergency, 'emergency');


		//emergency
		$emergency->addElement('select', 'userselect', array(
			'label' 		=> 	'Contractpersoon',
			'multioptions' 	=> array(
									'contractant' => 'Contractant',
									'custom' => 'Aangepast'
								),
			'validators' 	=> 	array(),
		));

		//gender
		$emergency->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$emergency->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' 	=> 	array(),
		));


			//firstname
		$emergency->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' 	=> 	array(),
		));

		//middlename
		$emergency->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' 	=> 	array(),
		));

		//name
		$emergency->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' 	=> 	array(),
		));

		//Phone home
		$emergency->addElement('text', 'phone', array(
			'label' => 'Telefoon',
			'validators' 	=> 	array(),
		));



		//email
		$emergency->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress')
		));


	/**
	 * invoice data
	 */
	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');


		//Invoice interval
		$invoice->addElement(
			new Zend_Form_Element_Select('rate',
				array(
					'label' => 'Facturering per',
					'multioptions' => UserInvoicePreferences::getInvoicePeriodOptions(),
					'value' => (Settings::get('software_type') == 'energy'? 4: 12)
				)
			)
		);

		$invoice->addElement('text', 'title', array(
			'label' 		=> 	'Factuur omschrijving',
			'validators' 	=> 	array(),
			'attribs' 		=> array(
								'placeholder' => (Settings::get('software_type') == 'energy' ? 'Voorschotfactuur' : 'Factuur') . ' {$periode}',
								'title' => 'Factuur omschrijving, voeg de de variabele {$periode} in op de plaats waar de periode bepaling in de omschrijving geplaatst moet worden.',
								'class' => 'forceAutoHint'
							)
		));

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('type',
				array(
					'label' => 'Type betaling',
					'multioptions' => array(
						'collection' => 'Automatische incasso',
						'ideal' => 'Handmatig overmaken'
					),
					'value' => 'ideal'
				)
			)
		);

		$invoice->addElement('textarea', 'mandate_id', array(
			'label' => 'Machtigingskenmerk',
			'required' => false,
			'attribs' => array(
				'style' => 'width: 300px; height: 40px;',
				'placeholder' => (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
				'title' => 'Machtigingskenmerk voor incasso. De standaard waarde die hiervoor gebruikt wordt is het ' . (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 10, 'y': 0}"
			)
		));

		$invoice->addElement('text', 'mandate_date', array(
			'label' 		=> 	'Machtigingsdatum',
			'validators' 	=> 	array(),
			'placeholder' => '01-11-2009',
			'attribs' => array(
				'class' => 'DatePicker',
				'title' => 'Datum van ondertekening van de machtiging. Bij bestaande machtigingen is deze datum altijd 1 november 2009, de standaard datum voor SEPA incasso.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 2, 'y': -3}"
			)
		));	

		$invoice->addElement('select', 'sepa_status', array(
			'label' => 'SEPA incasso status',
			'multiOptions' => array(
				'FRST' => 'Eerste incasso',
				'RCUR' => 'Wederkerende incasso',
				'FNAL' => 'Laatste incasso',
				'OOFF' => 'Eenmalige incasso'
			),
			'attribs' => array(
				'title' => 'Mogelijke sequentie opties ten behoeve van het genereren van een SEPA-incasso bestand.<br /><br /> Standaard staat deze waarde op "Eerste incasso" en bij het afronden van de eerste incasso wordt deze automatisch op "Wederkerende incasso" gezet.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));	

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('bill',
				array(
					'label' => 'Factuur ontvangen per',
					'multioptions' => array(
						'email' => 'E-mail',
						'mail' => 'Post',
						'none' => 'Geen factuur versturen',
					)
				)
			)
		);

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('tax',
				array(
					'label' => 'BTW-plichtig',
					'multioptions' => array(
						0 => 'Nee',
						1 => 'Ja'
						
					),
				)
			)
		);

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('tax_compensation',
				array(
					'label' => 'BTW-compensatie',
					'multioptions' => array(
						0 => 'Nee',
						1 => 'Ja'
					)
				)
			)
		);
		
		// bankaccount
		$invoice->addElement('text', 'bankaccount', array(
			'label' 		=> 	'Rekeningnummer',
			'validators' 	=> 	array(),
		));
		
		// IBAN
		$invoice->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
			'validators' 	=> 	array('iban'),
		));	

		// BIC
		$invoice->addElement('text', 'bic', array(
			'label' 		=> 	'BIC',
			'validators' 	=> 	array(),
		));	


			// bankaccountname
		$this->addElement('text', 'bankaccountname', array(
			'label' 		=> 	'Tenaamstelling rek. nr.',
			'validators' 	=> 	array(),
		));	

		// banktype
		$invoice->addElement('select', 'banktype', array(
			'label' 		=> 	'Bank/giro',
			'multioptions' 	=> array(
									'normal' => 'Bank',
									'giro' => 'Giro'
								),
			'validators' 	=> 	array(),
		));

		// banktype
		$invoice->addElement('select', 'addr', array(
			'label' 		=> 	'Adres',
			'multioptions' 	=> array(
									'object' => 'Object adres',
									'invoice' => 'Factuur adres'
								),
			'validators' 	=> 	array(),
		));
								
		//submit
		$this->addElement('submit', 'submit', array(
			'label' => 'Toevoegen'
		));		
	?>
