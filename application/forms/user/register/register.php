<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'register')
			->setAttrib('class', 'register form');

	$register = new Form_SubForm($this);
	$this->addSubForm($register, 'user');

	//email
	$register->addElement('text', 'username', array(
		'label' => translate()->_('username'),
		'validators' => array(
			array(
				'validator' => 'stringLength',
				'options'   => array(3, 50)
			)
		),
		'attribs' => array(
				'placeholder' => translate()->_('username')
		),
		'required' => true
	));

	//password
	$register->addElement('password', 'password', array(
		'label' => translate()->_('password'),
		'required' => true,
		'renderPassword' => true,
		'validators' => [
            'passwordPattern'
        ],
		'attribs' => array(
				'placeholder' => translate()->_('password')
		),
	));

	//password
	$register->addElement('password', 'password_repeat', array(
		'label' => translate()->_('password_repeat'),
		'required' => true,
		'renderPassword' => true,
		'validators' => array(
			array(
				'validator' => 'stringLength',
				'options'   => array(5, 30)
			)
		),
		'attribs' => array(
				'placeholder' => translate()->_('password_repeat')
		),
	));

	//register submit
	$this->addButtons(false);
	$this->addElement('submit', 'register', array(
		'label'	=> translate()->_('submit'),
	));

?>