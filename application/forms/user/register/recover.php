<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'recover')
			->setAttrib('class', 'recover form');



	//email
	$this->addElement('text', 'email', array(
		'label' => translate()->_('email_address'),
		'validators' => array('emailAddress')
	));

	

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'recover', array(
		'label' => translate()->_('send')
	));

?>