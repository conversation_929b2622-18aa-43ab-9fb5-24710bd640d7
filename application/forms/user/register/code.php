<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'register')
			->setAttrib('class', 'register form');



	//code
	$this->addElement('text', 'code', array(
		'label' => translate()->_('personal_code'),
		'validators' => array(
			array(
				'validator' => 'stringLength',
				'options'   => array(6, 6)
			)
		),
		'required' => true
	));

	//code
	$this->addElement('hidden', 'language', array(
	));

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'register', array(
		'label' => translate()->_('submit')
	));

?>