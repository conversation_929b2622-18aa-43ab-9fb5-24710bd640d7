<?php

if ($type['is_bool']) {
    $spec_group_subform->addElement('select', $type['type_name'], [
        'label' => translate()->_($type['type_name']),
        'multioptions' => [
            -1 => ucfirst(translate()->_('no_preference')),
            '0' => ucfirst(translate()->_('no')),
            '1' => ucfirst(translate()->_('yes'))
        ],
        'validators' => [],
        'value' => $type['value']
    ]);
} elseif ($type['input_type'] == 'select') {

    $specification_type_options = [];
    foreach ($specificationSelectOptions as $specification_option) {
        if ($specification_option['type_id'] == $type['type_id']) {
            $specification_type_options[$specification_option['id']] = $specification_option['value'];
        }
    }
    $spec_group_subform->addElement('select', $type['type_name'], [
        'label' => translate()->_($type['type_name']),
        'multioptions' => [-1 => 'Geen voorkeur'] + $specification_type_options,
        'validators' => [],
        'value' => $type['value']
    ]);
} elseif ($type['input_type'] == 'input') {
    $spec_group_subform->addElement('text', $type['type_name'], [
        'label' => translate()->_($type['type_name']),
        'validators' => [],
        'value' => $type['value']
    ]);
} else {
    $spec_group_subform->addElement('range', $type['type_name'], [
        'label' => translate()->_($type['type_name']),
        'value' => is_null($type['value_min']) ? $type['filter_range_default'] : $type['value_min'] . ',' . $type['value_max'],
        'attribs' => [
            'multiple' => true,
            'min' => $type['filter_range_min'],
            'max' => $type['filter_range_max'],
            'step' => $type['filter_range_step']
        ]
    ]);
}
