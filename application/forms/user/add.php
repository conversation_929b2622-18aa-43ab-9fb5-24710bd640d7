<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'useradd')
			->setAttrib('class', 'useradd form');
		
		//initials
		$this->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' 	=> 	array(),
		));	
		
		//name
		$this->addElement('text', 'firstname', array(
			'label' 		=> 	'Voornaam',
			'validators' 	=> 	array(),
		));		

		//middlename
		$this->addElement('text', 'middlename', array(
			'label' 		=> 	'Tussenvoegsel',
			'validators' 	=> 	array(),
		));	
			
		//name
		$this->addElement('text', 'investor', array(
			'label' 		=> 	'Belegger',
			'validators' 	=> 	array(),
		));	
			
		//name
		$this->addElement('text', 'name', array(
			'label' 		=> 	'Achternaam',
			'validators' 	=> 	array(),
		));		
		
		//gender
		$this->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man', 
									'female' => 'Vrouw',
									'unknown' => 'Onbekend'
								),
			'validators' 	=> 	array(),
		));

		//olddebtorcode
		$this->addElement('text', 'olddebtorcode', array(
			'label' 		=> 	'Deb. code financieel',
			'validators' 	=> 	array(),
		));		

		//first
		$this->addElement('select', 'first', array(
			'label' 		=> 	'Contracteren afgerond',
			'multioptions' 	=> array('1' => 'Nee', '0' => 'Ja'),
			'validators' 	=> 	array(),
		));		

		/**
		 * name data
		 */
		$partner = new Form_SubForm($this);
		$this->addSubForm($partner, 'partner');

			
			//id
			$partner->addElement('hidden', 'id', array(
				'label' 		=> 	'',
				'validators' 	=> 	array(),
			));	
			
			//initials
			$partner->addElement('text', 'initials', array(
				'label' 		=> 	'Voorletters',
				'validators' 	=> 	array(),
			));	
			
			//name
			$partner->addElement('text', 'firstname', array(
				'label' 		=> 	'Voornaam',
				'validators' 	=> 	array(),
			));		
	
			//middlename
			$partner->addElement('text', 'middlename', array(
				'label' 		=> 	'Tussenvoegsel',
				'validators' 	=> 	array(),
			));	
				
			//name
			$partner->addElement('text', 'investor', array(
				'label' 		=> 	'Belegger',
				'validators' 	=> 	array(),
			));	
				
			//name
			$partner->addElement('text', 'name', array(
				'label' 		=> 	'Naam',
				'validators' 	=> 	array(),
			));	
			
			// telephone 1
			$partner->addElement('text', 'phone1', array(
				'label' 		=> 	'Telefoonnummer [thuis]',
				'validators' 	=> 	array(),
			));		
			
			// telephone 2
			$partner->addElement('text', 'phone2', array(
				'label' 		=> 	'Telefoonnummer [mobiel]',
				'validators' 	=> 	array(),
			));	
			
			
			// telephone emergency
			$partner->addElement('text', 'phone_emergency', array(
				'label' 		=> 	'Telefoonnummer [nood]',
				'validators' 	=> 	array(),
			));	
			
			// email
			$partner->addElement('text', 'email', array(
				'label' 		=> 	'Email',
				'validators' 	=> 	array(),
			));	
			
			//birthdate
			$partner->addElement('text', 'bdate', array(
				'label' 		=> 	'Geboortedatum',
				'validators' 	=> 	array(),
			));		
				
			// bankaccount
			$partner->addElement('text', 'bankaccount', array(
				'label' 		=> 	'Bankrekening',
				'validators' 	=> 	array(),
			));	

		
			
			// bankaccountname
			$partner->addElement('text', 'bankaccountname', array(
				'label' 		=> 	'Tenaamstelling rek. nr.',
				'validators' 	=> 	array(),
			));
			
			// BSN
			$partner->addElement('text', 'BSN', array(
				'label' 		=> 	'Burgerservicenummer',
				'validators' 	=> 	array(),
			));

			// identication_type
			$partner->addElement('select', 'identication_type', [
				'label' 		=> 	'Identificatiesoort',
				'multiOptions'	=> Users::$identificationTypeLabels,
			]);

			// identication_valid_till
			$partner->addElement('text', 'identication_valid_till', [
				'label' 		=> 'Identificatie geldig tot',
				'attribs' 		=> [
					'class' => 'DatePicker',
				],
				'validators' 	=> [
					[
						'validator'	=> 'Date',
						'options'	=> [
							'locale' => 'nl',
						]
					]
				]
			]);


			// banktype
			$partner->addElement('select', 'banktype', array(
				'label' 		=> 	'Bank/giro',
				'multioptions' 	=> array(
										'normal' => 'Bank', 
										'giro' => 'Giro'
									),
				'validators' 	=> 	array(),
			));	
			
			// corporation
			$partner->addElement('text', 'corporation', array(
				'label' 		=> 	'Bedrijf',
				'validators' 	=> 	array(),
			));			
				
			//gender
			$partner->addElement('select', 'gender', array(
				'label' 		=> 	'Geslacht',
				'multioptions' 	=> array(
										'male' => 'Man', 
										'female' => 'Vrouw'
									),
				'validators' 	=> 	array(),
			));	
			
		/**
		 * invoice data
		 */
		$invoice = new Form_SubForm($this);
		$this->addSubForm($invoice, 'invoice');
	
		
			//Invoice interval
			$invoice->addElement(
				new Zend_Form_Element_Select('address',
					array(
						'label' => 'Factuur adres',
						'multioptions' => array(
							'-1' =>	'Selecteer een adres',
							'object' 	=> 'Objectadres',
							'user' 	=> 'Factuuradres'
						),
					)
				)
			);
			
			//Invoice interval
			$invoice->addElement(
				new Zend_Form_Element_Select('rate',
					array(
						'label' => 'Facturering per',
						'multioptions' => array('-1' =>	'Selecteer een periode') + UserInvoicePreferences::getInvoicePeriodOptions()
					)
				)
			);

			$invoice->addElement('text', 'title', array(
				'label' 		=> 	'Factuur omschrijving',
				'validators' 	=> 	array(),
				'attribs' 		=> array(
										'placeholder' => (Settings::get('software_type') == 'energy' ? 'Voorschotfactuur' : 'Factuur') . ' {$periode}',
										'title' => 'Factuur omschrijving, voeg de de variabele {$periode} in op de plaats waar de periode bepaling in de omschrijving geplaatst moet worden.',
										'class' => 'forceAutoHint'
									)
			));

			//Payment type
			$invoice->addElement(
				new Zend_Form_Element_Select('type',
					array(
						'label' => 'Type betaling',
						'multioptions' => array(
							'-1' =>	'Selecteer een type',
							'ideal' => 'Handmatig overmaken',
							'collection' => 'Automatische incasso'
							
						)
					)
				)
			);


			// provision percentage
			$invoice->addElement('text', 'provision', array(
				'label' 		=> 	'Provisie (percentage aan klant doorberekend)',
				'validators' 	=> 	array(),
				'attribs'		=> array('title' => 'Percentage klantprovisie', 'class' => 'forceAutoHint')

			));

			$invoice->addElement('textarea', 'mandate_id', array(
				'label' => 'Machtigingskenmerk',
				'required' => false,
				'attribs' => array(
					'style' => 'width: 300px; height: 40px;',
					'placeholder' => (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
					'title' => 'Machtigingskenmerk voor incasso. De standaard waarde die hiervoor gebruikt wordt is het ' . (Settings::get('software_type') == 'energy' ? 'VaSof ' : 'Omniboxx') . ' klantnummer',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': 10, 'y': 0}"
				)
			));

			$invoice->addElement('text', 'mandate_date', array(
				'label' 		=> 	'Machtigingsdatum',
				'validators' 	=> 	array(),
				'placeholder' => '01-11-2009',
				'attribs' => array(
					'class' => 'DatePicker',
					'title' => 'Datum van ondertekening van de machtiging. Bij bestaande machtigingen is deze datum altijd 1 november 2009, de standaard datum voor SEPA incasso.',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': 2, 'y': -3}"
				)
			));	

							
			$invoice->addElement('select', 'sepa_status', array(
				'label' => 'SEPA incasso status',
				'multiOptions' => array(
					'FRST' => 'Eerste incasso',
					'RCUR' => 'Wederkerende incasso',
					'FNAL' => 'Laatste incasso',
					'OOFF' => 'Eenmalige incasso'
				),
				'attribs' => array(
					'title' => 'Mogelijke sequentie opties ten behoeve van het genereren van een SEPA-incasso bestand.<br /><br /> Standaard staat deze waarde op "Eerste incasso" en bij het afronden van de eerste incasso wordt deze automatisch op "Wederkerende incasso" gezet.',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': -0, 'y': 0}"
				)
			));
	
			//Payment type
			$invoice->addElement(
				new Zend_Form_Element_Select('bill',
					array(
						'label' => 'Factuur ontvangen per',
						'multioptions' => array(
							'-1' =>	'Selecteer een methode',
							'email' => 'E-mail',
							'mail' => 'Post',
							'none' => 'Geen factuur versturen'
						)
					)
				)
			);	

			// does this user have to pay taxes
			$invoice->addElement(
				new Zend_Form_Element_Select('tax',
					array(
						'label' => 'BTW-plichtig',
						'multioptions' => Settings::get('general_company_shortname') == 'woonbemiddeling' ? array(1 => 'Ja', 0 => 'Nee') : array(0 => 'Nee', 1 => 'Ja')
					)
				)
			);				
				
		//birthdate
		$this->addElement('text', 'bdate', array(
			'label' 		=> 	'Geboortedatum',
			'validators' 	=> 	array(),
		));	
		
		//address
		$this->addElement('text', 'street', array(
			'label' 		=> 	'Straat',
			'validators' 	=> 	array(),
		));	

		//number
		$this->addElement('text', 'addressnr', array(
			'label' 		=> 	'Huisnummer',
			'validators' 	=> 	array(),
		));	
				
		//zipcode
		$this->addElement('text', 'zip', array(
			'label' 		=> 	'Postcode',
			'validators' 	=> 	array(),
		));	
		
		//city
		$this->addElement('text', 'city', array(
			'label' 		=> 	'Plaats',
			'validators' 	=> 	array(),
		));	

		//country
		$this->addElement('select', 'country', array(
			'label' 		=> 	'Land',
			'multiOptions' => Address::getCountriesForFormMultiselect(),
			'validators' 	=> 	array(),
		));	
		
		// telephone 1
		$this->addElement('text', 'phone1', array(
			'label' 		=> 	'Telefoonnummer [thuis]',
			'validators' 	=> 	array(),
		));		
		
		// telephone 2
		$this->addElement('text', 'phone2', array(
			'label' 		=> 	'Telefoonnummer [mobiel]',
			'validators' 	=> 	array(),
		));	
		
		
		// telephone emergency
		$this->addElement('text', 'phone_emergency', array(
			'label' 		=> 	'Telefoonnummer [nood]',
			'validators' 	=> 	array(),
		));	
		
		// email
		$this->addElement('text', 'email', array(
			'label' 		=> 	'E-mail',
			'validators' 	=> 	array('emailAddress'),
		));		
		
		// bankaccount
		$this->addElement('text', 'bankaccount', array(
			'label' 		=> 	'Bankrekening',
			'validators' 	=> 	array(),
		));	
		
		// bankaccountname
		$this->addElement('text', 'bankaccountname', array(
			'label' 		=> 	'Tenaamstelling rek. nr.',
			'validators' 	=> 	array(),
		));

		// IBAN
		$this->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
			'validators' 	=>  array('iban'),
		));	

		// BIC
		$this->addElement('text', 'bic', array(
			'label' 		=> 	'BIC',
			'validators' 	=> 	array(),
		));	

		//SMS benaderen
		$this->addElement('checkbox', 'sms', array(
			'label' => 'Informeren per sms?',
			'validators' => array(),
		));
		
		// BSN
		$this->addElement('text', 'BSN', array(
			'label' 		=> 	'Burgerservicenummer',
			'validators' 	=> 	array(),
		));	

		// identication_type
		$this->addElement('select', 'identication_type', [
			'label' 		=> 	'Identificatie soort',
			'multiOptions'	=> Users::$identificationTypeLabels,
		]);

		// identication_valid_till
		$this->addElement('text', 'identication_valid_till', [
			'label' 		=> 'Identificatie geldig tot',
			'attribs' 		=> [
				'class' => 'DatePicker',
			],
			'validators' 	=> [
				[
					'validator'	=> 'Date',
					'options'	=> [
						'locale' => 'nl',
					]
				]
			]
		]);
			

		// corporation
		$this->addElement('text', 'corporation', array(
			'label' 		=> 	'Bedrijf',
			'validators' 	=> 	array(),
		));	
		
		// banktype
		$this->addElement('select', 'banktype', array(
			'label' 		=> 	'Bank/giro',
			'multioptions' 	=> array(
									'normal' => 'Bank', 
									'giro' => 'Giro'
								),
			'validators' 	=> 	array(),
		));			

		// comment
		$this->addElement('textarea', 'comment', array(
			'label' 		=> 	false,
			'validators' 	=> 	array(),
		));	

		// description
		$this->addElement('text', 'description', array(
			'label' 		=> 	'Omschrijving',
			'validators' 	=> 	array(),
		));	
				
		//submit
		$this->addElement('submit', 'submit', array(
			'label' => 'Toevoegen'
		));		
	?>
