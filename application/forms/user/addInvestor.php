<?
	//general form options
	$this->setAction('user/save/type/investor/')
			->setMethod('post')
			->setAttrib('id', 'useradd')
			->setAttrib('class', 'useradd form');
		
		//initials
		$this->addElement('hidden', 'id', array());

		//initials
		$this->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' 	=> 	array(),
		));	
		
		//name
		$this->addElement('text', 'firstname', array(
			'label' 		=> 	'Voornaam',
			'validators' 	=> 	array(),
		));		

		//middlename
		$this->addElement('text', 'middlename', array(
			'label' 		=> 	'Tussenvoegsel',
			'validators' 	=> 	array(),
		));	
			
		//name
		$this->addElement('text', 'investor', array(
			'label' 		=> 	'Belegger',
			'validators' 	=> 	array(),
		));	
			
		//name
		$this->addElement('text', 'name', array(
			'label' 		=> 	'Naam',
			'validators' 	=> 	array(),
		));

		$this->addElement('text', 'abbreviation', [
			'label' 		=> 	'Afkorting (4 letters)',
			'validators' 	=> 	[
				['validator' => 'Alpha'],
				['validator' => 'StringLength', 'options' => [4,4]],
				['validator' => 'InvestorAbbreviation']
			],
			'placeholder'	=> 'Autom. aangemaakt na opslaan'
		]);

		//gender
		$this->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man', 
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		$this->addElement('text', 'bdate', [
			'label' => 'Geboortedatum',
			'attribs' => [
				'class' => 'DatePicker',
				'datepicker_options' => json_encode([
					'yearRange' => date('Y') - 1900,
					'yearStart' =>  1900
                ])
            ],
			'validators' => [
				[
					'validator'	=> 'Date',
					'options'	=> [
						'locale' => 'nl',
                    ]
                ]
            ]
        ]);

		$this->addElement('text', 'birthplace', [
			'label' => 'Geboorteplaats',
		] );

		//gender
		$this->addElement('select', 'grouped', array(
			'label' 		=> 	'Verzamelfactuur',
			'multioptions' 	=> array(
									true => 'Ja', 
									false => 'Nee'
								),
			'validators' 	=> 	array(),
		));

		$this->addElement('text', 'username', array(
			'label' 		=> 	'Gebruikersnaam',
			'validators' 	=> 	array('uniqueValue'),
			'attribs' 		=> array('autocomplete' => 'off')
		));



		$this->addElement( 'text', 'password', [
			'label'   => 'Wachtwoord',
			'attribs' => [
				'autocomplete' => 'off',
				'title'        => 'Dit veld is alleen voor invoer/vervanging van het wachtwoord, het huidige wachtwoord wordt nooit weergegeven.',
				'class'        => 'forceAutoHint',
			]
		] );

		$usersModel = new Users();
		$intUsersCommercial = $usersModel->getInternalUsers('false', true);

		foreach ($intUsersCommercial as $user)
			if(empty($user['enddate']))
				$intUsersCommercial[$user['id']] = User::buildname($user);

		$intUsersCommercial = [0 => 'Geen'] + $intUsersCommercial;

		$this->addElement( 'select', 'investor_manager', [
			'label'   => 'portefeuillemanager',
            'multiOptions'	=> $intUsersCommercial,
            'required' 		=>	true,
            'value' =>  null,
            'attribs'		=> ['title' => 'De portefeuillemanager wordt op de portal getoond met contactinformatie voor de belegger .', 'class' => 'forceAutoHint']

        ] );

		$this->addElement( 'select', 'investor_manager_account', [
			'label'   => 'Accountmanager',
			'multiOptions'	=> $intUsersCommercial,
			'required' 		=>	true,
			'value' =>  null,
			'attribs'		=> ['title' => 'De Accountmanager wordt op de portal getoond met contactinformatie voor de belegger .', 'class' => 'forceAutoHint']

		] );


		$this->addElement('select', 'vacancy_report', array(
			'label' 		=> 	'Leegstand rapportage geactiveerd',
			'multioptions' 	=> array(

				false => 'Nee',
				true => 'Ja',
			),
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Tonen van leegstand en kosten van leegstand. Na aanpassen van deze instellingen dient de belegger opnieuw in te loggen.', 'class' => 'forceAutoHint')

		));

		$this->addElement('select', 'transaction_report', array(
			'label' 		=> 	'Openstaande posten rapportage geactiveerd',
			'multioptions' 	=> array(

				false => 'Nee',
				true => 'Ja',
			),
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Tonen van achterstand per debiteur. Na aanpassen van deze instellingen dient de belegger opnieuw in te loggen.', 'class' => 'forceAutoHint')

		));



		$this->addElement('select', 'default_kpi', array(
			'label' 		=> 	'Standaard KPI rapportage geactiveerd',
			'multioptions' 	=> array(

				false => 'Nee',
                true => 'Ja',
			),
			'validators' 	=> 	array(),
            'attribs'		=> array('title' => 'Huuromzet, kosten en direct rendement. Na aanpassen van deze instellingen dient de belegger opnieuw in te loggen.', 'class' => 'forceAutoHint')

        ));

		$this->addElement('select', 'extended_kpi', array(
			'label' 		=> 	'Uitgebreide KPI rapportage geactiveerd',
			'multioptions' 	=> array(
				false => 'Nee',
                true => 'Ja',
			),
			'validators' 	=> 	array(),
            'attribs'		=> array('title' => 'Default + indirect rendement, free cash flow, obligo en beleggingswaarde. Na aanpassen van deze instellingen dient de belegger opnieuw in te loggen.', 'class' => 'forceAutoHint')
		));

		$this->addElement( 'text', 'broker_publish_allowed_ips', [
			'label'   => 'IP adressen met toegang rapportage',
			'attribs' => [
				'autocomplete' => 'off',
				'title'        => 'Voer hier de IP adressen in die toegang moeten hebben tot de belegger rapportage van deze belegger. Scheid meerdere IP adressen d.m.v. spaties, bijv: "******* *******".
					Ga, om je huidige IP adres te bepalen, naar "https://'.$_SERVER['HTTP_HOST'].'/broker/report-ip-address".',
				'class'        => 'forceAutoHint',
			]
		] );
		
		//address
		$this->addElement('text', 'street', array(
			'label' 		=> 	'Straat',
			'validators' 	=> 	array(),
		));	

		//number
		$this->addElement('text', 'addressnr', array(
			'label' 		=> 	'Huisnummer',
			'validators' 	=> 	array(),
		));	
				
		//zipcode
		$this->addElement('text', 'zip', array(
			'label' 		=> 	'Postcode',
			'validators' 	=> 	array(),
		));	
		
		//city
		$this->addElement('text', 'city', array(
			'label' 		=> 	'Plaats',
			'validators' 	=> 	array(),
		));	
		
		// telephone 1
		$this->addElement('text', 'phone1', array(
			'label' 		=> 	'Telefoon nummer',
            'validators' => array( 'PhoneNumber' ),
		));		
		
		// telephone 2
		$this->addElement('text', 'phone2', array(
			'label' 		=> 	'Alternatief telefoon nummer',
            'validators' => array( 'PhoneNumber' ),
		));	
		
		// email
		$this->addElement('text', 'email', array(
			'label' 		=> 	'Email',
            'validators' => ['emailAddress'],
		));

        // email copy
        $this->addElement('text', 'email_parent', [
            'label' => translate()->_('email_parent'),
            'validators' => ['emailAddress'],
            'attribs' => ['autocomplete' => 'off',
                'title' => 'Als u wilt dat alle systeemmails naar deze belegger met een kopie verstuurd worden vul dan hier een 2e mailadres in',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': 0, 'y': 0}"]
        ]);

// bankaccount
		$this->addElement('text', 'bankaccount', array(
			'label' 		=> 	'Bankrekening',
			'validators' 	=> 	array(),
		));	

		// bankaccount
		$this->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
			'validators' 	=> 	array('iban'),
		));	

		// bankaccount
		$this->addElement('text', 'bic', array(
			'label' 		=> 	'BIC',
			'validators' 	=> 	array(),
		));	
		
		$this->addElement('text', 'bankaccountname', array(
			'label' 		=> 	'Naam bankrekening',
			'validators' 	=> 	array(),
		));

		$this->addElement('select', 'identication_type', [
			'label' => 'Identificatie soort',
			'multiOptions'	=> Users::$identificationTypeLabels,
		]);

		// BSN
		$this->addElement('text', 'BSN', array(
			'label' => 'Identificatie kenmerk',
			'validators' => array(
				array(
					'validator'	=> 'stringLength',
					'options'	=> array(
						'max' =>	20,
					)
				)
			)
		));

		$this->addElement('text', 'identication_valid_till', [
			'label' => 'Identificatie geldig tot',
			'attribs' => [
				'class' => 'DatePicker',
			],
			'validators' => [
				[
					'validator'	=> 'Date',
					'options'	=> [
						'locale' => 'nl',
					]
				]
			]
		]);

		// corporation
		$this->addElement('text', 'corporation', array(
			'label' 		=> 	'Bedrijf',
			'validators' 	=> 	array(),
		));


		$languageOptions = [
			'nl' => 'Nederlands',
			'en' => 'Engels',
		];

		if(Settings::get('software_french_enabled'))
			$languageOptions['fr'] = 'Frans';

		$this->addElement('select', 'language', array(
			'label' => 'Taal',
			'multiOptions' => $languageOptions,
		));
		
		// banktype
		$this->addElement('select', 'banktype', array(
			'label' 		=> 	'Bank/giro',
			'multioptions' 	=> array(
									'normal' => 'Bank', 
									'giro' => 'Giro'
								),
			'validators' 	=> 	array(),
		));			

		// comment
		$this->addElement('textarea', 'comment', array(
			'label' 		=> 	false,
			'validators' 	=> 	array(),
		));	

		// description
		$this->addElement('text', 'description', array(
			'label' 		=> 	'Omschrijving',
			'validators' 	=> 	array(),
		));	

		$this->addElement('text', 'olddebtorcode', [
			'label' 		=> 	'Debiteur code financieel',
		]);

		
		// provision percentage
		$this->addElement('text', 'provision', array(
			'label' 		=> 	'Beheerprovisie (percentage)',
			'validators' 	=> 	array(),
            'value'         =>  0,
            'required' 		=>	true,
			'attribs'		=> array('title' => 'Percentage beheerprovisie voor alle panden. Vult u het percentage per VHE in, vul dan hier 0 in. Maakt u gebruik van een vast bedrag per maand vul dan hier 0 in.', 'class' => 'forceAutoHint')

		));


		$contractDetails = new Form_SubForm($this);
		$this->addSubForm($contractDetails, 'contract_details');

		$contractDetails->addElement( 'select', 'manage_financial', [
			'label'        => 'Financieel beheer',
			'multioptions' => [
				'1' => 'Ja',
				'0' => 'Nee'
			],
			'attribs'      => [
				'title' => 'Kies "Ja" om aan te geven dat u, volgens contract, voor deze belegger
						    de facturatie, aanmaningen, borg, etc. in beheer hebt.',
				'class' => 'forceAutoHint',
			],
		] );

		$contractDetails->addElement( 'select', 'manage_technical', [
			'label'        => 'Technisch beheer',
			'multioptions' => [
				'1' => 'Ja',
				'0' => 'Nee'
			],
			'attribs'      => [
				'title' => 'Kies "Ja" om aan te geven dat u, volgens contract, voor deze belegger
							onderhoud, storing afhandeling, klachten e.d. in beheer hebt.',
				'class' => 'forceAutoHint',
			],
		] );

		$contractDetails->addElement( 'text', 'manage_start_date', [
			'label'      => 'Ingangsdatum beheer',
			'validators' => [
				[
					'validator' => 'Date',
					'options'   => [
						'locale' => 'nl',
					]
				]
			],
			'attribs'    => [
				'title'      => "Ingangsdatum van uw beheersovereenkomst met deze belegger.<br /><br />Met deze instelling wordt tevens de ingangdatum van beleggersuitkeringen voor deze belegger beinvloed.<br /><br />Let op; bij het aanpassen van deze instelling kunnen facturen voor de uitkering verborgen worden en bij het terugzetten van de datum kunnen eerder verborgen facturen weer tevoorschijn komen. ",
				'class'      => 'forceAutoHint DatePicker',
				'hintOffset' => '{x:-25, y:0}'
			],
		] );

		$contractDetails->addElement( 'text', 'manage_end_date', [
			'label'      => 'Einddatum beheer',
			'validators' => [
				[
					'validator' => 'Date',
					'options'   => [
						'locale' => 'nl',
					]
				]
			],
			'attribs'    => [
				'title'      => "Einddatum van uw beheersovereenkomst met deze belegger.<br /><br />Met deze instelling wordt tevens de einddatum van beleggersuitkeringen voor deze belegger beinvloed, m.u.v. kosten.",
				'class'      => 'forceAutoHint DatePicker',
				'hintOffset' => '{x:-25, y:0}'
			]
		]);

		$contractDetails->addElement( 'checkbox', 'manage_end_date_final', [
			'label'      => 'Beheer financieel afgehandeld',
			'attribs'    => [
				'title'      => "Beheersovereenkomst met deze belegger is beeindigd en financieel afgehandeld. <br /><br />Na het inschakelen van deze instelling zullen er geen beleggersuitkeringen meer gegenereed worden voor deze belegger, schakel deze instelling enkel in als de belegger volledig afgerekend is.",
			]
		]);

		$provisiondetails = new Form_SubForm($this);
		$this->addSubForm($provisiondetails, 'provisiondetails');

		$provisiondetails->addElement('select', 'type', array(
			'label' 		=> 	'Type factuur',
			'multioptions' 	=> array(
				'passthrough' => 'Doorstort',
				'provision' => 'Beheerprovisie'
			)
		));

		$provisiondetails->addElement('select', 'subtract_costs', array(
			'label' 		=> 	'Kosten verrekenen',
			'multioptions' 	=> array(
				'1' => 'Ja',
				'0' => 'Nee'
			),
			'attribs'		=> array(
				'title' => 'Kies "Nee" wanneer de kosten reeds afgeschreven worden van de rekening van de belegger en deze dus niet verrekend hoeven worden bij een uitkering.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		$provisiondetails->addElement('select', 'provision_for_empty_periods', array(
			'label' 		=> 	'Leegstand provisie',
			'multioptions' 	=> array(
				'1' => 'Ja',
				'0' => 'Nee'
			),
			'attribs'		=> array(
				'title' => 'De vaste provisie, zoals ingesteld bij de belegger instellingen, berekenen voor objecten waarbij in de betreffende periode geen facturen naar een huurder verstuurd zijn.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		$provisiondetails->addElement('select', 'only_recent_unpayed', array(
			'label' 		=> 	'Doorstort vooruitbetalen',
			'multioptions' 	=> array(
				'1' => 'Ja',
				'0' => 'Nee'
			),
			'attribs'		=> array(
				'title' => 'Doorstorten ondanks betalingsachterstand, enkel wanneer er maar 1 periode open staat voor de huurder.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		// provision discount
		$provisiondetails->addElement('text', 'discount', array(
			'label' 		=> 	'Beheerprovisie (kortingspercentage)',
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Percentage beheerprovisie', 'class' => 'forceAutoHint')

		));

		// provision discount
		$provisiondetails->addElement('text', 'provision_tenant', array(
			'label' 		=> 	'Beheerprovisie voor huurder',
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Percentage beheerprovisie voor de huurder', 'class' => 'forceAutoHint'),
			'value'			=> 0
		));


		$this->addElement('select', 'reporttype', array(
			'label' 		=> 	'Type beleggerafrekening',
			'multioptions' 	=> array(
				'default' => 'Standaard'
			),

			'validators' 	=> 	array(),
			'attribs'		=> array(
				'title' => 'Hier komen verschillende rapportage formats. Vooralsnog is alleen standaard beschikbaar',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)

		));

		$this->addElement('select', 'finalize_send_method', array(
			'label' 		=> 	'Voorkeur verzendmethode',
			'multioptions' 	=> [
				'default' => 'E-mail (indien ingevoerd)',
				'post' => 'Post'
			],
			'attribs'		=> [
				'title' => 'De gewenste verzendmethode bij directe verzending van afrekeningen',
				'class' => 'forceAutoHint',
			]
		));

		$this->addElement('checkbox', 'administrationcharge', array(
		'label' => 'Administratie vergoeding',
		'attribs' => array(
			'title' => 'Met deze instelling wordt over geboekte inkoopkosten automatisch een administratievergoeding berekend.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

		$this->addElement('text', 'administrationpercentage', array(
			'label' 		=> 	'Percentage administratievergoeding',
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Percentage vergoeding dat over de geboekte kosten wordt belast', 'class' => 'forceAutoHint'),
			'value'			=> 0
		));


		

/*
		// provision payment moment
		$provisiondetails->addElement('select', 'payment_moment', array(
			'label' 		=> 	'Beheerprovisie uitkeren',
			'validators' 	=> 	array(),
			'multioptions' 	=> array(
									'0' => 'Op uitkeringsdag, restbetaling naar volgende maand',
									'1' => 'Direct op basis van betaling', 
									'2' => 'Op uitkeringsdag, restbetaling direct'
								)

		));
		*/

		$provisiondetails->addElement('select', 'interval', array(
			'label' 		=> 	'Periode',
			'multioptions' 	=> ['month' => 'Maandelijks', 'quarter' => 'Kwartaal']
		));

		$provisiondetails->addElement('select', 'period_type', array(
			'label' 		=> 	'Periode type',
			'multioptions' 	=> ['default' => 'Standaard systeem instelling', 'invoice' => 'Factuurperiode', 'payed' => 'Betaalperiode'],
			'attribs'		=> array(
				'title' => '<b>Standaard systeem instelling</b><br />De standaard waarde uit de software instellingen (ingesteld op: ' . (Settings::get('investor_provision_period_date') == 'invoice' ? 'factuurperiode' : 'betaalperiode') . ') wordt gebruikt voor deze belegger<br /><br /><b>Factuurperiode</b><br />Bij een uitkering op factuurperiode wordt de uitkering gemaakt over alle facturen geboekt in de betreffende periode.<br /><br /><b>Betaalperiode</b><br />Bij een uitkering op betaalperiode wordt de uitkering gemaakt over alle betalingen die in de periode ontvangen zijn.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		$provisiondetails->addElement('select', 'auto_period_shift', array(
			'label' 		=> 	'Automatisch doorschuiven',
			'multioptions' 	=> ['1' => 'Ingeschakeld' , '0' => 'Uitgeschakeld' ],
			'attribs'		=> array(
				'title' => 'Schuif inkomsten na het verzenden van een beleggersuitkering automatisch door naar de volgende periodieke uitkering.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		$provisiondetails->addElement('select', 'invoice_period_late_payment', array(
			'label' 		=> 	'Late betaling verbergen',
			'multioptions' 	=> ['1' => 'Ingeschakeld' , '0' => 'Uitgeschakeld' ],
			'attribs'		=> array(
				'title' => 'Betaling na uitkering periode verbergen en doorschuiven naar betaalperiode',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		// provision payment day
		$default_payment_day = Settings::get('investor_provision_send_day') > 0 ? Settings::get('investor_provision_send_day') : false;
		$default_payment_day_text = $default_payment_day ? 'De standaard uitkeringsdag is ingesteld op de '.$default_payment_day.'e van de maand. Met deze invoer kan een afwijkende uitkeringsdag ingesteld worden voor deze belegger.' : 'Kies de gewenste uitkeringsdag voor deze belegger';
		$provisiondetails->addElement('number', 'payment_day', array(
			'label' 		=> $default_payment_day ? 'Afwijkende uitkeringsdag' : 'Voorkeur uitkeringsdag',
			'max'			=> 28,
			'attribs'		=> ['title' => $default_payment_day_text, 'class' => 'forceAutoHint'],
			'validators' => [
				['validator' => 'Float'],
				[
					'validator' => 'Between',
					'options' => [
						'min' => 0,
						'max' => 28,
					]
				]
			]
		));


		$provisiondetails->addElement('select', 'payment_og_grouping', array(
			'label' 		=> 	'Inkomsten groeperen per objectgroep',
			'multioptions' 	=> array(
				'0' => 'Niet groeperen',
				'1' => 'Groeperen'
			),
			'validators' 	=> 	array(),
			'attribs'		=> array(
				'title' => 'Bij de specificatie van de kosten groeperen per objectgroep',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)
		));

		$provisiondetails->addElement('select', 'complex_overview', array(
			'label' 		=> 	'Recapitulatie per complex',
			'multioptions' 	=> [
				'0' => 'Uitgeschakeld',
				'1' => 'Ingeschakeld'
            ],
			'attribs'		=> [
				'title' => 'Bij de specificatie een totaaloverzicht weergeven van de opbrengsten en uitgaven per complex',
				'class' => 'forceAutoHint',
            ]
		));

		$provisiondetails->addElement('select', 'group_costs_by_objectgroup', array(
			'label' 		=> 	'Kosten groeperen',
			'multioptions' 	=> array(
				'0' => 'Groeperen per objectgroep bij kosten voor meerdere objecten',
				'1' => 'Altijd per objectgroep',
				'2' => 'Niet groeperen, altijd per object'
			),
			'validators' 	=> 	array(),
			'attribs'		=> array(
				'title' => 'Bij de specificatie van de kosten groeperen per objectgroep',
				'class' => 'forceAutoHint',
				'style' => 'width: 150px;',
				'hintOffset' => '{x:5, y:-2}'
			)

		));


		$provisiondetails->addElement('select', 'group_service_payments', array(
			'label' 		=> 	'Servicekosten groeperen specificatie',
			'multioptions' 	=> array(
				'0' => 'Niet groeperen',
				'1' => 'Groeperen'
			),

			'validators' 	=> 	array(),
			'attribs'		=> array(
				'title' => 'Bij de specificatie van de inkomsten de servicekosten componenten groeperen',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			)

		));

		$provisiondetails->addElement('select', 'show_ledger_on_specification', array(
			'label' 		=> 	'Grootboek tonen op afrekening',
			'multioptions' 	=> [
				'0' => 'Niet tonen',
				'1' => 'Tonen'
			],
			'validators' 	=> 	[],
			'attribs'		=> [
				'title' => 'Bij de afrekening specificatie de grootboek van de componenten tonen',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			]
		));

		$provisiondetails->addElement('select', 'payment_overview_type', [
			'label' 		=> 	'Specificatie huurinkomsten',
			'multioptions' 	=> [
				'normal' => 'Gespecificeerd per object/huurder',
				'summary' => 'Samenvatting',
				'both' => 'Beide'
			],
			'validators' 	=> 	[],
			'attribs'		=> [
				'title' => 'Het type specificatie huurinkomsten dat op de afrekening weergegeven wordt',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
			]
		]);


		$provisiondetails->addElement('select', 'vat', [
		    'label' => 'Provisie (percentage) berekenen op basis van:',
		    'multiOptions'=> [
		        true => 'Huur inclusief BTW',
		        false => 'Huur exclusief BTW',
            ],
        ]);

		// provision amount
		$this->addElement('text', 'provisionamount', [
			'label' 		=> 	'Beheerprovisie (vast bedrag per maand)',
			'validators' 	=> 	[],

			'attribs'		=> [
				'title' => 'Vast totaalbedrag aan provisie per maand voor alle objecten van de belegger, invoeren excl. BTW Voert u een bedrag per VHE in, vul hier dan 0 hier.',
				'class' => 'forceAutoHint',
				'hintOffset' => '{x:5, y:-2}'
            ]
        ]);



		// provision investor ledger
		$this->addElement('text', 'investorprofit_code_override', [
			'label'   => 'Grootboek omzet beheerprovisie',
			'attribs' => [
				'placeholder' => Settings::get('financial_export_investorprofit_code')
			],
			'attribs'		=> [
				'title' => 'Het grootboek voor de provisie voor deze belegger aanpassen, de standaardinstelling voor grootboek is: ' . (Settings::get('financial_export_investorprofit_code') ?: 8300),
				'class' => 'forceAutoHint'
			]
		]);

		// provision investor cost center
		$this->addElement('text', 'cost_center', [
			'label' 		=> 	'Kostenplaats',
			'validators' 	=> 	[],
			'attribs'		=> [
				'title' => 'De kostenplaats voor deze belegger aanpassen. Indien leeg zal geen kostenplaats meegegeven worden',
				'class' => 'forceAutoHint'
			]
		]);

		$this->addElement('text', 'ledger', [
			'label' 		=> 	'Grootboek huurpenningen',
			'validators' 	=> 	[],
			'attribs'		=> [
				'title' => 'Indien huurstromen ook naar het financiele pakket worden uitgewisseld word een tussenrekening gebruikt.
						De standaardinstelling voor beheerprovisie is: ' . (Settings::get('financial_export_rental_code') ?: 2020) .'
						en kan worden ingesteld in de algemene sofware instellingen. Wilt u een aparte tussenrekening per belegger dan kunt u
						hier de grootboek rekening per belegger instellen',
				'class' => 'forceAutoHint'
			]
		]);


				
		//submit
		$this->addElement('submit', 'submit', array(
			'label' => 'Toevoegen'
		));		
	?>
