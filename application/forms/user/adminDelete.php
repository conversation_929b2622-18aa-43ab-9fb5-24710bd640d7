<script type="text/javascript">
	window.addEvent('domready', function () {
		// add a datepicker to all date input fields
		$$('input.DatePicker').each(function (input) {
			new DatePicker(input, {
				yearStart: (new Date().getFullYear())-1,
				yearRange: 5,
			});
		});
	});
</script>

<?

$userId = $this->getOwner()->userIdForForm;

//general form options
$this->setAction("user/admin-delete/id/$userId/")
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$adminDeleteForm = new Form_SubForm($this);
$adminDeleteForm->setAttrib('title', 'Gebruiker inactief maken');
$this->addSubForm($adminDeleteForm, 'admin_delete_form');

	// eind datum
	$adminDeleteForm->addElement('text', 'enddate', [
		'label' => 'Niet meer inloggen per',
		'required' => true,
		'attribs' => [
			'class' => 'DatePicker'
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$adminDeleteForm->addElement('select', 'inheritor', [
		'label' => 'Overdragen aan',
		'required' => true,
		'registerInArrayValidator' => false,
	]);


	$adminDeleteForm->addElement('hidden', 'id', [
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Inactief maken'
]);

?>
