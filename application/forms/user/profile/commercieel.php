<?

	//name form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

	/**
	 * login data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//username
		$user->addElement('text', 'username', array(
			'label' => 'Gebruikersnaam',
			'validators' => array(),
			'disabled' => true, #TODO dit in rechten?
			'ignore' => true
		));

		//password
		$user->addElement('password', 'password', array(
			'label' => 'Wachtwoord',
			'renderPassword' => true,
			'attribs' => [
				'title' => 'U hoeft dit veld alleen in te vullen als u uw wachtwoord wilt bijwerken.',
				'class' => 'forceAutoHint',
			],
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 100)
				)
			)
		));

	/**
	 * name data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//gender
		$general->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
			'required' 		=>	true
		));

		// initials
		$general->addElement('text', 'initials', array(
			'label' 		=> 	'Initialen',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			),
			'required' 		=>	true
		));


			//firstname
		$general->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			)
		));

		//middlename
		$general->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			),
		));

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' 		=>	true
		));

		//email
		$general->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress'),
			'required' 		=>	true,
			'attribs' 		=> ['autocomplete' => 'off']
		));
	
		//Phone home
		$general->addElement('text', 'phone1', array(
			'label' => 'Telefoonnummer',
			'validators' => $language != 'nl' ? array() : array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 13)
				)
			),
			'attribs' 		=> ['autocomplete' => 'off']
		));
			
		//Phone home
		$general->addElement('text', 'phone2', array(
			'label' => 'Alternatief telefoonnummer',
			'validators' => $language != 'nl' ? array() : array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 13)
				)
			),
		));	
		
		// corporation
		$general->addElement('text', 'company', array(
			'label' 		=> 	'Bedrijfsnaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			),
			'required' 		=>	true
		));
		
		$general->addElement('text', 'chain', array(
			'label' => 'Keten',
			'validators' => array(),
		));

		// kvk
		$general->addElement('text', 'kvk', array(
			'label' 		=> 	'KvK-nummer',
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 8)
				)
			)
		));

		// kvk
		$general->addElement('text', 'tax', array(
			'label' 		=> 	'BTW-nummer (zonder NL)',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 16)
				)
			)
		));

		//City
		$general->addElement('text', 'statutory', array(
			'label' => 'Statutair gevestigd te',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			),
			'required' 		=>	true

		));

	/**
	 * object start data
	 */
	$start = new Form_SubForm($this);
	$this->addSubForm($start, 'start');	
		
		//Date
		$start->addElement('text', 'date', array(
			'label' => 'Opleverdatum',
			//'validators' => array('delivery'),
			'required' => true
		));	
		
		//Meter
		$start->addElement('text', 'meter', array(
			'label' => 'Meterstand(en) opleverdatum',
			'validators' => array()
		));
		
	/**
	 * invoiceing data
	 */
	$invoiceing = new Form_SubForm($this);
	$this->addSubForm($invoiceing, 'invoiceing');

		//userselect
		$invoiceing->addElement('select', 'userselect', array(
			'label' 		=> 	'Ter attentie van',
			'multioptions' 	=> array(
									'contractant' => 'Contractant',
									'custom' => 'Aangepast'
								),
			'validators' 	=> 	array(),
		));

		//gender
		$invoiceing->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$invoiceing->addElement('text', 'initials', array(
			'label' 		=> 	'Initialen',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			),
		));


			//firstname
		$invoiceing->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
		));

		//middlename
		$invoiceing->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			),
		));

		//name
		$invoiceing->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
		));

		// tav
		$invoiceing->addElement('text', 'tav', array(
			'label' 		=> 	'Ter attentie van',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			)
		));

		// tav
		$invoiceing->addElement('text', 'department', array(
			'label' 		=> 	'Afdeling',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			)
		));

		// mention
		$invoiceing->addElement('text', 'mention', array(
			'label' 		=> 	'Onder vermelding van',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			)
		));


		//address
		$invoiceing->addElement('text', 'address', array(
			'label' => 'Straatnaam',
			'validators' => array(),
			'required' 		=>	true
		));

		//number
		$invoiceing->addElement('text', 'number', array(
			'label' => 'Huisnummer',
			'validators' => array(),
			'required' 		=>	true
		));

		$zipcode_validate = $language == 'nl' ? array(array('regex', false, 'options' => array('pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/'))) : array();

		//zipcode
		$invoiceing->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => $zipcode_validate,
			'required' 		=>	true
		));

		//City
		$invoiceing->addElement('text', 'city', array(
			'label' => 'Vestigingsplaats',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			),
			'required' 		=>	true

		));


	/**
	 * emergency contact data
	 */
	$emergency = new Form_SubForm($this);
	$this->addSubForm($emergency, 'emergency');


		//emergency
		$emergency->addElement('select', 'userselect', array(
			'label' 		=> 	'Contractpersoon',
			'multioptions' 	=> array(
									'contractant' => 'Contractant',
									'custom' => 'Aangepast'
								),
			'validators' 	=> 	array(),
		));

		//gender
		$emergency->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$emergency->addElement('text', 'initials', array(
			'label' 		=> 	'Initialen',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			),
		));


			//firstname
		$emergency->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
		));

		//middlename
		$emergency->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			),
		));

		//name
		$emergency->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
		));

		//Phone home
		$emergency->addElement('text', 'phone', array(
			'label' => 'Telefoon',
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 13)
				)
			),
		));



		//email
		$emergency->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress')
		));


	
	/**
	 * invoice data
	 */
	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');


		//Invoice interval
		$invoice->addElement(
			new Zend_Form_Element_Select('rate',
				array(
					'label' => 'Facturering per',
					'multioptions' => UserInvoicePreferences::getInvoicePeriodOptions(),
					'disabled' => true,
					'ignore' => true
				)
			)
		);

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('type',
				array(
					'label' => 'Type betaling',
					'multioptions' => array(
						'collection' => 'Automatische incasso',
						'ideal' => 'Handmatig overmaken'
					),
					'required' 		=>	true
				)
			)
		);

		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('bill',
				array(
					'label' => 'Factuur ontvangen per',
					'multioptions' => array(
						'email' => 'E-mail',
						'mail' => 'Post'
					)
				)
			)
		);

	


		/*
		$project_validator = Settings::get('software_type') == "real estate" ? array() : array(
			'validator' => 'Iban'
		);
		*/

		
		
		// IBAN
		$invoice->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
			'validators' 	=>  array('iban'),
			'required' => true
				
		));

		// banktype
		$invoice->addElement('select', 'addr', array(
			'label' 		=> 	'Ik wil communicatie ontvangen',
			'multioptions' 	=> array(
									'object' => 'Op bovenstaand adres',
									'user' => 'Op een ander adres'
								),
			'validators' 	=> 	array(),
		));



	//submit
	$this->addElement('submit', 'profile', array(
		'label' => 'Gegevens opslaan'
	));

?>
