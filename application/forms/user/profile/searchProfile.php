<?php
$user = new User();

//name form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form');




$searchprofile = new Form_SubForm($this);
$this->addSubForm($searchprofile, 'searchprofile');

$searchprofile->addElement('text', 'rent_from', [
	'label' => translate()->_('rent_from_period'),
	'validators' => [],
	'class' => 'DatePicker bdate'
]);

$searchprofile->addElement('range', 'rent_price', [
	'label' => 'Budget',
	'validators' =>  ['Digits'],
	'value' => '300,2000',
	'attribs' => [
		'multiple' => true,
		'min' => 0,
		'max' => 10000,
		'step' => 1
	]
]);


$searchprofile->addElement('text', 'object_min_size', [
    'label' => translate()->_('size_from'),
    'validators' => ['Digits'],

]);

$spec_group_subform = $searchprofile;

$spec_model = new ObjectsSpecificationsValues();
$spec_model->setFilterOgType('private');
$specifications = $spec_model->getSearchSpecificationsForUser(loginManager::data()->id);

$specificationSelectOptionsQuery = db()->select()->from(
    'objects_specifications_values_options',
    ['type_id', 'id', 'value']
);
$specificationSelectOptions = db()->fetchAll($specificationSelectOptionsQuery);

$allowedSpecificationFormFields = [
    'number_of_bedrooms',
    'price_including_servicecosts',
    'build_type',
];


foreach($specifications as $spec_group_id => $spec_group){
    foreach($spec_group['types'] as $type_id => $type) {
        if(!in_array($type['type_name'], $allowedSpecificationFormFields)){
            continue;
        }

        require 'forms/user/includes/object_specification_values_options.php';
    }
}

$searchregion = new Form_SubForm($this);
$this->addSubForm($searchregion, 'searchregion');

$pc = new ProjectsCities();

$searchregion->addElement('select', 'allowed_cities', [
    'multiple' => 'multiple',
    'label' => translate()->_('cities'),
    'registerInArrayValidator' => false,
    'validators' => [],
    'required' => true,
    'attribs' => ['autocomplete' => 'off'],
    'multiOptions' => $pc->getListForSelect(),
]);



//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => translate()->_('save')
));
