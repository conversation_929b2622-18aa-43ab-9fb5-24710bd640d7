<? #TODO waarom is alles disabled? maak er dan een <span> van ofzo...

	$user = new User();
	$language = $user->getLanguage(loginManager::data()->id, loginManager::data()->info['object']);

	$project_model = new Projects();
	$project_row = $project_model->getById($this->getOwner()->userprofile['other']['project']);


	//name form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

	/**
	 * login data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//username
		$user->addElement('text', 'username', array(
			'label' => translate()->_('username'),
			'validators' => array(),
			'disabled' => true, #TODO dit in rechten?
			'ignore' => true
		));

		//password
		$user->addElement('password', 'password', array(
			'label' => translate()->_('password'),
			'renderPassword' => false,
			'validators' => [
                'passwordPattern',
            ]
		));

	$tenant_subforms = [
		[
			'tenant_subform' => 'general',
			'address_subform' => 'user_address'
		]
	];

	$u_model = new Users();

	foreach($u_model->getPartnerUserFields() as $partnerUserField){
		$subforms = $u_model->getPartnerSubforms($partnerUserField);

        $tenant_subforms[] = [
            'tenant_subform' => $subforms['tenant'],
            'address_subform' => $subforms['address']
		];
	}

	// bind the subform name array to the view
	$this->getOwner()->view->tenant_subforms = $tenant_subforms;

	foreach ($tenant_subforms as $tenant) {
		include 'subforms/tenant.php';
        include 'subforms/tenant_address.php';
    }

	/**
	 * object address data
	 */
	$object_address = new Form_SubForm($this);
	$this->addSubForm($object_address, 'object_address');

		//address_id
		$object_address->addElement('hidden', 'id', array(
			'validators' => array(
				array()
			)
		));

		//address
		$object_address->addElement('text', 'address', array(
			'label' => translate()->_('address'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//number
		$object_address->addElement('text', 'number', array(
			'label' => translate()->_('number'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//zipcode
		$object_address->addElement('text', 'zipcode', array(
			'label' => translate()->_('postal'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//City
		$object_address->addElement('text', 'city', array(
			'label' => translate()->_('city'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));
		
		


	/**
	 * object start data
	 */
	$start = new Form_SubForm($this);
	$this->addSubForm($start, 'start');	
		
		//Date
		$start->addElement('text', 'date', array(
			'label' => translate()->_('startdate'),
			'validators' => array('delivery')
		));	
		
		//Date
		$start->addElement('text', 'enddate', array(
			'label' => translate()->_('enddate'),
		));	
		
		//Meter
		$start->addElement('text', 'meter', array(
			'label' => 'Meterstand(en)',
			'validators' => array()
		));

	/**
	 * invoice data
	 */
	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');

		//Invoice interval
		$invoice->addElement(
			new Zend_Form_Element_Select('rate',
				array(
					'label' => 'Facturering per',
					'multioptions' => UserInvoicePreferences::getInvoicePeriodOptions(),
					'disabled' => true,
					'ignore' => true
				)
			)
		);

		//Payment type

            if ($project_row->tenant_page_collection_payment == '1' && $project_row->tenant_page_manual_payment == '0') {
                $payment_type = array(
                    'collection' => translate()->_('collection')
                );
            } elseif ($project_row->tenant_page_collection_payment == '0' && $project_row->tenant_page_manual_payment == '1') {
                $payment_type = array(
                    'ideal' => translate()->_('ideal')
                );
            } elseif  (Settings::get('modules_tenantLogin_hide_collection_type')) {

                $payment_type = array(
                    'ideal' => translate()->_('ideal')
                );

			}
			else {
				$payment_type = array(
					'collection' => translate()->_('collection'),
					'ideal' => translate()->_('ideal')
				);
			}

			$invoice->addElement(
				new Zend_Form_Element_Select('type',
					array(
						'label' => translate()->_('payment_type'),
						'multioptions' => $payment_type
					)
				)
			);

if (Settings::get('tenantportal_lock_changeprofile')) {
    $invoice->type->disabled = true;
}
			

		if($project_row['tenant_page_communication_options'] == 'all' || !isset($project_row['tenant_page_communication_options'])){
            $invoice_bill_options = array(
                'email' => 'E-mail',
                'mail' => 'Post'
            );
		}
		elseif($project_row['tenant_page_communication_options'] == 'email'){
            $invoice_bill_options = array(
                'email' => 'E-mail'
            );
		}
		elseif($project_row['tenant_page_communication_options'] == 'post'){
            $invoice_bill_options = array(
                'mail' => 'Post'
            );
		}
        elseif  (Settings::get('modules_tenantLogin_payment_type_only_mail')) {

            $invoice_bill_options = array(
                'email' => 'E-mail'
            );
        } else {
            $invoice_bill_options = array(
                'email' => 'E-mail',
                'mail' => 'Post'
            );
        }







		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('bill',
				array(
					'label' => translate()->_('invoice_methode'),
					'multioptions' => $invoice_bill_options,
				)
			)
		);

if (Settings::get('tenantportal_lock_changeprofile')) {
    $invoice->bill->disabled = true;
}

		
		if ($language == "nl" ) {
			// bankaccount
			$invoice->addElement('text', 'bankaccount', array(
				'label' 		=> 	translate()->_('bankaccount'),
				'validators' 	=> 	array(array('validator' => 'elfProef'))
					
			));
		} else {
			// bankaccount
			$invoice->addElement('text', 'bankaccount', array(
				'label' 		=> 	translate()->_('bankaccount')
					
			));
		}
		
		
		 
		
		
		// BIC
		$invoice->addElement('text', 'bic', array(
			'label' 		=> 	'BIC',
			'validators' 	=> 	array(
					array(
						/*'validator' => 'elfProef'*/
					)
				)
				
		));

if (Settings::get('tenantportal_lock_changeprofile')) {
    $invoice->bic->disabled = true;
}

		/*
		$project_validator = Settings::get('software_type') == "real estate" ? array() : array(
			'validator' => 'Iban'
		);

		$ibantrue = Settings::get('software_type') == "real estate" ? false : true ;
		
		*/

	if ($language != "en") {

		// IBAN
		$invoice->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
			'validators' 	=>  array('iban'),
			'required' => false,
		));

	} else {

		// IBAN
		$invoice->addElement('text', 'iban', array(
			'label' 		=> 	'IBAN',
		));

	}

if (Settings::get('tenantportal_lock_changeprofile')) {
    $invoice->iban->disabled = true;
}

		

	/**
	 * tenant info
	 */
	$tenantinfo = new Form_SubForm($this);
	$this->addSubForm($tenantinfo, 'tenantinfo');


		// The marital status of the tenant
		$tenantinfo->addElement(
			$status = new Zend_Form_Element_Select('maritalstatus', array(
				'label' => 'Burgelijke staat',
				'multioptions' => array(
					'-' => '-',
					'married' => 'Gehuwd',
					'together' => 'Samenwonend',
					'single' => 'Alleenstaand'
				)
			))
		);

		// The number of adults
		$tenantinfo->addElement('text', 'adults', array(
			'label' => translate()->_('adults'),
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(1, 10)
				)
			)
		));

		// The number of kids
		$tenantinfo->addElement('text', 'kids', array(
			'label' => 'Aantal kinderen',
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(1, 10)
				)
			)
		));



	//submit
	$this->addElement('submit', 'profile', array(
		'label' => translate()->_('save'),
	));

?>
