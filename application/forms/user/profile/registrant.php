<?php
$user = new User();

//name form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form');



/**
 * login data
 */
$user = new Form_SubForm($this);
$this->addSubForm($user, 'user');

//username
$user->addElement('text', 'username', [
    'label' => translate()->_('username'),
    'validators' => [],
    'disabled' => true, #TODO dit in rechten?
    'ignore' => true
]);

//password
$user->addElement('password', 'password', [
    'label' => translate()->_('password'),
    'renderPassword' => false,
    'validators' => [
        'passwordPattern',
    ]
]);

$tenant_subforms = [
    [
        'tenant_subform' => 'general',
        'address_subform' => 'user_address'
    ]
];

$u_model = new Users();



foreach($u_model->getPartnerUserFields() as $partnerUserField){
    $subforms = $u_model->getPartnerSubforms($partnerUserField);

    $tenant_subforms[] = [
        'tenant_subform' => $subforms['tenant'],
        'address_subform' => $subforms['address']
    ];
}

// bind the subform name array to the view
$this->getOwner()->view->tenant_subforms = $tenant_subforms;
foreach ($tenant_subforms as $tenant) {
    include 'subforms/registrant.php';
}



if(!Settings::get('hide_current_adddress')) {
    $user_address = new Form_SubForm($this);
    $this->addSubForm($user_address, 'user_address');

    $user_address->addElement('text', 'address', [
        'label' => translate()->_('address'),
        'required' => true,
        'validators' => [
            [
            ]
        ]
    ]);

    $user_address->addElement('text', 'number', [
        'label' => translate()->_('number'),
        'required' => true,
        'validators' => []
    ]);

    $zipcode_validate = $language == 'nl' ? [
        [
            'regex',
            false,
            'options' => ['pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/']
        ]
    ] : [];

    $user_address->addElement('text', 'zipcode', [
        'label' => translate()->_('postal'),
        'required' => true
    ]);

    $user_address->addElement('text', 'city', [
        'label' => translate()->_('city'),
        'required' => true,
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [2, 50]
            ]
        ]
    ]);
}

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => translate()->_('save')
));
