<?php
$user_address = new Form_SubForm($this, ['class' => 'currentaddress tenant_subform_address ' . $tenant['tenant_subform']]);
$this->addSubForm($user_address, $tenant['address_subform']);


if($project_row->tenant_page_current_address_required) {
    $user_address->addElement('select', 'invoice', [
        'label' =>  translate()->_('current_address'),
        'multioptions' => [
            'user' => translate()->_('next_address')
        ],
        'validators' => [],
        'disabled' => 'disabled',
        'registerInArrayValidator' => false
    ]);
} else {
    $user_address->addElement('select', 'invoice', [
        'label' => 'Voorkeur adres',
        'multioptions' => [
            'object' => 'Bovenstaand adres',
            'user' => 'Een ander adres'
        ],
        'validators' => [],
        'registerInArrayValidator' => false
    ]);
}

if($project_row->tenant_page_current_address_required == '1')
    $user_address->invoice->setAttrib('disabled', 'disabled');

$user_address->addElement('text', 'address', [
    'label' => translate()->_('address'),
    'validators' => [
        [
        ]
    ]
]);

$user_address->addElement('text', 'number', [
    'label' => translate()->_('number'),
    'validators' => []
]);

$zipcode_validate = $language == 'nl' ? [
    [
        'regex',
        false,
        'options' => ['pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/']
    ]
] : [];

$user_address->addElement('text', 'zipcode', [
    'label' => translate()->_('postal')
]);

$user_address->addElement('text', 'city', [
    'label' => translate()->_('city'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 50]
        ]
    ]
]);

//country
$user_address->addElement('select', 'country', array(
    'label' 		=> 	translate()->_('country'),
    'multiOptions' => Address::getCountriesForFormMultiselect(),
    'validators' 	=> 	array(),
));



if ($project_row->tenant_page_current_address_required) {
    foreach ($user_address as $user_addres_input_name => $user_address_input) {
        if ($user_addres_input_name != 'invoice') {
            $user_address_input->setRequired(true);
        }
    }
}