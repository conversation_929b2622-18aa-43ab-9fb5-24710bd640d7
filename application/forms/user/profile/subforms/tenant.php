<?php
$tenant_subform = new Form_SubForm($this, ['class' => 'tenant_subform ' . $tenant['tenant_subform']]);
$this->addSubForm($tenant_subform, $tenant['tenant_subform']);

$project = (Project::getDetails(loginManager::data()->project['id']));

$tenant_subform->addElement('hidden', 'should_be_saved');

$tenant_subform->addElement('select', 'gender', [
    'label' => translate()->_('gender'),
    'multioptions' => [
        'female' => ucfirst(translate()->_('gender_female')),
        'male' => ucfirst(translate()->_('gender_male')),
        'unknown' => ucfirst(translate()->_('unknown')),
        'inherit' => ucfirst(translate()->_('inherit')),
        'none' => ucfirst(translate()->_('none'))
    ],
    'validators' => [],
]);

if (Settings::get('software_country') != 'be') {
    $tenant_subform->addElement('text', 'initials', [
        'label' => translate()->_('initials'),
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [0, 100]
            ]
        ],
        'filters' => [
            'DotsBetweenInitials'
        ]
    ]);
}

$tenant_subform->addElement('text', 'firstname', [
    'label' => translate()->_('firstname'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

if (Settings::get('software_country') != 'be') {
    $tenant_subform->addElement('text', 'middlename', [
        'label' => translate()->_('middlename'),
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [2, 20]
            ]
        ]
    ]);
}

$tenant_subform->addElement('text', 'name', [
    'label' => translate()->_('surname'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

$fieldConfig = [
    'label' => translate()->_('phone'),
    'validators' => $language != 'nl' ? [] : [
        [
            'validator' => 'digits'
        ],
        [
            'validator' => 'stringLength',
            'options' => [9, 20]
        ],
    ]
];

if (Settings::get('tenantportal_lock_changeprofile')) {
    $fieldConfig['disabled'] = true;
}

$tenant_subform->addElement('text', 'phone_home', $fieldConfig);

$tenant_subform->addElement('text', 'birthplace', [
    'label' => translate()->_('birthplace'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

$fieldConfig = [
    'label' => translate()->_('nationality'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
];
if (Settings::get('tenantportal_lock_changeprofile')) {
    $fieldConfig['disabled'] = true;
}

$tenant_subform->addElement('text', 'nationality', $fieldConfig);

$tenant_subform->addElement('text', 'bdate', [
    'label' => translate()->_('birth'),
    'validators' => [],
    'required' => true,
    'class' => 'DatePicker bdate'
]);


if (Settings::get('software_country') == 'be') {
    $tenant_subform->addElement('text', 'BSN', [
        'label' => translate()->_('BSNBE'),
        'validators' => [],
        'required' => true,
    ]);

}


$fieldConfig = [
    'label' => 'E-mail',
    'validators' => ['emailAddress'],
    'required' => true,
    'attribs' => ['autocomplete' => 'off']
];

if (Settings::get('tenantportal_lock_changeprofile')) {
    $fieldConfig['disabled'] = true;
}

$tenant_subform->addElement('text', 'email', $fieldConfig);

 if ($project['contracting']['student_complex'] && Settings::get('software_country') == 'be') {
 $tenant_subform->addElement('text', 'email_parent', [
    'label' => translate()->_('email_parent'),
    'validators' => ['emailAddress'],
    'required' => true,
    'attribs' => ['autocomplete' => 'off']
]);

 }

$fieldConfig = [
    'label' => translate()->_('checkemail'),
    'validators' => [
        [
            'emailAddress'
        ],

        ['identical', false, ['token' => 'email']]
    ],
    'required' => true,
    'attribs' => ['autocomplete' => 'off']
];
if (Settings::get('tenantportal_lock_changeprofile')) {
    $fieldConfig['disabled'] = true;
}

$tenant_subform->addElement('text', 'emailcheck', $fieldConfig);


if ($project['contracting']['student_complex']) {
    $tenant_subform->addElement('text', 'school', [
        'label' => translate()->_('school'),
        'required' => true,
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [2, 100]
            ]
        ]
    ]);


    $tenant_subform->addElement('text', 'study_direction', [
        'label' => translate()->_('study_direction'),
        'required' => true,
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [2, 100]
            ]
        ]
    ]);

    $tenant_subform->addElement('select', 'school_input', [
        'label' => translate()->_('school_input'),
        'required' => true,
        'multioptions' => [
            '1e Bachelor' => '1e Bachelor',
            '2e Bachelor' => '2e Bachelor',
            '3e Bachelor' => '3e Bachelor',
            '1e Master' => '1e Master',
            '2e Master' => '2e Master',
            'Specialisatiejaar' => 'Specialisatiejaar',
            'Individueel traject' => 'Individueel traject',
            'Banama' => 'Banama',
            'Manama' => 'Manama'
        ]
    ]);


    $tenant_subform->addElement('text', 'emergency_contactname', [
        'label' => translate()->_('emergency_contactname'),
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [2, 100]
            ]
        ]
    ]);

    $tenant_subform->addElement('text', 'phone_emergency', [
        'label' => translate()->_('emergency_contactphone'),
        'validators' => $language != 'nl' ? [] : [
            [
                'validator' => 'Digits'
            ],
            [
                'validator' => 'stringLength',
                'options' => [9, 20]
            ]
        ],
        'required' => !$this->getOwner()->view->first
    ]);


}
