<?php
$tenant_subform = new Form_SubForm($this, ['class' => 'tenant_subform ' . $tenant['tenant_subform']]);
$this->addSubForm($tenant_subform, $tenant['tenant_subform']);

$tenant_subform->addElement('hidden', 'should_be_saved');

$tenant_subform->addElement('select', 'gender', [
    'label' => translate()->_('gender'),
    'multioptions' => [
        'female' => ucfirst(translate()->_('gender_female')),
        'male' => ucfirst(translate()->_('gender_male')),
        'unknown' => ucfirst(translate()->_('unknown')),
        'inherit' => ucfirst(translate()->_('inherit')),
        'none' => ucfirst(translate()->_('none'))
    ],
    'validators' => [],
]);

$tenant_subform->addElement('text', 'initials', [
    'label' => translate()->_('initials'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [0, 100]
        ]
    ],
    'filters' => [
        'DotsBetweenInitials'
    ]
]);

$tenant_subform->addElement('text', 'firstname', [
    'label' => translate()->_('firstname'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

$tenant_subform->addElement('text', 'middlename', [
    'label' => translate()->_('middlename'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 20]
        ]
    ]
]);

$tenant_subform->addElement('text', 'name', [
    'label' => translate()->_('surname'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

$tenant_subform->addElement('text', 'bdate', [
    'label' => translate()->_('birth'),
    'validators' => [],
    'required' => true,
    'class' => 'DatePicker bdate'
]);

$tenant_subform->addElement('text', 'birthplace', [
    'label' => translate()->_('birthplace'),
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [2, 100]
        ]
    ],
    'required' => true
]);

$tenant_subform->addElement('text', 'phone_home', [
    'label' => translate()->_('mobile'),
    'validators' => $language != 'nl' ? [] : [
        [
            'validator' => 'Digits'
        ],
        [
            'validator' => 'stringLength',
            'options' => [9, 20]
        ]
    ],
    'required' => true
]);

$tenant_subform->addElement('text', 'email', [
    'label' => 'E-mail',
    'validators' => ['emailAddress'],
    'required' => true,
    'attribs' => ['autocomplete' => 'off']
]);

if(!Settings::get('hide_income_and_living_fields')) {
    $tenant_subform->addElement('select', 'source_of_income', [
        'label' => translate()->_('source_of_income'),
        'multiOptions' => [
            '' => 'Onbekend',
            'loondienst' => 'Loondienst',
            'Loondienst (tijdelijk)' => 'Loondienst (tijdelijk)',
            'Loondienst (onbepaald)' => 'Loondienst (onbepaald)',
            'pensioen' => 'Pensioen',
            'uitkering' => 'Uitkering',
            'ondernemer' => 'Ondernemer',
            'zzp' => 'ZZP',
        ],
        'required' => true,
    ]);

    $tenant_subform->addElement('text', 'income_amount', [
        'label' => translate()->_('income_amount'),
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [
                    'max' => 255,
                ]
            ]
        ],
        'required' => true,
    ]);

    $tenant_subform->addElement('select', 'living_situation', [
        'label' => translate()->_('living_situation'),
        'multiOptions' => [
            '' => 'Onbekend',
            'koopwoning' => 'Koopwoning',
            'Koopwoning (verkocht)' => 'Koopwoning (verkocht)',
            'Koopwoning (niet verkocht)' => 'Koopwoning (niet verkocht)',
            'huurwoning' => 'Huurwoning',
            'thuiswonend' => 'Thuiswonend'
        ],
        'required' => true,
    ]);
}




if (loginManager::data()->info['profile_completion'] == '2' || loginManager::data()->info['profile_completion'] == '3' ) {
    
    $tenant_subform->addElement('textarea', 'nameplate', [
        'label' => translate()->_('nameplate'),
        'required' => false,
        'rows' => 5
    ]);
}
