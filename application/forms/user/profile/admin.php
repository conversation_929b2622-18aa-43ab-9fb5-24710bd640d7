<?

	//name form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

	/**
	 * login data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//username
		$user->addElement('text', 'username', array(
			'label' => 'Gebruikersnaam',
			'validators' => array(),
		));

		//password
		$user->addElement('password', 'password', array(
			'label' => 'Wachtwoord',
			'renderPassword' => true,
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 100)
				)
			)
		));

	/**
	 * name data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//gender
		$general->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$general->addElement('text', 'initials', array(
			'label' 		=> 	'Initialen',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));


			//firstname
		$general->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));

		//middlename
		$general->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			)
		));

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));


		//email
		$general->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress')
		));


	//submit
	$this->addElement('submit', 'profile', array(
		'label' => 'Gegevens opslaan'
	));

?>