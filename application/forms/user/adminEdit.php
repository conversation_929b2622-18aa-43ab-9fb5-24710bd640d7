<?
	$this->setNewStyle(true);

	//name form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

	/**
	 * login data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//username
		$user->addElement('text', 'username', array(
			'label' => 'Gebruikersnaam',
            'validators' =>	['uniqueValue'],
		));

		

		//password
		$user->addElement('password', 'password', array(
			'label' => 'Wachtwoord',
			'renderPassword' => true,
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 100)
				)
			)
		));
		
		$user->addElement('password', 'confirm_password', array(
			'label' => 'Wachtwoord (nogmaals)',
			'renderPassword' => true,
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 100)
				)
			)
		));

		$user->addElement('select', 'internal', [
			'label' => 'Afdeling',
			'multioptions' => Departments::getDepartments(),
		]);

		$userRights =
			[
				'internal' => '1. Stagiaire',
				'employee' => '2. Werknemer',
				'manager' => '3. Manager',
				'cfo' => '4. Directie',
				'admininternal' => '5. Administrator',
			];

		if(loginManager::data()->rights == 'admin')
            $userRights = $userRights +
				[
					'admin' => '6. Omniboxx admin',
				];

		$user->addElement('select', 'type', array(
			'label' => 'Rechten',
			'multioptions' 	=> $userRights,
			'validators' => array()
		));

		$user->addElement('multiselect', 'branch_locations', [
			'label' => 'Vestigingen',
			'multiOptions' => (new BranchLocations())->getAssociativeList(),
			'attribs' => [
				'title' => 'Met deze optie kunnen project worden gekozen voor deze gebruiker. Een aanpassing wordt toegepast nadat de gebruiker opnieuw is ingelogd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
            ]
        ]);


        $languageOptions = [
            'nl' => 'Nederlands',
            'en' => 'Engels',
        ];

        if(Settings::get('software_french_enabled'))
            $languageOptions['fr'] = 'Frans';

        $user->addElement('select', 'language', [
            'label'        => 'Taal',
            'multiOptions' => $languageOptions,
        ]);


		$mailaccounts = array();
        $defaultmailaccounts[0] = 'Geen ingesteld';

		foreach(EmailAccounts::getList() as $account){
            $mailaccounts[$account['id']] = $account['name'];
            $defaultmailaccounts[$account['id']] = $account['name'];

        }


		$user->addElement('checkbox', 'hide_menu_items_for_project_user', array(
			'label' => 'Beperkt menu',
			'attribs' => array(
				'title' => 'Deze vestigings-medewerker heeft een beperkte menu opties',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$user->addElement('multiselect', 'mailaccounts', [
			'label' => 'Mail accounts verbergen dashboard',
			'multiOptions' => $mailaccounts,
			'attribs' => [
				'title' => 'Met deze optie kunnen mail accounts worden verborgen voor deze gebruiker op het dashboard. Een aanpassing wordt toegepast nadat de gebruiker opnieuw is ingelogd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
                'autocomplete' => 'off',
            ]
        ]);

        $user->addElement('multiselect', 'mailaccounts_support', [
            'label' => 'Mail accounts verbergen huurderspagina',
            'multiOptions' => $mailaccounts,
            'attribs' => [
                'title' => 'Met deze optie kunnen berichten van mail accounts worden verborgen voor deze gebruiker op de huurderpagina. Een aanpassing wordt toegepast nadat de gebruiker opnieuw is ingelogd.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
                'autocomplete' => 'off',
            ]
        ]);

        $user->addElement('select', 'default_mailaccount', array(
            'label' => 'Standaard mailaccount',
            'multiOptions' => $defaultmailaccounts,
            'attribs' => array(
                'title' => 'Met deze optie kunt u een standaard mail account kiezen. Dit is het standaard mail adres dat wordt gekozen bij het maken van mails',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));



		// does financial info need to be hidden for this user
		$user->addElement('checkbox', 'hide_financial_info', array(
			'label' => 'Verberg financiele info',
			'attribs' => array(
				'title' => 'Indien aangevinkt, vervallen alle financiele opties uit het menu en bij huurders worden geen
							huursommen en openstaande posten getoond.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)		
		));

$user->addElement('checkbox', 'lock_user_object_and_component_version', [
    'label' => 'Vergrendel contract data en facturatie bedragen',
    'attribs' => [
        'title' => 'Met deze setting kan deze medewerker niet contract data en facturatie bedragen bij het object invoeren of bewerken, maar wel inzien. Na het aanpassen van deze instelling moet de gebruiker opnieuw inloggen voordat de nieuwe instelling actief wordt.',
        'class' => 'forceAutoHint',
    ]
]);

if (loginManager::data()->rights !== 'admin') {
    $user->lock_user_object_and_component_version
        ->setAttrib('disabled', true)
        ->setIgnore(true);
}

		$user->addElement('checkbox', 'only_inspections_user', array(
			'label' => 'Alleen inspectie app login',
			'attribs' => array(
				'title' => 'Deze medewerker mag alleen inspecties uitvoeren op de inspectie app en niet inloggen in de Omniboxx backoffice.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

        $user->addElement('checkbox', 'calendar_hide', array(
            'label' => 'Verberg gebruiker in Agenda',
            'attribs' => array(
                'title' => 'Indien aangevinkt dan wordt de gebruiker standaard niet aan de lijst toegevoegd.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

		$user->addElement('checkbox', 'show_assigned_email_only', [
			'label' => 'Nieuwe mails verbergen',
			'attribs' => [
				'title' => 'In eigen mailbox enkel de eigen berichten tonen, niet-toegewezen berichten verbergen. De gebruiker heeft wel de mogelijkheid om naar de Inbox of mailbox van andere gebruikers te gaan.',
				'class' => 'forceAutoHint',
			]
		]);

		$user->addElement('checkbox', 'show_own_mail_only', [
			'label' => 'Enkel eigen E-mail tonen',
			'attribs' => [
				'title' => 'Enkel de eigen E-mail berichten weergeven, geen optie om te wisselen naar de inbox of de mailbx van andere gebruikers. Let op; de instelling is pas van kracht na opnieuw inloggen.',
				'class' => 'forceAutoHint',
			]
		]);

	$mplus = new Form_SubForm($this);
	$this->addSubForm($mplus, 'mplus');

		$mplus->addElement('text', 'mplus_id', array(
			'label' => 'Mplus gebruiker id',
			'validators' => array()
		));

	/**
	 * name data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//gender
		$general->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$general->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				),
				//'filters' => array(
				//	'filter' => 'dotsBetweenInitials',
				//),
			),
			'required' => true
		));
		
		$general->initials->addFilter(new Zend_Filter_DotsBetweenInitials());


			//firstname
		$general->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));
		//middlename
		$general->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			)
		));

		//shortname
		$general->addElement('text', 'shortname', array(
			'label' => 'Shortname',
			'validators' => array(),
			'required' => true,
			'attribs' => [
				'title'      => 'Vul hier 2-3 letters in die deze persoon identificeren, zoals zijn/haar initialen.
					Deze worden gebruikt om aan te geven over welke gebruiker het gaat op schermen in het systeem waar
					de gehele naam te veel ruimte in zou nemen.',
				'class'      => 'forceAutoHint',
				'hintOffset' => "{'x': -20, 'y': 0}"
			]
		));

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));


		//direct telefoonnummer of mobiel
		$general->addElement('text', 'internal_user_phone', array(
			'label' => 'Telefoonnummer (direct)',
			'required' => false,
		));


		//generiek tekst veld voor melden info/beschikbaarheid
		$general->addElement('textarea', 'internal_user_comment_signature', array(
			'label' => 'Aanwezigheid',
			'rows' => 5,
			'cols' => 35,
			'validators' => array(),
		));

		
		


		//email
		$general->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress'),
			'required' => true,
		));




	//submit
	$this->addElement('submit', 'profile', array(
		'label' => 'Gegevens opslaan',
		'class' => 'class="btn btn-info waves-effect waves-light"',
	));

?>
