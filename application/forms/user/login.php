<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'login')
			->setAttrib('class', 'login form');

	$login = new Form_SubForm($this);
	$this->addSubForm($login, 'user');

		//email
		$login->addElement('text', 'username', array(
			'label' => translate()->_('username'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 50)
				)
			),
			'attribs' => array(
				'placeholder' => translate()->_('username')
			),
			'required' => true
		));

		//password
		$login->addElement('password', 'password', array(
			'label' => translate()->_('password'),
			'attribs' => array(
				'placeholder' => translate()->_('password')
			),
			'required' => true,
			'renderPassword' => true
		));

    //GA code
    $login->addElement('text', 'GA_code', array(
      'label' => translate()->_('Authenticator code'),
      'attribs' => array(
        'autofocus'=>'true',
        'placeholder' => translate()->_('Authenticator code')
      ),
      'required' => false
    ));

    $login->addElement('checkbox', 'remember_device_and_location', [
        'label' => 'Apparaat en locatie onthouden'
    ]);

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'login', array(
			'label' => translate()->_('login')
	));

?>
