<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'cellular')
			->setAttrib('class', 'cellular form');

	$this->addElement('checkbox', 'skip', array(
		'label' => translate()->_('havenomobile'),
		'validators' => array(
		),
	));

	$this->addElement('text', 'phone', array(
		'label' => translate()->_('phone'),
		'validators' => array(
		),
	));

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => translate()->_('save')
	));