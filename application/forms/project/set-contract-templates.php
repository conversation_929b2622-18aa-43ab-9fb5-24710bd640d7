<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('id', 'project')
		->setAttrib('class', 'object form');

$templates = new Form_SubForm($this);
$this->addSubForm($templates, 'templates');

	// template for commercial contracts
	$templates->addElement('select', 'contract_template_commercial', array(
		'label' 		=> 	'Commercieel',
		'multioptions' 	=> 	$this->getAttrib('contractTemplatesCommercial'),
		'validators' 	=> 	array(),
		'registerInArrayValidator' => false,
	));

	// template for private contracts
	$templates->addElement('select', 'contract_template_private', array(
		'label' 		=> 	'Particulier',
		'multioptions' 	=> 	$this->getAttrib('contractTemplatesPrivate'),
		'validators' 	=> 	array(),
		'registerInArrayValidator' => false,
	));

	// template for commercial contracts in English
	$templates->addElement('select', 'contract_template_commercial_en', array(
		'label' 		=> 	'Commercieel (Engels)',
		'multioptions' 	=> 	$this->getAttrib('contractTemplatesCommercial'),
		'validators' 	=> 	array(),
		'registerInArrayValidator' => false,
	));

	// template for private contracts in English
	$templates->addElement('select', 'contract_template_private_en', array(
		'label' 		=> 	'Particulier (Engels)',
		'multioptions' 	=> 	$this->getAttrib('contractTemplatesPrivate'),
		'validators' 	=> 	array(),
		'registerInArrayValidator' => false,
	));


    // template for commercial contracts in English
    $templates->addElement('select', 'contract_template_commercial_fr', array(
        'label' 		=> 	'Commercieel (Frans)',
        'multioptions' 	=> 	$this->getAttrib('contractTemplatesCommercial'),
        'validators' 	=> 	array(),
        'registerInArrayValidator' => false,
    ));

    // template for private contracts in English
    $templates->addElement('select', 'contract_template_private_fr', array(
        'label' 		=> 	'Particulier (Frans)',
        'multioptions' 	=> 	$this->getAttrib('contractTemplatesPrivate'),
        'validators' 	=> 	array(),
        'registerInArrayValidator' => false,
    ));

//submit
$this->addElement('submit', 'project', array(
	'label' => 'Keuzes opslaan'
));


