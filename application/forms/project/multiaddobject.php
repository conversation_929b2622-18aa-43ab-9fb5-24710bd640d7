<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'object')
			->setAttrib('class', 'object form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//object identifier
		$general->addElement('text', 'identifier', array(
			'label' 		=> 	'Objectnummer',
			'validators' 	=> 	array(),
			'disabled'		=>	true,
			'ignore' 		=> true
		));

		//object id
		$general->addElement('text', 'id', array(
			'label' 		=> 	'Klantnummer',
			'value' 		=> 	'Automatisch',
			'validators' 	=> 	array(),
			'disabled'		=>	true,
			'ignore' 		=> true
		));

		//meter
		$general->addElement('text', 'meter', array(
			'label' 		=> 	'Meternummer + type',
			'validators' 	=> 	array()
		));

		//build
		$general->addElement('text', 'build', array(
			'label' 		=> 	(Settings::get('build_label')  ? Settings::get('build_label') : (Settings::get('software_type') == 'energy' ? "Bouwnummer" : 'Kamernummer')),
			'validators' 	=> 	array()
		));

		//lot
		$general->addElement('text', 'lot', array(
			'label' 		=> 	'Kavelnummer',
			'validators' 	=> 	array()
		));


	$address = new Form_SubForm($this);
	$this->addSubForm($address, 'address');

		//address
		$address->addElement('text', 'address', array(
			'label' 		=> 	'Adres',
			'validators' 	=> 	array(),
		));

		//zipcode
		$address->addElement('text', 'zip', array(
			'label' 		=> 	'Postcode',
			'validators' 	=> 	array(),
		));

		//city
		$address->addElement('text', 'city', array(
			'label' 		=> 	'Woonplaats',
			'validators' 	=> 	array(),
		));


	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');

		//submit
	$this->addElement('submit', 'project', array(
		'label' => 'Objecten opslaan'
	));

?>
