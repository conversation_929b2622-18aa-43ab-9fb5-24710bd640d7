<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'project')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'project form');

 	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//naam
		$general->addElement('text', 'name', array(
			'label' => 'Naam project',
			'placeholder' => 'Projectnaam invullen',
			'validators' => array(),
			'required'		=>	true
		));

		//naam
		$general->addElement('text', 'invoicename', array(
			'label' => 'Naam project tbv facturatie',
			'validators' => array(),
		));

		// short name
		$general->addElement('text', 'shortname', array(
			'label' 		=> 	'Afkorting ',
			'placeholder' => 	'Projectafkorting (4 Karakters)',
			'validators' 	=> 	array(),
			'required'		=>	true
		));

		//city
		$general->addElement('text', 'city', array(
			'label' => 'Plaats ',
			'validators' => array(),
		));
		
		//street
		$general->addElement('text', 'address', array(
			'label' => 'Straat',
			'validators' => array(),
		));

		//number
		$general->addElement('text', 'number', array(
			'label' => 'Nummer',
			'validators' => array(),
		));
		//zipcode
		$general->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => array(),
		));

        $general->addElement('text', 'country', [
            'label' => 'Land',
            'validators' => [],
        ]);

		//date
		$general->addElement('text', 'date', array(
			'label' => 'Datum oplevering',
			'validators' => array(),
		));

		$months = [0 => 'Geen afrekening'];
		for($i = 1; $i <= 12; $i++)
			$months[$i] = ucfirst(strftime('%B', mktime(0, 0, 0, $i)));

		//date
		$general->addElement('select', 'final_nota_month', array(
			'label' => 'Maand servicekosten afrekening',
			'validators' => array(),
			'multiOptions' 	=> 	$months
		));

		//date
        for ($i=0; $i < 10; $i++)
            if($year = date('Y', strtotime('-' . $i . ' years')))
                $years[$year] = $year;
		$general->addElement('select', 'final_nota_start_year', array(
			'label' => 'Startjaar servicekosten afrekening',
            'multioptions' 	=> $years,
		));

		//date
		$general->addElement('text', 'consumption_estimate_adjust_percentage', array(
			'label' => 'Verbruiksschatting bijstel percentage',
			'validators' => array(),
		));

		// Corporation
		$general->addElement('select', 'corporation', array(
			'label' 		=> 	'Juridische entiteit',
			'attribs'		=> [
				'style' => 'max-width: 275px;'
			],
		));

        $general->addElement('select', 'branch_location', [
            'disabled' => !in_array(loginManager::data()->rights, ['admin', 'admininternal']) ? 'disabled' : null,
            'label' => 'Vestiging',
            'multiOptions' => [0 => '-- Selecteer een vestiging --'] + (new BranchLocations())->getAssociativeList(),
        ]);

		// CA
		$general->addElement('select', 'ca', array(
			'label' 		=> 	'Incassobureau',
			'validators' 	=> 	array(),
			'multiOptions' 	=> 	array(),
		));



		
		// langauge
		$general->addElement('select', 'language', array(
			'label' 		=> 	'Voorkeurstaal',
			'validators' 	=> 	array(),
			'multiOptions' 	=> 	array(
				'nl' => 'Nederlands',
				'en' => 'Engels'
			),
		));

		// Project identifier
		$general->addElement('text', 'identifier', array(
			'label' 		=> 	'Projectnummer',
			'validators' 	=> 	array(),
			'required'		=>	Settings::get('software_type') == 'energy'
		));
		
		// Ledger  
		$general->addElement('text', 'ledger', array(
			'label' 		=> 	'Grootboekrekening',
			'validators' 	=> 	array(),
			'required'		=>	false
		));

		// Third party project  
		$general->addElement('checkbox', 'for_third_party', array(
			'label' 		=> 	'Beheer project',
			'validators' 	=> 	array(),
			'checked'		=>  Settings::get( 'for_third_party' )? true : false,	
			'required'		=>	false,
		));

		$general->addElement('select', 'investor_corporation', array(
			'label' 		=> 	'Juridische entiteit beheer',
			'attribs'		=> [
				'style' => 'max-width: 275px;'
			],
		));

		$general->addElement('select', 'rental_corporation', array(
			'label' 		=> 	'Juridische entiteit verhuur',
			'attribs'		=> [
				'style' => 'max-width: 275px;'
			],
		));

		// Cost Center  
		$general->addElement('text', 'cost_center', array(
			'label' 		=> 	'Kostenplaats',
			'validators' 	=> 	array(),
			'required'		=>	false
		));

		$general->addElement('text', 'project_code_financial', [
			'label' 		=> 	'Projectcode',
		]);

		$general->addElement('text', 'relation_code_project_financial', [
			'label' 		=> 	'Relatiecode',
		]);

		$general->addElement('text', 'external_id', [
			'label' 		=> 'Extern ID',
		]);

if (Settings::get('software_XXL')) {
		$usersModel = new Users();
		/*
		 * dep 3 is financial
		 * dep 2 is technical
		 * dep 1 is commercial
		 */
		$intUsersFinancial = $usersModel->getInternalUsers(false , true);
		$intUsersTechnical = $usersModel->getInternalUsers(false, true);
		$intUsersCommercial = $usersModel->getInternalUsers(false, true);

		foreach ($intUsersFinancial as $user)
			if(empty($user['enddate']))
				$intUsersFinancial[$user['id']] = User::buildname($user);

			$intUsersFinancial = [0 => 'Geen'] + $intUsersFinancial;

		foreach ($intUsersTechnical as $user)
			if(empty($user['enddate']))
				$intUsersTechnical[$user['id']] = User::buildname($user);

			$intUsersTechnical = [0 => 'Geen'] + $intUsersTechnical;

		foreach ($intUsersCommercial as $user)
			if(empty($user['enddate']))
				$intUsersCommercial[$user['id']] = User::buildname($user);

			$intUsersCommercial = [0 => 'Geen'] + $intUsersCommercial;


		$general->addElement('select', 'responsible_financial', [
			'label' => 'Financieel verantw.',
             'value' =>  null,
			'multiOptions'	=> (array) $intUsersFinancial,
		]);

		$general->addElement('select', 'responsible_technical', [
			'label' => 'Technisch verantw.',
             'value' =>  null,
			'multiOptions'	=> (array) $intUsersTechnical,
		]);

		$general->addElement('select', 'account_manager', [
			'label' => 'Accountmanager',
			'multiOptions'	=> (array) $intUsersCommercial,
             'value' =>  null,
		]);
}


		// Ratesheet
		$general->addElement('select', 'ratesheet', array(
			'label' 		=> 	'Standaard componenten template',
			'validators' 	=> 	array(),
			'multiOptions' 	=>  array(),
			'required'		=>	false
		));

		$general->addElement('text', 'project_code_type_financial', [
			'label' 		=> 	'Type project',
		]);

		$general->addElement('checkbox', 'purchase_invoice_vat_use_half_as_default', [
			'label' 		=> 	'Inkoopfactuur, standaard 10.5% / 4,5% BTW',
			'validators' 	=> 	[],
			'multiOptions' 	=>  [],
			'required'		=>	false
        ]);

		// Project identifier
		$general->addElement('select', 'investor', array(
			'label' 		=> 	'Investeerder',
			'validators' 	=> 	array(),
			'multiOptions' 	=> 	array(
				1 => 'Ja',
				0 => 'Nee'
			),
		));
		
		$general->addElement('checkbox', 'end_exploitation', array(
			'label' => 'Uit exploitatie',
			'validators' => array()
		));

		// description
		$general->addElement('textarea', 'description', array(
			'label' => 'Korte omschrijving',
			'placeholder' => 	'Geef hier een korte samenvatting van het project en de objecten binnen het project.',
			'validators' => array(),
			'rows'=> 15,
			'cols'=> 35
		));

		$general->addElement('textarea', 'description_long', [
			'label' => 'Uitgebreide omschrijving',
			'rows'=> 15,
			'cols'=> 35
        ]);

		$general->addElement('select', 'investor_tech_report_building_id', [
			'label' 		=> 	'Gebouwcode in meters database',
			'attribs' => [
				'title' => 'Aangezien het belangrijk is dat deze waarde niet dubbel is, zijn hier alleen de opties beschikbaar'
					.' die nog niet ingesteld zijn bij andere projecten.',
				'class' => 'forceAutoHint',
			]
		]);




	/**
	 * Project details
	 */
	$details = new Form_SubForm($this);
	$this->addSubForm($details, 'details');


		// complex
		$details->addElement('checkbox', 'complex', [
			'label' => 'Betreft het project een complex?',
		] );

$projecttypes = (array) db()->fetchPairs( db()->select()->from('projects_type', array('id', 'name')) );

		//type real estate
		$details->addElement('select', 'projectType', array(
			'label' => 'Type project',
			'validators' => array(),
			'multiOptions' => $projecttypes
		));

		// size (m2)
		$details->addElement('text', 'size', array(
			'label' => 'Totaaloppervlakte',
            'validators' => array(),
		));

		// size (m2)
		$details->addElement('textarea', 'plot', array(
			'label' => 'Kadastrale gegevens',
			'rows'=> 7,
			'cols'=> 35,
			'validators' => array()
		));


		$details->addElement('text', 'woz_previous_year', [
			'label' 		=> 	'WOZ ' . (date('Y') - 1) . ' €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
            'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
            ],
		]);

		$details->addElement('text', 'WOZ', [
			'label' 		=> 	'WOZ ' . date('Y') . ' €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
            'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
            ],
        ]);

		$details->addElement('text', 'woz_next_year', [
			'label' 		=> 	'WOZ ' . (date('Y') + 1) . ' €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
            'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
            ],
        ]);

		$details->addElement('text', 'purchase_costs', [
			'label' 		=> 	'Aanschafkosten €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
            'validators' => [
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
            ]
		]);

		$details->addElement('text', 'purchase_price', [
			'label' 		=> 	'Aankoopsom €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
			'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
			],
		]);

		$details->addElement('text', 'appraisal_value', [
			'label' 		=> 	'Taxatiewaarde (actueel) €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "."Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
				'class' => 'forceAutoHint',
			],
            'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
            ],
		]);

		$details->addElement('text', 'internal_appraisal_value', [
			'label' 		=> 	'Interne waarde (actueel) €',
            'attribs' => [
                'title' => 'Vul de waarde in hele duizendtallen in, eventueel gescheiden door een "." Alles achter een komma  wordt niet opgeslagen. Wilt u de waarde verwijderen haal dan het hele veld leeg en vul niets in.',
                'class' => 'forceAutoHint',
            ],
			'validators' 	=> 	[
                [
                    'validator'	=> 'GreaterThan',
                    'options'	=> [
                        'min' => 0,
                    ]
                ],
			],
		]);


		// homes
		$details->addElement('text', 'private', array(
			'label' => 'Aantal VHE particulier',
			'validators' => array(
				['validator' => 'float']
			)
		));

		// shops
		$details->addElement('text', 'commercial', array(
			'label' => 'Aantal VHE commercieel',
			'validators' => array()
		));

		// startdate
		$details->addElement('text', 'startdate', array(
			'label' => 'Standaard opleverdatum',
			'validators' => array()
		));

		// deadline_startdate
		$details->addElement('text', 'deadline_startdate', [
			'label' => 'Deadline opleverdatum',
			'attribs' => [
				'title' => 'Stel hier wat de deadline is waarop zaken als een omgevingsvergunning geregeld moeten zijn.
				In contract templates kan deze datum gebruikt worden als word merge variabele om aan te geven dat het
				contract ontbonden mag worden indien de deadline niet gehaald wordt.',
				'class' => 'forceAutoHint',
			]
		] );

		// enddate
		$details->addElement('text', 'first_enddate', array(
			'label' => 'Standaard einddatum eerste periode',
			'validators' => array()
		));
		
		// startdate
		$details->addElement('checkbox', 'auto_task', array(
			'label' => 'Acties aanmaken',
			'validators' => array()
		));

		$pc = new ProjectsCities();
		$details->addElement('select', 'allowed_cities', [
			'multiple' => 'multiple',
			'label' => 'Plaatsnamen',
			'registerInArrayValidator' => false,
			'validators' => [],
			'multiOptions' => $pc->getListForSelect(),
			'attribs' => array(
				'title' => 'De plaatsnamen waaruit gekozen kan worden beperken voor vestigingmedewerkers gekoppeld aan dit project. Verwijder de invoer om alle plaatsnamen mogelijk te maken.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		]);


	$contracting = new Form_SubForm($this);
	$this->addSubForm($contracting, 'contracting');		


		$contracting->addElement('checkbox', 'enabled', array(
			'label' => 'Digitaal contracteren',
			'validators' => array()
		));

		$contracting->addElement('checkbox', 'signature_enabled', [
			'label' => "Digitaal contracteren met handtekening",
			'validators' => [],
			'value' => true
        ]);

		$contracting->addElement('checkbox', 'student_complex', array(
			'label' => 'Studenten complex',
			'validators' => array(),
			'attribs' => array(
				'title' => 'Indien studenten complex worden tijdens digitale registratie en profiel velden over studie en nood contactpersoon toegevoegd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)

		));

if (Settings::get('modules_tenantLogin_nameplate_email')) {
        $contracting->addElement('checkbox', 'nameplate_enabled', [
            'label' => 'Naamplaatje ingeschakeld',
            'validators' => [],
            'value' => true
        ]);
}


$contracting->addElement('checkbox', 'current_uo', array(
			'label' => 'Leegstand situatie overnemen',
			'validators' => array()
		));

		$contracting->addElement('select', 'tenant_page_communication_options', array(
			'label' => 'Communicatie opties',
			'multiOptions' 	=> 	array(
                'system' => 'Systeemvoorkeur',
				'all' => 'E-mail en post',
				'email' => 'Enkel E-mail',
				'post' => 'Enkel post'
			),
			'attribs' => array(
				'title' => 'Instellen uit welke communicatie opties er beschikbaar zijn voor de huurders',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

        $documentSets = RentalDocumentcheckSets::getUserSets();
        $contracting->addElement('select', 'tenant_page_documentcheck_set', array(
            'label' => 'Document controle set (indien afwijkend van default)',
            'multiOptions' 	=>  [nullValue() => 'Systeemstandaard'] + $documentSets,
            'attribs' => [
                'title' => 'Instellen uit welke documenten aangeleverd moeten worden tijdens verhuurproces',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ]
        ));


		$contracting->addElement('checkbox', 'tenant_page_current_address_required', array(
			'label' => 'Huidige adres verplicht',
			'attribs' => array(
				'title' => 'Met deze opties kan de invoer van een huidig adres verplicht gemaakt worden voor de huurders',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));


		$contracting->addElement('checkbox', 'tenant_page_collection_payment', array(
			'label' => 'Automatische incasso',
			'attribs' => array(
				'title' => 'De keuze voor betaalmethode "automatische incasso" in- of uitschakelen voor de huurders',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$contracting->addElement('checkbox', 'tenant_page_iban_hide_before_contract', array(
			'label' => 'IBAN gegevens verbergen voor contracteren',
			'attribs' => array(
				'title' => 'Verberg de IBAN bij invoer voordat het contract ondertekend is',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));
 
 

		$contracting->addElement('checkbox', 'tenant_page_manual_payment', array(
			'label' => 'Handmatig betalen',
			'attribs' => array(
				'title' => 'De keuze voor betaalmethode "handatig" in- of uitschakelen voor de huurders',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$contracting->addElement('text', 'tenant_page_emergency_phone', array(
			'label' => 'Nood telefoonnummer'
		));		


		


 
	$roomselector = new Form_SubForm($this);
	$this->addSubForm($roomselector, 'roomselector');

		if (Settings::get('modules_hospitality')) {
			$roomselector->addElement('checkbox', 'hospitality_enabled', [
				'label' => 'Hospitality project',
				'value' => false,
				'attribs' => [
					'title' => 'Bepaald of er voor dit project hospitality diensten worden aangeboden.',
					'class' => 'forceAutoHint',
				]
			]);
		}

		$roomselector->addElement('text', 'roomselector_projectname', array(
			'label' => 'Projectnaam',
			'validators' => array()
		));

		$roomselector->addElement('text', 'roomselector_contractcosts', array(
			'label' => 'Hoogte contractkosten',
			'validators' => array()
		));

		$roomselector->addElement('select', 'roomselector_contractcosts_invoice_send', [
			'label' 		=> 	'Contractkosten factuur verzenden',
			'multiOptions' 	=> 	[
				'off'       => 'Niet',
				'manual'    => 'Handmatig',
				'auto'      => 'Automatisch',
			],
			'attribs' => [
				'title' => 'Moet er na contract ondertekening een factuur opgesteld worden voor de contractkosten,'
					.' en zo ja, moet die automatisch verzonden worden of alleen klaargezet bij de handmatige facturen.',
				'class' => 'forceAutoHint',
			]
		]);
 

		$roomselector->addElement('text', 'roomselector_url', array(
			'label' => 'Url website',
			'validators' => array()
		));

 
		$roomselector->addElement('textarea', 'roomselector_locationmeeting', array(
			'label' => 'Locatie afspraak & toelichting documenten',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));

		$roomselector->addElement('textarea', 'roomselector_explanation_beheerder_nl', array(
			'label' => 'Introductietekst projectportal (o.a. project of uitleg beheerder)',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));

		$roomselector->addElement('textarea', 'roomselector_explanation_privacy_nl', array(
			'label' => 'Toelichting privacy',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));


		$roomselector->addElement('textarea', 'roomselector_explanation_beheerder_en', array(
			'label' => 'Introductietekst projectportal (o.a. project of uitleg beheerder Engels)',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));

		$roomselector->addElement('textarea', 'roomselector_explanation_privacy_en', array(
			'label' => 'Toelichting privacy Engels',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));


		$roomselector->addElement('textarea', 'roomselector_explanation_beheerder_fr', array(
			'label' => 'Introductietekst projectportal (o.a. project of uitleg beheerder Frans)',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));

		$roomselector->addElement('textarea', 'roomselector_explanation_privacy_fr', array(
			'label' => 'Toelichting privacy Frans',
			'cols'	=> 35,
			'rows'	=> 12,
			'validators' => array()
		));
		

		$roomselector->addElement('text', 'roomselector_logo', array(
			'label' => 'Logo uploaden',
			'attribs' => array(
				'class' => 'imageUpload',
				'image-upload-type' => 'portal_logo'
			)
		));

		$roomselector->addElement('text', 'roomselector_background', array(
			'label' => 'Achtergrond persoonlijke pagina uploaden',
			'attribs' => array(
				'class' => 'imageUpload',
				'image-upload-type' => 'portal_background'
			)
		));


		$email = new EmailAccounts();



		$roomselector->addElement('select', 'communication_mail_account', array(
			'label' => 'E-mailaccount voor verzending',
			'multioptions' 	=> $email->getForForm(),
			'attribs' => array ('style' => 'width: 300px'),
		));


		$roomselector->addElement('textarea', 'project_email_closure', array(
			'label' => 'Getoonde afsluiting standaardmails',
			'rows' => 3,
			'cols' => 50,
			'attribs' => array(
				'placeholder' => "Met vriendelijke groet, " . (Settings::get('general_company') == 'Vaanster' ? 'Vaanster klantenservice' : Settings::get('general_company')),
				'title' => 'De afsluiting van automatisch gegenereerde E-mail berichten en mails die worden opgesteld via mail adres op de huurder pagina.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)			
		));

	
			// template for commercial contracts
		$roomselector->addElement('select', 'contract_template_commercial', array(
			'label' 		=> 	'Commercieel contract',
			'multioptions' 	=> 	 array(),
			'validators' 	=> 	array(),
			'registerInArrayValidator' => false,
			'attribs' => array ('style' => 'width: 300px'),
		));

		$roomselector->addElement('select', 'contract_template_commercial_small', array(
			'label' 		=> 	'Kleinzakelijk contract',
			'multioptions' 	=> 	 array(),
			'validators' 	=> 	array(),
			'registerInArrayValidator' => false,
			'attribs' => array ('style' => 'width: 300px'),
		));

		// template for private contracts
		$roomselector->addElement('select', 'contract_template_private', array(
			'label' 		=> 	'Particuliercontract',
			'multioptions' 	=> 	 array(),
			'validators' 	=> 	array(),
			'registerInArrayValidator' => false,
			'attribs' => array ('style' => 'width: 300px'),
		));

		// template for private contracts
		$roomselector->addElement('select', 'contract_template_invite', array(
			'label' 		=> 	'Uitnodigingsbrief',
			'multioptions' 	=> 	 array(),
			'validators' 	=> 	array(),
			'registerInArrayValidator' => false,
			'attribs' => array(
				'style' => 'width: 300px',
				'title' => 'Merk op dat voor de verwerking van het template voor deze brief '
					.'(in grote lijnen) alleen de object gerelateerde variabelen beschikbaar zijn. Controleer '
					.'het template dus voor u het naar huurders verzend!',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)	
		));

		$pp_options = [
			'profile' => 'Wijzigen persoonlijke gegevens',
			'invoices' => 'Facturen inzien',
			'documents' => 'Documenten inzien',
			'email-list' => 'Emails inzien',
			'info/tarieven' => 'Tarieven inzien',
			'migrate' => 'Verhuizing doorgeven',
			'consumption' => 'Meterstanden inzien'
		];

		if(Settings::get('software_type') != 'energy')
			unset($pp_options['migrate'],$pp_options['consumption'],$pp_options['info/tarieven']);

 
		$roomselector->addElement('select', 'personal_page_settings', array(
			'label' 		=> 	'Opties persoonlijke pagina',
			'multioptions' 	=> 	 $pp_options,
			'validators' 	=> 	array(),
			'multiple'			=>	true,
			'attribs' => array ('style' => 'width: 300px'),
			'registerInArrayValidator' => false

		));

		$pp_change_options = [
			'legal_company' => 'Contractant',
			'legal_contact' => 'Rechtsgeldig vertegenwoordigd',
			'contract_date' => 'Oplevering',
			'address_invoice' => 'Adressering facturatie',
			'contact_complaint' => 'Contactpersoon bij storing',
			'payment_info' => 'Betalingsgegevens'
		];


 
		$roomselector->addElement('select', 'personal_page_change_settings', array(
			'label' 		=> 	'Opties persoonlijke gegevens verbergen',
			'multioptions' 	=> 	 $pp_change_options,
			'validators' 	=> 	array(),
			'multiple'			=>	true,
			'attribs' => [
			    'style' => 'width: 300px',
                'title' => 'Door hier opties te selecteren kunnen deze formulier onderdelen verborgen worden op de persoonlijke pagina voor commerciële huurders.',
                'class' => 'forceAutoHint',
            ],
			'registerInArrayValidator' => false

		));




	/**
	 * Project rates
	 */
	$rate = new Form_SubForm($this);
	$this->addSubForm($rate, 'rate');

		// home
		$rate->addElement('text', 'home', array(
			'label' => 'Tariefblad woningen',
			'validators' => array()
		));

		// shop
		$rate->addElement('text', 'shop', array(
			'label' => 'Tariefblad winkels',
			'validators' => array()
		));

	$email = new Form_SubForm($this);
	$this->addSubForm($email, 'email');

		$email->addElement('textarea', 'signature', array(
			'label' => 'Handtekening',
			'rows' => 5,
			'cols' => 50,
			'attribs' => array(
				'placeholder' => 'Enkel invoeren indien afwijkend van algemene instelling',
				'title' => 'In deze tekst kunnen de volgende variabelen worden toegepast:<br /><br /><b>[user_name]</b>: Naam huidige gebruiker<br /><b>[company_name]</b>: Naam van het bedrijf<br /><b>[address_street]</b>: Straat<br /><b>[address_number]</b>: Huisnummer<br /><b>[address_zipcode]</b>: Postcode<br /><b>[address_city]</b>: Stad<br /><b>[phone]</b>: Telefoon<br /><b>[email]</b>: E-mail<br /><b>[phone_user]</b>: Telefoon direct<br /><b>[aanwezigheid]</b>: Aanwezigheid<br /><b>[website]</b>: Website<br /><b>[logo]</b>: Logo <small>Afhankelijk van instelling: <i>Positie logo</i></small>',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)
		));

        $emailaccounts = new EmailAccounts();

        $email->addElement('select', 'inspection_mail_account', array(
            'label' => 'E-mailaccount voor verzending van inspectierapporten',
            'multioptions' 	=> $emailaccounts->getForForm(),
            'attribs' => array ('style' => 'width: 300px'),
        ));


	$financial_report = new Form_SubForm($this);
	$this->addSubForm($financial_report, 'financial_report');

		$financial_report->addElement('text', 'financial_report_tab', array(
			'label' => 'Titel tabblad',
			'validators' => array()
		));

		$financial_report->addElement('checkbox', 'exclude_from_broker_publish', [
			'label' 		=> 	'Uitsluiten van rapportage',
			'checked'		=>	false,
			'attribs' => [
				'title' => 'Sluit dit project geheel uit van de belegger rapportage (heeft geen invloed op rapportages
					die al gepubliceerd zijn).',
				'class' => 'forceAutoHint',
			]
		]);

	$complaint = new Form_SubForm($this);
	$this->addSubForm($complaint, 'complaint');

		$c_lib = new Complaint();

		$complaint->addElement( 'select', 'complaint_add_employee_info_to_mail', [
			'label'        => 'Medewerkergegevens in klant-e-mails',
			'multioptions' => [
				'setting' => 'Instelling overnemen',
				'yes'     => 'Ja',
				'no'      => 'Nee',
			],
			'attribs'      => [
				'title' => 'Zet de naam, het telefoonnummer en de aanwezigheid gegevens van de interne medewerker
									die aan een melding gekoppeld is in de e-mails die een klant informeren over de status van een
									melding. Dit overschrijft de algemene instelling, die op "' .
				           ( ! Settings::get( 'complaint_add_employee_info_to_mail' ) ? 'NIET' : 'WEL' ) . ' toevoegen" staat.',
				'class' => 'forceAutoHint',
			]
		] );

		$complaint->addElement('textarea', 'access_presumed_text', array(
			'label' => 'Melding toetreding verondersteld',
			'placeholder' => $c_lib->access_presumed_text,
			'validators' => array(),
			'rows'=> 7,
			'cols'=> 35,
			'attribs' => array(
				'title' => 'Deze tekst kan optioneel worden toegevoegd aan de melding naar de huurder wanneer de optie "Toetreding verondersteld" ingeschakeld wordt.<br /><br />(Enkel aanpassen om de tekst bij dit project te laten afwijken van de standaardtekst.)',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -30, 'y': 0}"
			)
		));	

		$complaint->addElement('textarea', 'access_presumed_text_en', array(
			'label' => 'Melding toetreding verondersteld Engels',
			'placeholder' => $c_lib->access_presumed_text_en,
			'validators' => array(),
			'rows'=> 7,
			'cols'=> 35,
			'attribs' => array(
				'title' => 'De engelstalige variant van voorgaande instelling',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -30, 'y': 0}"
			)
		));	
	/**
	 * Project details
	 */
	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');

		// invoicable
		$invoice->addElement('checkbox', 'enabled', array(
			'label' 		=> 	'Factureren',
			'validators' 	=> 	array(),
            'attribs' => array(
				'title' => 'Indien niet aangevinkt worden geen automatische prolongatie runs gemaakt Let op: dit kan wel handmatig alsnog evenals handamtige facturen.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			),
			'checked'		=>	true
		));

		//startdate
		$invoice->addElement('text', 'startdate', array(
			'label' => 'Facturatie vanaf',
			'value' => date('d-m-Y'),
			'validators' => array(),
		));

        $invoice->addElement('checkbox', 'create_initial_project_runs', [
            'label' 		=> 	'Factuurruns aanmaken vanaf startdatum',
            'validators' 	=> 	[]
        ]);
		
		
		$invoice->addElement('text', 'percentage_costs_first_formalnotice', array(
			'label' => 'Percentage WIK ',
			'validators' => array(),
			'attribs' => array(
				'title' => 'Vul hier het percentage tussen 0 en 100 in voor WIK toeslag.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$invoice->addElement('text', 'post_amount', array(
			'label' => 'Posttoeslag',
			'validators' => array(),
			'attribs' => array(
				'title' => 'Vul hier de toeslag voor postverzending in. Vul 0 indien indien geen posttoeslag',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
				'placeholder' => '€ ' . ((string) new StringFormat(Settings::get('invoice_post_amount'), 'money'))
			)
		));

		// commercial message
		$invoice->addElement('textarea', 'commercialmessage', array(
			'label' => 'Melding wettelijke rente / contract afspraken zakelijk',
			'validators' => array(),
			'rows'=> 7,
			'cols'=> 35
		));

		$invoice->addElement('textarea', 'invoice_message', array(
			'label' => 'Opmerking op facturen',
			'rows'=> 7,
			'cols'=> 35,
			'attribs' => [
				'title' => 'Extra opmerking onderaan alle facturen plaatsen. ' . (Settings::get('for_third_party') ? 'Enkel bij facturen vanuit de normale entiteit, niet bij facturen vanuit de beheer entiteit.' : ''),
				'class' => 'forceAutoHint'
			]
		));


$vidii = new Form_SubForm($this);
$this->addSubForm($vidii, 'vidii');

$vidii->addElement('checkbox', 'vidii_enabled', [
	'label'   => 'Vidii project',
	'value'   => false,
	'attribs' => [
		'title' => 'Bepaald of dit project op aparte nieuwbouw site gepubliceerd wordt.',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('select', 'vidii_registration_method', [
	'label' => 'Registratie methode',
	'multiOptions' => [
        'as_rental_deal' => 'als deal'
	],
	'attribs' => [
		'title' => "Bepaald op welke manier inschrijvingen op objecten uit dit project verwerkt worden:<br>"
			."* <b>als deal</b>: inschrijvers worden geregistreert als deals voor het object"
		,
		'class' => 'forceAutoHint',
	],
]);


$vidii->addElement('select', 'vidii_deal_method', [
    'label' => 'Deal methode',
    'multiOptions' => [
        'as_rental_deal' => 'Onder website aanvragen',
    ],
    'attribs' => [
        'title' => "Bepaald op welke manier inschrijvingen op objecten uit dit project verwerkt worden:<br>"
            ."* <b>Onder website aanvragen</b>: Inschrijvingen komen onder Website aanvragen"
        ,
        'class' => 'forceAutoHint',
    ],
]);

$vidii->addElement('text', 'vidii_wordpress_import_import_url', [
	'label' => 'Vidii Wordpress import url',
	'readonly' => true,
	'attribs' => [
		'title' => 'Deze url moet in de "WP All Import" ingevuld worden in het "Download from url" veld.',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('text', 'vidii_wordpress_import_trigger_url', [
	'label' => 'Vidii Wordpress bijwerk trigger url',
	'attribs' => [
		'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
			.'import van dit project->Cron Scheduling. Heeft ongeveer dit formaat:'
			.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=trigger',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('text', 'vidii_wordpress_import_process_url', [
	'label' => 'Vidii Wordpress bijwerk execution url',
	'attribs' => [
		'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
			.'import van dit project->Cron Scheduling. Heeft ongeveer dit formaat:'
			.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=processing',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('text', 'vidii_wordpress_availability_trigger_url', [
	'label' => 'Vidii Wordpress beschikbaarheid trigger url',
	'attribs' => [
		'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
			.'import van dit project->Cron Scheduling. Heeft ongeveer dit formaat:'
			.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=trigger',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('text', 'vidii_wordpress_availability_process_url', [
	'label' => 'Vidii Wordpress beschikbaarheid execution url',
	'attribs' => [
		'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
			.'import van dit project->Cron Scheduling. Heeft ongeveer dit formaat:'
			.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=processing',
		'class' => 'forceAutoHint',
	],
]);

$vidii->addElement('checkbox', 'vidii_new_lead_notification', [
	'label'   => 'Nieuwe inschrijving notificatie',
	'value'   => false,
]);

$vidii->addElement('checkbox', 'vidii_dossier_submitted_notification', [
	'label'   => 'Dosier ingediend notificatie',
	'value'   => false,
]);


$rental = new Form_SubForm($this);
$this->addSubForm($rental, 'rental');


	$select = db()->select()
		->from('rental_deals_status_templates', ['id', 'name'])
        ->where('finalized = ?', true)
        ->where('concept = ?', false)
		->where('archived = ?', false)
		->order(['default DESC', 'name ASC']);

	if($allowed_projects = loginManager::allowedProjects()){
        $select->where('`projects` IS NULL OR CONCAT(",", `projects`, ",") REGEXP ",(' . implode('|', $allowed_projects) . '),"');
	}

	$rental->addElement('select', 'rental_deals_default_template', [
		'label' => 'Standaard deal template',
		'validators' => [],
		'multiOptions' => ['0' => 'Selecteer een template'] +  db()->fetchPairs($select)
    ]);

    $rental->addElement('number', 'rental_price_min', [
        'label' => 'Huurprijs vanaf',
    ]);

    $rental->addElement('number', 'rental_price_max', [
        'label' => 'Huurprijs t/m',
    ]);

    $rental->addElement('number', 'square_measurements_min', [
        'label' => 'Inhoud vanaf',
    ]);

    $rental->addElement('number', 'square_measurements_max', [
        'label' => 'Inhoud t/m',
    ]);

    $rental->addElement('number', 'living_area_size_min', [
        'label' => 'Woonoppervlakte vanaf',
    ]);

    $rental->addElement('number', 'living_area_size_max', [
        'label' => 'Woonoppervlakte t/m',
    ]);



//submit
	$this->addElement('submit', 'project', array(
		'label' => 'Project opslaan'
	));

?>
