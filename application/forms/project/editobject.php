<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'object')
			->setAttrib('class', 'object form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//object identifier
		$general->addElement('checkbox', 'inactive', array(
			'label' 		=> 	translate()->_('object_edit_inactive'),
			'validators' 	=> 	[],
		));

        $objectInactiveReason = (new ObjectInactiveReason())->getObjectInactiveReasons(Settings::get('modules_rental'));

        $general->addElement('select', 'inactive_reason', [
            'label' 		=> translate()->_('object_edit_inactive_specification'),
            'multioptions' 	=> array_column($objectInactiveReason, 'reason', 'id'),
            'validators' 	=> 	[],
        ]);

        $general->addElement('text', 'inactive_till', [
            'label' 		=> 	translate()->_('object_edit_inactive_enddate'),
            'validators' 	=> 	[],
            'class' => 'DatePicker',
            'attribs' => [
                'title' => translate()->_('object_edit_inactive_enddate_warning'),
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -30, 'y': 0}",
            ],
        ]);

		$general->addElement('checkbox', 'finalNotaBlock', array(
			'label' 		=> 	'SVK afrekening blokkeren',
			'validators' 	=> 	[],
		));

		$general->addElement('checkbox', 'indexationBlock', array(
			'label' 		=> 	'Indexatie blokkeren',
			'validators' 	=> 	[],
		));

        //object identifier
        $general->addElement('text', 'bag_id', array(
            'label' 		=> 	'BAG ID',
            'validators' 	=> 	[],
        ));

		//object identifier
		$general->addElement('text', 'identifier', array(
			'label' 		=> 	translate()->_('object_edit_identifier'),
			'validators' 	=> 	[],
			'disabled'		=>	true,
			'ignore' => true
		));

		

		//meter
		$general->addElement('text', 'meter', array(
			'label' 		=> 	'Meternummer + type',
			'validators' 	=> 	[]
		));

		if (Settings::get('software_type') == "real estate") { 
			$bankaccounts = db()->fetchPairs(db()->select()->from(array('cba'=>'corporations_bank_accounts'), array('id','iban'))
																	->order('id DESC')
																	->where('corporation_id = ?', $this->getOwner()->project->corporation) 
																	->where('is_current = ?', '0') 
																	->where('is_active = ?', '1') 	
																	);
			
			
			

			$default = array(NULL=> 'Standaardrekeningnummer');
			$bankaccounts = $default + $bankaccounts;
			
			$general->addElement('select', 'bankaccount', array(
				'label' 		=> 	'Overmaken naar',
				'multioptions' 	=> 	$bankaccounts,
				'validators' 	=> 	[],
				'multiple'			=>	false,
				'registerInArrayValidator' => false

			));
		}


        $build_options = [
            'label' => (Settings::get('build_label')  ? Settings::get('build_label') : translate()->_('object_edit_build_label'))
        ];



        $general->addElement('text', 'build', $build_options);


		

		//lot

        $lot_options = [
            'label' => (Settings::get('lot_label')  ? Settings::get('lot_label') : translate()->_('object_edit_lot_label'))
        ];

        if(Settings::get('build_label_autoincrement')) {
            $lot_options['disabled'] = true;
            $lot_options['ignore'] = true;
        }

		$general->addElement('text', 'lot', $lot_options);


		// Home owner
		$this->addElement('text', 'ownerlabel', array(
			'label' 		=> 	'Contractant',
			'validators' 	=> 	[],
		));

		// Home owner
		$this->addElement('hidden', 'owner', array(
			'validators' 	=> 	[],
		));

		// Tenant id
		$this->addElement('text', 'tenantlabel', array(
			'label' 		=> 	'Geselecteerde huurder/koper',
			'validators' 	=> 	[],
		));

		// 2nd Tenant id
		$this->addElement('text', 'tenant2label', array(
			'label' 		=> 	'Tweede huurder/koper',
			'validators' 	=> 	[],
		));
		
		



		$this->addElement('hidden', 'tenant', array(
			'validators' 	=> 	[],
		));

		$this->addElement('hidden', 'tenant2', array(
			'validators' 	=> 	[],
		));

	$connection = new Form_SubForm($this);
	$this->addSubForm($connection, 'connection');

		$mcp_lib = new MeterConnectionPrices();

		$providers = [];
		foreach($mcp_lib->getProviders() as $provider)
			$providers[$provider['value']] = $provider['label'];

		$meter_types = [];
		foreach($mcp_lib->getTypes() as $type_key => $meter_type){
			if(!isset($meter_types[$type_key])) $meter_types[$type_key] = [];

			foreach($meter_type as $meter_type_item)
				$meter_types[$type_key][$meter_type_item['value']] = $meter_type_item['label'];
		}

		$connection->addElement('select', 'provider', array(
			'label' 		=> 	'Netleverancier',
			'multioptions' 	=> 	array('Selecteer een netleverancier') + $providers,
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$connection->addElement('select', 'electricity', array(
			'label' 		=> 	'Electriciteitaansluiting',
			'multioptions' 	=> 	array('Selecteer een electriciteitaansluiting') + $meter_types['electricity'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$connection->addElement('select', 'gas', array(
			'label' 		=> 	'Gasaansluiting',
			'multioptions' 	=> 	array('Selecteer een gasaansluiting') + $meter_types['gas'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

	$address = new Form_SubForm($this);
	$this->addSubForm($address, 'address');

		//address
		$address->addElement('text', 'address', array(
			'label' 		=> 	translate()->_('object_edit_address'),
			'validators' 	=> 	[],
		));

		//number
		$address->addElement('text', 'number', array(
			'label' 		=> 	translate()->_('object_edit_number'),
			'validators' 	=> 	[],
		));

		//zipcode
		$address->addElement('text', 'zip', array(
			'label' 		=> 	translate()->_('object_edit_zipcode'),
			'validators' 	=> 	[],
		));

		//city
		$address->addElement('text', 'city', array(
			'label' 		=> 	translate()->_('object_edit_city'),
			'validators' 	=> 	[],
		));


        $pc = new ProjectsCities();
        $cities = $pc->getListForSelect();
        $allowed_cities_filtered = $pc->allowedCitiesFiltered($owner->project->id);

        if(Settings::get('object_defined_cities_enabled')) {

            $pc->filterForUser($owner->project->id);
            $address->addElement('select', 'city', [
                'label' => translate()->_('object_edit_city'),
                'validators' => [],
                'multiOptions' => $cities
            ]);
        }

        if (Settings::get('software_only_rental') && Settings::get('software_country') == 'nl') {
            array_unshift($cities, translate()->_('select_option'));
            $address->addElement('select', 'city', [
                'label' => 	translate()->_('object_edit_city'),
                'registerInArrayValidator' => false,
                'validators' => [],
                'multiOptions' => $cities,
            ]);
        }







        $ts = new ProjectsTownships();

        $address->addElement('select', 'township', [
            'label' => translate()->_('object_edit_township'),
            'validators' => [],
            'multiOptions' => $ts->getListForSelect()
        ]);

		$address->addElement( 'select', 'district', [
			'label'        => translate()->_('object_edit_township'),
			'multiOptions' => Address::getDistricts()
		] );


        $pr = new ProjectsProvinces();

        $address->addElement('select', 'province', [
            'label' => translate()->_('object_edit_province'),
            'validators' => [],
            'multiOptions' => $pr->getListForSelect()
        ]);

        $address->addElement('text', 'country', [
            'label' => translate()->_('object_edit_country'),
            'validators' => [],
        ]);

	$objecttext = new Form_SubForm($this);
	$this->addSubForm($objecttext, 'objecttext');

	$objecttext->addElement('textarea', 'short_nl', array(
		'label' 		=> 	'Intro kort (nl)',
		'validators' 	=> 	[],
		'rows'			=> 	4,
		'required' 		=>	false
	));

	$objecttext->addElement('textarea', 'short_en', array(
		'label' 		=> 	'Intro kort (en)',
		'validators' 	=> 	[],
		'rows'			=> 	4,
		'required' 		=>	false
	));

	$objecttext->addElement('textarea', 'long_nl', array(
		'label' 		=> 	'Uitgebreide tekst (nl)',
		'validators' 	=> 	[],
		'rows'			=> 	8,
		'required' 		=>	false
	));

	$objecttext->addElement('textarea', 'long_en', array(
		'label' 		=> 	'Uitgebreide tekst (en)',
		'validators' 	=> 	[],
		'rows'			=> 	8,
		'required' 		=>	false
	));


    $financial = new Form_SubForm($this);
    $this->addSubForm($financial, 'financial');

        //Grootboek
        $financial->addElement('text', 'ledger', array(
            'label' 		=> 	'Grootboekrekening',
            'validators' 	=> 	[]
        ));

        //Ledger
        $financial->addElement('text', 'cost_center', array(
            'label' 		=> 	'Kostenplaats',
            'validators' 	=> 	[]
        ));

        //Ledger
        $financial->addElement('text', 'cost_carrier', array(
            'label' 		=> 	'Kostendrager',
            'validators' 	=> 	[]
        ));

        $financial->addElement('text', 'project_code_override', [
                'label' 		=> 	'Projectcode '.FinancialExportSystems::getLabelForActiveSystem(),
                'attribs'		=> [
                    'title' => 'Met dit veld kan de Projectcode die op het project is ingesteld overschreven worden.',
                    'class' => 'forceAutoHint',
                ]
            ]);

        $financial->addElement('text', 'project_relation_code_override', [
                'label' 		=> 	'Relatiecode '.FinancialExportSystems::getLabelForActiveSystem(),
                'attribs'		=> [
                    'title' => 'Met dit veld kan de Relatiecode die op het project is ingesteld overschreven worden.',
                    'class' => 'forceAutoHint',
                ]
            ]);

        //Purchase price
        $financial->addElement('text', 'purchase_price', array(
                'label' 		=> 	'Aankoopsom €',
                'validators' 	=> 	[]
            ));


        $financial->addElement('text', 'woz_previous_year', [
                'label' 		=> 	'WOZ ' . (date('Y') - 1) . ' €',
                'validators' 	=> 	[]
            ]);

        $financial->addElement('text', 'woz', [
                'label' 		=> 	'WOZ ' . date('Y') . ' €',
                'validators' 	=> 	[],
            ]);

		$financial->addElement('text', 'woz_next_year', [
			'label' 		=> 	'WOZ ' . (date('Y') + 1) . ' €',
			'validators' 	=> 	[],
		]);

        $financial->addElement('html', 'woz_link', [
            'label' 		=> 	'',
            'html' => '<a class="woz_link" href="https://www.wozwaardeloket.nl/" target="_blank">WOZ waarde opzoeken</a>',
        ]);


		$financial->addElement('text', 'appraisal_value', [
			'label' 		=> 	'Taxatiewaarde (actueel) €',
			'validators' 	=> 	[],
		]);
		$financial->addElement('text', 'internal_appraisal_value', [
			'label' 		=> 	'Interne waarde (actueel) €',
			'validators' 	=> 	[],
		]);

        $financial->addElement('text', 'purchase_costs', [
                'label' 		=> 	'Aanschafkosten €',
                'validators' 	=> 	[]
            ]);


$energydetails = new Form_SubForm($this);
$this->addSubForm($energydetails, 'energydetails');


//Energy label

if(Settings::get('software_country') == 'be'){
    $energydetails->addElement('text', 'energy_label', array(
        'label' 		=> 	'Energie label',
        'validators' 	=> 	[]
    ));
} else {
    $energydetails->addElement('select', 'energy_label', [
        'label' => translate()->_('object_edit_energy_label'),
        'multioptions' => [
            'false' => '-',
            'A+++++' => 'A+++++',
            'A++++' => 'A++++',
            'A+++' => 'A+++',
            'A++' => 'A++',
            'A+' => 'A+',
            'A' => 'A',
            'B' => 'B',
            'C' => 'C',
            'D' => 'D',
            'E' => 'E',
            'F' => 'F',
            'G' => 'G',
            'Niet van toepassing' => 'Niet van toepassing',
            'Recreatie' => 'Recreatie',
            'In ontwikkeling' => 'In ontwikkeling',
            'Monument' => 'Monument',
        ],
        'validators' => [],
    ]);


}

$energydetails->addElement('text', 'energy_label_issue_date', [
    'label' => translate()->_('energy_label_issue_date'),
    'attribs' =>
        [
            'class' => 'DatePicker',
        ],

]);

$energydetails->addElement('text', 'energy_label_valid_till_date', [
    'label' => translate()->_('energy_label_valid_till_date'),
    'attribs' =>
        [
            'class' => 'DatePicker',
        ],

    
]);

$energydetails->addElement('text', 'energy_label_final', [
    'label' => translate()->_('object_edit_energy_label_final'),
    'validators' => [],
]);

$energydetails->addElement('text', 'energy_index', [
    'label' => translate()->_('object_edit_energy_index'),
    'validators' => [],
]);



	$objectdetails = new Form_SubForm($this);
	$this->addSubForm($objectdetails, 'objectdetails');

		if (Settings::get('software_type') == "energy") {
			//tapklasse
			$objectdetails->addElement('text', 'tapkl', array(
				'label' 		=> 	'Tapklasse',
				'validators' 	=> 	[]
			));
		}


        // object type
        $otModel = new ObjectType();
        $types = $otModel->fetchAll($otModel->select());
        $objectTypes = ['' => 'Onbekend objecttype'];
        foreach ($types as $type)
            $objectTypes[$type['id']] = $type['name'];

        $objectdetails->addElement('select', 'type', array(
            'label' 		=> 	'Objecttype',
            'multioptions' 	=> 	$objectTypes,
            'validators' 	=> 	[],
            'multiple'		=>	false
        ));

		//huurpunten
		$objectdetails->addElement('text', 'points', array(
			'label' 		=> 	'Huurpunten',
			'validators' 	=> 	[]
		));


		//aantal parkeerplekken
		$objectdetails->addElement('text', 'parking_spots', array(
			'label' 		=> 	'Aantal parkeerplaatsen',
			'validators' 	=> 	[]
		));


        //aantal parkeerplekken
        $objectdetails->addElement('checkbox', 'independent', array(
            'label' 		=> 	'Zelfstandig',
            'validators' 	=> 	[]
        ));

        $rentTypes = ['' => 'Maak een keuze',
            'social' => 'Sociaal',
            'socialMiddle' => 'Sociaal (midden inkomen)',
            'socialHigh' => 'Sociaal (hoog inkomen)',
            'middle' => 'Middenhuur',
            'freeSector' => 'Vrije sector'];

        //aantal parkeerplekken
        $objectdetails->addElement('select', 'rent_type', array(
            'label' 		=> 	'Huursegment',
            'multioptions' 	=> 	$rentTypes,
            'validators' 	=> 	[]
        ));

		//parkeernummber
		$objectdetails->addElement('text', 'parking_number', array(
			'label' 		=> 	'Parkeerplaats nummer',
			'validators' 	=> 	[]
		));

		//aantal bewoners
		$objectdetails->addElement('text', 'number_occupants', array(
			'label' 		=> 	'Max. aantal bewoners',
			'validators' 	=> 	[]
		));


		//verdeelsleutel
		$objectdetails->addElement('text', 'proportionality', array(
			'label' 		=> 	'Verdeelsleutel',
			'validators' 	=> 	[]
		));
		



		$objectdetails->addElement('text', 'current_utilities_price', array(
			'label' 		=> 	'Huidig verbruik G/W/E €',
			'validators' 	=> 	[]
		));

		$objectdetails->addElement('text', 'city_taxes_price', array(
			'label' 		=> 	'Gemeentelijke lasten',
			'validators' 	=> 	[]
		));

		$objectdetails->addElement('text', 'sewage_collection_price', array(
			'label' 		=> 	'Rioolheffing',
			'validators' 	=> 	[]
		));

		$objectdetails->addElement('text', 'waste_collection_price', array(
			'label' 		=> 	'Afvalstoffenheffing',
			'validators' 	=> 	[]
		));





		// waarborg
		$objectdetails->addElement('text', 'deposit', array(
			'label' 		=> 	'Waarborg  €',
			'validators' 	=> 	[]
 		));

        $objectdetails->addElement('text', 'inspection_fee', [
            'label' => translate()->_('inspection_fee'),
        ]);


    //oppervlakte
		$objectdetails->addElement('text', 'm2', array(
			'label' => translate()->_('object_edit_m2'),
			'validators' 	=> 	[]
		));

		//oppervlakte
		$objectdetails->addElement('text', 'm2_bvo', array(
            'label' => translate()->_('object_edit_m2_bvo'),
			'validators' 	=> 	[]
		));

		//oppervlakte
		$objectdetails->addElement('text', 'year_rent', array(
            'label' => translate()->_('object_edit_year_rent_price'),
			'validators' 	=> 	[]
		));

		//oppervlakte
		$objectdetails->addElement('text', 'building_reference', array(
            'label' => translate()->_('object_edit_building_reference'),
			'validators' 	=> 	[]
		));

		//kw aansluitvermogen
		$objectdetails->addElement('text', 'kw', array(
			'label' 		=> 	'Kilowatt aansluitvermogen',
			'validators' 	=> 	[]
		));

		// warm override
		$objectdetails->addElement('text', 'warm_override', array(
			'label' 		=> 	'Individueel maandbedrag warmte',
			'validators' 	=> 	[]
		));

		// cold override
		$objectdetails->addElement('text', 'cold_override', array(
			'label' 		=> 	'Individueel maandbedrag koude',
			'validators' 	=> 	[]
		));



		$o_model = new Objects();


		$objectdetails->addElement('select', 'interior', array(
			'label' 		=> 	'Interieur',
			'multioptions' 	=>	$o_model->rental_field_values['interior'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'garden', array(
			'label' 		=> 	'Tuin',
			'multioptions' 	=>	$o_model->rental_field_values['garden'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'garden_situation', array(
			'label' 		=> 	'Ligging tuin',
			'multioptions' 	=>	$o_model->rental_field_values['garden_situation'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'garden_size', array(
			'label' 		=> 	'Tuin oppervlakte',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));


		$objectdetails->addElement('select', 'balcony', array(
			'label' 		=> 	'Balkon',
			'multioptions' 	=>	$o_model->rental_field_values['balcony'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'balcony_situation', array(
			'label' 		=> 	'Ligging balkon',
			'multioptions' 	=>	$o_model->rental_field_values['balcony_situation'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'balcony_size', array(
			'label' 		=> 	'Balkon oppervlakte',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'terrace', array(
			'label' 		=> 	'Terras',
			'multioptions' 	=>	$o_model->rental_field_values['terrace'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'terrace_situation', array(
			'label' 		=> 	'Ligging terras',
			'multioptions' 	=>	$o_model->rental_field_values['terrace_situation'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'terrace_size', array(
			'label' 		=> 	'Terras oppervlakte',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'kitchen', array(
			'label' 		=> 	'Keuken',
			'multioptions' 	=>	$o_model->rental_field_values['kitchen'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'parking_spots_type', array(
			'label' 		=> 	'Parkeerplaatsen',
			'multioptions' 	=>	$o_model->rental_field_values['parking_spots_type'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'parking_spots_text', array(
			'label' 		=> 	'Parkeerplaatsen toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'smoking', array(
			'label' 		=> 	'Roken toegestaan?',
			'multioptions' 	=> array(
									'false' => 'Nee',
									'1' => 'Ja'

								),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'animals', array(
			'label' 		=> 	'Huisdieren toegestaan?',
			'multioptions' 	=> array(
									'false' => 'Nee',
									'1' => 'Ja'

								),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'animals_text', array(
			'label' 		=> 	'Huisdieren toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'storage_room', array(
			'label' 		=> 	'Berging',
			'multioptions' 	=> array(
				'false' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'storage_room_text', array(
			'label' 		=> 	'Berging toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'garage', array(
			'label' 		=> 	'Garage',
			'multioptions' 	=> array(
				'false' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'garage_text', array(
			'label' 		=> 	'Garage toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'elevator', array(
			'label' 		=> 	'Lift',
			'multioptions' 	=> array(
									'false' => 'Nee',
									'1' => 'Ja'

								),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'elevator_text', array(
			'label' 		=> 	'Lift toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'fire_place', array(
			'label' 		=> 	'Open haard',
			'multioptions' 	=> array(
				'false' => 'Nee',
				'1' => 'Ja'

			),
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('text', 'fire_place_text', array(
			'label' 		=> 	'Open haard toelichting',
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'windows', array(
			'label' 		=> 	'Ramen',
			'multioptions' 	=>	$o_model->rental_field_values['windows'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));

		$objectdetails->addElement('select', 'type_build', array(
			'label' 		=> 	'Type bouw',
			'multioptions' 	=>	$o_model->rental_field_values['type_build'],
			'validators' 	=> 	[],
			'multiple'		=>	false
		));


		//Slaapkamers
		$objectdetails->addElement('text', 'bedrooms', array(
			'label' 		=> 	'Aantal slaapkamers',
			'validators' 	=> 	[],
		));

		// toiletten
		$objectdetails->addElement('text', 'toilets', array(
			'label' 		=> 	'Aantal toiletten',
			'validators' 	=> 	[],
		));

		// badkamers
		$objectdetails->addElement('text', 'bathrooms', array(
			'label' 		=> 	'Aantal badkamers',
			'validators' 	=> 	[],
		));

		//investor
		$objectdetails->addElement('text', 'investorlabel', array(
			'label' 		=> 	'Belegger',
			'validators' 	=> 	[],
		));

		$objectdetails->addElement('hidden', 'investor', array(
			'validators' 	=> 	[],
		));

        $objectdetails->addElement('hidden', 'reseller');
        $objectdetails->addElement('text', 'resellerlabel', [
            'label' => 'Reseller beheer',
            'autocomplete' => 'off',
            'attribs' => [
                'title' => 'Zoeken op Reseller',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
                'placeholder' => '',
                'autocompleter' => 'object/find-resellers/limit/10/',
                'autocompleter_options' => Zend_Json::encode(
                    ['hiddenElementId' => 'objectdetails-reseller'],
                    false,
                    ['enableJsonExprFinder' => true]
                )
            ],
            'validators' => [
                'ComboboxRequired' => [
                    'validator' => 'ComboboxRequired',
                    'options' => [
                        'field' => $objectdetails->getElement('reseller'),
                    ]
                ],
            ],
        ]);

        // realtor purchase amount
        $objectdetails->addElement('text', 'resellerprovisionnamount', array(
            'label' 		=> 	'Inhouding (vast bedrag per maand)',
            'validators' 	=> 	[],
            'attribs'		=> array('title' => 'Bedrag dat ingehouden wordt bij afrekening met de makelaar', 'class' => 'forceAutoHint')

        ));

        // provision percentage
        $objectdetails->addElement('text', 'resellerprovisionpercentage', array(
            'label' 		=> 	'Inhouding (percentage)',
            'validators' 	=> 	[],
            'attribs'		=> array('title' => 'Percentage at ingehouden wordt bij afrekening met de makelaar', 'class' => 'forceAutoHint')

        ));


$provision = new Form_SubForm($this);
		$this->addSubForm($provision, 'provision');

//		$provision->addElement('text', 'investor_period_formula', array(
//			'label' 		=> 	'Verleng formule',
//			'validators' 	=> 	[],
//		));
//
//		$provision->addElement('html', 'investor_period_formula_explain', array(
//			'html' 			=> 	'<small class="formulaexplain">Verlengingformule volgens indeling:<br /><b>[aantal keer herhaling] * [aantal maanden]</b><br />Bij oneindige herhaling 999*[maanden] gebruiken. \'
//				+\'Naast maanden zijn ook andere perioden bruikbaar door een letter achter het getal te zetten: \'
//				+\'d = dagen, w = weken, v = vier weken, m = maanden, j = jaren</small>',
//		));
//
//		$provision->addElement('text', 'investor_notice_period', array(
//			'label' 		=> 	'Opzegtermijn (mnd)',
//			'validators' 	=> 	['digits'],
//		));


		// provision percentage
		$provision->addElement('text', 'provision', array(
			'label' 		=> 	'Beheerprovisie (percentage)',
			'validators' 	=> 	[],
			'attribs'		=> array('title' => 'Percentage beheerprovisie bij dit object (optionele invoer om de instelling bij de geselecteerde belegger te overschrijven voor dit object)', 'class' => 'forceAutoHint')

		));

		// provision discount
		$provision->addElement('text', 'discount', array(
			'label' 		=> 	'Beheerprovisie (kortingspercentage)',
			'validators' 	=> 	[],
			'attribs'		=> array('title' => 'Percentage beheerprovisie bij dit object  (optionele invoer om de instelling bij de geselecteerde belegger te overschrijven voor dit object)', 'class' => 'forceAutoHint')

		));


		// provision amount
		$provision->addElement('text', 'provisionamount', array(
			'label' 		=> 	'Beheerprovisie (vast bedrag per maand)',
			'validators' 	=> 	[],
			'attribs'		=> array('title' => 'Bedrag beheerprovisie bij dit object (maand bv 277.50)  (optionele invoer om de instelling bij de geselecteerde belegger te overschrijven voor dit object)', 'class' => 'forceAutoHint')

		));



	$details = new Form_SubForm($this);
	$this->addSubForm($details, 'details');

		// invoice address
		$details->addElement('text', 'invoice', array(
			'label' 		=> 	'Factuur adres',
			'validators' 	=> 	[],
		));

		// oplevering date
		$details->addElement('hidden', 'date_from', array(
			'label' 		=> 	'',
			'validators' 	=> 	[]
		));

		// oplevering date
		$details->addElement('hidden', 'date_final', array(
			'label' 		=> 	'',
			'validators' 	=> 	[]
		));

		// verhuizing date
		$details->addElement('text', 'date_move', array(
			'label' 		=> 	'Datum verhuizing',
			'validators' 	=> 	[],
		));

		// contract signed
		$details->addElement('checkbox', 'unsigned', array(
			'label' 		=> 	'Contract niet getekend',
			'validators' 	=> 	[],
		));

	$vacancy = new Form_SubForm($this);
	$this->addSubForm($vacancy, 'vacancy');

		// temporary no owner or tenant
		$vacancy->addElement('checkbox', 'toggle', array(
			'label' 		=> 	'Leegstand',
			'validators' 	=> 	[],
		));

		// vacancy-date
		$vacancy->addElement('text', 'date', array(
			'label' 		=> 	'Datum leegstand',
			'validators' 	=> 	[],
		));


	$publish = new Form_SubForm($this);
	$this->addSubForm($publish, 'publish');

		$publish->addElement('checkbox', 'enabled', array(
			'label' => translate()->_('object_edit_publication_enabled'),
			'validators' 	=> 	[],
		));

		$publish->addElement('text', 'start_date', array(
			'label' => translate()->_('object_edit_publication_start_date'),
			'validators' 	=> 	[],
			'class' => 'DatePicker',
		));

        $publish->addElement('text', 'acceptance_date', [
            'label' => translate()->_('object_edit_publication_acceptance_date'),
            'validators' 	=> 	[],
            'class' => 'DatePicker',
        ]);

$publish->addElement('text', 'report_date', [
    'label' => 'Aanmeld datum',
    'validators' => [],
    'class' => 'support-datepicker',
    'attribs' => [
        'title' => 'De datum waarop het object aangemeld is, vanaf dit tijdstip wordt 48 uur berekend totdat ' .
            'de loting uitgevoerd mag worden.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -30, 'y': 0}",
    ],
]);

		$publish->addElement('checkbox', 'end_date_toggle', array(
            'label' => translate()->_('object_edit_publication_end_date_toggle'),
			'validators' 	=> 	[],
		));

		$publish->addElement('text', 'end_date', array(
			'label' 		=> 	'',
			'validators' 	=> 	[],
			'class' => 'DatePicker',
		));

		$publish->addElement('checkbox', 'mailing', array(
            'label' => translate()->_('object_edit_publication_mailing'),
			'validators' 	=> 	[],
		));

        $label = translate()->_('object_edit_publication_website');
        if (Settings::get('wonen31_easy_mode')) {
            $label .= ' / Wonen31';
        }

		$publish->addElement('checkbox', 'website', array(
            'label' => $label,
			'validators' 	=> 	[],
		));

		$publish->addElement('select', 'status_override', [
            'label' => translate()->_('object_edit_publication_status_override'),
			'validators' 	=> 	[],
			'multioptions' => ObjectsPublish::getOverrideStatusValues(),
		]);


        $publish->addElement('numberInput', 'rent_price_override', [
            'label' => 'Huur bedrag €',
            'validators' => []
        ]);

        $publish->addElement('numberInput', 'service_costs_override', [
            'label' => 'Servicekosten €',
            'validators' => []
        ]);

        $publish->addElement('numberInput', 'utility_costs_override', [
            'label' => 'Kosten Nutsvoorzieningen €',
            'validators' => [],
            'attribs' => ['title' => 'Indien ingevuld worden deze kosten in de externe bronnen bij de servicekosten opgeteld.']
        ]);
        $publish->addElement('select', 'costs_override_status', [
            'label' => 'Aangepaste prijzen',
            'validators' => [],
            'multioptions' => [
                ObjectsPublish::COSTS_OVERRIDE_DEFAULT => 'Gebruik de bedragen van het tariefblad',
                ObjectsPublish::COSTS_OVERRIDE_PUBLIC_OVERRIDE => 'Gebruik de overschreven kosten'
            ],
            'attribs' => ['title' => 'Als hier bedragen worden ingevuld zullen de bedragen van het tariefblad worden overschreven naar de externe bronnen. Voor de facturatie zelf blijven de bedragen op het tariefblad van toepassing.']

        ]);

        if (!Settings::get('wonen31_easy_mode')) {
            $Wonen31CheckBox = [
                'label' => 'Wonen31',
                'validators' => [],
    //            'disabled' => false,
                'attribs' => ['title' => 'Via deze optie wordt het object gepubliceerd op wonen31.nl']
            ];

            if (!Settings::get('wonen31_module')) {
                $Wonen31CheckBox['disabled'] = true;
            }

            $publish->addElement('checkbox', 'wonen31', $Wonen31CheckBox);
        }
		$publish->addElement('checkbox', 'funda', array(
			'label' 		=> 	'Funda/Tiara',
			'validators' 	=> 	[],
		));

		$publish->addElement('checkbox', 'pararius', array(
			'label' 		=> 	'Pararius',
			'validators' 	=> 	[],
		));

	// Discount percentage
	$this->addElement('text', 'discountperc', array(
		'label' 		=> 	'Eindejaarskorting',
		'disabled'		=>	true,
		'ignore' 		=>	true,
		'validators' 	=> 	[],
	));

	//submit
	$this->addElement('submit', 'project', array(
		'label' => translate()->_('object_edit_save_page')
	));

?>
