<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'project')
			->setAttrib('class', 'project form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//naam
		$general->addElement('text', 'name', array(
			'label' => 'Naam project',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 30)
				)
			),
		));

		//city
		$general->addElement('text', 'city', array(
			'label' => 'Plaats project',
			'validators' => array(),
		));

		//gp
		$general->addElement('select', 'projectType', array(
			'label' => 'Type project',
			'validators' => array(),
			'multiOptions' => array()
		));

	//submit
	$this->addElement('submit', 'project', array(
		'label' => 'Project toevoegen'
	));

?>