<?php

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'emailGroup')
			->setAttrib('class', 'emailGroup form');


	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Naam',
			'required' => true
		));

    $general->addElement('checkbox', 'is_mail_chimp_sync', [
        'label' => 'MailChimp synchroniseren',
        'attribs' => [
            'title' => 'Het synchronisatie process wordt nachtelijks uitgevoerd.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ]
    ]);

		$eug = new EmailUsersGroup();

		//name
		$general->addElement('select', 'type', array(
			'label' => 'Ontvanger',
			'multiOptions' => $eug->getTypeTitles()
		));

		//name
		$general->addElement('text', 'objects', array('hidden' => true));

	$filters = new Form_SubForm($this);
	$this->addSubForm($filters, 'filters');

		$filters->addElement('select', 'project', array(
			'label' 		=> 	'Project',
			'multioptions' 	=> 	db()->fetchPairs(db()->select()->from(array('p'=>'projects'), array('id','name'))->order('name')->where('exploitation = ?', true)),
			'validators' 	=> 	array(),
			'multiple'			=>	true,
			'registerInArrayValidator' => false

		));

		$filters->addElement('button', 'filter', array(
			'label' 		=> 	'Filter',
			'validators' 	=> 	array(),
			'ignore'		=>	true
		));


$contact = new Form_SubForm($this);
$contact->setAttrib('title', 'Contact gegevens');
$this->addSubForm($contact, 'contact');

$contact->addElement('text', 'mail_chimp_contact_company', [
    'label' => 'Bedrijf',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);

$contact->addElement('text', 'mail_chimp_contact_address1', [
    'label' => 'Adres',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);

$contact->addElement('text', 'mail_chimp_contact_city', [
    'label' => 'Stad',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);

$contact->addElement('text', 'mail_chimp_contact_state', [
    'label' => 'Provincie',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);

$contact->addElement('text', 'mail_chimp_contact_zip', [
    'label' => 'Postcode',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);

$contact->addElement('text', 'mail_chimp_contact_country', [
    'label' => 'Land',
    'attribs' => [
        'title' => 'Deze informatie wordt bij iedere mail in de footer geplaatst',
        'class' => 'forceAutoHint'
    ]
]);


$campaignDefaults = new Form_SubForm($this);
$campaignDefaults->setAttrib('title', 'Standaard gegevens');
$this->addSubForm($campaignDefaults, 'campaign_defaults');

$campaignDefaults->addElement('text', 'mail_chimp_campaign_defaults_from_name', [
    'label' => 'Van Naam',
    'attribs' => [
        'title' => 'Naam van de afzender.',
        'class' => 'forceAutoHint'
    ]
]);

$campaignDefaults->addElement('text', 'mail_chimp_campaign_defaults_from_email', [
    'label' => 'Van Email',
    'attribs' => [
        'title' => 'Dit e-mailadres wordt gebruikt als afzender.',
        'class' => 'forceAutoHint'
    ]
]);

$campaignDefaults->addElement('text', 'mail_chimp_campaign_defaults_permission_reminder', [
    'label' => 'Herinnering aan toestemming',
    'attribs' => [
        'title' => 'Een herinnering naar de wijze dat de er toestemming is verleend om het e-mailadres te mogen gebruiken',
        'class' => 'forceAutoHint'
    ]
]);



	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Groep opslaan'
	));