<?

	//general form options
	$this->setAction('consumption/add/')
			->setMethod('post')
			->setAttrib('id', 'consumptionadd')
			->setAttrib('class', 'documentadd form');

	
	//date
	$this->addElement('text', 'date', array(
		'label' => 'Datum meterstand',
		'validators' => array(),
	));
	
	//value meter
	$this->addElement('text', 'value', array(
		'label' 		=> 	'Waarde meterstand',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));
			
		//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	)); 
