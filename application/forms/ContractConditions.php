<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$contractConditions = new Form_SubForm($this);
$contractConditions->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($contractConditions, 'contract_condition_form');

	$contractConditions->addElement('text', 'title', array(
		'label' => 'Naam',
		'required' => true,
	));

	$contractConditions->addElement('textarea', 'description', array(
		'label' => 'Bepaling',
		'required' => true,
		'attribs' => array(
			'rows' => 15,
			'cols' => 35,
		),
	));


//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Bepaling opslaan'
));

?>