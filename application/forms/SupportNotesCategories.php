<?
 //general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$SupportNotesCategories = new Form_SubForm($this);
$SupportNotesCategories->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($SupportNotesCategories, 'support_notes_categories_form');

$SupportNotesCategories->addElement('text', 'category', array(
		'label' => 'Naam',
		'required' => true,
	));


//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Categorie opslaan'
));



?>