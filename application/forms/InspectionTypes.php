<?php /** @noinspection PhpUnhandledExceptionInspection */

$model = new Inspection();
$inspectionTypeModel = new InspectionTypes();
$u_lib = new User();


$inspectionTypeModelRow = null;
if($this->getOwner()->id){
    $inspectionTypeModelRow = $inspectionTypeModel->getById($this->getOwner()->id);
}



$user_name = false;
if (0 < $this->getOwner()->view->data['user']) {
    $user_name = $u_lib->buildname(false, $this->getOwner()->view->data['user']);
}

//data form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');

$data = new Form_SubForm($this);

$data->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($data, 'data');


$data->addElement('text', 'description_nl', [
    'label' => 'Omschrijving NL',
]);

$data->addElement('text', 'description_en', [
    'label' => 'Omschrijving EN',
]);

$data->addElement('text', 'description_fr', [
    'label' => 'Omschrijving FR',
]);

$data->addElement('checkbox', 'signature_tenant', [
    'label' => 'Handtekening huurder',
    'value' => true,
    'attribs' => [
        'title' => 'Bij afronden handtekening huurder vereist.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]
]);


if($inspectionTypeModelRow->origin !== 'system'){

    $data->addElement('select', 'type', [
        'label' 		=> 	'Wat voor een type inspectie betreft het',
        'multioptions' 	=> [
            'custom' => 'Eigen type',
            'interim' => 'Tussentijdse inspectie',
            'first' => 'Begininspectie',
            'pre_end' => 'Voorinspectie',
            'end' => 'Eindinspectie',
        ],

        'validators' 	=> [],

    ]);
}



 $data->addElement('select', 'visible_for_user', [
    'label' 		=> 	'Standaard zichtbaar',
    'multioptions' 	=> $inspectionTypeModel->getVisibleTypes(),
    'validators' 	=> [],

]);
 $data->addElement('checkbox', 'meter_usage_on_report', [
    'label' => 'Meterstanden op rapport',
    'value' => true,
    'attribs' => [
        'title' => 'Bij het inspectierapport meterstanden meenemen.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]

]);


$data->addElement('checkbox', 'allow_copying_from_previous_inspection', [
    'label' => 'Gegevens kopiëren van vorige inspectie',
    'value' => true,
    'attribs' => [
        'title' => 'Bij een nieuwe inspectie de gegevens kopiëren van de voorgaande inspectie.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]
]);

$data->addElement('checkbox', 'hide_non_applicable_items', [
    'label' => 'Verberg niet van toepassing items op het inspectie-rapport',
    'value' => true,
    'attribs' => [
        'title' => 'Items die in de inspectie app op NVT zijn gekozen worden niet meer in het PDF rapport getoond.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]
]);


$data->addElement('checkbox', 'project_inspection', [
    'label' => 'Rapport koppelen aan project van object ipv huurder of object',
    'value' => true,
    'attribs' => [
        'title' => 'Met deze instelling wordt het rapport aan het project gekoppeld. (Alleen wanneer een inspectie op een leegstand object wordt gekoppeld.)',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]
]);


$data->addElement('hidden', 'user_id', [
    'value' => loginManager::data()->id,
]);


$data->addElement('hidden', 'origin', [
    'value' => 'user'
]);


$datatext = new Form_SubForm($this);
$datatext->setAttrib('title', 'Teksten inspectietype');
$this->addSubForm($datatext, 'datatext');

$datatext->addElement('textarea', 'intro_text_nl', [
    'label' => 'Intro Nederlands',
    'rows' => 5,
    'cols' => 80,
]);

$datatext->addElement('textarea', 'end_text_nl', [
    'label' => 'Eind Nederlands',
    'rows' => 5,
    'cols' => 80,
]);

$datatext->addElement('textarea', 'intro_text_en', [
    'label' => 'Intro Engels',
    'rows' => 5,
    'cols' => 80,
]);

$datatext->addElement('textarea', 'end_text_en', [
    'label' => 'Eind Engels',
    'rows' => 5,
    'cols' => 80,
]);


$datatext->addElement('textarea', 'intro_text_fr', [
    'label' => 'Intro Frans',
    'rows' => 5,
    'cols' => 80,
]);

$datatext->addElement('textarea', 'end_text_fr', [
    'label' => 'Eind Frans',
    'rows' => 5,
    'cols' => 80,
]);

//submit
$this->addElement('submit', 'aanmaken', [
    'label' => 'Inspectietype opslaan'
]);

?>
