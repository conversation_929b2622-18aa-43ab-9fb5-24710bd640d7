<?php

$this->setAction('tiara_distribution')
    ->setMethod('post')
    ->setAttrib('id', 'distribution');

$this->addElement(
    'submit',
    'aanmaken',
    [
        'label' => 'Submit!',
    ]
);

$dates = new Form_SubForm($this);
$dates->setAttrib('title', 'Data');

$multis = new Form_SubForm($this);
$multis->setAttrib('title', 'Multi-waarden');

$checkboxes = new Form_SubForm($this);
$checkboxes->setAttrib('title', 'Ja/nee waarden');

$this->addSubForm($dates, 'dates');
$this->addSubForm($multis, 'multis');
$this->addSubForm($checkboxes, 'checkboxes');

$multis->addElement(
    'multiselect',
    'entiteit',
    [
        'label' => 'Entiteittype',
        'multiOptions' => [
            'object' => 'Object',
            'objecttype' => 'Objecttype',
            'project' => 'Project',
        ],
    ]
);

$this->addElement(
    'text',
    'projectguid',
    [
        'label' => 'Project-GUID',
    ]
);

$this->addElement(
    'text',
    'objecttypeguid',
    [
        'label' => 'Objecttype-GUID',
    ]
);

$this->addElement(
    'select',
    'bouwperiode',
    [
        'label' => 'Bouwperiode',
        'multiOptions' => [
            '-1906' => '-1906',
            '1906-1930' => '1906-1930',
            '1931-1944' => '1931-1944',
            '1945-1959' => '1945-1959',
            '1960-1970' => '1960-1970',
            '1971-1980' => '1971-1980',
            '1981-1990' => '1981-1990',
            '1991-2000' => '1991-2000',
            '2001-2010' => '2001-2010',
            '2011-' => '2011-',
            'onbekend [MP]' => 'onbekend [MP]'
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'bouwvorm',
    [
        'label' => 'Bouwvorm',
        'multiOptions' => [
            'bestaande bouw' => 'Bestaande bouw',
            'n.v.t.' => 'Niet van toepassing',
            'nieuwbouw' => 'Nieuwbouw',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'objectsoort',
    [
        'label' => 'Objectsoort',
        'multiOptions' => [
            'appartement' => 'Appartement',
            'woonhuis' => 'Woonhuis',
            'bouwgrond' => 'Bouwgrond',
            'overig' => 'Overig',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'huidigestatus',
    [
        'label' => 'Huidige status',
        'multiOptions' => [
            'onder bod' => 'Onder bod',
            'onder optie' => 'Onder optie',
            'verhuurd onder voorbehoud' => 'Verhuurd onder voorbehoud',
            'verkocht onder voorbehoud' => 'Verkocht onder voorbehoud',
            'beschikbaar' => 'Beschikbaar',
            'verhuurd' => 'Verhuurd',
            'verkocht' => 'Verkocht',
            'ingetrokken' => 'Ingetrokken',
            'verkocht bij inschrijving' => 'Verkocht bij inschrijving',
            'geveild' => 'Geveild',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'woningtype',
    [
        'label' => 'Woningtype',
        'multiOptions' => [
            'vrijstaande woning' => 'Vrijstaande woning',
            'geschakelde woning' => 'Geschakelde woning',
            '2-onder-1-kapwoning' => '2-onder-1-kapwoning',
            'tussenwoning' => 'Tussenwoning',
            'hoekwoning' => 'Hoekwoning',
            'eindwoning' => 'Eindwoning',
            'halfvrijstaande woning' => 'Halfvrijstaande woning',
            'geschakelde 2-onder-1-kapwoning' => 'Geschakelde 2-onder-1-kapwoning',
            'verspringend' => 'Verspringend',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'overigog',
    [
        'label' => 'Overig OG',
        'multiOptions' => [
            'Parkeerplaats' => 'Parkeerplaats',
            'Woonwagenstandplaats' => 'Woonwagenstandplaats',
            'Parkeerkelder' => 'Parkeerkelder',
            'Ligplaats' => 'Ligplaats',
            'Berging' => 'Berging',
            'Onderstuk' => 'Onderstuk',
            'Stacaravanstandplaats' => 'Stacaravanstandplaats',
            'Opslagruimte' => 'Opslagruimte',
            'InpandigeGarage' => 'Inpandige garage',
            'Garage' => 'Garage',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'hoofdtuinpositie',
    [
        'label' => 'Hoofdtuinpositie',
        'multiOptions' => [
            'noord' => 'Noord',
            'noordoost' => 'Noordoost',
            'oost' => 'Oost',
            'zuidoost' => 'Zuidoost',
            'zuid' => 'Zuid',
            'zuidwest' => 'Zuidwest',
            'west' => 'West',
            'noordwest' => 'Noordwest',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'badkamervoorzieningen',
    [
        'label' => 'Hoofdtuinpositie',
        'multiOptions' => [
            'ligbad' => 'Ligbad',
            'zitbad' => 'Zitbad',
            'toilet' => 'Toilet',
            'douche' => 'Douche',
            'urinoir' => 'Urinoir',
            'bidet' => 'Bidet',
            'stoomcabine' => 'Stoomcabine',
            'jacuzzi' => 'Jacuzzi',
            'dubbele wastafel' => 'Dubbele wastafel',
            'wastafel' => 'Wastafel',
            'wastafelmeubel' => 'Wastafelmeubel',
            'inloopdouche' => 'Inloopdouche',
            'whirlpool' => 'Whirlpool',
            'wasmachineaansluiting' => 'Wasmachineaansluiting',
            'sauna' => 'Sauna',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'woningvoorzieningen',
    [
        'label' => 'Hoofdtuinpositie',
        'multiOptions' => [
            'mechanische ventilatie' => 'Mechanische ventilatie',
            'alarminstallatie' => 'Alarminstallatie',
            'rolluiken' => 'Rolluiken',
            'tv kabel' => 'TV Kabel',
            'buitenzonwering' => 'Buitenzonwering',
            'zwembad' => 'Zwembad',
            'lift' => 'Lift',
            'airconditioning' => 'Airconditioning',
            'windmolen' => 'Windmolen',
            'zonnecollectoren' => 'Zonnecollectoren',
            'satellietschotel' => 'Satellietschotel',
            'jacuzzi' => 'Jacuzzi',
            'stoomcabine' => 'Stoomcabine',
            'rookkanaal' => 'Rookkanaal',
            'schuifpui' => 'Schuifpui',
            'frans balkon' => 'Frans balkon',
            'dakraam' => 'Dakraam',
            'CCTV' => 'CCTV',
            'domotica' => 'Domotica',
            'zonnepanelen' => 'Zonnepanelen',
            'balansventilatie' => 'Balansventilatie',
            'natuurlijke ventilatie' => 'Natuurlijke ventilatie',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'garagesoorten',
    [
        'label' => 'Garagesoorten',
        'multiOptions' => [
            'geen garage' => 'Geen garage',
            'aangebouwd steen' => 'Aangebouwd steen',
            'aangebouwd hout' => 'Aangebouwd hout',
            'vrijstaand steen' => 'Vrijstaand steen',
            'vrijstaand hout' => 'Vrijstaand hout',
            'inpandig' => 'Inpandig',
            'garagebox' => 'Garagebox',
            'parkeerkelder' => 'Parkeerkelder',
            'garage mogelijk' => 'Garage mogelijk',
            'carport' => 'Carport',
            'parkeerplaats' => 'Parkeerplaats',
            'souterrain' => 'Souterrain',
            'garage met carport' => 'Garage met carport',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'liggingen',
    [
        'label' => 'Liggingen',
        'multiOptions' => [
            'aan bosrand' => 'Aan bosrand',
            'aan water' => 'Aan water',
            'aan drukke weg' => 'Aan drukke weg',
            'aan rustige weg' => 'Aan rustige weg',
            'in centrum' => 'In centrum',
            'in woonwijk' => 'In woonwijk',
            'vrij uitzicht' => 'Vrij uitzicht',
            'beschutte ligginge' => 'Beschutte ligging',
            'open ligging' => 'Open ligging',
            'buiten bebouwde kom' => 'Buiten bebouwde kom',
            'aan vaarwater' => 'Aan vaarwater',
            'landelijk gelegen' => 'Landelijk gelegen',
            'zeezicht' => 'Zeezicht',
            'bedrijventerrein' => 'Bedrijventerrein',
        ],
    ]
);

$multis->addElement(
    'multiselect',
    'bijzonderheden',
    [
        'label' => 'Bijzonderheden',
        'multiOptions' => [
            'monumentaal pand' => 'Monumentaal pand',
            'monument' => 'Monument',
            'gestoffeerd' => 'Gestoffeerd',
            'gedeeltelijk gestoffeerd' => 'Gedeeltelijk gestoffeerd',
            'gedeeltelijk verhuurd' => 'Gedeeltelijk verhuurd',
            'gemeubileerd' => 'Gemeubileerd',
            'dubbele bewoning mogelijk' => 'Dubbele bewoning mogelijk',
            'dobbele bewoning aanwezig' => 'Dobbele bewoning aanwezig',
            'beschermd stads- of dorpsgezicht' => 'Beschermd stads- of dorpsgezicht',
            'toegankelijk voor ouderen' => 'Toegankelijk voor ouderen',
            'toegankelijk voor minder validen' => 'Toegankelijk voor mindervaliden',
            'kluswoning' => 'Kluswoning',
            'sloopwoning' => 'Sloopwoning',
            'erfgoed' => 'Erfgoed',
        ],
    ]
);

$checkboxes->addElement('checkbox', 'isrecreatiewoning', ['label' => 'Recreatiewoning']);
$checkboxes->addElement('checkbox', 'haskelder', ['label' => 'Heeft kelder']);
$checkboxes->addElement('checkbox', 'ismonument', ['label' => 'Monument']);
$checkboxes->addElement('checkbox', 'isactief', ['label' => 'Actief']);
$checkboxes->addElement('checkbox', 'ishuur', ['label' => 'Ter huur']);
$checkboxes->addElement('checkbox', 'iskoop', ['label' => 'Te koop']);
$checkboxes->addElement('checkbox', 'isnieuwbouw', ['label' => 'Nieuwbouw']);
$checkboxes->addElement('checkbox', 'isvertrouwelijk', ['label' => 'Vertrouwelijk']);

$this->addElement('text', 'vestigingsnummers', ['label' => 'Vestigingsnummers']);
$this->addElement('text', 'woonplaats', ['label' => 'Woonplaats']);
$this->addElement('text', 'straatnaam', ['label' => 'Straatnaam']);

$this->addElement(
    'number',
    'huisnummer',
    [
        'label' => 'Huisnummer',
        'min' => '0',
        'validators' => [
            [
                'greaterThan',
                false,
                [0],
            ],
        ],
    ]
);

$this->addElement('text', 'huisnummertoevoeging', ['label' => 'Toevoeging']);
$this->addElement(
    'text',
    'postcode',
    [
        'label' => 'Postcode',
        'validators' => [
            [
                'postCode', false, [
                    'locale' => 'nl_NL',
                    'format' => '^[0-9]{4}\s[A-Z]{2}$',
                ],
            ],
        ],
    ]
);

$this->addElement('number', 'postcodestraal', ['label' => 'Postcode straal']);

$this->addElement(
    'text',
    'postcodevan',
    [
        'label' => 'Postcode van',
        'validators' => [
            [
                'postCode', false, [
                    'locale' => 'nl_NL',
                    'format' => '^[0-9]{4}\s[A-Z]{2}$',
                ],
            ],
        ],
    ]
);
$this->addElement(
    'text',
    'postcodetot',
    [
        'label' => 'Postcode tot',
        'validators' => [
            [
                'postCode', false, [
                    'locale' => 'nl_NL',
                    'format' => '^[0-9]{4}\s[A-Z]{2}$'],
            ],
            [
                'greaterThanField',
                false,
                ['postcodevan', 'Postcode van', true],
            ],
        ],
    ]
);

// The regex matches the following format:
// Latitude|Longitude|Radius
// Latitude and Longitude are doubles from -90 to 90 and -180 to 180 respectively.
// Radius is an integer between 1 and 20015.
$this->addElement(
    'text',
    'latlonstraal',
    [
        'label' => 'Latitude|Longitude|Straal',
        'validators' => [
            [
                'regex',
                false,
                ['/^[-]?([1-8]?\d(\.\d+)?|90(\.0+)?)\|[-]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)\|(200[0-1][0-5]|1?\d{1,4})$/m'],
            ],
        ],
    ]
);

$this->addElement('text', 'zoekterm', ['label' => 'Vrije zoekterm']);

$this->addElement(
    'select',
    'energieklassevan',
    [
        'label' => 'Energieklasse van',
        'multiOptions' => [
            '-',
            'G',
            'F',
            'E',
            'D',
            'C',
            'B',
            'A',
            'A+',
            'A++',
            'A+++',
            'A++++',
            'A+++++',
        ],
        'validators' => [
            [
                'lessThanField',
                false,
                ['energieklassetot', 'Energieklasse tot']
            ],
        ],
    ]
);

$this->addElement(
    'select',
    'energieklassetot',
    [
        'label' => 'Energieklasse tot',
        'multiOptions' => [
            '-',
            'G',
            'F',
            'E',
            'D',
            'C',
            'B',
            'A',
            'A+',
            'A++',
            'A+++',
            'A++++',
            'A+++++',
        ],
        'validators' => [
            [
                'greaterThanField',
                false,
                ['energieklassevan', 'Energieklasse van']
            ],
        ],
    ]
);

$formElementFactory = new \Common\Infrastructure\Form\FormElementFactory();

$this->addElement(
    'select',
    'land',
    [
        'label' => 'Land',
        'multiOptions' => $formElementFactory->getCountryOptions(),
    ]
);

$this->addElements($formElementFactory->buildPriceRange('huurprijsvan', 'huurprijstot', 'Huurprijs'));
$this->addElements($formElementFactory->buildPriceRange('koopprijsvan', 'koopprijstot', 'Koopprijs'));

$this->addElements($formElementFactory->buildNumberRange(
    'perceeloppervlaktevan',
    'perceeloppervlaktetot',
    'Perceeloppervlakte m3'
));

$this->addElements($formElementFactory->buildNumberRange(
    'woonoppervlaktevan',
    'woonoppervlaktetot',
    'Woonoppervlakte m3'
));
$this->addElements($formElementFactory->buildNumberRange('brutoinhoudvan', 'brutoinhoudtot', 'Bruto inhoud m3'));
$this->addElements($formElementFactory->buildNumberRange('aantalkamersvan', 'aantalkamerstot', 'Aantal kamers'));
$this->addElements($formElementFactory->buildNumberRange(
    'aantalslaapkamersvan',
    'aantalslaapkamerstot',
    'Aantal slaapkamers'
));
$this->addElements($formElementFactory->buildNumberRange('woonlaagvan', 'woonlaagtot', 'Woonlaag'));
$this->addElements($formElementFactory->buildNumberRange(
    'tuinoppervlaktevan',
    'tuinoppervlaktetot',
    'Tuinoppervlakte'
));
$this->addElements($formElementFactory->buildNumberRange(
    'aantalbadkamersvan',
    'aantalbadkamerstot',
    'Aantal badkamers'
));
$this->addElements($formElementFactory->buildNumberRange(
    'garagecapaciteitvan',
    'garagecapaciteittot',
    'Garagecapaciteit'
));

$dates->addElements($formElementFactory->buildDateRange('aanmelddatumvan', 'aanmelddatumtot', 'Aanmelddatum'));
$dates->addElements($formElementFactory->buildDateRange(
    'prijsgewijzigdvandatum',
    'prijsgewijzigdtotdatum',
    'Prijs Gewijzigd'
));
$dates->addElements($formElementFactory->buildDateRange('gewijzigdvandatum', 'gewijzigdtotdatum', 'Gewijzigd'));
$dates->addElements($formElementFactory->buildDateRange('transactiedatumvan', 'transactiedatumtot', 'Transactiedatum'));
