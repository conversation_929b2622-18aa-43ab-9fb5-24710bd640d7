<?

$contractProductId = isset($this->getOwner()->contractProductId) && intval($this->getOwner()->contractProductId) > 0?
	$this->getOwner()->contractProductId: null;

//general form options
$this->setAction('contract-product/edit/id/'.$contractProductId)
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$contractProductForm = new Form_SubForm($this);
$this->addSubForm($contractProductForm, 'type_form');

	$contractProductForm->addElement('text', 'name', [
		'label' => 'Naam',
		'required' => true,
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
