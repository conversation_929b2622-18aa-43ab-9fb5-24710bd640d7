<?
	//general form options
	$this->setMethod('post')
	 ->setAttrib('class', 'complaintadd form');
	
	//title
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Onderwerp:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	true
	));

    $companyId = loginManager::data()->info['company_id'];
    $companyModel = new \Company();
    $companyProjects = $companyModel->getProjects($companyId);

    foreach ($companyProjects as $project)
        $projects[$project['name']] = $project['name'];

    $this->addElement('select', 'project', [
        'label' => 'Project.',
        'required' 		=>	true,
        'multiOptions'	=> (array) $projects,
    ]);

    //description
    $this->addElement('textarea', 'description', array(
        'label' 		=>  (translate()->_('your_question')),
        'validators' 	=> 	array(),
        'rows'			=> 	8,
        'required' 		=>	true
    ));
	
	//name default invoeren
	$this->addElement('text', 'name', array(
		'label' 		=> 	(translate()->_('name')),
		'validators' 	=> 	array(),
        'value'         =>  loginManager::data()->info['rendered_name'],
		'size' 			=> 	60,
		'required' 		=>	true
	)); 

	//phone default invoeren
	$this->addElement('text', 'phone', array(
		'label' 		=> 	'Uw directe telefoonnummer:',
		'validators' 	=> 	array(),
		'size' 			=> 	60,
		'required' 		=>	false
	));

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => (translate()->_('send'))
	));
