<?

$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form')
;

//Invoice interval
/*$this->addElement(
	new Zend_Form_Element_Select('rate',
		array(
			'label' => 'Facturering per',
			'multioptions' => array(
				1 	=> 'Maand',
				3 	=> 'Kwartaal',
				12 	=> 'Jaar'
			),
			'disabled' => true,
			'ignore' => true
		)
	)
);*/

$this->addSubForm($user     = new Form_SubForm($this), 'user');
$this->addSubForm($invPrefs = new Form_SubForm($this), 'invoice_preferences');

//Payment type
$invPrefs->addElement(
	new Zend_Form_Element_Select('bill',
		array(
			'label' => 'Factuur ontvangen per',
			'multioptions' => array(
				'email' => 'E-mail',
				'mail' => 'Post'
			)
		)
	)
);

// banktype
$user->addElement('select', 'banktype', array(
	'label' 		=> 	'Bank/giro',
	'multioptions' 	=> array(
							'normal' => 'Bank',
							'giro' => 'Giro'
						),
	'validators' 	=> 	array()
));

// bankaccount
$user->addElement('text', 'bankaccount', array(
	'label' 		=> 	'Rekeningnummer',
	'validators' 	=> 	array(array('validator' => 'elfProef'))
		
));

// BIC
$user->addElement('text', 'bic', array(
	'label' 		=> 	'BIC',
	'validators' 	=> 	array(
			array(
				/*'validator' => 'elfProef'*/
			)
		),
		
));

// IBAN
$user->addElement('text', 'iban', array(
	'label' 		=> 	'IBAN',
	'validators' 	=> 	array(
			array(
				/*'validator' => 'elfProef'*/
				
				)
		),
		'required' => true
		
));



//Payment type
$invPrefs->addElement(
	new Zend_Form_Element_Select('type',
		array(
			'label' => 'Type betaling',
			'multioptions' => array(
				'collection' => 'Automatische incasso',
				'ideal' => 'Handmatig overmaken'
			)
		)
	)
);

$this->addElement('submit', 'profile', array(
	'label' => "Opslaan",
));

?>
