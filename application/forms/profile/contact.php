<?

$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form');

$this->addElement('select', 'userselect', array(
	'label' 		=> 	'Contactpersoon',
	'multioptions' 	=> array(
		'contractee' => 'Contractant',
		'custom'     => 'Aangepast',
	),
	'validators' 	=> 	array(),
));

$this->addElement('select', 'gender', array(
	'label' 		=> 	'Geslacht',
	'multioptions' 	=> array(
							'male' => 'Man',
							'female' => 'Vrouw',
						),
	'validators' 	=> 	array(),
));

$this->addElement('text', 'initials', array(
	'label' 		=> 	'Initialen',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(0, 100),
		),
	),
));

$this->addElement('text', 'firstname', array(
	'label' => 'Voornaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100),
		)
	),
));

$this->addElement('text', 'middlename', array(
	'label' => 'Tussenvoegsel',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 20),
		)
	),
));

$this->addElement('text', 'name', array(
	'label' => 'Achternaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100),
		)
	),
));

$this->addElement('text', 'phone', array(
	'label' => 'Telefoon',
	'validators' => array(
		array(
			'validator' => 'Digits'
		),
		array(
			'validator' => 'stringLength',
			'options'   => array(0, 13),
		)
	),
));

$this->addElement('text', 'email', array(
	'label' => 'E-mail',
	'validators' => array('emailAddress'),
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));
?>
