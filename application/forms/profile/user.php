<?
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form')
;
	
$this->addElement('text', 'username', array(
	'label' => "Gebruikersnaam",
	'validators' => array(),
	'disabled' => true,
	'ignore' => true,
));

//password
$this->addElement('password', 'password', array(
	'label' => "Wachtwoord",
	'renderPassword' => true,
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(6, 100),
		),
	),
));

$this->addElement('password', 'password_check', array(
	'label' => "Wachtwoord (nogmaals)",
	'renderPassword' => true,
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(6, 100),
		),
		array('identical', false, array('token' => 'password')),
	),
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));
		
?>
