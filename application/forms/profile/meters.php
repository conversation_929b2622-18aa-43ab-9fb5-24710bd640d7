<?

$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

$this->addElement('text', 'date', array(
	'label' => 'Opleverdatum',
	'validators' => array('delivery'),
	'required' => true
));	

//Date
$this->addElement('text', 'enddate', array(
	'label' => 'Einddatum',
));	

//Meter
$this->addElement('text', 'meter', array(
	'label' => 'Meterstand(en)',
	'validators' => array()
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));

?>
