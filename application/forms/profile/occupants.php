<?

$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

$this->addElement(
	$status = new Zend_Form_Element_Select('marital', array(
		'label' => 'Burgelijke staat',
		'multioptions' => array(
			'-' => '-',
			'married' => 'Gehuwd',
			'together' => 'Samenwonend',
			'single' => 'Alleenstaand'
		)
	))
);

// The number of adults
$this->addElement('text', 'adults', array(
	'label' => 'Aantal volwassenen',
	'validators' => array(
		array(
			'validator' => 'Digits'
		),
		array(
			'validator' => 'stringLength',
			'options'   => array(1, 10)
		)
	)
));

// The number of kids
$this->addElement('text', 'kids', array(
	'label' => 'Aantal kinderen',
	'validators' => array(
		array(
			'validator' => 'Digits'
		),
		array(
			'validator' => 'stringLength',
			'options'   => array(1, 10)
		)
	)
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));
		
?>
