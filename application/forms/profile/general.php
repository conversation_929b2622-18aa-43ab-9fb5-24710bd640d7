<?

$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form')
;

$this->addElement(new Zend_Form_Element_Select('maritalstatus', array(
	'label' => 'Burgelijke staat',
	'multioptions' => array(
		'-' => '-',
		'married' => 'Gehuwd',
		'together' => 'Samenwonend',
		'single' => 'Alleenstaand',
	),
)));

$this->addElement('select', 'gender', array(
	'label' 		=> 	'Geslacht',
	'multioptions' 	=> array(
							'male' => 'Man',
							'female' => 'Vrouw'
						),
	'validators' 	=> 	array(),
));

$this->addElement('text', 'initials', array(
	'label' 		=> 	'Initialen',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(0, 100)
		)
	)
));

$this->addElement('text', 'firstname', array(
	'label' => 'Voornaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100)
		)
	),
	'required' => true
));

$this->addElement('text', 'middlename', array(
	'label' => 'Tussenvoegsel',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 20)
		)
	)
));

$this->addElement('text', 'name', array(
	'label' => 'Achternaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100)
		)
	),
	'required' => true
));

$phoneHome = $this->addElement('text', 'phone_home', array(
	'label' => 'Telefoon (vast)',
	'validators' => array(
		/*array(
			'validator' => 'digits'
		),*/
		array(
			'validator' => 'stringLength',
			'options'   => array(10, 13)
		),
	),
	'filters' => array(
		new Zend_Filter_Digits,
	),
	'required' => true
));

$phoneCell = $this->addElement('text', 'phone_home', array(
	'label' => 'Telefoon (mobiel)',
	'validators' => array(
		/*array(
			'validator' => 'Digits'
		),*/
		array(
			'validator' => 'stringLength',
			'options'   => array(0, 13)
		)
	),
	'filters' => array(
		new Zend_Filter_Digits,
	),
	'required' => true
));

$this->addElement('text', 'bdate', array(
	'label' => 'Geboortedatum',
	'validators' => array(
	)
));

$this->addElement('text', 'email', array(
	'label' => 'E-mail',
	'validators' => array('emailAddress'),
	'required' 		=>	true
));

$this->addElement('text', 'emailcheck', array(
	'label' => 'E-mail (nogmaals)',
	
	'validators' => array(
		array(
			'emailAddress'
		),

		 array('identical', false, array('token' => 'email'))
	),
	'required' 		=>	true
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));

?>
