<?

$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form')
;

$object_address = new Form_SubForm($this);
	$this->addSubForm($object_address, 'object_address');

		//address_id
		/*$object_address->addElement('hidden', 'id', array(
			'validators' => array(
				array()
			)
		));*/

		//address
		$object_address->addElement('text', 'address', array(
			'label' => 'Straatnaam',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//number
		$object_address->addElement('text', 'number', array(
			'label' => 'Huisnummer',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//zipcode
		$object_address->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//City
		$object_address->addElement('text', 'city', array(
			'label' => 'Plaats',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));
		
$user_address = new Form_SubForm($this);
	$this->addSubForm($user_address, 'user_address');


		// banktype
		$user_address->addElement('select', 'invoice', array(
			'label' 		=> 	'Ik wil communicatie ontvangen',
			'multioptions' 	=> array(
									'object' => 'Op bovenstaand adres',
									'user' => 'Op een ander adres'
								),
			'validators' 	=> 	array(),
		));
		
		//address
		$user_address->addElement('text', 'address', array(
			'label' => 'Straatnaam',
			'validators' => array(
				array(
				)
			)
		));

		//number
		$user_address->addElement('text', 'number', array(
			'label' => 'Huisnummer',
			'validators' => array(  )
		));

		//zipcode
		$user_address->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => array(
				array(
					'regex',
					false,
					'options' => array(
						'pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/'
					)
				)
			)
		));

		//City
		$user_address->addElement('text', 'city', array(
			'label' => 'Plaats',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			)

		));


//submit
$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));

?>
