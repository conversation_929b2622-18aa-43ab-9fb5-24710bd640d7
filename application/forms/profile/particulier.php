<? #TODO waarom is alles disabled? maak er dan een <span> van ofzo...

	$user = new User();
	$language = $user->getLanguage(loginManager::data()->id, loginManager::data()->info['object']);	

	//name form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'profile')
			->setAttrib('class', 'profile form');

	/**
	 * login data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//username
		$user->addElement('text', 'username', array(
			'label' => translate()->_('username'),
			'validators' => array(),
			'disabled' => true, #TODO dit in rechten?
			'ignore' => true
		));

		//password
		$user->addElement('password', 'password', array(
			'label' => translate()->_('password'),
			'renderPassword' => true,
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(3, 100)
				)
			)
		));

	/**
	 * name data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//gender
		$general->addElement('select', 'gender', array(
			'label' 		=> 	translate()->_('gender'),
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$general->addElement('text', 'initials', array(
			'label' 		=> 	translate()->_('initials'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			),
			'filters'  => array(
				'DotsBetweenInitials'
			)
		));


			//firstname
		$general->addElement('text', 'firstname', array(
			'label' => translate()->_('firstname'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));

		//middlename
		$general->addElement('text', 'middlename', array(
			'label' => translate()->_('middlename'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			)
		));

		//name
		$general->addElement('text', 'name', array(
			'label' => translate()->_('surname'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			),
			'required' => true
		));


		//Phone home
		$general->addElement('text', 'phone_home', array(
			'label' => translate()->_('mobile'),
			'validators' => array(
				array(
					'validator' => 'digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(10, 13)
				),
			),
			'required' => true
		));



		//Birth date
		$general->addElement('text', 'bdate', array(
			'label' => translate()->_('birth'),
			'validators' => array(
			)
		));

		//email
		$general->addElement('text', 'email', array(
			'label' => 'E-mail',
			'validators' => array('emailAddress'),
			'required' 		=>	true
		));
		
		//email voor check
		$general->addElement('text', 'emailcheck', array(
			'label' => translate()->_('checkemail'),
			
			'validators' => array(
				array(
					'emailAddress'
				),

				 array('identical', false, array('token' => 'email'))
			),
			'required' 		=>	true
		));
	
		

	/**
	 * address data
	 */
	$user_address = new Form_SubForm($this);
	$this->addSubForm($user_address, 'user_address');


		// banktype
		$user_address->addElement('select', 'invoice', array(
			'label' 		=> 	'Ik wil communicatie ontvangen',
			'multioptions' 	=> array(
									'object' => 'Op bovenstaand adres',
									'user' => 'Op een ander adres'
								),
			'validators' 	=> 	array(),
		));
		
		//address
		$user_address->addElement('text', 'address', array(
			'label' => translate()->_('address'),
			'validators' => array(
				array(
				)
			)
		));

		//number
		$user_address->addElement('text', 'number', array(
			'label' => translate()->_('number'),
			'validators' => array(  )
		));

		$zipcode_validate = $language == 'nl' ? array(array('regex', false, 'options' => array('pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/'))) : array();

		//zipcode
		$user_address->addElement('text', 'zipcode', array(
			'label' => translate()->_('postal'),
			'validators' => $zipcode_validate
		));

		//City
		$user_address->addElement('text', 'city', array(
			'label' => translate()->_('city'),
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			)

		));


	/**
	 * object address data
	 */
	$object_address = new Form_SubForm($this);
	$this->addSubForm($object_address, 'object_address');

		//address_id
		$object_address->addElement('hidden', 'id', array(
			'validators' => array(
				array()
			)
		));

		//address
		$object_address->addElement('text', 'address', array(
			'label' => translate()->_('address'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//number
		$object_address->addElement('text', 'number', array(
			'label' => translate()->_('number'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//zipcode
		$object_address->addElement('text', 'zipcode', array(
			'label' => translate()->_('postal'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		//City
		$object_address->addElement('text', 'city', array(
			'label' => translate()->_('city'),
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));
		
		


	/**
	 * object start data
	 */
	$start = new Form_SubForm($this);
	$this->addSubForm($start, 'start');	
		
		//Date
		$start->addElement('text', 'date', array(
			'label' => translate()->_('startdate'),
			'validators' => array('delivery'),
			'required' 		=>	true
		));	
		
		//Date
		$start->addElement('text', 'enddate', array(
			'label' => translate()->_('enddate'),
		));	
		
		//Meter
		$start->addElement('text', 'meter', array(
			'label' => 'Meterstand(en)',
			'validators' => array()
		));

	/**
	 * invoice data
	 */
	$invoice = new Form_SubForm($this);
	$this->addSubForm($invoice, 'invoice');


		//Invoice interval
		$invoice->addElement(
			new Zend_Form_Element_Select('rate',
				array(
					'label' => 'Facturering per',
					'multioptions' => UserInvoicePreferences::getInvoicePeriodOptions(),
					'disabled' => true,
					'ignore' => true
				)
			)
		);

		//Payment type
		
		if (Settings::get('software_type') == "energy") { 
			$invoice->addElement(
				new Zend_Form_Element_Select('type',
					array(
						'label' => translate()->_('payment_type'),
						'multioptions' => array(
							'collection' => translate()->_('collection'),
							'ideal' => translate()->_('ideal')
						)
					)
				)
			);
		} elseif (!Settings::get('modules_tenantLogin_hide_payment_type')) {
			$invoice->addElement(
				new Zend_Form_Element_Select('type',
					array(
						'label' =>  translate()->_('payment_type'),
						'multioptions' => array(
							'ideal' =>  translate()->_('ideal')
						)
					)
				)
			);
			
		}
	if ($language == "nl" ) {
		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('bill',
				array(
					'label' => translate()->_('invoice_methode'),
					'multioptions' => array(
						'email' => 'E-mail',
						'mail' => 'Post'
					)
				)
			)
		);
	} else {
		//Payment type
		$invoice->addElement(
			new Zend_Form_Element_Select('bill',
				array(
					'label' => translate()->_('invoice_methode'),
					'multioptions' => array(
						'email' => 'E-mail'
					)
				)
			)
		);	
			
	}

		
		if ($language == "nl" ) {
			// bankaccount
			$invoice->addElement('text', 'bankaccount', array(
				'label' 		=> 	translate()->_('bankaccount'),
				'validators' 	=> 	array(array('validator' => 'elfProef'))
					
			));
		} else {
			// bankaccount
			$invoice->addElement('text', 'bankaccount', array(
				'label' 		=> 	translate()->_('bankaccount')
					
			));
		}
		
		
		$invoice->addElement('text', 'bankaccountname', array(
			'label' 		=> 	'Tenaamstelling'				
		));
		
		
		if ($language != "en") {
		
			// BIC
			$invoice->addElement('text', 'bic', array(
				'label' 		=> 	'BIC',
				'validators' 	=> 	array(
						array(
							/*'validator' => 'elfProef'*/
						)
					)
					
			));
			
			// IBAN
			$invoice->addElement('text', 'iban', array(
				'label' 		=> 	'IBAN',
				'validators' 	=> 	array(
						array(
							/*'validator' => 'elfProef'*/
							
							)
					)
					
			));
		
		}

		if (Settings::get('software_type') == "energy") { 
			// banktype
			$invoice->addElement('select', 'banktype', array(
				'label' 		=> 	'Bank/giro',
				'multioptions' 	=> array(
										'normal' => 'Bank',
										'giro' => 'Giro'
									),
				'validators' 	=> 	array()
			));
		
		} elseif(!Settings::get('modules_tenantLogin_hide_payment_type')) {
			
				// banktype
			$invoice->addElement('select', 'banktype', array(
				'label' 		=> 	'Bank/giro',
				'multioptions' 	=> array(
										'normal' => 'Bank'
									),
				'validators' 	=> 	array()
			));
			
		}

	/**
	 * tenant info
	 */
	$tenantinfo = new Form_SubForm($this);
	$this->addSubForm($tenantinfo, 'tenantinfo');


		// The marital status of the tenant
		$tenantinfo->addElement(
			$status = new Zend_Form_Element_Select('maritalstatus', array(
				'label' => 'Burgelijke staat',
				'multioptions' => array(
					'-' => '-',
					'married' => 'Gehuwd',
					'together' => 'Samenwonend',
					'single' => 'Alleenstaand'
				)
			))
		);

		// The number of adults
		$tenantinfo->addElement('text', 'adults', array(
			'label' => translate()->_('adults'),
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(1, 10)
				)
			)
		));

		// The number of kids
		$tenantinfo->addElement('text', 'kids', array(
			'label' => 'Aantal kinderen',
			'validators' => array(
				array(
					'validator' => 'Digits'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(1, 10)
				)
			)
		));

	//submit
	$this->addElement('submit', 'profile', array(
		'label' => translate()->_('save'),
	));

?>