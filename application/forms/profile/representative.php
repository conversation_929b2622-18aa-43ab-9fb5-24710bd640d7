<?

$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'profile')
	->setAttrib('class', 'profile form');

$this->addElement('select', 'gender', array(
	'label' 		=> 	'Geslacht',
	'multioptions' 	=> array(
							'male' => 'Man',
							'female' => 'Vrouw'
						),
	'validators' 	=> 	array(),
	'required' 		=>	true
));

$this->addElement('text', 'initials', array(
	'label' 		=> 	'Initialen',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(0, 100)
		)
	),
	'required' 		=>	true
));

$this->addElement('text', 'firstname', array(
	'label' => 'Voornaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100)
		)
	)
));

$this->addElement('text', 'middlename', array(
	'label' => 'Tussenvoegsel',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 20)
		)
	),
));

$this->addElement('text', 'name', array(
	'label' => 'Achternaam',
	'validators' => array(
		array(
			'validator' => 'stringLength',
			'options'   => array(2, 100)
		)
	),
	'required' 		=>	true
));

$this->addElement('submit', 'profile', array(
	'label' => "Verder",
));

?>
