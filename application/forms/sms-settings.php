<?php

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'smssettings')
			->setAttrib('class', 'smssettings form');

	$prefs = new Form_SubForm($this);
	$prefs->setAttrib('title', 'Voorkeuren');
	$this->addSubForm($prefs, 'prefs');
	
		$prefs->addElement('text', 'sms_originator', array(
			'label' => 'Afzender',
			'value' => substr(preg_replace('/[^a-zA-Z\s]/', '', Settings::get('general_company')), 0, 11),
			'attribs' => array(
				'title' => 'Maximaal 11 letters, zonder spaties (technische beperking SMS)',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -7, 'y': 0}",
				'maxlength' => 11
			),
			'validators' => array(
				array(
					'validator' => 'Alpha'
				),
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 11)
				)
			)
		));

	$login = new Form_SubForm($this);
	$login->setAttrib('title', 'MessageBird gegevens');
	$this->addSubForm($login, 'login');
	
		$login->addElement('hidden', 'sms_enabled', array(
			'value' => '1',
		));

		$login->addElement('text', 'sms_access_key', array(
			'label' => 'Access key',
		));

			//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Opslaan'
	));

