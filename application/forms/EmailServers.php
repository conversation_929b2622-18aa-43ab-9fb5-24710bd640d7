<?php

$this->setAction('email/server-edit/' . ($this->getOwner()->id ? 'id/' . $this->getOwner()->id . '/' : ''))
    ->setMethod('post')
    ->setAttrib('id', 'emailServer')
    ->setAttrib('class', 'emailServer form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');

$general->addElement('text', 'host', [
    'label' => 'Hostnaam',
    'required' => true,
    'attribs' => [
        'title' => 'Het adres waarop de E-mail server te bereiken is, bijvoorbeeld: imap.gmail.com',
        'class' => 'forceAutoHint',
        'placeholder' => 'imap.gmail.com',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);

$general->addElement('select', 'ssl', [
    'label' => 'Beveiliging',
    'multiOptions' => ['0' => 'Onbeveiligd', 'SSL' => 'SSL', 'TLS' => 'TLS'],
    'value' => 'SSL',
    'attribs' => [
        'title' => 'Een beveiligde verbinding maken met de server',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);

$office365 = new Form_SubForm($this);
$office365->setAttrib('title', 'Office365 IMAP OAuth aanvullende gegevens');
$this->addSubForm($office365, 'office365');

$office365->addElement('text', 'office365_tenant_id', [
    'label' => 'Tenant ID',
    'attribs' => [
        'title' => 'Het tenant id, te vinden in Azure active directory -> App registrations -> App',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);

$office365->addElement('text', 'office365_client_id', [
    'label' => 'Client ID (App ID)',
    'attribs' => [
        'title' => 'Het client id, te vinden in Azure active directory -> App registrations -> App. Dit kan ook App ID heten',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);

$office365->addElement('text', 'office365_client_secret', [
    'label' => 'Client secret (value)',
    'attribs' => [
        'title' => 'Het client secret, dit werd bij het aanmaken eenmalig door Microsoft getoond. Het gaat hier om de waarde (value) van het secret en niet om het ID van de secret.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);

$office365->addElement('date', 'office365_client_secret_expire_date', [
    'label' => 'Client secret Expiratiedatum',
    'attribs' => [
        'title' => 'Vanuit Microsoft\'s veiligheidreden, is uw client secret maar een beperkte tijd houdbaar. Wanneer deze verlopen is, zal uw connectie niet meer werken. Door hier de houdbaarheidsdatum op te geven, ontvangt u enige tijd voor het verlopen hiervan een notificatie mail.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -25, 'y': 0}"
    ]
]);
