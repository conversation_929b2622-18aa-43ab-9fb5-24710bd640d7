<?php

use FormModels\Transaction\CollectionBundle;

$transactionCollectionIds = $this->getOwner()->getParam('ids');
$collectionBundle = new CollectionBundle($transactionCollectionIds);

//general form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('id', 'collection-bundle')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);

$element = new Zend_Form_Element_Text("process_date");
$element->setLabel("Verwerkingsdatum");

// set the validators
$correctedProcessDate = $collectionBundle->getMinProcessDate()->sub(new DateInterval('P1D'));
$element->setValidators([
    new Zend_Validate_DateAfter(["min" => $correctedProcessDate->format('d-m-Y')])
]);
$element->setRequired();
$element->setErrorMessages([$collectionBundle->getErrorMessage()]);
$element->setAttribs([
        'title' => 'De verwerkings datum',
        'class' => 'forceAutoHint DatePicker',
        'hintOffset' => "{'x': -0, 'y': 0}"
    ]);
// add the element to the form
$this->addElement($element);

$this->addElement('submit', 'Samenvoegen', []);