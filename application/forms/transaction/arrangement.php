<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'arrangement')
			->setAttrib('class', 'arrangement form');

	/**
	 * General arrangement data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//period
		$general->addElement('text', 'period', array(
			'label' => 'Aantal maanden',
			'validators' => array(
			),
		));

		//monthly
		$general->addElement('text', 'monthly', array(
			'label' => 'Maandelijks',
			'validators' => array(),
		));

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => 'Opslaan'
	));