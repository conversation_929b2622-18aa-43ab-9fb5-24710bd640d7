<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'transactionedit')
			->setAttrib('class', 'transactionedit form');

	/**
	 * General arrangement data
	 */
	$general = new Form_SubForm($this);

		//monthly
		$this->addElement('numberInput', 'payed', array(
			'label' => 'Betaald',
			'validators' => array(),
		));

		$this->addElement('text', 'payed_date', array(
			'label' => 'Betaaldatum',
			'validators' => array(),
			'value' => date('d-m-Y'),
			'attribs' => array(
				'title' => 'De datum van de aanpassing van het betaalde bedrag',
				'class' => 'forceAutoHint DatePicker',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));


		$this->addElement('numberInput', 'penalty_payed', array(
			'label' => 'Boete betaald',
			'validators' => array(),
			'attribs' => array(
				'steps' => 'any',
				'min' => 10
			)
		));

		$this->addElement('select', 'penalty_method', array(
			'label' => 'Reden',
			'validators' => array(),
			'multioptions' 	=> array(
				'penalty_payment' => 'Betaling',
				'penalty_absolve' => 'Kwijtschelden'
			),
		));

		$this->addElement('textarea', 'penalty_description', array(
			'label' => 'Toelichting',
			'validators' => array(),
			'rows' => 5,
			'cols' => 80,
		));

		$this->addElement('text', 'penalty_payed_date', array(
			'label' => 'Betaaldatum',
			'validators' => array(),
			'value' => date('d-m-Y'),
			'attribs' => array(
				'title' => 'De datum van de aanpassing van het betaalde bedrag',
				'class' => 'forceAutoHint DatePicker',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));


		$params = ['label' => 'Opslaan'];
		$this->addElement('submit', 'submitbutton', $params);
		
		

	
