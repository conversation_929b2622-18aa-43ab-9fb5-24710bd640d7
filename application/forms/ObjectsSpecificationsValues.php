<?

//general form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'object')
	->setAttrib('autocomplete', 'off')
	->setAttrib('class', 'object form');

$specification_options = db()->fetchAll(db()->select()->from('objects_specifications_values_options', ['type_id', 'value', 'id']));

/* for generation validation rules for _type table
echo json_encode([
	[
		'validator' => 'digits'
	],
	[
		'validator' => 'stringLength',
		'options'   => array(10, 13)
	]
]);
*/
global $language;

foreach($this->getOwner()->view->specifications as $spec_group_id => $spec_group){
	$spec_group_subform = new Form_SubForm($this, ['tab' => $spec_group['tab_name']]);
	$spec_group_subform->setAttrib('title', $spec_group['group_title']);
	$this->addSubForm($spec_group_subform, 'object_specifications_' . $spec_group_id);

	foreach($spec_group['types'] as $type_id => $type) {

		$classes = [];

		$specification_type_options = [-1 => '-'];
		if($type['is_bool']){
			$specification_type_options = [
					'0' => $language === 'en' ? 'No' : 'Nee',
					'1' => $language === 'en' ? 'Yes' : 'Ja'
			];
		} else {
			foreach($specification_options as $specification_option)
				if($specification_option['type_id'] == $type['type_id'])
					$specification_type_options[$specification_option['id']] = $specification_option['value'];
		}

		if($type['general'])
			$classes[] = 'general';

		foreach(['website', 'funda', 'pararius', 'vidii'] as $for_type)
		if($type['for_' . $for_type])
			$classes[] = 'for_' . $for_type;

		if($type['input_type'] == 'select') {
			$spec_group_subform->addElement('select', $type['type_name'], array(
				'label' => $type['type_title'],
				'tab' => $spec_group['tab_name'],
				'multioptions' => $specification_type_options,
				'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
				'class' => implode(' ', $classes)
			));
        } elseif($type['input_type'] == 'multiselect'){
            $spec_group_subform->addElement('select', $type['type_name'], [
                'label' => $type['type_title'],
                'tab' => $spec_group['tab_name'],
                'multiple' => 'multiple',
                'multioptions' => $specification_type_options,
                'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
                'class' => implode(' ', $classes),
                'registerInArrayValidator' => false
            ]);
		} elseif($type['input_type'] == 'textarea'){
			$spec_group_subform->addElement('textarea', $type['type_name'], array(
				'label' => $type['type_title'],
				'tab' => $spec_group['tab_name'],
				'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
				'class' => implode(' ', $classes),
				'expandable' => true
			) );
		} elseif($type['input_type'] == 'time') {
			$spec_group_subform->addElement('time', $type['type_name'], array(
				'label' => $type['type_title'],
				'tab' => $spec_group['tab_name'],
				'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
				'placeholder' => 'Onbekend',
				'class' => implode(' ', $classes)
			));
		} elseif($type['input_type'] == 'date') {
			$spec_group_subform->addElement('date', $type['type_name'], array(
				'label' => $type['type_title'],
				'tab' => $spec_group['tab_name'],
				'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
				'placeholder' => 'Onbekend',
				'class' => implode(' ', $classes)
			));
		} else {
			$spec_group_subform->addElement('text', $type['type_name'], array(
				'label' => $type['type_title'],
				'tab' => $spec_group['tab_name'],
				'validators' => $type['validation'] ? json_decode($type['validation'], true) : [],
				'placeholder' => 'Onbekend',
				'class' => implode(' ', $classes)
			));
		}
	}
}
