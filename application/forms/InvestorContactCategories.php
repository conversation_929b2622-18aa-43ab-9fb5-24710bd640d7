<?php

$this->setAction('')
		->setMethod('post')
		->setAttrib('class', 'form');


$categories = new Form_SubForm($this);
$categories->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($categories, 'categories_form');

    $categories->addElement('text', 'name_nl', [
        'label' => 'Omschrijving (nl)',
        'required' => true,
    ]);

    $categories->addElement('text', 'name_en', [
        'label' => 'Omschrijving (en)',
        'required' => true,
    ]);

if(Settings::get('software_french_enabled')) {
    $categories->addElement('text', 'name_fr', [
        'label' => 'Omschrijving (fr)',
        'required' => true,
    ]);

}

    $email_accounts_select = function(){
        return db()->select()->from('email_accounts', array('id',  'CONCAT_WS(" - ", name, address)'))->order('name');
    };

    $email_accounts =  db()->fetchPairs($email_accounts_select());
    $email_accounts[0] = 'Standaard (gelijk aan algemene instelling)';
    ksort($email_accounts);


    $categories->addElement('select', 'receive_mailaccount', array(
        'label' => 'E-mail account voor ontvangst melding',
        'multioptions' 	=> $email_accounts,
    ));



$this->addElement('submit', 'aanmaken', [
	'label' => 'Categorie opslaan'
]);
?>