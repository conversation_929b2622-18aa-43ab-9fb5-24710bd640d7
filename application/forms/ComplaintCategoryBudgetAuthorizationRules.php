<?php



$this->setAction('')
    ->setAttrib('id', 'complaint_category_budget_authorization_rules')
    ->setMethod('post')
    ->setAttrib('class', 'form');


$main = new Form_SubForm($this);
$main->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($main, 'main');

$main->addElement('numberInput', 'from_budget', [
    'label' => 'Vanaf bedrag',
    'validators' => [],
]);

$departmentRows = $this->getOwner()->getFormSelectDepartments();
$main->addElement('select', 'department_id', [
    'label' => 'Afdeling',
    'multiOptions' => $departmentRows
]);

$aclRoleRows = $this->getOwner()->getFormSelectAclRoles();

$aclRoleRows =
    [
//        'internal' => '1. Stagiaire',
//        'employee' => '2. Werknemer',
//        'manager' => '3. Manager',
//        'cfo' => '4. Directie',
//        'admininternal' => '5. Administrator',
        '5' => '1. Stagiaire',
        '6' => '2. Werknemer',
        '7' => '3. Manager',
        '8' => '4. Directie',
        '9' => '5. Administrator',
    ];

$main->addElement('select', 'acl_role_id', [
    'label' => 'Rechten',
    'multiOptions' => $aclRoleRows
]);

$categoryid = $this->getOwner()->getCategoryId();
$main->addElement('hidden', 'support_complaints_category_id', [
    'value' => $categoryid
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);
