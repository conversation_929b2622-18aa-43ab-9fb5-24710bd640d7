<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'remindsStatus')
			->setAttrib('class', 'remindsStatus form');

	$status = new Form_SubForm($this);
	$status->setAttrib('title', 'Status aanpassen');
	$this->addSubForm($status, 'status');

	$ir = new InvoiceReminds();
	$fines = array(0 => '-');

	foreach($ir->fines as $fine_id => $item)
		$fines[$fine_id] = $item['title'];

	$fines['Speciale status'][99 . '_curator'] = 'Curator';
	$fines['Speciale status'][99 . '_unfindable'] = 'Onvindbaar';

	$status->addElement('select', 'status', array(
		'label' => 'Te verzenden:',
		'required' => true,
		'value' => date('d-m-Y'),
		'multiOptions' => $fines,
		'attribs' => array(
			'title' => 'Pas de eerst volgende te verzenden herinnering aan voor deze factuur',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -5, 'y': -5}"
		)
	));

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Status aanpassen'
	));

?>