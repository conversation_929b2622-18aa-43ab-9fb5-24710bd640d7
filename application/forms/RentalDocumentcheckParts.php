<?


    //general form options
    $this->setAction('')
        ->setMethod('post')
        ->setAttrib('enctype', 'multipart/form-data')
        ->setAttrib('class', 'form');


    $RentalDocumentcheckParts = new Form_SubForm($this);
    $RentalDocumentcheckParts->setAttrib('title', 'Algemene gegevens');
    $this->addSubForm($RentalDocumentcheckParts, 'rental_documentcheck_parts_form');

    $RentalDocumentcheckParts->addElement('text', 'name', array(
        'label' => 'Naam',
        'required' => true,
    ));

    $RentalDocumentcheckParts->addElement('select', 'type', array(
        'label' => 'type',
        'multioptions' 	=> [
            'general' => 'Algemeen altijd aan te leveren',
            'privacy' => 'Privacy toelichting',
            'payroll_undetermined' => 'Loondienst onbepaald',
            'payroll_determined' => '<PERSON><PERSON>ienst bepaald',
            'business_owner' => 'Ondernemer',
            'freelancer' => 'ZZP',
            'retirement' => 'Pensioen',
            'social_benefits' => 'Uitkering',
            'student' => 'Student',
            'owner_occupied_home_sold' => 'Koopwoning verkocht',
            'owner_occupied_home_unsold' => 'Koopwoning niet verkocht',
            'rental_house' => 'Huurwoning',
            'home_living' => 'Thuiswonend',
        ],
        'required' => true,
    ));

    $RentalDocumentcheckParts->addElement('textarea', 'description_nl', array(
        'label' => 'Inhoud',
        'attribs' => array(
            'rows' => 5,
            'cols' => 50,
        ),
        'required' => true,
    ));

    $RentalDocumentcheckParts->addElement('textarea', 'description_en', array(
        'label' => 'Inhoud engels',
        'attribs' => array(
            'rows' => 5,
            'cols' => 50,
        ),
        'required' => false,
    ));

    $RentalDocumentcheckParts->addElement('hidden', 'source', array(
       'value' => 'user',
    ));


    //submit
    $this->addElement('submit', 'aanmaken', array(
        'label' => 'Opslaan'
    ));

