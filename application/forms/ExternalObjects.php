<?

//address form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('enctype', 'multipart/form-data')
	->setAttrib('class', 'form');


$address = new Form_SubForm($this);
$address->setAttrib('title', '<PERSON><PERSON> gegevens');
$this->addSubForm($address, 'address_form');

	$address->addElement('text', 'address', [
		'label' => 'Adres',
		'validators' => [],
	]);

	$address->addElement('text', 'number', [
		'label' => 'Nummer',
		'validators' => [],
	]);

	$address->addElement('text', 'city', [
		'label' => 'Stad',
		'validators' => [],
	]);

	$address->addElement('text', 'zipcode', [
		'label' => 'Postcode',
		'validators' => [],
	]);

$info = new Form_SubForm($this);
$info->setAttrib('title', 'Object details');
$this->addSubForm($info, 'info_form');

	$info->addElement('textarea', 'description', [
		'label' => 'Omschrijving',
		'validators' => [],
		'expandable' => true
	]);

	$info->addElement('textarea', 'url', [
		'label' => 'Website link',
		'validators' => [],
		'placeholder' => 'http://',
		'expandable' => true
	]);

	$info->addElement('text', 'start', [
		'class' => 'DatePicker',
		'label' => 'Beschikbaar vanaf',
		'validators' => [],
	]);

	$info->addElement('numberInput', 'price', [
		'label' => 'Huurbedrag',
		'validators' => [],
	]);

	$info->addElement('numberInput', 'size', [
		'label' => 'Oppervlakte (m2)',
		'validators' => [],
	]);

	$info->addElement('select', 'has_carpeting', [
		'label' => 'Gestoffeerd',
		'multiOptions' => [
			-1 => '-',
			'Ja',
			'Nee',
			'Eventueel',
			'Gedeeltelijk',
		]
	]);

	$info->addElement('select', 'has_furniture', [
		'label' => 'Gemeubileerd',
		'multiOptions' => [
			-1 => '-',
			'Ja',
			'Nee',
			'Eventueel',
			'Gedeeltelijk',
		]
	]);

	$info->addElement('hidden', 'id', [
		'label' => '',
		'validators' => [],
	]);

//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Object opslaan'
]);