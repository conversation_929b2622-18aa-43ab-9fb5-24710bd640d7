<?php

$keyRingFormModel = new \FormModels\KeyManagement\KeyRings();
$objectId = $this->getOwner()->getParam('object_id');
$locationId = $this->getOwner()->getParam('location_id');
$keyringId = $this->getOwner()->getParam('id');

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'keyRing');

$general->addElement('hidden', 'location_id', ['value' => $locationId]);

$general->addElement('hidden', 'object_id', ['value' => $objectId]);

$general->addElement('text', 'object_name', [
    'label' => 'Object',
    'required' => true,
    'value' => $keyRingFormModel->getObjectAddressForKeyring($objectId),
    'attribs' => [
        'title' => 'Zoeken op adres',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}",
        'autocompleter' => 'key-management_keyring/find-objects/limit/10/',
        'autocompleter_options' => Zend_Json::encode(
            ['hiddenElementId' => 'keyRing-object_id'],
            false,
            ['enableJsonExprFinder' => true]
        )
    ],
    'validators' => [
        'ComboboxRequired' => [
            'validator' => 'ComboboxRequired',
            'options' => [
                'field' => $general->getElement('object_id'),
            ]
        ],
    ],
]);

$general->addElement('text', 'label', [
    'value' => $keyRingFormModel->getLabel($keyringId),
    'label' => 'Label',
    'required' => true,
    'validators' => [
        [
            'validator' => 'LabelCheckKeyRing',
            'options' => [
                'callback' => function ($value) use ($keyRingFormModel, $keyringId, $locationId) {
                    return $keyRingFormModel->isValidLabel($value, $keyringId, $locationId);
                }
            ],
        ]
    ]
]);

$general->addElement('select', 'group', [
    'value' => $keyRingFormModel->getSelectedGroup($keyringId),
    'label' 		=> 	'Groep',
    'multioptions' 	=> $keyRingFormModel->getGroupsForSelect($locationId),
    'validators' 	=> 	[],
]);

$general->addElement('textarea', 'remark', [
	'label' => 'Opmerking',
	'rows' => 3,
	'cols' => 40
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);