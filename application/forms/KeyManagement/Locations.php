<?php

$locationFormModel = new \FormModels\KeyManagement\Locations();
$locationId = $this->getOwner()->getParam('id');

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('id', 'location')
    ->setAttrib('class', 'form');

$location = new Form_SubForm($this);
$location->setAttrib('title', 'Locatie');
$this->addSubForm($location, 'location');

$location->addElement('text', 'name', [
    'label' => 'Naam',
    'required' => true,
    'title' => 'Vestigingsnaam',
    'class' => 'forceAutoHint',
]);

$location->addElement('hidden', 'project_id', [
    'value' => count(loginManager::data()->projects) == 1 ? end(loginManager::data()->projects) : false
]);

if (Settings::get('Vestigingsomgeving module')) {
    $location->addElement('text', 'project', [
        'label' => 'Project',
        'required' => true,
        'value' => $locationFormModel->getProjectNameForLocation($locationId),
        'attribs' => [
            'title' => 'Zoeken op naam',
            'class' => 'forceAutoHint',
            'autocompleter' => 'key-management_location/find-projects/limit/10/',
            'autocompleter_options' => Zend_Json::encode(
                ['hiddenElementId' => 'location-project_id'],
                false,
                ['enableJsonExprFinder' => true]
            )
        ],
        'validators' => [
            'ComboboxRequired' => [
                'validator' => 'ComboboxRequired',
                'options' => [
                    'field' => $location->getElement('project_id'),
                ]
            ],
        ],
    ]);
}

$this->addElement('submit', 'aanmaken', ['label' => 'Opslaan']);