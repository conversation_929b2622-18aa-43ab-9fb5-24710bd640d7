<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$checkOutTenant = new Form_SubForm($this);
$checkOutTenant->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($checkOutTenant, 'check_out_employee');

$checkOutTenant->addElement('hidden', 'user_id', [
    'value' => loginManager::data()->id
]);

$checkOutTenant->addElement('text', 'find_user', [
    'label' => 'Medewerker',
    'required' => true,
    'autocomplete' => 'off',
    'value' => loginManager::data()->name,
    'keep_for_add_another' => true,
    'attribs' => [
        'title' => 'Zoeken op medewerker',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}",
        'placeholder' => '',
        'autocompleter' => 'key-management_keyring/find-employees/limit/10/',
        'autocompleter_options' => Zend_Json::encode(
            ['hiddenElementId' => 'check_out_employee-user_id'],
            false,
            ['enableJsonExprFinder' => true]
        )
    ],
    'validators' => [
        'ComboboxRequired' => [
            'validator' => 'ComboboxRequired',
            'options' => [
                'field' => $checkOutTenant->getElement('user_id'),
            ]
        ],
    ],
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Checkout',
]);