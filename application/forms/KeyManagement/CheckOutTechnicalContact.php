<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$checkOutTenant = new Form_SubForm($this);
$checkOutTenant->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($checkOutTenant, 'check_out_technical_contact');

$checkOutTenant->addElement('hidden', 'user_id');

$checkOutTenant->addElement('text', 'find_user', [
    'label' => 'Technische partij',
    'required' => true,
    'autocomplete' => 'off',
    'value' => '',
    'keep_for_add_another' => true,
    'attribs' => [
        'title' => 'Zoeken op technische partij',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}",
        'placeholder' => '',
        'autocompleter' => 'key-management_keyring/find-technical-contacts/limit/10/',
        'autocompleter_options' => Zend_Json::encode(
            ['hiddenElementId' => 'check_out_technical_contact-user_id'],
            false,
            ['enableJsonExprFinder' => true]
        )
    ],
    'validators' => [
        'ComboboxRequired' => [
            'validator' => 'ComboboxRequired',
            'options' => [
                'field' => $checkOutTenant->getElement('user_id'),
            ]
        ],
    ],
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Checkout',
]);