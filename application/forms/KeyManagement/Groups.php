<?php

$locationId = $this->getOwner()->getParam('location_id');

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('id', 'group')
    ->setAttrib('class', 'form');

$group = new Form_SubForm($this);
$group->setAttrib('title', 'Groep');
$this->addSubForm($group, 'group');

$group->addElement('text', 'name', [
    'label' => 'Naam',
    'required' => true
]);

$group->addElement('hidden', 'location_id', ['value' => $locationId]);

$this->addElement('submit', 'aanmaken', ['label' => 'Opslaan']);