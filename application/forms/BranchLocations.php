<?php /** @noinspection PhpUnhandledExceptionInspection */

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');

$branchLocationsSubForm = new Form_SubForm($this);
$branchLocationsSubForm->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($branchLocationsSubForm, 'branch_locations_form');

$branchLocationsSubForm->addElement('text', 'description', [
    'label' => 'Vestigingsnaam',
    'required' => true,
]);

if (Settings::get('modules_rental')) {
    $branchLocationsSubForm->addElement('text', 'funda_number', [
        'label' => 'Funda nummer',
    ]);
}

$email_accounts_select = function(){
    return db()->select()->from('email_accounts', array('id',  'CONCAT_WS(" - ", name, address)'))->order('name');
};

$email_accounts =  db()->fetchPairs($email_accounts_select());
$email_accounts[0] = 'Standaard (gelijk aan algemene instelling)';
ksort($email_accounts);


$branchLocationsSubForm->addElement('select', 'mailaccount', array(
    'label' => 'E-mail account voor verzending',
    'multioptions' 	=> $email_accounts,
));


$this->addElement('submit', 'aanmaken', [
    'label' => 'Vestiging opslaan',
]);
