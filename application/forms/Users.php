<script type="text/javascript" src="media/javascript/iban_to_bic.js"></script>
<? $uModel = new Users(); $u = intval($this->getOwner()->userid) < 1? false: $uModel->getById($this->getOwner()->userid); ?>
<? $params = $this->getOwner()->getAllParams(); ?>

<?
$spec_model = new ObjectsSpecificationsValues();
$spec_model->setFilterOgType('private');
$specifications = $spec_model->getSearchSpecificationsForUser($u ? $u->id : false);
?>

<script type="text/javascript">
	var presetEmployee = <?= json_encode( (isset($params['usertype']) && $params['usertype'] == 'employee') ) ?>;
	var typeChangeAllowed = !presetEmployee && <?= json_encode(intval($this->getOwner()->userid) < 1) ?>;

	var originalCompanyAndRole = <?= json_encode( ($u)? array(
		'company_id' => $u->employee->company->id,
		'role' => $u->employee->role,
		'rolename' => Employee::$roleNames[$u->employee->role]
	) : array()) ?>;

	var uiFunctions = {};

	uiFunctions.userTypeToggle = function(type) {
		// all the subforms that should be toggleable
		var allForms = [
			'user_form',
			'bank_account_form',
			'partner_form',
			'invoice_addr_form',
			'invoice_prefs_form',
			'comment_form',
			'employee_form',
			'contact_form',
			'search_form',
			'registrant_convert_form',
<? foreach($specifications as $spec_group_id => $spec_group): ?>
			'object_specifications_<?= $spec_group_id ?>',
<? endforeach; ?>
		];

		// list of subforms to toggle between,
		// the first element of the array should have
		// the value of the selection dropdown,
		// after that its forms can be listed
		var formList = [
			['user', 'user_form', 'bank_account_form', 'partner_form', 'invoice_addr_form',
			'invoice_prefs_form', 'comment_form'],

			['employee', 'employee_form', 'comment_form'],

			['none', 'bank_account_form'],

			['registrant','invoice_addr_form', 'bank_account_form', 'partner_form', 'contact_form', 'search_form',  'registrant_convert_form', 'user_form',
<? if(!Settings::get('software_only_rental')):?>
            'bank_account_form','invoice_prefs_form',
<?endif;?>
<? foreach($specifications as $spec_group_id => $spec_group): ?>
				'object_specifications_<?= $spec_group_id ?>',
<? endforeach; ?>
                'comment_form'
			]
		];

		var curList = [];

		formList.each(function(item) {
			// select the correct list of subforms to show
			if(item[0] === type) {
				curList = item;
				return;
			}
		});

		if(type == 'none' && $('general-utype').get('value') === 'registrant') {
			curList.push('registrant_convert_form');
			curList.push('invoice_addr_form');
		}

		allForms.each(function(form) {
			// purposfully ignore the first entry of the
			// array, as that contains the user type
			if(curList.indexOf(form) > 0) {
				$$('[name='+form +']').show();
			} else {
				$$('[name='+form +']').hide();
			}

		});

		if(typeChangeAllowed) {
			$$('[name=usertype_form]').show();
		} else {
			$$('[name=usertype_form]').hide();
		}

		// set the users type to the right kind
		switch (type) {
			case 'user':
				$('general-utype').set('value', 'tenant');
				$('general-corporation-label').show();
				$('general-corporation-element').show();
				break;

			case 'employee':
				$('general-utype').set('value', 'tenant');
				$('general-corporation-label').hide();
				$('general-corporation-element').hide();
				break;

			case 'registrant':
				$('general-utype').set('value', 'registrant');
				$('general-corporation-label').show();
				$('general-corporation-element').show();
				break;
			
			case 'none':
				if($('general-utype').get('value') !== 'registrant') {
					$('general-utype').set('value', 'tenant');
					$('general-corporation-label').show();
					$('general-corporation-element').show();
				}
				break;

			default:
				$('general-utype').set('value', 'tenant');
		}
	};

	uiFunctions.updateEmployeeTypes = function(input) {
		$$('#employee_form-role option').destroy();
		existingRole = false;
		roles = [];

		Object.keys(input).each(function(item){
			roles.push({
				role: item,
				rolename: input[item],
				select: false
			});
		});

		roles = roles.filter(function(item) {
			if(item.role == 'company_id') {
				if(typeof originalCompanyAndRole.company_id !== 'undefined'
						&& item.rolename == originalCompanyAndRole.company_id) {
					item.role = originalCompanyAndRole.role;
					item.rolename = originalCompanyAndRole.rolename;
					item.select = true;
					existingRole = item;
				}
				return false;
			}
			return true;
		});

		if(existingRole) {
			roles = roles.filter(function(item){ return item.role !== existingRole.role; });
			roles.unshift(existingRole);
		}

		roles.each(function(item){
			attribs = {
				label: item.rolename,
				value: item.role,
				html: item.rolename
			};
			if(item.select === true)
				attribs.selected = 'selected';

			$$('#employee_form-role').grab( new Element('option', attribs));
		});
	};

	uiFunctions.fetchEmployeeTypes = function(c_id) {
		new Request.JSON({
			url: 'company/get-available-employee-roles',
			onSuccess: uiFunctions.updateEmployeeTypes
		}).get({'company_id': c_id});
	};

	window.addEvent('domready', function () {
		// add a datepicker to all date input fields
		$$('input.datepicker').each(function (input) {
			new DatePicker(input, {
				yearStart: (new Date().getFullYear())-120,
				yearRange: 130,
			});
		});

		new ibanToBic('.form.useredit', 'bank_account_form-bank_account_iban', 'bank_account_form-bank_account_bic');

		var min_el = $('invoice_prefs_form-minimum_indexation');
		var max_el = $('invoice_prefs_form-maximum_indexation');

		if(min_el && max_el){
			min_el.addEvent('change', function(){
				checkMinMax(min_el, max_el, 'min');
			});
			max_el.addEvent('change', function(){
				checkMinMax(min_el, max_el, 'max');
			});
		}
		var addition_el = $('invoice_prefs_form-indexation_addition');

		if(addition_el)
			addition_el.addEvent('change', function(){
				if(addition_el.get('value') > 100){
					alert('Het percentage mag niet groter dan 100% zijn.');
					addition_el.set('value', 100);
				}

				if(addition_el.get('value') < 0){
					alert('Het percentage mag niet kleiner dan 0% zijn.');
					addition_el.set('value', 0);
				}
			});

		[2,3,4].each(function(partner_number) {

		    const partner_element = $('partner_unlink_' + partner_number);

            if ($(partner_element))
                $(partner_element).addEvent('click', function () {

                    if (!confirm('Weet u zeker dat u de ' + partner_number + 'e bewoner wilt loskoppelen? Deze actie kan niet ongedaan gemaakt worden.')) return;

                    new Request({
                        'url': 'user/partner-unlink/user/<?= $this->getOwner()->userid ?>/partner_number/' + partner_number + '/',
                        'onComplete': function () {
                            partner_element.getParent('dl').getElements('select, input').each(function (input) {
                                if (input !== partner_element) {
                                    input.erase('value');
                                }
                            });
                        }
                    }).send();

                });
        });


		// allow the form to toggle between different user type subforms
		$$('#usertype_form-usertype').addEvent('change', function (evt) {
			uiFunctions.userTypeToggle(evt.target.value);
		});

		// make sure that the correct subform is shown intially
		uiFunctions.userTypeToggle($$('#usertype_form-usertype').getSelected()[0][0].value);

		// show only the employee types that can be set
		$$('#employee_form-company').addEvent('change', function (evt) {
			uiFunctions.fetchEmployeeTypes(evt.target.value);
		});
		if('undefined' !== typeof $$('#employee_form-company').getSelected()[0][0])
			uiFunctions.fetchEmployeeTypes($$('#employee_form-company').getSelected()[0][0].value);

		$('delete').addEvent('click', function(event) {
			if(!confirm('Weet u zeker dat u deze persoon (met alle gerelateerde gegevens) wil verwijderen?'))
				event.stop();
		});

		if($('bank_account_form-additional_iban'))
			addAdditionalIbanBox();

		// allow switching between these two stages
		if($('general-profile_completion') && ['2', '3'].contains($('general-profile_completion').value)) {
			$('general-profile_completion').getChildren('option').each(function (option) {
				if(['2', '3'].contains(option.value)) {
					option.removeProperty('disabled');
				}
			});
		}

        if(allowed_cities = $('search_form-allowed_cities')) {
            var mSelect = new multipleSelectFilter(allowed_cities);
        }
	});

	function addAdditionalIbanBox(){
		var multi_select = $('bank_account_form-additional_iban');
		var multi_select_save_values = $('bank_account_form-additional_iban_save_values');

		var add_button = new Element('a', {
			'class': 'icon add',
			'html': ' &nbsp;'
		}).setStyles({'width': '16px', 'display': 'inline-block', 'margin': '0 5px'});

		var delete_button = new Element('a', {
			'class': 'icon delete',
			'html': ' &nbsp;'
		}).setStyles({'width': '16px', 'display': 'inline-block', 'margin': '0 5px'});

		add_button.inject(multi_select, 'after');
		delete_button.inject(add_button, 'after');


		multi_select.addEvent('change', function(){
			if(multi_select.getSelected().length > 0)
				delete_button.show();
			else
				delete_button.hide();

			var values = [];
			$each(multi_select.getElements('option'), function(option){
				values.extend([option.get('value')]);
			});
			
			multi_select_save_values.set('value', values.join(','));
		}).fireEvent('change');

		add_button.addEvent('click', function(){ addNewIban(); });

		delete_button.addEvent('click', function(){
			if(confirm('Weet u zeker dat u deze aanvullende IBAN nummers wilt verwijderen?'))
				multi_select.getSelected().destroy();

			multi_select.fireEvent('change');
		})		
	}

	function addNewIban(value){
		var multi_select = $('bank_account_form-additional_iban');
		
		var iban = prompt('Voer een nieuwe IBAN rekening in', value ? value : 'NL');

		if(iban == null) return;
		
		new Request.JSON({
			'url': 'user/iban-to-bic',
			'method': 'post',
			'data': {'iban': iban},
			'onSuccess': function(response){
				if(response != false){
					multi_select.adopt(new Element('option', {'text': iban, 'value': iban}));
					multi_select.fireEvent('change');
				} else {
					alert('Het ingevoerde IBAN nummer is niet correct en kan niet opgeslagen worden.');
					addNewIban(iban);
				}

			}
		}).send();		
	}
</script>

<style>
    #invoice_prefs_form-invoiceprefs_address-element {
        max-width: 70%;
        overflow: hidden;
    }

    #invoice_prefs_form-invoiceprefs_address-element > select {
        max-width: 100%;
    }
</style>

<link href="media/style/autocompleter.css" media="screen" rel="stylesheet" type="text/css" >
<script type="text/javascript" src="media/javascript/autocompleter/Autocompleter.js"></script>
<script type="text/javascript" src="media/javascript/autocompleter/Autocompleter.Request.js"></script>
<script type="text/javascript" src="media/javascript/invoice_custom.js"></script>


<?	

//general form options
$this->setAction('user/edit-crm/' . ($this->getOwner()->userid ? 'id/' . $this->getOwner()->userid . '/' : '') . ($this->getOwner()->uoid ? '?uoid=' . $this->getOwner()->uoid : ''))
		->setMethod('post')
		->setAttrib('id', 'user')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form useredit');


$usertype = new Form_SubForm($this);
$usertype->setAttrib('title', ucfirst(translate()->_('relation_kind')));
$usertype->setAttrib('style', 'display: none;');
$this->addSubForm($usertype, 'usertype_form');

$usertypes = [
    'user' => ucfirst(translate()->_('tenant')),
    'employee' => ucfirst(translate()->_('employee')),
    'registrant' => ucfirst(translate()->_('lead')),
];

if(Settings::get('software_only_rental')){
    $usertypes = array_reverse($usertypes, true);
}

	$usertype->addElement('select', 'usertype', array(
		'label' => ucfirst(translate()->_('relation_kind')),
		'multiOptions'	=> $usertypes ,
		'registerInArrayValidator' => false,
		'value' => $this->getOwner()->type ?: ''
	));

$employee = new Form_SubForm($this);
$employee->setAttrib('title', ucfirst(translate()->_('employee')));
$employee->setAttrib('style', 'display: none;');
$this->addSubForm($employee, 'employee_form');

	// retrieve the list of companies
	$c = Company::getList();
	$companies = array();
	foreach ($c as $cs) {
		$companies[$cs['id']] = $cs['name'];
	}
	natsort($companies);

	// this textfield is used as a combo box,
	// see library/Zend/Form/Decorator/ComboBox.php for details
	$employee->addElement('select', 'company', array(
		'label' => 'Bedrijf',
		'autocomplete' => 'off',
		'multiOptions'	=> $companies,
		'registerInArrayValidator' => false,
		// 'decorators' => array(
		// 	array('ViewHelper'),
		// 	array('ComboBox', array('items' => $companies,)),
		// 	array('Label'),
		// 	array('DtDdWrapper'),
		// ),
	));

	$employee->addElement('select', 'role', array(
		'label' => 'Soort medewerker',
		'multiOptions'	=> Employee::$roleNames,
		'value' => $this->getOwner()->type ?: false
	));

	$employee->addElement('text', 'department', array(
		'label' => 'Afdeling',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));

	$employee->addElement('text', 'function', array(
		'label' => 'Functie',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));

$general = new Form_SubForm($this);
$general->setAttrib('title', ucfirst(translate()->_($params['usertype'] == 'employee' ? 'general' : 'first_tenant')));
$this->addSubForm($general, 'general');
    if(Settings::get('modules_rental') && !Settings::get('modules_user_project')){
        $project_list = Project::getMenuList();

        $project_options = array_combine(array_keys($project_list), array_column($project_list, 'name'));

        if(count($project_options) > 1)
			$project_options = [false => 'Kies een project'] + $project_options;

        $general->addElement('select', 'project', [
            'label' => 'Project',
            'validators' => [],
            'multiOptions' => $project_options,
            'attribs' => ['style' => 'min-width:220px;'],
            'required' => count($project_options) > 0 && loginManager::isProjectLimited(),
            'style' => 'width: 150px;'
        ]);
    } else if (Settings::get('modules_user_project'))  {

        $branchLocationOptions = (new BranchLocations())->getAssociativeList();

        if (count($branchLocationOptions) > 1) {
            $branchLocationOptions = [false => 'Kies een vestiging'] + $branchLocationOptions;
        }

        $general->addElement('select', 'branch_location', [
            'label' => ucfirst(translate()->_('branch_location')),
            'multiOptions' => $branchLocationOptions,
            'value' => loginManager::data()->selected_branch_location,
            'attribs' => [
                'title' => 'Met deze optie kan de vestiging worden gekozen voor deze lead.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ]
        ]);
    }

	// geslacht
	$general->addElement('select', 'gender', array(
		'label' => ucfirst(translate()->_('gender')),
		'multiOptions'	=> array(
			'female' => ucfirst(translate()->_('gender_female')),
			'male' => ucfirst(translate()->_('gender_male')),
			'unknown' => ucfirst(translate()->_('unknown')),
            'inherit' => ucfirst(translate()->_('inherit')),
			'none' => ucfirst(translate()->_('none'))
		),
		'registerInArrayValidator' => false,
	));


	// voorletters
	$general->addElement('text', 'initials', array(
		'label' => ucfirst(translate()->_('initials')),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));


	// voornaam
	$general->addElement('text', 'firstname', array(
		'label' => ucfirst(translate()->_('firstname')),
 		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));


	// tussenvoegsel
	$general->addElement('text', 'middlename', array(
		'label' => ucfirst(translate()->_('middlename')),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	20,
				)
			)
		)
	));


	// achternaam
	$general->addElement('text', 'name', array(
		'label' => ucfirst(translate()->_('surname')),
        'required' => true,
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));

    // email
    $general->addElement('text', 'email_address', array(
        'label' => ucfirst(translate()->_('email')),
        'required' => Settings::get('software_only_rental'),
        'validators' =>  array('EmailAddress'),
    ));

// email copy
$general->addElement('text', 'email_parent', [
    'label' => translate()->_('email_parent'),
    'validators' => ['emailAddress'],
    'attribs' => ['autocomplete' => 'off',
        'title' => 'Als u wilt dat alle systeemmails via BCC kopie naar een extra mail adres gaan, kunt u hier een adres invoeren. Bedoeld o.a. voor bewindvoerder, ouders en begeleiders',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': 0, 'y': 0}"]
]);

	$general->addElement('select', 'identication_type', [
		'label' => ucfirst(translate()->_('identification_type')),
		'multiOptions'	=> Users::$identificationTypeLabels,
	]);

	// BSN
	$general->addElement('text', 'BSN', array(
		'label' => ucfirst(translate()->_('social_security_identifier')),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	20,
				)
			)
		)
	));

	$general->addElement('text', 'identication_valid_till', [
		'label' => ucfirst(translate()->_('identification_valid_till')),
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);


	// geboortedatum
	$general->addElement('text', 'bdate', array(
		'label' => ucfirst(translate()->_('birth')),
		'attribs' => array(
			'class' => 'DatePicker',
			'datepicker_options' => json_encode(array(
				'yearRange' => date('Y') - 1900,
				'yearStart' =>  1900
			))
		),
		'validators' => array(
			array(
				'validator'	=> 'Date',
				'options'	=> array(
					'locale' => 'nl',
				)
			)
		)
	));

	// birthplace
	$general->addElement('text', 'birthplace', [
		'label' => ucfirst(translate()->_('birthplace')),
	] );

    $languageOptions = [
        'nl' => 'Nederlands',
        'en' => 'Engels',
    ];

    if(Settings::get('software_french_enabled'))
        $languageOptions['fr'] = 'Frans';

    $general->addElement('select', 'language', [
        'label'        => ucfirst(translate()->_('language')),
        'multiOptions' => $languageOptions,
    ]);



    $general->addElement('text', 'phone_secondary', array(
        'label' => ucfirst(translate()->_('mobile')),
        'validators' => array( 'PhoneNumber' ),
    ));

	// telefoonnummers
	$general->addElement('text', 'phone_primary', array(
		'label' => ucfirst(translate()->_('phone')),
		'validators' => array( 'PhoneNumber' ),
	));





	$general->addElement('text', 'phone_emergency', array(
		'label' => ucfirst(translate()->_('emergency_contactphone')),
		'validators' => array( 'PhoneNumber' ),
	));

	$general->addElement('text', 'phone_secondary_emergency', array(
		'label' => ucfirst(translate()->_('emergency_contactphone_second')),
		'validators' => array( 'PhoneNumber' ),
	));



	if( Settings::get('sms_enabled') )
		$general->addElement('select', 'sms', [
			'label' => ucfirst(translate()->_('inform_sms')),
			'multiOptions'	=> [
				'1' => 'Ja',
				'0' => 'Nee',
			],
		]);


	// bedrijfsnaam
	$general->addElement('text', 'corporation', array(
		'label' => ucfirst(translate()->_('company')),
		'attribs' => array(
			'title' => 'Alleen als notitie gebruiken. Indien u deze persoon 
						als bedrijf wil gebruiken dient u het bedrijf eerst 
						in te voeren onder "Relaties->Bedrijf Invoeren". 
						U kunt deze persoon dan als contractant selecteren / invoeren.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	255,
				)
			)
		)
	));

	// alleen indien koppeling met financieel systeem.
    $general->addElement('text', 'olddebtorcode', array(
    'label' => ucfirst(translate()->_('olddebtorcode')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	255,
            )
        )
    )
    ));

    $general->addElement('text', 'free_field_1', array(
        'label' => ucfirst(translate()->_('free_field_1')),
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	255,
                )
            )
        )
    ));

    $general->addElement('text', 'free_field_2', array(
        'label' => ucfirst(translate()->_('free_field_2')),
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	255,
                )
            )
        )
    ));







	// arrival date and time (Ravel specific)
	if(in_array(Settings::get('general_company_shortname'), ['ravel', 'bhw']))
		$general->addElement('text', 'arrival', [
			'label' => 'Verhuisdatum',
			'attribs' => [
				'title' => 'Gebruik hier het formaat "jjjj-mm-dd uu:mm:ss", 
					dus bijvoorbeeld: "2015-09-16 15:15:00" (zonder " tekens). 
					Let op: 1 spatie in het midden, alle waarden behalve jaar 
					met 2 tekens en - of : tekens tussendoor.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			],
			'filters' => [
				'StringTrim',
			],
			'validators' => [
				[
					'Callback',
					true,
					'options' => [
						'callback' => function($value) {
							if(!is_string($value))
								return true;

							if(trim($value) == false)
								return true;

							// must match this: 2015-09-16 15:15:00
							return preg_match('/^20\d\d-[01]\d-[0123]\d [01]\d:[012345]\d:[012345]\d$/', $value) === 1 && strtotime($value) !== false;
						}
					]
				]
			]
		]);

	if ( Settings::get( 'modules_tenantLogin_roomselector' )  || Settings::get( 'modules_rental' ) ) {
        $general->addElement('select', 'source_of_income', [
            'label' => ucfirst(translate()->_('source_of_income')),
            'required' => $params['type'] == 'registrant',
            'multiOptions' => [
                '' => 'onbekend',
                'Loondienst (onbepaald)' => 'Loondienst (onbepaald)',
                'Loondienst (tijdelijk)' => 'Loondienst (tijdelijk)',
                'pensioen' => 'Pensioen',
                'uitkering' => 'Uitkering',
                'ondernemer' => 'Ondernemer / ZZP',
                'uitzendkracht' => 'Uitzendkracht',
            ]
        ]);

		$general->addElement('text', 'income_amount', array(
			'label' => ucfirst(translate()->_('income_amount')),
			'validators' => array(
				array(
					'validator'	=> 'stringLength',
					'options'	=> array(
						'max' =>	255,
					)
				)
			)
		));

		$general->addElement('select', 'living_situation', [
            'label' => ucfirst(translate()->_('living_situation')),
            'required' => $params['type'] == 'registrant',
            'multiOptions' => [
                '' => 'onbekend',
                'Koopwoning (verkocht)' => 'Koopwoning (verkocht)',
                'Koopwoning (niet verkocht)' => 'Koopwoning (niet verkocht)',
                'koopwoning' => 'Koopwoning',
                'huurwoning' => 'Huurwoning',
                'thuiswonend' => 'Thuiswonend'
            ]
        ]);

		$general->addElement( 'select', 'profile_completion', [
			'label'        => ucfirst(translate()->_('profile_completion')),
			'multiOptions' => [
                '9' => 'Account aangemaakt/inschrijving ontvangen',
                '1' => 'Ingeloot/toegewezen',
                '2' => 'Wachten op aanlevering dossier',
                '3' => 'Dossier compleet, wachten op goedkeuring dossier',
                '0' => 'Dossier goedgekeurd (mag ondertekenen of heeft ondertekend)',
			],
			'attribs'      => [
				'title'   => 'Hier kan je aangeven welke status van verhuurflow de kandidaat zich bevindt.',
				'class'   => 'forceAutoHint',
			],
		] );
	}


if (Settings::get('modules_tenantLogin') && Settings::get('software_type') !== 'energy') {
    $general->addElement('checkbox', 'contract_signing_allowed', [
        'label' => ucfirst(translate()->_('contract_signing_allowed')),
        'value' => !Settings::get('modules_tenantLogin_checkbeforesign'),
        'attribs'      => [
            'title'   => 'Als dit vinkje uit staat mag deze huurder tijdens het contracteer proces 
                    zijn/haar contract alleen inzien maar niet tekenen.',
            'class'   => 'forceAutoHint',
        ],
    ]);
}

if (Settings::get('api_partner_pete') && Settings::get('api_partner_pete_api_key')) {
    $general->addElement('checkbox', 'consent_to_contact_from_budget_manager', [
        'label' => 'Toestemming maandlastenmanager',
        'value' => '',
        'attribs' => [
            'title' => 'Als dit vinkje uit staat dan heeft de gebruiker geen toestemmming gegeven voor de maandlasten manager.',
            'class' => 'forceAutoHint',
        ],
    ]);
}

if (Settings::get('general_company_shortname') == 'HB') {

		// Beroep/Student
		$general->addElement('select', 'profession', [
			'label' => 'Beroep/Student',
			'multiOptions'	=> [
				'' => '',
				'student' => 'Student',
				'werkend' => 'Werkend',
				'werkzoekend' => 'Werkzoekend'
			],
			'registerInArrayValidator' => false
		]);

		// study level
		$general->addElement('select', 'study_level', [
			'label' => 'Opleidingsniveau',
			'multiOptions'	=> [
				'' => '',
				'MBO' => 'MBO',
				'HBO' => 'HBO',
				'Universitair' => 'Universitair'
			],
			'registerInArrayValidator' => false
		]);

		// school
		$general->addElement('select', 'school', [
			'label' => 'School',
			'multiOptions'	=> Controllers_GeneralForm_Hopibon_Schools::$schools,
			'attribs' => [
				'style' => 'width: 313px;'
			],
			'registerInArrayValidator' => false
		]);

		if ($u->school_input) {
			$general->addElement('text', 'school_input', [
				'label' => 'School anders',
				'validators' => [
					[
						'validator' => 'stringLength',
						'options' => [
							'max' => 255,
						]
					]
				]
			]);
		}

		// study direction
		$general->addElement('select', 'study_direction', [
			'label' => 'Studie richting',
			'multiOptions'	=> Controllers_GeneralForm_Hopibon_StudyDirection::$directions,
			'attribs' => [
				'style' => 'width: 313px;'
			],
			'registerInArrayValidator' => false
		]);

		if ($u->study_direction_input) {
			$general->addElement('text', 'study_direction_input', [
				'label' => 'Studie richting anders',
				'validators' => [
					[
						'validator' => 'stringLength',
						'options' => [
							'max' => 255,
						]
					]
				]
			]);
		}

		$general->addElement('text', 'school_location', [
			'label' => 'Plaats opleiding',
			'value' => '', // $user
			'autocomplete' => 'off',
			'required' => true,
			'attribs' => [
				'size' => 42,
				'title' => 'Zoek plaatsnaam',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
				'placeholder' => '',
				'autocompleter' => 'general-form/find-cities/',
			],
		]);

		$general->addElement('textarea', 'comment', [
			'label' => 'Opmerkingen',
			'attribs' => [
				'rows' => 8,
				'cols' => 54
			]
		]);
}


	// type, ie: tenant, admin, etc
	$general->addElement('hidden', 'utype', array(
	));

	// this users id
	$this->general->addElement('hidden', 'id', array(
	));


$bank_account = new Form_SubForm($this);
$bank_account->setAttrib('title', 'Bankrekening(en)');
$bank_account->setAttrib('style', 'display: block;');
$this->addSubForm($bank_account, 'bank_account_form');

	// iban
	$bank_account->addElement('text', 'bank_account_iban', [
		'label' => ucfirst(translate()->_('IBAN_CLEAN')),
		'filters' => ['alnum'],
		'validators' =>  ['iban']
	]);

	// bic
	$bank_account->addElement('text', 'bank_account_bic', array(
		'label' => ucfirst(translate()->_('BIC')),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	11,
				)
			)
		)
	));

	if($this->getOwner()->userid > 0)
		$ibans = db()->fetchPairs(db()->select()->from('bank_account', array('test' => 'iban', 'iban'))->where('type = ?', 'user_secondary')->where('map_to = ?', $this->getOwner()->userid));

	if(!$ibans)
		$ibans = array();

	$bank_account->addElement('multiselect', 'additional_iban', array(
		'label' => ucfirst(translate()->_('additional_iban')),
		'multiOptions' => $ibans,
		'attribs' => array(
			'style' => 'width: 180px;',
			'title' => 'Aanvullende IBAN nummers t.b.v. het automatisch matchen van betalingen, maak een selectie om te kunnen verwijderen.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -10, 'y': 0}"
		)		
	));	

	$bank_account->addElement('hidden', 'additional_iban_save_values', array('value' => implode(',', $ibans)));

$invoice_addr = new Form_SubForm($this);

$invoice_addr->setAttrib('title', $u->type == 'registrant' || $this->getOwner()->type == 'registrant' ? ucfirst(translate()->_('current_address')) : ucfirst(translate()->_('invoice_address')));
$this->addSubForm($invoice_addr, 'invoice_addr_form');

$invoice_addr->addElement('text', 'invoice_address_street', array(
    'label' => ucfirst(translate()->_('street')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	100,
            )
        )
    )
));

$invoice_addr->addElement('text', 'invoice_address_number', array(
    'label' => ucfirst(translate()->_('number')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	20,
            )
        )
    )
));


$invoice_addr->addElement('text', 'invoice_address_zipcode', array(
    'label' => ucfirst(translate()->_('zipcode')),
    'filters' => array('StringTrim'),
    'validators' => array(
        'postcode' => array(
            'validator'	=> 'PostCode',
            'options'	=> array(
                'format'	=> '\d{4}[ ]?[a-zA-Z]{2}'
            )
        )
    )
));

$a_model = new Address();
if(isset($_POST))
    if(isset($_POST['invoice_addr_form']['invoice_address_country']))
        if($locale = $a_model->getCountryLocale($_POST['invoice_addr_form']['invoice_address_country'])) {
            //echo('locale'.$locale);
            //$invoice_addr->invoice_address_zipcode->getValidator('postcode')->setLocale($locale);
            $invoice_addr->invoice_address_zipcode->clearValidators();
        } else {
            $invoice_addr->invoice_address_zipcode->clearValidators();
        }

$invoice_addr->addElement('text', 'invoice_address_city', array(
    'label' => ucfirst(translate()->_('city')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	50,
            )
        )
    )
));

$invoice_addr->addElement('select', 'invoice_address_country', array(
    'label' => ucfirst(translate()->_('country')),
    'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	200,
            )
        )
    ),
    'multiOptions' => Address::getCountriesForFormMultiselect(),
));



$personal_page = new Form_SubForm($this);
$personal_page->setAttrib('title', 'Portal');
$personal_page->setAttrib('style', 'display: block;');
$this->addSubForm($personal_page, 'user_form');

$personal_page->addElement('text', 'username', array(
    'label' => ucfirst(translate()->_('username')),
     'validators' => array(
        array(
            'validator'	=> 'stringLength',
            'options'	=> array(
                'max' =>	50,
            )
        ),
        'uniqueValue' => [
            'validator'	=> 'uniqueValue',
            'options'	=> [
                'allowedId' => $this->getOwner()->userid ?: null,
            ]
        ]
    )
));
$personal_page->addElement('select', 'first', array(
    'label' => ucfirst(translate()->_('contract_signed')),
    'multiOptions'	=> array(
        '1' => 'Nee',
        '0' => 'Ja',
    ),
    'registerInArrayValidator' => false,
));
$personal_page->addElement('checkbox', 'PHD', [
    'label' => 'Commercieel template digitaal contracteren gebruiken',
]);

if ($params['usertype'] != 'employee') {
    Settings::get('software_number_tenants') > 2 ? $nr = (Settings::get('software_number_tenants') + 1) : $nr = 3;
    for ($i = 2; $i < $nr; $i += 1) {
        include 'subforms/tenant.php';
    }
}



// verbergen bij alleen rental

$invoice_prefs = new Form_SubForm($this);
$invoice_prefs->setAttrib('title', 'Facturatie voorkeuren');
$this->addSubForm($invoice_prefs, 'invoice_prefs_form');


	if(Settings::get('general_company_shortname') == 'debazaar')
		$invoice_prefs->addElement(
			new Zend_Form_Element_Select('user_status',
				array(
					'label' => 'Klantstatus',
					'multioptions' => [
						'default' => 'Standaard',
						'historic' => 'Geen huurder meer',
						'collection_agency' => 'Incassobureau',
						'payment_arrangement' => 'Betalingsregeling',
					]
				)
			)
		);

	$userId = $this->getOwner()->userid;
	$address_options = array();

	if(isset($userId)) {
		$addresses = Object::getUserAddress($userId);

		if(isset($addresses) && count($addresses) > 0) {
			if (isset($addresses['user'])) {
				$address_options['user'] = $u->type == 'registrant' || $this->getOwner()->type == 'registrant' ? 'Huidig adres' : 'Factuuradres';
			}

            if (isset($addresses['object'])) {
                foreach ($addresses['object'] as $objectAddress) {
                    $addressSuffix = '';
                    if (count($addresses['object']) > 1) {
                        $addressSuffix = ': ' . $objectAddress['objectAddress'];
                    }
                    $address_options['object_' . $objectAddress['objectId']] = 'Object adres' . $addressSuffix;
                }
            }

			if (count($address_options) > 0) {
				$address_options['-1'] = 'Selecteer een adres';
			}
		}
	}

	if (count($address_options) < 1) {
		$address_options['-1'] = 'Geen adressen beschikbaar';
	}

$invoice_prefs->addElement('select', 'invoiceprefs_address', [
        'label' => 'Factuuradres',
        'autocomplete' => 'off',
        'multioptions' => $address_options,
        'registerInArrayValidator' => false,
        'class' => 'invoicePreferencesAddress'
    ]
);

	$invoice_prefs->addElement('select', 'invoiceprefs_rate', array(
		'label' => 'Facturering per',
		'multiOptions'	=> UserInvoicePreferences::getInvoicePeriodOptions(),
		'registerInArrayValidator' => false,
		'attribs' => [
			'title' => 'Het interval dat u hier instelt bepaald met welke frequentie deze klant gefactureerd wordt.',
			'class' => 'forceAutoHint',
		],
	));

	$invoice_prefs->addElement('select', 'invoiceprefs_type', array(
		'label' => 'Type betaling',
		'multiOptions'	=> array(
			'-1' =>	'Selecteer een type',
			'ideal' => 'Handmatig overmaken',
			'collection' => 'Automatische incasso',
		),
		'registerInArrayValidator' => false,
	));

    $invoice_prefs->addElement('select', 'invoiceprefs_collection_type', [
        'label' => 'Type automatische incasso',
        'multiOptions'	=> [
            '-1' =>	'Selecteer een type',
            'core' => 'normaal',
            'B2B' => 'B2B',
        ],
        'registerInArrayValidator' => false,
    ]);

	if(Settings::get('software_type') == 'energy')
		$invoice_prefs->addElement('text', 'invoiceprefs_title', array(
			'label' => 'Aangepaste factuur titel',
			'attribs' => array(
				'title' => 'Bij lege invoer zal de standaard factuur titel gebruikt worden. Eventueel kan de periode in de gekozen titel toevoegd worden door {$periode} in dit veld te gebruiken.',
				'class' => 'forceAutoHint'
			),
		));


	$invoice_prefs->addElement('select', 'invoiceprefs_specification', array(
		'label' => 'Gespecificeerde rekening',
		'multiOptions'	=> array(
			'-1' =>	'Systeemvoorkeur',
			'0' => 'Nee',
			'1' => 'Ja',
		),
        'attribs' => array(
            'title' => 'Een gespecificeerde rekening laat alle componenten van de huuropbouw op de factuur zien. Kiest u voor nee dan komt een zin met "Huursom volgens contract op de factuur"',
            'class' => 'forceAutoHint'
        ),
		'registerInArrayValidator' => false,
	));

	$invoice_prefs->addElement('select', 'invoice_grouped', array(
		'label' 		=> 'Verzamelfactuur',
		'multioptions' 	=> array('Nee', 'Ja'),
		'attribs' => array(
			'title' => 'Maak een verzamelfactuur voor meerdere objecten van deze ' . (Settings::get('software_type') == 'energy' ? 'bewoner ' : 'huurder') . ' binnen één factuurrun vallen.',
			'class' => 'forceAutoHint'
		),
		'validators' 	=> 	array(),
	));	

 		$invoice_prefs->addElement('select', 'invoiceprefs_period_start_uo', array(
			'label' => 'Factuurperiode op ingangsdatum',
			'validators' => [],
			'multioptions' 	=> Settings::get('software_country') == 'be' ? ['1' => 'Ja', '0' => 'Nee'] : ['0' => 'Nee', '1' => 'Ja'],
			'attribs' => [
				'title' => 'De factuurperiode starten op de ingangsdatum van de verhuurperiode.',
				'class' => 'forceAutoHint',
			],		
		));	

		$invoice_prefs->addElement('text', 'invoiceprefs_offset_days', array(
			'label' => 'Factuurperiode-afwijking',
			'validators' => [],
			'attribs' => [
				'placeholder' => '0',
				'title' => 'Optionele instelling om een afwijkende facuratie periode in te stellen. Voer het aantal dagen ten opzichte van het begin van de period in, de standaard waarde is 0.',
				'class' => 'forceAutoHint',
			],		
		));

        $invoice_prefs->addElement('text', 'mention', array(
            'label' => 'OVV vermelding factuur',
            'validators' => [],
            'attribs' => [
                'title' => 'Optionele text bij aanhef facturatie.',
                'class' => 'forceAutoHint',
            ],
        ));


	$invoice_prefs->addElement('select', 'invoiceprefs_bill', array(
		'label' => 'Factuur ontvangen per',
		'multiOptions'	=> array(
			'email' => 'E-mail',
			'mail' => 'Post',
			'none' => 'Geen factuur versturen'
		),
		'registerInArrayValidator' => false,
	));

	// email
//	$invoice_prefs->addElement('text', 'email_address_copy', array(
//		'label' => 'Kopie factuur (e-mail)',
//		'validators' =>  array('EmailAddress'),
//	));
	

	$invoice_prefs->addElement('textarea', 'mandate_id', array(
		'label' => 'Machtigingskenmerk',
		'attribs' => array(
			'style' => 'width: 300px; height: 40px;',
			'placeholder' => (Settings::get('use_iban_as_mandate_reference') ? 'IBAN huurder ' : 'Omniboxx klantnummer') . ' ',
			'title' => 'Machtigingskenmerk voor incasso. De standaard waarde die hiervoor gebruikt wordt is het ' . (Settings::get('use_iban_as_mandate_reference') ? 'IBAN nummer van de huurder ' : 'Omniboxx klantnummer ') . ' ',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 10, 'y': 0}"
		),
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));
 
	$invoice_prefs->addElement('text', 'mandate_date', array(
		'label' 		=> 	'Machtigingsdatum',
		'validators' 	=> 	array(),
		'placeholder' => '01-11-2009',
		'attribs' => array(
			'title' => 'Datum van ondertekening van de machtiging. Bij bestaande machtigingen is deze datum altijd 1 november 2009, de standaard datum voor SEPA incasso.',
			'class' => 'forceAutoHint datepicker',
			'hintOffset' => "{'x': 2, 'y': -3}"
		),
		'validators' => array(
			array(
				'validator'	=> 'Date',
				'options'	=> array(
					'locale' => 'nl',
				)
			)
		)
	));	

	$invoice_prefs->addElement('select', 'sepa_status', array(
		'label' => 'SEPA incasso status',
		'multiOptions' => array(
			'FRST' => 'Eerste incasso',
			'RCUR' => 'Wederkerende incasso',
			'FNAL' => 'Laatste incasso',
			'OOFF' => 'Eenmalige incasso'
		),
		'attribs' => array(
			'title' => 'Mogelijke sequentie opties ten behoeve van het genereren van een SEPA-incasso bestand.<br /><br /> Standaard staat deze waarde op "Eerste incasso" en bij het afronden van de eerste incasso wordt deze automatisch op "Wederkerende incasso" gezet.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)
	));

	if(Settings::get('software_type') != 'energy') {
		$invoice_prefs->addElement('text', 'minimum_indexation', array(
			'label' => 'Minimale indexatie',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat minimaal geindexeerd wordt', 'class' => 'forceAutoHint')

		));

		$invoice_prefs->addElement('text', 'maximum_indexation', array(
			'label' => 'Maximale indexatie',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat maximaal geindexeerd wordt', 'class' => 'forceAutoHint')

		));

		$invoice_prefs->addElement('text', 'indexation_addition', array(
			'label' => 'Indexatietoeslag',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat er extra bij het indexatie percentage gerekend wordt.', 'class' => 'forceAutoHint')
		));
	}


	$invoice_prefs->addElement('text', 'invoice_post_amount', array(
		'label' => 'Posttoeslag',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	8,
				)
			)
		)
	));

	$invoice_prefs->addElement('text', 'percentage_costs_first_formalnotice', array(
		'label' => 'WIK percentage',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	8,
				)
			)
		)
	));

    $invoice_prefs->addElement('checkbox', 'disable_invoice_reminds', array(
        'label' => 'Uitschakelen herinneringen en aanmaningen',
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	255,
                )
            )
        )
    ));
					




$comment = new Form_SubForm($this);
$comment->setAttrib('title', ucfirst(translate()->_('remarks')));
$this->addSubForm($comment, 'comment_form');

	//name
	$comment->addElement('textarea', 'comment', array(
		'label' => ucfirst(translate()->_('remarks')),
		'attribs' => array(
			'rows' => 5,
			'cols' => 50,
		)
	));

	if (Settings::get('module_reception_telephone_support_enabled')) {
	    $receptionTelephoneSupportSubForm = new Form_SubForm($this);
        $receptionTelephoneSupportSubForm->setAttrib('title', 'Receptie telefonische ondersteuning');
        $this->addSubForm($receptionTelephoneSupportSubForm, 'reception_telephone_support_form');

        $receptionTelephoneSupportSubForm->addElement('textarea', 'representation_employee_instructions', [
            'validators' => [
                [
                    'textAreaMaxTextLine',
                    false,
                    [5]
                ]
            ],
            'label' => 'Aandachtspunten medewerkers',
            'attribs' => array(
                'rows' => 5,
                'cols' => 50,
            )
        ]);
    }



$registrant_convert = new Form_SubForm($this);
$registrant_convert->setAttrib('title', ucfirst(translate()->_('save_as_tenant')));
$this->addSubForm($registrant_convert, 'registrant_convert_form');

	$registrant_convert->addElement('checkbox', 'convert_registrant_to_tenant', [
		'label' => ucfirst(translate()->_('save_as_tenant')),
	]);

	$registrant_convert->addElement('textarea', 'convert_info', [
		'label' => 'Toelichting',
		'disabled' => 'disabled',
		'value' => 'Door de bovenstaande optie aan te vinken wordt deze persoon omgezet naar een standaardhuurder. '
		.'Hierdoor is deze op alle normale plekken te vinden in het systeem en kan deze aan objecten gekoppeld worden e.d. '
		.'Let op: deze handeling is niet ongedaan te maken!',
		'attribs' => [
			'rows' => 6,
			'cols' => 45,
		],
	]);

$searchprofile = new Form_SubForm($this);
$searchprofile->setAttrib('title', ucfirst(translate()->_('search-profile')));
$searchprofile->setAttrib('style', 'display: none;');
$this->addSubForm($searchprofile, 'search_form');


	$searchprofile->addElement('text', 'rent_from', array(
		'label' => ucfirst(translate()->_('start_date')),
		'value' => date('d-m-Y'),
		'validator'	=> 'Date',
			));

	$searchprofile->addElement('text', 'period_search', array(
		'label' => ucfirst(translate()->_('period')),
		'validators' =>  array('Digits'),
	));

    $searchprofile->addElement('text', 'object_min_size', [
        'label' => ucfirst(translate()->_('square_size')),
        'validators' =>  ['Digits'],
    ]);

	$searchprofile->addElement('range', 'rent_price', array(
		'label' => ucfirst(translate()->_('budget')),
		'validators' =>  array('Digits'),
		'value' => '300,2000',
        'attribs' => [
            'multiple' => true,
            'min' => 0,
            'max' => 10000,
			'step' => 1
        ]
	));

    $searchprofile->addElement('checkbox', 'payed_account', array(
        'label' => ucfirst(translate()->_('payed_account')),
    ));

    $searchprofile->addElement('text', 'payed_account_start_date', array(
        'label' => ucfirst(translate()->_('payed_account_start_date')),
        'value' => date('d-m-Y'),
        'validator'	=> 'Date',
    ));




if (Settings::get('modules_rental')) {
    $pc = new ProjectsCities();
        if ($u->project) {
        $pc->filterForUser($u->project);
    }
    if (empty($pc->getListForSelect())) {
        $pc = new ProjectsCities();
    }
     $searchprofile->addElement('select', 'allowed_cities', [
        'multiple' => 'multiple',
        'label' => ucfirst(translate()->_('cities')),
        'registerInArrayValidator' => false,
        'validators' => [],
        'multiOptions' => $pc->getListForSelect(),
        'attribs' => ['style' => 'min-width:220px;']
    ]);
}

	// object type
		$otModel = new ObjectType();
		$types = $otModel->fetchAll($otModel->select());
		$objectTypes = array();
		foreach ($types as $type)
			$objectTypes[$type['id']] = $type['name'];

	    $searchprofile->addElement('select', 'object_type', array(
			'label' 		=> 	ucfirst(translate()->_('object_type')),
			'multioptions' 	=> 	$objectTypes,
			'registerInArrayValidator' => false,
			'validators' 	=> 	array(),
			'multiple'		=>	true
		));


$specificationSelectOptionsQuery = db()->select()->from(
    'objects_specifications_values_options',
    ['type_id', 'id', 'value']
);
$specificationSelectOptions = db()->fetchAll($specificationSelectOptionsQuery);

foreach($specifications as $spec_group_id => $spec_group){
	$spec_group_subform = new Form_SubForm($this);
	$spec_group_subform->setAttrib('title', ucfirst(translate()->_('search-profile')).' ' . $spec_group['group_title']);
	$spec_group_subform->setAttrib('style', 'display: none;');
	$this->addSubForm($spec_group_subform, 'object_specifications_' . $spec_group_id);

	foreach($spec_group['types'] as $type_id => $type) {
        require 'forms/user/includes/object_specification_values_options.php';
	}
}

$contactprofile = new Form_SubForm($this);
$contactprofile->setAttrib('title', ucfirst(translate()->_('approach')));
$contactprofile->setAttrib('style', 'display: none;');
$this->addSubForm($contactprofile, 'contact_form');
		

$contactprofile->addElement('select', 'mailing', array(
			'label' 		=> 	ucfirst(translate()->_('mailing')),
			'multioptions' 	=> [
					'daily' => ucfirst(translate()->_('daily')),
                    'weekly' => ucfirst(translate()->_('weekly')),
					'none' => ucfirst(translate()->_('none'))
            ],
			'validators' 	=> 	[],
			'multiple'		=>	[]
		));




// delete button
$attribs = $this->getOwner()->deletionAllowed && !$this->getOwner()->isAjaxRequest ? array() : array('disabled' => 'disabled') ;
$this->addElement('submit', 'delete', array(
	'label' => ucfirst(translate()->_('delete_relation')),
	'attribs' => array_merge(array(
		'formaction' => 'user/delete/id/'.$this->getOwner()->userid.'/',
	), $attribs),
));

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => ucfirst(translate()->_('save_relation'))
));

?>
