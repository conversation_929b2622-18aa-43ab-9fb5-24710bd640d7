<?

// general form options
$this->setAction('')
	->setMethod('post');

$months = DegreeDays::getMonthNames();
$year = DegreeDays::ensureValidYear(Zend_Controller_Front::getInstance()->getRequest()->getParam('year'));

foreach ($months as $en => $nl) {
	$this->addElement('text', $en, array(
		'label' => $nl,
		'autocomplete' => 'off',
		'required' 		=>	strtotime($en.' '.$year) <= strtotime('last month'),
		'validators' => array(
			array(
				'validator' => 'Callback',
				'options' => array(
					'callback' => function($value) {
						return strpos(''.$value, '.') === false;
					}
				),
			),
			array(
				'validator' => 'Float',
			),
			array(
				'validator' => 'Between',
				'options' => array(
					'min' => 0,
					'max' => $this->getOwner()->maxDegreeDaysValue,
				),
			),
		),
	));
}

$this->addElement('submit', 'bewerken', array(
	'label' => 'Opslaan'
));
?>
