<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'custom')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'customInvoice form' . (Settings::get('software_type') == 'energy' ? ' energy' : ''));


    // once made with the idea someone of low level knowledge can pre enter some data without knowing the specifics to be finalized by another colleague
	$disable_requiered_for_purchase_employee_rights = $this->getOwner()->is_purchase && InvoicesCustomsStatus::hasEmployeeRights();

	/**
	 * Select invoiceable user
	 */
	$select = new Form_SubForm($this);
	$this->addSubForm($select, 'select');

		if($this->getOwner()->type['name'] != 'custom'){
			$user_types = array(
				'contact' => 'Contactpersoon'
			);
		} else {
			$user_types = array(
				'user' => Settings::get('resident_label')  ? Settings::get('resident_label') : (Settings::get('software_type') == 'energy' ? "Bewoner" : 'Huurder'),
				'investor' => 'Belegger',
				'relation' => 'Overig'
			);
		}

		if($this->getOwner()->type['name'] == 'sets-costs'){
			$select->addElement('hidden', 'complaint', array(
				'label' => '',
				'validators' => array(),
			));

			$select->addElement('text', 'complaintfind', array(
				'label' => 'Storingsnummer',
				'validators' => array()
			));
		}
	
		//type
		$select->addElement('select', 'type', array(
			'label' => 'Type',
			'multioptions' 	=> $user_types,
			'validators' => array()
		));



        $project_validator = $disable_requiered_for_purchase_employee_rights || $this->getOwner()->type['name'] == 'purchase' ? array() : array(
                    'validator' => 'Between',
                    'options' => [
                        'min' => 1,
                        'max' => 9999999999
                    ]
        );

        if ($disable_requiered_for_purchase_employee_rights || $this->getOwner()->type['name'] == 'purchase') {
            $project_required = false;
        } else {
            $project_required = true;
        }

		//project
		$select->addElement('select', 'project', array(
			'label' => 'Project',
			'multioptions' 	=> array(
				false => 'Selecteer een project'
			),

			'validators' => array($project_validator),
            'required' => $project_required

		));

		//gebruiker
		$select->addElement('text', 'find', array(
			'label' => 'Zoeken',
            'attribs' => [
                'style' => 'width: 90%;'
            ],
			'validators' => array(),
		));

		//gebruiker
		$select->addElement($this->getOwner()->type['name'] == 'custom' ? 'select' : 'hidden', 'object', array(
			'label' => $this->getOwner()->type['name'] == 'custom' ? 'Object' : '',
			'validators' => array(),
		));		

		//gebruiker
		$select->addElement('hidden', 'has_inactive_object', array(
			'label' => '',
			'validators' => array(),
		));

		$select->addElement('hidden', 'attach', array(
			'label' => '',
			'validators' => array(),
		));

	if($this->getOwner()->type['name'] == 'purchase'){
		$purchase_status = new Form_SubForm($this);
		$this->addSubForm($purchase_status, 'purchase_status');


			$ic_status_model = new InvoicesCustomsStatus();

			$purchase_status->addElement('select', 'validation_user', array(
				'label' => 'Medewerker validatie',
				'required' => !$disable_requiered_for_purchase_employee_rights,
				'multiOptions' => [false => 'Selecteer een medewerker'] + $ic_status_model->getWithValidationRightsGroupedByCategory()
			));

			if(!InvoicesCustomsStatus::hasManagerRights() && !InvoicesCustomsStatus::hasValidationRights())
				$purchase_status->validating
					->setAttrib('disabled', true)
					->setIgnore(true);
	}

	if($this->getOwner()->type['name'] == 'purchase' && Settings::get('second_validation_employee_purchase_invoices')   ){


		$ic_status_model = new InvoicesCustomsStatus();

		$purchase_status->addElement('select', 'validation_user_second', [
			'label' => 'Tweede medewerker validatie',
			'required' => !$disable_requiered_for_purchase_employee_rights,
			'multiOptions' => [false => 'Selecteer een medewerker'] + $ic_status_model->getWithValidationRightsGroupedByCategory(),
            'validators' => [

                'NotIdentical' => [
                    'validator'	=> 'NotIdentical',
                    'options'	=> [
                        'token' => 'validation_user',
                    ]
                ]
			]
        ]);

		if(!(new InvoicesCustomsStatus)->hasManagerRights() && !(new InvoicesCustomsStatus)->hasValidationRights()) {
            $purchase_status->validating
                ->setAttrib('disabled', true)
                ->setIgnore(true);
        }
	}


	if($this->getOwner()->type['name'] == 'purchase'){
		$purchase_complaint = new Form_SubForm($this);
		$this->addSubForm($purchase_complaint, 'purchase_complaint');

		$purchase_complaint->addElement('hidden', 'complaint_id', []);

		$purchase_complaint->addElement('text', 'complaint_find', array(
			'label' => 'Melding',
			'validators' => array(),
			'placeholder' => 'Zoeken op meldingnummer',
            'attribs' => array(
                'class' => 'forceAutoHint',
                'title' => 'Wanneer u een factuur koppelt aan een melding die mininaal technisch of administratief gereed is zal deze op financieel gereed worden gezet.',
                'hintOffset' => '{x:0, y:0}'
            )
		));
	}

	/**
	 * name data
	 */
	$relation = new Form_SubForm($this);
	$this->addSubForm($relation, 'relation');
				
		//gebruiker
		$relation->addElement('text', 'find', array(
			'label' => 'Zoeken',
			'validators' => array(),
		));

		//gebruiker
		$relation->addElement('hidden', 'id', array(
			'label' => '',
			'validators' => array(),
		));



	$corporation = new Form_SubForm($this);
	$this->addSubForm($corporation, 'corporation');

		$corporation_validator = $disable_requiered_for_purchase_employee_rights ? array() : array(
			'validator' => 'Between',
			'options'   => array(1, 999999)
		);

        if (Settings::get('for_third_party')) {
            $corporationHintMessage = 'Bij het instellen of bewerken van de ontvanger wordt de juridische entiteit automatisch aangepast naar de ingestelde juridische entiteit van het project, wanneer de ontvanger een belegger is naar de ingestelde juridische entiteit beheer van het project.';
        } else {
            $corporationHintMessage = 'Bij het instellen of bewerken van de ontvanger wordt de juridische entiteit automatisch aangepast naar de ingestelde juridische entiteit van het project.';
        }

		$corporation->addElement('select', 'corporation', array(
			'label' => 'Juridische entiteit',
			'required' => !$disable_requiered_for_purchase_employee_rights,
			'multiOptions' => array(false => 'Selecteer een juridische entiteit'),
			'validators' => [
				$corporation_validator,
			],
			'attribs' => [
				'class' => 'limitWidth forceAutoHint',
				'title' => $corporationHintMessage,
				'hintOffset' => '{x:0, y:0}'
			],
		));

if (Settings::get('invoice_custom_corporation_bankaccount')) {
    $corporation->addElement('select', 'corporation_bank_accounts', [
        'label' => 'Rekening',
        'multiOptions' => [false => 'Selecteer een rekening'],
        'validators' => [
            [
                'validator' => 'Between',
                'options' => [1, 999999]
            ]
        ],
        'attribs' => [
            'class' => 'limitWidth'
        ],
        'RegisterInArrayValidator' => false,
    ]);
}

    /**
	 * Select invoiceable user
	 */
	$total = new Form_SubForm($this);
	$this->addSubForm($total, 'total');

		$total->addElement('text', 'amount', array(
			'label' => 'Totaalbedrag',
			'disabled' => true,
		));
		$total->addElement('text', 'paymentamount', array(
			'label' => 'Uit te betalen',
			'disabled' => true,
		));


		// substract from unpayed invoice
		$total->addElement('multiselect', 'substract', array(
			'label' => 'Verrekenen met',
			'multiOptions' => array(
				false => 'Geen facturen gevonden'
			),
			'disabled' => true,
			'validators' => array(),
		));

		$total->addElement('checkbox', 'subtract_for_owner', array(
			'label' => 'Verrekening voor kosten beheerder',
			'validators' => array(),
			'attribs' => array(
				'class' => 'forceAutoHint',
				'title' => 'Met deze optie wordt de verrekening op bovenstaande geselecteerde facturen niet in mindering gebracht op de eerst volgende beleggeruitkering.',
				'hintOffset' => '{x:0, y:0}'
			)
		));

		// substract from unpayed invoice
		$total->addElement('text', 'final', array(
			'label' => 'Door ontvanger te betalen / ontvangen',
			'disabled' => true,
		));


        if ($this->getOwner()->type['name'] == 'purchase') {
            $descriptionCollection = 'Voor de betaling van deze factuur wordt een bankopdracht klaargezet onder Bankverwerking -> Overboekingopdrachten. Alleen mogelijk indien bij de leverancier een IBAN is ingevuld. Wisselt u deze factuur uit naar een financieel pakket waaruit u de facturen betaald? Laat deze optie dan uitgevinkt.';
        } else {
            $descriptionCollection = 'Voor de incasso van deze factuur wordt een bankopdracht klaargezet onder Bankverwerking -> Incasso-opdrachten. Of Bankverwerking -> Overboekingopdrachten als het om een uitbetaling gaat. Alleen mogelijk indien bij de huurder een IBAN is ingevuld.';
        }


		$total->addElement('checkbox', 'collection', array(
			'label' => 'Incasso/overboeking opdracht',
			'validators' => [],
            'attribs' => [
                'class' => 'forceAutoHint',
                'title' => $descriptionCollection,
                'hintOffset' => '{x:0, y:3}',
            ]
		));

		$total->addElement('checkbox', 'investor_payout_subtract', array(
			'label' => 'Inhouden op doorstort',
			'validators' => array(),
		));

	/**
	 * General invoice vars
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		
	//title
		$general->addElement('date', 'invoicedate', [
			'label' => 'Factuurdatum',
			'value' => date('Y-m-d'),
			'validators' => [],
			'required' => !$disable_requiered_for_purchase_employee_rights,
            'attribs' => ['required' => true]
        ]);

        $numberofdays = Settings::get('days_between_invoice_and_expiredate_on_purchase_invoice') ? : 30 ;


		$is_required = !$disable_requiered_for_purchase_employee_rights && $this->getOwner()->type['name'] != 'custom';
		$general->addElement('date', 'expiredate', [
			'label' => 'Vervaldatum',
			'value' => date('Y-m-d', strtotime('+'.$numberofdays.' days')),
			'validators' => [],
			'required' => $is_required,
            'attribs' => ['required' => $is_required]
        ]);

		//title
		$general->addElement('text', 'title', array(
			'label' => $this->getOwner()->type['name'] == 'custom' ? 'Factuurtitel' : 'Factuurnummer',
			'value' => '',
            'attribs' => [
                'style' => 'width: 90%;'
            ],
			'validators' => array(),
			'required' => !$disable_requiered_for_purchase_employee_rights,
		));

		$general->addElement('text', 'mutation_number', array(
			'label' => 'Mutatienummer',
			'validators' => [
				['validator' => 'digits'],
				['validator' => 'stringLength', 'options'	=> ['min' => 4, 'max' => 6]]
			]
		));

		//description
		$general->addElement('textarea', 'description', array(
			'label' => 'Omschrijving',
			'rows' => 2,
			'validators' => array(),
		));

		$general->addElement('textarea', 'import_notifications', [
			'label' => 'Opmerkingen importeer proces',
			'rows' => 3,
			'validators' => [],
			'attribs' => [
				'class' => 'forceAutoHint',
				'title' => 'Als er meerdere juridische entiteiten in Omniboxx zijn met dezelfde administratie code controleer op juistheid van de entiteit.',
				'hintOffset' => '{x:0, y:0}'
			],
			'readonly' => true
        ]);

		//description
		$general->addElement('select', 'advance', array(
			'label' => 'Bevoorschotting',
			'validators' => array(),
			'multiOptions' => array(
				'' => '-',
				'1' => 'Ja',
				'0' => 'Nee'
			),
			'required' => $this->getOwner()->type['name'] == 'custom' && Settings::get('software_type') == 'energy',
		));

		//credit
		$general->addElement('checkbox', 'credit', array(
			'label' => 'Creditfactuur',
			'validators' => array(),
		));

		$general->addElement('select', 'investor_costs', array(
			'label' => 'Kostendrager',
			'validators' => array(),
			'multiOptions' => [0 => 'Huurder', 1 => 'Eigenaar'],
			'value' => 1
		));

		$general->addElement('checkbox', 'investor_provision_deduction', array(
			'label' => 'Opnemen in beleggerafrekening?',
			'value' => true,
			'validators' => array(),
			'attribs' => array(
				'class' => 'forceAutoHint',
				'title' => 'Aangevinkt wordt deze factuur opgenomen in de beleggerafrekening?',
				'hintOffset' => '{x:0, y:3}',
			)
		));
		
		$general->addElement('select', 'tax_rate', array(
			'label' => 'BTW percentage',
			'validators' => array(),
			'value' => $this->getOwner()->type['name'] == 'custom' ? '0': '21'
		));	

		$general->addElement('text', 'cost_center', array(
			'label' => 'Kostenplaats',
			'validators' => array(),
		));	

		foreach(array('cost_center') as $key)
			$general->$key->setAttribs(array(
				'class' => 'forceAutoHint',
				'title' => 'Waarde wordt automatisch overschreven bij het selecteren van een bewoner'
			));

	/**
	 * Credit invoice
	 */
	$credit = new Form_SubForm($this);
	$this->addSubForm($credit, 'credit');

		// credit for invoice
		$credit->addElement('multiselect', 'invoice', array(
			'label' => 'Credit voor',
			'multiOptions' => array(
				false => 'Geen facturen gevonden'
			),
			'disabled' => true,
			'validators' => array()
		));

	/**
	 * Invoice frequence
	 */
	$frequence = new Form_SubForm($this);
	$this->addSubForm($frequence, 'frequence');

		//repeat
		$frequence->addElement('select', 'repeat', array(
			'label' => 'Herhaling',
			'multioptions' 	=> array(
								'0' => 'Eenmalig',
								'1' => 'Herhalen',
							),
			'validators' => array(),
		));

		$periods = array(
			'0' => 'Maandelijks',
			'3' => 'Kwartaal',
			'52' => 'Wekelijks',
			'13' => 'Vier wekelijks',
			'1' => 'Jaarlijks'
		);

		if(Settings::get('invoice_show_fourweekly_options') !== true)
			unset($periods['13']);

		//periode
		$frequence->addElement('select', 'period', array(
			'label' => 'Periode',
			'multioptions' 	=> $periods,
			'validators' => array(),
		));

		//number
		$frequence->addElement('text', 'number', array(
			'label' => 'Aantal herhalingen',
			'validators' => array()
		));


		//number
		$frequence->addElement('date', 'start', [
			'label' => 'Factuur datum',
			'validators' => [],
            'attribs' => ['required' => $is_required]
        ]);

		$frequence->addElement('select', 'endpreset', array(
			'label' => 'Vervalperiode',
			'multioptions' 	=> array(
								'14' => '14 dagen',
								'30' => '30 dagen',
								'custom' => 'Handmatig'
							),
			'value' =>	 Settings::get('default_custom_invoice_payment_conditions') ? : '30',
			'validators' => array(),
		));

		$frequence->addElement('text', 'end', [
			'label' => 'Aantal dagen',
			'validators' => [],
			'value' => 30
		]);


	$fiscal = new Form_SubForm($this);
	$this->addSubForm($fiscal, 'fiscal');



	if (Settings::get('financial_export_system') ==  'exactonline') {
		if(Settings::get('general_company_shortname') == 'NV Zeedijk') {
            $fiscal->addElement('date', 'deferred_from', [
                'label' => 'Start',
                'validators' => [],
				'attribs' => ['required' => true]
            ]);

            $fiscal->addElement('date', 'deferred_to', [
                'label' => 'Einde',
                'validators' => [],
                'attribs' => ['required' => true]
            ]);
        }


	}
        for ($i=-1; $i < 10; $i++)
            if($year = date('Y', strtotime('-' . $i . ' years')))
                $years[$year] = $year;

        $fiscal->addElement('select', 'period', array(
            'label' => 'Boekperiode',
            'multioptions' 	=> Invoice::getFiscalPeriodOptions(),
            'value' => Settings::get('general_company_shortname') == 'debazaar' ? 'f_' . ceil(date('W')/4) : 'm_' . date('n'),
            'validators' => array(),
        ));

        $fiscal->addElement('select', 'year', array(
            'label' => 'Boekjaar',
            'multioptions' 	=> $years,
            'value' => date('Y'),
        ));






 		$original_invoice = new Form_SubForm($this);
		$this->addSubForm($original_invoice, 'original_invoice');

			$original_invoice->addElement('hidden', 'preview', array(
				'label' => 'Toegevoegd',
			));	

			$original_invoice->addElement('file', 'upload', array(
				'label' => 'Uploaden',
			));

            if ($this->getOwner()->type['name'] == 'purchase') {
               if (Settings::get('default_dont_send_invoice_attachment_on_investor_report')) {
                    $default_add_attachement_on_purchase_invoice = false;
               } else {
                    $default_add_attachement_on_purchase_invoice = true;
                }
            } else {
                $default_add_attachement_on_purchase_invoice = false;
            }


			$original_invoice->addElement('checkbox', 'send', array(
				'label' => $this->getOwner()->type['name'] == 'purchase' ? 'Bijsluiten beleggersafrekening' : 'Meesturen',
				'value' => $default_add_attachement_on_purchase_invoice,
				'validators' => [
   					['validator' => 'PdfUpload']
   				],
                'attribs' => [
				'class' => 'forceAutoHint',
				'title' => 'Hiermee wordt de geuploade pdf als bijlage met de factuur en beleggersafrekening meegestuurd.',
				'hintOffset' => '{x:0, y:3}',
			    ]
			));


	$finalizing = new Form_SubForm($this);
	$this->addSubForm($finalizing, 'finalizing');

		$finalizing->addElement('select', 'method', [
			'label' => 'Methode',
            'multioptions' 	=> [
                'normal' => 'Handmatige verzending',
                'add_rows_to_invoicerun' => 'Regels toevoegen aan factuurrun',
            ],
            'attribs' => [
            	'disabled' => 'disabled',
                'class' => 'forceAutoHint',
                'title' => '
					<b>Handmatige verzending:</b><br />De factuur kan normaal definitief gemaakt worden door deze in de handmatige facturen te selecteren voor verzending.<br /><br />
					<b>Regels toevoegen aan factuurrun:</b><br />Er wordt geen handmatige factuur gemaakt, maar na het definitief maken van deze handmatige factuur worden de regels toegevoegd aan de factuur in de factuurrun voor de geselecteerde huurder.
				',
            ]
        ]);


		$finalizing->addElement('select', 'invoice_run', [
			'label' => 'Factuurrun',
			'multioptions' 	=> [
				0 => 'Selecteer een factuurrun'
			],
            'RegisterInArrayValidator' => false,
		]);

		$finalizing->addElement('checkbox', 'done', [
			'label' => 'Definitief maken',
            'attribs' => [
                'class' => 'forceAutoHint',
                'title' => 'Deze handmatige factuur definitief maken, na het opslaan worden de regels aan de run toegevoegd.',
            ]
		]);
//submit
	$this->addElement('submit', 'save', array(
		'label' => 'Opslaan'
	));

?>
