<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'adjustment')
			->setAttrib('class', 'adjustment form');



	//name
	$this->addElement('text', 'name', array(
		'label' => 'Interne naam uitzondering',
		'required' => true
	));

	//date
	$this->addElement('textarea', 'description', array(
		'label' => 'Omschrijving op factuur',
		'required' => true,
		'cols' => 50,
		'rows' => 	8
	));
	
	//type
	$this->addElement('select', 'type', array(
		'label' => 'Type',
		'multiOptions'	=> array(
			'remark' => 'Opmerking',
			'normal' => 'Aanpassing'
		)
	));

	//type
	$this->addElement('select', 'on_type', array(
		'label' => 'Toepassen op',
		'multiOptions'	=> array(
			'user_object' => 'Huidige huurder van het object',
			'object' => 'Object'
		)
	));
	
	//amount
	$this->addElement('select', 'amount_or_percentage', array(
		'label' => 'Bedrag',
		'multiOptions'	=> array(
			'amount' => 'Vast bedrag',
			'percentage' => 'Percentage periodieke factuurbedrag'
		)
	));

	//amount
	$this->addElement('numberinput', 'amount', array(
		'label' => 'Vast bedrag',
		'attribs' => [
			'title' => 'Bedrag exclusief BTW',
			'class' => 'forceAutoHint',
		],		
	));
	
	$this->addElement('text', 'ledger', [
		'label' => 'Grootboekrekening',
        'required' => strlen(Settings::get('financial_export_system')) > 0
    ]);


	$this->addElement('select', 'tax', [
		'label' => 'BTW. percentage',
		'multioptions' 	=> [
			'0' => '0%',
			'1' => '6%',
			'3' => '9%',
			'2' => '21%'
		],
	]);

	//percentage
	$this->addElement('text', 'percentage', array(
		'label' => 'Percentage',
		'attribs' => [
			'title' => 'Percentage van het totale periodieke factuurbedrag (exclusief BTW), eventuele andere uitzonderingen worden niet meegerekend.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 20, 'y': 0}",
		]
	));
	
	//substract
	$this->addElement('select', 'substract', array(
		'label' 		=> 	'Actie',
		'multioptions' 	=> array(
								'0' => 'Optellen',
								'1' => 'Aftrekken' 
							),
		'validators' 	=> 	array(),
	));

	$this->addElement('select', 'endnote', array(
		'label' => 'Bevoorschotting',
		'validators' => array(),
		'multiOptions' => array(
			'' => '-',
			'1' => 'Ja',
			'0' => 'Nee'
		),
		'required' => Settings::get('software_type') == 'energy',
	));
		

	//number
	$this->addElement('text', 'number', array(
		'label' 		=> 	'Aantal keer toepassen',
		'validators' 	=> 	array(),
		'value' =>			1
	));	

	//applied
	$this->addElement('text', 'applied', array(
		'label' 		=> 	'Aantal keer toegepast',
		'validators' 	=> 	array(),
		'value' =>			1,
		'disabled' =>		true,
		'ignore' =>			true
	));
	
	//runs
	$this->addElement('select', 'run', array(
		'label' 		=> 	'Vanaf run',
		'validators' 	=> 	array(),
	));	
		
	//submit
	$this->addElement('submit', 'edit', array(
		'label' => 'Opslaan'
	));

?>