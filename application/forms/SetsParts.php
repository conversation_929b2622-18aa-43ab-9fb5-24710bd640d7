<?
//general form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'sets')
	->setAttrib('class', 'sets profile form');

/**
 * General usage profile data
 */
$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemeen');
$this->addSubForm($general, 'general');

	$general->addElement('text', 'name', array(
		'label' 		=> 	'Naam',
		'validators' 	=> 	array(),
		'attribs' 		=> array(
			'placeholder' => 'Na<PERSON> van het onderdeel',
			'autocompleter' => 'sets/get-names/'
		)
	));

	$general->addElement('text', 'brand', array(
		'label' 		=> 	'Merk',
		'validators' 	=> 	array(),
		'attribs' 		=> array(
			'autocompleter' => 'sets/get-brands/'
		)
	));

	$general->addElement('text', 'product_code', array(
		'label' 		=> 	'Type',
		'validators' 	=> 	array(),
		'attribs' => array(
			'title' => 'Bijvoorbeeld typenummer',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'autocompleter' => 'sets/get-product-codes/',
			'autocompleter_options' => json_encode(array('postOtherFormFields' => true))
		)
	));

	$general->addElement('text', 'number', array(
		'label' 		=> 	'Aantal',
		'validators' 	=> 	array()
	));

$details = new Form_SubForm($this);
$details->setAttrib('title', 'Eigenschappen');
$this->addSubForm($details, 'details');

	$details->addElement('text', 'build_date', array(
		'label' 		=> 	'Fabricagedatum',
		'validators' 	=> 	array(),
		'class'			=> 	'DatePicker'
	));

	$details->addElement('text', 'warranty', array(
		'label' 		=> 	'Garantie (maanden)',
		'validators' 	=> 	array()
	));

	$details->addElement('select', 'demarcation', array(
		'label' => 'Demarcatie',
		'multiOptions' => array(1 => 'Ja', 0 => 'Nee')
	));

//submit
$this->addElement('submit', 'meters', array(
	'label' => 'Opslaan'
));