<?

$contractRelationId = isset($this->getOwner()->contractRelationId) && intval($this->getOwner()->contractRelationId) > 0?
	$this->getOwner()->contractRelationId: null;

$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
$this->getOwner()->contractId: null;

$contractType = Contracts::getTypeClassname($contractId);

$isSignedUo = !!$this->getOwner()->isSignedUo;

//general form options
$this->setAction('contract-relation/edit/'.(isset($contractRelationId)? 'id/'.$contractRelationId.'/': '') . (isset($contractId)? 'contract-id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('id', 'contactRelationUoForm')
		->setAttrib('class', 'form');



$contractRelationUoForm = new Form_SubForm($this);
$contractRelationUoForm->setAttrib('title', 'Algemeen');
$this->addSubForm($contractRelationUoForm, 'contract_relation_uo_form');

	$contractRelationUoForm->addElement('text', 'object_selector', array(
		'label' => 'Object',
		'required' => true,
		($isSignedUo? 'readonly': 'notreadonly') => 'readonly',
		'value' => (intval($this->getOwner()->preselects_object_id) > 0? Address::buildname($this->getOwner()->preselects_object_id, 'object'): null),
		'autocomplete' => 'off',
		'attribs' => array(
			'title' => 'Zoeken op adres, de zoekresultaten bevatten enkel beschikbare objecten.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'transaction/find-objects/',
			'autocompleter_options' => Zend_Json::encode(array(
				'hiddenElementId' => 'contract_relation_uo_form-object',
				'postData' => [
					'contractType' => $contractType
				],
				'postOtherFormFields' => true
			), false, array('enableJsonExprFinder' => true))
		)
	));

	$contractRelationUoForm->addElement('text', 'from', [
		'label' => 'Begindatum',
		'required' => !$isSignedUo,
		($isSignedUo? 'disabled': 'notdisabled') => 'disabled',
		'value' => $this->getOwner()->preselects_from,
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$contractRelationUoForm->addElement('hidden', 'uo_warning_override');

	$contractRelationUoForm->addElement('text', 'period_formula', [
		'label' => 'Verlengformule',
	]);

	$contractRelationUoForm->addElement('hidden', 'period_formula_explain', [
	]);

	$contractRelationUoForm->addElement('text', 'notice_period', [
		'label' => 'Opzegtermijn (mnd)',
	]);

	$contractRelationUoForm->addElement('text', 'till', [
		'label' => 'Eindigt op',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$contractRelationUoForm->addElement('checkbox', 'delete_till', [
		'label' => 'Einddatum verwijderen',
		'attribs' => [
			'class' => 'forceAutoHint',
			'title' => 'Door dit vinkje in te schakelen en de pagina op te slaan kan de einddatum worden verwijderd.',
		],
	]);

	$contractRelationUoForm->addElement('checkbox', 'finalenddate', [
		'label' => 'Einddatum definitief',
		'attribs' => [
			'class' => 'forceAutoHint',
			'title' => 'Stel dit veld alleen in als zeker is dat de huurder het object op deze datum definitief zal verlaten.',
		],
	]);	

if($isSignedUo){
	if(!$this->getOwner()->needsLastInvoice){
		$contractRelationUoForm->addElement('html', 'text', array(
			'label' => '',
			'html' => '<a class="roundedButton icon inactive">Eindnota reeds gemaakt voor dit object</a>'
		));	
	} else {

		$uo_select = db()->select()->from('users_objects')
			->where('role = ?', 'normal')
			->order('from DESC')
			->where('id = ?', $this->getOwner()->uoid);

		$uo_row = db()->fetchRow($uo_select);

		if($uo_row['till'])
			$contractRelationUoForm->addElement('html', 'text', array(
				'label' => '',
				'html' => '<a target="_blank" class="roundedButton icon pdf" href="user/make-last-invoice/contract/' . $this->getOwner()->contract->id . '/uoid/' . $this->getOwner()->uoid . '">Eindnota maken voor dit object</a>'
			));	
		else
			$contractRelationUoForm->addElement('html', 'text', array(
				'label' => '',
				'html' => '<a class="roundedButton icon inactive">Na opslaan kan eindnota gemaakt worden.</a>'
			));	

	}
}

	$contractRelationUoForm->addElement('hidden', 'object', array(
		'required' => true,
		'value' => $this->getOwner()->preselects_object_id,
	));

	$contractRelationUoForm->addElement('hidden', 'objects_components_versions_id', array());	

	$contractRelationUoForm->addElement('hidden', 'is_signed_uo', [
		'value' => ((int) $isSignedUo),
	]);	

	$contractRelationUoForm->addElement('hidden', 'has_non_concept_rights', [
		'value' => ((int) acl()->hasRole('manager') || acl()->hasRole('admin')),
	]);


if ( $contractType != 'ContractsPeriodical') {


$depositForm = new Form_SubForm($this);
$depositForm->setAttrib('title', 'Waarborgbedrag');
$this->addSubForm($depositForm, 'deposit_form');

	$depositForm->addElement('text', 'deposit_object_amount', [
		'label' => 'Ingesteld op object',
		'disabled' => true,
	]);

	$depositForm->addElement('text', 'deposit_amount', [
		'label' => 'Waarborgsom contract',
		'validators' => [
			[
				'validator'	=> 'Float',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'placeholder' => '0,00',
		'value' => null,
	]);

	$depositForm->addElement('text', 'deposit_payed', [
		'label' => 'Waarborgsom betaald',
		'validators' => [
			[
				'validator'	=> 'Float',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'placeholder' => '0,00',
		'value' => null,
	]);

if(Settings::get('for_third_party')):  
	$depositForm->addElement('checkbox', 'deposit_payed_to_broker', [
		'label' => 'Betaald aan belegger',
	]);
endif;
	$depositForm->addElement('select', 'deposit_type', [
		'label' => 'Type waarborg',
		'multiOptions'	=> [
			'cash' 				=> 'Gestort',
			'bank_guarantee' 	=> 'Bank Garantie',
			'concern_guarantee' => 'Concern Garantie',
		],
	]);

}

//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
