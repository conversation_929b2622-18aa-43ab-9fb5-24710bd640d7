<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general');

	$general->addElement('text', 'name', array(
		'label' => 'Naam',
		'required' => true,
	));

	$general->addElement('text', 'date', [
		'label' => 'Datum',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);	

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Opslaan'
));