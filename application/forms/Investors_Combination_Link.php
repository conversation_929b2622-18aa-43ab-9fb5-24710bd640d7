<?
$id_param_string = $this->getOwner()->id ? 'id/' .  $this->getOwner()->id . '/' : '';
$investor_combination_id = $this->getOwner()->investor_combination_id;

if ($this->getOwner()->id) {
    $investorCombinationLine = (new Investors_Combination_Link())->fetchRowById($this->getOwner()->id);
    $investor = (new Investors())->fetchRowById($investorCombinationLine['investor_id']);
}

$this->setAction('investor_combination_link/edit/investor_combination_id/' . $investor_combination_id . '/' . $id_param_string)
	->setMethod('post')
	->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Belegger toevoegen');

	$this->addSubForm($general, 'general');

	$general->addElement('hidden', 'investor_combination_id', [
		'value' => $investor_combination_id
	]);

	$general->addElement('hidden', 'investor_id', [
		'value' => isset($investorCombinationLine) ?  $investorCombinationLine['investor_id'] : null
	]);

	$general->addElement('text', 'investor', [
		'label' => 'Belegger',
		'attribs' => [
			'title' => 'Zoek belegger',
			'class' => 'forceAutoHint',
			'autocompleter' => 'investor/find',
			'autocompleter_options' => Zend_Json::encode(
				['hiddenElementId' => 'general-investor_id']
				//false,
				//['enableJsonExprFinder' => true]
			)
		],
        'value' => isset($investor) ? $investor['name'] : null
	]);

    $totalCurrentPercentage = (new \Investors\Combination\Service\CalculateCurrentPercentageService())->calculate($investor_combination_id, $this->getOwner()->id);
    $max = 100;
    $percentageRemainder = $max - $totalCurrentPercentage;

	$general->addElement('text', 'percentage', [
		'label' => 'Percentage',
        'validators' => [
            [
                'validator' => 'Float',
            ],
            [
                'validator' => 'Between',
                'options' => [
                    'min' => 1,
                    'max' => $totalCurrentPercentage == 0 ? $max : $percentageRemainder
                ]
            ]
        ]
	]);

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Belegger koppeling opslaan'
));