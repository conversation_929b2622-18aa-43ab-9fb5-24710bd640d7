<?
$contractDiscountId = isset($this->getOwner()->contractDiscountId) && intval($this->getOwner()->contractDiscountId) > 0?
	$this->getOwner()->contractDiscountId: null;
$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
	$this->getOwner()->contractId: null;
$discountRow = isset($this->getOwner()->discount_row) && $this->getOwner()->discount_row !== false ?
	$this->getOwner()->discount_row: false;
$contractRow = isset($this->getOwner()->contractRow) && $this->getOwner()->contractRow !== false ?
	$this->getOwner()->contractRow: false;
;

$crModel = new ContractsRental();
$cRelModel = new ContractRelations();
$cdModel = new ContractDiscounts();
$contractDiscount = $contractDiscountId? $cdModel->getById($contractDiscountId): null;


//general form options
$this->setAction('contract-discount/edit/'.(isset($contractDiscountId)? 'id/'.$contractDiscountId.'/': '') . (isset($contractId)? 'contract-id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('id', 'contactDiscountForm')
		->setAttrib('class', 'form');


$contractDiscountForm = new Form_SubForm($this);
$contractDiscountForm->setAttrib('title', 'Algemeen');
$this->addSubForm($contractDiscountForm, 'contract_discount_form');

	$objs = (array) $cRelModel->getObjects($contractId);

	$contractDiscountForm->addElement('hidden', 'contractType', ['value' => $contractRow['type']]);

	$contractDiscountForm->addElement('select', 'object', [
		'label' => 'Object',
		'multiOptions'	=> [nullValue() => 'Alle objecten binnen contract'] + $objs,
	]);


	if($contractRow['type'] == 'transient'){
		$component_select = db()->select()
			->from(['c' => 'components'], ['id', 'name'])
			->joinLeft(['pr' => 'pricelists_rows'], 'pr.component = c.id', false)
			->where('pr.id IS NOT NULL')
			->group('c.id')
			->order('c.name');

		$components = ['Weekendkaart' => db()->fetchPairs($component_select)];
	} else {

		$object_components = array();

		if($discountRow->object > 0)
			foreach(Components::getCurrentComponentsForObject($discountRow->object) as $object_component)
				$object_components[$object_component['id']] = $object_component['label'];
		$components = ['Omzet componenten' => $object_components];
	}

	$contractDiscountForm->addElement('select', 'component', [
		'label' => 'Component',
		'RegisterInArrayValidator' => false,
		'multiOptions'	=> $components,
	]);	

	$contractDiscountForm->addElement('text', 'description', [
		'label' => 'Omschrijving',
	]);

	foreach (ContractDiscounts::$types as $type => $settings)
		$typeLabels[$type] = $settings['label'];

	$contractDiscountForm->addElement('select', 'type', [
		'label' => 'Type',
		'multiOptions'	=> (array) $typeLabels,
	]);

	$contractDiscountForm->addElement('text', 'from', [
		'label' => 'Van',
		'required' => true,
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$contractDiscountForm->addElement('text', 'till', [
		'label' => 'Tot',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$contractDiscountForm->addElement('hidden', 'current_component', [
	]);	



	$contractDiscountAmountForm = new Form_SubForm($this);
	$contractDiscountAmountForm->setAttrib('title', 'Bedrag');
	$this->addSubForm($contractDiscountAmountForm, 'contract_discount_amount_form');

		$contractDiscountAmountForm->addElement('select', 'percentage_or_amount', [
			'label' => 'Type korting',
			'multiOptions'	=> array('amount' => 'Vast bedrag', 'percentage' => 'Percentage')
		]);	

		$contractDiscountAmountForm->addElement('text', 'percentage', [
			'label' => 'Percentage',
			// TODO: validators hier op!
		]);

		$contractDiscountAmountForm->addElement('text', 'amount', [
			'label' => 'Bedrag',
			// TODO: validators hier op!
		]);



//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
