<?
 //general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');

$BrancheLocations = new Form_SubForm($this);
$BrancheLocations->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($BrancheLocations, 'branche_locations_form');

$BrancheLocations->addElement('text', 'description', array(
		'label' => 'Vestigingsnaam',
		'required' => true,
	));

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Vestiging opslaan'
));

?>