<?

$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
	$this->getOwner()->contractId: null;

$contractsModel = new Contracts();
$contract = $contractId? $contractsModel->getById($contractId): null;

$this->getOwner()->view->contractType = 'ContractsFramework';

//general form options
$this->setAction('contract/edit/'.(isset($contractId)? 'id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');

$projectLib = new Project();
$projs = (array) $projectLib->getList();
foreach ($projs as $project)
	$projects[$project['id']] = $project['name'];


$contractForm = new Form_SubForm($this);
$contractForm->setAttrib('title', 'Servicecontract');
$this->addSubForm($contractForm, 'contract_form');

	$contractForm->addElement('select', 'project', [
		'label' => 'Project',
		'required' 		=>	true,
		'multiOptions'	=> (array) $projects,
	]);

	$contractForm->addElement('text', 'from', [
		'label' => 'Begindatum',
		'required' 		=>	true,
		'attribs' => [
			'class' => 'DatePicker',
			'datepicker_options' => json_encode([
				'yearRange' => 100,
				'yearStart' => date('Y') - 25,
			]),
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$contractForm->addElement('text', 'till', [
		'label' => 'Einddatum',
		'required' 		=>	true,
		'attribs' => [
			'class' => 'DatePicker',
			'datepicker_options' => json_encode([
				'yearRange' => 100,
				'yearStart' => date('Y') - 25,
			]),
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$contractForm->addElement('text', 'amount_remaining', [
		'label' => 'Restwaarde',
		'validators' => [
			[
				'validator'	=> 'Float',
			],
			[
				'validator'	=> 'GreaterThan',
				'options'	=> [
					'min' => 0,
				]
			],
		],
	]);

$memoForm = new Form_SubForm($this);
$memoForm->setAttrib('title', '&nbsp;');
$this->addSubForm($memoForm, 'memo_form');

	$memoForm->addElement('textarea', 'memo', [
		'label' => 'Memo',
		'attribs' => [
			'rows' => 15,
			'cols' => 30,
		],
		'value' => '',
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
