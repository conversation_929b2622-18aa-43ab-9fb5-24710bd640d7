<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');

$objectInactiveReason = new Form_SubForm($this);
$objectInactiveReason->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($objectInactiveReason, 'object_inactive_reason_form');

$objectInactiveReason->addElement('text', 'reason', [
    'label' => 'Reden',
    'required' => true,
]);

$objectInactiveReason->addElement('checkbox', 'is_default', [
    'label' => 'Markeren als standaardwaarde'
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Reden opslaan'
]);
