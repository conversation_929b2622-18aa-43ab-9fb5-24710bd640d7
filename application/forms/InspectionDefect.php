<?php

$model = new InspectionDefect();

$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general_form');

$general->addElement('text', 'name_nl', [
	'label' => 'Naam (Nederlandstalig)',
]);

$general->addElement('text', 'name_en', [
	'label' => 'Naam (Engelstalig)',
]);

if (Settings::get('software_french_enabled')) {
	$general->addElement('text', 'name_fr', [
		'label' => 'Naam (Franstalig)',
	]);
}

$price = new Form_SubForm($this);
$price->setAttrib('title', 'Prijs');
$this->addSubForm($price, 'price_form');	

	$price->addElement('select', 'unit', [
		'label' => 'Prijs Eenheid',
		'multiOptions' => $model->unit_labels
	]);

	$price->addElement('select', 'cost_owner', [
		'label' => 'Kostendrager',
		'multiOptions' => $model->cost_owner_labels
	]);

	$price->addElement('numberInput', 'price', [
		'label' => 'Bedrag',
		'attribs' => [
			'title' => 'Prijs per eenheid, exclusief BTW',
			'class' => 'forceAutoHint'
		]
	]);	


$attach = new Form_SubForm($this);
$attach->setAttrib('title', 'Koppelen');
$this->addSubForm($attach, 'attach_form');

	$select = db()->select()
		->from(['ost' => 'objects_spaces_types'], ['space' => 'name_nl', 'space_id' => 'id'])
		->joinLeft(['iit' => 'inspection_item_type'], 'iit.object_spaces_type = ost.id', ['id', 'item' => 'title_nl'])
		->order('ost.name_nl ASC')
		->order('iit.order ASC')
		->where('iit.id IS NOT NULL');

$previous_space_id = false;
	foreach(db()->fetchAll($select) as $item){
		if($previous_space_id !== $item['space_id'])
			$attach->addElement('html', 'space_title_' . $item['space_id'], [
				'html' => '<h4>' . $item['space'] . '</h4>',
			]);

		$attach->addElement('checkbox', 'item_type_' . $item['id'], [
			'label' => $item['item'],
		]);

		$previous_space_id = $item['space_id'];
	}	

$this->addElement('submit', 'aanmaken', [
	'label' => 'Defect opslaan'
]);
