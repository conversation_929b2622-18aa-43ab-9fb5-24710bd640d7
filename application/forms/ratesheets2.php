<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'rate')
			->setAttrib('class', 'rate form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//date
		$general->addElement('text', 'date', array(
			'label' => 'Datum',
			'validators' => array(
			),
		));

		// title
		$general->addElement('text', 'name', array(
			'label' => 'Naam tariefblad',
			'validators' => array(
			),
		));

		//
		$general->addElement('text', 'emptyrate', array(
			'label' => 'Percentage bij leegstand',
			'validators' => array(
			),
		));



	$override = new Form_SubForm($this);
	$this->addSubForm($override, 'override');

		// warm override
		$override->addElement('checkbox', 'warmoverride', array(
			'label' => 'Vastrecht warmte',
			'validators' => array(
			),
		));

		// cold override
		$override->addElement('checkbox', 'coldoverride', array(
			'label' => 'Vastrecht koude',
			'validators' => array(
			),
		));

	$this->addElement('text', 'rates', array(
		'label' => 'Rates',
		'validators' => array(
		),
	));

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => 'Opslaan'
	));

?>