<?
    $usersModel = new Users();
    $internal_users = $usersModel->getInternalUsersShort();
	$objectgroups = (array) db()->fetchPairs( db()->select()->from('objectgroup', array('id', 'description')) );

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'settings')
			->setAttrib('class', 'settings form');

	$software = new Form_SubForm($this);
	$software->setAttrib('title', 'Software');
	$this->addSubForm($software, 'software');
	
		$software->addElement('select', 'software_type', array(
			'label' => 'Software type',
			'multiOptions' => array(
				'real estate' => 'OmniBoxx',
				'support' => 'Support'
			)
		));


		$software->addElement('select', 'stationary_logo', array(
			'label' => 'Logo briefpapier',
			'multiOptions' => array(
				'top' => 'Boven',
				'right' => 'Rechts',
				'left' => 'Links'
			)
		));

 
		$software->addElement('select', 'software_country', array(
			'label' => 'Land',
			'multiOptions' => array(
				'nl' => 'Nederland',
				'be' => 'Belgie'
			)
		));

		$software->addElement('select', 'software_language', array(
			'label' => 'Taal',
			'multiOptions' => array(
				'nl' => 'Nederlands',
				'en' => 'Engels'
			)
		));
		
		$software->addElement('checkbox', 'search_default_soundex', array(
			'label' => 'Standaard fonetisch zoeken',
		));	

		$software->addElement('checkbox', 'meters', array(
			'label' => 'Gebruikersprofielen en meters',
		));	
		
		$software->addElement('text', 'software_logo', array(
			'label' => 'Afwijkend software logo',
		));
		$software->addElement('text', 'number_licensed_objects', array(
			'label' => 'Aantal objecten licentie',
		));
		$software->addElement('text', 'number_licensed_users', array(
			'label' => 'Aantal gebruikers licentie',
		));	
		$software->addElement('text', 'number_licensed_personal_page', array(
			'label' => 'Aantal logins persoonlijk pagina licentie',
		));		

		$software->addElement('text', 'start_date_license', array(
			'label' => 'Ingangsdatum licentie',
		));	

		$software->addElement('checkbox', 'general_ip_unrestricted', array(
			'label' => 'IP-restrictie uitgeschakeld',
		));	
		$software->addElement('checkbox', 'for_third_party', array(
			'label' => 'Beheer voor derden?',
		));	

		$software->addElement('checkbox', 'hide_financial_for_technical', array(
			'label' => 'Verberg financiele info voor techniek?',
		));	


		$software->addElement('checkbox', 'use_firstname_insystem', array(
			'label' => 'Gebruik voornamen ipv initialen?',
		));	

		$software->addElement('checkbox', 'address_right_on_letters', array(
			'label' => 'NAW rechts op envelop?',
		));	

		$software->addElement('checkbox', 'modules_user_project', array(
			'label' => 'Vestigingsomgeving module',
		));	

		$software->addElement('checkbox', 'modules_contracts', array(
			'label' => 'Contracten functionaliteit',
		));

	$tenantLogin = new Form_SubForm($this);
	$tenantLogin->setAttrib('title', 'Persoonlijke pagina module');
	
	$this->addSubForm($tenantLogin, 'tenantLogin');
	
		$tenantLogin->addElement('checkbox', 'modules_tenantLogin', array(
			'label' => 'Persoonlijke pagina',
		));	

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_multilanguage', array(
			'label' => 'Persoonlijke pagina meertalig',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_checkbeforesign', [
			'label' => "'Contract ondertekenen toegestaan' vinkje bij huurders staat standaard uit, en verplichte ".
				'document(en) moeten ingevoerd zijn voordat getekend mag worden.',
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_firstnamebasis', array(
			'label' => 'Tutoyeren in digitaal contracteren en persoonlijke pagina',
		));		

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_contact_writing', array(
			'label' => 'Verberg het post methode van contact leggen op de contactpagina',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_payment_type', array(
			'label' => 'Betalingsmethode verbergen',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_collection_type', array(
			'label' => 'Geen automatische incasso aanbieden',
		));


		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_invoice_payment_status', array(
			'label' => 'Betaalstatus verbergen',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_document_upload', array(
			'label' => 'Documenten upload ingeschakeld',
		));


		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_maintainance', array(
			'label' => 'Onderhoudsmodus',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_no_indexed_ratesheet_values', array(
			'label' => 'Geindexeerde huurbedragen negeren',
		));

		$tenantLogin->addElement('text', 'modules_tenantLogin_custom_dummy_login_url', array(
			'label' => 'Dummy login url',
		));


	$roomselector = new Form_SubForm($this);
	$roomselector->setAttrib('title', 'Roomselector');
	
	$this->addSubForm($roomselector, 'roomselector');

		$roomselector->addElement('select', 'modules_tenantLogin_roomselector_objectgroups', array(
			'label' => 'Objectgroepen waarbinnen geselecteerd mag worden',

			'multiple' => true,

			'RegisterInArrayValidator' => false,

			'multiOptions' => $objectgroups,
		));

		$roomselector->addElement('text', 'modules_tenantLogin_roomselector_registrant_start_date', array(
			'label' => 'Standaard ingangsdatum voor registreerders',
			'title' => 'dd/mm/yyyy',
			'class' => 'forceAutoHint',
		));

		$roomselector->addElement('checkbox', 'modules_tenantLogin_roomselector_no_object_selection', array(
			'label' => 'Geen object selecteren via roomselector',
		));

	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Financieel');
	
	$this->addSubForm($financial, 'financial');

		$financial->addElement('checkbox', 'financial_tax_foreign_rates_enabled', array(
			'label' => 'Internationale btw tarieven',
		));

		$financial->addElement('select', 'financial_export_system', array(
			'label' => 'Financiele koppeling ',

			'multiOptions' => array(
				'none' => 'Geen',
				'exactonline' => 'Exact Online',
				'exactglobe' => 'Exact Globe',
				'accountview' => 'Accountview',
				'mamut' => 'Mamut',
				'accountview_energy' => 'Accountview energie',
				'twinfield' => 'Twinfield'
			),

			'RegisterInArrayValidator' => false,
		));

		$financial->addElement('checkbox', 'financial_export_group_collection', array(
			'label' => 'Gegroepeerde automatische incasso boeking',
		));

		$financial->addElement('text', 'invoice_post_amount', array(
			'label' => 'Toeslag verzending per post',
		));

		

		$financial->addElement('text', 'financial_export_sales_journalcode', array(
			'label' => 'Verkoopboek',
		));
		$financial->addElement('text', 'financial_export_sales_debtcode', array(
			'label' => 'Debiteuren',
		));

		$financial->addElement('text', 'financial_export_sales_journalpurchasecode', array(
			'label' => 'Inkoopboek',
		));
		$financial->addElement('text', 'financial_export_sales_credcode', array(
			'label' => 'Crediteuren',
		));

		$financial->addElement('text', 'financial_export_investor_no_payout', array(
			'label' => 'Doorstort grootboek'
		));
		// VAT purchase 
		$financial->addElement('text', 'financial_export_sales_vathigh', array(
			'label' => 'BTW hoog',
		));
		$financial->addElement('text', 'financial_export_sales_vathighcode', array(
			'label' => 'BTW Code Hoog',
		));		
		$financial->addElement('text', 'financial_export_sales_vatlow', array(
			'label' => 'BTW laag',
		));
		$financial->addElement('text', 'financial_export_sales_vatlowcode', array(
			'label' => 'BTW Code Laag',
		));
		// VAT purchase 
		$financial->addElement('text', 'financial_export_purchase_vathigh', array(
			'label' => 'BTW hoog inkoop',
		));
		$financial->addElement('text', 'financial_export_purchase_vathighcode', array(
			'label' => 'BTW Code hoog inkoop',
		));		
		$financial->addElement('text', 'financial_export_purchase_vatlow', array(
			'label' => 'BTW laag inkoop',
		));
		$financial->addElement('text', 'financial_export_purchase_vatlowcode', array(
			'label' => 'BTW Code laag inkoop',
		));



		$financial->addElement('text', 'financial_export_depositcode', array(
			'label' => 'Grootboek waarborg',
		));
		$financial->addElement('text', 'financial_export_investorprofit_code', array(
			'label' => 'Grootboek omzet beleggerprovisie',
		));

		$financial->addElement('text', 'financial_export_investorprofit_cost_center', array(
			'label' => 'Kostenplaats omzet beleggerprovisie',
		));

		$financial->addElement('text', 'financial_export_investorprofit_user_code', array(
			'label' => 'Grootboek omzet beleggerprovisie huurder',
		));

		$financial->addElement('text', 'financial_export_rental_code', array(
			'label' => 'Grootboek omzet huur (tijdelijke oplossing)',
		));

		$financial->addElement('text', 'financial_penalty_prioritize_penalty_payment', array(
			'label' => 'Betaling eerst afboeken op boete',
		));

		$financial->addElement('text', 'financial_export_twinfield_username', array(
			'label' => 'Twinfield gebruikersnaam',
		));

		$financial->addElement('text', 'financial_export_twinfield_password', array(
			'label' => 'Twinfield wachtwoord',
		));

		$financial->addElement('text', 'financial_export_twinfield_company', array(
			'label' => 'Twinfield administratie naam',
		));

		$financial->addElement('text', 'financial_export_twinfield_last_import_date', array(
			'label' => 'Twinfield laatste datum waarop facturen zijn ingelezen',
			'attribs' => array(
				'title' => 'Formaat = yyyy-mm-dd, wordt door de code zelf aangepast bij het inlezen. Elke dag om 6uur in de ochtend wordt dit opgestart. Na het afronden wordt de datum automatisch bijgewerkt.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)		
		));


		
	$company = new Form_SubForm($this);
	$company->setAttrib('title', 'Bedrijf');
	
	$this->addSubForm($company, 'company');
	
		$company->addElement('text', 'general_company_number_length', array(
			'label' => 'Nummer lengte',
			'attribs' => array(
				'title' => 'Afwijkende lengte voor B.V. nummers t.b.v. klantnummer en factuurnummer, als deze waarde niet ingevoerd is dan is de standaard waarde 2.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)			
		));

		$company->addElement('text', 'general_company', array(
			'label' => 'Bedrijfsnaam',
		));
		
		$company->addElement('text', 'general_company_shortname', array(
			'label' => 'Korte bedrijfsnaam (voor naam van template folders e.d.)',
		));
		
		$company->addElement('text', 'general_phone', array(
			'label' => 'Telefoonnummer',
		));
		
		$company->addElement('text', 'general_fax', array(
			'label' => 'Fax',
		));
		
		$company->addElement('text', 'general_email', array(
			'label' => 'E-mail',
		));
		
		$company->addElement('text', 'general_website', array(
			'label' => 'Alg. Website',
		));
		
		$company->addElement('text', 'general_website_en', array(
			'label' => 'Alg. Website (engelstalig)',
		));
		
		$company->addElement('text', 'portal_website', array(
			'label' => 'Portal Website',
		));
		
		$company->addElement('text', 'general_address_street', array(
			'label' => 'Straat',
		));
		
		$company->addElement('text', 'general_address_number', array(
			'label' => 'Nummer',
		));
		
		$company->addElement('text', 'general_address_zipcode', array(
			'label' => 'Postcode',
		));
		
		$company->addElement('text', 'general_address_city', array(
			'label' => 'Stad',
		));
	
	$post = new Form_SubForm($this);
	$post->setAttrib('title', 'Post adres');
	
	$this->addSubForm($post, 'post');
		
		$post->addElement('text', 'general_post_address_street', array(
			'label' => 'Straat',
		));
		
		$post->addElement('text', 'general_post_address_number', array(
			'label' => 'Nummer',
		));
		
		$post->addElement('text', 'general_post_address_zipcode', array(
			'label' => 'Postcode',
		));
		
		$post->addElement('text', 'general_post_address_city', array(
			'label' => 'Stad',
		));
		
	
	$labels = new Form_SubForm($this);
	$labels->setAttrib('title', 'Labels');
	
	$this->addSubForm($labels, 'labels');
		
		$labels->addElement('text', 'build_label', array(
			'label' => 'Label bouwnummer',
		));

		$labels->addElement('text', 'resident_label', array(
			'label' => 'Label bewoner',
		));

		$labels->addElement('text', 'usageaddress_label', array(
			'label' => 'Label afname adres',
		));

		$labels->addElement('text', 'lot_label', array(
			'label' => 'Label kavelnummer',
		));

		$labels->addElement('checkbox', 'lot_label_on_invoice', array(
			'label' => 'Kavelnummer op factuur',
		));
		
	$externalform = new Form_SubForm($this);
	$externalform->setAttrib('title', 'Tekst external support mail');
	
	$this->addSubForm($externalform, 'externalform');
		
		$externalform->addElement('textarea', 'extra_info_external_support', array(
			'label' => 'Tekst',
			'rows' => 5,
			'cols' => 80,
		));

		
			
	
	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Financieel');
	$this->addSubForm($financial, 'financieel');
		
		$financial->addElement('checkbox', 'object_ledger_enabled', array(
			'label' => 'Grootboek per object',
		));
		
	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Meterstanden import');
	$this->addSubForm($financial, 'financieel');

		$financial->addElement('text', 'meterimport_report_address', [
			'label' => 'Raportage sturen',
		]);
	
	
	
	$invoice = new Form_SubForm($this);
	$invoice->setAttrib('title', 'Facturen');
	$this->addSubForm($invoice, 'invoice');
		
		$invoice->addElement('checkbox', 'invoice_no_email', array(
			'label' => 'Facturen niet per E-mail',
		));
		
		$invoice->addElement('checkbox', 'invoice_date_equals_expire_date', array(
			'label' => 'Factuurdatum gelijk aan vervaldatum',
		));

		$invoice->addElement('checkbox', 'invoice_first_enabled', array(
			'label' => 'Eerste factuur ingeschakeld',
		));

		
		$invoice->addElement('checkbox', 'invoice_hide_email', array(
			'label' => 'E-mail adres niet op factuur tonen',
		));

		$invoice->addElement('checkbox', 'invoice_hide_title', array(
			'label' => 'Titel niet op factuur tonen',
		));

		$invoice->addElement('checkbox', 'invoice_hide_footer', array(
			'label' => 'Footer niet op factuur tonen',
		));

		$invoice->addElement('checkbox', 'invoice_small_layout', array(
			'label' => 'Smallere breedte factuurlayout',
		));

		$invoice->addElement('checkbox', 'invoice_hide_payment_method', array(
			'label' => 'Betaalwijze niet op de factuur tonen',
		));

		$invoice->addElement('checkbox', 'invoice_custom_post_add_stationary', array(
			'label' => 'Briefpapier bij postverzending handmatige facturen',
		));

		$invoice->addElement('text', 'invoice_send_day', array(
			'label' => 'Gewenste dag verzending facturen',
		));

		
		$invoice->addElement('checkbox', 'invoice_finalize_by_user', array(
			'label' => 'Automatische verzending facturen',
		));

		$invoice->addElement('checkbox', 'invoice_tax_for_private', array(
			'label' => 'BTW specificatie particuliere facturen',
		));

		$invoice->addElement('checkbox', 'invoice_finalnota_disable_costs', array(
			'label' => 'Kosten bij eindafrekening uitschakelen',
		));		
		
		$invoice->addElement('text', 'invoice_consumption_estimate_adjust_percentage', array(
			'label' => 'Verbruiksschatting bijstel percentage',
		));

		$invoice->addElement('checkbox', 'invoice_discount_percentage_add_total', array(
			'label' => 'Contract percentage-korting oplopend',
			'attribs' => array(
				'title' => 'Bij meerdere percentages korting op een component wordt het totaalbedrag waarover de korting berekend wordt na iedere korting bijgewerkt.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)	
		));	

		$invoice->addElement('checkbox', 'invoice_disable_grouped_rates', array(
			'label' => 'Gegroepeerde componenten uitschakelen',
		));



		$invoice->addElement('checkbox', 'invoice_indexation_commercial_no_rounding', array(
			'label' => 'Comm. indexatie afronding uitschakelen',
		));

		$invoice->addElement('checkbox', 'invoice_indexation_commercial_for_non_commercial', array(
			'label' => 'Comm. indexatie voor particulieren',
		));

		$invoice->addElement('text', 'invoice_postpone_days', array(
			'label' => 'Aantal dagen maximaal uitstel',
		));
		
		$invoice->addElement('text', 'clieop_month_day', array(
			'label' => 'Dag van de maand clieop incasso',
		));

		$invoice->addElement('text', 'object_invoice_empty', array(
			'label' => 'Leegstand factuur belegger',
		));

		$invoice->addElement('text', 'invoice_footer_color', array(
			'label' => 'Kleur lijn footer factuur',
		));
		
		$invoice->addElement('checkbox', 'invoice_reference_enabled', array(
			'label' => 'Betalingskenmerk ingeschakeld',
		));

		$invoice->addElement('checkbox', 'invoice_show_build_on_invoice', array(
			'label' => 'Toon bouwnummer/kamernummer op factuur',
		));
		$invoice->addElement('checkbox', 'hide_footerblock_print_layout', array(
			'label' => 'Verberg footer op blanco briefpapier',
		));
		$invoice->addElement('checkbox', 'build_to_address_on_invoice', array(
			'label' => 'Bouwnummer toevoegen aan adres op factuur',
		));
		$invoice->addElement('checkbox', 'invoice_show_vat_on_summary', array(
			'label' => 'Toon BTW totaal op invoice summary',
		));
		$invoice->addElement('checkbox', 'invoice_show_collection_total_on_summary', array(
			'label' => 'Toon totaal automatische incasso op invoice summary',
		));
		$invoice->addElement('checkbox', 'invoice_hide_collection_indicator', array(
			'label' => 'Verstop de "Automatisch incasso ja/nee" regel op facturen',
		));

		$invoice->addElement('checkbox', 'invoice_custom_show_vat_included', array(
			'label' => 'Toon inclusief BTW op handmatige facturen',
		));

		$invoice->addElement('checkbox', 'invoice_final_functionality', array(
			'label' => 'Eindafrekeningen aanmaken/verzenden is mogelijk',
		));

		$invoice->addElement('checkbox', 'hide_vatblock_invoice', array(
			'label' => 'Verberg BTW Specificatie engelstalige facturen',
		));

		$invoice->addElement('select', 'invoice_xml_export_users', array(
			'label' => 'XML naar gebruikers',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
		));	

		$invoice->addElement('checkbox', 'invoice_show_fourweekly_options', array(
			'label' => 'Toon "4 wekelijks" als facturatie periode.',
		));

		
	$email = new Form_SubForm($this);
	$email->setAttrib('title', 'Email');
	$this->addSubForm($email, 'email');
		
		$email->addElement('text', 'email_bcc_to_archive', array(
			'label' => 'Uitgaande E-mail BCC naar adres',
		));
		
	$ratesheet = new Form_SubForm($this);
	$ratesheet->setAttrib('title', 'Tariefblad');
	$this->addSubForm($ratesheet, 'ratesheet');
		
		$ratesheet->addElement('checkbox', 'ratesheet_point_enabled', array(
			'label' => 'Huurpunten in tariefblad',
		));

		$ratesheet->addElement('checkbox', 'ratesheet_amounts_enabled', array(
			'label' => 'Bedragen in tariefblad',
		));

		$ratesheet->addElement('checkbox', 'ratesheet_day_enabled', array(
			'label' => 'Vastrecht dagen',
		));	

		$ratesheet->addElement('checkbox', 'ratesheet_kwh_enabled', array(
			'label' => 'Variabel kwh',
		));	
		
		$ratesheet->addElement('checkbox', 'ratesheet_point_round_amount', array(
			'label' => 'Huurpunten-bedrag afronden',
		));	
		
	$ca = new Form_SubForm($this);
	$ca->setAttrib('title', 'Herinneringen & Aanmaningen');
	$this->addSubForm($ca, 'ca');

		$ca->addElement('checkbox', 'reminder_finalize_by_user', array(
			'label' => 'Automatische verzending',
		));

		
	
		$ca->addElement('checkbox', 'hide_expire_date_reminders', array(
			'label' => 'Verberg vervaldatum in herinneringen'
		));

		$ca->addElement('text', 'reminder_toformalnotice_days', array(
			'label' => 'Dagen niveau 1'
		));

		$ca->addElement('checkbox', 'reminder_disable_notax_notice', array(
			'label' => 'Waarschuwing BTW-plichtig uitschakelen'
		));
		
		$ca->addElement('text', 'label_reminder', array(
			'label' => 'Naam niveau 1',
		));

		$ca->addElement('text', 'formalnotice_tocollectionagency_days', array(
			'label' => 'Dagen niveau 2',
		));

		$ca->addElement('text', 'label_formalnotice', array(
			'label' => 'Naam niveau 2',
		));

		$ca->addElement('checkbox', 'collection_agency_inform_enabled', array(
			'label' => 'Niveau 3 ingeschakeld',
		));		

		$ca->addElement('text', 'collection_agency_inform_days', array(
			'label' => 'Dagen niveau 3',
		));

		$ca->addElement('text', 'label_collection_agency_inform', array(
			'label' => 'Naam niveau 3',
		));

		$ca->addElement('text', 'tocollectionagency_days', array(
			'label' => 'Aantal dagen naar incassobureau',
		));

		$ca->addElement('text', 'percentage_costs_first_formalnotice', array(
			'label' => 'Percentage wettelijke kosten',
		));

		$ca->addElement('select', 'ca_responsible_employee', array(
			'label' => 'Contactpersoon incassobureau',
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));	

	
	$investor_provision = new Form_SubForm($this);
	$investor_provision->setAttrib('title', 'Beheerfacturen');
	$this->addSubForm($investor_provision, 'investor_provision');

		$investor_provision->addElement('select', 'investor_provision_period_date', array(
			'label' => 'Beheerfacturen periode',
			'multiOptions' => array(
				'payed' => 'Betaaldatum',
				'invoice' => 'Factuurdatum'
			)
		));

		$investor_provision->addElement('text', 'investor_provision_send_day', array(
			'label' => 'Gewenste dag aanmaken beheerfacturen',
		));

		$investor_provision->addElement('checkbox', 'investor_provision_period_description_end_date', array(
			'label' => 'Maand aanduiding beheerfactuur op einddatum',
		));

		$investor_provision->addElement('text', 'investor_provision_expire_days', array(
			'label' => 'Beheerfactuur aantal dagen tot uitvoerdatum'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_second_investor', array(
			'label' => 'Invoer 2e belegger ingeschakeld'
		));
	
	$tenant = new Form_SubForm($this);
	$tenant->setAttrib('title', 'Bewoners/Objecten');
	$this->addSubForm($tenant, 'tenant');
	
		
		$tenant->addElement('checkbox', 'user_register_current_uo', array(
			'label' => 'Registratie unieke code neemt huidige sitatie over',
		));	
			
		$tenant->addElement('checkbox', 'object_deposit_enabled', array(
			'label' => 'Waarborg ingeschakeld',
		));	
		
		$tenant->addElement('checkbox', 'object_woz_enabled', array(
			'label' => 'WOZ ingeschakeld',
		));	
		$tenant->addElement('checkbox', 'object_ratesheet_override_enabled', array(
			'label' => 'Overschrijven tariefbladwaarden',
		));	
			
		$tenant->addElement('checkbox', 'project_first_enddate', array(
			'label' => 'Einddatum eerste periode',
		));	
	$sms = new Form_SubForm($this);
	$sms->setAttrib('title', 'SMS');
	$this->addSubForm($sms, 'sms');
	
		$sms->addElement('checkbox', 'sms_enabled', array(
			'label' => 'Sms ingeschakeld',
			'attribs' => array(
				'autocomplete' => 'off',
				'title' => 'Belangrijk! Na het invullen van deze waarden moet in het MessageBird account 
					onder "Developers"->"API toegang" de oude API nog aangezet worden. Dit is een linkje in een klein
					font onderaan de pagina. Anders geeft de API altijd "Wrong password" terug.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		));	
		
		$sms->addElement('text', 'sms_access_key', array(
			'label' => 'MessageBird Access Key',
			'attribs' => array('autocomplete' => 'off')
		));	

		$sms->addElement('text', 'sms_originator', array(
			'label' => 'Afzender',
		));	
		
	$complaint = new Form_SubForm($this);
	$complaint->setAttrib('title', 'Klachten en storingen');
	$this->addSubForm($complaint, 'complaint');
	
		$complaint->addElement('select', 'complaint_inform_urgent', array(
			'label' => 'Gebruikers informeren',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));	

		$complaint->addElement('text', 'complaint_added_email', array(
			'label' => 'Melding bij nieuwe k/s sturen naar',
		));

		$complaint->addElement('checkbox', 'complaint_add_corporation_info_to_mail', array(
			'label' => 'Voeg BV info toe aan standaard mail',
		));	

		$complaint->addElement('text', 'complaint_responsetime_complaint', array(
			'label' => 'Responsetijd klacht (uren)',
		));	

		$complaint->addElement('text', 'complaint_responsetime_malfunction', array(
			'label' => 'Responsetijd storing (uren)',
		));	

		$complaint->addElement('text', 'complaint_responsetime_urgent_malfunction', array(
			'label' => 'Responsetijd urgente storing (uren)',
		));

	$task = new Form_SubForm($this);
	$task->setAttrib('title', 'Taken');
	$this->addSubForm($task, 'task');

		$task->addElement('checkbox', 'task_mail_assigned', array(
			'label' => 'Melding nieuwe taak',
		));

		$task->addElement('checkbox', 'task_mail_completed', array(
			'label' => 'Melding taak afgerond',
		));

		$task->addElement('checkbox', 'task_mail_dayly_overview', array(
			'label' => 'Dagoverzicht afgeronde taken',
		));

		$task->addElement('checkbox', 'task_mail_weekly_personal', array(
			'label' => 'Persoonlijk wekelijks overzicht',
		));

		$task->addElement('checkbox', 'task_mail_weekly_overview', array(
			'label' => 'Weekoverzicht afgeronde taken',
		));

		$task->addElement('select', 'task_mail_weekly_management_overview', array(
			'label' => 'Weekoverzicht management',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));
				
	$ideal = new Form_SubForm($this);
	$ideal->setAttrib('title', 'iDeal');
	$this->addSubForm($ideal, 'ideal');	
	
		$ideal->addElement('checkbox', 'ideal_enabled', array(
			'label' => 'iDeal ingeschakeld',
		));	

		$ideal->addElement('checkbox', 'ideal_ip_restricted_enabled', array(
			'label' => 'IP-restrictie (voor tijdens test)',
		));	

		$ideal->addElement('text', 'ideal_ip_restricted', array(
			'label' => 'IP toegestaan',
		));	

		$ideal->addElement('checkbox', 'ideal_test_mode', array(
			'label' => 'Test modus',
		));	

		$ideal->addElement('select', 'ideal_bank', array(
			'label' => 'Bank - iDeal variant',
			'multiOptions' => array(
				// any new bank types should also be added in /aplication/forms/corporation.php
				'abn' => 'ABN-AMRO - Only Kassa'
			)
		));	

		$ideal->addElement('text', 'ideal_id', array(
			'label' => 'Identifier (PSPID)',
		));	

		$ideal->addElement('text', 'ideal_secret', array(
			'label' => 'Secret',
		));	

		global $config;

		$ideal->addElement('text', 'ideal_url', array(
			'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal/',
			'label' => 'URL formulier',
			'ignored' => true,
			'disabled' => true,
			'style' => 'width:330px'
		));	

		$ideal->addElement('text', 'ideal_sha', array(
			'value' => 'SHA-1',
			'label' => 'Hash-algoritme',
			'ignored' => true,
			'disabled' => true,
		));		

		foreach(array('cancel', 'accept', 'descline', 'exception', 'cancel') as $page)
			$ideal->addElement('text', 'ideal_' . $page . '_url', array(
				'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal/page/' . $page . '/',
				'label' => 'URL ' . $page,
				'ignored' => true,
				'disabled' => true,
			'style' => 'width:330px'
			));			

		$ideal->addElement('text', 'ideal_post_payment', array(
			'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal-payment/page/accept/',
			'label' => 'Post-payment',
			'ignored' => true,
			'disabled' => true,
			'style' => 'width:330px'
		));

	$bankimport = new Form_SubForm($this);
	$bankimport->setAttrib('title', 'Bank import');
	$this->addSubForm($bankimport, 'bankimport');	
	
		$bankimport->addElement('checkbox', 'bankimport_livedrive_enabled', array(
			'label' => 'LiveDrive ingeschakeld',
		));	

		$bankimport->addElement('text', 'bankimport_livedrive_accountname', array(
			'label' => 'LiveDrive account naam',
		));	
	
		$bankimport->addElement('text', 'bankimport_livedrive_username', array(
			'label' => 'LiveDrive share gebruikersnaam',
		));	

		$bankimport->addElement('text', 'bankimport_livedrive_password', array(
			'label' => 'LiveDrive share wachtwoord',
			'attribs' => array('autocomplete' => 'off')
		));	
		
		$bankimport->addElement('select', 'bankimport_livedrive_parser', array(
			'label' => 'LiveDrive bestandsformaat',
			'multiOptions' => array('filename' => 'Herkennen aan bestandsnaam', 'csv' => 'C.S.V.', 'mt940' => 'MT940', 'swift' => 'SWIFT'),
			'RegisterInArrayValidator' => false,
		));	

		$bankimport->addElement('select', 'bankimport_livedrive_bank', array(
			'label' => 'LiveDrive bank',
			'multiOptions' => array('abn' => 'ABN-AMRO', 'triodos' => 'Triodos'),
			'RegisterInArrayValidator' => false,
		));

		$bankimport->addElement('checkbox', 'bankimport_exact_enabled', [
			'label' => 'Banktransactie import vanuit Exact Online ingeschakeld',
		]);

		$bankimport->addElement('checkbox', 'bankimport_match_by_account_enabled', array(
			'label' => 'Matchen op bankrekeningnummer ingeschakeld',
		));	
			
		$bankimport->addElement('checkbox', 'bankimport_report_enabled', array(
			'label' => 'Rapportage ingeschakeld',
		));	
		
		$bankimport->addElement('select', 'bankimport_report_users', array(
			'label' => 'Naar gebruikers',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
            'attribs' => array(
                'title' => 'Mails met verwerking bankverwerking of uitwisseling facturen financieel systeem foutmeldingen gaan naar deze gebruikers.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

		$bankimport->addElement('text', 'bankimport_csv_parser_columns', array(
			'label' => 'CSV import kolommen',
		));	
	
	$lucidChart = new Form_SubForm($this);
	$lucidChart->setAttrib('title', 'Lucidchart');
	
	$this->addSubForm($lucidChart, 'lucidChart');
	
		$lucidChart->addElement('checkbox', 'lucidchart_in_use', array(
			'label' => 'Lucidchart ingeschakeld',
		));

		$lucidChart->addElement('text', 'lucidchart_username', array(
			'label' => 'Lucidchart account naam',
		));
		
		$lucidChart->addElement('text', 'lucidchart_password', array(
			'label' => 'Lucidchart wachtwoord',
		));

	$voyanta = new Form_SubForm($this);
	$voyanta->setAttrib('title', 'Voyanta');
	$this->addSubForm($voyanta, 'voyanta');
	
		$voyanta->addElement('select', 'voyanta_report_recipients', array(
			'label' => 'Rapporteren aan:',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));	
					
		
	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Instellingen opslaan'
	));

?>
