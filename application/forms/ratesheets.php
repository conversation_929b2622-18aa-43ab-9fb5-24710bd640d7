<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'rate')
			->setAttrib('class', 'rate form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

		//date
		$general->addElement('text', 'date', array(
			'label' => 'Datum',
			'value' => date('d-m-Y'),
			'validators' => array(
			),
		));

		// title
		$general->addElement('text', 'title', array(
			'label' => 'Naam template',
			'validators' => array(
			),
		));

		$general->addElement('select', 'inactive', array(
			'label' => 	'Inactief',
			'value' => '0',
			'multioptions' 	=> [
				true => 'Ja',
				false => 'Nee'
			]
		));

		//
		$general->addElement('text', 'emptyrate', array(
			'label' => 'Percentage bij leegstand',
			'validators' => array(
			),
		));

		// warm override
		$general->addElement('checkbox', 'warmoverride', array(
			'label' => 'Vastrecht warmte overschrijfbaar per object',
			'validators' => array(
			),
		));

		// cold override
		$general->addElement('checkbox', 'coldoverride', array(
			'label' => 'Vastrecht koude overschrijfbaar per object',
			'validators' => array(
			),
		));

	$this->addElement('text', 'rates', array(
		'label' => 'Rates',
		'validators' => array(
		),
	));

	//submit
	$this->addElement('submit', 'submitbutton', array(
		'label' => 'Opslaan'
	));

?>