<?php

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'complaintCategory')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'form');


	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

        $typeLabels = SupportComplaints::getLabels('nl');
		$general->addElement('select', 'type', array(
			'label' => 'Type',
			'required' => true,
			// ironically, do not use SupportComplaints::getTypeSelectList() at this point, or the customer will never
			// be able to add (categories for) new types.
			'multiOptions' => $typeLabels,
		));

		$general->addElement('text', 'name', array(
			'label' => 'Omschrijving',
			'required' => true,
		));

        $general->addElement('text', 'name_en', array(
            'label' => 'Omschrijving Engels',

        ));
if (Settings::get('software_french_enabled')) {
        $general->addElement('text', 'name_fr', array(
            'label' => 'Omschrijving Frans',

        ));
}
		if(Settings::get( 'complaint_use_assigntime' )) {
			$general->addElement( 'text', 'assigntime', [
				'label'    => 'Toewijstijd (uren)',
				'attribs'  => [
					'title' => 'Indien ingesteld zal deze waarde bij nieuwe, niet toegewezen meldingen gebruikt worden als
					deadline die automatisch verdwijnt als de melding toegewezen wordt. (Eenmaal) toegewezen meldingen
					hebben/krijgen de normale deadline die bij hun categorie hoort.',
					'class' => 'forceAutoHint',
					'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_assigntime_complaint'],
				],
			] );
		}

		$general->addElement('text', 'responsetime', [
			'label' => 'Reactietijd (uren)',
			'attribs'  => [
				'title' => "Standaardwaarden voor dit veld zijn: "
				           .ComplaintCategories::$defaultResponseTimes['complaint_responsetime_complaint']
				           .' uur (klachten en meldingen) en '
				           .ComplaintCategories::$defaultResponseTimes['complaint_responsetime_malfunction'].' uur (storingen)',
				'class' => 'forceAutoHint',
				'placeholder' => 'zie aanv. info in hint',

			],
		] );

		$general->addElement('select', 'urgent_allowed', array(
			'label' => 'Mag urgent zijn?',
			'multiOptions' => array(
				'ja' => 'Ja',
				'nee' => 'Nee'
			),
		));


        $general->addElement('select', 'real_estate_type', array(
            'label' => 'Gebruiken voor',
            'multiOptions' => array('both' => 'Residentieel en commercieel', 'residential' => 'Residentieel', 'commercial' => 'Commercieel'),
            'required' => true
        ));


        $types = db()->fetchPairs(db()->select()->from(
            ['ot' => 'object_type'],
            ['id', 'name']
        )->order('name'));
        ksort($types);
        $types = [null => ' --- Alle objecttypen ---'] + $types;

        $general->addElement('multiselect', 'real_estate_type_object_types', array(
            'label' => 'Gebruiken voor',
            'multiOptions' => $types,
        ));



        $general->addElement('select', 'category_hide_on_portal', array(
            'label' => 'Categorie verbergen op de persoonlijke pagina?',
            'multiOptions' => [
                '0' => 'Nee',
                '1' => 'Ja'
            ],
            'attribs'  => [
                'title' => "Indien ja gekozen wordt kan de huurder via de portal deze categorie niet selecteren/zien. ",
                'class' => 'forceAutoHint',
                'placeholder' => 'zie aanv. info in hint',

            ],
        ));

		$general->addElement('text', 'responsetime_urgent', [
			'label' => 'Reactietijd bij urgentie (uren)',
			'attribs'  => [
				'placeholder' => ComplaintCategories::$defaultResponseTimes['complaint_responsetime_urgent_malfunction'],
			],
		] );


		$uModel = new Users();
		$internalUsers = ['Geen'];
		foreach ( $uModel->getInternalUsersOptions() as $id => $name ) {
			$internalUsers[$id] = $name;
		}

		$general->addElement( 'select', 'default_assignee', [
			'label'   => 'Standaard toegewezen gebruiker',
			'attribs' => [
				'title' => 'Nieuwe meldingen in deze categorie worden standaard toegewezen aan deze interne gebruiker.',
				'class' => 'forceAutoHint',
			],
			'multiOptions' => $internalUsers,
		] );

		$general->addElement('select', 'default_component_id', [
			'label' => 'Standaard component',
			'multiOptions' => $this->getOwner()->components,
		]);

		$general->addElement('textarea', 'attentionfield', array(
			'label' => 'Hints getoond bij kiezen categorie door huurder',
			'rows' => 12,
			'cols' => 35
		));

        $general->addElement('textarea', 'attentionfield_en', array(
            'label' => 'Hints getoond bij kiezen categorie door huurder (Engels)',
            'rows' => 12,
            'cols' => 35
        ));
if (Settings::get('software_french_enabled')) {
        $general->addElement('textarea', 'attentionfield_fr', array(
            'label' => 'Hints getoond bij kiezen categorie door huurder (Frans)',
            'rows' => 12,
            'cols' => 35
        ));
}
		$general->addElement('hidden', 'category_id', [
			'value' => $this->getOwner()->categoryId
		]);

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Categorie opslaan'
	));
?>
