<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'collectionAgency')
			->setAttrib('class', 'collectionAgency form');



	//name
	$this->addElement('text', 'title', array(
		'label' => 'Omschrijving',
		'required' => true
	));

    //name
    $this->addElement('text', 'title_en', array(
        'label' => 'Omschrijving engels',
     ));


    //name
    $this->addElement('text', 'title_fr', array(
        'label' => 'Omschrijving frans',
     ));


    $this->addElement('select', 'visibleForUser', array(
        'label' 		=> 	'Standaard zichtbaar',
        'multioptions' 	=> array(
            '0' => 'Intern',
            '1' => 'Voor de huurder en belegger',
            '2' => 'Voor de huurder',
            '3' => 'Voor de belegger',
            '9' => 'Extern',
        ),
        'validators' 	=> 	array(),
    ));

	 //required
		$this->addElement('select', 'required', array(
			'label' 		=> 	'Verplicht document',
			'multioptions' 	=> array(
									'no' => 'Nee',
									'yes' => 'Ja'
							),
			'validators' 	=> 	array(),
		));
		
	 //identification
		$this->addElement('select', 'identification', array(
			'label' 		=> 	'Legitimatie?',
			'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
								),
			'validators' 	=> 	array(),
		));

	//EDR Credit Check
	$this->addElement('select', 'edr_credit_check', array(
		'label' 		=> 	'EDR Credit Check?',
		'multioptions' 	=> array(
			'0' => 'Nee',
			'1' => 'Ja'
		),
		'validators' 	=> 	array(),
	));


// Template for dynamic commercial contract generation
		$this->addElement('select', 'contract_template_commercial', array(
			'label' 		=> 	'Contract template (commercieel)',
			'multioptions' 	=> array(
				'0' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	array(),
		));
		
		// Template for dynamic private contract generation
		$this->addElement('select', 'contract_template_private', array(
			'label' 		=> 	'Contract template (particulier)',
			'multioptions' 	=> array(
				'0' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	array(),
		));

		// Template for personal page invitations
		$this->addElement('select', 'contract_template_invite', array(
			'label' 		=> 	'Uitnodigingtemplate persoonlijke pagina',
			'multioptions' 	=> array(
				'0' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	array(),
		));
	

		//Sepa incasso formulier
		$this->addElement('select', 'sepa_mandate', array(
			'label' 		=> 	'SEPA Machtiging',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));	

	 //contract
		$this->addElement('select', 'contract', array(
			'label' 		=> 	'Leveringsovereenkomst',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));	
		
		//Chamber
		$this->addElement('select', 'chamber', array(
			'label' 		=> 	'KVK',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));	

		$this->addElement('select', 'inspection', array(
			'label' 		=> 	'Inspectie',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));			
		
			//Ratesheet
		$this->addElement('select', 'ratesheet', array(
			'label' 		=> 	'Tariefblad',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));		
		
			//Rental
		$this->addElement('select', 'rental', array(
			'label' 		=> 	'Huurovereenkomst',
				'multioptions' 	=> array(
									'0' => 'Nee',
									'1' => 'Ja'
							),
			'validators' 	=> 	array(),
		));		
		
		
		// Logo for investor page
		$this->addElement('select', 'logo', array(
			'label' 		=> 	'Logo voor op persoonlijke pagina',
			'multioptions' 	=> array(
				'0' => 'Nee',
				'1' => 'Ja'
			),
			'validators' 	=> 	array(),
		));

		// Logo for investor page
		$this->addElement('checkbox', 'upload', array(
			'label' 		=> 	'Upload op persoonlijke pagina',
			'validators' 	=> 	array(),
		));
		
		// Document type
		$this->addElement('select', 'type_document', array(
			'label' 		=> 	'Type document',
				'multioptions' 	=> array(
									'particulier' => 'Particulier',
									'zakelijk' => 'Zakelijk',
								    'overig' => 'Overig'

							),
			'validators' 	=> 	array(),
		));	
		

		// This kind of document should be related to contracts
		$this->addElement('select', 'contract_document', [
			'label' 		=> 	'Contract gerelateerd',
			'multioptions' 	=> [
				'0' => 'Nee',
				'1' => 'Ja'
			],
		]);


	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Categorie opslaan'
	));

?>