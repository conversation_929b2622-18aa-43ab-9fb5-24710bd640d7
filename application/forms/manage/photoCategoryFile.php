<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'collectionAgency')
			->setAttrib('class', 'collectionAgency form');



	//name
	$this->addElement('text', 'title', array(
		'label' => 'Titel',
		'required' => true
	));

	 //required
		$this->addElement('select', 'required', array(
			'label' 		=> 	'Verplichte titel?',
			'multioptions' 	=> array(
									'yes' => 'Ja',
									'no' => 'Nee'
								),
			'validators' 	=> 	array(),
		));


	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Titel opslaan'
	));

?>