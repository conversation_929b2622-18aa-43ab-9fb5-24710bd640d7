<?php

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('id', 'collectionAgency')
    ->setAttrib('class', 'collectionAgency form');

$this->addElement('select', 'type', [
    'label' => 'Type',
    'multiOptions' => [
        'project' => 'Project',
        'objectgroup' => 'Objectgroep',
        'object' => 'Object',
        'user' => 'Gebruiker',
    ]
]);

$this->addElement('text', 'category', [
    'label' => 'Naam',
    'required' => true
]);

if (Settings::get('modules_vidii')) {
    $this->addElement('checkbox', 'vidii_images', [
        'Label' => 'Vidii afbeeldingen'
    ]);
}

$this->addElement('submit', 'aanmaken', [
    'label' => 'Categorie opslaan'
]);
