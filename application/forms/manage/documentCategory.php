<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'collectionAgency')
			->setAttrib('class', 'collectionAgency form');



	//name
	$this->addElement('text', 'category', array(
		'label' => 'Omschrijving',
		'required' => true
	));

    $this->addElement('text', 'category_en', array(
        'label' => 'Omschrijving Engels',
     ));

    $this->addElement('text', 'category_fr', array(
        'label' => 'Omschrijving Frans',
     ));

	//contract attachments for contracting private
	$this->addElement('checkbox', 'category_contractdocs_private', array(
		'label' => 'Digitaal contracteren particulier?'
	));

	//contract attachments for contracting commercial
	$this->addElement('checkbox', 'category_contractdocs_commercial', array(
		'label' => 'Digitaal contracteren commercieel?'
	));

	
	 


	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Categorie opslaan'
	));

?>