<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'deposit')
			->setAttrib('class', 'deposit form');

	$deposit = new Form_SubForm($this);
	$deposit->setAttrib('title', 'Waarborgbedrag');
	$this->addSubForm($deposit, 'deposit');

include('deposit/userObjectSelect.php');

		$deposit->addElement('text', 'object_amount', array(
			'label' => 'Bedrag bij object',
			'disabled' => true,
			'ignored' => true
		));
		
		$deposit->addElement('text', 'amount', array(
			'label' => 'Bedrag'
		));

		$deposit->addElement('text', 'payed', array(
			'label' => 'Betaald'
		));

		$deposit->addElement('select', 'deposit_type', array(
			'label' => 'Type waarborg',
			'multioptions' 	=> array(
									'none' => 'Geen waarborgsom',
									'cash_deposit' => 'Gestort',
									'bank_guarantee' => 'Bank Garantie',
									'concern_guarantee' => 'Concern Garantie'
								),
			'value' => 'cash_deposit'
		));

		$deposit->addElement('checkbox', 'deposit_payed_to_broker', array(
			'label' => 'Betaald aan belegger'
		));


			//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Waarborgsom opslaan'
	));

?>
