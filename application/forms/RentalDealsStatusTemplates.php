<?

//general form options
$this->setAction('')
    ->setAttrib('id', 'rental_deals_status_templates')
    ->setMethod('post')
    ->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general');

    $general->addElement('text', 'name', [
        'label' => 'Naam',
        'validators' => [],
    ]);

    $project_list = (new Project)->getSimpleList();

    $general->addElement('multiselect', 'projects', [
        'label' => 'Projecten beperken',
        'multiOptions' =>
            [0 => ' - Zichtbaar voor alle projecten - '] +
            array_combine(
                array_column($project_list, 'id'),
                array_column($project_list, 'name')
            ),
        'value' => 0,
        'attribs' => [
            'style' => 'width: 200px; height: 200px;',
            'title' => '<PERSON><PERSON> aan welke projecten deze template gekoppeld is, wanneer er geen selectie gemaakt wordt is de template voor alle projecten zichtbaar',
            'class' => 'forceAutoHint'
        ]
    ]);

$rentalDealTemplateId = $this->getOwner()->id;

$rentalDealsStatusTypesListMultiOptions = [];
if ($rentalDealTemplateId) {
    $rentalDealsStatusTypesList = (new RentalDealsStatusTypes())->getListForTemplateId($rentalDealTemplateId);

    $rentalDealsStatusTypesListMultiOptions = array_combine(
        array_column($rentalDealsStatusTypesList, 'id'),
        array_column($rentalDealsStatusTypesList, 'description')
    );
}

$general->addElement('select', 'start_status_type_id', [
    'label' => 'Standaard start status',
    'multiOptions' => $rentalDealsStatusTypesListMultiOptions,
    'validators' => [],
    'attribs' => [
        'title' => 'Bij het aanmaken van een deal komt deze standaard eerst in deze status',
        'class' => 'forceAutoHint'
    ]
]);

$general->addElement('select', 'start_status_type_id_group_viewing', [
    'label' => 'Start status bij groepsbezichtiging',
    'multiOptions' => $rentalDealsStatusTypesListMultiOptions,
    'validators' => [],
    'attribs' => [
        'title' => 'Bij een groepsbezichtiging komt de deal in deze status',
        'class' => 'forceAutoHint'
    ]
]);


if (!$rentalDealTemplateId) {
    $general->start_status_type_id->setOptions([
        'multiOptions' => [
            null => 'Instelbaar na toevoegen van workflow-stappen',
        ],
        'ignored' => true,
        'disabled' => true,
    ]);

    $general->start_status_type_id_group_viewing->setOptions([
        'multiOptions' => [
            null => 'Instelbaar na toevoegen van statussen',
        ],
        'ignored' => true,
        'disabled' => true,
    ]);
}

$general->addElement('checkbox', 'finish_website_deal_always_allowed', [
    'label' => 'Bezichtiging overslaan altijd mogelijk',
    'validators' => [],
    'attribs' => [
        'title' => 'De optie "Bezichtiging overslaan" bij website deals is altijd beschikbaar, ook wanneer er reeds een actieve deal is voor het object',
        'class' => 'forceAutoHint'
    ]
]);


    $general->addElement('checkbox', 'direct_create_user_object', [
        'label' => 'Direct conceptkoppeling',
        'validators' => [],
        'attribs' => [
            'title' => 'Bij het handmatig aanmaken van een deal direct een conceptkoppeling aanmaken tussen lead en object.',
            'class' => 'forceAutoHint'
        ]
    ]);

$general->addElement('number', 'contact_date_on_workflow_start', [
    'label' => 'Contact datum bij start workflow, aantal dagen in toekomst',
    'validators' => [],
    'attribs' => [
        'title' => 'Na het doorzetten van een website deal naar een actieve deal, zet de contact datum een X aantal dagen in de toekomst.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -30, 'y': 0}"
    ]
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Template opslaan'
]);
