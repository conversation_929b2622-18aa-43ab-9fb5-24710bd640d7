<?

	//general form options
	$this->setAction('photo/add')
			->setMethod('post')
			->setAttrib('id', 'photoadd')
			->setAttrib('class', 'photoadd form');

	//name
	$this->addElement('hidden', 'photo', array(
		'label' 		=> 	'Foto',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//note
	$this->addElement('textarea', 'note', array(
		'label' 		=> 	'Notitie',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));
				
	//overrule
	include('forms/support/overrule.php');
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
