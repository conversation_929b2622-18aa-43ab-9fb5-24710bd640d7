<?
	//project objectgroup
	$this->addElement('checkbox', 'fullProject', array(
		'label' 		=> 'Voor gehele project',
		'checked'		=> 'checked'
	));	

	//project objectgroup
	$this->addElement('select', 'mapToOverrule', array(
		'label' 		=> 'of voor'
	));



$typeParam = $this->getOwner()->getParam('type');
$mapToParam = $this->getOwner()->getParam('mapTo');

if ($typeParam === 'project' && $mapToParam) {
    $objectGroups = (new Project())->getObjectGroups($mapToParam);
    $this->mapToOverrule->setMultiOptions($objectGroups);
}
