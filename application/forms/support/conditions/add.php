<?

	//general form options
	$this->setAction('conditions/add')
			->setMethod('post')
			->setAttrib('id', 'contactsadd')
			->setAttrib('class', 'contactsadd form small');


	$this->addElement('hidden', 'type', [
		'label' => '',
	]);

	$this->addElement('hidden', 'map_to', [
		'label' => '',
	]);

	
	$ccModel = new ContractConditions();
	$types = $ccModel->fetchAll($ccModel->select());
	$ContractConditions = array();
	foreach ($types as $type)
		$ContractConditions[$type['id']] = $type['title'];

	$this->addElement('select', 'condition_id', array(
		'label' 		=> 	'Bijzondere bepaling',
		'multioptions' 	=> 	$ContractConditions,
		'validators' 	=> 	array(),
		'multiple'		=>	false
	));	
	
 	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
