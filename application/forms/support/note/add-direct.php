<?
	//general form options
	$this->setAction('note/add')
			->setMethod('get')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintadd form');

	$this->addElement('hidden', 'type', array());
	$this->addElement('hidden', 'mapTo', array());
    $this->addElement('hidden', 'directnote', [
        'value' => 'true'
    ]);
	$this->addElement('text', 'map_to_multi_search', array(
		'label' 		=> 	'Koppelen',
		'validators' 	=> 	array(),
		'autocomplete' => 'off',
		'attribs' => array(
			'placeholder' => 'Zoek op adres, objectgroep of bewoner',
            'title' => 'Selecteer hier de bewoner of object voor wie de notitie wordt gedaan. <br/> Is de het object leeg, selecteer dan het juiste object of de objectgroep',
            'class' => 'forceAutoHint',
			'autocompleter' => 'complaint/add-direct-search-results/',
			'autocompleter_options' => Zend_Json::encode(array(
				'hiddenElementId' => 'mapTo'
			))
		)	
	));

	$this->addButtons(false);
	$this->addElement('submit', 'submitButton', array(
		'label' => 'Notitie aanmaken'
	));	
