<?

	//general form options
	$this->setAction('note/add')
			->setMethod('post')
			->setAttrib('id', 'noteadd')
			->setAttrib('class', 'noteadd form');

	//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Titel',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//name
	$this->addElement('textarea', 'message', array(
		'label' 		=> 	'Notitie',
		'validators' 	=> 	array(),
		'rows'			=> 	8,
	));

	$type = $this->getOwner()->getParam('type');

	if ($type != 'objectgroup') {
        //warning
        $this->addElement('checkbox', 'warning', array(
            'label' => 'Waarschuwing',
            'validators' => array(),
            'title' => 'Deze notitie wordt gemarkeerd als waarschuwing en bovenaan deze lijst geplaatst.'
        ));
    }

	//category
	$db = Zend_Db_Table::getDefaultAdapter();
	$this->addElement('select', 'category', array(
		'label' 		=> 'Categorie',
		'multiOptions'	=> $db->fetchPairs($db->select()->from(array('sdc'=>'support_notes_categories'), array('*'))->order('category ASC')),
		'registerInArrayValidator' => false
	));

	if (Settings::get('modules_surveys_enabled')) {
		$this->addElement('checkbox', 'survey', [
			'label' => 'Enquête uitnodiging versturen',
			'validators' => [],
		]);
	}
				
	//overrule
	include('forms/support/overrule.php');
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
