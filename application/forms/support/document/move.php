<?

//general form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');


//category
$db = Zend_Db_Table::getDefaultAdapter();

$options = $db->fetchPairs(
    $db->select()->from(array('sdc'=>'support_documents_categories'),
    array('*')) ->order('sdc.category')
);
$result['items'][0] =  "Maak je keuze" ;


foreach ($options as $key => $value) {
    $result['items'][$key] = $value;
}

$this->addElement('select', 'category', array(
    'label' 		=> 'Categorie',
    'multiOptions'	=> $result['items'],
    'required' 		=>	true
));

//catagoryTitle
$this->addElement('select', 'categoryTitle', array(
    'label' 		=> 'Bestand',
    'RegisterInArrayValidator' => false,

));


$this->addElement('select', 'categoryTo', array(
    'label' 		=> 'Categorie',
    'multiOptions'	=> $result['items'],
    'required' 		=>	true
));

//catagoryTitle
$this->addElement('select', 'categoryTitleTo', array(
    'label' 		=> 'Bestand',
    'RegisterInArrayValidator' => false

));


$this->addElement('submit', 'submitBtn', array(
	'label' => translate()->_('send'),

));

