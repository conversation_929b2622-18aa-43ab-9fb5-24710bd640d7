<?

//general form options
$this->setAction('document/' . (!$this->getOwner()->view->is_add_action ? 'edit' : 'add'))
	->setMethod('post')
	->setAttrib('id', 'document_add')
	->setAttrib('class', 'documentadd form ' . ($this->getOwner()->view->is_add_action ? '' : ' small'));

//name
$this->addElement('text', 'title', array(
	'label' 		=> 	'Titel',
	'validators' 	=> 	array(),
	'required' 		=>	true,
	'value'			=> 	isset($this->getOwner()->view->attachment) ? $this->getOwner()->view->attachment['name'] : ''
));


//document
$this->addElement('text', 'document', array(
	'label' 		=> 	'Document',
	'validators' 	=> 	array(),
	'required' 		=>	!isset($this->getOwner()->view->attachment)
));

//date
$this->addElement('text', 'originalDate', array(
	'label' => 'Document datum',
	'validators' => array()
));

$this->addElement('checkbox', 'set_valid_from', array(
	'label' => 'Begindatum instellen',
	'validators' => array(),
));

$this->addElement('text', 'from_date', array(
	'label' => 'Geldig vanaf',
	'validators' => array(),
));

/*$this->addElement('checkbox', 'set_valid_until', array(
	'label' => 'Einddatum instellen',
	'validators' => array(),
));

$this->addElement('text', 'valid_until', array(
	'label' => 'Geldig tot',
	'validators' => array(),
));*/

//catagory
$db = Zend_Db_Table::getDefaultAdapter();

$options = $db->fetchPairs($db->select()->from(array('sdc'=>'support_documents_categories'), array('*')) ->order('sdc.category') );

$result['items'][0] =  "Maak je keuze" ;


foreach ($options as $key => $value) {
	$result['items'][$key] = $value;
}



$this->addElement('select', 'category', array(
	'label' 		=> 'Categorie',
	'multiOptions'	=> $result['items'],
	'required' 		=>	true
));

//catagoryTitle
$this->addElement('select', 'categoryTitle', array(
	'label' 		=> 'Bestand',
	'RegisterInArrayValidator' => false,
	'disabled'		=>	true
));

//description
$this->addElement('textarea', 'description', array(
	'label' 		=> 	'Omschrijving',
	'validators' 	=> 	array(),
	'rows'			=> 	6,
	'value'			=> isset($this->getOwner()->view->attachment) ? 'Opgeslagen bijlage bij E-mail, ontvangen op ' . strftime('%A %d %B %Y om %H:%M', strtotime($this->getOwner()->view->attachment->parentEmail->date)) : ''
));

$this->addElement('select', 'visibleForUser', array(
	'label' 		=> 	'Zichtbaar',
	'multioptions' 	=> array(
        '0' => 'Intern',
        '1' => 'Voor de huurder en belegger',
        '2' => 'Voor de huurder',
        '3' => 'Voor de belegger',
        '9' => 'Extern',
	),
	'validators' 	=> 	array(),
));

//tags
$this->addElement('textarea', 'tags', array(
	'label' 		=> 	'Trefwoorden',
	'validators' 	=> 	array(),
	'rows'			=> 	2
));

//overrule
include('forms/support/overrule.php');

$this->addElement('text', 'objects', array('hidden' => true));


$filters = new Form_SubForm($this);
$this->addSubForm($filters, 'filters');

$projects = db()->fetchPairs(db()->select()->from(array('p'=>'projects'), array('id','name'))->order('name')->where('exploitation = ?', true));
$objectgroups = count($projects) > 0 ? db()->fetchPairs(db()->select()->from(array('og'=>'objectgroup'), array('id','description'))->order('description')->where('project = ?', array_keys($projects)[0])) : array();

$filters->addElement('select', 'project', array(
	'label' 		=> 	'Project',
	'multioptions' 	=> 	$projects,
	'validators' 	=> 	array(),
	'multiple'			=>	false,
	'registerInArrayValidator' => false
));

$filters->addElement('select', 'objectgroup', array(
	'label' 		=> 	'Objectgroup',
	'multioptions' 	=>  $objectgroups,
	'validators' 	=> 	array(),
	'multiple'			=>	false,
	'registerInArrayValidator' => false
));

$filters->addElement('button', 'filter', array(
	'label' 		=> 	'Filter',
	'validators' 	=> 	array(),
	'ignore'		=>	true
));

$this->addElement('submit', 'submitBtn', array(
	'label' => 'Opslaan',
));

//submit
if(!$this->getOwner()->view->is_add_action)
	$this->addButtons(false);
