<?

	//general form options
	$this->setAction('finance/add')
			->setMethod('post')
			->setAttrib('id', 'contactsadd')
			->setAttrib('class', 'contactsadd form small');

	$this->addElement('text', 'finance_company', array(
		'label' 		=> 	'Financier',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('text', 'contract', array(
		'label' 		=> 	'Hyp. nr.',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('text', 'initial_value', array(
		'label' 		=> 	'Hoofdsom',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('hidden', 'type', array(
		'label' 		=> 	false
	));

	$this->addElement('text', 'current_value', array(
		'label' 		=> 	'Actueel saldo ',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

    $this->addElement('text', 'payoff', array(
        'label' 		=> 	'Aflossing',
        'validators' 	=> 	array(),
        'required' 		=>	true
    ));

    $this->addElement('select', 'payoff_period', array(
        'label' 		=> 	'Aflossing per',
        'validators' 	=> 	array(),
        'multioptions' 	=> array(
            'monthly' => 'Maand',
            'quarterly' => 'Kwartaal'
        ),
        'required' 		=>	true
    ));

    $this->addElement('text', 'payoff_start', array(
        'label' 		=> 	'Eerste aflossings datum',
        'validators' 	=> 	array(),
        'required' 		=>	true
    ));

    $this->addElement('text', 'start_date', array(
        'label' 		=> 	'Ingangsdatum',
        'validators' 	=> 	array(),
        'required' 		=>	true
    ));

    $this->addElement('checkbox', 'inactive', array(
        'label' 		=> 	'Inactief?',
        'validators' 	=> 	array(),
     ));

    $this->addElement('checkbox', 'mortgage_registration', array(
        'label' 		=> 	'Hypothecaire inschrijving?',
        'validators' 	=> 	array(),
    ));

	$this->addElement('text', 'end_date', array(
		'label' 		=> 	'Einddatum',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	//Interest type
 	$this->addElement('select', 'interest_type', array(
			'label' 		=> 	'Rente type ',
			'multioptions' 	=> array(
									'vast' => 'Vast',
									'variabel' => 'Variabel'
								),
			'validators' 	=> 	array(),
	));

	//base
 	$this->addElement('text', 'base_component', array(
		'label'			=> 	'Grondslag (%) ',
		'validators' 	=> 	array(),
		'required' 		=>	false
	));

	//markup
 	$this->addElement('text', 'markup_component', array(
		'label'			=> 	'Opslag (%) ',
		'validators' 	=> 	array(),
		'required' 		=>	false
	));

	//Interest duration
 	$this->addElement('text', 'interest_duration', array(
		'label'			=> 	'Rentevaste periode (jaar) ',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('text', 'duration_years', array(
		'label' 		=> 	'Looptijd (jaar)',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('text', 'interest', array(
		'label' 		=> 	'Rente %',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

    $this->addElement('text', 'rent_end_date', array(
        'label' 		=> 	'Einddatum rente',
        'validators' 	=> 	array(),
        'required' 		=>	true
    ));

	//address
	$this->addElement('textarea', 'pawn', array(
		'label' 		=> 	'Voorwaarden',
		'validators' 	=> 	array(),
		'rows'			=> 	3,
		'required' 		=>	true
	));

	//note
	$this->addElement('textarea', 'note', array(
		'label' 		=> 	'Notitie',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));

	//overrule
	include('forms/support/overrule.php');

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
