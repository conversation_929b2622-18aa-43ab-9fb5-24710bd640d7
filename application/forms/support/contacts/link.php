<?

	//general form options
	$this->setAction('contacts/link')
			->setMethod('post')
			->setAttrib('id', 'contactslink')
			->setAttrib('class', 'contactsadd form small');


	$this->addElement('text', 'contact', array(
		'label' => 'Zoek',
		'validators' => array(),
		'required' 		=>	true
	));
	$this->addElement('hidden', 'contactType', array(
		'validators' => array()
	));
				
	//overrule
	include('forms/support/overrule.php');
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Koppel'
	));
