<?

	//general form options
	$this->setAction('contacts/link-user')
			->setMethod('post')
			->setAttrib('id', 'contactslinkuser')
			->setAttrib('class', 'contactsadd form small');


	$this->addElement('text', 'contactUser', [
		'label' => 'Zoek',
		'validators' => [],
		'required' 		=>	true
    ]);
	$this->addElement('hidden', 'contactUserType', [
		'validators' => []
    ]);

	//overrule
	include('forms/support/overrule.php');
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', [
		'label' => 'Koppel'
    ]);
