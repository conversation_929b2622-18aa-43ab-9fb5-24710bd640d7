<?

	//general form options
	$this->setAction('contacts/add')
			->setMethod('post')
			->setAttrib('id', 'contactsadd')
			->setAttrib('class', 'contactsadd form small');

	//name
	$this->addElement('text', 'name', array(
		'label' 		=> 	'Naam',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));
	
	$this->addElement('select', 'visibleForUser', array(
		'label' 		=> 	'Zichtbaar',
		'multioptions' 	=> array(
								'0' => 'Intern',
								'1' => 'Voor de klant'
							),
		'validators' 	=> 	array(),
	));	

	//phone
	$this->addElement('text', 'phone', array(
		'label' 		=> 	'Telefoon',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//address
	$this->addElement('textarea', 'address', array(
		'label' 		=> 	'Adres',
		'validators' 	=> 	array(),
		'rows'			=> 	3,
		'required' 		=>	true
	));	

	//email
	$this->addElement('text', 'email', array(
		'label' 		=> 	'Email',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//email
	$this->addElement('text', 'bankaccount', array(
		'label' 		=> 	'Bankrekening',
		'validators' 	=> 	array(array('validator' => 'elfProef'))
	));	

	//note
	$this->addElement('textarea', 'note', array(
		'label' 		=> 	'Notitie',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));
				
	//overrule
	include('forms/support/overrule.php');
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
