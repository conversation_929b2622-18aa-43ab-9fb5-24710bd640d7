<?

if(trim($currentComplaintType) == false) {
	$currentComplaintType = 'malfunction_or_request';
}

$perComplaintType = SupportStatusTypes::getSelectListsPerComplaintType();

$perComplaintTypeListForJson = [];
foreach ($perComplaintType as $complaintTypeName => $statusTypes) {
    foreach ($statusTypes as $statusTypeKey => $statusTypeName) {
        if (!isset($perComplaintTypeListForJson[$complaintTypeName])) {
            $perComplaintTypeListForJson[$complaintTypeName] = [];
        }

        $perComplaintTypeListForJson[$complaintTypeName][] = [
            'id' => $statusTypeKey,
            'name' => $statusTypeName,
        ];
    }
}

$this->addElement( 'select', 'status', [
	'label'        => 'Status',
	'multiOptions' => $perComplaintType[$currentComplaintType],
	'registerInArrayValidator' => false,
	'attribs' => [
		'options_per_type' => json_encode($perComplaintTypeListForJson),
	]
] );
