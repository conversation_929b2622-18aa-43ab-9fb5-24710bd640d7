<?

	//general form options
	$this->setAction('support/upload')
			->setMethod('post')
			->setAttrib('id', 'documentadd')
			->setAttrib('class', 'documentadd form small');

	//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Title',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//document
	$this->addElement('hidden', 'document', array(
		'label' 		=> 	'Document',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	//date
	$this->addElement('text', 'originalDate', array(
		'label' => 'Document datum',
		'validators' => array(),
	));

	//catagory
	$db = Zend_Db_Table::getDefaultAdapter();
	$this->addElement('select', 'category', array(
		'label' 		=> 'Categorie',
		'multiOptions'	=> $db->fetchPairs($db->select()->from(array('sdc'=>'support_documents_categories'), array('*')))
	));

	//catagory
	$db = Zend_Db_Table::getDefaultAdapter();
	$this->addElement('select', 'type', array(
		'label' 		=> 'Project type',
		'multiOptions'	=> $db->fetchPairs($db->select()->from(array('pt'=>'projects_type'), array('*'))),
		'value' 		=> 2
	));

	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));

	$this->addElement('select', 'visibleForUser', array(
		'label' 		=> 	'Zichtbaar',
		'multioptions' 	=> array(
								'1' => 'Voor de klant',
								'0' => 'Intern'
							),
		'validators' 	=> 	array(),
	));		

	//tags
	$this->addElement('textarea', 'tags', array(
		'label' 		=> 	'Trefwoorden',
		'validators' 	=> 	array(),
		'rows'			=> 	2
	));

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
