<?

	//general form options
	$this->setAction('spaces/add')
			->setMethod('post')
			->setAttrib('id', 'contactsadd')
			->setAttrib('class', 'contactsadd form small');

	$this->addElement('text', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

	$this->addElement('text', 'location', array(
		'label' 		=> 	'Locatie',
		'validators' 	=> 	array(),
		'attribs' => array(
			'title' => 'Bijvoorbeeld: Eerste verdieping',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
		)
	));

	$otModel = new ObjectType();
	$types = $otModel->fetchAll($otModel->select());
	$objectTypes = array();
	foreach ($types as $type)
		$objectTypes[$type['id']] = $type['name'];

	$this->addElement('select', 'type', array(
		'label' 		=> 	'Object type',
		'multioptions' 	=> 	$objectTypes,
		'validators' 	=> 	array(),
		'multiple'		=>	false
	));	

	$this->addElement('numberInput', 'size', array(
		'label' 		=> 	'Oppervlakte VVO (m2)',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Het oppervlakte in vierkante meters',
			'class' => 'forceAutoHint number',
			'hintOffset' => "{'x': -0, 'y': 0}",
		)
	));	

	$this->addElement('numberInput', 'size_bvo', array(
		'label' 		=> 	'Oppervlakte BVO (m2)',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Het oppervlakte in vierkante meters',
			'class' => 'forceAutoHint number',
			'hintOffset' => "{'x': -0, 'y': 0}",
		)
	));	

	$this->addElement('numberInput', 'price', array(
		'label' 		=> 	'Marktwaarde jaarprijs',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Jaarprijs exclusief BTW',
			'class' => 'forceAutoHint number',
			'hintOffset' => "{'x': -0, 'y': 0}",
		)
	));	
	
 	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen'
	));
