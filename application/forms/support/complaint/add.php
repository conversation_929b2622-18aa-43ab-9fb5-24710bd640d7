<?
	$is_company = in_array(loginManager::data()->rights, ['company', 'call_center']);
    $isCallCenterCompanyType = loginManager::data()->rights === 'call_center';

	if ( $this->getOwner() && intval( $this->getOwner()->complaintId ) > 0 ) {
		$complaint = SupportComplaints::get( $this->getOwner()->complaintId );
	}

	
	$url =  '';
	foreach($this->getOwner()->getAllUrlParams() as $param_name => $param_value)
		$url .= '/' . $param_name . '/' . $param_value;

	if($is_company)
		$action =  'company/complaints-add' . $url . ($_SERVER['REDIRECT_QUERY_STRING'] != '' ? '?' . $_SERVER['REDIRECT_QUERY_STRING'] : '/');	
	elseif($this->getOwner()->addPage)
		$action = 'complaint/add-page' . ($_SERVER['REDIRECT_QUERY_STRING'] != '' ? '?' . $_SERVER['REDIRECT_QUERY_STRING'] : '/');
	else
		$action = 'complaint/add' . ($this->getOwner()->addPage ? '/add-page/1/' : '');

	//general form options
	$this->setAction($action)
			->setMethod('post')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintadd form');

	//identifier
	$this->addElement('text', 'identifier', array(
		'label' 		=> 	$is_company ? 'Meldingsnr.' : 'Meldingsnummer',
		'validators' 	=> 	array(),
		'required' 		=>	false,
		'disabled' 		=>	true,
		'value'			=>	'Wordt automatisch aangemaakt tijdens het opslaan'
	));


    $this->addElement('checkbox', 'support_complaint_project', [
        'label' => 'Is het onderdeel van een werk?',
        'attribs' => [
            'title' => 'Is deze opdracht onderdeel van een werk?',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': 0, 'y': 0}"
        ]
    ]);


    $this->addElement($is_project ? 'hidden' : 'select', 'support_complaint_project_id', [
        'label' => '',
        'validators' => [],
        'value' => loginManager::data()->id,
        'disabled' => $is_project ? true : null
    ]);


//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Titel',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));

    $this->addElement('text', 'budget', array(
        'label' 		=> 	'Opdrachtwaarde',
        'validators' => [
            [
                'validator'	=> 'Int',
            ],
        ],
        'required' 		=>	!!Settings::get('support_complaint_budgets'),
    ));

	//date
	$this->addElement('text', 'date', array(
		'label' => 'Datum aanmelding',
		'validators' => array(),
	));
	
	//deadlineOffset
	$this->addElement('text', 'deadlineOffset', array(
		'label' => 'Reactietijd',
		'validators' => array(),
		'disabled' => true,
		'ignore' => true
	));

	//deadlineOffsetInHours
	$this->addElement('text', 'deadlineOffsetInHours', array(
		'label' => 'Reactietijd',
		'validators' => array(),
		'ignore' => true
	));
	
	//deadline
	$this->addElement('text', 'deadline', array(
		'label' => 'Datum deadline',
		'validators' => array(),
		'disabled' => true,
		'ignore' => true
	));
	

	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'rows'			=> 	8,
		'cols'			=> 	50,
		'required' 		=>	true
	));

	$recursedSelectList = ComplaintCategories::asRecursedSelectList('nl', false, $this->getOwner()->view->object,$this->getOwner()->view->category );
    $typeLabels = SupportComplaints::getLabels('nl');


// complaint_type
	$this->addElement( 'select', 'complaint_type', [
		'label'        => 'Type',
		'multiOptions' => $typeLabels
	] );

	// select the complaint_type in order to fill the complaint_category select with the correct options
	reset( $recursedSelectList['select_list'] );
	$complaintType = key( $recursedSelectList['select_list'] );

	if ( $complaint && $complaint->complaint_type ) {
		$complaintType = $complaint->complaint_type;
	}

	$this->addElement( 'select', 'complaint_category', [
		'label'        => 'Categorie',
		'multiOptions' => isset($recursedSelectList[ $complaintType ]) ? $recursedSelectList[ $complaintType ]['select_list'] : [],
		'registerInArrayValidator' => false,
	] );

	// select the complaint_category to be used to fill the complaint_subcategory select with the correct options
	reset( $recursedSelectList[ $complaintType ]['select_list'] );
	$complaintCategory = key( $recursedSelectList[ $complaintType ]['select_list'] );

	if ( $complaint && $complaint->category ) {
		$complaintCategory = $complaint->category;
	}

	$this->addElement( 'select', 'complaint_subcategory', [
		'label'        => 'Subcategorie',
		'multiOptions' => ((array) $recursedSelectList[ $complaintType ][$complaintCategory]['select_list']),
		'registerInArrayValidator' => false,
	] );


	// add form fields that are not submitted but used to have the complaint (sub) categories available so the two
	// fields above can be filled according to their upper neighbour by javascript
	foreach ( $recursedSelectList['select_list'] as $compTypeKey => $compTypeLabel ) {

		$this->addElement( 'select', "complaint_category_{$compTypeKey}", [
			'label'        => "Categorie {$compTypeLabel}",
			'multiOptions' => $recursedSelectList[$compTypeKey]['select_list'],
			'disabled' => true,
		] );

		foreach ( $recursedSelectList[$compTypeKey]['select_list'] as $catKey => $catLabel ) {

			$this->addElement( 'select', "complaint_subcategory_{$catKey}", [
				'label'        => "Categorie {$catLabel}",
				'multiOptions' => $recursedSelectList[$compTypeKey][$catKey]['select_list'],
				'disabled' => true,
			] );
		}
	}
	
	$this->addElement('select', 'urgency', array(
		'label' => 'Urgentie',
		'multiOptions'	=> array(
			'0' => 'Niet Urgent',
			'1' => 'Urgent'
		)
	));

	$this->addElement('select', 'component_id', [
		'label' => 'Component',
		'multiOptions'	=> $this->getOwner()->components,
	]);

	$this->addElement('select', 'service_charge', [
		'label' => 'Service kosten',
		'multiOptions'	=> [
			'unknown' => 'Niet bepaald',
			'yes' => 'Ja',
			'no' => 'Nee',
		]
	]);

	//via mail of telefoon
	$this->addElement( 'select', 'source', [
		'label'        => 'Via',
		'multiOptions' => SupportComplaints::getSourceLabels()
	] );

	//toewijzen
 		include('forms/support/assign.php');

	if ( $complaint && $complaint->complaint_type ) {
		$currentComplaintType = $complaint->complaint_type == 'complaint'? 'complaint': 'malfunction_or_request';
	}
	//status
	include('forms/support/status.php');
	
	//date_closed
	$this->addElement('select', 'demarcation', array(
		'label' => 'Demarcatie',
		'validators' => array(),
		'multiOptions'	=> array(
			'inside' => 'Binnen demarcatie',
			'outside' => 'Buiten demarcatie',
		),
		'attribs' => array(
			'title' => 'Kies voor de tekst ter afmelding naar de bewoner of de melding binnen demarcatie valt.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)			
	));	

	$this->addElement('text', 'date_closed', array(
		'label' => 'Datum opgelost',
		'validators' => array(),
	));	


	//date_closed
	$this->addElement('textarea', 'closed_description', array(
		'label' => 'Oplossing omschrijving',
		'validators' => array(),
		'rows'			=> 	8,
		'cols'			=> 	50,
        'required' => !$is_company
	));

	$type = $this->getOwner()->getParam('type');

    //inform
    $this->addElement('checkbox', 'inform', array(
        'label' => "Informeer huurder",
        'checked' => 'checked',
        'attribs' => [
            'title' => "Informeer de huurder via e-mail over de voortgang aan deze melding. Bij commerciële
            huurders is hieronder te selecteren wie er geïnformeerd moet worden.<br>
            Houd de Control of CTRL toets ingedrukt (of Command/CMD op een Apple Mac) om meer dan 1 ontvanger te
            selecteren of om een ontvanger te deselecteren.",
            'class' => 'forceAutoHint',
        ]
    ));

	$this->addElement('checkbox', 'access_allowed', array(
		'label' => 'Toestemming toetreding',
		'attribs' => array(
			'title' => 'De huurder heeft voor deze melding toestemming gegeven voor toetreden van het object.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));	
	
	$this->addElement('checkbox', 'access_allowed_presumed', array(
		'label' => 'Toetreding verondersteld',
		'attribs' => array(
			'title' => 'De huurder informeren dat er zonder tegenbericht uitgegaan wordt van toestemming tot toetreding. Deze tekst kan worden aangepast bij de project instellingen.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	//inform select
	$this->addElement( 'select', 'inform_select', [
		'label'                    => 'Stuur e-mail naar',
		'registerInArrayValidator' => false,
		'multiple'                 => 'multiple',
	] );

	//inform third
	$this->addElement($is_company ? 'hidden' : 'checkbox', 'inform_contact_toggle', array(
		'label' => $is_company ? '' : 'Servicepartij',
		'checked' => $is_company,
		'disabled' => $is_company ? true : null

	));

    //inform third
    $this->addElement($is_company ? 'hidden' : 'checkbox', 'send_appointment', array(
        'label' => $is_company ? '' : 'Afspraak maken toegestaan',
        'checked' => $is_company,
        'disabled' => $is_company ? true : null

    ));

	//inform third
	$this->addElement($is_company ? 'hidden' : 'select', 'inform_contact', array(
		'label' => '',
		'validators' => array(),
		'value' => loginManager::data()->id,
		'disabled' => $is_company ? true : null 
	));

	$employees = [
		0 => 'Algemeen E-mail adres bedrijf'
	];

    if ($complaint && $complaint->inform_contact > 0) {
        $company_employees = (new Company())->getTechnicalContactEmployees($complaint->inform_contact);

        foreach ($company_employees as $employee_id => $employee_name) {
            $employees[$employee_id] = $employee_name;
        }
    }

    //inform third specific contactperson
    $this->addElement($is_company ? 'hidden' : 'select', 'inform_contact_person', [
        'label' => '',
        'validators' => [],
        'value' => loginManager::data()->id,
        'disabled' => $is_company ? true : null,
		'registerInArrayValidator' => false,
		'multiOptions'	=> $employees
	]);

	$this->addElement('textarea', 'inform_contact_note', [
		'label' => 'Toelichting voor servicepartij',
		'rows' => 12,
		'cols' => 50,
        'disabled' => $is_company && !Settings::get('technicalparty_enable_edit_inform_note')  && !$isCallCenterCompanyType ? true : null,
    ]);
	
	//template
	$this->addElement('checkbox', 'use_as_template', array(
		'label' => 'Veelvoorkomend'
	));

	//overrule
	include('forms/support/overrule.php');

	//date
	$this->addElement('text', 'appointment_date', array(
		'label' => 'Datum afspraak',
		'validators' => array(),
	));		

	$this->addElement('select', 'appointment_action', array(
		'label' => 'Afspraak',
		'multiOptions'	=> array(
			'edit' => 'Huidige afspraak bewerken',
			'new' => 'Nieuwe afspraak',
		)
	));	

	$this->addElement('checkbox', 'appointment_edit_confirm', array(
		'label' => 'Akkoord bewoner',
		'attribs' => array(
			'title' => 'Akkoord ontvangen van de bewoner voor het aanpassen van de afspraak',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)		
	));			
	
	$this->addElement('textarea', 'appointment_description', array(
		'label' => 'Toelichting afspraak',
		'validators' => array(),
		'rows'			=> 	8,
		'cols'			=> 	50,
	));

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit_button', array(
		'label' => 'Opslaan'
	));
