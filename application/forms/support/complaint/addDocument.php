<?

	//general form options
	$this->setAction('complaint/document-add/')
			->setMethod('post')
			->setAttrib('id', 'documentadd')
			->setAttrib('class', 'documentadd form');

	//name
	$this->addElement('hidden', 'sessionid', array(
		'label' 		=> 	'',
		'validators' 	=> 	array(),
	));	
	
	//name
	$this->addElement('hidden', 'document', array(
		'label' 		=> 	'Document',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//note
	$this->addElement('text', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));

    //note
    $this->addElement('checkbox', 'visibleForUser', array(
        'label' 		=> 	'Zichtbaar servicepartij',
        'value'         =>  '1',
        'validators' 	=> 	array()
    ));
			
		//submit
	$this->addButtons(false);
    $this->addElement('button', 'cancel', [
        'label' => 'Annuleren',
        'class' => 'no-ajax'
    ]);

	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen',
		'class' => 'no-ajax'
	));
