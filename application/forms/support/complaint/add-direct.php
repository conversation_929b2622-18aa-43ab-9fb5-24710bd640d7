<?
	//general form options
	$this->setAction(in_array(loginManager::data()->rights, ['call_center', 'company']) ? 'company/complaints-add' : 'complaint/add-page')
			->setMethod('get')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintadd form');

	$this->addElement('hidden', 'type', array());
	$this->addElement('hidden', 'mapTo', array());
    $this->addElement('hidden', 'object', []);

	$this->addElement('text', 'map_to_multi_search', array(
		'label' 		=> 	'Koppelen',
		'validators' 	=> 	array(),
		'autocomplete' => 'off',
		'attribs' => array(
			'placeholder' => 'Zoek op adres, objectgroep of bewoner',
            'title' => 'Selecteer hier de bewoner voor wie de melding wordt gedaan. <br/> Is de het object leeg, selecteer dan het juiste object of de objectgroep',
            'class' => 'forceAutoHint',
			'autocompleter' => 'complaint/add-direct-search-results/',
			'autocompleter_options' => Zend_Json::encode(array(
				'hiddenElementId' => 'mapTo'
			))
		)	
	));

	$this->addButtons(false);
	$this->addElement('submit', 'submitButton', array(
		'label' => 'Melding aanmaken'
	));	
