<?
	$is_company = in_array(loginManager::data()->rights, ['company', 'call_center']);
    $isCallCenterCompanyType = loginManager::data()->rights === 'call_center';

	if ( $this->getOwner() && intval( $this->getOwner()->complaintId ) > 0 ) {
		$complaint = SupportComplaints::get( $this->getOwner()->complaintId );
	}

	
	$url =  '';
	foreach($this->getOwner()->getAllUrlParams() as $param_name => $param_value)
		$url .= '/' . $param_name . '/' . $param_value;

	if($is_company)
		$action =  'company/complaints-add' . $url . ($_SERVER['REDIRECT_QUERY_STRING'] != '' ? '?' . $_SERVER['REDIRECT_QUERY_STRING'] : '/');	
	elseif($this->getOwner()->addPage)
		$action = 'complaint/add-page' . ($_SERVER['REDIRECT_QUERY_STRING'] != '' ? '?' . $_SERVER['REDIRECT_QUERY_STRING'] : '/');
	else
		$action = 'complaint/add' . ($this->getOwner()->addPage ? '/add-page/1/' : '');

	//general form options
	$this->setAction($action)
			->setMethod('post')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintadd form');

	//identifier
	$this->addElement('text', 'identifier', array(
		'label' 		=> 	$is_company ? 'Meldingsnr.' : 'Meldingsnummer',
		'validators' 	=> 	array(),
		'required' 		=>	false,
		'disabled' 		=>	true,
		'value'			=>	'Wordt automatisch aangemaakt tijdens het opslaan'
	));


//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	'Korte omschrijving',
        'attribs' => array(
            'placeholder' => 'Noteer hier een korte titel van de melding.',
            'autocompleter' => 'complaint/add-direct-search-results/',
            'autocompleter_options' => Zend_Json::encode(array(
                'hiddenElementId' => 'mapTo'
            ))
        ),
		'validators' 	=> 	array(),
		'required' 		=>	true
	));


	//date
	$this->addElement('text', 'date', array(
		'label' => 'Datum aanmelding',
		'validators' => array(),
	));
	
	//deadlineOffset
	$this->addElement('text', 'deadlineOffset', array(
		'label' => 'Reactietijd',
		'validators' => array(),
		'disabled' => true,
		'ignore' => true
	));

	//deadlineOffsetInHours
	$this->addElement('text', 'deadlineOffsetInHours', array(
		'label' => 'Reactietijd',
		'validators' => array(),
		'ignore' => true
	));
	
	//deadline
	$this->addElement('text', 'deadline', array(
		'label' => 'Datum deadline',
		'validators' => array(),
		'disabled' => true,
		'ignore' => true
	));


	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	'Omschrijving',
        'attribs' => array(
            'placeholder' => 'Noteer hier de inhoud van de storing, klacht of melding. Noteer ook details als bijvoorbeeld welke ruimte/kamer het betreft',
            'autocompleter' => 'complaint/add-direct-search-results/',
            'autocompleter_options' => Zend_Json::encode(array(
                'hiddenElementId' => 'mapTo'
            ))
        ),
		'validators' 	=> 	array(),
		'rows'			=> 	8,
		'cols'			=> 	50,
		'required' 		=>	true
	));

	$recursedSelectList = ComplaintCategories::asRecursedSelectList();

	// complaint_type
	$this->addElement( 'select', 'complaint_type', [
		'label'        => 'Type',
        'attribs' => array(
            'title' => 'Storing = Voor als er iets kapot is aan het object <br/>
                        Klacht = niet technisch, bijvoorbeeld over dienstverlening/geluidsoverlast <br/>
                        Melding = al het overige wat u kwijt wil <br/>',
            'class' => 'forceAutoHint',
        ),
		'multiOptions' => $recursedSelectList['select_list']
	] );

	// select the complaint_type in order to fill the complaint_category select with the correct options
	reset( $recursedSelectList['select_list'] );
	$complaintType = key( $recursedSelectList['select_list'] );

	if ( $complaint && $complaint->complaint_type ) {
		$complaintType = $complaint->complaint_type;
	}

	$this->addElement( 'select', 'complaint_category', [
		'label'        => 'Categorie',
        'attribs' => array(
            'title' => 'Kies de categorie en subcategorie die het <br/>
                        meest overeenkomt met de melding',
            'class' => 'forceAutoHint',
        ),
		'multiOptions' => isset($recursedSelectList[ $complaintType ]) ? $recursedSelectList[ $complaintType ]['select_list'] : [],
		'registerInArrayValidator' => false,
	] );

	// select the complaint_category to be used to fill the complaint_subcategory select with the correct options
	reset( $recursedSelectList[ $complaintType ]['select_list'] );
	$complaintCategory = key( $recursedSelectList[ $complaintType ]['select_list'] );

	if ( $complaint && $complaint->category ) {
		$complaintCategory = $complaint->category;
	}

	$this->addElement( 'select', 'complaint_subcategory', [
		'label'        => 'Subcategorie',
		'multiOptions' => ((array) $recursedSelectList[ $complaintType ][$complaintCategory]['select_list']),
		'registerInArrayValidator' => false,
	] );


	// add form fields that are not submitted but used to have the complaint (sub) categories available so the two
	// fields above can be filled according to their upper neighbour by javascript
	foreach ( $recursedSelectList['select_list'] as $compTypeKey => $compTypeLabel ) {

		$this->addElement( 'select', "complaint_category_{$compTypeKey}", [
			'label'        => "Categorie {$compTypeLabel}",
			'multiOptions' => $recursedSelectList[$compTypeKey]['select_list'],
			'disabled' => true,
		] );

		foreach ( $recursedSelectList[$compTypeKey]['select_list'] as $catKey => $catLabel ) {

			$this->addElement( 'select', "complaint_subcategory_{$catKey}", [
				'label'        => "Categorie {$catLabel}",
				'multiOptions' => $recursedSelectList[$compTypeKey][$catKey]['select_list'],
				'disabled' => true,
			] );
		}
	}

	$this->addElement('select', 'urgency', array(
		'label' => 'Urgentie',
        'attribs' => array(
            'title' => 'De Urgentie staat standaard op niet-urgent. De binnendienst zal  <br/>
                        beoordelen of uw storing, klacht of melding urgent is.',
            'class' => 'forceAutoHint',
        ),
		'multiOptions'	=> array(
			'0' => 'Niet Urgent',
			'1' => 'Urgent'
		)
	));


	//via mail of telefoon
	$this->addElement( 'select', 'source', [
		'label'        => 'Via',
		'multiOptions' => SupportComplaints::getSourceLabels()
	] );

	//toewijzen
	if(!$is_company) {
        include('forms/support/assign.php');
    } else {
	    if($this->getOwner()->getParam('mapTo') && $this->getOwner()->getParam('type')) {
            $hierarchy = new Hierarchy($this->getOwner()->getParam('mapTo'), $this->getOwner()->getParam('type'));

            if($projectId = $hierarchy->_data['project']) {
                $technicalAssignee = (new Projects())->getTechnicalAssignee($projectId);

                if ($technicalAssignee) {
                    $this->addElement('hidden', 'assign', [
                        'label' => '',
                        'value' => $technicalAssignee['id'],
                    ]);
                }
            }
        }
    }

	if ( $complaint && $complaint->complaint_type ) {
		$currentComplaintType = $complaint->complaint_type == 'complaint'? 'complaint': 'malfunction_or_request';
	}
	//status
	include('forms/support/status.php');
	
	//date_closed
	$this->addElement('select', 'demarcation', array(
		'label' => 'Demarcatie',
		'validators' => array(),
		'multiOptions'	=> array(
			'inside' => 'Binnen demarcatie',
			'outside' => 'Buiten demarcatie',
		),
		'attribs' => array(
			'title' => 'Kies voor de tekst ter afmelding naar de bewoner of de melding binnen demarcatie valt.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)			
	));	

	$this->addElement('text', 'date_closed', array(
		'label' => 'Datum opgelost',
		'validators' => array(),
	));	


	//date_closed
	$this->addElement('textarea', 'closed_description', array(
		'label' => 'Oplossing omschrijving',
		'validators' => array(),
		'rows'			=> 	8,
		'cols'			=> 	50,
		'required' 		=>	true
	));

	$type = $this->getOwner()->getParam('type');

    //inform
    $this->addElement('checkbox', 'inform', array(
        'label' => "Informeer huurder",
        'checked' => 'checked',
        'attribs' => [
            'title' => "Informeer de huurder via e-mail over de voortgang aan deze melding. Bij commerciële
            huurders is hieronder te selecteren wie er geïnformeerd moet worden.<br>
            Houd de Control of CTRL toets ingedrukt (of Command/CMD op een Apple Mac) om meer dan 1 ontvanger te
            selecteren of om een ontvanger te deselecteren.",
            'class' => 'forceAutoHint',
        ]
    ));

	$this->addElement('checkbox', 'access_allowed', array(
		'label' => 'Toestemming toetreding',
		'attribs' => array(
			'title' => 'De huurder heeft voor deze melding toestemming gegeven voor toetreden van het object.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));	
	
	$this->addElement('checkbox', 'access_allowed_presumed', array(
		'label' => 'Toetreding verondersteld',
		'attribs' => array(
			'title' => 'De huurder informeren dat er zonder tegenbericht uitgegaan wordt van toestemming tot toetreding. Deze tekst kan worden aangepast bij de project instellingen.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	//inform select
	$this->addElement( 'select', 'inform_select', [
		'label'                    => 'Stuur e-mail naar',
		'registerInArrayValidator' => false,
		'multiple'                 => 'multiple',
	] );


    $hideThirdSelection = $is_company && (!$isCallCenterCompanyType || Settings::get('serviceparty_disable_assign_technical_party') );
    $this->addElement($hideThirdSelection ? 'hidden' : 'checkbox', 'inform_contact_toggle', [
		'label' => $hideThirdSelection ? '' : 'Servicepartij',
		'checked' => $hideThirdSelection,
		'disabled' => $hideThirdSelection ? true : null
    ]);

    $this->addElement($hideThirdSelection ? 'hidden' : 'checkbox', 'send_appointment', [
        'label' => $hideThirdSelection ? '' : 'Afspraak maken toegestaan',
        'checked' => $hideThirdSelection,
        'disabled' => $hideThirdSelection ? true : null

    ]);

	$this->addElement($hideThirdSelection ? 'hidden' : 'select', 'inform_contact', [
		'label' => '',
		'value' => $isCallCenterCompanyType ? null : loginManager::data()->id,
		'disabled' => $hideThirdSelection ? true : null
    ]);

	$employees = [
		0 => 'Algemeen E-mail adres bedrijf'
	];

    if ($complaint && $complaint->inform_contact > 0) {
        $company_employees = (new Company())->getTechnicalContactEmployees($complaint->inform_contact);

        foreach ($company_employees as $employee_id => $employee_name) {
            $employees[$employee_id] = $employee_name;
        }
    }

    //inform third specific contactperson
    $this->addElement($hideThirdSelection ? 'hidden' : 'select', 'inform_contact_person', [
        'label' => '',
        'validators' => [],
        'value' => $isCallCenterCompanyType ? null : loginManager::data()->id,
        'disabled' => $hideThirdSelection ? true : null,
		'registerInArrayValidator' => false,
		'multiOptions'	=> $employees
	]);

	$this->addElement($hideThirdSelection ? 'hidden' : 'textarea', 'inform_contact_note', array(
		'label' => $hideThirdSelection ? '' : 'Toelichting voor servicepartij',
		'validators' => array(),
		'rows'			=> 	4,
		'cols'			=> 	50,
	));

	//template
	$this->addElement('checkbox', 'use_as_template', array(
		'label' => 'Veelvoorkomend'
	));

	//overrule
	include('forms/support/overrule.php');

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit_button', array(
		'label' => 'Versturen'
	));
