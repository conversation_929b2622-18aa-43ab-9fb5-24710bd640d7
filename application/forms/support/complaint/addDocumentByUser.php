<?

	//general form options
	$this->setAction('complaint/document-add-by-user/')
			->setMethod('post')
			->setAttrib('id', 'documentadd')
			->setAttrib('class', 'documentadd form');

	//name
	$this->addElement('hidden', 'sessionid', array(
		'label' 		=> 	'',
		'validators' 	=> 	array(),
	));	
	
	//name
	$this->addElement('hidden', 'document', array(
		'label' 		=> 	'Document',
		'validators' 	=> 	array(),
		'required' 		=>	true
	));	

	//note
	$this->addElement('text', 'description', array(
		'label' 		=> 	'Omschrijving',
		'validators' 	=> 	array(),
		'rows'			=> 	8
	));

			
		//submit
	$this->addButtons(false);
    $this->addElement('button', 'cancel', [
        'label' => 'Annuleren',
        'class' => 'no-ajax'
    ]);

	$this->addElement('submit', 'submit', array(
		'label' => 'Toevoegen',
		'class' => 'no-ajax'
	));
