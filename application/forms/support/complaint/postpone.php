<?
	//general form options
	$this->setAction('complaint/postpone')
			->setMethod('post')
			->setAttrib('id', 'postpone')
			->setAttrib('class', 'postpone form');

	//date
	$this->addElement('text', 'date', array(
		'label' 		=> 	'Nieuwe deadline',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'value'			=>	$this->getOwner()->deadline->date ? $this->getOwner()->deadline->date : date('Y-m-d H:i:s')
	));
	
	//text
	$this->addElement('textarea', 'text', array(
		'label' 		=> 	'Uitstelreden:',
        'attribs' => [
            'rows' => 8,
            'cols' => 32
        ],
		'validators' 	=> 	array()
	));

	$this->addElement('checkbox', 'inform_user', array(
		'label' => 'Informeer huurder',
		'attribs' => array(
			'title' => 'Door deze optie te selecteren ontvangt de huurder per e-mail de reden van uitstel zoals hierboven beschreven',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));
	
	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => 'Versturen'
	));
