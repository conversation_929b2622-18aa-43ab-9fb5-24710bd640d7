<?
	//general form options
	$this->setAction('complaint/add')
			->setMethod('post')
			->setAttrib('id', 'complaintadd')
			->setAttrib('class', 'complaintadd form');

	//identifier
	$this->addElement('text', 'identifier', array(
		'label' 		=> 	'Incidentnummer',
		'validators' 	=> 	array(),
		'required' 		=>	false,
		'disabled' 		=>	true,
		'placeholder'	=>	'Wordt automatisch aangemaakt tijdens het opslaan'
	));
	
	//name
	$this->addElement('text', 'title', array(
		'label' 		=> 	translate()->_('about'),
		'validators' 	=> 	array(),
		'placeholder'	=>	translate()->_('description_complaint_short'), '',
		'required' 		=>	true
	));


    $objects = Objectusers::getActiveObjectsForUser(loginManager::data()->id);
  
    $this->addElement( 'select', 'for_object', [
        'label'        => translate()->_('for_object'),
        'multioptions' => $objects ,
        'registerInArrayValidator' => false,
        'required' => true,

    ] );


	$recursedSelectList = ComplaintCategories::asRecursedSelectList(loginManager::data()->info['language'],true);


    // if the complaint type does not have any enabled categories, unset it for the select options
    foreach($recursedSelectList['select_list'] as $typeKey => $typeItem){
        if(count($recursedSelectList[$typeKey]['select_list']) > 0){
            continue;
        }

        unset($recursedSelectList['select_list'][$typeKey]);
    }
    
	// complaint_type
	$this->addElement( 'select', 'complaint_type', [
		'label'        => 'Type',
		'multiOptions' => $recursedSelectList['select_list']
	] );

	// select the complaint_type in order to fill the complaint_category select with the correct options
	reset( $recursedSelectList['select_list'] );
	$complaintType = key( $recursedSelectList['select_list'] );

	if ( $complaint && $complaint->complaint_type ) {
		$complaintType = $complaint->complaint_type;
	}

    $categoryValidator = new Zend_Validate_Callback(function ($value) {return is_numeric($value); });

	$this->addElement( 'select', 'complaint_category', [
		'label'        => translate()->_('category'),
		'multiOptions' => $recursedSelectList[ $complaintType ]['select_list'],
		'registerInArrayValidator' => false,
        'required' => true,
        'validators' => [['validator' => $categoryValidator]],
	] );

    $this->addElement('html', 'complaint_category_hint', []);

	// select the complaint_category to be used to fill the complaint_subcategory select with the correct options
	reset( $recursedSelectList[ $complaintType ]['select_list'] );
	$complaintCategory = key( $recursedSelectList[ $complaintType ]['select_list'] );

	if ( $complaint && $complaint->category ) {
		$complaintCategory = $complaint->category;
	}

	$this->addElement( 'select', 'complaint_subcategory', [
		'label'        => translate()->_('sub_category'),
		'multiOptions' => (array) $recursedSelectList[ $complaintType ][$complaintCategory]['select_list'],
		'registerInArrayValidator' => false,
        'validators' => [['validator' => $categoryValidator]],
	] );

    $this->addElement('html', 'complaint_subcategory_hint', []);

	// add form fields that are not submitted but used to have the complaint (sub) categories available so the two
	// fields above can be filled according to their upper neighbour by javascript
	foreach ( $recursedSelectList['select_list'] as $compTypeKey => $compTypeLabel ) {

		$this->addElement( 'select', "complaint_category_{$compTypeKey}", [
			'label'        => "Categorie {$compTypeLabel}",
			'multiOptions' => $recursedSelectList[$compTypeKey]['select_list'],
			'disabled' => true,
		] );

		foreach ( $recursedSelectList[$compTypeKey]['select_list'] as $catKey => $catLabel ) {

			$this->addElement( 'select', "complaint_subcategory_{$catKey}", [
				'label'        => "Categorie {$catLabel}",
				'multiOptions' => $recursedSelectList[$compTypeKey][$catKey]['select_list'],
				'disabled' => true,
			] );
		}
	}
	

	// add one element per complaint category
	foreach ((array) $all['complaint'] as $c) {
		if(count($c['subcategories']) > 0) {

			$subCats = array();
			foreach ($c['subcategories'] as $subCat) {
				$subCats[$subCat['id']] = $subCat['name'];
			}

			$this->addElement('select', 'complaint_technical_location_'.$c['id'], array(
				'label' => 'Locatie',
				'multiOptions' => $subCats,
			));
		}
	}


	//description
	$this->addElement('textarea', 'description', array(
		'label' 		=> 	translate()->_('description'),
		'validators' 	=> 	array(),
		'rows'			=> 	8,
		'placeholder'	=>	translate()->_('description_complaint'),
		'required' 		=>	true
	));

	$this->addElement('checkbox', 'access_allowed', array(
		'label' 		=> 	translate()->_('complaint_access_allowed'),
		'validators' 	=> 	array()
	));

	//submit
	$this->addButtons(false);
	$this->addElement('submit', 'submit', array(
		'label' => translate()->_('send')
	));
