<?

$this->setAction('maintenance/add')
    ->setMethod('post')
    ->setAttrib('id', 'maintenanceadd')
    ->setAttrib('class', 'maintenanceadd form');

$typeParam = $this->getOwner()->getParam('type');
$this->addElement('hidden', 'type', [
    'value' => $typeParam,
]);

$mapToParam = $this->getOwner()->getParam('mapTo');
$this->addElement('hidden', 'mapTo', [
    'value' => $mapToParam,
]);

$this->addElement('hidden', 'identifier', [
    'value' => Maintenance::generateIdentifier()
]);

$this->addElement('text', 'title', [
    'label' => 'Titel',
    'validators' => [],
    'required' => true
]);

$this->addElement('textarea', 'description', [
    'label' => 'Omschrijving',
    'validators' => [],
    'required' => true,
    'rows' => 8
]);


$this->addElement('select', 'activity_link', [
    'label' => 'Herhaalschema',
    'multiOptions'	=> ['' => 'Maak een keuze'] + Activities::getListForSelect('maintenance'),
    'validators' => [],
    'required' => false
]);

$this->addElement('text', 'start', [
    'label' => 'Invoerdatum',
    'validators' => [],
    'required' => true
]);




include('forms/support/assign.php');
$this->assignNote->setLabel('Toelichting voor servicepartij');

include('forms/support/status.php');

$contacts = (array)(new SupportContacts())->getFor($mapToParam, $typeParam);

$hasContacts = count($contacts) > 0;

if ($hasContacts) {
    $contacts = ['false' => 'Selecteer een derde partij'] + $contacts;
}

$this->addElement('select', 'inform_select', [
    'label' => '',
    'registerInArrayValidator' => false,
    'multiple' => 'multiple',
]);

$this->addElement('checkbox', 'inform_contact_toggle', [
    'label' => 'Servicepartij',
    'checked' => false,
    'ignore' => !$hasContacts,
    'required' => $hasContacts,
]);

if(!$hasContacts) {
    $this->inform_contact_toggle
        ->setAttrib('disabled', true)
        ->setAttrib('title', 'Toewijzen is niet mogelijk omdat er geen servicepartij/contact ingevoerd is.')
        ->setAttrib('class', 'forceAutoHint');
}

$this->addElement('select', 'inform_contact', [
    'label' => '',
    'validators' => [],
    'registerInArrayValidator' => false,
    'multiOptions' => $contacts,
    'ignore' => !$hasContacts,
    'required' => $hasContacts,
]);

if(!$hasContacts) {
    $this->inform_contact
        ->setAttrib('disabled', true);
}

include('forms/support/overrule.php');

$this->addButtons(false);
$this->addElement('submit', 'submit', [
    'label' => $this->getOwner()->getParam('id') ? 'Bewerken' : 'Toevoegen'
]);
