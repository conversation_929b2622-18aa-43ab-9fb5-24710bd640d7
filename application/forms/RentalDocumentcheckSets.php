<?


    //general form options
    $this->setAction('')
        ->setMethod('post')
        ->setAttrib('enctype', 'multipart/form-data')
        ->setAttrib('class', 'form');


    $RentalDocumentcheckSets = new Form_SubForm($this);
    $RentalDocumentcheckSets->setAttrib('title', 'Algemene gegevens');
    $this->addSubForm($RentalDocumentcheckSets, 'rental_documentcheck_sets_form');

    $RentalDocumentcheckSets->addElement('text', 'name', array(
        'label' => 'Naam',
        'required' => true,
    ));

    $RentalDocumentcheckSets->addElement('select', 'privacy', [
        'label' => 'Privacy statement',
        'multiOptions' => ['' => 'Maak een keuze'] + RentalDocumentcheckParts::getParts('privacy'),
        'required' => true
    ]);

    $RentalDocumentcheckSets->addElement('select', 'general', [
        'label' => 'Algemene tekst',
        'multiOptions' => ['' => 'Maak een keuze'] + RentalDocumentcheckParts::getParts('general'),
        'required' => true
    ]);

    $RentalDocumentcheckSets->addElement('select', 'payroll_undetermined', [
        'label' => 'Loondienst onbepaald',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('payroll_undetermined'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'payroll_determined', [
        'label' => 'Loondienst bepaald',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('payroll_determined'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'business_owner', [
        'label' => 'Ondernemer',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('business_owner'),
     ]);


    $RentalDocumentcheckSets->addElement('select', 'freelancer', [
        'label' => 'ZZP',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('freelancer'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'retirement', [
        'label' => 'Pensioen',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('retirement'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'social_benefits', [
        'label' => 'Uitkering',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('social_benefits'),
    ]);

    $RentalDocumentcheckSets->addElement('select', 'student', [
        'label' => 'Student',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('student'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'owner_occupied_home_sold', [
        'label' => 'Koopwoning verkocht',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('owner_occupied_home_sold'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'owner_occupied_home_unsold', [
        'label' => 'Koopwoning niet verkocht',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('owner_occupied_home_unsold'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'rental_house', [
        'label' => 'Huurwoning',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('rental_house'),
     ]);

    $RentalDocumentcheckSets->addElement('select', 'home_living', [
        'label' => 'Thuiswonend',
        'multiOptions' => [nullValue() => 'Verwerkt in algemene tekst'] + RentalDocumentcheckParts::getParts('home_living'),
    ]);


    $RentalDocumentcheckSets->addElement('hidden', 'source', array(
        'value' => 'user',
    ));

    //submit
    $this->addElement('submit', 'aanmaken', array(
        'label' => 'Opslaan'
    ));

