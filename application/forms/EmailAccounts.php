<?	

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'emailAccount')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'emailAccount form');


	$general = new Form_SubForm($this);
	$general->setAttrib('title', 'Algemene instellingen');
	$this->addSubForm($general, 'general');

		//name
		$general->addElement('text', 'name', array(
			'label' => 'Naam',
			'required' => true,
			'attribs' => array(
				'title' => 'Alleen letters, geen leestekens of spaties',
				'class' => 'forceAutoHint'
			)
		));

		$number_of_accounts = db()->fetchOne(db()->select()->from('email_accounts', array('count' => 'COUNT(`id`)')));



		//name
		$general->addElement('text', 'address', array(
			'label' => 'E-mail adres',
			'required' => true
		));

        $general->addElement('checkbox', 'dashboard', array(
            'label' => 'Actief',
            'attribs' => array(
                'title' => 'Wordt getoond als inbox op het dashboard en binnenkomende berichten worden automatisch geimporteerd',
                'class' => 'forceAutoHint'
            )
        ));





$settings = new Form_SubForm($this);
$settings->setAttrib('title', 'Voorkeuren');
$this->addSubForm($settings, 'settings');

if(loginManager::data()->rights != 'admin'){
    $settings->addElement('checkbox', 'system', array(
        'label' => 'Systeem',
        'attribs' => array(
            'title' => 'Berichten verzonden vanuit deze account worden gemarkeerd als zijnde systeem berichten',
            'class' => 'forceAutoHint'
        )
    ));
}

//default
$settings->addElement('checkbox', 'default', array(
    'label' => 'Standaard',
    'value' => $number_of_accounts == 0,
    'attribs' => array(
        'title' => 'Deze account gebruiken als standaard account voor het verzenden van berichten, 
                            waaronder belangrijke berichtgeving van het systeem aan u. Er moet daarom altijd een 
                            standaard account zijn.',
        'class' => 'forceAutoHint'
    )
));


$settings->addElement('checkbox', 'invoice', array(
    'label' => 'Verzending facturen',
    'attribs' => array(
        'title' => 'Dit mailaccount gebruiken voor verzending van de facturen (prolongatie en handmatig) indien bij geen mail account dit is ingesteld zal voor de facturatie het standaard mail adres gebruikt worden',
        'class' => 'forceAutoHint'
    )
));


if(Settings::get('modules_rental')):


    $settings->addElement('checkbox', 'rental', array(
        'label' => 'Mailaccount verhuur',
        'attribs' => array(
            'title' => 'Dit mailaccount gebruiken voor verzending van systeemmails in de verhuurmodule',
            'class' => 'forceAutoHint'
        )
    ));


endif;

	$connection = new Form_SubForm($this);
	$connection->setAttrib('title', 'Verbinding');
	$this->addSubForm($connection, 'connection');


        $connection->addElement('html', 'text', array(
            'label' => '',
            'html' => '<div style="font-style: italic; color: #8a6d3b; background-color: #fcf8e3; border: 1px solid #faebcc; padding: 15px; font-size: 11px;">
                            <h1>Verbinding op basis van gebruikersnaam en wachtwoord</h1>
                            <p>Hieronder vindt u de configuratie voor het opzetten van een verbinding tussen Omniboxx en uw mailserver met behulp van uw gebruikersnaam en wachtwoord.</p>
                            <br>
                            <h1>Verbinding op basis van moderne authenticatie</h1>
                            <p><b>Let op! het hieronderstaande heeft betrekking op alle office365/Outlook mailboxen!</b></p>
                            <p>Indien u een verbinding wilt maken met behulp van moderne authenticatie, vult u het veld voor gebruikersnaam in met het e-mailadres van de mailbox. Het wachtwoordveld kan leeg worden gelaten.</p>
                            <p>Nadat u alle velden correct hebt ingevoerd en op de knop "Account opslaan" hebt geklikt, wordt u doorgestuurd naar een overzichtspagina van al uw e-mailaccounts.</p>
                            <p>Op deze pagina vindt u de benodigde informatie voor het koppelen van uw e-mailaccount met behulp van moderne authenticatie.</p>
                            <p><b>Let op!</b> Het testen van de verbinding kan pas worden uitgevoerd nadat de bovenstaande stap is voltooid.</p>
                        </div>'
        ));


        $connection->addElement('select', 'incoming', array(
			'label' => 'Binnenkomend',
			'multiOptions' => db()->fetchPairs(
				db()->select()
					->from(array('es'=>'email_servers'), array('id', 'name' => 'CONCAT(es.`host`, " (", es.`type`, ")")'))
					->where('es.type != ?', 'SMTP')
			)
		));

		$connection->addElement('text', 'username', array(
			'label' => 'Gebruikersnaam',
			'attribs' => array(
				'title' => 'Gebruikersnaam om in te loggen op de binnenkomende E-mail server',
				'class' => 'forceAutoHint'
			)
		));

		$connection->addElement('password', 'password', array(
			'label' => 'Wachtwoord',
			'renderPassword' => true,
			'attribs' => array(
				'title' => 'Wachtwoord om in te loggen op de binnenkomende E-mail server. Leeg laten om niet te wijzigen.',
				'class' => 'forceAutoHint'
			)
		));

        $connection->addElement('select', 'outgoing', array(
            'label' => 'Uitgaand',
            'disabled' => 'disabled',
            'multiOptions' => array_merge(
                array(0 => 'SendGrid'),
                db()->fetchPairs(db()->select()->from(array('es'=>'email_servers'), array('id', 'host'))->where('es.type = ?', 'SMTP'))
            )
        ));

	$signature = new Form_SubForm($this);
	$signature->setAttrib('title', 'Handtekening');
	$this->addSubForm($signature, 'signature');
		
		$signature->addElement('checkbox', 'enabled', array(
			'label' => 'Ingeschakeld',
		));

		$signature->addElement('textarea', 'text', array(
			'label' => 'Handtekening tekst',
			'rows' => 10,
			'value' => "Met vriendelijke groet,\n\n[user_name]\n[company_name]",
			'attribs' => array(
				'title' => 'In deze tekst kunnen de volgende variabelen worden toegepast (<b>LET OP: na het aanpassen eerst uitloggen en inloggen in Omniboxx om de wijzingen te verwerken.</b>):<br /><br /><b>[user_name]</b>: Naam huidige gebruiker<br /><b>[company_name]</b>: Naam van het bedrijf<br /><b>[address_street]</b>: Straat<br /><b>[address_number]</b>: Huisnummer<br /><b>[address_zipcode]</b>: Postcode<br /><b>[address_city]</b>: Stad<br /><b>[phone]</b>: Telefoon<br /><b>[email]</b>: E-mail<br /><b>[phone_user]</b>: Telefoon direct<br /><b>[aanwezigheid]</b>: Aanwezigheid<br /><b>[website]</b>: Website<br /><b>[logo]</b>: Logo <small>Afhankelijk van instelling: <i>Positie logo</i></small>',
				'class' => 'forceAutoHint'
			)
		));



	$logo = new Form_SubForm($this);
	$logo->setAttrib('title', 'Logo');
	$this->addSubForm($logo, 'logo');
		
		$logo->addElement('select', 'location_logo', array(
			'label' => 'Positie Logo',
			'multiOptions' => array(
									'top' => 'Bovenaan',
									'bottom' => 'Onderaan',
									'signature' => 'Zelf definieren in de handtekening'
								),
		));	

		$logo->addElement('hidden', 'preview', array(
			'label' => 'Huidige logo',
		));	
		$logo->addElement('file', 'upload', array(
			'label' => 'Nieuwe uploaden',
		));	


	$autoreply = new Form_SubForm($this);
	$autoreply->setAttrib('title', 'Automatisch antwoord');
	$this->addSubForm($autoreply, 'autoreply');
		
		$autoreply->addElement('checkbox', 'enabled', array(
			'label' => 'Ingeschakeld',
		));

		$autoreply->addElement('textarea', 'text', array(
			'label' => 'Antwoord tekst',
			'rows' => 10
		));		

	$exclusive = new Form_SubForm($this);
	$exclusive->setAttrib('title', 'Exclusieve account');
	$this->addSubForm($exclusive, 'exclusive');
		
		$exclusive->addElement('html', 'text', array(
			'label' => '',
			'html' => '<div style="font-style: italic; font-size: 11px;">De account mag exclusief door ' . (Settings::get('software_type') != 'energy' ? 'Omniboxx' : 'VaSof') . ' gebruikt worden. De account mag niet ingesteld zijn op bijvoorbeeld een andere E-mail client, Outlook server of telefoon.<br /><br />Als de account toch op een ander apparaat ingesteld is zullen berichten enkel in ' . (Settings::get('software_type') != 'energy' ? 'Omniboxx' : 'VaSof') . ', of enkele op het andere apparaat binnenkomen.</div>'
		));

		$exclusive->addElement('checkbox', 'exclusive', array(
			'label' => 'Akkoord',
			'required' => true
		));	

if(Settings::get('modules_user_project') && is_null($this->getOwner()->view->id)){
	$project_limited = new Form_SubForm($this);
	$project_limited->setAttrib('title', 'Vestigings medewerkers');
	$this->addSubForm($project_limited, 'project_limited');
		
		$project_limited->addElement('html', 'text', array(
			'label' => '',
			'html' => '<div style="font-style: italic; font-size: 11px;">Het nieuwe E-mail account zal niet zichtbaar zijn voor vestigings medewerkers. Bij de instellingen van deze gebruiker kan het E-mail account zichtbaar gemaakt worden.</div>'
		));
}


	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Account opslaan'
	));

?>
