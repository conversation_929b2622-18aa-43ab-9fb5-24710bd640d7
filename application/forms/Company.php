<script type="text/javascript" src="media/javascript/iban_to_bic.js"></script>

<script type="text/javascript">
	var typeChangeAllowed = <?= json_encode(intval($this->getOwner()->companyid) < 1) ?>;
	var uiFunctions = {};

	uiFunctions.companyTypes = <?= json_encode((array) CompanyType::getList()) ?>;

	uiFunctions.userTypeToggle = function(type) {
		// get more information about the company type that was selected
		companyType = {};
		for (var i = uiFunctions.companyTypes.length - 1; i >= 0; i--) {
			companyType = uiFunctions.companyTypes[i];
			if(companyType.id === type)
				break;
		};

		// list of subforms to show depending on certain conditions
		var formList = {
			'commercial_user': ['contractor_form', 'billing_form', 'invoice_form'],
			'technical_contact': ['invoice_form']
		};

		var hideElements = {
			'commercial_user': [
                'company_form-technical_complaints'
            ],
			'technical_contact': [
                'invoice_form-billing_interval',
                'invoice_form-billing_send',
                'invoice_form-tax_mandatory',
                'invoice_form-tax_mandatory',
                'invoice_form-tax_compensation',
                'invoice_form-collection_type',
                'invoice_form-invoice_specification',
                'invoice_form-mandate_id',
                'invoice_form-mandate_date',
                'invoice_form-sepa_status',
                'invoice_form-sepa_status',
                'invoice_form-minimum_indexation',
                'invoice_form-maximum_indexation',
                'invoice_form-provision',
                'invoice_form-invoice_grouped',
                'invoice_form-indexation_addition',
                'invoice_form-disable_invoice_reminds',
                'invoice_form-billing_period_start_uo',
                'invoice_form-billing_offset_days',
                'invoice_form-language',
                'invoice_form-additional_iban',
                'company_form-project',

            ],
			'general': [
                'invoice_form-billing_interval',
                'invoice_form-billing_send',
                'invoice_form-tax_mandatory',
                'invoice_form-tax_mandatory',
                'invoice_form-tax_compensation',
                'invoice_form-collection_type',
                'invoice_form-invoice_specification'
            ]
		};

		// the subforms that will be show, with defaults set
		var shownForms = [
		    'company_form',
            'personalpage_form' ,
            'employees_form',
            'addresses_form',
            'comment_form',
            'invoice_form',
            'reception_instructions_support_form'
        ];

        // if (settings && settings.financial_export_system !== 'afas') {
        //     hideElements['technical_contact'].push('company_form-olddebtorcode');
        //     hideElements['general'].push('company_form-olddebtorcode');
        // }

		// if this is an existing company, prevent changing the type
		if(typeChangeAllowed)
			shownForms.combine(['relation_form']);

		// add subforms to be shown, depending on the company type
		if(companyType.commercial_user === '1')
			shownForms.combine(formList.commercial_user);
		else
			shownForms.combine(['address_form']);

		if(companyType.technical_contact === '1')
			shownForms.combine(formList.technical_contact);

		if (companyType.hospitality === '1') {
		    // remove from default list
            shownForms.erase('invoice_form');
            shownForms.erase('personalpage_form');
            shownForms.combine(['hospitality_form']);
        }


		if( (companyType.commercial_user !== '1' && companyType.technical_contact !== '1') ) {
			hideElements['general'].each(function(hide_element){
				toggleDefListItem($(hide_element), false );
			});

		} else {
			hideElements['commercial_user'].each(function(hide_element){
				toggleDefListItem($(hide_element), companyType.commercial_user !== '1');
			});

			hideElements['technical_contact'].each(function(hide_element){
				toggleDefListItem($(hide_element), companyType.technical_contact !== '1');
			});
		}


		// show/hide the subforms
		$$('[name$=_form]').each(function(item) {
			if(shownForms.contains(item.name)) {
				item.show();
			} else {
				item.hide();
			}
		});
	};

	uiFunctions.invoiceOverruleToggle = function(show) {
		label = $('billing_form-invoice_overrule_text-label');
		field = $('billing_form-invoice_overrule_text');
		addr = $('billing_form-invoice_address');
		emp = $('billing_form-invoice_employee');
		if(show == '1') {
			label.show();
			field.show();
			addr.set('disabled', true);
		} else {
			label.hide();
			field.hide();
			addr.erase('disabled');
			emp.erase('disabled');
		}
	};


    function getPaymentTypeValue() {
        var el = $('invoice_form-collection_type')
        return el ? el.get('value') : '';
    }

    function showCollectionType() {
        $('invoice_form-collection_type_type-label').show();
        $('invoice_form-collection_type_type').show();
    }

    function hideCollectionType() {
        $('invoice_form-collection_type_type-label').hide();
        $('invoice_form-collection_type_type').hide();
    }

    function toggleCollectionType() {
        if (getPaymentTypeValue() === 'collection') {
            showCollectionType();
        } else {
            hideCollectionType();
        }
    }

	window.addEvent('domready', function () {

        toggleCollectionType();

        $('invoice_form-collection_type').addEvent('change', function () {
            toggleCollectionType();
        });

		// add a datepicker to all date input fields
		$$('input.datepicker').each(function (input) {
			new DatePicker(input, {
				yearStart: (new Date().getFullYear())-120,
				yearRange: 130,
			});
		});

		var min_el = $('invoice_form-minimum_indexation');
		var max_el = $('invoice_form-maximum_indexation');

		if(min_el && max_el){
			min_el.addEvent('change', function(){
				checkMinMax(min_el, max_el, 'min');
			});
			max_el.addEvent('change', function(){
				checkMinMax(min_el, max_el, 'max');
			});
		}

		var addition_el = $('invoice_form-indexation_addition');

		if(addition_el)
			addition_el.addEvent('change', function(){
				if(addition_el.get('value') > 100){
					alert('Het percentage mag niet groter dan 100% zijn.');
					addition_el.set('value', 100);
				}

				if(addition_el.get('value') < 0){
					alert('Het percentage mag niet kleiner dan 0% zijn.');
					addition_el.set('value', 0);
				}
			});

		new ibanToBic('.form.useredit', 'invoice_form-iban', 'invoice_form-bic');

		// allow the form to toggle between different subforms, but
		// only when changing the type of company is allowed (ie: when 
		// creating a new company)
		if(typeChangeAllowed)
			$$('#relation_form-relation_type').addEvent('change', function (evt) {
				uiFunctions.userTypeToggle(evt.target.value);
			});

		// when creating a new company, and the correct setting is set, make a default selection
		// for the users_invoice_preferences.bill field
		if(typeChangeAllowed && <?= json_encode(trim(Settings::get('new_company_uip_bill_default')) != false)?>) {
			$$('#invoice_form-billing_send').set('value', '<?= Settings::get('new_company_uip_bill_default')?>');
		}

		// make sure that the correct subform is shown intially
		uiFunctions.userTypeToggle($$('#relation_form-relation_type').getSelected()[0][0].value);

		$$('#billing_form-invoice_overrule').addEvent('change', function (evt) {
			uiFunctions.invoiceOverruleToggle(evt.target.value);
		}).fireEvent('change', {target: $$('#billing_form-invoice_overrule').getSelected()[0][0]} );

		// add a confirm to the delete button
		$('delete').addEvent('click', function(event) {
			if(!confirm('Weet u zeker dat u dit bedrijf (met alle gerelateerde gegevens) wil verwijderen?'))
				event.stop();
		});

		if($('invoice_form-additional_iban'))
			addAdditionalIbanBox();
	});

	function addAdditionalIbanBox(){
		var multi_select = $('invoice_form-additional_iban');
		var multi_select_save_values = $('invoice_form-additional_iban_save_values');

		var add_button = new Element('a', {
			'class': 'icon add',
			'html': ' &nbsp;'
		}).setStyles({'width': '16px', 'display': 'inline-block', 'margin': '0 5px'});

		var delete_button = new Element('a', {
			'class': 'icon delete',
			'html': ' &nbsp;'
		}).setStyles({'width': '16px', 'display': 'inline-block', 'margin': '0 5px'});

		add_button.inject(multi_select, 'after');
		delete_button.inject(add_button, 'after');


		multi_select.addEvent('change', function(){
			if(multi_select.getSelected().length > 0)
				delete_button.show();
			else
				delete_button.hide();

			var values = [];
			$each(multi_select.getElements('option'), function(option){
				values.extend([option.get('value')]);
			});

			multi_select_save_values.set('value', values.join(','));
		}).fireEvent('change');

		add_button.addEvent('click', function(){ addNewIban(); });

		delete_button.addEvent('click', function(){
			if(confirm('Weet u zeker dat u deze aanvullende IBAN nummers wilt verwijderen?'))
				multi_select.getSelected().destroy();

			multi_select.fireEvent('change');
		})		
	}

	function addNewIban(value){
		var multi_select = $('bank_account_form-additional_iban');
		
		var iban = prompt('Voer een nieuwe IBAN rekening in', value ? value : 'NL');

		if(iban == null) return;
		
		new Request.JSON({
			'url': 'user/iban-to-bic',
			'method': 'post',
			'data': {'iban': iban},
			'onSuccess': function(response){
				if(response != false){
					multi_select.adopt(new Element('option', {'text': iban, 'value': iban}));
					multi_select.fireEvent('change');
				} else {
					alert('Het ingevoerde IBAN nummer is niet correct en kan niet opgeslagen worden.');
					addNewIban(iban);
				}

			}
		}).send();		
	}	
</script>

<?

//general form options
$this->setAction('company/edit/' . ($this->getOwner()->companyid ? 'id/' . $this->getOwner()->companyid . '/' : '') . ($this->getOwner()->uoid ? '?uoid=' . $this->getOwner()->uoid : ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form useredit');

$c = Company::get($this->getOwner()->companyid);

$relation = new Form_SubForm($this);
$relation->setAttrib('title', 'Relatie');
$this->addSubForm($relation, 'relation_form');

    $isTenantForm = $this->getOwner()->getParam('type') === 'tenant';
    $forceToChooseCompanyType = empty($c) && !$isTenantForm;

    $companyTypeList = (new CompanyType())->getList(['order_by_commercial_user' => true]);
    $companyTypeSelectOptions = array_column($companyTypeList, 'name', 'id');

    if ($forceToChooseCompanyType) {
        $companyTypeSelectOptions = ['' => 'Maak een keuze'] + $companyTypeSelectOptions;
    }

    $relation->addElement('select', 'relation_type', [
        'label' => 'Soort bedrijf',
        'multiOptions' => $companyTypeSelectOptions,
        'required' => count($companyTypeList) > 0 && $forceToChooseCompanyType
    ]);

    if (!$forceToChooseCompanyType) {
        $relation->setAttrib('style', 'display: none;');
    }


$hospitality = new Form_SubForm($this);
$hospitality->setAttrib('title', 'Hospitality');
$this->addSubForm($hospitality, 'hospitality_form');


if ($c->hospitality_logo) {
    $destination = Main::app()->getDir('hospitality') . $this->getOwner()->companyid . '/logo/' . $c->hospitality_logo;

    $url = http_build_query([
        'file' => $destination,
        'resize' => 1,
        'h' => 165,
        'w' => 165
    ]);

    $hospitality->addElement('html', 'logo_preview', [
        'html' => '<div style="
        background-image: url(' . $this->getOwner()->view->baseDir . '/thumbnail?' . $url . ');
        height: 165px;
        width: 165px;
        background-repeat: no-repeat;
        background-position: center center;
     "></div>',
    ]);
}


$isAjax = $this->getOwner()->isAjaxRequest;

// See http://stackoverflow.com/a/42237893 or http://framework.zend.com/issues/browse/ZF-12159 for why this is needed.
// In short: Zend Framework has a bug where file form elements are validated when empty, when they are submitted via AJAX.
if (!$isAjax) {
    $hospitality->addElement('file', 'hospitality_logo', [
        'label' => 'Bedrijfslogo',
        'attribs' => [
            'title' => 'Om het logo juist te kunnen tonen moet de afbeelding voldoen aan een hoogte en breedte van 250 pixels.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ]
    ]);
}


$hospitality->addElement('text', 'hospitality_url', [
    'label' => 'Url bestelpagina',
    'attribs' => [
        'style' => 'width: 270px'
    ]
]);

$hospitality->addElement('text', 'hospitality_url_title', [
	'label' => 'Url weergave',
	'attribs' => [
		'style' => 'width: 270px',
		'title' => 'De url zoals deze weergegeven wordt voor de gebruiker, dus bijvoorbeeld "Omniboxx.nl" in plaats van "http://www.omniboxx.nl/"',
		'class' => 'forceAutoHint',
		'hintOffset' => "{'x': -0, 'y': 0}",
	]
]);

$company = new Form_SubForm($this);
$company->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($company, 'company_form');

	$company->addElement('text', 'name', array(
		'label' => 'Bedrijfsnaam',
	));

	$company->addElement('hidden', 'id', array(
		'label' => '',
	));

    if (Settings::get('modules_user_project')) {
        $project_list = Project::getMenuList();

        $project_options = array_combine(array_keys($project_list), array_column($project_list, 'name'));

        if(count($project_options) > 1)
            $project_options = [false => 'Kies een project'] + $project_options;

		$company->addElement('select', 'project', [
            'label' => 'Project',
            'validators' => [],
            'multiOptions' => $project_options,
            'attribs' => ['style' => 'min-width:220px;'],
            'required' => count($project_options) > 0
        ]);
    }

	$rtModel = new RelationType();
    $types = $rtModel->fetchAll($rtModel->select()->order('name'));
	$relationTypes = array();
	foreach ($types as $type)
		$relationTypes[$type['id']] = $type['name'];
    

	$company->addElement('select', 'company_relation_type', array(
		'label' => 'Relatie type',
		'multiOptions'	=> count($relationTypes)? ['' => 'Maak een keuze'] + $relationTypes : array('-1' => 'Geen relatie types ingesteld'),
	));

	$company->addElement('text', 'chain', array(
		'label' => 'Keten',
	));

	$company->addElement('text', 'kvk', array(
		'label' => 'Kvk nummer',
	));

	$company->addElement('text', 'tax', array(
		'label' => 'BTW nummer',
	));

	$company->addElement('text', 'email_address', array(
		'label' => 'Email adres',
		'validators' =>  array('EmailAddress'),
	));

    $company->addElement('text', 'email_address_copy', array(
        'label' => 'Kopie factuur email adres',
        'validators' =>  array('EmailAddress'),
    ));

	$company->addElement('text', 'phone_primary', array(
		'label' => 'Telefoon nummer',
		'validators' => array( 'PhoneNumber' ),
	));

	$company->addElement('text', 'phone_secondary', array(
		'label' => 'Alternatief telefoon nummer',
		'validators' => array( 'PhoneNumber' ),
	));

	$company->addElement('text', 'phone_emergency', array(
		'label' => 'Telefoon nummer bij nood',
		'validators' => array( 'PhoneNumber' ),
	));

	if( Settings::get('sms_enabled') )
		$company->addElement('select', 'sms', array(
			'label' => 'Informeren per sms?',
			'multiOptions'	=> array(
				'1' => 'Ja',
				'0' => 'Nee',
			),
		));

	$company->addElement('text', 'statutory', array(
		'label' => 'Vestigingsplaats',
	));


    $company->addElement('text', 'olddebtorcode', array(
        'label' => 'Debiteurcode financieel pakket',
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	255,
                )
            )
        )
    ));

    $company->addElement('checkbox', 'technical_complaints', array(
        'label' => 'Mag storingen afhandelen',
        'value' => false,
        'attribs' => array(
            'title' => 'Wanneer u deze technische partij wil gebruiken om storingen aan toe te wijzen, laat dan deze optie ingeschakeld',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -5, 'y': -3}"
        ),
    ));

	$company->addElement('hidden', 'dummy_user_id', array(
		'label' => '',
	));

$address = new Form_SubForm($this);
$address->setAttrib('title', 'Factuur adres');
$address->setAttrib('style', 'display: none;');
$this->addSubForm($address, 'address_form');

	$address->addElement('text', 'address_street', array(
		'label' => 'Straatnaam',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));

	$address->addElement('text', 'address_number', array(
		'label' => 'Huisnummer',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	20,
				)
			)
		)
	));

	$address->addElement('text', 'address_zipcode', array(
		'label' => 'Postcode',
		'filters' => array('StringTrim'),
		'validators' => array(
			'postcode' => array(
				'validator'	=> 'PostCode',
				'options'	=> array(
					'format'	=> '\d{4}[ ]?[a-zA-Z]{2}'
				)
			)
		)
	));

	$addressModel = new Address();
	if(isset($_POST))
		if(isset($_POST['address_form']['address_country']) && isset($address->address_zipcode))
            if(
                $_POST['address_form']['address_country'] !== '' &&
                ($locale = $addressModel->getCountryLocale($_POST['address_form']['address_country']))
            ) {
				if ($locale != 'NL') {
					try {
						$address->address_zipcode->getValidator('postcode')->setLocale($locale);
					} catch (Zend_Validate_Exception $e) {
						$address->address_zipcode->clearValidators();
					}
				}
			} else {
				$address->address_zipcode->clearValidators();
			}

	$address->addElement('text', 'address_city', array(
		'label' => 'Plaats',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	50,
				)
			)
		)
	));

	$address->addElement('select', 'address_country', array(
		'label' => 'Land',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	200,
				)
			)
		),
		'multiOptions' => Address::getCountriesForFormMultiselect(),
	));

	$address->addElement('text', 'address_department', array(
		'label' => 'Afdeling',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	200,
				)
			)
		)
	));

	$address->addElement('text', 'address_mention', array(
		'label' => 'Onder vermelding van',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	200,
				)
			)
		)
	));

$contractor = new Form_SubForm($this);
$contractor->setAttrib('title', 'Rechtsgeldig vertegenwoordigd');
$contractor->setAttrib('style', 'display: none;');
$this->addSubForm($contractor, 'contractor_form');

	$contractor->addElement('hidden', 'existing_user', array(
	));

	$contractor->addElement('text', 'existing_user_buildname', array(
		'label' => 'Bestaande gebruiker:',
		'autocomplete' => 'off',
		'attribs' => array(
			'title' => 'Zoek op achternaam of vul hieronder een nieuwe persoon in.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'company/find-contractants/',
			'autocompleter_options' => Zend_Json::encode(array(
				'hiddenElementId' => 'contractor_form-existing_user',

				'onSelected' => new Zend_Json_Expr(
					'function(selected){
						$each(selected, function(value, key){
							if(key == \'lastname\')
								key = \'name\';

							if($(\'contractor_form-contractor_\' + key))
								$(\'contractor_form-contractor_\' + key).set(\'value\', value);
						});
					}'
				)
			), false, array('enableJsonExprFinder' => true))
		)
	));

	// geslacht
	$contractor->addElement('select', 'contractor_gender', array(
		'label' => 'Geslacht',
		'multiOptions'	=> array(
			'female' => 'Vrouw',
			'male' => 'Man',
			'unknown' => 'Onbekend',
			'none' => 'Geen aanhef'
		),
		'registerInArrayValidator' => false,
	));


	// voorletters
	$contractor->addElement('text', 'contractor_initials', array(
		'label' => 'Voorletters',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100,
				)
			)
		)
	));


	// voornaam
	$contractor->addElement('text', 'contractor_firstname', array(
		'label' => 'Voornaam',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100
				)
			)
		)
	));


	// tussenvoegsel
	$contractor->addElement('text', 'contractor_middlename', array(
		'label' => 'Tussenvoegsel',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	20
				)
			)
		)
	));


	// achternaam
	$contractor->addElement('text', 'contractor_name', array(
		'label' => 'Achternaam',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	100
				)
			)
		)
	));

	if(Settings::get('software_type') != 'energy') {
		$contractorLanguageOptions = [
			'nl' => 'Nederlands',
			'en' => 'Engels',
		];

		if(Settings::get('software_french_enabled'))
			$contractorLanguageOptions['fr'] = 'Frans';

		$contractor->addElement('select', 'contractor_language', [
			'label'        => 'Taal',
			'multiOptions' => $contractorLanguageOptions,
		]);
	}

	$contractor->addElement('select', 'contractor_identication_type', [
		'label' => 'Identificatie soort',
		'multiOptions'	=> Users::$identificationTypeLabels,
	]);


	// BSN
	$contractor->addElement('text', 'contractor_BSN', array(
		'label' => 'Identificatie kenmerk',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	20
				)
			)
		)
	));

	$contractor->addElement('text', 'contractor_identication_valid_till', [
		'label' => 'Identificatie geldig tot',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);


	// geboortedatum
	$contractor->addElement('text', 'contractor_bdate', array(
		'label' => 'Geboortedatum',
		'attribs' => array(
			'class' => 'DatePicker',
			'datepicker_options' => json_encode(array(
				'yearRange' => date('Y') - 1900,
				'yearStart' =>  1900
			))
		),
		'validators' => array(
			array(
				'validator'	=> 'Date',
				'options'	=> array(
					'locale' => 'nl',
				)
			)
		)
	));

    // BSN
    $contractor->addElement('text', 'contractor_birthplace', array(
        'label' => 'Geboorteplaats',
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'min' =>	3
                )
            )
        )
    ));


	// email
	$contractor->addElement('text', 'contractor_email_address', array(
		'label' => 'Email',
		'validators' =>  array('EmailAddress'),
	));


	// telefoonnummer
	$contractor->addElement('text', 'contractor_phone_primary', array(
		'label' => 'Telefoon nummer',
		'validators' => array( 'PhoneNumber' ),
	));

	$contractor->addElement('hidden', 'contractor_id', array(
	));

$login = new Form_SubForm($this);
$login->setAttrib('title', 'Inloggegevens portal');
$this->addSubForm($login, 'personalpage_form');
	$login->addElement('text', 'username', array(
			'label' 		=> 	'Gebruikersnaam',
			'validators' 	=> 	[
				'uniqueValue' => [
					'validator'	=> 'uniqueValue',
					'options'	=> [
						'allowedId' => $c->dummy_user,
					]
				]
			],
			'attribs' 		=> array('autocomplete' => 'off')
		));	

		$login->addElement('text', 'password', array(
			'label' 		=> 	'Wachtwoord',
			'validators' 	=> 	array(),
			'attribs' 		=> array('autocomplete' => 'off'),
			'attribs' => [
				'title' => 'Dit veld is alleen voor invoer/vervanging van het wachtwoord, het huidige wachtwoord wordt nooit weergegeven.',
				'class' => 'forceAutoHint',
			],
		));	


$employees = new Form_SubForm($this);
$employees->setAttrib('title', 'Medewerkers');
$employees->setAttrib('style', 'display: none;');
$this->addSubForm($employees, 'employees_form');

	$employees->addElement('html', 'employees', array(
		'label' => '',
		'html' => Company::listEmployeesInHtmlTable($c),
	));

	if(!empty($c))
		$employees->addElement('html', 'employeesAdd', array(
			'label' => '',
			'html' => '<a href="user/edit-crm/usertype/employee/company_id/'.$c->id.'/" target="_blank">Voeg een nieuwe medewerker toe</a>',
		));


$addresses = new Form_SubForm($this);
$addresses->setAttrib('title', 'Adressen');
$addresses->setAttrib('style', 'display: none;');
$this->addSubForm($addresses, 'addresses_form');

	$addresses->addElement('html', 'addressesRows', array(
		'label' => '',
		'html' => Company::listAddressesInHtmlTable($c),
	));

	if(!empty($c))
		$addresses->addElement('html', 'addressesAdd', array(
			'label' => '',
			'html' => '<a href="address/edit/type/company/type_id/'.$c->id.'/" target="_blank">Voeg een nieuw adres toe</a>',
		));

$billing = new Form_SubForm($this);
$billing->setAttrib('title', 'Adressering facturatie');
$billing->setAttrib('style', 'display: none;');
$this->addSubForm($billing, 'billing_form');

	$billing->addElement('select', 'invoice_address', array(
		'label' => 'Adres',
		'multiOptions'	=> intval($c->dummyuser->id) > 0? User::getInvoiceAdresses($c->dummyuser->id): array('0' => 'Object adres'),
		'style' => 'width: 150px;',
	));

	$billing->addElement('select', 'invoice_employee', array(
		'label' => 'Ter attentie van',
		'multiOptions'	=> intval($c->id) > 0? Company::getInvoiceUserOptions($c): array('0' => 'Bedrijf'),
		'style' => 'width: 150px;',
	));

	$billing->addElement('select', 'invoice_overrule', array(
		'label' => 'Adressering overschrijven',
		'multiOptions'	=> array(
			'0' => 'Nee',
			'1' => 'Ja',
		),
		'attribs' => array(
			'title' => 'Door hier \'ja\' te kiezen kunt u een vrije adressering voor de facturatie ingeven',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -5, 'y': -3}"
		),
	));

	$billing->addElement('textarea', 'invoice_overrule_text', array(
		'attribs' => array(
			'rows' => 5,
			'cols' => 50,
		),
		'value' => '',
	));

$invoice = new Form_SubForm($this);
$invoice->setAttrib('title', 'Betalingsgegevens en facturatie voorkeuren');
$invoice->setAttrib('style', 'display: none;');
$this->addSubForm($invoice, 'invoice_form');
	
	if(Settings::get('general_company_shortname') == 'debazaar')
		$invoice->addElement(
			new Zend_Form_Element_Select('user_status',
				array(
					'label' => 'Klantstatus',
					'multioptions' => [
						'default' => 'Standaard',
						'historic' => 'Geen huurder meer',
						'collection_agency' => 'Incassobureau',
						'payment_arrangement' => 'Betalingsregeling',
					]
				)
			)
		);

	$invoice->addElement('select', 'billing_interval', array(
		'label' => 'Facturering per',
		'multiOptions'	=> UserInvoicePreferences::getInvoicePeriodOptions(),
		'attribs' => [
			'title' => 'Het interval dat u hier instelt bepaald met welke frequentie deze klant gefactureerd wordt.',
			'class' => 'forceAutoHint',
		],
	));

	if(Settings::get('software_type') != 'energy'){
		$invoice->addElement('select', 'billing_period_start_uo', array(
			'label' => 'Factuurperiode op ingangsdatum',
			'validators' => [],
			'multioptions' 	=> Settings::get('software_country') == 'be' ? ['1' => 'Ja', '0' => 'Nee'] : ['0' => 'Nee', '1' => 'Ja'],
			'attribs' => [
				'title' => 'De factuurperiode starten op de ingangsdatum van de verhuurperiode.',
				'class' => 'forceAutoHint',
			],		
		));	

		$invoice->addElement('text', 'billing_offset_days', array(
			'label' => 'Factuur periode afwijking',
			'validators' => [],
			'attribs' => [
				'placeholder' => '1',
				'title' => 'Optionele instelling om een afwijkende facuratie periode in te stellen. Voer het aantal dagen ten opzichte van het begin van de period in, de standaard waarde is 1.',
				'class' => 'forceAutoHint',
			],		
		));	
	}

	$invoice->addElement('select', 'billing_send', array(
		'label' => 'Factuur ontvangen per',
		'multiOptions'	=> array(
			'email' => 'Email',
			'mail' => 'Post',
			'none' => 'Geen factuur versturen',
		),
	));

        $invoice->addElement('text', 'mention', array(
            'label' => 'Onder vermelding van:',
            'attribs' => [
                'title' => 'Deze vermelding komt op de 2e regel in de aanhef van de prolongatie factuur.',
                'class' => 'forceAutoHint',
            ],
        ));


    $invoice->addElement('select', 'language', [
        'label' => 'Factuur taal',
        'multiOptions' => $contractorLanguageOptions,
    ]);

	$invoice->addElement('select', 'tax_mandatory', array(
		'label' => 'BTW-plichtig',
		'multiOptions'	=> array(
			'1' => 'Ja',
			'0' => 'Nee',
		),
	));

	$invoice->addElement('select', 'tax_compensation', array(
		'label' => 'BTW-compensatie',
		'multiOptions'	=> array(
			'0' => 'Nee',
			'1' => 'Ja',
		),
        'attribs' => array(
            'style' => 'width: 120px;',
            'title' => 'Als u deze aanzet, zal automatisch op het component waar HUUR in de omschrijving staat de btw niet berekend worden.',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -10, 'y': 0}"
        ),
    ));


	$invoice->addElement('text', 'iban', [
		'label' => 'IBAN',
		'filters' => ['alnum'],
		'validators' =>  ['iban'],
	]);

	$invoice->addElement('text', 'bic', array(
		'label' => 'BIC',
		'validators' => array(
			array(
				'validator'	=> 'stringLength',
				'options'	=> array(
					'max' =>	11,
				)
			)
		)
	));

	if($c->id > 0)
		$ibans = db()->fetchPairs(db()->select()->from('bank_account', array('test' => 'iban', 'iban'))->where('type = ?', 'company_secondary')->where('map_to = ?', $c->id));

	if(!$ibans)
		$ibans = array();

	$invoice->addElement('multiselect', 'additional_iban', array(
		'label' => 'Aanvullende IBAN nummers',
		'multiOptions' => $ibans,
		'attribs' => array(
			'style' => 'width: 120px;',
			'title' => 'Aanvullende IBAN nummers t.b.v. het automatisch matchen van betalingen, maak een selectie om te kunnen verwijderen.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -10, 'y': 0}"
		),		
	));

	$invoice->addElement('hidden', 'additional_iban_save_values', array('value' => implode(',', $ibans)));			


	$invoice->addElement('select', 'collection_type', array(
		'label' => 'Type betaling',
		'multiOptions'	=> array(
			'-1' =>	'Selecteer een type',
			'ideal' => 'Handmatig overmaken',
			'collection' => 'Automatische incasso',
		),
	));

    $invoice->addElement('select', 'collection_type_type', [
            'label' => 'Type automatische incasso',
            'multiOptions'	=> [
                '-1' =>	'Selecteer een type',
                'core' => 'normaal',
                'B2B' => 'B2B',
            ],
            'registerInArrayValidator' => false,
        ]);

	if(Settings::get('software_type') == 'energy')
		$invoice->addElement('text', 'billing_title', array(
			'label' => 'Aangepaste factuur titel',
			'attribs' => array(
				'title' => 'Bij lege invoer zal de standaard factuur titel gebruikt worden. Eventueel kan de periode in de gekozen titel toevoegd worden door {$periode} in dit veld te gebruiken.',
				'class' => 'forceAutoHint'
			),
		));


	$invoice->addElement('select', 'invoice_specification', array(
		'label' => 'Gespecificeerde rekening',
		'multiOptions'	=> array(
			'-1' =>	'Systeemvoorkeur',
			'0' => 'Nee',
			'1' => 'Ja'
		),
        'attribs' => array(
            'title' => 'Een gespecificeerde rekening laat alle componenten van de huuropbouw op de factuur zien. Kiest u voor nee dan komt een zin met "Huursom volgens contract op de factuur"',
            'class' => 'forceAutoHint'
        ),
	));

	$invoice->addElement('select', 'invoice_grouped', array(
		'label' 		=> 	'Verzamelfactuur',
		'multioptions' 	=> array('Nee', 'Ja'),
		'attribs' => array(
			'title' => 'Maak een verzamelfactuur voor meerdere objecten van deze ' . (Settings::get('software_type') == 'energy' ? 'bewoner ' : 'huurder') . ' binnen één factuurrun vallen.',
			'class' => 'forceAutoHint'
		),
		'validators' 	=> 	array(),
	));		

	$invoice->addElement('textarea', 'mandate_id', array(
		'label' => 'Machtigingskenmerk',
        'attribs' => array(
            'style' => 'width: 300px; height: 40px;',
            'placeholder' => (Settings::get('use_iban_as_mandate_reference') ? 'IBAN huurder ' : 'Omniboxx klantnummer') . ' ',
            'title' => 'Machtigingskenmerk voor incasso. De standaard waarde die hiervoor gebruikt wordt is het ' . (Settings::get('use_iban_as_mandate_reference') ? 'IBAN nummer van de huurder ' : 'Omniboxx klantnummer ') . ' ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': 10, 'y': 0}"
        ),
        'validators' => array(
            array(
                'validator'	=> 'stringLength',
                'options'	=> array(
                    'max' =>	100,
                )
            )
        )
	));

	$invoice->addElement('text', 'mandate_date', array(
		'label' 		=> 	'Machtigingsdatum',
		'placeholder' => '01-11-2009',
		'attribs' => array(
			'title' => 'Datum van ondertekening van de machtiging. Bij bestaande machtigingen is deze datum altijd 1 november 2009, de standaard datum voor SEPA incasso.',
			'class' => 'forceAutoHint datepicker',
			'hintOffset' => "{'x': 2, 'y': -3}"
		),
		'validators' => array(
			array(
				'validator'	=> 'Date',
				'options'	=> array(
					'locale' => 'nl',
				)
			)
		)
	));	
					
	$invoice->addElement('select', 'sepa_status', array(
		'label' => 'SEPA incasso status',
		'multiOptions' => array(
			'FRST' => 'Eerste incasso',
			'RCUR' => 'Wederkerende incasso',
			'FNAL' => 'Laatste incasso',
			'OOFF' => 'Eenmalige incasso'
		),
		'attribs' => array(
			'title' => 'Mogelijke sequentie opties ten behoeve van het genereren van een SEPA-incasso bestand.<br /><br /> Standaard staat deze waarde op "Eerste incasso" en bij het afronden van de eerste incasso wordt deze automatisch op "Wederkerende incasso" gezet.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)
	));

	if(Settings::get('software_type') != 'energy')
		$invoice->addElement('text', 'provision', array(
			'label' 		=> 	'Provisie (percentage aan klant doorberekend)',
			'validators' 	=> 	array(),
			'attribs'		=> array('title' => 'Percentage klantprovisie', 'class' => 'forceAutoHint')

		));		

	if(Settings::get('software_type') != 'energy') {
		$invoice->addElement('text', 'minimum_indexation', array(
			'label' => 'Minimale indexatie',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat minimaal geindexeerd wordt', 'class' => 'forceAutoHint')

		));

		$invoice->addElement('text', 'maximum_indexation', array(
			'label' => 'Maximale indexatie',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat maximaal geindexeerd wordt', 'class' => 'forceAutoHint')

		));

		$invoice->addElement('text', 'indexation_addition', array(
			'label' => 'Indexatie toeslag',
			'validators' => array(),
			'attribs' => array('title' => 'Percentage dat er extra bij het indexatie percentage gerekend wordt.', 'class' => 'forceAutoHint')
		));


        $invoice->addElement('checkbox', 'disable_invoice_reminds', array(
            'label' => 'Uitschakelen herinneringen en aanmaningen',
            'validators' => array(
                array(
                    'validator'	=> 'stringLength',
                    'options'	=> array(
                        'max' =>	255,
                    )
                )
            )
        ));

	}


$comment = new Form_SubForm($this);
$comment->setAttrib('title', 'Opmerkingen');
$this->addSubForm($comment, 'comment_form');
	$comment->addElement('textarea', 'comment', array(
		'attribs' => array(
			'cols'	=> 65,
			'rows'	=> 12,
		)
	));

if (Settings::get('module_reception_telephone_support_enabled')) {
    $receptionInstructionsSupportSubForm = new Form_SubForm($this);
    $receptionInstructionsSupportSubForm->setAttrib('title', 'Receptie telefonische ondersteuning');
    $this->addSubForm($receptionInstructionsSupportSubForm, 'reception_instructions_support_form');

    $receptionInstructionsSupportSubForm->addElement('textarea', 'how_to_pickup_the_phone', [
        'validators' => [
            [
                'textAreaMaxTextLine',
                false,
                [10]
            ]
        ],
        'label' => 'Hieronder kunt u instructies opgeven voor aanname van de receptie. Maximaal 10 regels.',
        'attribs' => [
            'rows' => 10,
            'cols' => 35,
        ],
    ]);

    $receptionInstructionsSupportSubForm->addElement('textarea', 'description_of_product_or_service', [
        'validators' => [
            [
                'textAreaMaxTextLine',
                false,
                [5]
            ]
        ],
        'label' => 'Hieronder kunt u algemene beschrijving opgeven over uw bedrijf en producten en diensten. Maximaal 5 regels.',
        'attribs' => [
            'rows' => 5,
            'cols' => 35,
        ],
    ]);
}



// delete button
$attribs = $this->getOwner()->deletionAllowed && !$this->getOwner()->isAjaxRequest ? array() : array('disabled' => 'disabled') ;
$this->addElement('submit', 'delete', array(
	'label' => 'Bedrijf verwijderen',
	'attribs' => array_merge(array(
		'formaction' => 'company/delete/id/'.$this->getOwner()->companyid.'/',
	), $attribs),
));

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Bedrijf opslaan'
));

?>
