<?php
$Form = new Controllers_CreditCheck_Form();
$userId = $this->getOwner()->getParam('user');


$this->setAction('credit-check/form-ajax')->setMethod('post');

$userNames = $userId ? $Form->getCustomerNames($userId) : null;

$tenant = [
    'label' => 'Huurders/Zoekende:',
    'value' => $userNames[$userId],
    'autocomplete' => 'off',
    'required' => true,
    'attribs' => [
        'size' => 42,
        'title' => 'Zoek op achternaam.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}",
        'placeholder' => '',
        'autocompleter' => 'credit-check/find-tenants/',
        'autocompleter_options' => Zend_Json::encode([
            'hiddenElementId' => 'user',
        ], false, [
            'enableJsonExprFinder' => true
        ])
    ],
];

$this->addElement('text', 'value', $tenant);

$user = [
    'id' => 'user',
    'value' => $userId
];

$this->addElement('hidden', 'user', $user);

$residents = [
    'disabled' => true,
    'label' => 'Bewoners',
    'id' => 'residents',
    'multioptions' => [],
    'value' => $userId,
];

if ($userId > 0) {
    unset($residents['disabled']);
    $residents['multioptions'] = $userNames;
}

$this->addElement('select', 'residents', $residents);

$address = [
    'disabled' => true,
    'label' => 'Uitvoeren op adres',
    'id' => 'address',
    'multioptions' => []
];

if ($userId > 0) {
    unset($address['disabled']);

    $findAddressesForUserService = new \Controllers\CreditCheck\FindAddressesForUserService();
    $addresses = $findAddressesForUserService->execute($userId);

    $address['multioptions'] = array_combine(
        array_column($addresses, 'id'),
        array_column($addresses, 'address')
    );
}

$this->addElement('select', 'address', $address);


$product = [
    'label' => 'Product',
    'id' => 'product',
    'multioptions' => $Form->getProductList()
];

$this->addElement('select', 'product', $product);

$submit = [
    'label' => 'Credit Check Uitvoeren',
    'id' => 'uitvoeren'
];

$this->addElement('submit', 'submit', $submit);
