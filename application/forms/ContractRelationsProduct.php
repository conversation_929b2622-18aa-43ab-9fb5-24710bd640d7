<?
$crModel = new ContractRelations();
$crpModel = new ContractRelationsProduct();

$contractRelationId = isset($this->getOwner()->contractRelationId) && intval($this->getOwner()->contractRelationId) > 0?
	$this->getOwner()->contractRelationId: null;

$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
$this->getOwner()->contractId: null;

if(!empty($contractRelationId))
	$contractRelation = $crModel->getById($contractRelationId);

if(!empty($contractRelation) && intval($contractRelation->fields) > 0)
	$contractProduct = $crpModel->getById($contractRelation->fields);

//general form options
$this->setAction('contract-relation/edit/role/product/'.(isset($contractRelationId)? 'id/'.$contractRelationId.'/': '') . (isset($contractId)? 'contract-id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('id', 'contactRelationProductForm')
		->setAttrib('class', 'form');


$contractRelationsProductForm = new Form_SubForm($this);
$this->addSubForm($contractRelationsProductForm, 'contract_relation_product_form');

	$contractRelationsProductForm->addElement('select', 'product', [
		'label' => 'Product',
		'required' 		=>	true,
		'multiOptions'	=> ContractProducts::getListForSelect(),
	]);

	$contractRelationsProductForm->addElement('text', 'from', [
		'label' => 'Van',
		'required' => true,
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$contractRelationsProductForm->addElement('text', 'till', [
		'label' => 'Tot en met',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$contractRelationsProductForm->addElement('text', 'amount', [
		'label' => 'Jaarlijks bedrag €',
		'validators' => [
			[
				'validator'	=> 'Float',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
	]);

	$contractRelationsProductForm->addElement('text', 'indexation_date', [
		'label' => 'Indexatie datum',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value) {
						return (isset($value) && strtotime($value) !== false)?
							date('d-m-Y', strtotime($value)): null;
					}
				]
			]
		],
	]);

	$contractRelationsProductForm->addElement('checkbox', 'notify', [
		'label' => 'Signalering',
	]);

	$contractRelationsProductForm->addElement('checkbox', 'active', [
		'label' => 'Actief',
		'value' => true,
	]);


$indexFormulasForm = new Form_SubForm($this);
$indexFormulasForm->setAttrib('title', 'Indexatie formule');
$this->addSubForm($indexFormulasForm, 'index_formulas_form');

	$indexFormulaPartsModel = new IndexesFormulasPartsTable();

	$partsList = (!empty($contractProduct) && intval($contractProduct->indexes_formula) > 0)?
		$indexFormulaPartsModel->getList(['formula' => $contractProduct->indexes_formula]):
		[];

	$parts = [];
	foreach ($partsList as $part) {
		$parts[] = [
			'percentage' 	=> $part['percentage'],
			'indexes_type' 	=> $part['indexes_type'],
		];
	}

	$indexFormulasForm->addElement('hidden', 'index_formula_anchor', [
		'disabled' => 'disabled',
		'attribs' => [
			'parts' => json_encode($parts),
		],
	]);

	$indexesModel = new IndexesTypesTable();

	$indexes = $indexesModel->getList([]);
	$indexTypes = [];
	foreach ($indexes as $index)
		$indexTypes[$index['id']] = $index['name'];

	$indexFormulasForm->addElement('select', 'index_types', [
		'disabled' => 'disabled',
		'attribs' => [
			'style' => 'display: none;',
		],
		'multiOptions'	=> $indexTypes,
	]);

//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>