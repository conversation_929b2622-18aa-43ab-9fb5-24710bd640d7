<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'finalNota')
			->setAttrib('class', 'finalnota form');

	$period = new Form_SubForm($this);
	$period->setAttrib('title', 'Periode');
	$this->addSubForm($period, 'period');

		$period->addElement('text', 'override_startdate', array(
			'label' => 'Start datum',
			'class' => 'DatePicker',
			'value' => date('d-m-Y')
		));

		$period->addElement('text', 'override_enddate', array(
			'label' => 'Eind datum',
			'class' => 'DatePicker',
			'value' => date('d-m-Y')
		));

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Opslaan'
	));

?>