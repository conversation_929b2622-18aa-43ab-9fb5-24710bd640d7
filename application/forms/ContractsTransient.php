<?
$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
	$this->getOwner()->contractId: null;

$contractsModel = new Contracts();
$contract = $contractId? $contractsModel->getById($contractId): null;

$this->getOwner()->view->contractType = 'ContractsTransient';

//general form options
$this->setAction('contract/edit/'.(isset($contractId)? 'id/'.$contractId.'/': ''))
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');

$usersModel = new Users();



$contractForm = new Form_SubForm($this);
$contractForm->setAttrib('title', 'Algemeen');
$this->addSubForm($contractForm, 'contract_form');

	$contractForm->addElement('hidden', 'tenant', array(
	));

	$contractForm->addElement('text', 'tenant_buildname', array(
		'label' => 'Relatie:',
		'autocomplete' => 'off',
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Zoek op achter- of bedrijfsnaam.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}",
			'placeholder' => '',
			'autocompleter' => 'contract/find-contractants/',
			'autocompleter_options' => Zend_Json::encode(array(
				'hiddenElementId' => 'contract_form-tenant',
			), false, array('enableJsonExprFinder' => true))
		),
		'validators' => [
			'ComboboxRequired' => [
				'validator'	=> 'ComboboxRequired',
				'options'	=> [
					'field' => $contractForm->getElement('tenant')
				]
			],
		],
	));

	$cId = $contract->contact->map_to;
	$contractForm->addElement('select', 'contact', [
		'label' => 'Contactpersoon',
		'registerInArrayValidator' => false,
		'multiOptions'	=> [
			'-1' => 'Geen persoon gekozen',
			$cId => 'Huidig gekozen persoon',
		],
	]);

if(!Settings::get('modules_contracts_use_custom_identifier')) {
	$contractForm->addElement('text', 'system_identifier', [
		'label' => 'Systeem contractnummer',
		'readonly' => true,
	]);
} else {
	$contractForm->addElement('text', 'identifier', [
		'label' => 'Contractnummer',
	]);
}

if(Settings::get('modules_contracts_detailinfo')) {
	$contractForm->addElement('text', 'description', [
		'label' => 'Omschrijving',
	]);
}

	foreach (Contracts::$states as $state => $settings)
		$stateLabels[$state] = $settings['label'];

	switch ($contract->state) {
		case 'concept':
			break;

		case 'signed':
			unset($stateLabels['concept']);
			break;

		case 'ended':
			unset($stateLabels['concept']);
			unset($stateLabels['signed']);
			unset($stateLabels['canceled']);
			break;

		case 'canceled':
			unset($stateLabels['concept']);
			unset($stateLabels['signed']);
			unset($stateLabels['ended']);
			break;
	}

	$contractForm->addElement('select', 'general_state', [
		'label' => 'Status',
		'multiOptions'	=> (array) $stateLabels,
	]);

	$contractForm->addElement('text', 'general_cancel_reason', [
		'label' => 'Reden van annuleren',
	]);

	$contractForm->addElement('text', 'general_signed', [
		'label' => 'Getekend op',
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		]
	]);

	$intUsers = $usersModel->getInternalUsers();

	foreach ($intUsers as $user)
		if(empty($user['enddate']))
			$internalUsers[$user['id']] = User::buildname($user);

	$contractForm->addElement('select', 'account_manager', [
		'label' => 'Accountmanager',
		'multiOptions'	=> (array) $internalUsers,
		'value' => (intval(loginManager::data()->id) > 0)? loginManager::data()->id: null,
	]);

	$contractForm->addElement('text', 'contract_labels', [
		'label' 		=> 'Labels',
		'required' 		=> (in_array(Settings::get('general_company_shortname'), ['debazaar', ])),
	]);



$datesForm = new Form_SubForm($this);
$datesForm->setAttrib('title', 'Datums');
$this->addSubForm($datesForm, 'dates_form');

	$datesForm->addElement('text', 'from', [
		'label' => 'Begindatum',
		'value' => date('d-m-Y'),
		'attribs' => [
			'class' => 'DatePicker',
		],
		'validators' => [
			[
				'validator'	=> 'Date',
				'options'	=> [
					'locale' => 'nl',
				]
			]
		],
		'filters' => [
			[
				'filter' => 'Callback',
				'options' => [
					'callback' => function($value, $isDirectionUi) {
						if(isset($isDirectionUi) && $isDirectionUi === true)
							return (isset($value) && strtotime($value) !== false)?
								date('d-m-Y', strtotime($value)): null;
					},
					'options' => [
						'isDirectionUi' => !$this->getOwner()->getRequest()->isPost(),
					]
				]
			]
		],
	]);

	$datesForm->addElement('text', 'period_formula', [
		'label' => 'Verlengformule',
	]);

	$datesForm->addElement('hidden', 'period_formula_explain', [
	]);

	$datesForm->addElement('text', 'notice_period', [
		'label' => 'Opzegtermijn (mnd)',
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>