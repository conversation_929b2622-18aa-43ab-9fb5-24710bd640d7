<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'corporation')
			->setAttrib('enctype', 'multipart/form-data')
			->setAttrib('class', 'corporation form');



	//name
	$this->addElement('text', 'name', array(
		'label' => 'Bedrijfsnaam',
		'required' => true
	));

	//number
	$this->addElement('text', 'number', array(
		'label' => 'Nummer (voor facturen- en objectnummers)',
		'required' => Settings::get('software_type') == 'energy',
		'validators' => array(
			array(
				'validator' => 'Digits'
			),
			array(
				'validator' => 'stringLength',
				'options'   => Settings::get('general_company_number_length') ? array(Settings::get('general_company_number_length'), Settings::get('general_company_number_length')) : array(0, 2)
			)
		)		
	));

	// kvk number
	$this->addElement('text', 'kvk', array(
		'label' => Settings::get('software_country') == 'be' ? 'H.R. nummer' : 'K.V.K. nummer'
	));

	$this->addElement('text', 'kvk_subcode', array(
		'label' => Settings::get('software_country') == 'be' ? 'H.R. vestigingsnummer' : 'K.V.K. vestigingsnummer'
	));

	$this->addElement('checkbox', 'for_third_party', [
		'label'   => 'Derden rekening entiteit',
		'value'   => false,
		'attribs' => [
			'title' => 'Markeer deze entiteit met "(derden rekening)" op plekken waar onderscheid '
				.'gemaakt moet worden tussen interne en beheer entiteiten.',
			'class' => 'forceAutoHint',
		],
	]);

// tax number
	$this->addElement('text', 'tax', array(
		'label' => 'B.T.W. Nummer (zonder landcode)'
	));


	$this->addElement('checkbox', 'financial_export_enabled', [
		'label'   => 'Ingeschakeld',
		'value'   => false,
		'attribs' => [
			'title' => 'Vink dit aan om, voor deze entiteit het uitwisselen van data aan te zetten met '
				.FinancialExportSystems::getLabelForActiveSystem(),
			'class' => 'forceAutoHint',
		],
	]);

    $this->addElement('checkbox', 'financial_export_invoices', [
        'label'   => 'Facturen exporteren',
        'value'   => false,
        'attribs' => [
            'title' => 'Vink dit aan om de facturen die onder deze entiteit vallen te exporteren naar '
                .FinancialExportSystems::getLabelForActiveSystem(),
            'class' => 'forceAutoHint',
        ],
    ]);

    $this->addElement('checkbox', 'financial_import_third_party_purchase_invoices', [
        'label'   => 'Inkoopfacturen importeren',
        'value'   => false,
        'attribs' => [
            'title' => 'Vink dit aan om de inkoopfacturen die onder deze entiteit vallen te importen vanuit '
                .FinancialExportSystems::getLabelForActiveSystem(),
            'class' => 'forceAutoHint',
        ],
    ]);

    $this->addElement('checkbox', 'financial_import_transactions', [
        'label'   => 'Transacties importeren',
        'value'   => false,
        'attribs' => [
            'title' => 'Vink dit aan om de transacties die onder deze entiteit vallen te importen vanuit '
                .FinancialExportSystems::getLabelForActiveSystem(),
            'class' => 'forceAutoHint',
        ],
    ]);

    $this->addElement('select', 'financial_link_type', array(
        'label' 		=> 	'Financiele systeem',
        'validators' 	=> 	array(),
        'multiOptions' 	=> 	[
            'None' => 'Standaard financiele systeem ('. FinancialExportSystems::getLabelForActiveSystem() .')',
            'Exact Online',
        ]
    ));


$financialLinkMapToRowArr = ExactOnline_OauthManagerController::getTokensByType(ExactOnline_OauthManagerController::TOKEN_KEY_NAME);
$financialLinkMapToArr = [];
    foreach ($financialLinkMapToRowArr as $value) {
        $financialLinkMapToArr[$value['key']] = $value['name'];
    }

    $this->addElement('select', 'financial_link_map_to', array(
        'label' 		=> 	'Koppeling',
        'validators' 	=> 	array(),
        'multiOptions' 	=> 	$financialLinkMapToArr
    ));




$this->addElement('text', 'administration', array(
		'label' => 'Administratie',
		'attribs' => array(
			'title' => 'Nummer van de financiele administratie',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)
	));

	$this->addElement('text', 'financial_export_administration_access_key', [
		'label' => 'Administratie toegangscode',
		'attribs' => [
			'title' => 'Voer hier een toegangscode in die toegang heeft tot deze administratie.
						Indien afwezig wordt de toegangscode uit de systeembrede instelling gebruikt
						(huidige waarde: "'.Settings::get( 'financial_export_yuki_accesskey' ).'"")',
			'class' => 'forceAutoHint',
		]
	]);

	$this->addElement('text', 'financial_export_sales_journalcode', array(
			'label' => 'Verkoopboek code',
		));

	$this->addElement('text', 'financial_export_sales_debtcode', array(
			'label' => 'Grootboek debiteuren',
		));
	$this->addElement('text', 'financial_export_sales_vathigh', array(
			'label' => 'Grootboek BTW hoog',
		));
	$this->addElement('text', 'financial_export_sales_vathighcode', array(
			'label' => 'Code BTW hoog',
		));
    $this->addElement('text', 'financial_export_sales_vat_seven_percent', array(
        'label' => 'Grootboek BTW 7% (Duitsland)',
    ));
    $this->addElement('text', 'financial_export_sales_vat_seven_percent_code', array(
        'label' => 'Code BTW 7% (Duitsland)',
    ));
	$this->addElement('text', 'financial_export_sales_vatlow', [
			'label' => 'Grootboek BTW laag 6%',
    ]);
	$this->addElement('text', 'financial_export_sales_vatlowcode', [
			'label' => 'Code BTW laag 6%',
    ]);

    $this->addElement('text', 'financial_export_sales_vatlow_new', [
        'label' => 'Grootboek BTW laag 9%',
    ]);
    $this->addElement('text', 'financial_export_sales_vatlowcode_new', [
        'label' => 'Code BTW laag 9%',
    ]);
	$this->addElement('text', 'financial_export_sales_vatnone', array(
			'label' => 'Grootboek BTW 0%',
		));
	$this->addElement('text', 'financial_export_sales_vatnoncode', array(
			'label' => 'Code BTW nul 0%',
		));



		$this->addElement('text', 'general_name', array(
			'label' => 'Naam',
		));
		
		$this->addElement('text', 'general_phone', array(
			'label' => 'Telefoonnummer',
		));
		
		$this->addElement('text', 'general_fax', array(
			'label' => 'Fax',
		));
		
		$email_accounts_select = function(){
			return db()->select()->from('email_accounts', array('id',  'CONCAT_WS(" - ", name, address)'))->order('name');
		};
		
		$email_accounts =  db()->fetchPairs($email_accounts_select());
		$email_accounts[0] = 'Standaard (gelijk aan algemene instelling)';
		ksort($email_accounts);


		$this->addElement('select', 'general_mailaccount', array(
			'label' => 'E-mail account voor verzending',
			'multioptions' 	=> $email_accounts,
		));

        $this->addElement('text', 'purchase_invoice_mailaccount', [
            'label' => 'Digitale mailbox voor inkoopfacturen',
            'title' => 'Het mailadres waarnaar leveranciers de factuur kunnen mailen. Standaard in te stellen bij klachten en storingen maar indien per entiteit een apart mail adres dan hier in te vullen',
            'validators' =>  array('EmailAddress'),
            'class' => 'forceAutoHint',

        ] );

		$this->addElement('text', 'general_email', array(
			'label' => 'Getoond mail adres op templates',
		));

		$this->addElement('text', 'corporation_email_closure', array(
			'label' => 'Getoonde afsluiting standaard mails',
		));
		
		$this->addElement('text', 'general_website', array(
			'label' => 'Alg. Website',
		));
		
		$this->addElement('text', 'portal_website', array(
			'label' => 'Portal Website',
		));
		
		$this->addElement('text', 'general_address_street', array(
			'label' => 'Straat',
		));
		
		$this->addElement('text', 'general_address_number', array(
			'label' => 'Nummer',
		));
		
		$this->addElement('text', 'general_address_zipcode', array(
			'label' => 'Postcode',
		));
		
		$this->addElement('text', 'general_address_city', array(
			'label' => 'Stad',
		));

		// change? also in form corporations / upload.phtml and transactioncontroller
            $banks = array( 'abn' => 'ABN-AMRO',
                            'triodos' => 'Triodos Bank',
                            'rabobank' => 'Rabobank',
                            'deutsche' => 'Deutsche Bank' ,
                            'ing' => 'ING bank',
                            'insingergilissen' => 'Insinger Gilissen',
                            'sns' => 'SNS Bank' ,
                            'lanschot' => 'Van Lanschot',
                            'handel' => 'Handelsbank',
                            'safra' => 'Banque J. Safra Sarasin',
                            'knab' => 'Knab',
                            'kbc'=> 'KBC',
                            'MCB'=> 'MCB Bank',
                            'VOWA' => 'Volkswagen Bank',
                            'other' => 'Overig');

		if (Settings::get('software_country') == 'be'){
			$banks += array('bnp'=> 'BNP Paribas');
		}


		//name
	$this->addElement('text', 'invoice_post_amount', array(
		'label' => 'Posttoeslag voor factuur per post',
		'required' => false
	));

	$this->addElement('text', 'percentage_costs_first_formalnotice', array(
		'label' => 'Percentage WIK kosten aanmaning (0-100)',
		'required' => false
	));

	$this->addElement('hidden', 'uploadedletterhead', array(
			'label' => 'Huidig briefpapier',
		));

	$this->addElement('text', 'upload_letterhead', array(
			'label' => 'Logo briefpapier instellen',
			'attribs' => array(
				'class' => 'imageUpload',
				'image-upload-type' => 'stationary_logo'
			)
		));

	$this->addElement('textarea', 'email_signature', array(
			'label' => 'Handtekening',
			'rows' => 5,
			'cols' => 50,
			'attribs' => array(
				'placeholder' => 'Enkel invoeren indien afwijkend van algemene instelling',
				'title' => 'In deze tekst kunnen de volgende variabelen worden toegepast:<br /><br /><b>[user_name]</b>: Naam huidige gebruiker<br /><b>[company_name]</b>: Naam van het bedrijf<br /><b>[address_street]</b>: Straat<br /><b>[address_number]</b>: Huisnummer<br /><b>[address_zipcode]</b>: Postcode<br /><b>[address_city]</b>: Stad<br /><b>[phone]</b>: Telefoon<br /><b>[email]</b>: E-mail<br /><b>[phone_user]</b>: Telefoon direct<br /><b>[aanwezigheid]</b>: Aanwezigheid<br /><b>[website]</b>: Website<br /><b>[logo]</b>: Logo <small>Afhankelijk van instelling: <i>Positie logo</i></small>',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)
		));

	$this->addElement('text', 'upload_personalpagebackground', array(
		'label' => 'Achtergrond persoonlijke pagina uploaden',
		'attribs' => array(
			'class' => 'imageUpload',
			'image-upload-type' => 'portal_background'
		)
	));

    $currencies_select = function(){
        return db()->select()->from('currencies', array('id',  'currency_description'))->order('order ASC');
    };
    $currencies =  db()->fetchPairs($currencies_select());

    $this->addElement('select', 'currency', array(
        'label' => 'Valuta',
        'multioptions' 	=> $currencies,
    ));

	$this->addElement('text', 'upload_logopersonalpage', array(
		'label' => 'logo persoonlijke pagina upoaden',
		'attribs' => array(
			'class' => 'imageUpload',
			'image-upload-type' => 'portal_logo'
		)
	));

	$pp_options = [
		'profile' => 'Wijzigen persoonlijke gegevens',
		'invoices' => 'Facturen inzien',
		'documents' => 'Documenten inzien',
		'email-list' => 'Emails inzien',
		'info/tarieven' => 'Tarieven inzien',
		'migrate' => 'Verhuizing doorgeven',
		'consumption' => 'Meterstanden inzien'
	];

	if(Settings::get('software_type') != 'energy')
		unset($pp_options['migrate'],$pp_options['consumption'],$pp_options['info/tarieven']);

	$this->addElement('select', 'personal_page_settings', array(
			'label' 		=> 	'Opties persoonlijke pagina',
			'multioptions' 	=> 	 $pp_options,
			'validators' 	=> 	array(),
			'multiple'			=>	true,
			'registerInArrayValidator' => false

		));

	// Banktype
	$this->addElement('select', 'banktype', array(
		'label' 		=> 	'Bank',
		'validators' 	=> 	array(),
		'multiOptions' 	=> 	$banks
	));	

	// bank account name (overrides general company name on invoices near bank account)
	$this->addElement('text', 'bankaccountname', [
		'label' => 'Tenaamstelling',
		'attribs' => [
			'title' => 'Als hier een naam ingevuld is, overschrijft deze de algemene bedrijfsnaam op facturen en 
				herinneringen wanneer het de tenaamstelling van de rekening betreft.<br><br>
				Tevens vervalt met deze optie de vermelding van het BTW-nummer en het K.V.K. nummer op de facturen.',
			'class' => 'forceAutoHint',
		]
	]);

	// tax number
	$this->addElement('text', 'bank', array(
		'label' => 'Bankrekening'
	));		

	$this->addElement('checkbox', 'bank_grouped_collection', array(
		'label' => 'Gegroepeerde incasso',
		'attribs' => array(
			'title' => 'Bij de terugkoppeling van de incasso kunnen de opdrachten gegroepeerd worden weergegeven door de bank. Als deze optie ingeschakeld wordt dan worden facturen met automatische incasso direct op betaald gezet.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)		
	));	
		
	$this->addElement('text', 'iban', array(
		'label' => 'IBAN',
		'validators' 	=>  array('iban'),
		'required' => true
	));	
	
	$this->addElement('text', 'bic', array(
		'label' => 'BIC'
	));

	$this->addElement('text', 'creditor_id', array(
		'label' => 'Incassant ID',
		'attribs' => [
			'style' => 'width: 170px;',
			'title' => 'Op uw incasso contract vindt u uw Incassant-ID, de bank bepaald dit aan de hand van uw KvK nummer.<br /><br />Als er bij deze juridische entiteit een KvK nummer ingevoerd is zullen wij na het opslaan het Incassant-ID automatisch berekenen, echter dient dit door u gecontroleerd te worden met uw incasso contract.<br /><br />Wanneer het Incassant-ID niet correct is ingevoerd zal uw incasso batch hierop afgekeurd worden.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		]
	));
	
	// settings for optional iDeal override

	// can payment to this corporation be done via iDeal
	$this->addElement('checkbox', 'ideal_enabled', array(
		'label' => 'Ideal gebruiken',
		'attribs' => array(
			'title' => 'Klanten die onder deze entiteit vallen kunnen alleen via 
						iDeal afrekenen als dit vinkje ingeschakeld is.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)		
	));

	// the type of bank
	$this->addElement('text', 'mollie_key', array(
		'label' => 'Mollie Key',

	));



    $this->addElement('checkbox', 'period_rounded_to_fifty_percent', [
        'label' => 'Deelperiode 50%/100%',
        'attribs' => [
            'title' =>
                'Bij prolongatie vanuit deze entiteit moeten de facturen voor een deelperiode afgerond ' .
                'worden op 50% van de periode of 100% van de periode, in plaats van dat het per dag berekend wordt.' .
                '<br /><br /><b>Vooralsnog Omniboxx_Admin-only instelling.</b>',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ]
    ]);
		
	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Bedrijf opslaan'
	));

?>
