<?

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general_form');

$general->addElement('text', 'name_nl', [
    'label' => 'Naam (Nederlandstalig)',
]);

$general->addElement('text', 'name_en', [
    'label' => 'Naam (Engelstalig)',
]);

if (Settings::get('software_french_enabled')) {
    $general->addElement('text', 'name_fr', [
        'label' => 'Naam (Franstalig)',
    ]);
}

$general->addElement('checkbox', 'is_default', [
    'label' => 'Standaard ruimte',
]);

$types = db()->fetchPairs(db()->select()->from(
    ['it' => 'inspection_types'],
    ['id', 'description_nl']
)->order('description_nl')->where('archived = ?', 0));

$general->addElement('multiselect', 'inspection_type', [
    'label' => 'Inspectie type beperken',
    'multioptions' => $types,
    'attribs' => [
        'title' => 'Ruimte enkel beschikbaar maken voor een bepaald types inspectie. Deselecteer alle opties om de ruimte beschikbaar te maken voor alle type inspecties',
        'class' => 'forceAutoHint'
    ],
]);

$general->addElement('select', 'objectgroup_type', [
    'label' => 'Objectgroep type',
    'multioptions' => ['all' => 'Alle typen', 'private' => 'Particulier', 'commercial' => 'Commercieel'],
    'attribs' => [
        'title' => 'Ruimte voor inspecties enkel beschikbaar maken voor een bepaald type objectgroepen.',
        'class' => 'forceAutoHint'
    ],
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Ruimte opslaan'
]);
