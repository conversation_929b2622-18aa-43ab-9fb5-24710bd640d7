<?php /** @noinspection PhpUnhandledExceptionInspection */
$usersModel = new Users();
$internal_users = $usersModel->getInternalUsersShort();
$objectgroups = (array) db()->fetchPairs( db()->select()->from('objectgroup', array('id', 'description')) );

	$white_list = array(
		'company' => array(
			'general_company',
			'general_phone',
			'general_fax',
			'general_email',
			'general_website',
			'general_website_en',
			'general_address_street',
			'general_address_number',
			'general_address_zipcode',
			'general_address_city',

		),

		'collection' => [
			'financial_export_group_collection_payment'
		],


		'financial' => [
			'financial_export_system',
			'financial_import_transactions',
			'financial_export_invoices',
            'financial_export_sales_journalcode',
            'financial_export_sales_debtcode',
            'financial_export_sales_journalpurchasecode',
            'financial_export_sales_credcode',
            'financial_export_sales_vathigh',
            'financial_export_sales_vathighcode',
            'financial_export_sales_vatlow',
            'financial_export_sales_vatlowcode',
            'financial_export_sales_vatlow_new',
            'financial_export_sales_vatlowcode_new',
            'financial_export_purchase_vathigh',
            'financial_export_purchase_vathighcode',
            'financial_export_purchase_vatlow',
            'financial_export_purchase_vatlowcode',
            'financial_export_purchase_vatlow_new',
            'financial_export_purchase_vatlowcode_new',
            'financial_export_purchase_vatnon',
            'financial_export_purchase_vatnoncode',
            'financial_export_cost_carrier_input',


			'financial_export_fromdate',
            'financial_export_twinfield_exclude_from_payment',
			'financial_export_row_description',


			'financial_export_system_multivers_client_id',
			'financial_export_system_multivers_client_secret',

			'financial_export_payment_condition',
			'financial_export_twinfield_username',
			'financial_export_twinfield_password',
			'financial_export_twinfield_company',

			'financial_penalty_prioritize_penalty_payment',
			'financial_export_yuki_accesskey',
			'financial_import_third_party_purchase_invoices',
			'import_third_party_purchase_invoices_service_cost_filter'
        ],

		'invoice' => [
			'clieop_month_day',
            'invoice_enable_covid_discount',
		],

		'post' => array(
			'general_post_address_street','general_post_address_number','general_post_address_zipcode','general_post_address_city'
		),

		'investor_provision' => [
			'investor_provision_period_date',
            'investor_provision_disable_pdf_merge',
		],

		'internal_alerts' => [
			'alert_setting_changes',
			'alert_changed_IBAN_JE_investor',
		],

		'contracts' => [
			'modules_contracts_default_notice_period'
		],

		'complaint' => [
			'complaint_add_employee_info_to_mail',
		],

		'ca' => [
			'reminder_no_extra_post_copy'
		],

		'sms' => [
            'sms_enabled',
			'sms_invoice_reminder',
            'sms_access_key',
            'sms_originator'
		],

		'remind_private_penalty' => [
			'remind_private_penalty_method',
			'remind_private_penalty_amount_level_1',
			'remind_private_penalty_amount_level_2',
			'remind_private_penalty_amount_level_3',
		],

		'remind_commercial_penalty' => [
			'remind_commercial_penalty_method',
			'remind_commercial_penalty_increase',
			'remind_commercial_penalty_amount',
			'remind_commercial_penalty_interest_rate',
			'remind_commercial_penalty_interest_rate'
		]

		/*
		'tenantLogin' => array(
			'modules_tenantLogin_firstnamebasis',
			'modules_tenantLogin_allow_tenant_status_updates',
			'modules_tenantLogin_show_complaint_status_list',
		)
		*/
	);

	if(Settings::get('software_type') == 'energy')
		unset($white_list['company'][array_search('general_website_en', $white_list['company'])]);
	
	if(Settings::get('modules_tenantLogin_roomselector'))
		$white_list['roomselector'] = array('modules_tenantLogin_roomselector_registrant_start_date', 'modules_tenantLogin_roomselector_obligated_documents');

	if(Settings::get('modules_app_alert'))
		$white_list['app'] = ['modules_app_alert_register_enabled', 'modules_app_alert_intro_text_nl', 'modules_app_alert_intro_text_en'];

	if(Settings::get('modules_rental'))
		$white_list['rental'] = ['modules_rental_pararius_id', 'modules_rental_pararius_url'];

	if(Settings::get('financial_export_system') == 'afas')
		$white_list['financial'][] = 'financial_export_system_closed_date';

	if(Settings::get('software_country') == 'be'){
		$white_list['deposit_korfine'] = ['modules_deposit_korfine_enabled', 'modules_deposit_korfine_username', 'modules_deposit_korfine_password', 'modules_deposit_korfine_investor_email_override'];

		$white_list['invoice'] = ['invoice_sale_conditions_nl', 'invoice_sale_conditions_en', 'invoice_sale_conditions_fr'];
	}

	$objectgroups = (array) db()->fetchPairs( db()->select()->from('objectgroup', array('id', 'description')) );
if (Settings::get('general_company_shortname') === 'trotz') {
    $white_list['investor_provision'][] = 'investor_provision_multi_object_multi_investor_support';
}



	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'settings')
			->setAttrib('class', 'settings form');

	if(loginManager::data()->rights != 'admin')		
		$this->setWhitelist($white_list);


    $betaTesting = new Form_SubForm($this);
    $betaTesting->setAttrib('title', 'Beta Testing');
    $this->addSubForm($betaTesting, 'betaTesting');

    $betaTesting->addElement('checkbox', 'testing_new_indexes_update', [
        'label' => 'Batch updaten indexatietypen',
    ]);


	$survey = new Form_SubForm($this);
	$survey->setAttrib('title', 'Enquêtes');
	$this->addSubForm($survey, 'surveys');

	$survey->addElement('checkbox', 'modules_surveys_enabled', [
		'label' => 'Enquête smodule',
	]);

	$service = new Form_SubForm($this);
	$service->setAttrib('title', 'Servicekosten');
	$this->addSubForm($service, 'service');

		$service->addElement('checkbox', 'modules_service_charges_enabled', [
			'label' => 'Service kosten module',
		]);

		

	$tiara = new Form_SubForm($this);
	$tiara->setAttrib('title', 'Tiara');
	$this->addSubForm($tiara, 'tiara');

	$tiara->addElement('text', 'tiara_nvm_vestiging_nr', [
		'label' => 'NVM VestigingsNr'
	]);

	$software = new Form_SubForm($this);
	$software->setAttrib('title', 'Software (admin instellingen)');
	$this->addSubForm($software, 'software');


		$software->addElement('select', 'software_type', array(
			'label' => 'Software type',
			'multiOptions' => array(
				'real estate' => 'OmniBoxx',
				'support' => 'Support'
			)
		));

		$software->addElement('checkbox', 'software_only_rental', array(
			'label' => 'Klant gebruikt Omniboxx alleen voor verhuurmodule'
		));

		$software->addElement('checkbox', 'software_beta_options', array(
			'label' => 'Beta functionaliteiten'
		));

		$software->addElement('number', 'software_number_tenants', [
			'label' => 'Aantal bewoners op huurdossies',
			'attribs' => array(
				'title' => 'Default een 2e bewoner mogelijk. Maximaal 4 invullen dan worden een 3e en 4e bewoner mogelijk',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		]);

		$software->addElement('select', 'software_country', array(
			'label' => 'Land',
			'multiOptions' => array(
				'nl' => 'Nederland',
				'be' => 'Belgie'
			)
		));

		$software->addElement('checkbox', 'corporation_multiple_currencies', [
			'label' => 'Ondersteuning meerdere valuta',
            'attribs' => array(
                'title' => 'Deze functie NIET inschakelen tenzij de klant voor alleen de facturen de Curacao Gulden bij een juridische entiteit wil instellen.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
		]);

        $software->addElement('checkbox', 'software_multiple_languages_backoffice', [
            'label' => 'Ondersteuning meerdere talen backoffice (beperkt)',
            'attribs' => array(
                'title' => 'Deze functie maakt bij gebruikers taalkeuze voor backoffice beschikbaar. Vooralsnog alleen engels voor rental functionaliteiten (BETA alleen inschakelen na overleg in CS meeting).',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ]);

		$software->addElement('checkbox', 'modules_hospitality', [
			'label' => 'Hospitality module'
		]);

		$software->addElement('checkbox', 'modules_vacancy_management', [
			'label' => 'Leegstandbeheerder'
		]);



		$software->addElement('select', 'invoice_corporation_override_normal_behaviour', [
			'label' => 'Overschreven J.E. tenaamstelling bij betaalinstructies',
            'multiOptions' => [
                'legacy' => 'Origineel',
                'ascription' => 'Altijd tenaamstelling',
                'correct' => 'Correct gedrag'
            ],
			'attribs' => [
				'title' =>
                    'Deze instelling regelt het gedrag rondom de weergave van de juridische entiteit en de tenaamstelling (van de bankrekening) op de facturen. Er zijn drie opties mogelijk:<br /><br />' .
                    '1. - Origineel: Dit is het originele gedrag. * <br />' .
                    '2. - Altijd tenaamstelling: Met deze instelling wordt zowel in de bovenhoek als bij de betaalinformatie de overschreven tenaamstelling weergegeven. * <br />' .
                    '3. - Correct gedrag: Met deze instelling werkt het systeem zoals je mag verwachten, in de bovenhoek wordt de juridische entiteit weergegeven en in de betaalinformatie de tenaamstelling<br /><br />' .
                    'De standaard- en correcte instelling is "Correct gedrag". De eerste twee instellingen (*) zijn foutief gedrag, maar vele klanten hebben hun systeem ingericht op basis van dit gedrag en daarom houden we deze optie nog beschikbaar'
                ,
				'class' => 'forceAutoHint',
            ]
        ]);

		$software->addElement('checkbox', 'calculate_period_based_on_full_year', [
			'label' => 'Periode ratio op jaarbasis',
			'attribs' => [
				'title' => 'De factuur periode ratio bepalen op basis van het gehele jaar ipv. het aantal dagen in de periode.',
				'class' => 'forceAutoHint',
			]
		]);

		$software->addElement('checkbox', 'calculate_period_based_on_full_year_unrounded', [
			'label' => 'Periode ratio op jaarbasis, geen afronding daghuurprijs',
			'attribs' => [
				'title' => 'Wanneer de instelling "periode ratio op jaarbasis" is ingeschakeld wordt de berekende daghuurprijs afgerond op twee cijfers achter de komma, met deze instelling ingeschakeld kan je die afronding voorkomen en met het volledige bedrag rekenen.',
				'class' => 'forceAutoHint',
			]
		]);

        $software->addElement('checkbox', 'project_identifiers_unique', [
            'label' => 'Projectnummers niet oplopend per entiteit, maar algemeen oplopend (en dus altijd uniek).',
        ]);

        $software->addElement('checkbox', 'create_initial_project_runs', [
            'label' => 'Factuurruns direct aanmaken bij kiezen startdatum facturatie',
        ]);


		$software->addElement('checkbox', 'invoice_first_enabled', array(
			'label' => 'Eerste factuur ingeschakeld',
		));


		$software->addElement('checkbox', 'modules_kpi', [
			'label' => 'KPI verslagen module'
		]);

		$software->addElement('checkbox', 'api_partner_pete', [
			'label' => 'Maandlasten Manager'
		]);

		$software->addElement('text', 'api_partner_pete_api_key', [
			'label' => 'Maandlasten Manager Api Key'
		]);

		$software->addElement('checkbox', 'invoice_finalize_by_user', array(
			'label' => 'Automatische verzending facturen',
		));

		$software->addElement('checkbox', 'space_book', [
			'label' => 'Ruimteboek',
			'attribs' => [
				'title' => 'Hiermee komt op de object pagina de optie om ruimtes aan een object te koppelen',
				'class' => 'forceAutoHint',
]
		]);

        $software->addElement('checkbox', 'next_till_date_caches_disabled', [
            'label' => 'Verleng datum cache uitschakelen',
            'attribs' => [
                'title' => 'Deze cache versneld de huurderslijst uitgebreid, maar vertraagd object opslaan. Dit moet '
                    .'uiteraard definitief gefixt worden',
                'class' => 'forceAutoHint',
            ]
        ]);


		$software->addElement('select', 'stationary_logo', array(
			'label' => 'Logo briefpapier',
			'multiOptions' => array(
				'top' => 'Boven',
				'right' => 'Rechts',
				'left' => 'Links'
			)
		));

 


		$software->addElement('select', 'software_language', array(
			'label' => 'Standaard taal login portal',
            'title' => 'Standaard taal login portal',
            'class' => 'forceAutoHint',
			'multiOptions' => array(
				'nl' => 'Nederlands',
				'en' => 'Engels'
			)
		));

		$software->addElement('checkbox', 'software_french_enabled', [
			'label' => 'Franse taal instelbaar (BETA!)',
			'attribs' => [
				'title' => 'BETA! Met deze instelling aan is het mogelijk om "Frans" als taal in te stellen bij '
					.'huurders. Op moment van schrijven zorgt dat alleen voor franstalige herinnering berichten. '
					.'Wees terughoudend met inschakelen!',
				'class' => 'forceAutoHint',
			]
		]);
		
		$software->addElement('checkbox', 'software_XXL', array(
			'label' => 'OmniboXXL',
			'title' => 'Met OmniboXXL komen diverse functies beschikbaar voor grotere vastgoedbeheerders',
			'class' => 'forceAutoHint',
		));

        $software->addElement('checkbox', 'show_exploitation_ended_in_transaction_report', array(
            'label' => 'Openstaande posten inclusief projecten uit exploitatie',
            'title' => 'Het openstaande posten overzicht laat ook openstaande posten zien van projecten die inmiddels uit exploitatie zijn',
            'class' => 'forceAutoHint',
        ));

        $software->addElement('checkbox', 'show_only_initials_lastname_in_transaction_report', array(
            'label' => 'Afkorten pariculiere naam openstaande posten',
            'title' => 'Openstaande posten bij particulier alleen initialen en achternaam tonen (niet volledig rendered name)',
            'class' => 'forceAutoHint',
        ));

		$software->addElement('checkbox', 'search_default_soundex', array(
			'label' => 'Standaard fonetisch zoeken',
		));

		$software->addElement('checkbox', 'search_default_rentalhistorylink', array(
			'label' => 'Standaard link op object en huurder naar historie pagina verhuurmodule',
		));

        $software->addElement('checkbox', 'overide_default_expire_day', [
            'label' => 'Set vervaldatum facturen op de 26e van de maand',
            'attribs' => [
                'title' => 'Maatwerk gemaakt in december 2019 voor klant Lauwerecht. Hiermee komt op alle prolongatieruns de vervadatum op de 26e van de maand te liggen. '
                    . 'Wees terughoudend met inschakelen!',
                'class' => 'forceAutoHint',
            ]
        ]);





        if (Settings::get('meters')) {
            $software->addElement('checkbox', 'meters', [
                'label' => 'Gebruikersprofielen en meters',
            ]);
        } else {
            $href = '/meter/enable-module/';

            $html = '<a class="meters-enable forceAutoHint" href="' . $href . '">';
            $html .= '<i class="fas fa-plus-square"></i><span>Inschakelen</span>';
            $html .= '</a>';

            $software->addElement('html', 'meters', [
                'label' => 'Gebruikersprofielen en meters',
                'html' => $html
            ]);
        }

        $software->addElement('checkbox', 'usage_report', array(
			'label' => 'Bezetting raport (BZR)',
		));	


		
		$software->addElement('text', 'software_logo', array(
			'label' => 'Afwijkend software logo',
		));


        $software->addElement('text', 'menno_test', array(
            'label' => 'Menno test',
        ));

		$software->addElement('text', 'portal_logo', array(
			'label' => 'Afwijkend portal logo',
		));
		$software->addElement('text', 'number_licensed_objects', array(
			'label' => 'Aantal objecten licentie',
		));
		$software->addElement('text', 'number_licensed_users', array(
			'label' => 'Aantal gebruikers licentie',
		));	
		$software->addElement('text', 'number_licensed_personal_page', array(
			'label' => 'Aantal logins persoonlijk pagina licentie',
		));		

		$software->addElement('text', 'start_date_license', array(
			'label' => 'Ingangsdatum licentie',
		));	

		$software->addElement('checkbox', 'general_ip_unrestricted', array(
			'label' => 'IP-restrictie uitgeschakeld',
		));	


		$software->addElement('checkbox', 'for_third_party_notice', array(
			'label' => 'Beheer voor derden waarschuwing?',
		));

 		$software->addElement('checkbox', 'for_third_party', array(
			'label' => 'Beheer voor derden?',
		));	

		$software->addElement('checkbox', 'hide_financial_for_technical', array(
			'label' => 'Verberg financiele info voor techniek?',
		));	


		$software->addElement('checkbox', 'use_firstname_insystem', array(
			'label' => 'Gebruik voornamen ipv initialen?',
		));	

		$software->addElement('checkbox', 'address_right_on_letters', array(
			'label' => 'NAW rechts op envelop?',
		));

        $software->addElement('checkbox', 'company_header_show_address', array(
            'label' => 'Toon adres bij entiteit gegevens op de factuur. De komen van de juridische entiteit',
        ));
        
		$software->addElement('checkbox', 'modules_Loyalty4G_enabled', [
			'label' => 'Loyalty4G module',
            'attribs' => [
                'title' => 'Let op: Deze module uitsluitend voor Hopibon! ',
                'class' => 'forceAutoHint',
            ]
		]);

		$software->addElement('checkbox', 'modules_user_project', array(
			'label' => 'Vestigingsomgeving module',
		));
		$software->addElement('checkbox', 'modules_user_project_show_financial', array(
			'label' => 'Vestigingsomgeving, financieel tonen',
		));

		$software->addElement('checkbox', 'mailings_enabled', [
			'label' => 'Mailings ingeschakeld',
		]);

		$software->addElement('checkbox', 'object_properties_enabled', [
			'label' => 'Objecteigenschappen ingeschakeld',
		]);

        $software->addElement('checkbox', 'object_defined_cities_enabled', [
            'label' => 'Plaatsnamen bij objecten komen uit dropdown en zijn niet meer handmatig in te vullen',
        ]);

        $software->addElement('checkbox', 'object_defined_cities_new_list', [
            'label' => 'Nieuwe plaatsnamen lijst',
        ]);

		$software->addElement('select', 'new_company_uip_bill_default', [
			'label' => 'Standaard instelling voor factuur verzending methode van commerciele gebruikers',
			'multiOptions' => [
				false     => 'Standaard (daba afhankelijk)',
				'email'     => 'Email',
				'mail'     => 'Post',
				'none'     => 'Geen factuur versturen',
			]
		]);

		$software->addElement('checkbox', 'investor_vat_overview_enabled', [
			'label' => 'Belegger BTW overzicht ingeschakeld'
		]);

		$software->addElement('textarea', 'permitted_domains_for_cors_requests', [
			'label' => 'CORS requests toegestaan van deze domeinen',
			'attribs' => [
				'title' => 'Voer meerdere domeinen in door ze met ; te scheiden. Let op het juiste protocol '
					.'(http/https), als het domein op beide bereikbaar is moeten beide varianten hier ingevoerd worden!',
				'class' => 'forceAutoHint',
			],
			'style' => 'width:300px',
			'rows' => 5
		]);

		$software->addElement('checkbox', 'uo_disable_date_validation', [
			'label' => 'Huurder/object periode datum validatie uitgeschakeld',
            'attribs' => [
                'title' => 'LET OP; deze instelling moet met zorg gebruikt worden en de doorwerking in de rest van de software is nog niet doorgetest.',
                'class' => 'forceAutoHint',
            ]
		]);

		$software->addElement('checkbox', 'disable_iban_and_bic_validation', [
			'label' => 'IBAN en BIC validatie uitschakelen',
			'attribs' => [
				'title' => "Dit schakelt alle validatie op invoer/bewerking van IBAN's en BIC's uit. <br>
					LET OP: dit mag alleen ingeschakeld worden na SCHRIFTELIJKE bevestiging van de klant
					dat zij alle verantwoordelijkheid voor de correctheid van IBAN's/BIC's op zich nemen en dat ze 
					begrijpen dat wij geen ondersteuning meer leveren op (incorrecte) incasso en overboekings opdracht
					bestanden.",
				'class' => 'forceAutoHint',
			]
		]);

if (Settings::get('scheduler_enabled')) {
    $cronjob = new Form_SubForm($this);
    $cronjob->setAttrib('title', 'Cronjobs');
    $this->addSubForm($cronjob, 'cronjob');

    $cronjob->addElement('checkbox', 'cronjob_email_import_5_min', [
        'label' => 'cronjob/email-import 5 min'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_delete_email_account_temp', [
        'label' => 'cronjob/Delete-email-account-import 5 min',
        'attribs' => [
            'title' => 'NOOIT AANZETTEN ZONDER AKKOORD VAN DE KLANT, HIERMEE WORDEN MAILS VAN DE ACCOUNTS UIT ONDERSTAANDE SETTING UIT OMNIBOXX VERWIJDERD',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_delete_projects_temp', [
        'label' => 'cronjob/Delete-projects 5 min',
        'attribs' => [
            'title' => 'NOOIT AANZETTEN ZONDER AKKOORD VAN DE KLANT, HIERMEE WORDEN PROJECTEN EN DE GEGEVENS DAARIN UIT ONDERSTAANDE OMNIBOXX VERWIJDERD HIERVOOR MOET OOK HANDMATIG IDs in de database door techniek gezet worden.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('text', 'cronjob_delete_email_account_temp_account_ids', [
        'label' => 'cronjob/Delete-email-account-import ids',
        'attribs' => [
            'title' => 'de accountids van de mailboxen gescheiden door een komma waarvan de mails uit Omniboxx verwijderd worden',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_email_import_daily', [
        'label' => 'cronjob/email-import dagelijks'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_retry_import_new_email_from_error_directory', [
        'label' => 'cronjob/retry-import-new-email-from-error-directory'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_import_third_party_purchase_invoices', [
        'label' => 'cronjob/import-third-party-purchase-invoices'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_twinfield_retry_document_download', [
        'label' => 'cronjob/retry-twinfield-document-download'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_invoice_pdfs', [
        'label' => 'cronjob/invoice-pdfs'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_mail_expire_contracts', [
        'label' => 'Mail expire contracts'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_create_final_notas', [
        'label' => 'Automatisch aanmaken van tussentijdse afrekeningen'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_mail_expire_documents', [
        'label' => 'Mail te verlopen documenten 3 maanden van te voren via e-mail naar standaard mail account met titel "Aflopende documenten"'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_upcoming_maintenance', [
        'label' => 'Mail aankomend onderhoud 1 maand van te voren via e-mail naar standaard mail account'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_mail_expire_finances', [
        'label' => 'Mail aankomend aflopende financieren de komende 6 maanden van te voren via e-mail naar standaard mail account'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_mail_expire_leads_payed_account', [
        'label' => 'Mail expire payed account leads'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_leads_mail_matching_objects', [
        'label' => 'cronjob/leads-mail-matching-objects'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_send_missing_indexation_notifications', [
        'label' => 'cronjob/send-missing-indexation-notifications'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_check_activities', [
        'label' => 'cronjob/check-activities'
    ]);

    $cronjob->addElement('checkbox',
        'cronjob_send_published_objects_to_vidii_or_website_worker_strategy_all_published_in_rental', [
            'label' => 'cronjob_vidii/publish-all'
        ]);

    $cronjob->addElement('checkbox',
        'cronjob_send_published_objects_to_website_worker_strategy_publish_all_rental_light', [
        'label' => 'cronjob_vidii/publish-all-rental-light'
    ]);

    $cronjob->addElement('checkbox',
        'cronjob_send_published_objects_to_website_rental_worker_strategy_one_by_one', [
            'label' => 'cronjob_vidii/publish-all-rental-one-by-one'
        ]);

    $cronjob->addElement('checkbox', 'cronjob_transaction_import', [
        'label' => 'cronjob/transaction-import'
    ]);

    $cronjob->addElement('checkbox', 'tiara_medium_re_sync', [
        'label' => 'tiara_medium/re-sync'
    ]);

    $cronjob->addElement('checkbox', 'tiara_queue_process', [
        'label' => 'tiara_queue/process'
    ]);

    $cronjob->addElement('checkbox', 'tiara_request_internetplaatsingen', [
        'label' => 'tiara_request/internetplaatsingen'
    ]);

    $cronjob->addElement('checkbox', 'tiara_request_nvm_vestigingen', [
        'label' => 'tiara_request/nvm-vestigingen'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_sync_third_party_outstanding_invoices', [
        'label' => 'cronjob/sync-third-party-outstanding-invoices'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_nedap_ons_users_import', [
        'label' => 'cronjob/nedap-ons-users-import'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_update_investor_provision_data', [
        'label' => 'cronjob/update-investor-provision-data'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_vidii_publish_all_projects', [
        'label' => 'cronjob_vidii/publish-all-projects'
    ]);

    $cronjob->addElement('checkbox', 'cronjob_fix_invoice_already_been_exchanged_with_exact_online', [
        'label' => 'Cronjob fix Exact Online error dat de factuur al uit gewisseld is',
            'attribs' => [
            'title' => 'Dit fix het probleem voor bij Exact online als de error : "Onderwerp [GLTransactions] Bestaat reeds - Boekstuknummer:". cronjob_financial-third-party/fix-invoice-already-been-exchanged-with-exact-online',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_exchange-invoice-runs-to-third-party-financial-system', [
        'label' => 'Cronjob wissel nogmaals facturen uit met het financieel systeem',
        'attribs' => [
            'title' => 'De facturen die in het nog te wisselen overzicht staan worden sacons nog een poging gedaan om de uit te wisselen. cronjob_financial-third-party/exchange-invoice-runs-to-third-party-financial-system',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_generate_invoices', [
        'label' => 'cronjob_generate_invoices',
        'attribs' => [
            'title' => 'cronjob_generate_invoices',
            'class' => 'forceAutoHint',
        ]
    ]);


    $cronjob->addElement('checkbox', 'cronjob_mail-chimp-sync-email-user-groups', [
        'label' => 'cronjob_mail-chimp-sync-email-user-groups',
        'attribs' => [
            'title' => 'cronjob_mail-chimp-sync-email-user-groups',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_object-status-notifications', [
        'label' => 'cronjob_object-status-notifications',
        'attribs' => [
            'title' => 'cronjob_object-status-notifications',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_regenerate-all-open-invoiceruns', [
        'label' => 'cronjob_regenerate-all-open-invoiceruns',
        'attribs' => [
            'title' => 'cronjob_regenerate-all-open-invoiceruns',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_update-investor-provision-data', [
        'label' => 'cronjob_update-investor-provision-data',
        'attribs' => [
            'title' => 'cronjob_update-investor-provision-data',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_recalculate-investor-provision-data', [
        'label' => 'cronjob_recalculate-investor-provision-data',
        'attribs' => [
            'title' => 'cronjob_recalculate-investor-provision-data',
            'class' => 'forceAutoHint',
        ]
    ]);


    $cronjob->addElement('checkbox', 'cronjob_mailing-watchdog', [
        'label' => 'cronjob_mailing-watchdog',
        'attribs' => [
            'title' => 'cronjob_mailing-watchdog',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_transactions-import-from-financial-export', [
        'label' => 'cronjob_transactions-import-from-financial-export',
        'attribs' => [
            'title' => 'cronjob_transactions-import-from-financial-export',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_trigger-scheduling-events_interval_day', [
        'label' => 'cronjob_trigger-scheduling-events_interval_day',
        'attribs' => [
            'title' => 'cronjob_trigger-scheduling-events_interval_day',
            'class' => 'forceAutoHint',
        ]
    ]);

    $cronjob->addElement('checkbox', 'cronjob_send-scheduled-emails_interval_day', [
        'label' => 'cronjob_send-scheduled-emails_interval_day',
        'attribs' => [
            'title' => 'cronjob_send-scheduled-emails_interval_day',
            'class' => 'forceAutoHint',
        ]
    ]);
}



	$tenantLogin = new Form_SubForm($this);
	$tenantLogin->setAttrib('title', 'Persoonlijke pagina module');
	
	$this->addSubForm($tenantLogin, 'tenantLogin');
	
		$tenantLogin->addElement('checkbox', 'modules_tenantLogin', array(
			'label' => 'Persoonlijke pagina',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_maintainance', [
			'label' => 'Onderhoudsmodus',
			'attribs' => [
				'title' => 'Toon (na het inloggen) een onderhoudsmelding voor huurders en beleggers ipv hun portal.',
				'class' => 'forceAutoHint',
			]
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_new_styling', array(
			'label' => 'Nieuwe styling',
		));

		$tenantLogin->addElement('text', 'modules_tenantLogin_google_analytics', array(
			'label' => 'Google Analytics code',
			'placeholder' => 'Bijvoorbeeld: UA-99999999-2'
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_reports', array(
			'label' => 'Persoonlijke pagina rapportage module',
		));	

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_multilanguage', array(
			'label' => 'Persoonlijke pagina meertalig',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_firstnamebasis', array(
			'label' => 'Tutoyeren in digitaal contracteren en persoonlijke pagina',
		));		

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_contact_phone', array(
			'label' => 'Verberg de telefoon methode van contact leggen op de contactpagina',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_contact_writing', array(
			'label' => 'Verberg het post methode van contact leggen op de contactpagina',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_payment_type', array(
			'label' => 'Betalingsmethode verbergen',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_payment_type_only_mail', array(
			'label' => 'Verberg de methode factuur ontvangen per post',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_collection_type', array(
			'label' => 'Geen automatische incasso aanbieden',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_welcome_name', array(
			'label' => 'Niet gepersonaliseerde welkomsttekst',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_complaints', array(
			'label' => 'Storingen/meldingen op persoonlijke pagina verbergen',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_allowance', [
			'label' => 'Toestemming op persoonlijke pagina verbergen',
        ]);


		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_hide_invoice_payment_status', array(
			'label' => 'Betaalstatus verbergen',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_show_penalty', array(
			'label' => 'Boete weergeven',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_document_upload', array(
			'label' => 'Documenten upload ingeschakeld',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_investor_hide_tenant_and_object_warnings', [
			'label' => 'Verberg aandachtspunten van huurders en objecten in de beleggers portal',
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_investor_hide_tenant_and_object_documents', [
			'label' => 'Verberg documenten van huurders en objecten in de beleggers portal',
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_document_upload', array(
			'label' => 'Documenten upload ingeschakeld',
            'title' => 'Met deze optie kan per "document titel" in de instellingen van document categorieen en titels worden ingegeven of deze door de klant in de portal uploadbaar zijn.',
            'class' => 'forceAutoHint',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_document_upload_pre_contracting', array(
			'label' => 'Documenten upload beschikbaar voor contracteren afgerond is',
            'title' => 'Met deze optie kan de gebruiker de documenten waarvan is aangegeven dat ze uploadbaar zijn tijdens het contracteren zelf uploaden, onder Vidii instelling kunnen verplichte documenten geconfigureerd worden',
            'class' => 'forceAutoHint',
		));
		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_checkbeforesign', [

			'label' => "'Contract ondertekenen toegestaan' vinkje bij huurders staat standaard uit, en verplichte ".
				'document(en) moeten ingevoerd zijn voordat getekend mag worden.',
		]);

		$tenantLogin->addElement('checkbox', 'modules_allow_id_upload_portal', [
			'label' => "'Na contract ondertekenen ID uploaden toegestaan'",
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_contract_validating', [
			'label' => 'Na contracteren valideren door account manager',
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_confirm_cell_phone', [
			'label' => 'Contracteren bevestigen met sms',
		]);

		$tenantLogin->addElement('text', 'modules_tenantLogin_nameplate_email', [
			'label' => 'E-mail adres voor aanmaken naamplaatje',
		]);

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_no_indexed_ratesheet_values', array(
			'label' => 'Geindexeerde huurbedragen negeren',
		));

		$tenantLogin->addElement('text', 'modules_tenantLogin_custom_dummy_login_url', array(
			'label' => 'Dummy login url',
		));

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_allow_tenant_status_updates', [
			'label' => 'Sta status updates op meldingen van klanten toe',
		] );

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_show_complaint_status_list', [
			'label' => 'Toon status verloop en email contact op de portal.',
			'title' => 'Geeft klanten toegang tot de informatie in het "status verloop" blokje (van de
				support pagina van een melding) en het email verkeer, zolang deze melding open staat.',
			'class' => 'forceAutoHint',
		] );

		$tenantLogin->addElement('checkbox', 'modules_tenantLogin_add_complaint_link_to_complaint_emails', [
			'label' => 'Voeg een link naar de persoonlijke pagina toe aan meldingsmails. ZIE TOELICHTING!',
			'title' => 'Me deze link kan de huurder/gebruiker zonder inloggen direct naar de status vd melding op de
				p.p. kan gaan. Hij/zij is dan ook ingelogt op de persoonlijke pagina! De link stopt na enige
				tijd met functioneren.',
			'class' => 'forceAutoHint',
		] );


		$tenantLogin->addElement('checkbox', 'broker_page_filter_project_corporation_id', [
			'label' => 'Belegger pagina, huurder facturen enkel project entiteit',
			'title' => 'Facturen van huurders enkel tonen wanneer ze gemaakt zijn vanuit de entiteit die gekoppeld is aan het project',
			'class' => 'forceAutoHint',
		] );


		$tenantLogin->addElement('text', 'modules_tenantLogin_profile_complete_fields', [
			'label' => 'Minimale invoer persoonlijke pagina',
			'title' => 'Bij de mailing list kan een mailing worden aangemaakt op het event "Nieuw account, maar nog niet compleet", deze setting bepaald welke velden er gecontroleerd worden. Voorbeeld: phone_emergency, emergency_contactname',
			'class' => 'forceAutoHint'
 		]);


	$app = new Form_SubForm($this);
	$app->setAttrib('title', 'App module');

	$this->addSubForm($app, 'app');

		$app->addElement('checkbox', 'modules_app_enabled', array(
			'label' => 'App module ingeschakeld',
		));	

		$app->addElement('checkbox', 'modules_app_inspection', array(
			'label' => 'Inspectie app module',
		));	

		$app->addElement('checkbox', 'modules_app_alert', array(
			'label' => 'Alert app module',
		));

		$app->addElement('checkbox', 'modules_app_alert_register_enabled', array(
			'label' => 'Alert app registratie via app ingeschakeld',
		));

		$app->addElement('textarea', 'modules_app_alert_intro_text_nl', array(
			'label' => 'Alert app intro tekst',
			'placeholder' => 'De tekst voor wanneer de Alert App geopend wordt',
			'style' => 'width:330px',
			'rows' => 5
		));

		$app->addElement('textarea', 'modules_app_alert_intro_text_en', array(
			'label' => 'Alert app intro tekst Engels',
			'placeholder' => 'De Engelse tekst voor wanneer de Alert App geopend wordt',
			'style' => 'width:330px',
			'rows' => 5
		));

		$moduleReceptionTelephoneSupport = new Form_SubForm($this);
		$moduleReceptionTelephoneSupport->setAttrib('title', 'Receptie telefonische ondersteuning module');
		$this->addSubForm($moduleReceptionTelephoneSupport, 'module_reception_telephone_support');

		$moduleReceptionTelephoneSupport->addElement('checkbox', 'module_reception_telephone_support_enabled', [
			'label' => 'Activeer Receptie telefonische ondersteuning module',
		]);

        $moduleReceptionTelephoneSupport->addElement('checkbox', 'module_reception_telephone_redirect_support_page', [
            'label' => 'Receptie telefonische ondersteuning naar huurder pagina i.p.v. receptie pagina',
        ]);

		$moduleReceptionTelephoneSupport->addElement('number', 'module_reception_telephone_support_mail_template_id', [
			'label' => 'Standaard e-mail template id',
		]);

	$deposit_korfine = new Form_SubForm($this);

	$deposit_korfine->setAttrib('title', 'Waarborg Korfine module');

		$this->addSubForm($deposit_korfine, 'deposit_korfine');

		$deposit_korfine->addElement('checkbox', 'modules_deposit_korfine_enabled', [
			'label' => 'Korfine module ingeschakeld',
        ]);

		$deposit_korfine->addElement('text', 'modules_deposit_korfine_username', [
			'label' => 'Korfine gebruikersnaam',
        ]);

		$deposit_korfine->addElement('text', 'modules_deposit_korfine_password', [
			'label' => 'Korfine wachtwoord',
		]);

		$deposit_korfine->addElement('text', 'modules_deposit_korfine_investor_email_override', [
			'label' => 'Belegger E-mail adressen overschrijven',
            'attribs' => [
                'title' => 'Met deze instelling kan het belegger E-mail adres overschreven worden met een standaard waarde. Hierdoor zullen rapportages van Korfine naar dit mail adres verstuurd worden in plaasts van naar het E-mail adres van de beleggers.',
                'class' => 'forceAutoHint',
            ]
		]);

	$roomselector = new Form_SubForm($this);
	$roomselector->setAttrib('title', 'Roomselector module');
	
	$this->addSubForm($roomselector, 'roomselector');

		$roomselector->addElement('checkbox', 'modules_tenantLogin_roomselector', array(
			'label' => 'Roomselector module ingeschakeld',
		));

		$roomselector->addElement('checkbox', 'modules_tenantLogin_roomselector_manually_control_availability', [
			'label' => 'Handmatig beschikbaarheid bepalen',
			'attribs' => [
				'title' => 'Als deze instelling uit staat, worden objecten (die in Vidii staan en leeg zijn) automatisch aangeboden.
					Met de instelling aan is het aanbod handmatig te bepalen via het menu.',
				'class' => 'forceAutoHint',
			]
		] );

		$roomselector->addElement('select', 'modules_tenantLogin_roomselector_objectgroups', array(
			'label' => 'Objectgroepen waarbinnen geselecteerd mag worden',

			'multiple' => true,

			'RegisterInArrayValidator' => false,

			'multiOptions' => $objectgroups,
		));

		$roomselector->addElement('text', 'modules_tenantLogin_roomselector_registrant_start_date', array(
			'label' => 'Standaard ingangsdatum voor registreerders',
			'title' => 'yyyy-mm-dd',
			'class' => 'forceAutoHint',
		));

		$roomselector->addElement('checkbox', 'modules_tenantLogin_roomselector_no_object_selection', array(
			'label' => 'Geen object selecteren via roomselector',
		));

		$roomselector->addElement('text', 'modules_tenantLogin_roomselector_obligated_documents', array(
			'label' => 'Verplichten documenten voor contracteren',
			'title' => 'titleid-titleid-titleid',
			'class' => 'forceAutoHint',
		));


	$contracts = new Form_SubForm($this);
	$contracts->setAttrib('title', 'Contracten');
	$this->addSubForm($contracts, 'contracts');

		$contracts->addElement('checkbox', 'modules_contracts', [
			'label' => 'Contracten functionaliteit',
		]);

		$contracts->addElement('checkbox', 'modules_contracts_daily', [
			'label' => 'Contracten weekendkaarten',
		]);

		$contracts->addElement('checkbox', 'modules_contracts_disable_object_edit', [
			'label' => 'Object bewerkingen uitschakelen',
			'title' => 'Gebruikerkoppelingen en bedragen bewerken uitschakelen bij object',
			'class' => 'forceAutoHint',
		]);

		$contracts->addElement('checkbox', 'modules_contracts_detailinfo', [
			'label' => 'Contracten omschrijving inschakelen',
		]);

		$contracts->addElement('checkbox', 'modules_contracts_use_custom_identifier', [
			'label' => 'Overal eigen contractnummer gebruiken ipv systeem nummer',
		]);

		$contracts->addElement('text', 'modules_contracts_default_period_formula', [
			'label' => 'Standaard verlengformule voor huurcontracten',
			'title' => 'A*B,X*Y,etc',
			'class' => 'forceAutoHint',
		]);

		$contracts->addElement('text', 'modules_contracts_default_notice_period', [
			'label' => 'Standaard opzegtermijn voor huurcontracten (in maanden)',
		]);

		$contracts->addElement('text', 'modules_contracts_default_periodical_period_formula', [
			'label' => 'Standaard verlengformule voor periodekaarten',
			'title' => 'A*B,X*Y,etc',
			'class' => 'forceAutoHint',
		]);

		$contracts->addElement('text', 'modules_contracts_default_periodical_notice_period', [
			'label' => 'Standaard opzegtermijn voor periodekaarten (in maanden)',
		]);

		$contracts->addElement('checkbox', 'modules_contracts_get_notice_period_from_formula', [
			'label' => 'Opzegtermijn uit verlengformule [999] deel halen',
            'attribs' => [
                'title' => 'Wanneer er geen opzegtermijn is ingevuld, gebruik dan het [999] deel van de formule als opzegtermijn. Tot heden enkel toegepast bij Word merge variabele.',
                'class' => 'forceAutoHint',
            ],
		]);

		$contracts->addElement('text', 'notice_period_notification_investors_month_offset', [
			'label' => 'Notificatie opzegdatum belegger, [xxx] maanden van tevoren',
			'placeholder' => 2,
			'attribs' => [
				'title' => 'Wanneer de notificatie mail is ingesteld voor de klant (cronjob) dan zal de opzegdatum standaard 2 maanden voor de opzegdatum verstuurd worden. Met deze instelling kan die periode van 2 maanden aangepast worden.',
				'class' => 'forceAutoHint',
			],
		]);

	$rental = new Form_SubForm($this);
	$rental->setAttrib('title', 'Verhuur module');

	$this->addSubForm($rental, 'rental');

		$rental->addElement('checkbox', 'modules_rental', array(
			'label' => 'Verhuur module ingeschakeld',
		));

        $rental->addElement('checkbox', 'modules_rental_reset_debtorcode_when_tenant', array(
            'label' => 'reset deb. code als registrant wordt omgezet naar huurder',
        ));

		$rental->addElement('checkbox', 'modules_rental_contract_deals', array(
			'label' => 'Deals voor contracten',
		));

        $rental->addElement('checkbox', 'quick_publish_objects', [
            'label' => 'Objecten snel kunnen publiceren',
            'attribs' => [
                'title' => 'Met deze instelling komt er bij de objecten een knop om direct te kunnen publiceren. Echter werkt deze knop enkel wanneer er bij de website ook ondersteuning hiervoor is ingebouwd.',
                'class' => 'forceAutoHint',
           ]
        ]);


$rental->addElement('checkbox', 'modules_rental_workflow_deals', array(
			'label' => 'In plaats van deals per object, de workflow stappen als uitgangspunt gebruiken',
		));

        $rental->addElement('checkbox', 'modules_rental_workflow_status_on_deadline', array(
            'label' => 'De kleuren in de workflow bakjes worden bepaald tov de deadline. Indien deze setting uitstaat wordt dit gedaan op basis van de contactdatum van de deal.',
        ));

		$rental->addElement('checkbox', 'modules_rental_complete_dossier_for_assign', array(
			'label' => 'Upload dossier voor starten deal (controle documenten hoeft dan niet meer in de workflow)',
		));

		$rental->addElement('checkbox', 'modules_rental_object_search_history', array(
			'label' => 'Link op object bij zoekresultaten laten verwijzen naar objecthistorie ipv objectdetail pagina',
		));

		$rental->addElement('checkbox', 'soomedia_activated', array(
			'label' => 'SooMedia bestellingen toegestaan',
		));

 		$rental->addElement('checkbox', 'modules_rental_website_deal_viewing_inform_current_tenant', [
			'label' => 'Website deal, bezichtiging afspraak huidige en vorige huurder',
        ]);

		$rental->addElement('checkbox', 'modules_rental_dashboard_interhouse', array(
			'label' => 'Interhouse dashboard',
		));

		$rental->addElement('checkbox', 'modules_rental_lead_mailing', [
			'label' => 'Lead aanbod mailing ingeschakeld',
        ]);

        $rental->addElement('text', 'modules_rental_website_subscription_template', array(
            'label' => 'Afwijkend template bij versturen bevestiging website inschrijving',
            'attribs' => array(
                'title' => 'Alleen invullen door techniek, de template bestanden moeten ook aan Omniboxx toegevoegd zijn. Alleen de template naar, geen pad ingeven. Bestand wordt geplaats in rental/website',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'modules_rental_website_viewing_request_template', array(
            'label' => 'Afwijkend template bij versturen bevestiging bezichting aanvraag',
            'attribs' => array(
                'title' => 'Alleen invullen door techniek, de template bestanden moeten ook aan Omniboxx toegevoegd zijn. Alleen de template naar, geen pad ingeven. Bestand wordt geplaats in subscriptopns/override',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'modules_rental_website_request_documents_template', array(
            'label' => 'Afwijkend template bij opvragen documenten',
            'attribs' => array(
                'title' => 'Alleen invullen door techniek, de template bestanden moeten ook aan Omniboxx toegevoegd zijn. Alleen de template naar, geen pad ingeven. Bestand wordt geplaats in subscriptopns/override',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));


        $rental->addElement('text', 'modules_rental_cancel_missing_documents_template', array(
            'label' => 'Afwijkend template bij afwijzen missende/niet tijdig documenten',
            'attribs' => array(
                'title' => 'Alleen invullen door techniek, de template bestanden moeten ook aan Omniboxx toegevoegd zijn. Alleen de template naar, geen pad ingeven. Bestand wordt geplaats in subscriptopns/override',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));



        $rental->addElement('text', 'template_mail_matching_objects', array(
            'label' => 'Template versturen match mails',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'template_denied_website_viewing', array(
            'label' => 'Template uitloten website aanvraag',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'template_denied_website_group_viewing', array(
            'label' => 'Template annuleer overige kandidaten bij groepsbezichtiging',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                        Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'template_denied_dossier_check', array(
            'label' => 'Template afwijzen bij dossier afwijzing',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));

        $rental->addElement('text', 'template_files_approved_digital_sign', array(
            'label' => 'Template uitnodigen digitaal contracteren',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));



        $rental->addElement('text', 'template_mail_expire_leads_payed_account', array(
            'label' => 'Template bij versturen mail aflopend account',
            'attribs' => array(
                'title' => 'Als de klant niet de de standaardtekst wil dan kan een aparte template gemaakt worden in de mail templates map partials/email/rental/website/override. 
                Let op. Deze templates moeten ingevoerd worden door techniek.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            )
        ));


		$rental->addElement('text', 'modules_rental_website_url', array(
			'label' => 'URL voor objecten op website',
			'attribs' => array(
				'title' => 'Tot op heden kunnen de volgende variabelen gebruikt worden in de url: [object_identifier] [projectnummer]',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$rental->addElement('text', 'modules_rental_match_price_stretch', array(
			'label' => 'Percentage afwijking prijs range',
			'attribs' => array(
				'title' => 'Matchen tussen zoekenden en objecten met een percentage afwijking. Standaard 10%'
					.'Gaat in als een medewerker akkoord is gegaan met de initiele documenten en daarmee om '
					.'aanvullende documenten heeft gevraagd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
				'placeholder' => 10
			)
		));

		$rental->addElement('checkbox', 'modules_rental_default_merge_to_word', array(
			'label' => 'Standaard mergen naar Word',
			'attribs' => array(
				'title' => 'Staat hier omdat dit vooralsnog enkel voor mergen op het deals dashboard is',
					'Gaat in als een medewerker akkoord heeft gegeven tot contracteren.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
				'placeholder' => 10
			)
		));


			$rental->addElement('text', 'modules_rental_website_subscription_template', array(
				'label' => 'Template bij versturen bevesiting website inschrijving',
			));

            $rental->addElement('text', 'template_mail_expire_leads_payed_account', array(
                'label' => 'Template bij versturen mail aflopend account',
            ));



		global $config;



		$rental->addElement('checkbox', 'modules_rental_website_enabled', [
			'label' => 'Rental website ingeschakeld',
			'attribs' => [
				'title' => 'Op moment van schrijven bepaald dit alleen of bepaalde bevestigings emails '
					.'verzonden worden.<br><br>'
					.'<b>LET OP:</b> Is de wens voor een afwijkende template dan kan dit met de setting "modules_rental_website_subscription_template".',
				'class' => 'forceAutoHint',
			],
		]);

        $rental->addElement('textarea', 'modules_rental_website_viewings_explanation', [
            'label' => 'Toelichting Omniboxx website aanvragen',
            'attribs' => [
                'title' => 'Toelichting tekst die getoond wordt bij website aanvragen',
                'rows' => 5,
                'cols' => 80,
                'class' => 'forceAutoHint',
            ],
        ]);

        $rental->addElement('text', 'subscription_parts_path', [
            'label' => 'Overschreven template pad voor aanvullende documenten',
            'attribs' => [
                'title' => 'Alleen invullen door techniek, de template bestanden moeten ook aan Omniboxx toegevoegd zijn',
                'class' => 'forceAutoHint',
            ],
        ]);


        $rental->addElement('checkbox', 'modules_rental_text_fields_wysiwyg', [
            'label' => 'Opmaak inschakelen voor project- en objectomschrijvingen',
            'attribs' => [
                'title' => 'Met deze instelling krijgt de gebruiker beschikking over knoppen om de opmaak van de project en object teksten aan te passen.',
                'class' => 'forceAutoHint',
            ],
        ]);

		$rental->addElement('text', 'modules_rental_wordpress_import_import_url', [
			'label' => 'Wordpress import url',
			'value' => getExternalRootUrl()
					."/vidii_api/get-published-objects/strategy/all-published-in-rental/",
			'attribs' => [
				'readonly' => true,
				'ignored' => true,
				'title' => 'Deze url moet in de "WP All Import" ingevuld worden in het "Download from url" veld.',
				'class' => 'forceAutoHint',
			],
		]);

		$rental->addElement('text', 'modules_rental_wordpress_import_trigger_url', [
			'label' => 'Website Wordpress bijwerk trigger url',
			'attribs' => [
				'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
					.'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
					.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=trigger',
				'class' => 'forceAutoHint',
			],
		]);

		$rental->addElement('text', 'modules_rental_wordpress_import_process_url', [
			'label' => 'Website Wordpress bijwerk execution url',
			'attribs' => [
				'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
					.'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
					.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=processing',
				'class' => 'forceAutoHint',
			],
		]);

        $rental->addElement('text', 'modules_rental_light_wordpress_import_trigger_url', [
            'label' => 'Website Wordpress bijwerk light trigger url',
            'attribs' => [
                'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
                    .'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
                    .'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=trigger',
                'class' => 'forceAutoHint',
            ],
        ]);

        $rental->addElement('text', 'modules_rental_light_wordpress_import_process_url', [
            'label' => 'Website Wordpress bijwerk light execution url',
            'attribs' => [
                'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
                    .'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
                    .'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=processing',
                'class' => 'forceAutoHint',
            ],
        ]);

		$rental->addElement('text', 'modules_rental_wordpress_availability_trigger_url', [
			'label' => 'Website Wordpress beschikbaarheid trigger url',
			'attribs' => [
				'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
					.'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
					.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=trigger',
				'class' => 'forceAutoHint',
			],
		]);

		$rental->addElement('text', 'modules_rental_wordpress_availability_process_url', [
			'label' => 'Website Wordpress beschikbaarheid execution url',
			'attribs' => [
				'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
					.'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
					.'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=14&action=processing',
				'class' => 'forceAutoHint',
			],
		]);

        $rental->addElement('text', 'modules_rental_wordpress_quick_publish_object_trigger_url', [
            'label' => 'Website Wordpress snel object toevoegen trigger url',
            'attribs' => [
                'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
                    .'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
                    .'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=15&action=trigger',
                'class' => 'forceAutoHint',
            ],
        ]);

        $rental->addElement('text', 'modules_rental_wordpress_quick_publish_object_process_url', [
            'label' => 'Website Wordpress snel object toevoegen execution url',
            'attribs' => [
                'title' => 'In de wordpress backend te vinden onder All Import->Manage Imports->de '
                    .'import voor deze omgeving->Cron Scheduling. Heeft ongeveer dit formaat:'
                    .'http://hureninvidii.nl/wp-cron.php?import_key=123456&import_id=15&action=processing',
                'class' => 'forceAutoHint',
            ],
        ]);

		$rental->addElement('select', 'modules_rental_website_default_email_account', [
			'label' => 'Standaard email account',
			'multioptions' 	=> (new EmailAccounts())->getForForm(),
			'attribs' => [
				'style' => 'width: 300px',
				'title' => 'Bij automatische verzending wordt standaard dit account gebruikt als "Afzender" adres. '
							.'Op moment van schrijven alleen toegepast bij enkele bevestigings emails naar de huurder.',
				'class' => 'forceAutoHint',
			],
		]);

		$rental->addElement('checkbox', 'modules_rental_mail_lead_proposal', [
			'label' => 'Lead voorstel v. belegger mail knop'
		]);


		$tiara = new Form_SubForm($this);
		$tiara->setAttrib('title', 'Tiara');
		$this->addSubForm($tiara, 'tiara');

		$tiara->addElement('text', 'tiara_nvm_vestiging_nr', [
			'label' => 'NVM VestigingsNr'
		]);

	$vidii = new Form_SubForm($this);
	$vidii->setAttrib('title', 'Vidii module');

	$this->addSubForm($vidii, 'vidii');

		$vidii->addElement('checkbox', 'modules_vidii', [
				'label' => 'Vidii module ingeschakeld',
		]);

		$vidii->addElement('text', 'modules_vidii_deadline_stage_supplementary_documents', [
			'label' => 'Deadline "aanleveren aanvullende documenten"',
			'attribs' => [
				'title' => 'Aantal dagen dat een huurder heeft om aanvullende documenten aan te leveren. '
					.'Gaat in als een medewerker akkoord is gegaan met de initiele documenten en daarmee om '
					.'aanvullende documenten heeft gevraagd.',
				'class' => 'forceAutoHint',
			],
		]);

        $vidii->addElement('text', 'override_vidii_contract_template', [
            'label' => 'Template uitnodigen digitaal contracteren (afwijkend)"',
            'attribs' => [
                'title' => 'Template voor uitnodigen digitaal contracteren. Alleen door techniek in te vullen. Template dient te worden toegevoegd in subscriptions/override/ zonder de .phtml extensie en eindigend op een _ waar in de map een versie voor nl en en neergezet moet worden.',
                'class' => 'forceAutoHint',
            ],
        ]);

		$vidii->addElement('text', 'modules_vidii_deadline_stage_contract_sign', [
			'label' => 'Deadline "digitaal contracteren"',
			'attribs' => [
				'title' => 'Aantal dagen dat een huurder heeft om zijn/haar contract digitaal te ondertekenen. '.
					'Gaat in als een medewerker akkoord heeft gegeven tot contracteren.',
				'class' => 'forceAutoHint',
			],
		]);

		$vidii->addElement('checkbox', 'vidii_analytics', [
			'label' => 'Vidii analytics',
            'attribs' => [
                'title' => 'Het tonen van Vidii analytics widgets op het Vidii dashboard en het beleggers portaal.',
                'class' => 'forceAutoHint',
            ],
        ]);

		$vidii->addElement('checkbox', 'modules_vidii_access_code_enabled', [
			'label' => 'Vidii toegangscodes ingeschakeld',
		]);

		$vidii->addElement('text', 'modules_vidii_access_code_lifetime', [
			'label' => 'Geldigheidsduur toegangscodes (in dagen)',
			'validators' => [
				[
					'validator' => 'Int',
				],
				[
					'validator' => 'Between',
					'options'   => [
						'min'        => 0,
						'max'        => 1000,
						'translator' => 'asdfasdf',
					],
				],
			],
			'attribs' => [
				'title' => 'Hoe lang toegangscodes geldig zijn na het moment van uitgeven.',
				'class' => 'forceAutoHint',
			],
		]);

        $vidii->addElement('checkbox', 'modules_vidii_register_leads_english', [
            'label' => 'Leads standaard Engelstalig',
        ]);

	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Financiele koppeling');
	
	$this->addSubForm($financial, 'financial');



		$financial->addElement('select', 'financial_export_system', array(
			'label' => 'Financiele koppeling ',

			'multiOptions' => FinancialExportSystems::$labels,

			'RegisterInArrayValidator' => false,
		));

    $financial->addElement('checkbox', 'financial_import_transactions', [
        'label' => 'Transacties importeren',
        'attribs' => [
            'title' => 'Het inschakelen van het importeren van transacties uit het gekozen financiele pakket',
            'class' => 'forceAutoHint'
        ]
    ]);

    $financial->addElement('checkbox', 'financial_export_invoices', [
        'label' => 'Facturen exporten',
        'attribs' => [
            'title' => 'Het inschakelen van het exporteren van facturen naar het gekozen financiele pakket',
            'class' => 'forceAutoHint'
        ]
    ]);


		$financial->addElement('text', 'financial_export_sales_journalcode', array(
			'label' => 'Verkoopboek code*',
            'attribs' => [
                'title' => 'De code of omschrijving van het verkoopboek in uw financiele systeem. BV VRK of 80',
                'class' => 'forceAutoHint'
            ]
		));

		$financial->addElement('text', 'financial_export_sales_journalpurchasecode', array(
			'label' => 'Inkoopboek code *',
            'attribs' => [
                'title' => 'De code of omschrijving van het inkoopboek in uw financiele systeem. BV INK of 70',
                'class' => 'forceAutoHint'
            ]
		));

		$financial->addElement('text', 'financial_export_sales_debtcode', array(
			'label' => 'Debiteuren grootboek *',
            'attribs' => [
                'title' => 'De grootboekcode van debiteuren in uw financiele systeem. BV 1300',
                'class' => 'forceAutoHint'
            ]
		));


		$financial->addElement('text', 'financial_export_sales_credcode', array(
			'label' => 'Crediteuren grootboek *',
            'attribs' => [
                'title' => 'De grootboekcode van crediteuren in uw financiele systeem. BV 1600',
                'class' => 'forceAutoHint'
            ]
		));


		$financial->addElement('checkbox', 'financial_export_vat_code_on_components', [
			'label' => 'BTW code overschrijfbaar per component',
			'attribs' => [
				'title' => 'Tot dusver enkel geïmplementeerd bij de Exact Online koppeling',
				'class' => 'forceAutoHint'
			]
        ]);

		// VAT purchase
		$financial->addElement('text', 'financial_export_sales_vathigh', array(
			'label' => 'BTW hoog (excl)*',
            'attribs' => [
                'title' => 'Grootboekrekening van BTW hoog in de boekhouding (bv 1505)',
                'class' => 'forceAutoHint'
            ]
		));
		$financial->addElement('text', 'financial_export_sales_vathighcode', array(
			'label' => 'BTW Code Hoog (excl)*',
            'attribs' => [
                'title' => 'Code van BTW hoog verkoop in de boekhouding (bv 02)',
                'class' => 'forceAutoHint'
            ]
		));
		$financial->addElement('text', 'financial_export_sales_vatlow', [
			'label' => 'BTW laag (excl) 6%',
            'attribs' => [
                'title' => 'Grootboekrekening van BTW laag verkoop in de boekhouding (bv 1510)',
                'class' => 'forceAutoHint'
            ]
        ]);
		$financial->addElement('text', 'financial_export_sales_vatlowcode', [
			'label' => 'BTW Code Laag (excl) 6%',
            'attribs' => [
                'title' => 'Code van BTW laag verkoop in de boekhouding (bv 03)',
                'class' => 'forceAutoHint'
            ]
        ]);

		$financial->addElement('text', 'financial_export_sales_vatlow_new', [
			'label' => 'BTW laag (excl) 9%',
			'attribs' => [
				'title' => 'Grootboekrekening van BTW laag verkoop in de boekhouding (bv 1510)',
				'class' => 'forceAutoHint'
			]
        ]);
		$financial->addElement('text', 'financial_export_sales_vatlowcode_new', [
			'label' => 'BTW Code Laag (excl) 9%',
			'attribs' => [
				'title' => 'Code van BTW laag verkoop in de boekhouding (bv 03)',
				'class' => 'forceAutoHint'
			]
        ]);

		$financial->addElement('text', 'financial_export_sales_vat_seven_percent', array(
			'label' => 'BTW 7% (Duitsland)',
			'attribs' => [
				'title' => 'Grootboekrekening van BTW 7% verkoop in de boekhouding (bv 1510)',
				'class' => 'forceAutoHint'
			]
		));
		$financial->addElement('text', 'financial_export_sales_vat_seven_percent_code', array(
			'label' => 'BTW Code 7% (Duitsland) (excl)',
			'attribs' => [
				'title' => 'Code van BTW 7% verkoop in de boekhouding (bv 03)',
				'class' => 'forceAutoHint'
			]
		));
		// VAT purchase
		$financial->addElement('text', 'financial_export_purchase_vathigh', array(
			'label' => 'BTW hoog inkoop (excl)',
            'attribs' => [
                'title' => 'Grootboekrekening van BTW hoog inkoop in de boekhouding (bv 1530)',
                'class' => 'forceAutoHint'
            ]
		));
		$financial->addElement('text', 'financial_export_purchase_vathighcode', array(
			'label' => 'BTW Code hoog inkoop (excl)',
		));

		$financial->addElement('text', 'financial_export_purchase_vatlow', [
			'label' => 'BTW laag inkoop 6%',
        ]);
		$financial->addElement('text', 'financial_export_purchase_vatlowcode', [
			'label' => 'BTW Code laag inkoop (excl) 6%',
        ]);
		$financial->addElement('text', 'financial_export_purchase_vatlow_new', [
			'label' => 'BTW laag inkoop 9%',
		]);
		$financial->addElement('text', 'financial_export_purchase_vatlowcode_new', [
			'label' => 'BTW Code laag inkoop (excl) 9%',
		]);
		$financial->addElement('text', 'financial_export_purchase_vatnon', array(
			'label' => 'BTW 0% inkoop',
		));
		$financial->addElement('text', 'financial_export_purchase_vatnoncode', array(
			'label' => 'BTW Code 0% inkoop (excl)',
		));

		$financial->addElement('text', 'financial_export_purchase_vatcoderentoverride', array(
			'label' => 'BTW Code huurcomponenten',
		));

$financial->addElement('select', 'financial_export_cost_carrier_input', [
    'label' => 'Kostendrager invoer',
    'multiOptions' => [
        'disabled' => 'Uitgeschakeld',
        'componentCode' => 'Als code op de componenten',
        'object' => 'Bij de objectdetails',
    ],
    'attribs' => [
        'title' => 'De kostendragers kunnen op de componenten of bij de objecten ingevoerd worden. Wordt tot dusver enkel uitgewisseld naar AFAS en Exact Online.',
        'class' => 'forceAutoHint'
    ]
]);

        $financial->addElement('checkbox', 'financial_export_afas_remote_debtor_management', [
            'label' => 'AFAS debiteuren leading',
            'attribs' => [
                'title' => 'MAATWERK DE BAZAAR NIET ZOMAAR AANZETTEN Met deze instelling ingeschakeld zullen de debiteuren en AFAS ingevoerd worden en gesynced naar Omniboxx, ipv andersom',
                'class' => 'forceAutoHint'
            ]
        ]);


		$financial->addElement('text', 'financial_export_administration_charge', array(
			'label' => 'Admninistratie vergoeding grootboek',
            'attribs' => [
                'title' => 'Indien u automatische administratie vergoeding aanzet in omniboxx hier het grootboek invullen voor deze opbrengsten',
                'class' => 'forceAutoHint'
            ]
		));

		$financial->addElement('text', 'financial_export_depositcode', array(
			'label' => 'Grootboek waarborg',
		));
		$financial->addElement('text', 'financial_export_investorprofit_code', array(
			'label' => 'Grootboek omzet beleggerprovisie',
		));
		$financial->addElement('text', 'financial_export_investorprofit_cost_center', array(
			'label' => 'Kostenplaats omzet beleggerprovisie',
		));




		$financial->addElement('text', 'financial_export_investor_no_payout', array(
			'label' => 'grootboekcode van de tussenrekening voor gladlopen doorstort',
			'attribs' => [
				'title' => 'Indien huur voor derden ingelezen wordt in finan. administratie op tussenrekening,  deze code vullen',
				'class' => 'forceAutoHint'
			]
		));


		$financial->addElement('text', 'financial_export_rental_code', array(
			'label' => 'Grootboek omzet huur (tijdelijke oplossing)',
		));





		$financial->addElement('text', 'financial_export_fromdate', array(
			'label' => 'Factuurdatum vanaf waarvan facturen ingelezen worden',
			'attribs' => array(
				'title' => 'Formaat = yyyy-mm-dd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));

		$financial->addElement('checkbox', 'financial_export_fiscal_period_invoice_date', [
			'label' => 'Boekperiode op factuurdatum',
			'attribs' => [
				'title' => 'Boekperiode op factuurdatum in plaats van vervaldatum bepalen.
					Tot op heden enkel toegepast voor Exact Online en (iig deels) Centric.',
				'class' => 'forceAutoHint',
			]
		]);

        $financial->addElement('text', 'temporary_bypass_financial_echange_security_for_non_production_environment', [
            'label' => 'Tijdelijk beschikbaar maken van de financiële koppeling als het geen productie is',
            'attribs' => [
                'title' => 'Formaat = yyyy-mm-dd. Deze datum moet zich in de toekomst bevinden.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ]
        ]);

	$debtor_overviews = new Form_SubForm($this);
	$debtor_overviews->setAttrib('title', 'Debiteuren overzichten');
	$this->addSubForm($debtor_overviews, 'debtor_overviews');

		$debtor_overviews->addElement('checkbox', 'debtor_overviews_rental_component_only', array(
			'label' => 'Enkel huurcomponent gebruiken'
		));

	$company = new Form_SubForm($this);
	$company->setAttrib('title', 'Bedrijf');
	
	$this->addSubForm($company, 'company');
	
		$company->addElement('text', 'general_company_number_length', array(
			'label' => 'Nummer lengte',
			'attribs' => array(
				'title' => 'Afwijkende lengte voor B.V. nummers t.b.v. klantnummer en factuurnummer, als deze waarde niet ingevoerd is dan is de standaard waarde 2.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)			
		));

		$company->addElement('text', 'general_company', array(
			'label' => 'Bedrijfsnaam',
		));
		
		$company->addElement('text', 'general_company_shortname', array(
			'label' => 'Korte bedrijfsnaam (voor naam van template folders e.d.)',
		));
		
		$company->addElement('text', 'general_phone', array(
			'label' => 'Telefoonnummer',
		));
		
		$company->addElement('text', 'general_fax', array(
			'label' => 'Fax',
		));
		
		$company->addElement('text', 'general_email', array(
			'label' => 'E-mail',
		));
		
		$company->addElement('text', 'general_website', array(
			'label' => 'Alg. Website',
		));
		
		$company->addElement('text', 'general_website_en', array(
			'label' => 'Alg. Website (engelstalig)',
		));
		
		$company->addElement('text', 'portal_website', array(
			'label' => 'Portal Website',
		));

		$company->addElement('checkbox', 'portal_website_ssl', array(
			'label' => 'Portal Website HTTPS',
		));

		$company->addElement( 'checkbox', 'portal_website_not_omniboxx_root', [
			'label'   => 'Portal Website geen Omniboxx root',
			'attribs' => [
				'title'      => 'De opgegeven url is niet de root url van de Omniboxx
					installatie. I.i.g. gebruikt om urls van logo plaatjes in emails
					niet op de portal website url te baseren (wat dan niet werkt).',
				'class'      => 'forceAutoHint',
			],
		] );
		
		$company->addElement('text', 'general_address_street', array(
			'label' => 'Straat',
		));
		
		$company->addElement('text', 'general_address_number', array(
			'label' => 'Nummer',
		));
		
		$company->addElement('text', 'general_address_zipcode', array(
			'label' => 'Postcode',
		));
		
		$company->addElement('text', 'general_address_city', array(
			'label' => 'Stad',
		));

		$purchaseinvoices = new Form_SubForm($this);
		$purchaseinvoices->setAttrib('title', 'Inkoopfacturen');

		$this->addSubForm($purchaseinvoices, 'purchaseinvoices');

			$purchaseinvoices->addElement('checkbox', 'financial_import_third_party_purchase_invoices', [
			'label' => 'Inkoop facturen importeren derde partijen',
			'attribs' => [
				'title' => 'Het importeren van service kosten gerelateerde inkoopfacturen uit financiële pakketten. Op dit moment is dit mogelijk voor: "Exact Online, Twinfield"',
				'class' => 'forceAutoHint'
			]
		]);

		$purchaseinvoices->addElement('checkbox', 'import_third_party_purchase_invoices_service_cost_filter', [
			'label' => 'Inkoop facturen importeren -> service kosten filter',
			'attribs' => [
				'title' => 'Het filteren op service kosten gerelateerde inkoop facuturen.',
				'class' => 'forceAutoHint'
			]
		]);

		$purchaseinvoices->addElement('checkbox', 'import_third_party_purchase_invoices_persist_send_attachment', [
			'label' => 'Inkoop facturen importeren -> Standaard inhouden beleggersuitkering aan',
			'attribs' => [
				'title' => 'Zet voor iedere geimporteerde inkoop factuur standaard het vinkje: "Inhouden beleggersuitkering" aan.',
				'class' => 'forceAutoHint'
			]
		]);


$purchaseinvoices->addElement('checkbox', 'second_validation_employee_purchase_invoices', [
    'label' => 'Tweede medewerker voor validatie inkoopfacturen',

]);







	$post = new Form_SubForm($this);
	$post->setAttrib('title', 'Post adres');
	
	$this->addSubForm($post, 'post');
		
		$post->addElement('text', 'general_post_address_street', array(
			'label' => 'Straat',
		));
		
		$post->addElement('text', 'general_post_address_number', array(
			'label' => 'Nummer',
		));
		
		$post->addElement('text', 'general_post_address_zipcode', array(
			'label' => 'Postcode',
		));
		
		$post->addElement('text', 'general_post_address_city', array(
			'label' => 'Stad',
		));
		
	
	$labels = new Form_SubForm($this);
	$labels->setAttrib('title', 'Labels');
	
	$this->addSubForm($labels, 'labels');

		$labels->addElement('text', 'resident_label', array(
			'label' => 'Label bewoner',
		));

		$labels->addElement('text', 'lot_label', array(
			'label' => 'Label kavelnummer',
		));

		$labels->addElement('checkbox', 'lot_label_on_invoice', array(
			'label' => 'Kavelnummer op factuur',
		));
		
	$externalform = new Form_SubForm($this);
	$externalform->setAttrib('title', 'Tekst external support mail');
	
	$this->addSubForm($externalform, 'externalform');
		
		$externalform->addElement('textarea', 'extra_info_external_support', array(
			'label' => 'Tekst',
			'rows' => 5,
			'cols' => 80,
		));

		
			
	
	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Financieel');
	$this->addSubForm($financial, 'financieel');
		
		$financial->addElement('checkbox', 'object_ledger_enabled', array(
			'label' => 'Grootboek per object',
		));
		
	$financial = new Form_SubForm($this);
	$financial->setAttrib('title', 'Meterstanden import');
	$this->addSubForm($financial, 'financieel');
		
		$financial->addElement('text', 'meterimport_report_address', [
			'label' => 'Raportage sturen',
			'attribs' => [
				'title' => 'Zet hier het e-mail adres waar de meterimport rapportage heen gestuurd moet worden.',
				'class' => 'forceAutoHint',
			]
		]);




	

		
	$email = new Form_SubForm($this);
	$email->setAttrib('title', 'Email');
	$this->addSubForm($email, 'email');

		$email->addElement('checkbox', 'email_process_queue_enabled', [
			'label' => 'Uitgaande E-mail verwerking ingeschakeld',
		]);
		$email->addElement('checkbox', 'email_new_interface_dashboard', [
			'label' => 'Nieuwe e-mail interface',
            'attribs' => [
                'title' => 'Met deze optie verdwijnt de mailbox van het standaard dashboard en wordt alleen nieuwe mail interface gebruikt',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ],
		]);

	$email->addElement('checkbox', 'email_copy_as_to_email', [
		'label' => 'Kopie mail adres bij particuliere huurders niet als BCC maar als aparte ontvanger maken.',
		'attribs' => [
			'title' => 'Deze mail wordt ook op dossier en portal gekoppeld (mail voor factuur staat er dan 2x). De kopie ontvanger kan dan niet zien naar wie deze oorspronkelijk is gestuurd.
						Deze optie is gemaakt voor kopie mail voor bv bewindvoerder zonder dat huurder een mail adres heeft.',
			'class' => 'forceAutoHint',
		]
	]);
		
		$email->addElement('text', 'email_bcc_to_archive', array(
			'label' => 'Uitgaande E-mail BCC naar adres',
		));

		$email->addElement('checkbox', 'email_use_neutral_closure_fallback', [
			'label' => 'Neutrale email ondertekening',
			'attribs' => [
				'title' => 'Gebruik een neutrale, minimale ondertekening bij emails wanneer ondertekening 
				instellingen (zoals op de Juridische Entiteit) niet bekend of te bepalen zijn. Deze ondertekening bevat 
				de "Bedrijfsnaam" instelling NIET.',
				'class' => 'forceAutoHint',
			]
		]);


$email->addElement('checkbox', 'email_hide_matched_on_portal', [
    'label' => 'Gematchde E-mail berichten verbergen op de huurder portal',
]);

// Temporary disabled untill procedure is established
//    $email->addElement('checkbox', 'email_import_mark_as_imported_by_deleting', [
//        'label' => 'Binnenkomende email markeren als geimporteerd door bericht te verwijderen',
//    ]);
		
	$ratesheet = new Form_SubForm($this);
	$ratesheet->setAttrib('title', 'Tariefblad');
	$this->addSubForm($ratesheet, 'ratesheet');
		
		$ratesheet->addElement('checkbox', 'ratesheet_point_enabled', array(
			'label' => 'Huurpunten in tariefblad',
		));

		$ratesheet->addElement('checkbox', 'ratesheet_amounts_enabled', array(
			'label' => 'Bedragen in tariefblad',
		));

		$ratesheet->addElement('checkbox', 'ratesheet_day_enabled', array(
			'label' => 'Vastrecht dagen',
		));	

		$ratesheet->addElement('checkbox', 'ratesheet_kwh_enabled', array(
			'label' => 'Variabel kwh',
		));	
		
		$ratesheet->addElement('checkbox', 'ratesheet_point_round_amount', array(
			'label' => 'Huurpunten-bedrag afronden',
		));	
		




        $customization = new Form_SubForm($this);
        $customization->setAttrib('title', 'Maatwerk');
        $this->addSubForm($customization, 'customization');

        $customization->addElement('checkbox', 'ONS_interface_enabled', [
            'label' => 'ONS koppeling',
            'attribs' => [
				'title' => 'ONS koppeling (Klant De Tussenvoorziening). Haalt gegevens nieuwe huurders uit ONS op en voegt ONS nummer en ONS id aan diverse huurderoverzichten toe',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			]

        ]);

        $customization->addElement('checkbox', 'allow_collection_no_iban', [
            'label' => 'Incasso aanzetten bij handmatige factuur indien huurder geen iban heeft',
            'attribs' => [
                'title' => 'Specifiek maatwerk voor Wolf Huisvesting',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);

        $customization->addElement('checkbox', 'extended_realEstate_overview', [
            'label' => 'Extra vastgoedoverzicht',
            'attribs' => [
                'title' => 'Oorspronkelijk gemaakt voor REND. Daarom onder instelling, kolommen en volgorde zijn volgens mij van belang voor een gekoppelde tool. Alleen extra aanzetten en kolommen erna toevoegen voor andere klanten',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);

        $customization->addElement('checkbox', 'RIVA_sales_finalized_custom_overview', [
            'label' => 'Apart overzicht voor verzonden verkoopfacturen voor Riva',
            'attribs' => [
                'title' => 'invoice_sales_overview/finalized-list is aangepast voor deze klant',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);

        $customization->addElement('checkbox', 'payq_export_outstanding_invoices', [
            'label' => 'Apart overzicht naar excel voor openstaande posten tbv PAYQ',
            'attribs' => [
                'title' => 'report/payq-report/ wordt toegevoegd aan het menu financieel',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);
        $customization->addElement('checkbox', 'pom_export_outstanding_invoices', [
            'label' => 'Apart overzicht naar excel voor openstaande posten tbv POM',
            'attribs' => [
                'title' => 'report/pom-report/ wordt toegevoegd aan het menu financieel',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);

        $customization->addElement('text', 'from_date_pom_export_outstanding_invoices', [
            'label' => 'Datum vanaf voor openstaande posten tbv POM',
            'attribs' => [
                'title' => 'Factuurdatum vanaf welke in dit overzicht opgenomen worden',
                'class' => 'DatePicker',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);


        $customization->addElement('checkbox', 'ZS_use_project_mail_for_external_support_complaint_inform', [
            'label' => 'Bij uitzetten melding, niet huurder maar project mail adres opnemen als mail adres (Ziengs Scapino)',

        ]);

        $customization->addElement('checkbox', 'bridges_extra_notice_payment_instruction_mail_external', [
            'label' => 'Extra zin betalingskenmerk mail technische opdracht',
            'attribs' => [
                'title' => 'In de mail naar technische partijen wordt een extra zin toegevoegd, bestaand uit: meldingnr - code bij component ingesteld en kostenplaats',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);

        $customization->addElement('text', 'website_xml_object_prefix', [
            'label' => 'Voorloop nummer interhouse website koppeling',
            'attribs' => [
                'title' => 'ONS koppeling (Klant De Tussenvoorziening). Haalt gegevens nieuwe huurders uit ONS op en voegt ONS nummer en ONS id aan diverse huurderoverzichten toe',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]

        ]);


$remind_private_penalty = new Form_SubForm($this);
$remind_private_penalty->setAttrib('title', 'Particuliere boetes bij herinneringen & aanmaningen');
$this->addSubForm($remind_private_penalty, 'remind_private_penalty');

$remind_private_penalty->addElement('select', 'remind_private_penalty_method', [
    'label' => 'Methode',
    'multiOptions' => [
        'wik' => 'WIK',
        'fixed_per_reminder' => 'Vast bedrag per herinnering'
    ],
]);

$remind_private_penalty->addElement('text', 'percentage_costs_first_formalnotice', array(
    'label' => 'Percentage wettelijke kosten',
    'placeholder' => 100
));

if(Settings::get('reminder_inform_enabled'))
    $remind_private_penalty->addElement('text', 'remind_private_penalty_amount_level_1', [
        'label' => 'Boete vooraankondiging'
    ]);

$remind_private_penalty->addElement('text', 'remind_private_penalty_amount_level_2', [
    'label' => 'Boete ' . lcfirst(Settings::get('label_reminder') ?: 'herinnering')
]);

$remind_private_penalty->addElement('text', 'label_collection_agency_inform', [
    'label' => 'Boete ' . lcfirst(Settings::get('label_reminder') ?: 'aanmaning')
]);

	$investor_provision = new Form_SubForm($this);
	$investor_provision->setAttrib('title', 'Beheerfacturen');
	$this->addSubForm($investor_provision, 'investor_provision');

		$investor_provision->addElement('select', 'investor_provision_period_date', array(
			'label' => 'Beheerfacturen periode',
			'multiOptions' => array(
				'payed' => 'Betaaldatum',
				'invoice' => 'Factuurdatum'
			)
		));

		$investor_provision->addElement('checkbox', 'investor_provision_hide_non_payout_components', [
			'label' => 'Type afrekening "provisie" -> componenten zonder doorstort verbergen',
        ]);

		$investor_provision->addElement('text', 'investor_provision_send_day', array(
			'label' => 'Gewenste dag aanmaken beheerfacturen',
		));

        $investor_provision->addElement('text', 'investor_expire_days', array(
            'label' => 'Betaaltermijn (dagen) factuur beheervergoeding',
        ));

		$investor_provision->addElement('checkbox', 'investor_provision_quarterly', array(
			'label' => 'Kwartaal afrekeningen',
		));

		$investor_provision->addElement('checkbox', 'investor_provision_payout_vat_specification', [
			'label' => 'Uitgespitste BTW huur doorstort beleggerafrekening',
            'attribs' => [
                'title' => 'Deze instelling zorgt ervoor dat de doorgestorte BTW uitgesplitst en gespecificeerd wordt bij de doorstort',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}",
            ]
        ]);

$investor_provision->addElement('checkbox', 'investor_provision_always_show_current_tenant', [
    'label' => 'Altijd de huidige huurder weergeven',
    'attribs' => [
        'title' => 'Deze instelling zorgt ervoor dat altijd de huidige huurder wordt weergegeven op de afrekening, ook wanneer deze geen factuur heeft ontvangen of betalingsachterstand heeft',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -0, 'y': 0}",
    ]
]);

        $investor_provision->addElement('checkbox', 'investor_provision_address_truncate_in_middle', [
            'label' => 'Adres afkappen in midden',
            'attribs' => [
                'title' => 'Dus bijvoorbeeld: "Langestr..23A" ipv "Langestraat 2.."',
                'class' => 'forceAutoHint',
            ]
        ]);

		$investor_provision->addElement('checkbox', 'investor_provision_period_description_end_date', array(
			'label' => 'Maand aanduiding beheerfactuur op einddatum',
		));

		$investor_provision->addElement('text', 'investor_provision_expire_days', array(
			'label' => 'Beheerfactuur aantal dagen tot uitvoerdatum'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_second_investor', array(
			'label' => 'Invoer 2e belegger ingeschakeld'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_invoice_provision_only', array(
			'label' => 'Factuur enkel provisie weergeven',
            'attribs' => [
                'title' => 'Met deze instelling wordt enkel de beheervergoeding (geen huuropbrengst en kosten) op de factuur van de beheervergoeding weergegeven.',
                'class' => 'forceAutoHint',
            ]
		));

        $investor_provision->addElement('checkbox', 'investor_provision_invoice_substract_notification', [
            'label' => 'Factuur aangepaste zin inhouden doorstort',
            'attribs' => [
                'title' => 'LET OP; TIJDELIJK!, ENKEL VOOR GEVAERT! Zorgt ervoor dat er op de factuur komt te staan dat het bedrag wordt verrekend.',
                'class' => 'forceAutoHint',
            ]
        ]);

		$investor_provision->addElement('checkbox', 'investor_provision_invoice_provision_export_only', [
			'label' => 'Factuur enkel provisie uitwisselen'
		]);

		$investor_provision->addElement('checkbox', 'investor_provision_hide_components', array(
			'label' => 'Verberg huurder naam, periode en componenten specificatie op de "Betalingsoverzicht" pagina(s).'
		));

		$investor_provision->addElement('checkbox', 'investor_payment_problems_till_period', array(
			'label' => 'Huurder achterstand begrenzen tot einde periode'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_based_on_all_payed', array(
			'label' => 'Provisie op basis van facturatie bedragen',
			'attribs' => array(
				'title' => 'Door deze optie wordt de provisie niet berekend op basis van de inkomsten, maar op basis van de facturatie bedragen.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		));

		$investor_provision->addElement('checkbox', 'investor_provision_no_tax', [
			'label' => 'Geen BTW berekenen over provisie',
		]);

		$investor_provision->addElement('checkbox', 'investor_provision_all_payed', array(
			'label' => 'Uitkering op basis van facturatie bedragen',
			'attribs' => array(
				'title' => 'Door deze optie worden de beleggersuitkeringen niet berekend op basis van de inkomsten, maar op basis van de facturatie bedragen.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		));

		$investor_provision->addElement('checkbox', 'investor_provision_only_recent_unpayed', array(
			'label' => 'Doorstort voorschieten bij beperkte achterstand (BETA!)',
			'attribs' => array(
				'title' => 'Deze setting maakt de optie beschikbaar om dit per belegger in te stellen. Deze functie is niet gereed voor productie en slechts voor enkele klanten ingeschakeld die hiervan op de hoogte zijn.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		));


		$investor_provision->addElement('checkbox', 'investor_provision_add_penalty_payment', array(
			'label' => 'Boete betalingen doorstorten'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_payout_financial_specification', array(
			'label' => 'Doorstort specificatie financiele koppeling'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_overview_period_shifted_in_original_period', array(
			'label' => 'Beheerinkomsten overzicht doorgeschoven',
			'attribs' => array(
				'title' => 'Beheerinkomsten overzicht, doorgeschoven bedragen in uitkering-periode',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			)
		));

		$investor_provision->addElement('checkbox', 'investor_provision_overview_based_on_concept_invoices', array(
			'label' => 'Beheerinkomsten overzicht op theoretische huur',
			'attribs' => [
				'title' => 'Beheerinkomsten overzicht berekenen mbv totaalbedragen (concept) factuurruns',
				'class' => 'forceAutoHint'
			]
		));

		$investor_provision->addElement('checkbox', 'investor_provision_administration_charge', array(
			'label' => 'Administratie vergoeding inschakelen'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_grouped_provision', array(
			'label' => 'Gegroepeerde provisie inschakelen'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_grouped_costs', array(
			'label' => 'Gegroepeerde kosten inschakelen'
		));

		$investor_provision->addElement('checkbox', 'investor_provision_show_balance_warnings', [
			'label' => 'Saldo meldingen inschakelen',
			'attribs' => [
				'title' => 'Bij de controle lijst voor de afrekeningen een waarschuwing weergeven wanneer het saldo verloop incorrect is.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			]
		]);

		$investor_provision->addElement('checkbox', 'investor_provision_filter_project', [
			'label' => 'Uitkering per project',
			'attribs' => [
				'title' => 'Voor een belegger die aan meerdere projecten gekoppeld is een uitkering maken per project. Werkt (nog) niet samen met de instelling "Provisie op basis van facturatie bedragen"',
				'class' => 'forceAutoHint',
			]
		]);


        $investor_provision->addElement('checkbox', 'investor_provision_multi_object_multi_investor_support', [
            'label' => 'Ondersteuning voor verzamelfacturen met objecten van verschillende beleggers',
            'attribs' => [
                'title' => '<b>BETA functionaliteit</b>: deze instelling wordt tot nu toe getest door een kleine selectie klanten.<br /><br />Deze instelling zorgt voor betere afhandeling van verzamelfacturen van huurders die objecten huren van verschillende beleggers in Omniboxx.',
                'class' => 'forceAutoHint',
            ]
        ]);

        $investor_provision->addElement('checkbox', 'investor_provision_create_provision_collection', [
            'label' => 'Maak provisie overboeking bij derden rekening',
            'attribs' => [
                'title' => 'Maak tevens een overboekingopdracht voor de beheer provisie wanneer de huur ontvangen wordt op een derden rekening',
                'class' => 'forceAutoHint',
            ]
        ]);

        $investor_provision->addElement('checkbox', 'investor_provision_disable_pdf_merge', [
            'label' => 'Toevoegen van inkoopfacturen uitschakelen',
            'attribs' => [
                'title' => 'Met deze optie kan het toevoegen van de pdf\'s van inkoopfacturen aan de beleggerafrekening (tijdelijk) uitgeschakeld worden.',
                'class' => 'forceAutoHint',
            ]
        ]);


$investor_provision->addElement('checkbox', 'investor_provision_preview_triggers', [
    'label' => 'Automatisch bijwerken van afrekening previews',
    'attribs' => [
        'title' => 'Met deze optie worden previews van de beleggerafrekeningen automatisch bijgewerkt na aanleiding van diverse triggers binnen het systeem.',
        'class' => 'forceAutoHint',
    ]
]);




	$tenant = new Form_SubForm($this);
	$tenant->setAttrib('title', 'Bewoners/Objecten');
	$this->addSubForm($tenant, 'tenant');
	
		
		$tenant->addElement('checkbox', 'user_register_current_uo', array(
			'label' => 'Registratie unieke code neemt huidige sitatie over',
		));	
			
		$tenant->addElement('checkbox', 'object_deposit_enabled', array(
			'label' => 'Waarborg ingeschakeld',
		));

		$tenant->addElement('checkbox', 'extra_tentant_invoice_options', array(
			'label' => '1e koper/huurder en factureren aan ingeschakeld',
		));

$tenant->addElement('checkbox', 'object_woz_enabled', array(
			'label' => 'WOZ ingeschakeld',
		));	
		$tenant->addElement('checkbox', 'object_ratesheet_override_enabled', array(
			'label' => 'Overschrijven tariefbladwaarden',
		));	
			
		$tenant->addElement('checkbox', 'project_first_enddate', array(
			'label' => 'Einddatum eerste periode',
		));

		$tenant->addElement('checkbox', 'is_object_default_inactive', [
			'label' => 'Markeer nieuw object standaard als inactief',
		]);

		$tenant->addElement('checkbox', 'default_object_inactive_reason', [
			'label' => 'Gebruik standaard inactief reden',
		]);

	$sms = new Form_SubForm($this);
	$sms->setAttrib('title', 'SMS');
	$this->addSubForm($sms, 'sms');
	
		$sms->addElement('checkbox', 'sms_enabled', array(
			'label' => 'Sms ingeschakeld',
			'attribs' => array(
				'autocomplete' => 'off',
				'title' => 'Belangrijk! Onderstaande inloggegevens moeten niet de gegevens zijn waarmee de klant inlogt 
					op messagebird.com . Om te kunnen smsen moet er bij Messagebird via het menu naar "Developers" -> 
					"API-toegang (oud)" gegaan worden. Daar moet dan een (nieuw) wachtwoord gekozen worden, en het is 
					dat wachtwoord + de gebruikersnaam die Messagebird zelf invult die hieronder moeten staan. Zie evt. 
					de url hieronder in "Inlog toelichting" voor details.<br>
					Anders geeft de API altijd "Wrong password" terug.',
				'class' => 'forceAutoHint',
			)
		));

		$sms->addElement('text', 'sms_password_explain', [
			'label' => 'Inlog toelichting',
			'value' => 'https://support.messagebird.com/hc/en-us/articles/200787661',
			'attribs' => [
				'readonly' => 'readonly',
			],
		]);

		$sms->addElement('checkbox', 'sms_invoice_reminder', [
			'label' => 'Sms betalingsherinnering of aanmaning sturen',
			'attribs' => [
				'title' => 'Bij het versturen van de betalingsherinneringen en aanmaningen wordt naast de 
							gebruikelijke verzend methode ook een sms bericht verstuurd.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}",
			]
		]);
		
		$sms->addElement('text', 'sms_access_key', [
			'label' => 'MessageBird Access Key',
			'attribs' => [
				'title' => 'Zie MessageBird handleiding voor het aanmaken van een access key.',
				'class' => 'forceAutoHint',
				'autocomplete' => 'off',
			]
		]);

		$sms->addElement('text', 'sms_originator', [
			'label' => 'Afzender',
			'attribs' => [
				'title' => 'LET OP: dit veld mag maximaal 11 tekens bevatten',
				'class' => 'forceAutoHint',
			]
		]);






$mplus = new Form_SubForm($this);
	$mplus->setAttrib('title', 'MPlus');
	$this->addSubForm($mplus, 'mplus');
	
		$mplus->addElement('checkbox', 'mplus_enabled', array(
			'label' => 'Koppeling ingeschakeld',
		));	
		
		$mplus->addElement('text', 'mplus_article_code_vat_high', array(
			'label' => 'Artikel # BTW Hoog',
		));	

		$mplus->addElement('text', 'mplus_article_code_vat_low', array(
			'label' => 'Artikel # BTW Laag',
		));	

		$mplus->addElement('text', 'mplus_article_code_vat_none', array(
			'label' => 'Artikel # Geen BTW',
		));	


		$mplus->addElement('text', 'mplus_api_server', array(
			'label' => 'API server',
		));	

		$mplus->addElement('text', 'mplus_api_port', array(
			'label' => 'API port',
		));	

		$mplus->addElement('text', 'mplus_api_fingerprint', array(
			'label' => 'API certificate fingerprint',
		));	

		$mplus->addElement('text', 'mplus_api_ident', array(
			'label' => 'API ident',
		));	

		$mplus->addElement('text', 'mplus_api_secret', array(
			'label' => 'API secret',
		));	
	
		$mplus->addElement('select', 'mplus_default_employee', array(
			'label' => 'Standaard medewerker',
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
			'attribs' => array(
				'title' => 'Wanneer er geen geldige medewerker ingesteld is tijdens het verzenden van facturen, gebruik dan deze gebruiker.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)				
		));	



	$task = new Form_SubForm($this);
	$task->setAttrib('title', 'Taken');
	$this->addSubForm($task, 'task');

		$task->addElement('checkbox', 'task_mail_assigned', array(
			'label' => 'Melding nieuwe taak',
		));

		$task->addElement('checkbox', 'task_mail_completed', array(
			'label' => 'Melding taak afgerond',
		));

		$task->addElement('checkbox', 'task_mail_dayly_overview', array(
			'label' => 'Dagoverzicht afgeronde taken',
		));

		$task->addElement('checkbox', 'task_mail_weekly_personal', array(
			'label' => 'Persoonlijk wekelijks overzicht',
		));

		$task->addElement('checkbox', 'task_mail_weekly_overview', array(
			'label' => 'Weekoverzicht afgeronde taken',
		));

		$task->addElement('select', 'task_mail_weekly_management_overview', array(
			'label' => 'Weekoverzicht management',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));
				
	$ideal = new Form_SubForm($this);
	$ideal->setAttrib('title', 'iDeal legacy');
	$this->addSubForm($ideal, 'ideal');	
	
		$ideal->addElement('checkbox', 'ideal_enabled', array(
			'label' => 'iDeal ingeschakeld',
		));	

		$ideal->addElement('checkbox', 'ideal_ip_restricted_enabled', array(
			'label' => 'IP-restrictie (voor tijdens test)',
		));	

		$ideal->addElement('text', 'ideal_ip_restricted', array(
			'label' => 'IP toegestaan',
		));	

		$ideal->addElement('checkbox', 'ideal_test_mode', array(
			'label' => 'Test modus',
		));	

		$ideal->addElement('select', 'ideal_bank', array(
			'label' => 'Bank - iDeal variant',
			'multiOptions' => array(
				// any new bank types should also be added in /aplication/forms/corporation.php
				'abn' => 'ABN-AMRO - Only Kassa'
			)
		));	

		$ideal->addElement('text', 'ideal_id', array(
			'label' => 'Identifier (PSPID)',
		));	

		$ideal->addElement('text', 'ideal_secret', array(
			'label' => 'Secret',
		));	

		global $config;

		$ideal->addElement('text', 'ideal_url', array(
			'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal/',
			'label' => 'URL formulier',
			'ignored' => true,
			'disabled' => true,
			'style' => 'width:330px'
		));	

		$ideal->addElement('text', 'ideal_sha', array(
			'value' => 'SHA-1',
			'label' => 'Hash-algoritme',
			'ignored' => true,
			'disabled' => true,
		));		

		foreach(array('cancel', 'accept', 'descline', 'exception', 'cancel') as $page)
			$ideal->addElement('text', 'ideal_' . $page . '_url', array(
				'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal/page/' . $page . '/',
				'label' => 'URL ' . $page,
				'ignored' => true,
				'disabled' => true,
			'style' => 'width:330px'
			));			

		$ideal->addElement('text', 'ideal_post_payment', array(
			'value' => ($config->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl . 'transaction/ideal-payment/page/accept/',
			'label' => 'Post-payment',
			'ignored' => true,
			'disabled' => true,
			'style' => 'width:330px'
		));

		$idealNew = new Form_SubForm($this);
		$idealNew->setAttrib('title', 'iDeal Mollie');
		$this->addSubForm($idealNew, 'idealNew');

		$idealNew->addElement('checkbox', 'mollie_ideal_enabled', [
			'label' => 'Mollie iDeal ingeschakeld',
		]);

		$idealNew->addElement('text', 'mollie_ideal_api_key', [
			'label' => 'Mollie API-sleutel',
		]);

$bankimport = new Form_SubForm($this);
	$bankimport->setAttrib('title', 'Bank import');
	$this->addSubForm($bankimport, 'bankimport');	
	
		$bankimport->addElement('checkbox', 'bankimport_livedrive_enabled', array(
			'label' => 'LiveDrive ingeschakeld',
		));	

		$bankimport->addElement('text', 'bankimport_livedrive_accountname', array(
			'label' => 'LiveDrive account naam',
		));	
	
		$bankimport->addElement('text', 'bankimport_livedrive_username', array(
			'label' => 'LiveDrive share gebruikersnaam',
		));	

		$bankimport->addElement('text', 'bankimport_livedrive_password', array(
			'label' => 'LiveDrive share wachtwoord',
			'attribs' => array('autocomplete' => 'off')
		));	
		
		$bankimport->addElement('select', 'bankimport_livedrive_parser', array(
			'label' => 'LiveDrive bestandsformaat',
			'multiOptions' => array('filename' => 'Herkennen aan bestandsnaam', 'csv' => 'C.S.V.', 'mt940' => 'MT940', 'swift' => 'SWIFT'),
			'RegisterInArrayValidator' => false,
		));	

		$bankimport->addElement('select', 'bankimport_livedrive_bank', array(
			'label' => 'LiveDrive bank',
			'multiOptions' => array('abn' => 'ABN-AMRO', 'triodos' => 'Triodos'),
			'RegisterInArrayValidator' => false,
		));



		$bankimport->addElement('checkbox', 'bankimport_accountview_xml_upload', array(
			'label' => 'Accountview xml import ingeschakeld',
		));	

		$bankimport->addElement('checkbox', 'bankimport_match_by_account_enabled', array(
			'label' => 'Matchen op bankrekeningnummer ingeschakeld',
		));	

		$bankimport->addElement('checkbox', 'bankimport_match_by_collection_identifier', array(
			'label' => 'Matchen op incasso kenmerk',
		));

		$bankimport->addElement('checkbox', 'bankimport_match_by_rules', [
			'label' => 'Matchen op matchregels',
        ]);

		$bankimport->addElement('checkbox', 'bankimport_report_enabled', array(
			'label' => 'Rapportage ingeschakeld',
		));	
		
		$bankimport->addElement('select', 'bankimport_report_users', array(
			'label' => 'Naar gebruikers',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
		));	

		$bankimport->addElement('text', 'bankimport_csv_parser_columns', [
			'label' => 'CSV import kolommen',
            'title' => 'De inhoud van de csv-kolommen bepalen, bijvoorbeeld: "<b>entryDateString, description, account, bankaccount, code, debetCredit, amount, type, longDescription</b>"',
            'class' => 'forceAutoHint',
        ]);

		$bankimport->addElement('checkbox', 'bankimport_csv_parser_include_first_row', [
			'label' => 'Eerste regel ook importeren',
			'title' => 'Importeer van CSV bankbestanden ook de eerste regel als deze regel geen kolominformatie bevat.',
			'class' => 'forceAutoHint',
		]);


		$bankimport->addElement('checkbox', 'financial_penalty_prioritize_penalty_payment', array(
			'label' => 'Betaling eerst afboeken op boete',
		));

		$bankimport->addElement('checkbox', 'financial_transaction_import_check_bankaccount', array(
			'label' => 'Betalingen matchen bankrekening controle',
			'attribs' => array(
				'title' => 'Bij het matchen van betalingen enkel automatische matching toestaan wanneer de bankrekening van de juridische entiteit overeen komt met de bankrekening waarop de betaling ingelezen is.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': -0, 'y': 0}"
			)
		));


		$bankimport->addElement('checkbox', 'financial_transaction_import_matching_disabled', array(
			'label' => 'Betalingen matchen uitgeschakeld'
		));

		$bankimport->addElement('checkbox', 'penalty_unpayed_not_closed', [
			'label' => 'Factuur niet gesloten bij openstaande boete',
			'attribs' => array(
				'title' => 'Zo lang de boete niet betaald is zal de factuur weergegeven worden bij de openstaande posten lijst',
				'class' => 'forceAutoHint',
			)
]);


	
	$lucidChart = new Form_SubForm($this);
	$lucidChart->setAttrib('title', 'Lucidchart');
	
	$this->addSubForm($lucidChart, 'lucidChart');
	
		$lucidChart->addElement('checkbox', 'lucidchart_in_use', array(
			'label' => 'Lucidchart ingeschakeld',
		));

		$lucidChart->addElement('text', 'lucidchart_username', array(
			'label' => 'Lucidchart account naam',
		));
		
		$lucidChart->addElement('text', 'lucidchart_password', array(
			'label' => 'Lucidchart wachtwoord',
		));

		$lucidChart->addElement('checkbox', 'vat_report', array(
			'label' => 'BTW verhuuroverzicht',
		));


	$inspection = new Form_SubForm($this);
	$inspection->setAttrib('title', 'Inspectie APP');
	$this->addSubForm($inspection, 'inspection');


		$inspection->addElement('checkbox', 'inspection_new_version', [
			'label' => 'App versie 2.0'
        ]);

		$inspection->addElement('text', 'title_first_inspection', [
			'label' => 'Naam begininspectie',
			'placeholder' => 'Begininspectie'
		]);

		$inspection->addElement('text', 'title_interim_inspection', [
			'label' => 'Naam tussentijdse inspectie',
			'placeholder' => 'Tussentijdse inspectie'
		]);

		$inspection->addElement('text', 'title_pre_end_inspection', [
			'label' => 'Naam voorinspectie',
			'placeholder' => 'Voorinspectie'
		]);

		$inspection->addElement('text', 'title_end_inspection', [
			'label' => 'Naam eindinspectie',
			'placeholder' => 'Eindinspectie'
		]);
			

	$voyanta = new Form_SubForm($this);
	$voyanta->setAttrib('title', 'Voyanta');
	$this->addSubForm($voyanta, 'voyanta');
	
		$voyanta->addElement('select', 'voyanta_report_recipients', array(
			'label' => 'Rapporteren aan:',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false
		));	
					

	$internalAlerts = new Form_SubForm($this);
	$internalAlerts->setAttrib('title', 'Interne notificaties');
	$this->addSubForm($internalAlerts, 'internal_alerts');
	
		$internalAlerts->addElement('select', 'alert_setting_changes', [
			'label' => 'Wijzigingen aan deze instellingen rapporteren aan:',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
			'attribs' => [
				'title' => 'Aan deze gebruikers wordt een email notificatie gestuurd bij wijzigingen aan een van deze "Interne notificatie" instellingen.',
				'class' => 'forceAutoHint',
			]
		]);	
	
		$internalAlerts->addElement('select', 'alert_changed_IBAN_JE_investor', [
			'label' => 'IBAN wijzigingen van J.E. en beleggers rapporteren aan:',
			'multiple' => true,
			'multiOptions' => $internal_users,
			'RegisterInArrayValidator' => false,
			'attribs' => [
				'title' => 'Aan deze gebruikers wordt een email notificatie gestuurd bij wijzigingen aan een IBAN van Juridische Entiteiten en beleggers.',
				'class' => 'forceAutoHint',
			]
		]);


	$legacy = new Form_SubForm($this);
	$legacy->setAttrib('title', 'Software (admin instellingen legacy)');
	$this->addSubForm($legacy, 'legacy');

	$legacy->addElement('checkbox', 'invoice_tax_for_private', array(
			'label' => 'BTW specificatie particuliere facturen',
		));

	$legacy->addElement('checkbox', 'invoice_finalnota_disable_costs', array(
			'label' => 'Kosten bij eindafrekening uitschakelen',
		));

    $legacy->addElement('text', 'complaint_type_order', [
        'label' => 'Volgorde van meldings types',
        'title' => 'Hier is met een string als "request,complaint,malfunction" de volgorde van melding types in
                    dropdown elementen te bepalen.',
        'class' => 'forceAutoHint',
    ] );

	$legacy->addElement('text', 'invoice_consumption_estimate_adjust_percentage', array(
			'label' => 'Verbruiksschatting bijstel percentage',
		));

	$legacy->addElement('checkbox', 'invoice_discount_percentage_add_total', array(
		'label' => 'Contract percentage-korting oplopend',
		'attribs' => array(
			'title' => 'Bij meerdere percentages korting op een component wordt het totaalbedrag waarover de korting berekend wordt na iedere korting bijgewerkt.',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		)
	));

	$legacy->addElement('text', 'object_invoice_empty', array(
		'label' => 'Leegstand factuur belegger',
	));

	$legacy->addElement('text', 'invoice_post_amount', array(
		'label' => 'Toeslag verzending per post',
	));

	$legacy->addElement('checkbox', 'financial_tax_foreign_rates_enabled', array(
		'label' => 'Internationale btw tarieven',
	));

	$legacy->addElement('checkbox', 'invoice_show_fourweekly_options', array(
		'label' => 'Toon "4 wekelijks" als facturatie periode.',
	));

	$legacy->addElement('checkbox', 'invoice_fourweekly_is_default_period', [
		'label' => 'Tenzij anders ingesteld is "4 wekelijks" de standaard facturatie periode 
					bij het aanmaken van factuurruns.',
	]);

	$legacy->addElement('checkbox', 'invoice_custom_finalize_methods_enabled', [
		'label' => 'Handmatige facturen -> alternatieve verzendmethoden ingeschakeld',
		'attribs' => [
			'title' => 'LET OP: BETA functionaliteit, niet geschikt voor productie.',
			'class' => 'forceAutoHint',
		]
	]);

	$legacy->addElement('select', 'invoice_xml_export_users', array(
		'label' => 'XML naar gebruikers',
		'multiple' => true,
		'multiOptions' => $internal_users,
		'RegisterInArrayValidator' => false,
	));

	$legacy->addElement('checkbox', 'financial_tax_ten_and_a_half_percent_rate_enabled', [
		'label' => '10,5% BTW tarief',
	]);

	$legacy->addElement('checkbox', 'invoice_reference_enabled', array(
		'label' => 'Betalingskenmerk ingeschakeld',
		'attribs' => [
			'title' => 'Veranderd de tekst op de factuur van "onder vermelding van het debiteur- en factuurnummer" naar "onder vermelding van het betalingskenmerk en factuurnummer" en op handmatige facturen komen er allerlei regels bij ',
			'class' => 'forceAutoHint',
		]
	));

	$legacy->addElement('checkbox', 'invoice_small_layout', array(
		'label' => 'Smallere breedte factuurlayout',
	));



	$legacy->addElement('text', 'invoice_send_day', array(
		'label' => 'Gewenste dag verzending facturen',
	));

	$legacy->addElement('checkbox', 'invoice_show_vat_on_summary', array(
		'label' => 'Toon BTW totaal op invoice summary',
	));


$legacy->addElement('checkbox', 'invoice_custom_show_vat_included', array(
	'label' => 'Toon inclusief BTW op handmatige facturen',
));

$legacy->addElement('checkbox', 'invoice_custom_hide_zero_vat', array(
	'label' => 'Handm. facturen zonder BTW -> geen BTW 0% tonen',
));


$legacy->addElement('checkbox', 'hide_vatblock_invoice', array(
	'label' => 'Verberg BTW Specificatie engelstalige facturen',
));

$legacy->addElement('checkbox', 'invoice_only_total_amount', array(
	'label' => 'Op facturen enkel totaal tonen',
	'attribs' => array(
		'title' => 'De layout aanpassen voor de facturen zodat er geen rijen en BTW weergegeven worden',
		'class' => 'forceAutoHint',
		'hintOffset' => "{'x': -0, 'y': 0}"
	)
));

$calendar = new Form_Subform($this);
$calendar->setAttrib('title', 'Agenda instellingen');
$this->addSubForm($calendar, 'Agenda');

$calendar->addElement('checkbox', 'outlook_calendar_enable', [
    'label' => 'Outlook-koppeling inschakelen',
]);
$calendar->addElement('text', 'outlook_calendar_client_tenanted_id', [
    'label' => 'Azure open ID',
    'attribs' => [
        'title' => 'Afhankelijk wat voor een type tenanted je hebt. Dit kan "commen" of "multi-tenant" zijn. Voor commen stel je "commen" in. Als het een multi-tenant is moet het "tenantedID" in ge stelt worden',
        'class' => 'forceAutoHint',
    ]
]);
$calendar->addElement('text', 'outlook_calendar_client_id', [
    'label' => 'Azure app client ID',
]);
$calendar->addElement('text', 'outlook_calendar_client_secret', [
    'label' => 'Azure app client secret',
]);
$calendar->addElement('text', 'outlook_calendar_redirect_url', [
    'label' => 'Azure app redirect url',
]);


//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Instellingen opslaan'
	));

?>
