<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general');

	$general->addElement('select', 'component', array(
		'label' => 'Component',
		'required' => true,
		'multiOptions' => db()->fetchPairs(db()->select()->from('components', array('id', 'CONCAT_WS("-", group_label, name)'))->order('name'))
	));

	$pr_model = new PricelistsRows();

	$general->addElement('select', 'unit', array(
		'label' => 'Eenheid',
		'required' => true,
		'multiOptions' => $pr_model->unit_labels
	));	

	$general->addElement('select', 'object_type', array(
		'label' => 'Object type',
		'required' => true,
		'multiOptions' => db()->fetchPairs(db()->select()->from('object_type', array('id', 'name'))->order('name'))
	));	

	$general->addElement('numberInput', 'price', array(
		'label' 		=> 	'Prijs',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Prijs excl. BTW',
			'class' => 'forceAutoHint number',
			'hintOffset' => "{'x': -30, 'y': 0}",
			'decimals' => 4
		)
	));	

	$general->addElement('numberInput', 'price_discount', array(
		'label' 		=> 	'Dalprijs',
		'validators' 	=> 	array(),
		'required' 		=>	true,
		'attribs' => array(
			'title' => 'Dalprijs excl. BTW',
			'class' => 'forceAutoHint number',
			'hintOffset' => "{'x': -30, 'y': 0}",
			'decimals' => 4
		)
	));	

//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Opslaan'
));