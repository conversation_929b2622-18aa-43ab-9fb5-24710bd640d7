<?
$model = new Inspection();
$u_lib = new User();

$user_name = false;
if (0 < $this->getOwner()->view->data['user']) {
    $user_name = $u_lib->buildname(false, $this->getOwner()->view->data['user']);
}

//data form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form')
    ->setAttrib('fullwidth', true);

$data = new Form_SubForm($this);
$data->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($data, 'data');


if (Settings::get('inspection_new_version')) {
    $data->addElement('select', 'user_object_or_project', [
        'label' => 'Toevoegen voor: ',
        'multiOptions' => [
            'user_object' => 'Zoeken op huurder of adres',
            'project' => 'Alle objecten binnen project'
        ]
    ]);
}

$user_object_search_required = $_POST ? $_POST['data']['user_object_or_project'] != 'project' : true;



$data->addElement('text', 'users_objects_id_search', [
    'label' => 'Zoeken',
    'placeholder' => 'Zoek een huurder of adres',
    'style' => 'width:350px',
    'autocompleter' => 'inspection/find-new/',
    'value' => $user_name,
    'required' => $user_object_search_required,
    'attribs' => ['autocompleter_options' => json_encode(['hiddenElementId' => 'data-users_objects_id'])]
]);


if (Settings::get('inspection_new_version')) {
    $project_select = db()->select()
        ->from('projects', ['id', 'name'])
        ->order('name')
        ->where('exploitation = ?', true);

    $projects = db()->fetchPairs($project_select);

    $data->addElement('select', 'project', [
        'label' => 'Project',
        'attribs' => [
            'title' => 'Let op: ALLEEN een project kiezen indien u voor alle woningen in dit project in een keer een inspectie wilt toevoegen. Voor enkelvoudige inspecties dient u een huurder uit de zoekoptie hierboven te selecteren',
            'class' => 'forceAutoHint',
        ],
        'multiOptions' => [0 => 'Geen project'] + $projects
    ]);

    $data->addElement('select', 'user_object_current_or_next', [
        'label' => 'Toevoegen voor: ',
        'multiOptions' => [
            'current' => 'Huidige huurder',
            'next' => 'Toekomstige huurder'
        ]
    ]);



}



$data->addElement('hidden', 'users_objects_id', []);

$data->addElement('select', 'type_id', [
    'label' => 'Type',
    'multiOptions' => db()->fetchPairs(db()->select()->from(
        ['it' => 'inspection_types'],
        ['id', 'description_nl']
    )
    ->order('description_nl')->where('archived = ?', false))
]);

$data->addElement('text', 'date', [
    'label' => 'Datum',
    'attribs' => ['class' => 'DatePicker']
]);

$data->addElement('hidden', 'status', [
    'value' => 'inspection_planned'
]);







	if(Settings::get('inspection_new_version')) {

        $project_select = db()->select()
            ->from('projects', ['id', 'name'])
            ->order('name')
            ->where('exploitation = ?', true);

        $projects = db()->fetchPairs($project_select);

        $data->addElement('select', 'project', [
            'label' => 'Project',
            'attribs' => [
                'title' => 'Let op: ALLEEN een project kiezen indien u voor alle woningen in dit project in een keer een inspectie wilt toevoegen. Voor enkelvoudige inspecties dient u een huurder uit de zoekoptie hierboven te selecteren',
                'class' => 'forceAutoHint',
            ],
            'multiOptions' => [0 => 'Geen project'] + $projects
        ]);

        $data->addElement('select', 'user_object_current_or_next', [
            'label' => 'Toevoegen voor: ',
            'multiOptions' => [
                'current' => 'Huidige huurder',
                'next' => 'Toekomstige huurder'
            ]
        ]);
    }


	$data->addElement('hidden', 'users_objects_id', array());

    $data->addElement('select', 'type_id', [
        'label' => 'Type',
        'multiOptions' => db()->fetchPairs(db()->select()->from(
            ['it' => 'inspection_types'],
            ['id', 'description_nl']
        )
            ->order('description_nl')->where('archived = ?', false))
    ]);

	$data->addElement('text', 'date', array(
		'label' => 'Datum',
		'attribs' => array('class' => 'DatePicker')
	));

	$data->addElement('hidden', 'status', array(
		'value' => 'inspection_planned'
	));

 
$u_model = new Users();
$intUsers = $u_model->getInspectionAdminUsers();
$internalUsers = [];
foreach ($intUsers as $user) {
    $internalUsers[$user['id']] = User::buildname($user);
}

$data->addElement('select', 'assigned_user', [
    'label' => 'Medewerker',
    'multiOptions' => ['false' => 'Kies een medewerker'] + $internalUsers
]);


$data->addElement('textarea', 'comment', [
    'label' => 'Opmerking toevoegen voor inspectie',
    'rows'			=> 	4,
    'cols'			=> 	50

]);

//submit
$this->addElement('submit', 'aanmaken', [
    'label' => 'Inspectie opslaan'
]);


