<?

//general form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'components')
	->setAttrib('class', 'Components form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene instellingen');
$this->addSubForm($general, 'general');

$general->addElement('select', 'cost_type', array(
	'label' => 'Type component',
	'multiOptions' => array(
		'turnover' 	=> 'Omzet',
        'opex' 		=> 'Operationele kosten [Opex]',
		'capex' 	=> 'Investeringen [Capex]',
        'othercost'	=> 'Algemene kosten',
		'discount' 	=> 'Korting',
        'NULL' => 'n.v.t'
	),
	'required' => true
));

//name
$general->addElement('text', 'name', array(
	'label' => 'Omschrijving',
	'required' => true,
));


//name
$general->addElement('text', 'name_en', array(
    'label' => 'Omschrijving Engels',
 ));
 if(Settings::get('software_french_enabled')):
$general->addElement('text', 'name_fr', array(
    'label' => 'Omschrijving Frans',
 ));
endif;





	//code
	$general->addElement('text', 'code', array(
		'label' => 'Code',
	));


$units = array('Vast' => array(), 'Variabel' => array());
foreach(Units::getList() as $unit)
	$units[$unit['type'] == 'fixed' ? 'Vast' : 'Variabel'][$unit['id']] = $unit['label'];

if(Settings::get('software_type') != 'energy' && count($units['Variabel']) == 0)
	$units = array('Algemeen' => $units['Vast']);

$general->addElement('select', 'unit', array(
	'label' => 'Eenheid',
	'multiOptions' => $units,
	'required' => true
));

if(Settings::get('software_type') != 'energy'){
	$general->addElement('select', 'final_nota_basis', array(
		'label' => 'Afrekening basis',
		'multiOptions' => [
			'costs' => 'Ingevoerde kosten',
			'usage' => 'Verbruik'
		],
		'required' => true,
		'attribs' => array(
			'title' => 'Bij de afrekening worden de kosten voor dit component berekend op basis van ingevoerde kosten of op basis van verbruik (meterstanden x tarief).',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

    if(Settings::get('general_company_shortname') == 'debazaar') {
        $general->addElement('checkbox', 'object_factor', array(
            'label' => 'Object factor',
            'attribs' => array(
                'title' => 'Voor dit component kan bij het object een factor ingevoerd worden voor vermenigvuldiging met het tarief',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': 0, 'y': 0}"
            )
        ));
    }

}

if(Settings::get('software_type') == 'energy'){
	$general->addElement('text', 'invoice_grouping_label', array(
		'label' => 'Aangepast label factuur',
		'attribs' => array(
			'placeholder' => 'Voorschotbedrag levering warmte en koude of Maandelijkse kosten Green Package'
		)
	));

	$categories = array('fixed' => 'Vastrecht', 'variable' => 'Variabel');


	$products = array('Vastrecht' => array(), 'Variabel' => array());

	foreach(Products::getList() as $product)
		$products[$product['type'] == 'fixed' ? 'Vastrecht' : 'Variabel'][$product['id']] = $product['label'];

	foreach($categories as $category_name => $category)
		if(count($units[$category]) == 0)
			unset($categories[$category_name]);

	$general->addElement('select', 'category', array(
		'label' => 'Categorie',
		'multiOptions' => $categories,
		'required' => true
	));

	$general->addElement('select', 'product', array(
		'label' => 'Product',
		'multiOptions' => $products,
		'required' => true
	));

	if(Settings::get('software_type') == 'energy')
		$general->addElement('select', 'tapkl', array(
			'label' => 'Tapklasse',
			'multiOptions' => array(
				'false' => '-',
				'3' => 'CW3',
				'4' => 'CW4',
				'5' => 'CW5',
				'6' => 'CW6'
			)
		));

	$general->addElement('select', 'energy_law', array(
		'label' => 'Warmtewet component',
		'multiOptions' => array(
			'0' => 'Nee',
			'1' => 'Ja'
		),
		'required' => true
	));

} else {
	$products_model = new Products();
	$products_row = $products_model->matchRow(array('name' => 'huur'));

	$general->addElement('hidden', 'product', array(
		'value' => $products_row ? $products_row->id :1,
	));

    $general->addElement('select', 'type', array(
        'label' => 'Type',
        'multiOptions' => array('particulier' => 'Particulier', 'commercieel' => 'Commercieel'),
        'required' => true
    ));

	$general->addElement('checkbox', 'is_rental', array(
		'label' => 'Huurcomponent',
		'attribs' => array(
			'title' => 'Dit component is het huurcomponent',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));
}


//project
$general->addElement('select', 'project', array(
	'label' => 'Project',
	'multiOptions' => array(0 => 'Alle projecten') + db()->fetchPairs(db()->select()->from(array('p'=>'projects'), array('id','name'))->order('name')->where('exploitation = ?', true))
));


$financial = new Form_SubForm($this);
$financial->setAttrib('title', 'Financieel');
$this->addSubForm($financial, 'financial');

if(Settings::get('invoice_corporation_per_component')) {
    $corpLib = new Corporation();
    $corporationOptions = $corpLib->getSimpleList();
    natcasesort($corporationOptions);

    $financial->addElement('select', 'invoice_corporation_id', [
        'label' => 'Juridische enititeit facturatie',
        'multiOptions' => [0 => 'Zoals ingesteld bij project'] + $corporationOptions,
        'attribs' => ['style' => 'width: 200px;']
    ]);
}

foreach(tax()->getRates() as $rate_level => $rate)
	$tax_rates[$rate_level] = $rate['label'];

$financial->addElement('select', 'tax_rate', array(
	'label' => 'BTW Tarief',
	'required' => true,
	'multiOptions' => $tax_rates
));

$financial->addElement('text', 'ledger', array(
	'label' => 'Grootboek',
	'required' => true,
));

$financial->addElement('checkbox', 'custom_invoice_overridable', array(
	'label' => 'Aanpasbaar bij handmatige factuur',
	'value' => false,
	'attribs' => array(
		'title' => 'Met deze optionele instelling mag het BTW tarief en de grootboek afwijkend ingesteld worden bij regels op een handmatige factuur',
		'class' => 'forceAutoHint'
	)
));

if(Settings::get('financial_export_system') == 'accountview' || Settings::get('financial_export_vat_code_on_components')) {
    $financial->addElement('text', 'vatcode_financial_export', [
        'label' => 'BTW code financiele koppeling overschrijven',
        'attribs' => [
            'title' => 'Met deze optionele instelling kan de BTW code voor de koppeling naar het financiele pakket overschreven worden ten opzichte van de standaard instelling van de BTW code van het BTW tarief bij de juridische entiteit.',
            'class' => 'forceAutoHint'
        ]
    ]);
    $financial->addElement('text', 'vat_ledger_financial_export', [
        'label' => 'BTW grootboek financiele koppeling overschrijven',
        'attribs' => [
            'title' => 'Met deze optionele instelling kan de BTW grootboek voor de koppeling naar het financiele pakket overschreven worden ten opzichte van de standaard instelling van de BTW grootboek van het BTW tarief bij de juridische entiteit.',
            'class' => 'forceAutoHint'
        ]
    ]);
}

$financial->addElement('select', 'rato', array(
	'label' => 'Voorkeur verdeling',
	'multiOptions' => array( 'NULL' => 'n.v.t' ,'even' => 'Gelijk', 'key' => 'Verdeelsleutel', 'size' => 'Oppervlakte (m2)'),
));

$financial->addElement('select', 'service_charge', [
	'label' => 'Servicekosten',
	'multiOptions' => [
		'no' => 'Nee',
        'yes' => 'Ja',
	],
	'attribs' => [
		'title' => 'Dit bepaald de standaard instelling van de "Servicekosten" kolom als dit component geselecteerd '
			.'wordt bij een inkoopfactuur.',
		'class' => 'forceAutoHint'
	]
]);

$financial->addElement('text', 'costs_title', array(
    'label' => 'Servicekosten titel',
    'attribs' => array(
        'title' => 'Met de servicekosten titel wordt het kosten component gelinkt aan het bijbehorende omzet component.',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': 0, 'y': 0}"
    )
));

//label
$financial->addElement('text', 'group_label', array(
    'label' => 'Groep label',
    'attribs' => array(
        'title' => 'Op basis van het groep label kunnen componenten op een afrekening worden gegroepeerd',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': 0, 'y': 0}"
    )

));

$financial->addElement('number', 'admin_costs_percentage_service_charges', array(
    'label' => '% adm. kosten',
    'placeholder' => Settings::get('modules_service_charges_administration_charges_percentage'),
    'attribs' => array(
        'title' => 'Percentage administratie over van de werkelijke kosten',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': 0, 'y': 0}",
        'decimals' => 2,
        'step' => '.01',
    )

));



if(Settings::get('software_type') != 'energy'){
	$article = new Form_SubForm($this);
	$article->setAttrib('title', 'Artikel');
	$this->addSubForm($article, 'article');


	$article->addElement('checkbox', 'is_article', array(
		'label' => 'Artikel',
		'attribs' => array(
			'title' => 'Dit component is een artikel',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	$article->addElement('checkbox', 'article_first_nota', array(
		'label' => 'Eerst nota',
		'attribs' => array(
			'title' => 'Dit artikel toevoegen aan de eerste nota',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));

	if(Settings::get('general_company_shortname') == 'debazaar')
		$article->addElement('checkbox', 'article_first_nota_periodical', array(
			'label' => 'Eerst nota voor periodekaarten',
			'attribs' => array(
				'title' => 'Dit artikel toevoegen aan de eerste nota, ook wanneer het een periodekaart betreft.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)
		));

	$article->addElement('numberInput', 'article_price', array(
		'label' => 'Prijs',
		'attribs' => array(
			'title' => 'De prijs van dit artikel, exclusief BTW',
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': 0, 'y': 0}"
		)
	));
}



//submit
$this->addElement('submit', 'aanmaken', array(
	'label' => 'Component opslaan'
));

?>
