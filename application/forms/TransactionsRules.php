<?

//general form options
$this->setAction('')
        ->setAttrib('id', 'transaction_rules')
		->setMethod('post')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general');

	$general->addElement('text', 'name', [
		'label' => 'Naam',
		'validators' => [],
	]);

    if(Settings::get('general_company_shortname') == 'heyen') {

        $general->addElement('checkbox', 'make_payment_transfer', [
            'label' => 'Overboekingsopdract maken?'
        ]);

        $general->addElement('select', 'activity_link', [
            //'show_on_condition' => 'make_payment_file',
            'label' => 'Koppelen aan herhalende activiteit',
            'multiOptions'	=> ['' => 'Maak een keuze'] + Activities::getListForSelect(),
        ]);

        $corpLib = new Corporation();
        $corporationOptions = $corpLib->getSimpleList();
        natcasesort($corporationOptions);

        $general->addElement('select', 'payment_corporation_id', [
            //'show_on_condition' => 'make_payment_file',
            'label' => 'Vanuit entiteit',
            'multiOptions'	=> ['' => 'Maak een keuze'] + $corporationOptions,
            'attribs' => [
                'style' => 'width: 320px;'
            ]
        ]);
    }



$conditions = new Form_SubForm($this);
$conditions->setAttrib('title', 'Voorwaarden');
$this->addSubForm($conditions, 'conditions');


    $conditions->addElement('text', 'iban', [
        'label' => 'IBAN',
        'required' => true,
        'placeholder' => 'IBAN van de afschrijving',
        'attribs' => [
            'style' => 'width: 100%;'
        ]
    ]);

	$conditions->addElement('textarea', 'description', [
		'label' => 'Unieke omschrijving',
        'required' => true,
        'rows' => 4,
        'cols' => 30,
        'placeholder' => 'Max 140 karakters: Unieke omschrijving van deze afschrijving waarop Omniboxx de regel kan matchen'
	]);

	$conditions->addElement('select', 'amount_type', [
		'label' => 'Bedrag conditie',
		'multiOptions' => ['amount_equals' => 'Gelijk aan', 'amount_between' => 'Tussen']
	]);

	$conditions->addElement('numberInput', 'amount', [
	    'show_on_condition' => 'amount_equals',
		'label' => 'Match bedrag',

	]);

    $conditions->addElement('numberInput', 'amount_minimum', [
        'show_on_condition' => 'amount_between',
        'label' => 'Minimaal bedrag'
    ]);

    $conditions->addElement('numberInput', 'amount_maximum', [
        'show_on_condition' => 'amount_between',
        'label' => 'Maximaal bedrag'
    ]);

$action = new Form_SubForm($this);
$action->setAttrib('title', 'Actie');
$this->addSubForm($action, 'action');

    $action->addElement('select', 'action_type', [
        'label' => 'Boeken als',
        'multiOptions' => ['costs' => 'Kosten']
    ]);


$costs = new Form_SubForm($this);
$costs->setAttrib('title', 'Kosten instellingen');
$this->addSubForm($costs, 'costs');

    $costs->addElement('text', 'user_name', [
        'label' => 'Leverancier',
        'attribs' => [
            'autocompleter_options' => json_encode([
                'hiddenElementId' => 'costs-user_id',
            ]),
            'title' => 'Zoek en selecteer een leverancier',
            'class' => 'forceAutoHint',
            'autocompleter' => 'invoice/find-custom-user/type/contact',
            //'autocompleter_options' => json_encode(['postOtherFormFields' => true])
        ]
    ]);

    $costs->addElement('hidden', 'user_id');

    $component_fetch = function($type = false) {
        $select = db()->select()
            ->from('components', ['id', 'name'])
            ->where('cost_type IN ("opex", "capex")')
            ->where('deleted = ?', false)
            ->order('name');

        if ($type){
            $select->where('type = ?', $type);
        }

        return db()->fetchPairs($select);
    };

    $costs->addElement('select', 'component_id', [
        'label' => 'Component',
        'required' => true,
        'multioptions' 	=> [
            0 => 'Kies een component',
            'Particulier' => $component_fetch('particulier'),
            'Commercieel' => $component_fetch('commercieel'),
            'Overig' => $component_fetch()
        ],
    ]);

    $costs->addElement('select', 'component_amount_or_percentage', [
        'label' => 'Vast bedrag of percentage',
        'multiOptions' => [
            'amount' => 'Vast bedrag',
            'percentage' => 'Percentage',
        ]
    ]);

    $costs->addElement('numberInput', 'component_amount', [
        'label' => 'Bedrag',
        'attribs' => [
            'title' => 'Het bedrag waarmee de kostenregel geboekt wordt.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $costs->addElement('numberInput', 'component_percentage', [
        'label' => 'Percentage',
        'attribs' => [
            'title' => 'Het percentage van het bedrag van de betaling waarmee de kostenregel geboekt wordt.',
            'class' => 'forceAutoHint',
        ]
    ]);

    $project_lib = new Project();
    $projects = $project_lib->getList();

    $costs->addElement('select', 'project_id', [
        'label' => 'Project',
        'required' => true,
        'multiple' => true,
        'registerInArrayValidator' => false,
        'multioptions' 	=> array_combine(
            array_column($projects, 'id'),
            array_column($projects, 'name')
        )
    ]);

    $costs->addElement('text', 'object_ids', ['hidden' => true]);

$this->addElement('submit', 'aanmaken', [
	'label' => 'Regel opslaan'
]);
