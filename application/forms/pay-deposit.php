<?
	



	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'deposit')
			->setAttrib('class', 'deposit form');

	$deposit = new Form_SubForm($this);
	$deposit->setAttrib('title', 'Waarborgbedrag');

	$unpayedAmount = isset($this->getOwner()->payProbs['unpayedAmount']) ? $this->getOwner()->payProbs['unpayedAmount'] : 0;

	$this->addSubForm($deposit, 'deposit');

include('deposit/userObjectSelect.php');
		
		$deposit->addElement('html', 'amounts', array(
            'label' => 'Openstaande facturen',
            'html' => $unpayedAmount > 0 ? '&euro; ' . (string) new StringFormat($unpayedAmount/100, 'money') : '-',        
        ));

		$deposit->addElement('text', 'amount', array(
			'label' => 'Betaald bedrag',
			'disabled' => true,
			'ignored' => true
		));
		
		$deposit->addElement('text', 'pay_back', array(
			'label' => 'Uitkeren'
		));
		
		$deposit->addElement('textarea', 'description', array(
			'label' => 'Omschrijving',
			'rows' => 4,
			'cols' => 48,
			'value' => 'Uitbetaling borgbedrag'
		));
			//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Waarborgsom uitkeren'
	));

?>
