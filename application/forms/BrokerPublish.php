<? $publish_id = $this->getOwner()->id ?: false; ?>
<script type="text/javascript">
	window.addEvent('domready', function(){
		[
			'broker_publish_text-text_intro_management',
			'broker_publish_text-text_intro_balance',
			'broker_publish_text2-text_intro_finance',
			'broker_publish_text2-text_intro_service',
			'broker_publish_text2-text_intro_technical',
		].each(function (field) {
			new nicEditor({
				'buttonList' : 	['bold','italic', 'underline', 'link', 'unlink', 'forecolor', 'bgcolor', 'indent', 'outdent', 'left', 'center', 'right', 'justify'],
				'maxHeight' : 	350,
                'iconsPath' : 'media/images/nicEditIcons-latest.gif'
			}).panelInstance(field);
		});
	});

	function iframeLoaded(){
		refreshDocs();
	}

	function refreshDocs(){
		new Request.JSON({
			'url': 'broker-publish/get-documents/id/<?= $publish_id ?>/',
			'onSuccess': function(results){
				$('document-list').empty();

				if(results)
					$each(results, function(filename, hash){
						var li = new Element('li', {
							'class': 'doc'
						}).inject($('document-list'));

						new Element('a', {
							'target': '_blank',
							'href': 'broker/doc-download/id/' + hash + '/',
							'text': filename
						}).inject(li)

						var delete_button = new Element('span', {
							'class': 'delete'
						}).inject(li);

						delete_button.addEvent('click', function(){
							deleteDocument(hash);
						});
					});
				else
					var li = new Element('li', {
						'text': 'Geen documenten toegevoegd'
					}).inject($('document-list'));

			}
		}).send();
	}

	function deleteDocument(hash){

		if(!confirm('Weet u zeker dat u dit document definitief wilt verwijderen?')) return;

		new Request({
			'url': 'broker-publish/delete-documents/id/<?= $publish_id ?>/hash/' + hash + '/',
			'onComplete': function(){ refreshDocs(); }
		}).send();
	}
</script>

<?
$corpLib = new Corporation();
$bpModel = new BrokerPublish();

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('id', 'brokerPublish')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'broker_publish_form');

	$corporationOptions = $corpLib->getSimpleList();
	natcasesort($corporationOptions);
	$general->addElement('select', 'corporation', [
		'label' => 'Juridische entiteit',
		'multiOptions' => $corporationOptions,
		'attribs' => [
			'title' => 'Let op: dit veld geeft een foutmelding als de gekozen combinatie van juridische entiteit en jaar al bestaat.',
			'class' => 'forceAutoHint',
		],
		'validators' => [
			[
				'validator'	=> 'Callback',
				'options' => [
					'callback' => function($value, $allValues) use ($bpModel) {
						if(!is_string($allValues['year']) || trim($allValues['year']) == false || !is_numeric($allValues['year']) || empty($allValues['corporation']) || !is_numeric($allValues['corporation']))
							return false;

						$params = $this->getOwner()->getAllParams();
						$id = intval($params['id']);
						$row = $bpModel->matchRow(['year' => $allValues['year'], 'corporation' => $allValues['corporation']]);
						
						// only return true if there is no (other) publish with the given combination of year and corporation.
						return ($id <= 0 || !$row || $id == $row['id']);
					},
				],
			],
		],
	]);

	$general->addElement('text', 'year', [
		'label' => 'Jaar',
		'attribs' => [
			'title' => 'Voer hier een jaartal tussen 2000 en 2050 in.',
			'class' => 'forceAutoHint',
		],
		'validators' => [
			[
				'validator'	=> 'stringLength',
				'options'	=> [
					'min' =>	4,
					'max' =>	4,
				],
			],
			[
				'validator'	=> 'Callback',
				'options' => [
					'callback' => function($value) {
						if(!is_string($value) || trim($value) == false || !is_numeric($value))
							return false;

						// must match only 4 digit integers that are plausable years
						return (intval($value) > 2000 && intval($value) < 2050);
					}
				],
			],
		],
	]);


$files = new Form_SubForm($this);
$files->setAttrib('title', 'Bestanden');
$this->addSubForm($files, 'files');

    $file_list = '';

    foreach($this->getOwner()->files as $file_type => $files_for_type)
        foreach($files_for_type as $file_id => $filename) {
            $link = 'broker-publish/delete-upload/type/' . $file_type . '/id/' . $file_id . '/';
            $file_list .= '<li title="' . htmlentities($filename) . '"><i class="fa fa-file-excel-o"></i>' . $filename . ' <a class="delete_button" href="' . $link . '"><i class="fa fa-trash"></i></a></li>';
        }

    $files->addElement('html', 'file_list', [
        'label' => 'Bestanden',
        'html' => '<ul class="file_list">' . $file_list . '</ul>'
    ]);


if($publish_id !== false){

	$docs_model = new BrokerPublishDocs();

	$docs = new Form_SubForm($this);
	$docs->setAttrib('title', 'Documentenbibliotheek');
	$this->addSubForm($docs, 'broker_publish_docs');

		$docs->addElement('html', 'financial_project_list', array(
			'label' => 'Documenten',
			'html' => '<ul id="document-list"></ul>'
		));	

		$docs->addElement('html', 'financial_project_upload_form', array(
			'label' => 'Toevoegen',
			'html' => '<iframe onload="iframeLoaded()" src="broker-publish/document-upload/id/' . $publish_id . '/" ></iframe>'
		));			
}	



	$text = new Form_SubForm($this);
	$text->setAttrib('title', 'Teksten');
	$this->addSubForm($text, 'broker_publish_text');

		$text->addElement('textarea', 'text_intro_management', [
			'label' => 'Management',
			'placeholder' => 'Inleidende tekst voor de management pagina'
		]);

		$text->addElement('textarea', 'text_intro_balance', [
			'label' => 'Balans /RR',
			'placeholder' => 'Inleidende tekst voor de balans pagina'
		]);

	$text2 = new Form_SubForm($this);
	$text2->setAttrib('title', 'Teksten');
	$this->addSubForm($text2, 'broker_publish_text2');

		$text2->addElement('textarea', 'text_intro_finance', [
			'label' => 'Financiën',
			'placeholder' => 'Inleidende tekst voor de financiën pagina'
		]);

		$text2->addElement('textarea', 'text_intro_service', [
			'label' => 'Klantenservice',
			'placeholder' => 'Inleidende tekst voor de klantenservice pagina'
		]);

		$text2->addElement('textarea', 'text_intro_technical', [
			'label' => 'Techniek',
			'placeholder' => 'Inleidende tekst voor de techniek pagina'
		]);

	$wedge = new Form_SubForm($this);
	$this->addSubForm($wedge, 'wedge');


//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Opslaan'
]);

?>
