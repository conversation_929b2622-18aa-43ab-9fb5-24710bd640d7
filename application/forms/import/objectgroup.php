<?

// general form options
$this->setAction('')
	->setMethod('post')
	->setEnctype(Zend_Form::ENCTYPE_MULTIPART);


$this->addElement('file', 'users', array(
	'label' => 'Gebruikers',
	'required' 		=>	true,
	'validators' => array(
		array(
			'validator' => 'Extension',
			'options' => array(
				'csv'
			),
		),
	),
));

$this->addElement('file', 'objects', array(
	'label' => 'Objecten',
	'required' 		=>	true,
	'validators' => array(
		array(
			'validator' => 'Extension',
			'options' => array(
				'csv'
			),
		),
	),
));

$this->addElement('file', 'users_objects', array(
	'label' => 'Perioden',
	'required' 		=>	true,
	'validators' => array(
		array(
			'validator' => 'Extension',
			'options' => array(
				'csv'
			),
		),
	),
));


$object_groups = array();
foreach ((array) Project::getSimpleList(false) as $project)
	$object_groups[$project['name']] = (array) Project::getObjectGroups($project['id'], 'project', 'particulier');


$this->addElement('select', 'object_group', array(
	'label' => 'Objectgroep',
	'required' => true,
	'multiOptions' => $object_groups,
));


$this->addElement('submit', 'bewerken', array(
	'label' => 'Importeren'
));
?>
