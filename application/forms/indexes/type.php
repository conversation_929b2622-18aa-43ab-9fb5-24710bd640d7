<?
    $isEditCase = $this->getOwner()->is_edit_case;
    $isAutoFillCbsData = $this->getOwner()->is_auto_fill_cbs_data;

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'indexesType')
			->setAttrib('class', 'indexesType form');

	$this->addElement('text', 'name', array(
		'label' => 'Naam',
        'required'=> true
	));

	$this->addElement('textarea', 'description', array(
		'label' => 'Omschrijving',
		'rows'=> 7,

	));

    $this->addElement('textarea', 'url_calculation_nl', array(
        'label' => 'Berekening controle (NL)',
        'rows'=> 3,
        'value' => Indexes_Type::getUrlCalculationDefaultValue()
    ));

    $this->addElement('textarea', 'url_calculation_en', array(
        'label' => 'Berekening controle (EN)',
        'rows'=> 3,
        'value' => Indexes_Type::getUrlCalculationDefaultValueEn()
    ));

    $cbsAutoFillTypes = [
        'no' => 'Nee',
        '2015_100' => '2015 = 100'
    ];

    $fromDisabledKey = $isEditCase && $isAutoFillCbsData ? 'disabled': 'enabled';

    $this->addElement('select', 'auto_fill_cbs_data', [
        'label' => 'Automatisch CBS-cijfers gebruiken',
        'multiOptions' => $cbsAutoFillTypes,
        $fromDisabledKey => true,
    ]);

    $this->addElement('select', 'allow_concept_cbs_data', [
        'label' => 'Concept CBS-cijfers gebruiken',
        'multioptions' 	=> [
            '0' => 'Nee',
            '1' => 'Ja'

        ],
        'attribs' => [
            'title' => 'Default waarde is NEE. Deze instelling op ja zetten betekend dat ook concept cijfers opgehaald worden, indien u indexeert met concept cijfers en deze achteraf wijzigen dient u dit handmatig te corrigen. Wij adviseren deze instelling op NEE te laten staan. ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ],
    ]);

    $this->addElement('select', 'override_percentage_behaviour', [
        'label' => 'Overschreven percentage werking',
        'multioptions' 	=> [
            'use_as_topoff' => 'Als aftopping gebruiken',
            'use_as_max' => 'Verhogen naar dit percentage'

        ]
    ]);


	$variables_invoice = array(
		'oude_huur' => 'Het oude bedrag van het component voor de verhoging.',
		'nieuwe_huur' => 'Het nieuwe bedrag van het component voor de verhoging.',
		'component' => 'Naam van het component dat verhoogd wordt.',
		'indexatie_berekening' => 'De berekening van de huurverhoging',
		'berekening_controle_url' => 'Website met uitleg over de berekening',
        'percentage' => 'Het percentage van de CBS berekening',
        'calculated_percentage' => 'Het uiteindelijke percentage van de verhoging'
	);

    $variables_calculation = array(
        'max_indexation' => 'Het ingestelde maximale percentage bij de huurder.',
        'min_indexation' => 'Het ingestelde minimale percentage bij de huurder.',
        'percentage' => 'Het percentage van de CBS berekening',
    );

    $variables_limit = array(
        'limit_indexation' => 'De waarde van overschreven percentage',
    );

    $variables_letter = array(
        'indexatie_berekening' => 'De berekening van de huurverhoging',
        'berekening_controle_url' => 'Website met uitleg over de berekening'
    );

	$variables_invoice_html = '';

	foreach($variables_invoice as $variable_name => $variable_text)
        $variables_invoice_html .= '<b>[' . $variable_name . ']: </b> ' . $variable_text . '</br>';

    $variables_letter_html = '';

    foreach($variables_letter as $variable_name => $variable_text)
        $variables_letter_html .= '<b>[' . $variable_name . ']: </b> ' . $variable_text . '</br>';

    $variables_calculation_html = '';

    foreach($variables_calculation as $variable_name => $variable_text)
        $variables_calculation_html .= '<b>[' . $variable_name . ']: </b> ' . $variable_text . '</br>';


    foreach($variables_limit as $variable_name => $variable_text)
        $variables_limit_html .= '<b>[' . $variable_name . ']: </b> ' . $variable_text . '</br>';


	$this->addElement('textarea', 'adjustment_text_nl', array(
		'label' => 'Uitleg indexatie op factuur (NL)',
		'rows'=> 7,
		'attribs' => array(
			'title' => $variables_invoice_html,
			'class' => 'forceAutoHint',
			'hintOffset' => "{'x': -0, 'y': 0}"
		),
		'value' => Indexes_Type::getAdjustmentTextDefaultValue()
	));

    $this->addElement('textarea', 'adjustment_text_en', array(
        'label' => 'Uitleg indexatie op factuur (EN)',
        'rows'=> 7,
        'attribs' => array(
            'title' => $variables_invoice_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextDefaultValueEN()
    ));


        $this->addElement('textarea', 'adjustment_text_letter_nl', array(
            'label' => 'Uitleg indexatie op brief (NL)',
            'rows'=> 7,
            'attribs' => array(
                'title' => $variables_letter_html,
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentTextLetterDefaultValue()
        ));

        $this->addElement('textarea', 'adjustment_text_letter_en', array(
            'label' => 'Uitleg indexatie op brief (EN)',
            'rows'=> 7,
            'attribs' => array(
                'title' => $variables_letter_html,
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentTextLetterDefaultValueEN()
        ));


    $this->addElement('textarea', 'adjustment_text_max_nl', array(
        'label' => 'Uitleg bij limiet max in contract (Nl)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_calculation_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextMaxValueNL()
    ));

    $this->addElement('textarea', 'adjustment_text_max_en', array(
        'label' => 'Uitleg bij limiet max in contract (EN)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_calculation_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextMaxValueEN()
    ));


    $this->addElement('textarea', 'adjustment_text_min_nl', array(
        'label' => 'Uitleg bij ophogen naar min in contract (Nl)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_calculation_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextMinValueNL()
    ));


    $this->addElement('textarea', 'adjustment_text_min_en', array(
        'label' => 'Uitleg bij ophogen naar min in contract (EN)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_calculation_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextMinValueEN()
    ));


    $this->addElement('textarea', 'adjustment_text_limited_nl', array(
        'label' => 'Uitleg werking overschreven percentage (NL)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_limit_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextLimitedValueNL()
    ));


    $this->addElement('textarea', 'adjustment_text_limited_en', array(
        'label' => 'Uitleg werking overschreven percentage (EN)',
        'rows'=> 5,
        'attribs' => array(
            'title' => $variables_limit_html,
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}"
        ),
        'value' => Indexes_Type::getAdjustmentTextLimitedValueEN()
    ));



//tax
	$this->addElement('select', 'type', [
		'label' => 'Type indexatie',
        //'required'=> true,
		'multioptions' 	=> [
                                '' => 'Maak een keuze',
                                'private' => 'Trend huurverhoging (Particulier % verhoging)',
								'commercial' => 'CBS berekening (Commercieel / Liberaal) '
							],
        'attribs' => [
            'title' => 'Bij Trend huurverhoging vult u een percentage per jaar in waarmee de huren verhoogd worden. 
            Bij Commercieel jaar of maandcijfers (CBS) op basis waarvan de indexatie berekend wordt. ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ],
	]);

    $isPeriodTypeNull = $this->getOwner()->indexesTypeRow['period_type'];
    $fromDisabledKey = $isEditCase && $isPeriodTypeNull !== null ? 'disabled': 'enabled';

    $isRequired = $isEditCase && $this->getOwner()->indexesTypeRow['type'] !== 'private' && $isPeriodTypeNull === null;

    $this->addElement('select', 'period_type', [
        'label' => 'Periode type',
        'multioptions' 	=> [
            '' => 'Maak een keuze',
            'monthly' => 'Maandcijfers',
            'yearly' => 'Jaarcijfers'
        ],
        $fromDisabledKey => false,
        'required' => $isRequired
    ]);

    $this->addElement('checkbox', 'hpw', [
        'label' => 'Gereguleerde huurverhoging',
        'attribs' => [
            'style' => 'margin-right: 30px;',
            'title' => 'Als deze indextype gebruikt wordt voor verhoging die gereguleerd is vink dan deze optie aan. Hierbij komt een extra tekst over de gereguleerde huurprijs op de huurverhogingsbrief. ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ]
    ]);

    $rentLimitTypes = ['none' => 'Geen',
        'limit' => 'Aftoppen',
        'limit_and_lower' => 'Aftoppen en/of verlagen'];

    $this->addElement('select', 'rent_limit_logic', [
        'label' => 'Huuraftopping',
        'multioptions' 	=> 	$rentLimitTypes,
        'attribs' => [
            'style' => 'margin-right: 30px;',
            'title' => 'Objecten ingesteld als Sociale en/of Midden huur worden gecheckt bij de verhoging of de huur overeenkomt met de maximale huur. Op basis van deze instelling kunt u bepalen hoe deze berekening toegepast wordt bij de huurverhoging. ',
            'class' => 'forceAutoHint',
            'hintOffset' => "{'x': -0, 'y': 0}",
        ]
    ]);

        $this->addElement('textarea', 'adjustment_text_regulated_nl', array(
            'label' => 'Uitleg huurprijs regulering (NL Bovenaan)',
            'rows'=> 5,
            'attribs' => array(
                'title' => 'Toelichting op de brief voor de huurverhoging. Deze tekst komt bovenaan de brief.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentRegulatedValueNL()
        ));

        $this->addElement('textarea', 'adjustment_text_regulated_en', array(
            'label' => 'Uitleg huurprijs regulering  (EN Bovenaan)',
            'rows'=> 5,
            'attribs' => array(
                'title' => 'Toelichting op de brief voor de huurverhoging Engels. Deze tekst komt bovenaan de brief.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentRegulatedValueEN()
        ));




        $this->addElement('textarea', 'adjustment_text_regulated_bottom_nl', array(
            'label' => 'Uitleg huurprijs regulering (NL Onderaan)',
            'rows'=> 5,
            'attribs' => array(
                'title' => 'Toelichting op de brief voor de huurverhoging. Deze tekst komt onderaan de brief',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentRegulatedBottomValueNL()
        ));

        $this->addElement('textarea', 'adjustment_text_regulated_bottom_en', array(
            'label' => 'Uitleg huurprijs regulering  (EN Onderaan)',
            'rows'=> 5,
            'attribs' => array(
                'title' => 'Toelichting op de brief voor de huurverhoging Engels. Deze tekst komt onderaan de brief.',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': -0, 'y': 0}"
            ),
            'value' => Indexes_Type::getAdjustmentRegulatedBottomValueEN()
        ));


	//submit
	$this->addElement('submit', 'submit', array(
		'label' => 'Index opslaan'
	));
