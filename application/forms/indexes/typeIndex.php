<?php

    $this->setNewStyle(true);

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'indexesType')
			->setAttrib('class', 'indexesType form');

$startYear = date('Y') + (date('m') > 9 ? 1 : 0);
$years = range($startYear, $startYear - 10);

    $indexvalue = new Form_SubForm($this);
    $this->addSubForm($indexvalue, 'indexvalue');

        $isRequired = $this->getOwner()->is_auto_fill_cbs_data === 'no';

        $indexvalue->addElement('select', 'year', array(
            'label' => 'Jaar',
            'multioptions' 	=> array_combine($years, $years),
            'value' => date('Y'),
            'required' => $isRequired,
            'disabled' => $this->getOwner()->is_auto_fill_cbs_data,
        ));

        $hasMonthInput = $this->getOwner()->indexType && $this->getOwner()->indexType['period_type'] === 'monthly';

        $isRequired = $this->getOwner()->is_auto_fill_cbs_data === 'no' && $hasMonthInput;


        $indexvalue->addElement('select', 'month', array(
            'label' => 'Maand',
            'multioptions' 	=> Indexes::getIndexPeriodOptions(),
            'value' => date('n'),
            'required' => $isRequired,
            'disabled' => $this->getOwner()->is_auto_fill_cbs_data,
            'validators' => [
                [
                    'validator' => 'Callback',
                    'options' => [
                        'callback' => function ($value, $allValues) {

                            $id = $this->getOwner()->getParam('id');
                            $type = $this->getOwner()->getParam('indexId');

                            $select = db()->select()
                                ->from('indexes_types_indexes', ['id'])
                                ->where('year = ?', $allValues['year'])
                                ->where('month = ?', $allValues['month'])
                                ->where('type = ?', $type);

                            if ($id) {
                                $select->where('id != ?', $id);
                            }

                            $isUnique = db()->fetchOne($select) == false;

                            if (!$isUnique) {
                                $this->indexvalue->month->setErrorMessages([
                                    'duplicate' => 'Voor deze periode is al een waarde ingevoerd voor dit indexatietype.'
                                ]);
                            }

                            return $isUnique;
                        },
                    ],
                ],
            ],
        ));

        $isRequired = $this->getOwner()->is_auto_fill_cbs_data === 'no';
        $fromDisabledKey = $this->getOwner()->is_edit_case && $this->getOwner()->is_auto_fill_cbs_data ? 'disabled': 'enabled';

        $indexvalue->addElement('number', 'value', array(
            'label' => 'Waarde',
            'validators' 	=> 	[],
            'required' 		=>	$isRequired,
            $fromDisabledKey => true,
            'attribs' => [
                'title' => 'Waarde (decimalen scheiden door een komma)',
                'class' => 'forceAutoHint number',
                'hintOffset' => "{'x': -30, 'y': 0}",
                'decimals' => 2,
                'step' => '.01',
            ]
        ));

        $indexvalue->addElement('number', 'override_percentage', array(
            'label' => 'Afwijkend percentage',
            'validators' 	=> 	[
                [
                    'validator' => 'Between',
                    'options' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                ],
            ],
            'attribs' => [
                'title' => 'Als u het standaard berekende percentage wit aftoppen, vul dan hier het gewenste percentage in (maximaal 2 decimalen, gescheiden door een komma)',
                'class' => 'forceAutoHint number',
                'hintOffset' => "{'x': -30, 'y': 0}",
                'decimals' => 2,
                'max' => 100,
                'min' => 0,
                'step' => '.01',
            ]
        ));




        $indexvalue->addElement('hidden', 'type');





//submit
        $this->addElement('submit', 'indexesType', array(
            'label' => 'Gegevens opslaan',
            'class' => 'class="btn btn-info waves-effect waves-light"',
        ));
