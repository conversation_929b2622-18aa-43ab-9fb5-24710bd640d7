<?

//general form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');


//category
$db = Zend_Db_Table::getDefaultAdapter();



$i = new Indexes_Type();
$data = $i->getForFormList();

//p($data,'die');
//Huidig indexatie type
$this->addElement('select', 'index_type_from', array(
    'label' 		=> 	'Indexatietype',
    'required' 		=>	false,
    'multiOptions' => [0 => 'Maak een keuze'] + $data,
    'validators' 	=> 	[]
));


$rentTypes = [0 => 'Maak een keuze',
    'social' => 'Sociaal',
    'middle' => 'Middenhuur',
    'freeSector' => 'Vrije sector'];

//Huursegement
$this->addElement('select', 'rent_type', array(
    'label' 		=> 	'Huursegment',
    'multioptions' 	=> 	$rentTypes,
    'validators' 	=> 	[],
    'attribs' => [
        'title' => 'Zoals ingesteld op het object',
        'class' => 'forceAutoHint'
    ],
));

$this->addElement('number', 'points_from', array(
    'label' 		=> 	'WWS punten van',
    'validators' 	=> 	[],
    'attribs' => array(
        'title' => 'Zoals ingesteld op het object',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -30, 'y': 0}",
    )
));

$this->addElement('number', 'points_till', array(
    'label' 		=> 	'WWS punten tot en met',
    'validators' 	=> 	[],
    'attribs' => array(
        'title' => 'Zoals ingesteld op het object',
        'class' => 'forceAutoHint',
        'hintOffset' => "{'x': -30, 'y': 0}",
    )
));


$this->addElement('select', 'index_type_to', array(
    'label' 		=> 	'Indexatietype',
    'required' 		=>	true,
    'multiOptions' => [0 => 'Maak een keuze'] + $data,
    'validators' 	=> 	[],
));


$this->addElement('submit', 'submitBtn', array(
	'label' => translate()->_('send'),

));

