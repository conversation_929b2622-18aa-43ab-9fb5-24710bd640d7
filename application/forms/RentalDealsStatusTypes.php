<?

/** @noinspection PhpUnhandledExceptionInspection */

//general form options
$this->setAction('')
    ->setAttrib('id', 'rental_deals_status_types')
    ->setMethod('post')
    ->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general');

    $general->addElement('textarea', 'description', [
        'label' => 'Status',
        'validators' => [],
        'expandable' => true,
    ]);

if (Settings::get('modules_rental_workflow_deals')) {
    $general->addElement('number', 'deadline_workdays', [
        'label' => 'Deadline (aantal werkdagen)',
        'validators' => [],
        'required' => true,
    ]);
}

    $general->addElement('number', 'order', [
        'label' => 'Volgorde',
        'validators' => [],
    ]);

    $general->addElement('select', 'allow_concept_deal', [
        'label' => 'Concept aanmaken',
        'validators' => [],
        'multiOptions' => [
            '0' => 'Nee',
            '1' => 'Ja',
        ]
    ]);

    $general->addElement('select', 'allow_final_deal', [
        'label' => 'Definitief maken',
        'validators' => [],
        'multiOptions' => [
            '0' => 'Nee',
            '1' => 'Ja',
        ]
    ]);

    $general->addElement('textarea', 'text', [
        'label' => 'Toelichting',
        'validators' => [],
        'expandable' => true,
    ]);

    $general->addElement('hidden', 'template_id', [
        'value' => $this->getOwner()->template_id
    ]);


if (Settings::get('modules_rental_workflow_deals')) {

    $workflow = new Form_SubForm($this);
    $workflow->setAttrib('title', 'Workflow doorzetten');
    $this->addSubForm($workflow, 'workflow');

    $otherStatusTypesInTemplate = (new RentalDealsStatusTypes())
        ->matchAll(['template_id' => $this->getOwner()->template_id, 'deleted' => 0])
        ->toArray();

    if($currentId = $this->getOwner()->getParam('id')) {
        $otherStatusTypesInTemplate =
            array_filter($otherStatusTypesInTemplate, function ($currentStatusType) use ($currentId) {
                return $currentStatusType['id'] !== $currentId;
            });
    }

    $workflow->addElement('select', 'workflow_manual_forward', [
        'label' => 'Handmatig doorzetten:',
        'validators' => [],
        'multiOptions' => [
            '0' => 'Nee',
            '1' => 'Ja',
        ],
        'value' => true
    ]);

    $workflowForwardOnEventValues = RentalDealsStatusTypes::getForwardOnEventValues();

    foreach(array_column($otherStatusTypesInTemplate, 'workflow_forward_on_event') as $eventSetOnOtherType){
        unset($workflowForwardOnEventValues[$eventSetOnOtherType]);
    }

    $workflow->addElement('select', 'workflow_forward_on_event', [
        'label' => 'Automatisch doorzetten bij:',
        'validators' => [],
        'multiOptions' => ['0' => '-'] + $workflowForwardOnEventValues,
        'attribs' => [
            'title' => 'Deals worden automatisch doorgezet naar de volgende status
                        nadat dit event heeft plaatsgevonden in het systeem.<br /><br />
                        Een event mag enkel aan 1 status gekoppeld worden per template, 
                        daarna verdwijnt de status uit deze lijst.
                        ',
            'class' => 'forceAutoHint',
        ],
    ]);

    $workflowAdditionalActionsValues = RentalDealsStatusTypes::getAdditionalActionTitles();

    foreach(array_column($otherStatusTypesInTemplate, 'workflow_additional_action') as $actionSetOnOtherType){
        unset($workflowAdditionalActionsValues[$actionSetOnOtherType]);
    }

    $workflow->addElement('select', 'workflow_additional_action', [
        'label' => 'Uitvoeren bij doorzetten:',
        'validators' => [],
        'multiOptions' => ['0' => '-'] + $workflowAdditionalActionsValues,
        'attribs' => [
            'title' => 'Na het voltooien van een status zal deze actie uitgevoerd worden
                        door het systeem.<br /><br />
                        Een event mag enkel aan 1 status gekoppeld worden per template, 
                        daarna verdwijnt de status uit deze lijst.
                        ',
            'class' => 'forceAutoHint',
            'style' => 'width:300px'
        ],
    ]);


    $workflow->addElement('checkbox', 'workflow_cancel_website_deals_not_drawn', [
        'label' => 'Kandidaten informeren dat ze uitgeloot zijn',
        'attribs' => [
            'title' =>
                'Na het voltooien van een status zullen openstaande website-deals voor hetzelfde object ' .
                'geannuleerd worden en geïnformeerd worden per E-mail dat ze uitgeloot voor deze woning zijn.',
            'class' => 'forceAutoHint',
        ],
    ]);

    $workflow->addElement('checkbox', 'workflow_cancel_website_deals', [
        'label' => 'Website deals annuleren',
        'attribs' => [
            'title' =>
                'Na het voltooien van een status zullen openstaande website-deals voor hetzelfde object ' .
                'geannuleerd worden en geïnformeerd worden per E-mail dat de woning inmiddels verhuurd is.',
            'class' => 'forceAutoHint',
        ],
    ]);
    if (Settings::get('modules_rental_website_enabled')) {
        $workflow->addElement('checkbox', 'workflow_website_de_publish_object', [
            'label' => 'Afmelden website',
            'attribs' => [
                'title' =>
                    'Na het voltooien van de status zullen de publicatie instellingen op het object uitgezet worden, in de nachtelijke update zal de woning van de verhuurwebsite gehaald worden"',
                'class' => 'forceAutoHint',
            ],
        ]);
    }

    if (Settings::get('workflow_funda_de_publish')) {
        $workflow->addElement('checkbox', 'workflow_tiara_de_publish_object', [
            'label' => 'Afmelden Funda/Tiara',
            'attribs' => [
                'title' =>
                    'Na het voltooien van de status zal het object afgemelden worden op Funda/Tiara d.m.v. "verhuurd per jaar"',
                'class' => 'forceAutoHint',
            ],
        ]);
        $workflow->addElement('checkbox', 'workflow_tiara_object_put_under_option', [
            'label' => 'Onder optie Funda/Tiara',
            'attribs' => [
                'title' =>
                    'Na het voltooien van de status zal het object de status "Onder optie" worden op Funda/Tiara',
                'class' => 'forceAutoHint',
            ],
        ]);
    }


    $publicationStatus = new Form_SubForm($this);
    $publicationStatus->setAttrib('title', 'Publicatiestatus overschrijven');
    $this->addSubForm($publicationStatus, 'publicationStatus');

    $publicationStatus->addElement('select', 'workflow_publication_status_override_on_start', [
        'label' => 'Status bij start:',
        'validators' => [],
        'multiOptions' => ['0' => 'Niet aanpassen'] + ObjectsPublish::getOverrideStatusValues(),
        'attribs' => [
            'title' => 'Op het moment dat de deal in deze dealstatus komt zal de overschreven publicatiestatus van het object op de gekozen waarde worden ingesteld. Als u kiest voor "Niet aanpassen" blijft de waarde zoals nu op het object ingesteld in stand',
            'class' => 'forceAutoHint',
        ],
    ]);

    $publicationStatus->addElement('select', 'workflow_publication_status_override_on_forward', [
        'label' => 'Status bij doorzetten:',
        'validators' => [],
        'multiOptions' => ['0' => 'Niet aanpassen'] + ObjectsPublish::getOverrideStatusValues(),
        'attribs' => [
            'title' => 'Op het moment dat de deal uit deze dealstatus doorgezet wordt zal de overschreven publicatiestatus van het object op de gekozen waarde worden ingesteld. Als u kiest voor "Niet aanpassen" blijft de waarde zoals nu op het object ingesteld in stand',
            'class' => 'forceAutoHint',
        ],
    ]);

    $workflowback = new Form_SubForm($this);
    $workflowback->setAttrib('title', 'Workflow terugzetten');
    $this->addSubForm($workflowback, 'workflowback');

    $workflowback->addElement('select', 'workflow_manual_backwards', [
        'label' => 'Handmatig terugzetten:',
        'validators' => [],
        'multiOptions' => [
            '0' => 'Nee',
            '1' => 'Ja',
        ],
        'value' => false
    ]);

    $workflowback->addElement( 'select', 'workflow_set_profile_completion', [
        'label'        => 'Bij terugzetten status aanpassen naar',
        'multiOptions' => [
            '' => 'Geen',
            '9' => ' Inschrijving ontvangen',
            '1' => ' Reservering incl. documenten geupload',
            '2' => ' Documenten akkoord, wachten op dossier',
            '3' => ' Dossier compleet, wachten op goedkeuring',
            '0' => ' Mag ondertekenen of heeft ondertekend',
        ],
        'attribs'      => [
            'title'   => 'De status wordt teruggezet, je dient wel de klant op de hoogte te stellen van nieuwe status en reden daarvan',
            'class'   => 'forceAutoHint',
        ],
    ] );
}

$this->addElement('submit', 'aanmaken', [
    'label' => 'Status opslaan'
]);
