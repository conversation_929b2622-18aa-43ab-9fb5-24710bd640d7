<?
	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'meters')
			->setAttrib('class', 'meters profile form');

	$u_model = new UsageProfiles();

	$usageprofile = $u_model->getById($this->getOwner()->view->profile->id);
	$type = $usageprofile->type == 'commercial' ? 'commercieel' : 'particulier';
	$meter_type = $this->getOwner()->view->type;

	$units = array();
	foreach(Units::getList(array($meter_type => true)) as $unit)
		$units[$unit['id']] = $unit['label'];

	$component_select = db()->select()
		->from(array('c' => 'components'), array('id', 'name'))
		->joinLeft(array('u' => 'units'), 'u.id = c.unit', false)
		->order('c.name')
		->where('c.type = ?', $type)
		->where('u.' . $meter_type . ' = ?', true);

	/**
	 * General usage profile data
	 */
	$general = new Form_SubForm($this);
	$general->setAttrib('title', 'Algemeen');
	$this->addSubForm($general, 'general');

		// profile name
		$general->addElement('text', 'name', array(
			'label' 		=> 	'Naam',
			'validators' 	=> 	array()
		));

		$general->addElement('select', 'component', array(
			'label' => 'Component',
			'required' => true,
			'multiOptions' => db()->fetchPairs($component_select)
		));		

		$general->addElement('select', 'userunit', array(
			'label' => 'Aansluiting',
			'multiOptions' => $units,
			'required' => true
		));

	$usage = new Form_SubForm($this);
	$usage->setAttrib('title', 'Verbruik / aansluitvermogen');
	$this->addSubForm($usage, 'usage');


		$usage->addElement('select', 'unit', array(
			'label' => 'Eenheid',
			'multiOptions' => $units,
			'disabled' => true,
		));	

		$usage->addElement('text', 'value', array(
			'label' 		=> 	'Waarde',
			'validators' 	=> 	array(),
			'required' => true
		));

	//submit
	$this->addElement('submit', 'meters', array(
		'label' => 'Opslaan'
	));