<?
//general form options
$this->setAction('')
	->setMethod('post')
	->setAttrib('id', 'set')
	->setAttrib('class', 'set form');

/**
 * General usage profile data
 */
$general = new Form_SubForm($this);
$this->addSubForm($general, 'general');

// profile name
$general->addElement('text', 'name', array(
	'label' 		=> 	'Naam afgifteset',
	'validators' 	=> 	array()
));

// profile date
$general->addElement('text', 'date', array(
	'label' 		=> 	'Datum',
	'validators' 	=> 	array(),
	'class'			=> 	'DatePicker',
	'value'			=>	date('d-m-Y')
));

// profile type
$general->addElement('select', 'type', array(
	'label' 		=> 	'Type',
	'validators' 	=> 	array(),
	'multiOptions' => array('commercieel' => 'Commercieel', 'particulier' => 'Particulier')
));

//submit
$this->addElement('submit', 'usageprofile', array(
	'label' => 'Opslaan'
));