<?php

try {
    $language = $this->getOwner()->view->language;
    $inspection_types = (new Inspection())->type_labels;

    $introOptions = [
        'label' => 'Introtekst',
        'rows' => 5,
        'cols' => 80,
    ];

    $endOptions = [
        'label' => 'Eindtekst',
        'rows' => 5,
        'cols' => 80,
    ];

    $this->setAction('')
        ->setMethod('post');

    $first = new Form_SubForm($this);
    $first->setAttrib('title', ucfirst($inspection_types['first']));
    $this->addSubForm($first, 'first');
    $first->addElement('textarea', "intro_first_inspection_{$language}", $introOptions);
    $first->addElement('textarea', "end_first_inspection_{$language}", $endOptions);


    $interim = new Form_SubForm($this);
    $interim->setAttrib('title', ucfirst($inspection_types['interim']));
    $this->addSubForm($interim, 'interim');
    $interim->addElement('textarea', "intro_interim_inspection_{$language}", $introOptions);
    $interim->addElement('textarea', "end_interim_inspection_{$language}", $endOptions);


    $pre_end = new Form_SubForm($this);
    $pre_end->setAttrib('title', ucfirst($inspection_types['pre_end']));
    $this->addSubForm($pre_end, 'pre_end');
    $pre_end->addElement('textarea', "intro_pre_end_inspection_{$language}", $introOptions);
    $pre_end->addElement('textarea', "end_pre_end_inspection_{$language}", $endOptions);


    $end = new Form_SubForm($this);
    $end->setAttrib('title', ucfirst($inspection_types['end']));
    $this->addSubForm($end, 'end');
    $end->addElement('textarea', "intro_end_inspection_{$language}", $introOptions);
    $end->addElement('textarea', "end_end_inspection_{$language}", $endOptions);


    $this->addElement('submit', 'aanmaken', [
        'label' => 'Opslaan'
    ]);

} catch (Zend_Form_Exception $e) {
    throw $e;
}
