<?php
$userId = $this->getOwner()->getParam('customer');
$userObjectList = Objectusers::getListForUser($userId);

if (count($userObjectList) > 1) {

    $userObjectSelectOptions = [];
    foreach ($userObjectList as $userObjectListItem) {
        $labelParts = [
            $userObjectListItem['rendered_address'],
            ' | ',
            'van: ' . date('d-m-Y', strtotime($userObjectListItem['from']))
        ];

        if ($userObjectListItem['till']) {
            $labelParts[] = ' | tot: ' . date('d-m-Y', strtotime($userObjectListItem['till']));
        }

        $userObjectSelectOptions[$userObjectListItem['id']] = implode($labelParts);
    }

    $deposit->addElement('select', 'uoid', [
        'label' => 'Object/huurperiode',
        'multiOptions' => $userObjectSelectOptions,
        'value' => $this->getOwner()->getParam('uoid'),
            'style' => 'max-width:300px;'
    ]);
}
