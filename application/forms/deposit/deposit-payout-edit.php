<?php

$this->setAction('user/deposit-payout-edit/')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$this->addElement('text', 'expense_trustee', [
    'label' => 'Deel beheerder',
    'validators' => [
        [
            'validator' => 'Callback',
            'options' => [
                'callback' => function ($value, $allValues) {
                    $row = db()->select()
                        ->from(['i' => 'inspection'], false)
                        ->where('i.id = ?', $allValues['id'])
                        ->joinLeft(['uo' => 'users_objects'], 'uo.id = i.users_objects_id', 'deposit_payed')
                        ->query()
                        ->fetch();

                    $expenseTrustee = (new StringFormat($allValues['expense_trustee'], 'money_db'))->userstring;
                    $expenseOwner = (new StringFormat($allValues['expense_owner'], 'money_db'))->userstring;
                    $newTotal = $expenseTrustee + $expenseOwner;

                    return $row && $newTotal <= $row['deposit_payed'];
                }
            ],
        ],
    ],
    'attribs' => [
        'title' => 'Het nieuwe totaalbedrag mag niet hoger zijn dan de waarborg.',
        'class' => 'forceAutoHint number',
        'hintOffset' => "{'x': -30, 'y': 0}"
    ]
]);

$this->addElement('text', 'expense_owner', [
    'label' => 'Deel verhuurder',
    'validators' => [
        [
            'validator' => 'Callback',
            'options' => [
                'callback' => function ($value, $allValues) {
                    $row = db()->select()
                        ->from(['i' => 'inspection'], false)
                        ->where('i.id = ?', $allValues['id'])
                        ->joinLeft(['uo' => 'users_objects'], 'uo.id = i.users_objects_id', 'deposit_payed')
                        ->query()
                        ->fetch();

                    $expenseTrustee = (new StringFormat($allValues['expense_trustee'], 'money_db'))->userstring;
                    $expenseOwner = (new StringFormat($allValues['expense_owner'], 'money_db'))->userstring;
                    $newTotal = $expenseTrustee + $expenseOwner;

                    return $row && $newTotal <= $row['deposit_payed'];
                }
            ],
        ],
    ],
    'attribs' => [
        'title' => 'Het nieuwe totaalbedrag mag niet hoger zijn dan de waarborg.',
        'class' => 'forceAutoHint number',
        'hintOffset' => "{'x': -30, 'y': 0}"
    ]
]);

$this->addElement('text', 'user_iban', [
    'label' => 'Uitkeer Iban',
    'required' => true,
]);

$this->addElement('hidden', 'id');

$this->addElement('submit', 'submit', [
    'label' => 'Bewerken'
]);
