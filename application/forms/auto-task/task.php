<?

	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'auto-task-action')
			->setAttrib('class', 'auto-task-action form');

	$task = new Form_SubForm($this);
	$this->addSubForm($task, 'task');

		$task->addElement('text', 'title', array(
			'label' => 'Titel',
			'validators' => array(),
		));

		$task->addElement('textarea', 'description', array(
			'label' => 'Omschrijving',
			'rows' => 3,
			'validators' => array(),
		));

		$task->addElement('select', 'user', array(
			'label' => 'Medewerker',
			'validators' => array(),
			'multioptions' 	=> array(
				'false' => '-',
			),
		));


	$time = new Form_SubForm($this);
	$this->addSubForm($time, 'time');

		$time->addElement('text', 'offset', array(
			'label' => 'Dagen t.o.v. opleverdatum',
			'validators' => array(),
		));

		$time->addElement('select', 'repeat', array(
			'label' => 'Herhaling',
			'multioptions' 	=> array(
				'false' => '-',
				'yearly' => 'Jaarlijks',
			),
			'validators' => array(),
		));

	//submit
	$this->addElement('submit', 'save', array(
		'label' => 'Opslaan'
	));

?>