<?

//general form options
$this->setAction('')
		->set<PERSON>ethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('target', 'object-properties/edit/')
		->setAttrib('class', 'form');



$this->addElement('hidden', 'object', [
]);

$this->addElement('hidden', 'existingType', [
	'value' => 0,
]);

$this->addElement('select', 'type', [
	'label' => 'Type',
	'multiOptions' => ObjectPropertyTypes::getSelectList(),
]);

$this->addElement('text', 'value', [
	'label' => 'Waarde',
	'validators' => [
		[
			'Callback',
			true,
			'options' => [
				'callback' => function($value, $allValues) {
					$typeId = $allValues['existingType'] + $allValues['type'];
					$format = ObjectPropertyTypes::getFormatFor($typeId);

					if(empty($format))
						return false;
					else
						return $format['validator']($value);
				}
			],
		],
	],
]);

//submit
$this->addElement('submit', 'submit', [
	'label' => 'Object eigenschap opslaan',
]);

?>
