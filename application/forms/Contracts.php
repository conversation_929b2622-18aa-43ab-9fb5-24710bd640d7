<?

$contractId = isset($this->getOwner()->contractId) && intval($this->getOwner()->contractId) > 0?
	$this->getOwner()->contractId: null;

$this->getOwner()->view->contractType = 'Contracts';

//general form options
$this->setAction('contract/edit/')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$typeForm = new Form_SubForm($this);
$typeForm->setAttrib('title', 'Type contract');
$this->addSubForm($typeForm, 'type_form');

	foreach (Contracts::getTypesFiltered() as $type => $settings)
		$typeLabels[$type] = $settings['label'];

	$typeForm->addElement('select', 'type', [
		'label' => 'Type contract',
		'multiOptions'	=> (array) $typeLabels,
		'registerInArrayValidator' => false,
	]);



//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Verder'
]);

?>