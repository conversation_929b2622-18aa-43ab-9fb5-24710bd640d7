<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'category' . uniqid())
			->setAttrib('class', 'category form');

	/**
	 * General project data
	 */
	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');


		// title
		$general->addElement('select', 'component', array(
			'label' => 'Component',
			'validators' => array(),
		));		

		// title
		$general->addElement('text', 'formula', array(
			'label' => 'Formule ID',
			'validators' => array(),
		));
		
		$general->addElement('hidden', 'type', array(
			'label' => 'Type',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		$general->addElement('text', 'typeLabel', array(
			'label' => 'Type',
			'validators' => array(),
			'disabled' => true,
			'ignore' => true
		));

		$general->addElement('select', 'stairType', array(
			'label' => 'Trap type',
			'validators' => array(),
			'multiOptions' => array(
				'multiply' => 'Bedrag x verbruik',
				'no_multiply' => 'Periodiek bedrag'
			)
		));

//submit
	$this->addElement('submit', 'submit', array(
		'label' => 'Opslaan'
	));

?>