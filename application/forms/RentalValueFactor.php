<?

//general form options
$this->setAction('')
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$general = new Form_SubForm($this);
$general->setAttrib('title', 'Algemene gegevens');
$this->addSubForm($general, 'general_form');

	$general->addElement('text', 'city', [
		'label' => 'Plaats',
		'validators' => [],
	]);

	$general->addElement('select', 'object_type', [
		'label' => 'Soort vastgoed',
		'validators' => [],
        'multioptions' 	=> array(
            'particulier' => 'Particulier',
            'commercieel' => 'Commercieel'
		)
	]);

	$general->addElement('text', 'value', [
		'label' => 'Huurwaarde factor',
		'validators' => [],
	]);





//submit
$this->addElement('submit', 'aanmaken', [
	'label' => 'Factor opslaan'
]);

?>
