<?

$contractGroupId = isset($this->getOwner()->contractGroupId) && intval($this->getOwner()->contractGroupId) > 0?
	$this->getOwner()->contractGroupId: null;

//general form options
$this->setAction('contract-group/edit/id/'.$contractGroupId)
		->setMethod('post')
		->setAttrib('enctype', 'multipart/form-data')
		->setAttrib('class', 'form');


$contractGroupForm = new Form_SubForm($this);
$this->addSubForm($contractGroupForm, 'type_form');

	$contractGroupForm->addElement('text', 'name', [
		'label' => 'Naam',
		'required' => true,
	]);


//submit
$this->addElement('submit', 'submit_button', [
	'label' => 'Opslaan'
]);

?>
