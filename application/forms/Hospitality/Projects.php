<script type="text/javascript">
    window.addEvent('domready', function () {
        (function(){
            var categorySelect = $('general-category_id');
            hideAllOptionGroups();

            if (categorySelect)
                showCorrespondingOptionGroup(categorySelect.get('value'));

            multipleSelector();

            if (categorySelect)
                categorySelect.addEvent('change', function () {
                clearSelected();
                hideAllOptionGroups();
                showCorrespondingOptionGroup(this.get('value'))
            })
        }.delay(200));
    });

    function showCorrespondingOptionGroup(selectedCategory) {
        var optionGroup = $('general-products-optgroup-' + selectedCategory);

        if (optionGroup)
            optionGroup.show();
    }

    function hideAllOptionGroups() {
        $$('#general-products optgroup').hide();
    }

    function clearSelected(){
        $each($$('#general-products optgroup option'), function (option) {
            option.set('selected', false);
        });
    }

    function multipleSelector() {
        $$('#general-products option').addEvent('mousedown', function(event){
            event.preventDefault();
            this.set('selected', !this.get('selected'));
        });
    }
</script>

<?php

use FormModels\Hospitality\Projects;

$projectFormModel = new Projects();
$projectId = $this->getOwner()->getParam('project_id');
$hospitalityProjectId = $this->getOwner()->getParam('id');
$allCategories = $projectFormModel->getAllCategories($projectId, $hospitalityProjectId);

if (empty($allCategories)) {
    echo 'Er zijn geen categorieën meer beschikbaar.';
    die();
}


$this->setAction('')
    ->setAttrib('id', 'hospitalityProject')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$this->addSubForm($general, 'general');

$general->addElement('hidden', 'project_id', ['value' => $projectId]);

$general->addElement('select', 'category_id', [
    'label' => 'Categorie',
    'multioptions' => $allCategories,
    'required' => true
]);

$general->addElement('select', 'products', [
    'label' => '',
    'registerInArrayValidator' => false,
    'multiple' => 'multiple',
    'multioptions' => $projectFormModel->getAllActiveProducts(),
    'attribs' => [
        'style' => 'width: 300px; height: 300px;'
    ]
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);