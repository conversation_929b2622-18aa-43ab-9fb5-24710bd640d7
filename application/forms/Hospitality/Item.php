<?php

$itemId = $this->getOwner()->getParam('id');

$type = $this->getOwner()->type;

if ($itemId) {
    $projects = (new \DbTable\ItemProjectModel())->fetchAll(['item_id = ?' => $itemId])->toArray();
    $projects = array_column($projects, 'project_id');
}

$this->setAction('')
    ->setMethod('post')
    ->setAttrib('enctype', 'multipart/form-data')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$this->addSubForm($general, 'item');

$general->addElement('text', 'title', [
    'label' => 'Titel',
    'required' => true,
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [0, 255]
        ]
    ],
    'attribs' => [
        'style' => 'width: 232px'
    ]
]);

if ($type == 'activity') {
    $general->addElement('text', 'period_from', [
        'class' => 'DatePicker',
        'label' => 'Van datum',
        'validators' => [],
    ]);

    $general->addElement('text', 'period_till', [
        'class' => 'DatePicker',
        'label' => 'Tot datum',
        'validators' => [],
    ]);

    $general->addElement('text', 'supplier_url', [
        'label' => 'Leverancier url:',
        'validators' => [
            [
                'validator' => 'stringLength',
                'options' => [0, 100]
            ]
        ],
        'attribs' => [
            'style' => 'width: 232px'
        ]
    ]);

    $general->addElement('hidden', 'type', [
        'value' => 'activity'
    ]);
} else {
    $general->addElement('hidden', 'type', [
        'value' => 'news'
    ]);
}

$general->addElement('textarea', 'short_description', [
    'label' => 'Korte omschrijving',
    'required' => true,
    'rows' => 2,
    'cols' => 35,
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [0, 255]
        ]
    ]
]);

$general->addElement('textarea', 'description', [
    'label' => 'Omschrijving',
    'required' => true,
    'rows' => 2,
    'cols' => 35
]);

$general->addElement('file', 'image', [
    'label' => 'Afbeelding uploaden',
    'title' => 'De afbeelding moet 884px breed en 120px hoog zijn.',
    'class' => 'forceAutoHint',
    'validators' => [
        [
            'validator' => 'Extension',
            'options' => [
                'jpg', 'png', 'gif', 'jpeg', 'bmp'
            ],
        ],
    ],
]);

$general->addElement('select', 'projects', [
    'label' => 'Project',
    'multioptions' => db()->fetchPairs(db()->select()->from(['p' => 'projects'], ['id', 'name'])->order('name')->where('exploitation = ?', true)),
    'validators' => [],
    'multiple' => true,
    'registerInArrayValidator' => false,
    'attribs' => [
        'style' => 'height: 250px; width: 250px;'
    ],
    'value' => $projects
]);

$general->addElement('text', 'publish_date', [
    'class' => 'DatePicker',
//    'required' => true,
    'label' => 'Publiceer vanaf',
    'validators' => [
        [
            'validator'	=> 'Callback',
            'options' => [
                'callback' => function($value) {
                    return new DateTime($value) >= new DateTime();
                },
                'messages' => ['callbackValue' => 'Datum mag niet in het verleden liggen']
            ]
        ],
    ]
]);

$general->addElement('select', 'is_published', [
    'label' => 'Publiceer',
    'required' => true,
    'multioptions' => ['nee', 'ja'],
    'validators' => [],
    'registerInArrayValidator' => false
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);