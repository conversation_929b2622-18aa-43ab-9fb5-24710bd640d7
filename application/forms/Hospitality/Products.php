<?php

$productFormModel = new \FormModels\Hospitality\Products();

$userId = $this->getOwner()->getParam('user_id');

$this->setAction('')
    ->setAttrib('id', 'hospitalityProduct')
    ->setMethod('post')
    ->setAttrib('class', 'form');

$general = new Form_SubForm($this);
$this->addSubForm($general, 'general');

$general->addElement('hidden', 'company_id', ['value' => $productFormModel->getCompanyIdByUserId($userId)]);

$general->addElement('checkbox', 'active', [
    'label' => 'Actief',
    'attribs' => [
        'style' => 'margin-right: 30px;'
    ]
]);

$general->addElement('text', 'title', [
    'label' => 'Titel',
    'required' => true,
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [0, 100]
        ]
    ],
    'attribs' => [
        'style' => 'width: 580px'
    ]
]);

$general->addElement('select', 'category', [
    'label' => 'Categorie',
    'multioptions' => $productFormModel->getAllCategories(),
    'required' => true
]);

$general->addElement('textarea', 'short_description', [
    'label' => 'Korte omschrijving',
    'required' => true,
    'rows' => 2,
    'cols' => 35,
    'validators' => [
        [
            'validator' => 'stringLength',
            'options' => [0, 255]
        ]
    ]
]);

$general->addElement('numberInput', 'rate', [
    'label' => 'Tarief',
    'validators' => [
        [
            'regex',
            false,
            'options' => [
                'pattern' => '/(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/'
            ]
        ],
        [
            'validator' => 'GreaterThan',
            'options' => [
                'min' => -1
            ]
        ]
    ],
    'placeholder' => '0,00'
]);

$general->addElement('textarea', 'full_description', [
    'label' => 'Omschrijving',
    'validators' => [],
    'rows' => 2,
    'cols' => 70,
    'required' => true
]);


$this->addElement('submit', 'aanmaken', [
    'label' => 'Opslaan'
]);