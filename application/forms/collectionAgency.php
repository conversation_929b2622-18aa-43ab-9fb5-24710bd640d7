<?

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'collectionAgency')
			->setAttrib('class', 'collectionAgency form');



	//name
	$this->addElement('text', 'name', array(
		'label' => 'Naam',
		'required' => true
	));

	//Phone 
	$this->addElement('text', 'phone', array(
		'label' => 'Telefoon',
		'validators' => array(
			array(
				'validator' => 'Digits'
			),
			array(
				'validator' => 'stringLength',
				'options'   => array(0, 13)
			)
		)
	));

	//email
	$this->addElement('text', 'email', array(
		'label' => 'E-mail',
		'validators' => array('emailAddress'),
		'required' => true
	));
	
	$this->addElement('select', 'type', array(
		'label' => 'Type',
		'multioptions' 	=> array(
			'ca' => 'Incassobureau',
			'bailiff' => 'Deurwaarder'
		),
	));
	

	/**
	 * name data
	 */
	$user = new Form_SubForm($this);
	$this->addSubForm($user, 'user');

		//gender
		$user->addElement('select', 'gender', array(
			'label' 		=> 	'Geslacht',
			'multioptions' 	=> array(
									'male' => 'Man',
									'female' => 'Vrouw'
								),
			'validators' 	=> 	array(),
		));

		// initials
		$user->addElement('text', 'initials', array(
			'label' 		=> 	'Voorletters',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(0, 100)
				)
			)
		));


		//firstname
		$user->addElement('text', 'firstname', array(
			'label' => 'Voornaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			)
		));

		//middlename
		$user->addElement('text', 'middlename', array(
			'label' => 'Tussenvoegsel',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 20)
				)
			)
		));

		//name
		$user->addElement('text', 'name', array(
			'label' => 'Achternaam',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 100)
				)
			)
		));


	foreach(array('company', 'post') as $type){
		$address = new Form_SubForm($this);
		$this->addSubForm($address, $type . 'address');
		
		//address
		$address->addElement('text', 'street', array(
			'label' => $type == 'company' ? 'Straatnaam' : 'Straatnaam of postbus',
			'validators' => array(
				array(
				)
			)
		));

		//number
		$address->addElement('text', 'number', array(
			'label' => 'Huisnummer',
			'validators' => array(  )
		));

		//zipcode
		$address->addElement('text', 'zipcode', array(
			'label' => 'Postcode',
			'validators' => array(
				array(
					'regex',
					false,
					'options' => array(
						'pattern' => '/^[1-9]\d{3}\s{0,1}[A-Za-z]{2}$/'
					)
				)
			)
		));

		//City
		$address->addElement('text', 'city', array(
			'label' => 'Woonplaats',
			'validators' => array(
				array(
					'validator' => 'stringLength',
					'options'   => array(2, 50)
				)
			)

		));
		
	}
	
	foreach(array('p', 'c') as $type){
		$minimum = new Form_SubForm($this);
		$this->addSubForm($minimum, 'minimum_' . $type);
			
			$minimum->addElement('text', 'number', array(
				'label' => 'Aantal facturen',
				'validators' => array(
					array(
					)
				)
			));
			
			$minimum->addElement('select', 'andor', array(
				'label' 		=> 	'En / of',
				'multioptions' 	=> array(
										'and' => 'En',
										'or' => 'Of'
									),
				'validators' 	=> 	array(),
			));
			
			$minimum->addElement('text', 'amount', array(
				'label' => 'Totaal openstaand',
				'validators' => array(
					array(
					)
				)
			));	
	}

	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Incassobureau opslaan'
	));

?>