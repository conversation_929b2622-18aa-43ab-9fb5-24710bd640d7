<?php

$this->setAction('objectgroup/link-email-address')
    ->setMethod('post')
    ->setAttrib('id', 'link-email-address')
    ->setAttrib('class', 'form');



$emailValidator = new Zend_Validate_EmailAddress();

$callbackValidator = new Zend_Validate_Callback(function ($value) {
    $address = (new EmailAddress())->fetchRow(['address = ?' => $value]);
    return $address ? false : true;
});

$callbackValidator->setMessage('E-mailadres al gekoppeld', 'callbackValue');

$element = new Zend_Form_Element_Text("email_address");
$element->setLabel("E-mailadres")
    ->setValidators([$emailValidator, $callbackValidator])
    ->setRequired();

$this->addElement($element);

$select = db()->select()
    ->from(['eat' => 'email_address_types'], ['id', 'name'])
    ->where('eat.map_to = ?', 'objectgroup');

$types = db()->fetchPairs($select);

$this->addElement('select', 'email_address_type', [
    'label' => 'Type',
    'multiOptions' => $types
]);

$this->addElement('hidden', 'objectgroup_id', [
    'value' => $this->getOwner()->getParam('objectgroup')
]);

$this->addElement('submit', 'aanmaken', [
    'label' => 'Koppelen'
]);