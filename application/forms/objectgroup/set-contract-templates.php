<?php
$objectGroupId = $this->getAttrib('objectgroup_id');
$objectgroup = (new Objectgroups())->fetchRowById($objectGroupId);
$project = (new Projects())->fetchRowById($objectgroup['project']);

function handleValue($projectValue, $objectgroupValue)
{
    $value = null;

    if (!$objectgroupValue)
        $value = $projectValue;
    else if ($projectValue != $objectgroupValue)
        $value = $objectgroupValue;

    return $value ?: 0;
}

//general form options
$this->setAction('')
    ->setMethod('post')
    ->setAttrib('id', 'objectgroup')
    ->setAttrib('class', 'object form');

$templates = new Form_SubForm($this);
$this->addSubForm($templates, 'templates');

    // template for commercial contracts
    $templates->addElement('select', 'contract_template_commercial', [
        'value' => handleValue($project['contract_template_commercial'], $objectgroup['contract_template_commercial']),
        'label' => 'Commercieel',
        'multioptions' => $this->getAttrib('contractTemplatesCommercial'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);

    // template for private contracts
    $templates->addElement('select', 'contract_template_private', [
        'value' => handleValue($project['contract_template_private'], $objectgroup['contract_template_private']),
        'label' => 'Particulier',
        'multioptions' => $this->getAttrib('contractTemplatesPrivate'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);

    // template for commercial contracts in English
    $templates->addElement('select', 'contract_template_commercial_en', [
        'value' => handleValue($project['contract_template_commercial_en'], $objectgroup['contract_template_commercial_en']),
        'label' => 'Commercieel (Engels)',
        'multioptions' => $this->getAttrib('contractTemplatesCommercial'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);

    // template for private contracts in English
    $templates->addElement('select', 'contract_template_private_en', [
        'value' => handleValue($project['contract_template_private_en'], $objectgroup['contract_template_private_en']),
        'label' => 'Particulier (Engels)',
        'multioptions' => $this->getAttrib('contractTemplatesPrivate'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);


    // template for commercial contracts in English
    $templates->addElement('select', 'contract_template_commercial_fr', [
        'value' => handleValue($project['contract_template_commercial_fr'], $objectgroup['contract_template_commercial_fr']),
        'label' => 'Commercieel (Frans)',
        'multioptions' => $this->getAttrib('contractTemplatesCommercial'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);

    // template for private contracts in English
    $templates->addElement('select', 'contract_template_private_fr', [
        'value' => handleValue($project['contract_template_private_fr'], $objectgroup['contract_template_private_fr']),
        'label' => 'Particulier (Frans)',
        'multioptions' => $this->getAttrib('contractTemplatesPrivate'),
        'validators' => [],
        'registerInArrayValidator' => false,
    ]);





//submit
$this->addElement('submit', 'objectgroup', [
    'label' => 'Keuzes opslaan'
]);

// this is used to show an explanation for the user about the variables they
// can use in their template
include('ContractTemplateVariables.php');
ksort($contractTemplateVariables);
?>

<div class='pagesplit right' style='float:right;'>
    <div>
        U kunt in de template de volgende variabelen gebruiken:
        <dl style="height: 400px; overflow-y: scroll;">
            <? foreach ($contractTemplateVariables as $varName => $template) : ?>
                <? if (isset($varName) && strlen($varName) != 0) : ?>
                    <? $varName = ($template['type'] == 'ratesheet' ? $varName : '$' . $varName . '$') ?>
                    <dt style="font-weight: bold;"><?= $varName; ?></dt>
                    <dd><?= $template['userDescription']; ?></dd>
                    <br>
                <? endif; ?>
            <? endforeach; ?>
        </dl>
    </div>
</div>
