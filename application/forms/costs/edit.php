<?	

	//general form options
	$this->setAction('')
			->setMethod('post')
			->setAttrib('id', 'Costs')
			->setAttrib('class', 'Costs form');


	$general = new Form_SubForm($this);
	$this->addSubForm($general, 'general');

	$component_fetch = function($type = false) {
		$select = db()->select()
			->from('components', ['id', 'name', ])
			->where('cost_type =  "opex" OR cost_type =  "capex" ' )
			->where('deleted = ?', false)
			->order('name');

		if ($type)
			$select->where('type = ?', $type);

		return db()->fetchPairs($select);
	};

		$general->addElement('select', 'component', array(
			'label' => 'Component',
			'required' => true,
			'multioptions' 	=> [
				'Particulier' => $component_fetch('particulier'),
				'Commercieel' => $component_fetch('commercieel'),
				'Overig' => $component_fetch()
			],
		));

		$general->addElement('text', 'date', array(
			'label' => 'Boekdatum',
			'required' => true,
			'value' => date('d-m-Y')
		));

		$general->addElement('select', 'book_period', array(
			'label' => 'Boekperiode',
			'multioptions' 	=> ['month' => 'Maand', 'quarter' => 'Kwartaal',  'half-year' => 'Half jaar'   , 'year' => 'Jaar'],
			'value' => 'month'
		));

        $general->addElement('text', 'book_end_date', array(
            'label' => 'Einddatum Boekperiode t.b.v. Servicekosten',
            'required' => false,
            'attribs' => array(
				'title' => 'Indien de einddatum van deze kosten voor de servicekosten afwijkend zijn vab de boekdatum en boekperiode, vul hier de gewenste einddatum voor deze kosten in.',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			),
            'value' => NULL
        ));


		$general->addElement('text', 'payment_date', array(
			'label' => 'Betaaldatum',
			'value' => date('d-m-Y')
		));


        //total
        $general->addElement('text', 'amount_ex_vat', array(
            'label' => 'Bedrag (ex BTW)',
            'required' => (bool)(Settings::get('modules_service_charges_cost_entry_ex_vat')),
            'attribs' => array(
                'title' => 'Exclusief BTW, zoals gespecificeerd op het component',
                'class' => 'forceAutoHint',
                'hintOffset' => "{'x': 0, 'y': 0}"
            )
        ));

		//total
		$general->addElement('text', 'amount', array(
			'label' => 'Bedrag (incl BTW)',
			'required' => !(bool)(Settings::get('modules_service_charges_cost_entry_ex_vat')),
			'attribs' => array(
				'title' => 'Inclusief BTW, zoals gespecificeerd op het component',
				'class' => 'forceAutoHint',
				'hintOffset' => "{'x': 0, 'y': 0}"
			)
		));

		foreach(tax()->getRates() as $rate_level => $rate)
			$tax_rates[$rate['rate']] = $rate['label'];

		$general->addElement('select', 'tax_rate', array(
			'label' => 'BTW Tarief',
			'required' => true,
			'multiOptions' => $tax_rates
		));		


		for ($i=1; $i < 13; $i++)
			if($time = mktime(0,0,0, $i, 1))
				$months[$i] = ucfirst(strftime('%B', $time));


		for ($i=-0; $i < 10; $i++)
			if($year = date('Y', strtotime('-' . $i . ' years')))
				$years[$year] = $year;


		$general->addElement('select', 'book_month', array(
			'label' => 'Boekperiode',
			'multioptions' 	=> $months,
			'value' => date('n'),
			'validators' => array(),
		));

		$general->addElement('select', 'book_year', array(
			'label' => 'Boekjaar',
			'multioptions' 	=> $years,
		));		

		//Verdeelsleutel
		$general->addElement('select', 'rato', array(
			'label' => 'Verdeling',
			'required' => true,
			'multiOptions'	=> array('even' => 'Gelijk',
								 'size' => 'Oppervlakte (m2)',
								 'key' => 'Verdeelsleutel')
	 
		));

		$general->addElement('text', 'rato_total', array(
			'label' => 'Verdeling totaal',
			'required' => false,
		));

		$general->addElement('select', 'service_charge', [
			'label' => 'Servicekosten',
			'multiOptions' => [
				'yes' => 'Ja',
				'no' => 'Nee'
			],
		]);

        $general->addElement('text', 'fiscal_period_date', [
            'class' => 'DatePicker',
            'label' => 'Boekdatum'
        ]);

        if (Settings::get('for_third_party')) {
 			$general->addElement('checkbox', 'investor_payout', array(
				'label' => 'Beleggersuitkering',
				'value' => Settings::get('for_third_party') ? 1 : 0,
				'attribs' => array(
					'title' => 'Kosten met beleggerafrekeningen verrekenen',
					'class' => 'forceAutoHint',
					'hintOffset' => "{'x': 0, 'y': 0}"
				)
			));
		}
		

		$general->addElement('text', 'description', array(
			'label' => 'Omschrijving',
			'required' => false
		));

		//name
		$general->addElement('text', 'objects', array('hidden' => true));

	$filters = new Form_SubForm($this);
	$this->addSubForm($filters, 'filters');

        $filters->addElement('select', 'projects', [
            'label' => 'Project(en)',
            'multioptions' => (new Projects())->getActiveProjectList(),
            'validators' => [],
            'multiple' => true,
            'autocomplete' => 'off',
            'registerInArrayValidator' => false
        ]);

		$filters->addElement('button', 'filter', array(
			'label' 		=> 	'Filter',
			'validators' 	=> 	array(),
			'ignore'		=>	true
		));
	//submit
	$this->addElement('submit', 'aanmaken', array(
		'label' => 'Kosten opslaan'
	));

?>
