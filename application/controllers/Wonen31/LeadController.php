<?php

class Wonen31_LeadController extends GlobalController
{

    /**
     * http://dev4.omniboxx.johan.loc/wonen31_lead/list
     *
     * @return void
     * @throws Zend_View_Exception
     */
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Wonen31')->addCrumb('Leads');

        $this->view->assign('extraWidth', true);

        $select = db()->select()
            ->from(['wdl' => 'wonen31_desk_leads'])
            ->joinLeft(['rd' => 'rental_deals'], 'rd.id = wdl.omniboxx_lead_id', [
                'rental_deal_id' => 'id',
            ])
            ->joinLeft(['u' => 'users'], 'u.id = rd.user_id', [
                'user_id' => 'id',
                'user_rendered_name' => 'rendered_name'
            ])
            ->joinLeft(['o' => 'objects'], 'o.id = rd.object_id', [
                'object_id' => 'id',
                'object_rendered_address' => 'rendered_address'
            ])

            ->order('wdl.updated_at DESC')
        ;

        $data = db()->fetchAll($select);

        $this->view->ListView($data)
            ->setTypes([
                'id' => ['title' => 'ID', 'width' => 'xxxsmall'],
                'created_at' => ['title' => 'created_at', 'width' => 'large'],
                'rental_deal_id' => ['title' => 'Rental deal ID', 'width' => 'small'],
                'user_rendered_name' => ['title' => 'Lead name', 'width' => 'xxlarge'],
                'object_rendered_address' => ['title' => 'Object address', 'width' => 'xxxlarge'],

            ])
            ->addFormat('object_rendered_address', function ($value, $row) {
                return "<a href='object/edit-specifications/id/{$row['object_id']}'>$value</a>";
            })
            ->addFormat('object_rendered_address', function ($value, $row) {
                return "<a href='object/edit-specifications/id/{$row['object_id']}'>$value</a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }
}
