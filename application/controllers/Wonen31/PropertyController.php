<?php

class Wonen31_PropertyController extends GlobalController
{

    /**
     * http://dev4.omniboxx.johan.loc/wonen31_property/list
     *
     * @return void
     * @throws Zend_View_Exception
     */
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Wonen31')->addCrumb('Objecten');
        $this->view->assign('extraWidth', true);

        $select = db()->select()
            ->from(['wdp' => 'wonen31_desk_properties'])
            ->joinLeft(['o' => 'objects'], 'o.id = wdp.omniboxx_object_id', [
                'object_id' => 'id',
                'object_rendered_address' => 'rendered_address'
            ])

            ->order('wdp.updated_at DESC')
        ;

        $data = db()->fetchAll($select);

        $this->view->ListView($data)
            ->addLegend('list')
            ->setTypes([
                'id' => ['title' => 'ID', 'width' => 'xxxxsmall'],
                'updated_at' => ['title' => 'updated_at', 'width' => 'large'],
                'object_rendered_address' => ['title' => 'object address', 'width' => 'xxxlarge'],
//                'created_at' => ['title' => 'created_at', 'width' => 'large'],
                'property_id' => ['title' => 'Wonen31 object id', 'width' => 'small'],
                'link' => ['title' => 'Wonen31 link', 'width' => 'small'],
                'status' => ['status' => 'Statusk', 'width' => 'small'],
                'error' => ['title' => 'Error', 'width' => 'xxxxlarge'],

            ])
            ->addFormat('object_rendered_address', function ($value, $row) {
                return "<a href='object/edit/id/{$row['object_id']}'>$value</a>";
            })
            ->addFormat('error', function ($value, $row) {
                $value = str_replace('\n', '<br />', $value);
                return wordwrap($value, 150, "<br />\n");
            })

            ->addFormat('link', function ($value, $row) {
                if ($value) {
                    return "<a  target='_blank' href='{$value}'>link</a>";
                }
                return "Nog niet beschikbaar";
            })
            ->addFormat('status', function ($value, $row) {
                if (!$value) {
                    return '';
                }


                $statusList = [
                    1 => 'Beschikbaar',
                    2 => 'Onder optie',
                    3 => 'Niet meer beschikbaar',
                    4 => 'Van de markt gehaald',
                ];

                return $statusList[$value];
            })

            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }


    public function startBackgroundJobAction()
    {
        startWorker('sync-public-properties', 'cronjob_wonen-31-desk', 'direct', true);
        $this->_redirect('wonen31_property/list');
    }
}
