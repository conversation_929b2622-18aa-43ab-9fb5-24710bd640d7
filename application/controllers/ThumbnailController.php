<?

use Thumbnail\BuildCashThumbnailService;
use Thumbnail\GetCashThumbnailService;
use Thumbnail\LiveBuildThumbnailService;


class ThumbnailController extends GlobalController
{

    public function indexAction()
    {
        $this->disableView();

        $sizeType = $this->_getParam('size');
        $hash = $this->_getParam('hash');
        $rawFilePathToPhoto = $this->_getParam('file');
        $quality = $this->getParam('quality');
        $width = $this->getParam('w');
        $height = $this->getParam('h');

        $resizeType = 'resize';
        $sizes = [
            'preview' => [30, 30],
            'small' => [175, 175],
            'medium' => [250, 250],
            'large' => [600, 600],
            'full' => [1200, 1200],
        ];

        if ($sizeArr = $sizes[$sizeType]) {
            $resizeType = 'adaptive';
            $width = $sizeArr[0];
            $height = $sizeArr[1];
        }

        $options = [];

        if ($quality
            &&
            is_numeric($quality)
        ) {
            $options['jpegQuality'] = $quality;
        }

        if ($hash) {
            $getCashThumbnailService = new GetCashThumbnailService();
            try {
                $filpath = $getCashThumbnailService->execute($hash, $width, $height);
            } catch (Exception $exception) {
                try {
                    $buildCashThumbnailService = new BuildCashThumbnailService();
                    $buildCashThumbnailService->execute($hash, $width, $height, $resizeType, $options);

                    $filpath = $getCashThumbnailService->execute($hash, $width, $height);
                } catch (\Exception $exception) {
                    error_log('Thumbnail error: file not fond by hash: ' . $hash . ' ' . $exception->getMessage());
                    return;
                }
            }

            $this->showImage($filpath);
        }  else {
            try {
                $liveBuildThumbnailService = new LiveBuildThumbnailService();
                $liveBuildThumbnailService->execute($rawFilePathToPhoto, $width, $height, $resizeType, $options);
            } catch (Exception $exception) {
                error_log('Thumbnail error: ' . $exception->getMessage());
                return;
            }
        }
    }

    private function showImage($filename)
    {
        $imageInfo = getimagesize($filename);
        switch ($imageInfo['mime']) {
            case 'image/gif':
                header('Content-type: image/gif');
                $im = imagecreatefromgif($filename);
                imagegif($im);
                break;
            case 'image/jpeg':

                header('Content-Type: image/jpeg');
                $im = imagecreatefromjpeg($filename);
                imagejpeg($im);
                break;

            case 'image/png':
            case 'STRING':
                header('Content-type: image/png');
                $im = imagecreatefrompng($filename);
                imagepng($im);
                break;
            default:
                error_log('showImage type not fount: ', $imageInfo['mime']);

        }
    }
}

?>
