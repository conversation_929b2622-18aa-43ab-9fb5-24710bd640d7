<?php

use MaandlastenManager\Application\Service\CacheViewProductListDecorator;
use MaandlastenManager\Application\Service\ViewProductListService;

class MaandlastenManager_ProductController extends GlobalController
{
    public function listAction()
    {
        $userObjectId = $this->getParam('user_object_id');
        $language = $this->getParam('language');

        $service = new CacheViewProductListDecorator(
            new ViewProductListService()
        );

        $products = $service->execute($language);

        $this->view->assign('products', $products);
        $this->view->assign('userObjectId', $userObjectId);
    }
}
