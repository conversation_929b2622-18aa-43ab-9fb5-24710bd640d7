<?php

use MaandlastenManager\Application\Service\RegisterLeadRequest;
use MaandlastenManager\Application\Service\RegisterLeadService;
use MaandlastenManager\Domain\Model\Lead\LeadFactory;
use MaandlastenManager\Infrastructure\Domain\Model\House\LegacyHouseRepository;
use MaandlastenManager\Infrastructure\Domain\Model\Lead\PartnerPeteLeadRepository;
use MaandlastenManager\Infrastructure\Domain\Model\NewTenant\LegacyNewTenantRepository;

class MaandlastenManager_LeadController extends \GlobalController
{
    public function registerLeadAction()
    {
        $this->disableView();

        $userObjectId = $this->getParam('user_object_id');
        $products = $this->getParam('products', []);

        error_log('Partner Pete LeadController register lead ===============================================');
        error_log('user_object_id: ' . $userObjectId);
        error_log('products: ' . json_encode($products));

        $service = new RegisterLeadService(
            new LegacyNewTenantRepository(),
            new LegacyHouseRepository(),
            new LeadFactory(),
            new PartnerPeteLeadRepository()
        );

        try {
            $service->execute(new RegisterLeadRequest(
                $userObjectId,
                $products
            ));
        } catch (Exception $e) {
            // Should not hinder contract sign
            error_log('Partner Pete Application RegisterLeadService ========================================');
            error_log($e->getMessage());
        }
    }
}
