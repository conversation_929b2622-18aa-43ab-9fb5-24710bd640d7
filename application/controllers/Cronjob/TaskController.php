<?php



class Cronjob_TaskController extends CronjobController
{

    public function informTaskAssignedAction(){
        if (!\Settings::get('task_mail_assigned')) {
            return;
        }

        $TasksTable = new \TasksTable();

        $select = db()->select()
            ->from(['t' => 'tasks'], ['id', 'title', 'description', 'user', 'assignedBy', 'createDate', 'dueDate'])

            ->joinLeft(['cea' => 'crm_email_address'],
                'cea.map_to = t.user AND cea.type = "user"', ['address'])

            ->joinLeft(['p' => 'projects'],
                'p.id = t.project', ['project' => 'name'])

            ->joinLeft(['ip' => 'internal_projects'],
                'ip.id = t.internalproject',	['iproject' => 'name'])

            ->where('t.notified = ?', 0)
            ->where('t.user != t.assignedBy')
            ->where('t.completed = ?', 0)
            ->where('t.status = ?', 'normal')
            ->where('cea.address IS NOT NULL')
        ;

        $tasks = [];
        foreach (db()->fetchAll($select) as $task) {
            $tasks[$task['user']][] = $task;
        }

        foreach ($tasks as $user) {
            $e = new \EmailOutgoing([
                'to' => 			$user[0]['user'],
                'template' => 		'task/inform_assign.phtml',
                'subject' =>		'Nieuwe taak',
                'tasks' =>			$user
            ]);

            foreach ($user as $task) {
                $row = $TasksTable->fetchRow($TasksTable->select()->where('id = ?', $task['id']));
                $row->notified = 1;
                $row->save();
            }
        }
    }


    public function informTaskCompletedAction(){
        if (!\Settings::get('task_mail_completed')) {
            return;
        }

        $tasksTable = new \TasksTable();

        $select = db()->select()
            ->from(['t' => 'tasks'], [
                'id',
                'title',
                'description',
                'user',
                'assignedBy',
                'createDate',
                'dueDate',
                'completeDescription'
            ])

            ->joinLeft(['cea' => 'crm_email_address'],
                'cea.map_to = t.user AND cea.type = "user"', ['address'])

            ->joinLeft(['p' => 'projects'],
                'p.id = t.project', ['project' => 'name'])

            ->joinLeft(['ip' => 'internal_projects'],
                'ip.id = t.internalproject', ['iproject' => 'name'])

            ->where('t.notified != ?', 2)
            ->where('t.hiddentask != ?', 1)
            ->where('t.user != t.assignedBy')
            ->where('t.completed = ?', 1)
            ->where('t.status = ?', 'normal')
            ->where('cea.address IS NOT NULL')
        ;

        $tasks = [];
        foreach (db()->fetchAll($select) as $task) {
            $tasks[$task['assignedBy']][] = $task;
        }

        foreach ($tasks as $userid => $user) {

            $e = new \EmailOutgoing(array(
                'to' => 			$user[0]['assignedBy'],
                'template' => 		'task/inform_completed.phtml',
                'subject' =>		'Taken afgerond',
                'tasks' =>			$user
            ));

            foreach ($user as $task) {
                $row = $tasksTable->fetchRow($tasksTable->select()->where('id = ?', $task['id']));
                $row->notified = 2;

                $row->save();
            }
        }
    }

}
