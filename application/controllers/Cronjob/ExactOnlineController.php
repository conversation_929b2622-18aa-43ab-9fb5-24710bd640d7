<?php


//class Cronjob_Wonen31DeskController extends CronjobController
class Cronjob_ExactOnlineController extends CronjobController
{

    public function preDispatch()
    {
        $this->disableView();
    }

    /**
     * @Inject
     * @var \Accounting\Application\Service\SyncOutStandingItemsService
     */
    private $syncOutStandingItemsService;

    /**
     * @Inject
     * @var \Accounting\Application\Service\BruteForceSyncOutStandingItemsService
     */
    private $exactBruteForceSyncOutStandingItemsService;


    /**
     * http://dev.omniboxx.johan.loc/cronjob_exact-online/synchronize-transaction-status-invoices-by-corporation/corporation_id/56
     *
     * @return void
     */
    public function synchronizeTransactionStatusInvoicesByCorporationAction()
    {

        $corporationId = $this->getParam('corporation_id');

        if (!$corporationId) {
            error_log('Error cronjob ExactOnline synchronizeTransactionStatusInvoicesByCorporationAction corporation_id is not set');
            die('corporation_id is not set');
        }

        try {
            $corporationRow = (new \Accounting\Infrastructure\Domain\Model\Corporation\CorporationRepository(new Corporations()))->findByIdOrFail($corporationId);
        } catch (\Exception $exception) {
            error_log('Error '. $exception->getMessage());
            die( $exception->getMessage());
        }

        include 'library/PIDLock.php';
        $pid_lock = new PIDLockStatus('cronjob/exact-online/synchronize-transaction-status-invoices-by-corporation');

        if ($pid_lock->isLocked()) {
            error_log('Pid is locked. Er add to worker');
            startWorker(
                'synchronize-transaction-status-invoices-by-corporation/corporation_id/'. $corporationRow['id'],
                'cronjob_exact-online',
                'direct',
                true
            );
        }

        error_log('Start cronjob transactions import from financial export Exact Online for corporation '. $corporationRow['id'] . ' with token '. $corporationRow['financial_link_map_to']);

        $pid_lock = new PIDLock('cronjob/exact-online/synchronize-transaction-status-invoices-by-corporation');

        try {
            error_log('sync OutStanding Items Service ');
            $this->syncOutStandingItemsService->execute($corporationId);
            error_log('exact Brute Force Sync OutStanding Items Service ');
            $this->exactBruteForceSyncOutStandingItemsService->execute($corporationId);
        } catch (\Exception $exception) {
            $pid_lock->remove();
            error_log('Exact Online transactionsImportFromFinancialExport: ' . $exception->getMessage());
            die($exception->getMessage());
        }

        $pid_lock->remove();

    }
}
