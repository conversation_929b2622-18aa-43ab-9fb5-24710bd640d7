<?php


class Cronjob_FinalNotaController extends CronjobController
{
    /**
     * http://support.omniboxx.johan.loc/cronjob_final-nota/make-final-nota/uo/3035
     * @return void
     */
    public function makeFinalNotaAction(){

        $this->disableView();
        $usersobjectid = $this->_getParam('uo');
        $year = $this->_getParam('year');
        Invoice_Types_Final::generateFinalNota($usersobjectid, $year);
    }


    /**
     * http://support.omniboxx.johan.loc/cronjob_final-nota/regenerate-final-nota/invoice-id/2258960/uo/3035
     * @return void
     */
    public function regenerateFinalNotaAction(){

        $this->disableView();
        $itf = new Invoice_Types_Final($this->_getParam('uo'));
        $itf->regenerate($this->_getParam('invoice-id'),$this->_getParam('year'));

    }


}
