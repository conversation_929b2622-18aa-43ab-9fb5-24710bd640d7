<?php



class Cronjob_SchedulerController extends CronjobController
{

    /**
     * http://dev.omniboxx.johan.loc/cronjob_scheduler/update-setting-cash
     *
     * @see /scheduler.php
     * @return void
     */
    public function updateSettingCashAction()
    {

        $installationPath = '';

        $cacheFile = $installationPath . 'var/cache/settings/settings.json';
        $cacheTime = 60 * 60; // 60 min

        createFolder($cacheFile);

        if (file_exists($cacheFile) && (filemtime($cacheFile) > (time() - $cacheTime))) {
            $settingsJson = file_get_contents($cacheFile);
            $this->settingsData = json_decode($settingsJson, true);
        } else {
            $configFile = new ConfigFile($installationPath);

            $sql = "SELECT * FROM settings";
            $sth = db()->query($sql);
            $settingsData = $sth->fetchAll(PDO::FETCH_ASSOC);

            $this->settingsData = array_column($settingsData, 'value', 'name');
            $this->settingsData = $this->utf8EncodeRecursive($this->settingsData);

            $concurrentDirectory = dirname($cacheFile);
            if (!is_dir($concurrentDirectory) && !mkdir($concurrentDirectory, 0775, true)) {
                throw new \RuntimeException(sprintf('Directory "%s" was not created', $concurrentDirectory));
            }

            $jsonSettings = json_encode($this->settingsData);
            file_put_contents($cacheFile, $jsonSettings);
        }
    }

    private function utf8EncodeRecursive($data) {
        if (!is_array($data)) {
            return utf8_encode($data);
        }

        $result = [];
        foreach ((array) $data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = utf8_encode_recursive($value);
            } else {
                if (is_string($value)) {
                    if (preg_match('//u', $value)) {
                        $result[$key] = $value;
                    } else {
                        $result[$key] = utf8_encode($value);
                    }
                } else {
                    $result[$key] = $value;
                }
            }
        }

        return $result;
    }
}


class ConfigFile
{
    private $installationPath;
    private $data;

    public function __construct($installationPath)
    {
        $this->installationPath = $installationPath;
        $this->parse();
    }

    private function parse()
    {
        $this->data = parse_ini_file($this->installationPath . '/config.ini', true);
    }

    public function get($section, $name)
    {
        return isset($this->data[$section][$name]) ? $this->data[$section][$name] : null;
    }
}
