<?php


class Cronjob_EmailController extends CronjobController
{


    public function reSyncMessagesAction()
    {
        ini_set('memory_limit', '4096M');
        ini_set('max_execution_time', 0);


        $pidLock = new PIDLock('cronjob_email-re-sync-messages');

        $emailReSyncCache = new EmailReSyncCache();
        $skipEmailIds = (Array) $emailReSyncCache->load();


        $select = (new Emails())->select()
            ->where('direction = ?', 'incoming')
            ->where('date_created > ?', '2025-08-01 00:00:00')
            ->where('date_created < ?', '2025-08-26 00:00:00')
            ->where('id NOT IN ('. (implode_for_where_in($skipEmailIds)) .')')
            ->limit( 50)
            ->order('id DESC')
        ;

        $emailModelRows = (new Emails())->fetchAll($select);

        if (!count($emailModelRows)) {
            // kill Scheduler if the mail is done
            Settings::set('cronjob_re_import_email_message', false);
        }

        foreach ($emailModelRows as $emailModelRow) {
            $skipEmailIds[] = $emailModelRow->id;
            $emailReSyncCache->save($skipEmailIds);

            try {
                $this->reSyncMessageFromImap($emailModelRow->id);
            } catch (\Exception $exception) {
                error_log($exception->getMessage() .' email_id: '. $emailModelRow->id .' :: '. $exception->getTraceAsString());
            }
        }

        $pidLock->remove();
    }


    /**
     * @Inject
     * @var \EmailImport\Application\Service\FindEmailMessageService
     */
    private $findEmailMessageService;

    private function reSyncMessageFromImap($emailId)
    {
        if (!$emailId || !is_numeric($emailId)) {
            echo "Neet email id";
        }

        $emailModelRow = (new Emails())->getById($emailId);

        if (!$emailModelRow->id) {
            throw new Exception('No email fount by id:'. $emailId);
        }

        $emailAccountId = $emailModelRow->account;
        $messageId = $emailModelRow->message_id;
        $messageId = trim($messageId, '<>');

        /**
         * @var NewEmail
         */
        $newEmail = $this->findEmailMessageService->execute($emailAccountId, $messageId);

        if (!$newEmail) {
            throw new Exception('no email');
        }

        $emailModelRow->message = $newEmail->getContent();
        $emailModelRow->save();
    }

}

class EmailReSyncCache extends \Cache
{
    protected $name = 'email-re-sync';
}