<?php

use Controllers\Calendar\GetIcalService;

class Cronjob_CalendarController extends CronjobController
{
    public function buildIcalCacheAction()
    {
        $this->disableView();

        $type = $this->getParam('type');
        $link = $this->getParam('link');

        $pidLock = new PIDLock('calendar/build-ical-cache', [
            'type' => $type,
            'link' => $link,
        ]);

        foreach ([true, false] as $addTimezone) {
            $iCalCacheService = new \Controllers\Cronjob\Calendar\BuildICalCacheService(
                new Cache_Calendar(),
                new GetIcalService($addTimezone)
            );

            $iCalCacheService->execute($type, $link, $addTimezone);
        }

        $pidLock->remove();
    }
}
