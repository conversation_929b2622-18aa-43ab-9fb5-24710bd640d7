<?php

use Wonen31Desk\Infrastructure\GetAllObjectIdPubluseWebsiteService;
use Wonen31Desk\Application\SyncObjectService;
use \Wonen31Desk\Application\SyncFotosService;
use \Wonen31Desk\Application\SyncAllObjectService;

class Cronjob_Wonen31DeskController extends CronjobController
{

    public function preDispatch()
    {
        parent::preDispatch(); // TODO: Change the autogenerated stub

        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8G');
        ini_set('max_execution_time', 0);
    }

    /**
     * @Inject Wonen31Desk\Application\SyncAllObjectService
     * @var syncAllObjectService
     */
    private $syncAllObjectService;

    /**
     * http://dev4.omniboxx.johan.loc/cronjob_wonen-31-desk/sync-public-properties
     * @return void
     */
    public function syncPublicPropertiesAction()
    {
        $this->syncAllObjectService->execute();

    }


    /**
     * @Inject Wonen31Desk\Application\SyncFotosService
     * @var syncObjectService
     */
    private $syncObjectService;

    /**
     * http://dev4.omniboxx.johan.loc/cronjob_wonen-31-desk/sync-public-property/objectId/90080
     * @return void
     */
    public function syncPublicPropertyAction()
    {
        $objectId = $this->getParam('objectId');

        $this->syncObjectService->execute($objectId);

    }


    /**
     * @Inject Wonen31Desk\Application\SyncFotosService
     * @var \Wonen31Desk\Application\SyncFotosService
     */
    private $syncFotosService;


    /**
     * http://dev4.omniboxx.johan.loc/cronjob_wonen-31-desk/sync-property-photo/objectId/90080
     * @return void
     */
    public function syncPropertyPhotoAction()
    {
        $objectId = $this->getParam('objectId');
        $this->syncFotosService->execute($objectId);
    }



    /**
     * @Inject Wonen31Desk\Application\SyncFotosService
     * @var \Wonen31Desk\Application\SyncLeadsAndProfileByPropertyIdService
     */
    private $syncLeadsAndProfileByPropertyIdService;


    /**
     * http://dev4.omniboxx.johan.loc/cronjob_wonen-31-desk/sync-leads-and-profiles/objectId/301
     * @return void
     */
    public function syncLeadsAndProfilesAction()
    {
        $objectId = $this->getParam('objectId');
        $this->syncLeadsAndProfileByPropertyIdService->execute($objectId);

    }



    /**
     * @Inject Wonen31Desk\Application\SendEmailService
     * @var \Wonen31Desk\Application\SendEmailService
     */
    private $sendEmailService;

    /**
     * http://support.omniboxx.johan.loc/cronjob_wonen-31-desk/send-email
     * @return void
     */
    public function sendEmailAction()
    {
        die();

        $this->sendEmailService->execute();

    }










}
