<?php

class Cronjob_AfasController extends CronjobController {

	/**
	 * @var Controllers\Cronjob\Afas @model
	 */
	private $model;

	public function preDispatch()
	{
        if (!Settings::get('financial_export_afas_remote_debtor_management')) {
            http_response_code(403);
            die();
        }

		$this->model = new Controllers\Cronjob\Afas();

		if(!in_array($this->view->pageInfo['action'], ['status', 'fetch-and-update-single-debtor']))
			parent::preDispatch();
	}

	public function statusAction(){
		$this->view->Breadcrumbs()
			->addCrumb('Koppelingen')
			->addCrumb('AFAS koppeling')
			->addCrumb('Uitwisseling status overzicht');

		$this->view->last_sync_timestamp = Settings::get('financial_export_afas_sync_timestamp');

	}

	public function fetchAndUpdateDebtorsAction(){
		if(is_cli_call())
			$pid_lock = new PIDLock('afas-fetch-and-update-debtors');

		try {
			$this->model->fetchAndUpdateDebtors();
		} catch (Exception $e) {
			error_log($e);
		}

		if(isset($pid_lock))
			$pid_lock->remove();
	}

	public function fetchAndUpdateSingleDebtorAction(){
		$this->disableView();
		$this->model->fetchAndUpdateSingleDebtor($this->getParam('debtorid'));

		if($referer = $_SERVER['HTTP_REFERER'])
			header('Location: '. $referer);
	}

	public function postDebtorObjectsAction(){
		$this->model->postDebtorObjects();
	}

	public function postDebtorObjectsForContractAction(){
		$this->model->postDebtorObjectsForContract($this->getParam('contract'));
	}
}
