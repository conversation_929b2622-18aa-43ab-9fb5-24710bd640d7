<?php


class Cronjob_FixesController extends CronjobController
{
    /**
     *
     * http://support.omniboxx.johan.loc/cronjob_fixes/recalculate-transaction-by-invoice-Id/invoice_id/12669452/
     *
     * @return void
     * @throws Zend_Db_Adapter_Exception
     */
    public function recalculateTransactionByInvoiceIdAction()
    {
        $this->getRequest()->setControllerName('fixes_transactions');

        $this->forward('recalculate-transaction-by-invoice-id', 'fixes_transactions', null, null);

//        $controllerInstance = new Fixes_TransactionsController(
//            $this->getRequest(),
//            $this->getResponse()
//        );
//
//        $controllerInstance->recalculateTransactionByInvoiceIdAction();

    }


    /**
     *
     * http://support.omniboxx.johan.loc/cronjob_fixes/fixes_email-linking/fix-missing-links
     * @return void
     */
    public function fixMissingLinksAction()
    {
        $this->getRequest()->setControllerName('fixes_email-linking');

        $controllerInstance = new Fixes_EmailLinkingController(
            $this->getRequest(),
            $this->getResponse()
        );

        $controllerInstance->fixMissingLinksAction();

    }




}
