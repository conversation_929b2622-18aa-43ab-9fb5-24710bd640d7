<?php


class Cronjob_FinancialThirdPartyController extends CronjobController
{
    public function preDispatch()
    {
        $this->disableView();
    }

    /**
     * http://support.omniboxx.johan.loc/cronjob_financial-third-party/fix-invoice-already-been-exchanged-with-exact-online
     * @return void
     * @throws Zend_Db_Adapter_Exception
     */
    public function fixInvoiceAlreadyBeenExchangedWithExactOnlineAction()
    {
        db()->query("UPDATE invoices
            SET financial_invoice_id=id
            WHERE 
            financial_error_message LIKE('%Onderwerp [GLTransactions] Bestaat reeds - Boekstuknummer:%') 
            AND financial_invoice_id IS NULL;");
    }



    /**
     * http://support.omniboxx.johan.loc/cronjob_financial-third-party/exchange-invoice-runs-to-third-party-financial-system
     *
     * @return void
     */
    public function exchangeInvoiceRunsToThirdPartyFinancialSystemAction()
    {
        $FinancialExport = new Controllers\Invoice\FinancialExport();
        $select = $FinancialExport->getQuery()
            ->group('grouping_key');
        $data = db()->fetchAll($select);
        $data = $FinancialExport->setListStatusFromData($data);

        foreach ($data as $row) {
            if ( in_array($row['status'], ['new', 'partial'])) {

                $invoiceRunId = $row['run'];
                $userConfirmedExportFromNonProductionEnvironment = $this->getParam('userConfirmedExportFromNonProductionEnvironment');

                $url = "export-xml/id/$invoiceRunId";

                if ($userConfirmedExportFromNonProductionEnvironment) {
                    $url .= '/userConfirmedExportFromNonProductionEnvironment/yes';
                }

                startWorker($url, 'invoice', 'direct', true);
            }
        }
    }


}
