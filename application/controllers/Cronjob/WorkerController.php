<?php


class Cronjob_WorkerController extends CronjobController
{

    /**
     *
     * php /var/www/omniboxx_dev/scripts/zf-cli.php -s omniboxx_dev -a 'cronjob_worker/start/params/name=test%2Fid%2F648ae7c625453%2Fpriority%2Fdirect%2F&controller=cronjob_worker-test&priority=direct&isSerial=0' -e local >/dev/null 2>&1
     *
     * php /var/www/omniboxx_dev/_omniboxx_branch_symlink/scripts/zf-cli.php -s omniboxx_dev -a 'cronjob_worker/start/params/name=test%2Fid%2F648ae7c625453%2Fpriority%2Fdirect%2F&controller=cronjob_worker-test&priority=direct&isSerial=0' >/dev/null 2>&1
     *
     *
     * @return bool|void
     */
    public function startAction()
    {
        Logger::add(['startWorker', 'cli', 'log'], 'start Cronjob_Worker params: ' . $this->getParam('params'));

        $queryArr = [];
        parse_str($this->getParam('params'), $queryArr);

        $name = $queryArr['name'];
        $controller = $queryArr['controller'];
        $priority = $queryArr['priority'];
        $isSerial = $queryArr['isSerial'];

        $maxTries = 100;
        $currentTry = 0;

        while (true) {
            $currentTry++;
            if ($currentTry > $maxTries) {
                Logger::add(['startWorker', 'cli', 'log'], 'Max pogingen zijn overschreden van : '. $maxTries);
                return false;
            }

            if ($this->isServerBusy()) {
                sleep(10 * $currentTry);
                error_log('isServerBusy');
                Logger::add(['startWorker', 'cli', 'log'], 'Server is busy');

                continue;
            }

            try {
                $workerId = $this->startWorker($name, $controller, $priority, $isSerial);
                Logger::add(['startWorker', 'cli', 'log'], 'Aangemaakt onder id: ' . $workerId);

                return true;
            } catch (\System\WorkerQueue\Infrastructure\Domain\Service\JobAlreadyQueuedException $exception) {
                Logger::add(['startWorker', 'cli', 'log'], 'Job bestaat al in de queue');
                Logger::add(['startWorker', 'cli', 'error'], 'Job bestaat al in de queue');

                return false;

            } catch (\Exception $exception) {
                Logger::add(['startWorker', 'cli', 'log'], 'error !!!');
                Logger::add(['startWorker', 'cli', 'error'],
                    'Er was een error on try ' . $currentTry . ': ' . $exception->getMessage());

                sleep(10 * $currentTry);
            }
        }
    }

    private function isServerBusy()
    {
        if ($this->getServerLoadAverage() < 8) {
            return false;
        }

        return true;
    }

    private function getServerLoadAverage()
    {
        $systemLoadLevelData = sys_getloadavg();
        return $systemLoadLevelData[0];
    }

    private function startWorker($name, $controller = 'cronjob', $priority = 'low', $isSerial = false)
    {
        global $site_base_dir;

        $installationName = basename($site_base_dir);
        $callPath = $controller . '/' . $name;
        $origin = 'worker';

        try {
            $workerId = (new \System\WorkerQueue\Application\Service\QueueJobService())->execute(
                new \System\WorkerQueue\Application\Service\QueueJobRequest(
                    $installationName,
                    $callPath,
                    $origin,
                    $priority,
                    $isSerial
                )
            );
        } catch (\Exception $exception) {
            throw $exception;
        }

        return $workerId;
    }
}
