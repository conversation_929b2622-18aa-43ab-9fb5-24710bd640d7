<?php

use RentalPublish\Application\RentalObjectMutationsService;
use RentalPublish\Application\RentalPublishAllService;
use RentalPublish\Application\RentalQuickPublishObjectService;
use RentalPublish\Application\IsObjectRentalService;
use RentalPublish\Application\RentalPublishLightService;
use VidiiPublish\Application\IsVidiiObjectService;
use VidiiPublish\Application\IsVidiiProjectService;
use VidiiPublish\Application\VidiiPublishProjectService;
use VidiiPublish\Application\VidiiPublishObjectService;
use Vidii\Application\Service\ImportAnalyticsFileService;
use Vidii\Domain\Exception\NoNewFilesFoundException;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\AnalyticFileFactory;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\DimensionFactory;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\MetricFactory;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\ProjectFactory;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\ReportFactory;
use Vidii\Infrastructure\Domain\Factory\AnalyticFile\ReportLineFactory;
use Vidii\Infrastructure\Domain\Repository\PDOAnalyticFileRepository;
use Vidii\Infrastructure\Persist\Gateway\SaveProjectGateway;
use Vidii\Infrastructure\Persist\Gateway\SaveReportGateway;
use Vidii\Infrastructure\Service\ProdAnalyticFileProviderService;
use \RentalPublish\Infrastructure\GetAllObjectIdPubluseWebsiteService;

class Cronjob_VidiiController extends CronjobController
{
    public function importAnalyticFilesAction()
    {
        $analyticFileProvider = new ProdAnalyticFileProviderService();
        $dimensionFactory = new DimensionFactory();
        $metricFactory = new MetricFactory();
        $reportLineFactory = new ReportLineFactory($dimensionFactory, $metricFactory);
        $reportFactory = new ReportFactory($reportLineFactory);
        $productFactory = new ProjectFactory($reportFactory);
        $analyticFileFactory = new AnalyticFileFactory($productFactory);
        $saveProjectGateway = new SaveProjectGateway();
        $saveReportGateway = new SaveReportGateway();
        $analyticFileRepository = new PDOAnalyticFileRepository($saveProjectGateway, $saveReportGateway);
        $importAnalyticFilesService = new ImportAnalyticsFileService(
            $analyticFileProvider,
            $analyticFileFactory,
            $analyticFileRepository
        );

        try {
            $importAnalyticFilesService->execute();
        } catch (NoNewFilesFoundException $e) {
        } catch (Exception $e) {
            p('die', $e);
        }
    }

    public function downloadAnalyticFileAction()
    {
        $downloadFolder = "/var/www/mvgm.omniboxx.nl/_data/ga_vidii";

        $startDate = new DateTime('-2 day');
        $endData = new DateTime('-1 day');

        $from = $startDate->format('Y-m-d');
        $till = $endData->format('Y-m-d');

        $result = file_get_contents("http://mvgmanalytics.omniboxx.nl/?from=$from&till=$till");

        $filename = $endData->format('Y-m-d H:i:s') . '.json';
        file_put_contents("$downloadFolder/$filename", $result);
    }

    /**
     * http://dev.omniboxx.johan.loc/cronjob_vidii/publish-all-projects
     */
    public function publishAllProjectsAction()
    {
        $select = db()->select()
            ->from(['p' => (new Projects())->getTableName()])
            ->where('p.sales_status != ?', 'Exploitation_ended')
            ->where('p.vidii_wordpress_import_trigger_url != ?', '')
            ->where('p.vidii_wordpress_import_process_url != ?', '')
            ->where('p.vidii_wordpress_availability_trigger_url != ?', '')
            ->where('p.vidii_wordpress_availability_process_url != ?', '')
            ->where('p.vidii_enabled = ?', 'yes');
        $vidiiProjectRows = db()->fetchAll($select);

        foreach ($vidiiProjectRows as $vidiiProjectRow) {
            startWorker(
                sprintf(

                    "publish-project/projectId/%s",
                    $vidiiProjectRow['id']
                ),
                'cronjob_vidii',
                'direct',
                true
            );
        }
    }

    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-all
     */
    public function publishAllAction()
    {
        startWorker('publish-all-rental', 'cronjob_vidii', 'direct', true);
        startWorker('publish-all-projects', 'cronjob_vidii', 'direct', true);
    }


    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-object/objectId/3563
     */
    public function publishObjectAction()
    {
        $objectId = $this->getParam('objectId');

        startWorker(
            sprintf(

                "publish-rental-object/objectId/%s",
                $objectId
            ),
            'cronjob_vidii',
            'direct',
            true
        );

        startWorker(
            sprintf(

                "publish-vidii-object/objectId/%s",
                $objectId
            ),
            'cronjob_vidii',
            'direct',
            true
        );
    }

    /**
     *
     * http://support.omniboxx.johan.loc/cronjob_vidii/quick-publish-object/objectId/3563
     */
    public function quickPublishObjectAction()
    {
        $objectId = $this->getParam('objectId');

        startWorker(
            sprintf(

                "quick-publish-rental-object/objectId/%s",
                $objectId
            ),
            'cronjob_vidii',
            'direct',
            true
        );

        startWorker(
            sprintf(

                "publish-vidii-object/objectId/%s",
                $objectId
            ),
            'cronjob_vidii',
            'direct',
            true
        );
    }


    /**
     * @Inject VidiiPublish\Application\VidiiPublichProjectService
     * @var VidiiPublishProjectService
     */
    private $vidiiPublichProjectService;

    /**
     * @Inject VidiiPublish\Application\IsVidiiProjectService
     * @var IsVidiiProjectService
     */
    private $isVidiiProjectService;

    /**
     *
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-project/projectId/33
     */
    public function publishProjectAction()
    {
        $projectId = $this->getParam('projectId');
        if (!$this->isVidiiProjectService->execute($projectId)) {
            return;
        }

        $pid_lock = new PIDLock('vidii-publish-project', ['project-id' => $projectId]);
        $this->vidiiPublichProjectService->execute($projectId);
        $pid_lock->remove();
    }



    /**
     * @Inject VidiiPublish\Application\VidiiPublichObjectService
     * @var VidiiPublishObjectService
     */
    private $vidiiPublichObjectService;

    /**
     * @Inject VidiiPublish\Application\IsVidiiObjectService
     * @var isVidiiObjectService
     */
    private $isVidiiObjectService;

    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-vidii-object/objectId/3563
     */
    public function publishVidiiObjectAction()
    {
        $objectId = $this->getParam('objectId');

        if (!$this->isVidiiObjectService->execute($objectId)) {
            return;
        }

        $this->vidiiPublichObjectService->execute($objectId);
    }



    /**
     * @Inject RentalPublish\Application\RentalObjectMutationsService
     * @var RentalObjectMutationsService
     */
    private $rentalObjectMutationsService;

    /**
     * @Inject RentalPublish\Application\IsObjectRentalService
     * @var isObjectRentalService
     */
    private $isObjectRentalService;

    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-rental-object/objectId/215
     */
    public function publishRentalObjectAction()
    {
        $objectId = $this->getParam('objectId');

        if (!$this->isObjectRentalService->execute($objectId)) {
            return;
        }

        $this->rentalObjectMutationsService->execute($objectId);
    }


    /**
     * @Inject RentalPublish\Application\RentalQuickPublishObjectService
     * @var rentalQuickPublishObjectService
     */
    private $rentalQuickPublishObjectService;
    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/quick-publish-rental-object/objectId/3563
     */
    public function quickPublishRentalObjectAction()
    {
        $objectId = $this->getParam('objectId');
        if (!$this->isObjectRentalService->execute($objectId)) {
            return;
        }


        $this->rentalQuickPublishObjectService->execute($objectId);
    }


    /**
     * @Inject RentalPublish\Application\RentalPublishAllService
     * @var rentalPublishAllService
     */
    private $rentalPublishAllService;

    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-all-rental
     */
    public function publishAllRentalAction()
    {
        $this->rentalPublishAllService->execute();
    }

    /**
     * @Inject RentalPublish\Infrastructure\GetAllObjectIdPubluseWebsiteService
     * @var getAllObjectIdPubluseWebsiteService
     */
    private $getAllObjectIdPubluseWebsiteService;
    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-all-rental-one-by-one
     */
    public function publishAllRentalOneByOneAction()
    {
        $objectIds = $this->getAllObjectIdPubluseWebsiteService->execute();

        foreach ($objectIds as $objectId) {
            startWorker(
                sprintf(
                    "quick-publish-rental-object/objectId/%s",
                    $objectId
                ),
                'cronjob_vidii',
                'direct',
                true
            );
        }
    }

    /**
     * @Inject RentalPublish\Infrastructure\RentalPublishLightService
     * @var rentalPublishLightService
     */
    private $rentalPublishLightService;

    /**
     * http://support.omniboxx.johan.loc/cronjob_vidii/publish-all-rental-light
     */
    public function publishAllRentalLightAction()
    {

        $this->rentalPublishLightService->execute();
    }

}
