<?php

class AddressController extends GlobalController
{
    public function indexAction()
    {
        $this->_helper->redirector('list', 'address');
    }

    public function listAction()
    {
        $addressModel = new Address();
        $addressTypeLabels = Address::getAddressTypesForSelect();
        $data = $addressModel->getList([]);
        $data = !empty($data) && is_object($data) ? $data->toArray() : [];

        $sorting = $this->_getParam('sorting');
        if (!isset($sorting) && is_array($data) && count($data) > 0) {
            $data = array_reverse($data);
        }

        $this->view->addressListview = $this->view->listView($data)
            ->setTypes([
                'address' => [
                    'title' => 'Straatnaam',
                    'width' => 'xlarge truncate',
                ],
                'number' => [
                    'title' => 'Huisnr',
                    'width' => 'medium truncate',
                ],
                'zipcode' => [
                    'title' => 'Postcode',
                    'width' => 'medium truncate',
                ],
                'city' => [
                    'title' => 'Stad',
                    'width' => 'medium truncate',
                ],
                'address_type' => [
                    'title' => 'Type',
                    'width' => 'small truncate',
                ],
            ])
            ->addFormat('address_type', function ($value) use ($addressTypeLabels) {
                return $addressTypeLabels[$value];
            })
            ->setFilters([
                'address' => ['type' => 'input'],
                'number' => ['type' => 'input'],
                'zipcode' => ['type' => 'input'],
                'city' => ['type' => 'input'],
                'address_type' => [],
            ])
            ->setOptions([
                'item_title' => 'Adres',
            ])
            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
            ])
            ->render($this);
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Adressen overzicht', 'address/list/')
            ->addCrumb('Adres bewerken', '');

        $this->params = $params = $this->getAllUrlParams();

        $this->view->form = $this->view->EditView($this, 'Address')
            ->setOptions([
                'postHandler' => function ($post, $params) {
                    $aModel = new Address();
                    $aModel->save_return_type = 'inserted_id';
                    $newId = $aModel->save($post, $params);

                    if (empty($newId) || intval($newId) <= 0) {
                        $this->getHelper('Redirector')->goToUrl("address/list");
                    }

                    $address = $aModel->getById($newId);

                    if (empty($address) || empty($address->type) || empty($address->type_id) || !in_array($address->type, ['company',])) {
                        $this->getHelper('Redirector')->goToUrl("address/list");
                    }

                    $this->getHelper('Redirector')->goToUrl("company/edit/id/$address->type_id/");
                }
            ])
            ->render($this);
    }
}
