<?php

class KeyManagement_GroupController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbeheer');
    }

    public function indexAction()
    {
        $locationId = $this->getParam('location_id');

        $this->view->Breadcrumbs()->addCrumb('Groepen');

        $types = [
            'name' => ['title' => 'Locatie', 'width' => 'xxxxlarge'],
        ];

        $filters = [
            'name' => ['type' => 'input']
        ];

        $viewGroupService = new \KeyManagement\Group\ViewGroupService();
        $data = $viewGroupService->execute($locationId);

        $this->view->locationId = $locationId;
        $this->view->list = $this->view->ListView($data, $this->getAllParams())
            ->setTypes($types)
            ->setOptions([
                'item_title' => 'Groep',
                'disabled_buttons_test' => function () {
                    return false;
                }
            ])
            ->addButtons([
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen'
                ],
                [
                    'id' => 'id',
                    'location_id' => $locationId
                ]
            )
            ->setOptions([
                'render_to_controller' => false
            ])
            ->setFilters($filters)
            ->render($this);
    }

    public function editAction()
    {
        $locationId = $this->getParam('id');

        $crumbTitle = $locationId ? 'Bestaande groep bewerken' : 'Nieuwe groep aanmaken';
        $this->view->Breadcrumbs()->addCrumb($crumbTitle);

        $this->view->EditView($this, 'KeyManagement/Groups', [])
            ->setOptions([
                'redirect_to' => 'referer',
                'postHandler' => function ($post, $params) {

                    if ($params['id']) {
                        $editGroup = new \KeyManagement\Group\EditGroupService();
                        $editGroup->execute(new \KeyManagement\Group\EditGroupRequest(
                            $params['id'],
                            $post['group']['name']
                        ));

                        return true;
                    }

                    $createGroup = new \KeyManagement\Group\CreateNewGroupService();
                    $createGroup->execute(new \KeyManagement\Group\CreateNewGroupRequest(
                        $post['group']['location_id'],
                        $post['group']['name']
                    ));

                    return true;
                }
            ])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $deleteGroup = new \KeyManagement\Group\DeleteGroupService();
        $deleteGroup->execute(new \KeyManagement\Group\DeleteGroupRequest(
            $this->getParam('id')
        ));

        $this->_redirect('index', ['location_id' => $this->getParam('location_id')]);
    }
}