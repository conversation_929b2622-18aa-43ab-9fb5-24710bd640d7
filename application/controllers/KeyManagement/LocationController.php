<?php

use Controllers\KeyManagement\Location\Index;

class KeyManagement_LocationController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbeheer', 'key-management_location/index');
    }

    public function indexAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Locaties');

        $types = [
            'name' => ['title' => 'Locatie', 'width' => 'xxxxlarge'],
        ];

        $filters = [
            'name' => ['type' => 'input']
        ];

        $viewLocationService = new \KeyManagement\Location\ViewLocationService();
        $data = $viewLocationService->execute();

        $this->view->ListView($data)
            ->setTypes($types)
            ->addFormat('name', function ($value, $item) {
                return "<a href='key-management_keyring/index/location_id/{$item['id']}'>$value</a>";
            })
            ->setOptions([
                'item_title' => 'Locatie',
                'disabled_buttons_test' => function($row, $button){

                    if ($button == 'delete') {
                        if (Index::locationHasKeyRings($row['id']))
                             return true;

                        if (Index::locationHasGroups($row['id']))
                            return true;
                    }

                    return false;
                }
            ])
            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
                'delete' => 'Verwijderen'
            ])
            ->setFilters($filters)
            ->render($this);
    }

    public function editAction()
    {
        $locationId = $this->getParam('id');

        $crumbTitle = $locationId ? 'Bestaande locatie bewerken' : 'Nieuwe locatie aanmaken';
        $this->view->Breadcrumbs()->addCrumb($crumbTitle);

        $this->view->EditView($this, 'KeyManagement/Locations', [])
            ->setOptions([
                'redirect_to' => 'referer',
                'postHandler' => function ($post, $params) {

                    if ($params['id']) {
                        $editLocation = new \KeyManagement\Location\EditLocationService();
                        $editLocation->execute(new \KeyManagement\Location\EditLocationRequest(
                            $params['id'],
                            $post['location']['name'],
                            $post['location']['project_id']
                        ));

                        return true;
                    }

                    $createLocation = new \KeyManagement\Location\CreateNewLocationService();
                    $createLocation->execute(new \KeyManagement\Location\CreateNewLocationRequest(
                        $post['location']['name'],
                        $post['location']['project_id']
                    ));

                    return true;
                }
            ])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $deleteLocation = new \KeyManagement\Location\DeleteLocationService();
        $deleteLocation->execute(new \KeyManagement\Location\DeleteLocationRequest(
            $this->getParam('id')
        ));

        $this->_redirect('index');
    }

    public function findProjectsAction()
    {
        $this->disableView();

        $post = $this->getRequest()->getPost();

        $findProjectsService = new \KeyManagement\Location\FindProjectsService();
        $result = $findProjectsService->execute(new \KeyManagement\Location\FindProjectsRequest(
            $post['value'],
            $this->getParam('limit')
        ));

        echo json_encode($result);
    }
}