<?php

use Controllers\KeyRing\CheckOut;

class KeyManagement_KeyringController extends GlobalController
{
    private $locations = [];

    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbeheer', 'key-management_location/index');

        $viewService = new \KeyManagement\KeyRing\ViewKeyRingsService();
        $this->locations = $viewService->retrieveLocations();

        if ($locationId = $this->getParam('location_id')) {
            $this->view->Breadcrumbs()
                ->addCrumb(
                    'Locatie: ' . $this->locations[$locationId]['title'],
                    'key-management_keyring/index/location_id/' . $locationId . '/'
                );
        }
    }

    public function indexAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbossenoverzicht');


        $viewService = new \KeyManagement\KeyRing\ViewKeyRingsService();
        $data = $viewService->execute();


        if(is_null( $this->getParam('location_id'))) {
            if ((!loginManager::isProjectLimited() && count($this->locations) > 1)){
                $this->redirect('key-management_location/index');
                return;
            }
            $this->setParam('location_id', current(array_keys($this->locations)));
        }

        $locationId = $this->getParam('location_id');


        $types = [
            'object_id' => ['title' => '', 'width' => 'hidden'],
            'location_id' => ['title' => '', 'width' => 'hidden'],
            'object_address' => ['title' => 'Object', 'width' => 'xxxlarge'],
            'event_type' => ['title' => 'Status', 'width' => 'small'],
            'user' => ['title' => 'Meegegeven aan', 'width' => 'xlarge'],
            'label' => ['title' => 'Label', 'width' => 'xxsmall'],
            'identifier' => ['title' => '#Object', 'width' => 'small'],
            'actie' => ['title' => 'Actie', 'width' => 'xxxsmall'],
        ];



        $filters = [
            'location_id' => [
                'title' => 'Locatie',
                'renderSeparately' => true,
                'type' => 'select',
                'custom_options' => $this->locations,
                'custom_options_only' => true,
                'hideCount' => true,
                'show_all_disabled' => true
            ],
            'object_address' => ['type' => 'input'],
            'event_type' => [],
            'label' => [
                'title' => 'Label',
                'renderSeparately' => true,
                'type' => 'input'
            ],
            'user' => ['type' => 'input'],
            'object_id' => ['type' => 'input'],
        ];

        $this->view->locationId = $locationId;
        $this->view->list = $this->view->ListView($data, $this->getAllParams())
            ->setTypes($types)
            ->addFormat('object_address', function ($value, $item) {
                return "<a href='support/show/id/{$item['object_id']}/type/object' target='_blank'>$value </a>";
            })
            ->addFormat('event_type', function ($value) {
                return $value == 'in' || !$value ? 'Aanwezig' : 'Meegenomen';
            })
            ->addFormat('user', function ($value, $item) {
                $name = $value ? "<a href='support/show/id/{$item['user_id']}/type/user' target='_blank'>{$value}</a>" : $item['name'];
                return $item['event_type'] == 'in' ? '-' : $name;
            })
            ->addFormat('identifier', function ($value, $item) {
               $value =  $item['project_shortname'] . '.' . $item['object_identifier'];
                return $value;
            })

            ->addFormat('actie', function ($value, $item) {
                $in = "<a href='key-management_keyring/check-out-employee/location_id/{$item['location_id']}/keyring_id/{$item['id']}'<i class='fa fa-sign-out-alt tooltip' style='cursor: pointer' aria-hidden='true' title='Sleutel meenemen' rel=' '></i></a>";
                $out = "<a class='check-in' href='key-management_keyring/check-in/location_id/{$item['location_id']}/keyring_id/{$item['id']}'><i class='fa fa-sign-in-alt tooltip' style='cursor: pointer' aria-hidden='true' title='Sleutel terugbrengen' rel=' '></i></a>";
                return $item['event_type'] == 'in' || !$item['event_type'] ? $in : $out;
            })
            ->addFormat('location_id', function ($value, $item) {
                return $value > 0 ? $item['location_name'] : '-';
            })
            ->setOptions([
                'item_title' => 'Sleutelbos',
                'disabled_buttons_test' => function($row, $button){

                    if ($button == 'delete')
                        if (CheckOut::isKeyRingOut($row['id']))
                            return true;

                    return false;
                },
                'render_to_controller' => false,
                'no_ajax' => true
            ])
            ->addButtons(
                [
                    'add' => 'Toevoegen',
                    'edit' => '',
                    'delete' => ''
                ],
                [
                    'id' => 'id',
                    'location_id' => $locationId,
                    'object_id' => 'object_id'
                ]
            )
            ->setFilters($filters)
            ->render($this);
    }

    public function editAction()
    {
        $keyringId = $this->getParam('id');

        $crumbTitle = $keyringId ? 'Bestaande sleutelbos bewerken' : 'Nieuwe sleutelbos aanmaken';
        $this->view->Breadcrumbs()->addCrumb($crumbTitle);

        $this->view->EditView($this, 'KeyManagement/KeyRings', [])
            ->setOptions([
                'redirect_to' => 'referer',
                'postHandler' => function ($post, $params) {

                    if ($params['id']) {
                        $editKeyRing = new \KeyManagement\KeyRing\EditKeyRingService();
                        $editKeyRing->execute(new \KeyManagement\KeyRing\EditKeyRingRequest(
                            $params['id'],
                            $post['keyRing']['location_id'],
                            $post['keyRing']['object_id'],
                            $post['keyRing']['label'],
							$post['keyRing']['remark'],
                            $post['keyRing']['group']
                        ));

                        return true;
                    }

                    $createKeyRing = new \KeyManagement\KeyRing\CreateKeyRingService();
                    $createKeyRing->execute(new \KeyManagement\KeyRing\CreateKeyRingRequest(
                        $post['keyRing']['location_id'],
                        $post['keyRing']['object_id'],
                        $post['keyRing']['label'],
						$post['keyRing']['remark'],
                        $post['keyRing']['group']
                    ));

                    return true;
                }
            ])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $deleteKeyRing = new \KeyManagement\KeyRing\DeleteKeyRingService();
        $deleteKeyRing->execute(new \KeyManagement\KeyRing\DeleteKeyRingRequest(
            $this->_getParam('id')
        ));

        $this->_redirect('index', ['location_id' => $this->getParam('location_id')]);
    }

    private function bindCheckOutViewParams($checkoutType)
    {
        $this->view->keyringId = $this->getParam('keyring_id');
        $this->view->locationId = $this->getParam('location_id');
        $this->view->checkoutType = $checkoutType;
    }

    public function checkOutEmployeeAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbos checkout - medewerker');

        $this->bindCheckOutViewParams('employee');

        $keyringId = $this->getParam('keyring_id');

        $form = new Form($this, 'KeyManagement/CheckOutEmployee');

        if (CheckOut::isKeyRingOut($keyringId))
            die('deze sleutel bos is al buitenshuis');

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {

            $post = $this->getRequest()->getPost();

            $checkOut = new \KeyManagement\KeyRing\CheckOut\CheckOutEmployeeService();
            $checkOut->execute(new \KeyManagement\KeyRing\CheckOut\CheckOutEmployeeRequest(
                $keyringId,
                $post['check_out_employee']['user_id'],
                loginManager::data()->id
            ));

            $this->redirect("key-management_keyring/show-label/keyring_id/{$keyringId}");
        }

        $this->view->form = $form;
    }

    public function checkOutTenantAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbos checkout - huurder');

        $this->bindCheckOutViewParams('tenant');

        $keyringId = $this->getParam('keyring_id');

        $form = new Form($this, 'KeyManagement/CheckOutTenant');

        if (CheckOut::isKeyRingOut($keyringId))
            die('deze sleutel bos is al buitenshuis');

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {

            $post = $this->getRequest()->getPost();

            $checkOut = new \KeyManagement\KeyRing\CheckOut\CheckOutTenantService();
            $checkOut->execute(new \KeyManagement\KeyRing\CheckOut\CheckOutTenantRequest(
                $keyringId,
                $post['check_out_tenant']['user_id'],
                loginManager::data()->id
            ));

            $this->redirect("key-management_keyring/show-label/keyring_id/{$keyringId}");
        }

        $this->view->form = $form;
    }

    public function checkOutTechnicalContactAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbos checkout - technische partij');

        $this->bindCheckOutViewParams('technical-contact');

        $keyRingId = $this->getParam('keyring_id');

        $form = new Form($this, 'KeyManagement/CheckOutTechnicalContact');

        if (CheckOut::isKeyRingOut($keyRingId))
            die('deze sleutel bos is al buitenshuis');

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {

            $post = $this->getRequest()->getPost();

            $checkOut = new \KeyManagement\KeyRing\CheckOut\CheckOutTechnicalContactService();
            $checkOut->execute(new \KeyManagement\KeyRing\CheckOut\CheckOutTechnicalContactRequest(
                $keyRingId,
                $post['check_out_technical_contact']['user_id'],
                loginManager::data()->id
            ));

            $this->redirect("key-management_keyring/show-label/keyring_id/$keyRingId");
        }

        $this->view->form = $form;
    }

    public function checkOutThirdPartyAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Sleutelbos checkout - derde partij');

        $this->bindCheckOutViewParams('third-party');

        $keyRingId = $this->getParam('keyring_id');

        $form = new Form($this, 'KeyManagement/KeyringThirdParty');

        if (CheckOut::isKeyRingOut($keyRingId))
            die('deze sleutel bos is al buitenshuis');

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {

            $post = $this->getRequest()->getPost();

            $checkOut = new \KeyManagement\KeyRing\CheckOut\CheckOutThirdPartyService();
            $checkOut->execute(new \KeyManagement\KeyRing\CheckOut\CheckOutThirdPartyRequest(
                $post['third_party']['keyring_id'],
                $post['third_party']['company_name'],
                $post['third_party']['person_name'],
                $post['third_party']['phone'],
                loginManager::data()->id
            ));

            $this->redirect("key-management_keyring/show-label/keyring_id/{$post['third_party']['keyring_id']}");
        }

        $this->view->form = $form;
    }

    public function showLabelAction()
    {
        $keyRingId = $this->getParam('keyring_id');

        $viewService = new \KeyManagement\KeyRing\ViewShowLabelService();
        $row = $viewService->execute($keyRingId);

        $this->view->label = $row['label'];
		$this->view->remark = $row['remark'];
        $this->view->locationId = $row['location_id'];
        $this->view->groupName = $row['group_name'];
    }

    public function checkInAction()
    {
        $keyRingId = $this->getParam('keyring_id');
        $locationId = $this->getParam('location_id');

        if (!CheckOut::isKeyRingOut($keyRingId))
            die('deze sleutel bos is al binnenshuis');

        $checkInKeyRing = new \KeyManagement\KeyRing\CheckKeyRingInService();
        $checkInKeyRing->execute(new \KeyManagement\KeyRing\CheckKeyRingInRequest(
            $keyRingId,
            loginManager::data()->id
        ));

        $this->redirect("key-management_keyring/index/location_id/$locationId");
    }

    public function createPdfAction()
    {
        $this->disableView();

        $createPdf = new \KeyManagement\KeyRing\CreatePdfService();
        $createPdf->execute(new \KeyManagement\KeyRing\CreatePdfRequest(
            $this->getParam('company_name'),
            $this->getParam('person_name'),
            $this->getParam('phone'),
            $this->getParam('keyring_id')
        ));
    }

    public function findObjectsAction()
    {
        $this->disableView();

        $post = $this->getRequest()->getPost();

        $findObjects = new \KeyManagement\KeyRing\FindObjectsService();
        $result = $findObjects->execute(new \KeyManagement\KeyRing\FindObjectsRequest(
            $post['value'],
            $this->getParam('limit')
        ));

        echo json_encode($result);
    }

    public function findEmployeesAction()
    {
        $this->disableView();

        $post = $this->getRequest()->getPost();

        $findEmployee = new \KeyManagement\KeyRing\CheckOut\FindEmployeesService();
        $result = $findEmployee->execute(new \KeyManagement\KeyRing\CheckOut\FindEmployeesRequest(
            $post['value'],
            $this->getParam('limit')
        ));

        echo json_encode($result);
    }

    public function findTechnicalContactsAction()
    {
        $this->disableView();

        $post = $this->getRequest()->getPost();

        $findTechnicalContacts = new \KeyManagement\KeyRing\CheckOut\FindTechnicalContactsService();
        $result = $findTechnicalContacts->execute(new \KeyManagement\KeyRing\CheckOut\FindTechnicalContactsRequest(
            $post['value'],
            $this->getParam('limit')
        ));

        echo json_encode($result);
    }

    public function findTenantsAction()
    {
        $this->disableView();

        $post = $this->getRequest()->getPost();

        $findTenants = new \KeyManagement\KeyRing\CheckOut\FindTenantsService();
        $result = $findTenants->execute(new \KeyManagement\KeyRing\CheckOut\FindTenantsRequest(
            $post['value'],
            $this->getParam('limit')
        ));

        echo json_encode($result);
    }
}
