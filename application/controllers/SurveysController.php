<?php

use application\models\Surveys\Application\Request\WebHookEventRequest;
use application\models\Surveys\Application\Service\Setting\AvailableTemplatesService;
use application\models\Surveys\Application\Service\Setting\SaveSettingService;
use application\models\Surveys\Application\Service\Setting\ViewSettingsService;
use application\models\Surveys\Application\Service\Sync\SyncSurveyTemplatesService;
use application\models\Surveys\Application\Service\WebHookEventService;

class SurveysController extends GlobalController
{
    function overviewTechnicalContactsAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Enquêtes')->addCrumb('Service partij overzicht');

        if (!$this->_getParam('from')) $this->_setParam('from', date('Y-m-d', strtotime('-1 year')));
        if (!$this->_getParam('till')) $this->_setParam('till', date('Y-m-d'));

        $from = date('Y-m-d', strtotime($this->_getParam('from')));
        $till = date('Y-m-d', strtotime($this->_getParam('till')));

        $request = new \application\models\Surveys\Application\Request\OverviewTechnicalContactsRequest();
        $request->setFrom($from);
        $request->setTill($till);

        $service = new \application\models\Surveys\Application\Service\OverviewTechnicalContactsService();
        $data = $service->execute($request);

        $types = [
            'project_id' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'dummy_user' => ['title' => 'Technische partij', 'width' => 'xxlarge'],
            'general_avg_score' => ['title' => 'Waardering', 'width' => 'xlarge'],
            'invite_count' => ['title' => 'Aantal', 'width' => 'xxxsmall'],
            'details' => ['title' => 'Gedetailleerd', 'width' => 'small']


        ];

        $filters = [
            'from' => ['renderSeparately' => true, 'title' => 'Begindatum', 'type' => 'date', 'preApplied' => true],
            'till' => ['renderSeparately' => true, 'title' => 'Einddatum', 'type' => 'date', 'preApplied' => true],
            'project_id' => ['type' => 'input'],
            'dummy_user' => ['type' => 'input'],
            'general_avg_score' => ['type' => 'range', '']
        ];

        $this->view->ListView($data)
            ->setTypes($types)
            ->addFormat('project_id', 'project_details_link')
            ->addFormat('dummy_user', 'user_support_link')
            ->addFormat('general_avg_score', function ($value) {
                if ($value < 3) {
                    $color = "#C9302C";
                } elseif ($value >= 3 && $value < 3.5) {
                    $color = "#f7d438";
                } else {
                    $color = "#8DC43F";
                }

                $stars = [];

                for ($i = 1; $i <= 5; $i++) {
                    if ($value >= 1) {
                        $stars[] = "<i class='fa fa-star fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 1;
                    } elseif ($value >= 0.5) {
                        $stars[] = "<i class='fa fa-star-half-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 0.5;
                    } else {
                        $stars[] = "<i class='fa fa-star-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                    }
                }

                return implode('', $stars);

            })
            ->addFormat('details', function () {
                return '<i class="fa fa-bar-chart" aria-hidden="true"></i>';
            })
            ->setFilters($filters)
            ->render($this);
    }

    function overviewSupportAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Enquêtes')->addCrumb('Klantenservice overzicht');

        if (!$this->_getParam('from')) $this->_setParam('from', date('Y-m-d', strtotime('-1 year')));
        if (!$this->_getParam('till')) $this->_setParam('till', date('Y-m-d'));

        $from = date('Y-m-d', strtotime($this->_getParam('from')));
        $till = date('Y-m-d', strtotime($this->_getParam('till')));

        $request = new \application\models\Surveys\Application\Request\OverviewSupportRequest();
        $request->setFrom($from);
        $request->setTill($till);

        $service = new \application\models\Surveys\Application\Service\OverviewSupportService();
        $data = $service->execute($request);

        $types = [
            'user' => ['title' => 'Medewerker', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'general_avg_score_email' => ['title' => 'Mail afhandeling', 'width' => 'large'],
            'invite_count_email' => ['title' => 'Aantal', 'width' => 'xxxsmall'],
            'details_email' => ['title' => 'Gedetailleerd', 'width' => 'xlarge'],
            'general_avg_score_phone' => ['title' => 'Telefonische afhandeling', 'width' => 'large'],
            'invite_count_phone' => ['title' => 'Aantal', 'width' => 'xxxsmall'],
            'details_phone' => ['title' => 'Gedetailleerd', 'width' => 'small']
        ];

        $filters = [
            'from' => ['renderSeparately' => true, 'title' => 'Begindatum', 'type' => 'date', 'preApplied' => true],
            'till' => ['renderSeparately' => true, 'title' => 'Einddatum', 'type' => 'date', 'preApplied' => true],
            'user' => ['type' => 'input'],
            'general_avg_score_email' => ['type' => 'range', ''],
            'general_avg_score_phone' => ['type' => 'range', '']
        ];

        $this->view->ListView($data)
            ->setTypes($types)
            ->addFormat('user', 'buildname')
            ->addFormat('dummy_user', 'user_support_link')
            ->addFormat('general_avg_score_email', function ($value) {
                if ($value < 3) {
                    $color = "#C9302C";
                } elseif ($value >= 3 && $value < 3.5) {
                    $color = "#f7d438";
                } else {
                    $color = "#8DC43F";
                }

                $stars = [];

                for ($i = 1; $i <= 5; $i++) {
                    if ($value >= 1) {
                        $stars[] = "<i class='fa fa-star fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 1;
                    } elseif ($value >= 0.5) {
                        $stars[] = "<i class='fa fa-star-half-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 0.5;
                    } else {
                        $stars[] = "<i class='fa fa-star-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                    }
                }

                return implode('', $stars);

            })
            ->addFormat('details_phone', function () {
                return '<i class="fa fa-bar-chart" aria-hidden="true"></i>';
            })
            ->addFormat('general_avg_score_phone', function ($value) {
                if ($value < 3) {
                    $color = "#C9302C";
                } elseif ($value >= 3 && $value < 3.5) {
                    $color = "#f7d438";
                } else {
                    $color = "#8DC43F";
                }

                $stars = [];

                for ($i = 1; $i <= 5; $i++) {
                    if ($value >= 1) {
                        $stars[] = "<i class='fa fa-star fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 1;
                    } elseif ($value >= 0.5) {
                        $stars[] = "<i class='fa fa-star-half-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                        $value -= 0.5;
                    } else {
                        $stars[] = "<i class='fa fa-star-o fa-2x' style='color: $color; padding-right: 2px' aria-hidden='true'></i>";
                    }
                }

                return implode('', $stars);

            })
            ->addFormat('details_email', function () {
                return '<i class="fa fa-bar-chart" aria-hidden="true"></i>';
            })
            ->setFilters($filters)
            ->render($this);
    }

    function detailsAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/chart/alpha/Chart.js');
    }

    function settingsAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Enquêtes')->addCrumb('Instellingen');

        $service = new ViewSettingsService();
        $response = $service->execute();

        $this->view->extraWidth = true;
        $this->view->last_retrieve_date = $service->getLastRetrieveDate();
        $this->view->settings = $response;
    }

    function settingsPostAction()
    {
        $params = $this->getAllParams();

        if ($params['event_id']) {
            $saveSettingService = new SaveSettingService();
            $saveSettingService->execute(
                $params['event_id'],
                $params['frequency'],
                $params['template'],
                $params['send_threshold']
            );
        }

        $this->redirect('surveys/settings');
    }

    function syncTemplatesAjaxAction()
    {
        $this->disableView();

        $service = new SyncSurveyTemplatesService();

        try {
            $response = $service->execute();

            $comment = 'Nieuwe enquête sjablonen opgehaald.';

            // log user
            Journal::getForLinks([
                [
                    'type' => 'user',
                    'role' => 'commentor',
                    'map_to' => loginManager::data()->id,
                ],

            ])->record($comment);

            echo json_encode($response);

        } catch (Exception $e) {
            bootstrap_show_error_page($e);
            ob_clean();
            http_response_code(503); // service not available
        }
    }

    function settingsAvailableTemplatesAjaxAction()
    {
        $this->disableView();

        $service = new AvailableTemplatesService();
        $response = $service->execute();

        echo json_encode($response);
    }

    function webHookAction()
    {
        $this->disableView();

        $request = $this->getRequest();
        $body = $request->getRawBody();

        $postData = json_decode($body, true);

        // uncomment for testing with Postman
        // $postData = $this->getAllParams();

        if (is_numeric($postData['object_id'])) {

            $request = new WebHookEventRequest(
                $postData['name'],
                $postData['event_type'],
                $postData['event_id'],
                $postData['object_type'],
                $postData['object_id'],
                $postData['event_datetime']
            );

            $webHookEventListener = new WebHookEventService();

            try {
                $webHookEventListener->execute($request);
            } catch (Exception $e) {

                $uniqueId = uniqid();

                // place the exception in the error log prepended with the unique id
                $message = (is_object($e) && strpos(get_class($e), 'Exception') !== false) ? $e->getMessage() : 'Caught error was not an exception';
                $traceString = (is_object($e) && strpos(get_class($e), 'Exception') !== false) ? $e->getTraceAsString() : 'Caught error was not an exception';

                error_log("[$uniqueId] WebHookAction caught Exception ({$_SERVER['REQUEST_URI']}) with message: $message");
                error_log("[$uniqueId] WebHookAction caught Exception ({$_SERVER['REQUEST_URI']}) with stacktrace: $traceString");

                $data = [
                    'error_type' => 'webhook',
                    'unique_id' => $uniqueId,
                    'error_message' => 'Er is een algemene fout opgetreden.'
                ];

                $emailAccount = new \EmailAccount();
                $from = $emailAccount->getDefault();

                $address = [
                    'to' => [
                        'email' => $from->address,
                        'name' => 'Foutmelding Enquêtes',
                    ]
                ];

                $options = [
                    'from' => ['name' => $from->name, 'email' => $from->address],
                    'to' => $address['to'],
                    'data' => $data,
                    'subject' => 'Er is een fout opgetreden bij het ophalen van een enquête antwoord',
                    'template' => 'surveys/error.phtml',
                ];

                $emailOutgoing = new \EmailOutgoing($options);
            }
        } else {

            $uniqueId = uniqid();

            error_log("[$uniqueId] WebHookAction validation Error ({$_SERVER['REQUEST_URI']})");
            error_log("[$uniqueId] WebHookAction incoming data: " . json_encode($postData));

            $data = [
                'error_type' => 'webhook',
                'unique_id' => $uniqueId,
                'error_message' => 'Er is een algemene fout opgetreden.'
            ];

            $emailAccount = new \EmailAccount();
            $from = $emailAccount->getDefault();

            $address = [
                'to' => [
                    'email' => $from->address,
                    'name' => 'Foutmelding Enquêtes',
                ]
            ];

            $options = [
                'from' => ['name' => $from->name, 'email' => $from->address],
                'to' => $address['to'],
                'data' => $data,
                'subject' => 'Er is een fout opgetreden bij het ophalen van een enquête antwoord',
                'template' => 'surveys/error.phtml',
            ];

            $emailOutgoing = new \EmailOutgoing($options);
        }
    }
}
