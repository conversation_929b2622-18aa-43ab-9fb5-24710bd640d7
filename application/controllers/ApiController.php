<?php

use Controllers\Api\CheckVidiiCode;
use Controllers\Api\Data\WebsiteService;
use Controllers\Api\GetRentalProfile;
use Controllers\Api\Post\MakeRentalDealService\MakeRentalDealServiceFactory;
use Controllers\Api\RegisterLead\InformLeadService;
use Controllers\Api\RentalWebsiteLogger;
use Controllers\Api\SetRentalProfile;
use Settings\GetApiSettingsService;

class ApiController extends GlobalController {

		public $debug = true;

		public function preDispatch() {
			$this->disableView();

			$this->a = new Api();
			$this->config = 'website';
			$this->callback = $this->_getParam('callback');

            // Temporary block due to ticket #17898, to be removed if cause of that ticket is clear of <PERSON><PERSON> uses Vidii
            $action = $this->_getParam('action');
            if ($action === 'post' && Settings::get('general_company_shortname') === 'ravel') {
                $this->renderHeader('401 Unauthorized');
            }

			if ($this->actionRequiresHashCheck()) {
				if (!$this->checkHash() && (!$this->_getParam('debug') || !DEBUGGING)) {
					$this->renderHeader('401 Unauthorized');
				}
			}
		}

		private function actionRequiresHashCheck()
		{
			return in_array($this->_getParam('action'), [
				'config',
				'force-refresh',
			]);
		}

        public function checkEmailAddressExistsAction()
        {
            $post = $this->getRequest()->getPost();

            $where = ['address = ?' => $post['email_address']];
            $emailAddress = new CRMEmailAddress();
            $email = $emailAddress->fetchRow($where);

            if ($email)
                $this->render(['success' => true]);

            $this->render(['success' => false]);
		}

		public function filesAction()
        {
            $userObjectId = $this->getParam('user_object_id', false);
            $rentalDealsId = $this->getParam('rental_deals_id', false);
            $userId = $this->getParam('user_id', false);

            $post = $this->getRequest()->getPost();

            if (empty($_FILES) || (!is_numeric($userObjectId) && !is_numeric($rentalDealsId) && !is_numeric($userId))) {
                error_log(
                	'Mvgm/Vidii -> Registerform: call succeeded, but files upload failed.'
					. ' - user_object_id ' . $userObjectId
					. ' - rental_deals_id ' . $rentalDealsId
					. ' Files array: ' . json_encode($_FILES)
				);
                $this->render(['success' => false]);
            }

            error_log(
            	'Mvgm/Vidii -> Registerform -> post: ' . json_encode($post)
				. ' - user_object_id: ' . $userObjectId
				. ' - rental_deals_id ' . $rentalDealsId
				. ' - files:' . json_encode($_FILES)
			);

            $filesModel = new \Controllers\Api\Files();
            if (!$userId) {
                $userId = $filesModel->getUserId($userObjectId, $rentalDealsId);
            }


            foreach ($_FILES as $fileName => $fileInfo) {
                // get file name + extension
                $name = $post[$fileName . 'name'];

                $filePath = $filesModel->makeFilePath($name, $userId);

                createFolder($filePath);

                $filesModel->moveUploadedFile($filePath, $fileInfo);

                $categoryName = $filesModel->makeCategoryName($fileName);

                $titleRow = $filesModel->getCategoryOrCreate($categoryName, $fileName);

                list($documentId, $documentRow) = $filesModel->getDocumentOrCreate($titleRow, $userId, $filePath, $fileName);

                $version = $filesModel->getVersionOrCreate($documentId, $documentRow, $filePath);
            }

            $isSuccess = isset($version) && is_numeric($version) ? true : false;
            $this->render(['success' => $isSuccess]);
        }

		public function registerLeadAction()
		{
            /**
             * This action is called from rental websites, not from vidii sites
             */

		    $postData = $this->getRequest()->getPost();

            RentalWebsiteLogger::instance()->log(
                'api/register-lead',
                'Request',
                $postData
            );

			$this->registerLead(
                $postData,
				function ($user) {
					$userRow = $user['user'];
					if (is_object($userRow)) {
						$userRow->profile_completion = 9;
						$userRow->save();
					}
					return (array)$user;
				}
			);
		}

    public function registerLeadCompanyAction()
    {
        /**
         * This action is called from rental websites, not from vidii sites
         */

        $postData = $this->getRequest()->getPost();

        RentalWebsiteLogger::instance()->log(
            'api/register-lead-company',
            'Request',
            $postData
        );

        $this->registerLeadCompany(
            $postData,
            function ($user) {
                $userRow = $user['user'];
                if (is_object($userRow)) {
                    $userRow->profile_completion = 9;
                    $userRow->save();
                }
                return (array)$user;
            }
        );
    }



		public function registerLeadValidateFormAction()
		{
			/** @noinspection PhpUndefinedMethodInspection */
            $postData = $this->getRequest()->getPost();

			$validationErrors = $this->registerLeadValidateForm($postData);

			if (!empty($validationErrors)) {
                RentalWebsiteLogger::instance()->log(
                    'api/register-lead-validation-form',
                    'Request',
                    $postData
                );

                RentalWebsiteLogger::instance()->log(
                    'api/register-lead-validation-form',
                    'Validation errors',
                    $validationErrors
                );
            }

			CORSHelper::outputJSONResponse(
				$validationErrors,
				((count($validationErrors) > 0)? 400: 200)
			);
		}

		public function postAction(){
			// weten welke batch dit is voor direct object user of eerst user en dan object user
			if(!($name = $this->_getParam('name')) || !$this->getRequest()->isPost()) return false;

			$post_data = $this->getRequest()->getPost();
			if(!empty($post_data['uoId']) && trim($post_data['uoId']) != false && !is_numeric($post_data['uoId']))
				$post_data['uoId'] = preg_filter('/[^0-9]/', '', (string) $post_data['uoId']);

			if($name == 'add_potention_tenant'){
				Objectusers::addPotentionTenantFromWebsite($post_data, true);
			}

			if ($name == 'add_potential_tenant') {
                \Logger::add(['vidii', 'add_tenant'], 'strart add_potential_tenant');
                /**
                 * This action is called from vidii websites, not from rental sites
                 */
				error_reporting(E_ERROR);
				$objSelectDisabled = Settings::get('modules_tenantLogin_roomselector_no_object_selection');

				$addAsRentalDealService = (new MakeRentalDealServiceFactory())->build(
					MakeRentalDealServiceFactory::CREATE_VIDII_RENTAL_DEAL,
					MakeRentalDealServiceFactory::REGISTER_NEW_USER_FOR_DEAL,
					MakeRentalDealServiceFactory::ALLOW_ONLY_IF_PROJECT_REGISTRATION_IS_AS_RENTAL_DEAL
				);

                if (Settings::get('modules_vidii_register_leads_english')) {
                    $post_data['language'] = 'en';
                }

				if ($objSelectDisabled) {
					$this->registerLead($post_data, function ($user) {
						return (array)$user;
					});

				} elseif ($addAsRentalDealService->tenantShouldBeRegisteredAsDeal($post_data)) {
					try {
						$output = $addAsRentalDealService->execute($post_data);

                        \Logger::add(['vidii', 'add_tenant'], 'rentalDealsId:'. $output['rental_deals']['id']);
						$this->render([
							'success' => true,
							'rentalDealsId' => $output['rental_deals']['id'],
						]);
					} catch (Exception $e) {
                        \Logger::add(['vidii', 'add_tenant'], 'Vidii Api::post add_potential_tenant: '. $e->getMessage());
						$this->render([
							'success' => false,
						]);
					}
				} else {
					$output = (array) Objectusers::addPotentionTenantFromWebsite($post_data, false);

					$uoId = intval($output['users_objects']['id']) > 0? intval($output['users_objects']['id']) : false;


					if (Settings::get('general_company_shortname') === 'MVGM'){
						$hostname = ObjectsAlert::getUpdateLoftsHostnameForObject($output['object']['id']);
                        startWorker("update-lofts-worker/hostname/$hostname");
                    }
                    \Logger::add(['vidii', 'add_tenant'], 'UserObjectId: '. $uoId);

                    $this->render(array(
						'success' => !!$uoId,
						'uoId' => $uoId,
					));
				}
			}

			if ($name == 'set_appointment_date') {
				$objSelectDisabled = Settings::get('modules_tenantLogin_roomselector_no_object_selection');

				if(intval($post_data['uoId']) > 0 && intval($post_data['startStamp']) > 0) {
					try {
						if($objSelectDisabled) {
							$userId = intval($post_data['uoId']);
							$user = Users::get($userId);

						} else {
							$uoId = intval($post_data['uoId']);
							$uo = Objectusers::get($uoId);
							$user = Users::get($uo->customer);
						}

						if(isset($user) && is_object($user)) {
							$user->appointment_start = date('Y-m-d H:i:s', intval($post_data['startStamp']));

							if(trim($post_data['cancelUrl']) != false && filter_var($post_data['cancelUrl'], FILTER_VALIDATE_URL))
								$user->appointment_cancel_url = $post_data['cancelUrl'];

							$savedUserId = $user->save();
						}
						
					} catch (Exception $e) {
						$this->render(array(
							'success' => false,
							'error' => 'got exception when finding/modifying user',
						));
					}

					$this->render(array(
						'success' => ( isset($savedUserId) && intval($savedUserId) > 0 ),
					));

				} else {
					$this->render(array(
						'success' => false,
						'error' => 'users_objects id or startStamp not set',
					));
				}
			}

			if ($name == 'confirm_potential_tenant') {

				if(intval($post_data['data']) > 0) {
					$objSelectDisabled = Settings::get('modules_tenantLogin_roomselector_no_object_selection');
					$uoId = intval($post_data['data']);
				} else {
					$this->render(array(
						'success' => false,
						'error' => 'No users_objects id',
					));
				}

				if($objSelectDisabled) {
					$userId = $uoId;
				} else {
					$uo_row = Objectusers::get($uoId);
					$userId = $uo_row->customer;
				}
				

				if($userId && intval($userId) >= 0) {
					$user = Users::get($userId);
				} else {
					$this->render(array(
						'success' => false,
						'error' => 'No (valid) users_objects/user found',
					));
				}

				// if the user was not saved yet, do so now
				if($user && $user->profile_completion == 9) {

					if($objSelectDisabled) {
						$user->profile_completion = 0;
						$userId = $user->save();

 						if(Settings::get('general_company_shortname') === 'elfprov') {
							$addr = array(
								'from' => array(
									'name' => 'De Elf Provincien',
									'email' => '<EMAIL>'
								),
								'cc' => array(
									'name' => 'De Elf Provincien',
									'email' => '<EMAIL>'
								)
							);

						} else {
							$addr = array(
								'from' => array(
									'email' => Settings::get('general_email'),
									'name' => 'Klantenservice - '.Settings::get('general_company'),
								),
								'cc' => array(
									'email' => Settings::get('general_email'),
									'name' => 'Klantenservice - '.Settings::get('general_company'),
								)
							);
						}
						$emailParams = [
							'to' => $user->id,
							'data' => $user,
							'cc' => $addr['cc'],
							'from' => $addr['from'],
							'subject' => $user->language == 'nl' ?
								'Bevestiging van je registratie' :
								'Registration confirmed',
							'template' => 'subscriptions/confirmpayment_nl.phtml',
							'user' => $user,
						];

						if ($objSelectDisabled && intval($user->corporation) > 0
							&& $project = Projects::get($user->corporation)) {
							$emailParams['project'] = $project;
							$user->corporation = NULL;
							$user->save();
						}

						new EmailOutgoing($emailParams);

						$loyalty4G = new Loyalty4G($userId);
						$loyalty4G->rewardUser(Loyalty4G_Object_RewardType::REGISTER);

						$this->render(array(
							'success' => true,
						));

					} else if($this->_getParam('registration_type') == 'noLogin'){
						$language = $user->language;
						$address = Address::get($uo_row->object, 'object');

						$user_lib = new User();
						$user_lib->user = $user->id;

						$prefix = $language == 'en'? 'Rental contract': 'Huurovereenkomst';
						$filename = $prefix . ' ' . $address['address'] . ' ' .  $address['number'];
						$contract_id = $user_lib->addContract($uo_row->id, $filename);
						$user_lib->signContract($contract_id);

						$user->first = 0;
						$user->save();
						$invoice = new Invoice_Types_First();
						$invoice->setObject($uo_row->object);
						$invoice->setUser($uo_row->customer);
						$invoice->create();
						$invoice->save();
						$invoice->send();
						$invoice->setPayed();
					}

					// Need to have project and corporation settings for dynamic confirmation and according mail box and invoice settings.

					$project = Objects::getProject($uo_row->object);
 					$from = Email::getMailaccountForSend($project['corporation'],$project['id']);

					$language = in_array($user->language, ['nl', 'en'])? $user->language: 'nl';

					if ( $project['vidii_enabled'] == 'yes' ) {
						if ( $user->has( 'email_address' ) && filter_var( $user->email_address->address, FILTER_VALIDATE_EMAIL ) ) {
							$subject = ( $language == 'en' ) ? 'Registration confirmed' : 'Inschrijving bevestigd';
							new EmailOutgoing( [
								'to'       => $user->id,
								'from'     => [ 'name' => $from['name'], 'email' => $from['address'] ],
								'subject'  => $subject,
								'template' => "subscriptions/confirm_mvgm_{$language}.phtml",
								'data'     => [
									'name'    => User::buildname( false, $uo_row->customer ),
									'address' => Address::buildname( $uo_row->object, 'object' ),
									'language' => $language,
								],
							] );
						}

					} else {
						$appointmentStamp = strtotime($user['appointment_start']);
						if(intval($appointmentStamp) <= 0 || !isValidTimeStamp($appointmentStamp)) {
							if($language == 'nl') {
								$subject = 'Hartelijk dank voor u inschrijving!';
							} else {
								$subject = 'Thanks for your registration!';
							}

						} else {
							if($language == 'nl') {
								$subject = 'Kennismakingsgesprek en code persoonlijke pagina';
							} else {
								$subject = 'Access your personal page';
							}
						}

						new EmailOutgoing( [
							'to'       => $user->id,
							'from'     => [ 'name' => $from['name'], 'email' => $from['address'] ],
							'subject'  => $subject,
							'data'     => $user,
							'project'  => $project,
							'project_id' => $project->id,
							'code'     => $uo_row->code,
							'template' => "students/confirm_{$language}.phtml",
						] );
					}

					$notemplate = $this->_getParam( 'registration_type' ) == 'noLogin' ?
						'<h2>Nieuwe internationale aanmelding</h2>'
						.'<p><a href="http://'.$_SERVER['HTTP_HOST'].'/support/show/id/'.$user->id.'/type/user#naw">'
						.User::buildname(false, $user->id).'</a> heeft zich aangemeld en '
						.'de inschrijvingskosten betaald.'
						:

						'<h2>Nieuwe aanmelding</h2>'
						.'<p><a href="http://'.$_SERVER['HTTP_HOST'].'/support/show/id/'.$user->id.'/type/user#naw">'
						.User::buildname(false, $user->id).'</a> heeft zich aangemeld, een afspraak gekozen en '
						.'de inschrijvingskosten (via iDeal) betaald. De afspraak is op '
						.date('d-m-Y', strtotime($user->appointment_start)).' om '
						.date('H:i', strtotime($user->appointment_start)).'<br>'
						.'</p>'
					;

					$subject = 'Nieuwe gebruiker aangemeld via de website - ' . $project['roomselector_projectname'];

					if(trim($project['shortname'])) {
						$subject = '['.$project['shortname'].'] '.$subject;
					}

					new EmailOutgoing( [
						'to'         => [
							'email' => $from['address'],
							'name'  => $from['name'],
						],
						'from'       => [
							'name'  => $from['name'],
							'email' => $from['address'],
						],
						'subject'    => $subject,
						'notemplate' => $notemplate,
					] );

					// Vidii enabled projects follow a different registration process
					if ( $project['vidii_enabled'] == 'yes' ) {
						$user->profile_completion = 1;
					} else {
						$user->profile_completion = 0;
					}
					$userId = $user->save();

                    if (Settings::get('modules_tenantLogin_roomselector_manually_control_availability')) {
                        $ol_model = new ObjectsAlert();
                        if ($ol_model->matchRow(['object_id' => $uo_row->object])) {
                            $ol_model->updateStatus($uo_row->object, 'done');
                        }
                    }

					$loyalty4G = new Loyalty4G($userId);
					$loyalty4G->rewardUser(Loyalty4G_Object_RewardType::REGISTER);

					if(Settings::get('general_company_shortname') === 'bhw') {
						file_get_contents('http://smartcitylofts.nl/update_lofts');
					}

					$this->render(array(
						'success' => true,
					));

				} else {
					$this->render(array(
						'success' => false,
						'error' => 'User already saved',
					));
				}
			}

			if ($name == 'get_contract_url') {
				if(intval($post_data['uo_id']) > 0) {
					$uoId = intval($post_data['uo_id']);
				} else {
					$this->render(array(
						'success' => false,
						'error' => 'No users_objects id',
					));
				}

				$uo_row = Objectusers::get($uoId);

				if($uo_row && $uo_row->customer != 0) {
					// assemble the parts of the url, beginning with the base
					$url = 'http://'.$_SERVER['HTTP_HOST'].Zend_Controller_Front::getBaseUrl() .'/user/public-contract';

					// users_objects id
					$url .= '/uoid/'.$uo_row->id;

					// time this link should be active
					$time = time() + 60 * 30;
					$url .= '/time/'.$time;

					// hash to secure none of the above can be changed without invalidating the url
					$hash = sha1($uo_row->id.'_'.$time.'_'.Settings::get('general_company_shortname').'_omniboxx');
					$url .= '/hash/'.$hash;

					// we also need to return the amount the user has to pay
					$invoice = new Invoice_Types_First();
					$invoice->setObject($uo_row->object);
					$invoice->setUser($uo_row->customer);
					$invoice->create();
					$total = $invoice->getTotal();

					$this->render(array(
						'success' => true,
						'amount' => $total,
						'url' => $url,
					));
				} else {
					$this->render(array(
						'success' => false,
						'error' => 'No (valid) users_objects found',
					));
				}
			}

			if($name == 'available_objects_per_objectgroup') {

				if($post_data['objectgroups'] && $objectgroupIds = json_decode($post_data['objectgroups']))
					if(is_array($objectgroupIds) && count($objectgroupIds) > 0) {
						$output = [];

						foreach ($objectgroupIds as $objectgroupId)
							$output[$objectgroupId] = [
								'id' => $objectgroupId,
								'availables' => 0,
							];

						if(Settings::get('modules_tenantLogin_roomselector_manually_control_availability')) {
							$rows = db()->fetchAll(db()->select()->from(['o' => 'objects'], '*')
								->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['objectgroup_id' => 'id'])
								->where('og.id IN ('.(implode_for_where_in($objectgroupIds)).')')
							);

							foreach ($rows as $row)
								if(ObjectsAlert::currentStatus($row['id']) == 'available')
									$output[$row['objectgroup_id']]['availables'] = $output[$row['objectgroup_id']]['availables'] + 1;

						} else {

							foreach ($objectgroupIds as $objectgroupId) {
								foreach((array) Object::getForWebsite( $objectgroupId, false ) as $object) {
									if($object['status'] == 'empty')
										$output[$objectgroupId]['availables'] = ($output[$objectgroupId]['availables'] + 1);
								}
							}
						}

						$this->render([
							'success' => true,
							'output' => $output,
						]);
					}

				$this->render([
					'success' => false,
					'error' => 'No (valid) ids found',
				]);
			}
		}
		
		public function forceRefreshAction(){
			$this->renderHeader('304 Not Modified');
		}

		public function authorizeAction(){
			$this->render($this->a->authorize($this->_getParam('location')));
		}

		public function configAction(){
			$api = new Api();
	
			$config = array(
				'config' => array(
					'verbose' => true,
					'debug' => $this->debug,
				)
			);

			if(sha1(json_encode($config)) == $this->_getParam('hash'))
				$this->render(304);
			
			if($this->_getParam('debug'))
				p($config, 'die');
			
			ob_clean();
			$this->render(array_merge_recursive_distinct(array('hash' => sha1(json_encode($config))), $config));
		}
		
		
		public function getSettingsAction(){
			echo 'var settings = ' . json_encode(utf8_encode_recursive((new GetApiSettingsService())->execute())) . ';';
		}

		public function dataAction() {
			$service = new WebsiteService(
				new ObjectsAlert(),
				new Object(),
				new Objects()
			);

			$data = $service->execute($this->getAllParams());

			if (!$data) {
				return;
			}

			if (sha1(json_encode($data)) === $this->_getParam('hash')) {
				$this->render(304);
			}

			ob_clean();
			$this->render(array_merge_recursive_distinct(['hash' => sha1(json_encode($data))], $data));
		}	


		public function registrantsForWebsiteAction() {
			if(!http_authenticate()) return;
 			$db = db();

			$select = $db->select()
				->from (array('u' => 'users'),        array('*'))
				->where('u.type = ? ', 'registrant')
				->joinLeft(array('cea'  => 'crm_email_address'), 'cea.map_to  = u.id', array(
					'email'   => 'address'
				))
			;

			$rows = $db->fetchAll($select);
 			foreach ($rows  as $row) {
 					$row['username'] = $row['firstname'];
 					$created = strtotime($row['created_on']);
 					if( ($created > 1447088400 && $created < 1451347200) || $created > 1452601563)
 						$row['username'] = ($row['username'].'_'.$row['id']);

					$row['password'] = sha1($row['name']);
					$result[] = $row;
				}	
			$xml = new SimpleXMLElement("<root/>");
			data2XML($result, $xml, "Registrant");

			if(Settings::get('general_company_shortname') === 'bhw') {
				ob_clean();
				header('Content-Type: text/xml');
			}
			echo $xml->asXML();
		}

		public function objectsForWebsiteInterhouseAction()
		{
			if(!http_authenticate()) return;

			$doc = new DOMDocument('1.0');
			$doc->encoding = 'UTF-8';
			$doc->formatOutput = true; // we want a nice output


			// FMPXMLRESULT
			$root = $doc->createElement('FMPXMLRESULT');
			$root->setAttribute('xmlns', 'http://www.filemaker.com/fmpxmlresult');
			$root = $doc->appendChild($root);


			// ERRORCODE
			$title = $doc->createElement('ERRORCODE');
			$title = $root->appendChild($title);

			$text = $doc->createTextNode(0);
			$text = $title->appendChild($text);


			// PRODUCT
			$title = $doc->createElement('PRODUCT');
			$title->setAttribute('BUILD', '07-18-2011');
			$title->setAttribute('NAME', 'FileMaker');
			$title->setAttribute('VERSION', 'Pro 11.0v4');
			$title = $root->appendChild($title);


			// DATABASE
			$title = $doc->createElement('DATABASE');
			$title->setAttribute('DATEFORMAT', 'D-m-yyyy');
			$title->setAttribute('LAYOUT', ' ');
			$title->setAttribute('NAME', 'Findus MV');
			$title->setAttribute('RECORDS', '6718');
			$title->setAttribute('TIMEFORMAT', 'k:mm:ss ');
			$title = $root->appendChild($title);


			// METADATA
			$root = $doc->createElement('METADATA');
			$root = $doc->appendChild($root);

			// FIELD Code office
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'Code office');
			$title->setAttribute('TYPE', 'NUMBER');
			$title = $root->appendChild($title);

			// FIELD Code object
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'Code object');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD C status nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C status nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD C status en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C status en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD datum beschikbaar
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'datum beschikbaar');
			$title->setAttribute('TYPE', 'DATE');
			$title = $root->appendChild($title);

			// FIELD C huurprijs
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C huurprijs');
			$title->setAttribute('TYPE', 'NUMBER');
			$title = $root->appendChild($title);

			// FIELD C gwe
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C gwe');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD tekst nl groot
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'tekst nl groot');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD tekst nl klein
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'tekst nl klein');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD tekst en groot
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'tekst en groot');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD tekst en klein
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'tekst en klein');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD straat
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'straat');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD huisnr
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'huisnr');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD toevoeging
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'toevoeging');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD postcode
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'postcode');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD plaats
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'plaats');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD telefoonnr
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'telefoonnr');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD C nieuw nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C nieuw nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD C nieuw en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C nieuw en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD soort woning txt nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'soort woning txt nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD soort woning txt en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'soort woning txt en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD soort woning nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'soort woning nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);


			// FIELD aanbodnaam nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'aanbodnaam nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD aanbodnaam en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'aanbodnaam en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD extra info en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'extra info en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD extra info nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'extra info nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD beknopt nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'beknopt nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD beknopt en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'beknopt en');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD gestoffeerd nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'gestoffeerd nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD gemeubileerd nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'gemeubileerd nl');
			$title->setAttribute('TYPE', 'TEXT');
			$title = $root->appendChild($title);

			// FIELD kamers
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'kamers');
			$title->setAttribute('TYPE', 'NUMBER');
			$title = $root->appendChild($title);

			// FIELD slaapkamers
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'slaapkamers');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD woonoppervlak
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'woonoppervlak');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD borgsom
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'borgsom');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD url stad
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'url stad');
			$title->setAttribute('TYPE', 'TEXT');

			// FIELD ov nivo
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'ov nivo');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD P nivo
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'P nivo');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD lok nivo
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'lok nivo');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD kind nivo
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'kind nivo');
			$title->setAttribute('TYPE', 'NUMBER');

			// FIELD status bemiddeling
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'status bemiddeling');
			$title->setAttribute('TYPE', 'TEXT');

			// FIELD C_bemiddelingstekst_nl
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C_bemiddelingstekst_nl');
			$title->setAttribute('TYPE', 'TEXT');

			// FIELD C_bemiddelingstekst_en
			$title = $doc->createElement('FIELD');
			$title->setAttribute('EMPTYOK', 'YES');
			$title->setAttribute('MAXREPEAT', '1');
			$title->setAttribute('NAME', 'C_bemiddelingstekst_en');
			$title->setAttribute('TYPE', 'TEXT');


			// get All published objects
			$objectsForWebsiteInterhouse = new \application\models\Controllers\Api\ObjectsForWebsiteInterhouse();
			$objectsData = $objectsForWebsiteInterhouse->getAllPublishedObjectsData();


			// RESULTSET
			$root = $doc->createElement('RESULTSET');
			$root->setAttribute('FOUND', $objectsData['found']);
			$root = $doc->appendChild($root);

			foreach ($objectsData['rows'] as $row) {

				// ROW
				$title = $doc->createElement('ROW');
				$title->setAttribute('MODID', '2970');
				$title->setAttribute('RECORDID', $row['object_id']);
				$title = $root->appendChild($title);

				foreach ($row['cols'] as $value) {

					// COL
					$col = $doc->createElement('COL');
					$col = $title->appendChild($col);

					// DATA
					$data = $doc->createElement('DATA');
					$data = $col->appendChild($data);

					$text = $doc->createTextNode($value);
					$text = $data->appendChild($text);
				}
			}

			echo "Saving all the document:\n";
			echo $doc->saveXML() . "\n";

		}

		public function objectsForWebsiteAction() {

			if(!http_authenticate()) return;
			
			$db = db();

			$project = $this->_getParam('project');
			$project = (isset($project) && intval($project) > 0)? $project: 10;

			$select = $db->select()
				
				->from (array('obj' => 'objects'),        array(
					'object_id' => 'id',
					'm2',
					'build',
					'number_occupants',
					'WOZ',
					'deposit'
				))		
				->joinLeft(array('ot' => 'object_type'),     'ot.id = obj.type',     array(
					'type_object'=>'name' 
				))

				->joinLeft(array('og'  => 'objectgroup'), 'og.id  = obj.objectgroup', array(
					'type',
					'objectgroup_id'   => 'id',
					'og_descr'   => 'description',
				))
				->joinLeft(array('p'  => 'projects'), 'p.id = og.project', array(
					'project_id' => 'id',
					'project' => 'name'
				))
				->joinLeft(array('adr' => 'address'),     'adr.type = \'object\' AND adr.type_id = obj.id', array(
					'street'       => 'address',
					'house_number' => 'number',
					'zipcode',
					'city'

				))
				
				->where('og.type = ? ', 'particulier')
				->where('p.id = ? ', $project)
 				->order('adr.address')
				->order('CAST(adr.number AS UNSIGNED)')
				->order('adr.number')
				->order('og.description')
				->order('CAST(obj.build AS UNSIGNED)')
				->group('obj.id');
			;

		 
			$rows = $db->fetchAll($select);

			$availabilityInfo = [];
			foreach ($rows as $row) {
				
				$selectuo = $db->select()
					->from(array('uo'=>'users_objects'), array('from', 'till', 'customer','finaldate','contract')
					)
					->where('uo.role = ?', 'normal')
					->where('uo.object = ?', $row['object_id'])
					->order('uo.id DESC')
					->limit(1)					;
				
				$uorow = $db->fetchRow($selectuo);

				// if we did not retrieve+sort the availability info yet, do so now
				if(!is_array($availabilityInfo[$row['objectgroup_id']])) {
					$avInfo = [];
					foreach(Object::getForWebsite($row['objectgroup_id'], false) as $object)
						$avInfo[$object['id']] = $object;

					$availabilityInfo[$row['objectgroup_id']] = $avInfo;
				}

				// map the availability statusses to the expected output values
				switch($availabilityInfo[$row['objectgroup_id']][$row['object_id']]['status']) {
					case 'empty':
					case 'available':
						$row['status'] = 'available';
						$row['available_from'] =  $uorow['from'] != '' ? $uorow['from']  : '2015-09-01';
						break;

					case 'occupied':
						$row['status'] = 'rented';
						break;

					case 'optional':
						$row['status'] = 'reserved';
						break;

					default:
						$row['status'] = 'unknown';
				}

				// special snowflake exception for MVGM: they wanted only a PART of the project removed from
				// the roomselector/Vidii site. This prevents Vidii from displaying an incorrect available
				// amount of objects.
				if(Settings::get('general_company_shortname') == 'MVGM' && $project == 4
					&& in_array($row['objectgroup_id'], [59, 61, 62, 63, 64])) {
					$row['status'] = 'rented';
				}

				$oc_version_model = new ObjectsComponentsVersions();
				$version = $oc_version_model->getHighestForObject($row['object_id']);
		
				$invoiceamounts = array();
				// get all values for this version
				$db = Zend_Db_Table::getDefaultAdapter();

				$select = db()->select()
						->from(array('ocv'=>'objects_components_values'), array('value_excl','value_incl','version'))
						->joinLeft(array('c' => 'components'), 'c.id = ocv.component', array('id','name'))
						->where('ocv.version = ?', $version['id'])
						->where('ocv.object = ?', $row['object_id'])
						->where('ocv.value_excl > 0  OR ocv.value_excl < 0');
				$invoiceamounts = db()->fetchAll($select);	

 				$row['invoiceamounts'] = (array) $invoiceamounts;
			
				$photos = array();
				$select = db()->select()
					->from(array('ph'=>'photos'), array('photo'=>'filename'))
					->where('ph.type = ? ', 'object')
					->where('ph.map_to = ?', $row['object_id']);
					$photos = db()->fetchAll($select);
				$row['photos'] =  $photos;


				$p_id = 'project_'.$row['project_id'];
				$og_id = 'objectgroup_'.$row['objectgroup_id'] . '_object_'.$row['object_id'];

				if(!isset($filled[$p_id]) || !is_array($filled[$p_id]))
					$filled[$p_id] = array();

				if(!isset($filled[$p_id][$og_id]) || !is_array($filled[$p_id][$og_id]))
					$filled[$p_id][$og_id] = array();

				$row['m2'] = (round(floatval($row['m2'])) == floatval($row['m2']))? round(floatval($row['m2'])): $row['m2'];

				$filled[$p_id][$og_id] = $row;
 				
 				
 			}
 			$xml = new SimpleXMLElement("<root/>");
			data2XML($filled, $xml, "Objectitem");

			if(Settings::get('general_company_shortname') === 'bhw') {
				ob_clean();
				header('Content-Type: text/xml');
			}
			echo $xml->asXML();
		}

		/**
		 * @param mixed $value
		 */
		public function render($value){
			echo $this->callback . '(' . json_encode($value) . ')';
			die();
		}

		public function checkHash(){
			if(!$this->_getParam('authorizeHash') && ((!$this->_getParam('debug') && !DEBUGGING) && !$this->_getParam('location')))
				return false;
				
			$al = new ApiLocations();
			
			if($this->_getParam('authorizeHash')){
				$row = $al->fetchRow($al->select()->where('hash = ?', $this->_getParam('authorizeHash')));
			} else {
				$row = $al->fetchRow($al->select()->where('name = ?', $this->_getParam('location')));				
			}
			
			if(!$row)
				return false;
			
			$this->location = $row->id;
			$this->config = $row->config;
			$this->debug = $row->debug;
			return true;
		}

		public function renderHeader($header){
			$this->getResponse()->setRawHeader('HTTP/1.0 ' . $header);
		}

		private function registerLead($post_data, callable $postUserAddHandler)
		{

		    $errors = $this->registerLeadValidateForm($post_data);

			if(isset($errors['phone1'])){
				if ($errors['phone1']) {
					$errors['mobile_number'] = $errors['phone1'];
					unset($errors['phone1']);
				}
			} else {
				if ($errors['phone2']) {
					$errors['mobile_number'] = $errors['phone2'];
					unset($errors['phone2']);
				}
			}

			if ($errors['email'] && !empty($post_data['email'])) {
				$errors['email_not_unique'] = $errors['email'];
				unset($errors['email']);
			}

			if (Settings::get('modules_rental')) {
				list($post_data, $rentalFields)
					= Controllers\Api\Post\AddPotentialTenant::formatRentalSubscriptionFields($post_data);
			}

			// Return errors if there are any
			if (!empty($errors)) {
				// Log unsuccesful registration to the error log
				$errorPostDate = $post_data;
				$errorPostDate['hopibon_password'] = sha1($errorPostDate['hopibon_password']);
				$errorPostDate['hopibon_confirm_password'] = sha1($errorPostDate['hopibon_confirm_password']);
				error_log('Roomselector register form: unsuccessful registration, form data: '
					. json_encode($errorPostDate));

				$this->render([
					'success' => false,
					'errors' => $errors
				]);
			}


			if(!isset($post_data['phone1'])){
				$post_data['phone1'] = $post_data['phone2'];
				unset($post_data['phone2']);
			}

			if (intval($post_data['hidden_project_id']) > 0 && $project = Projects::get($post_data['hidden_project_id'])) {
				$post_data['corporation'] = $post_data['hidden_project_id'];
			}

			if ($post_data['form_type'] == 'company') {
                //refactordata  to save default company form
                $post_data['company_form']['name'] = $post_data['company'];
                $post_data['company_form']['phone_primary'] = $post_data['phone1'];
                $post_data['company_form']['email_address'] = $post_data['email'];
                $post_data['comment_form']['comment'] = $post_data['website']. nl2br(" Medewerkers: ") .$post_data['employees'];
                $post_data['address_form']['address_street'] = $post_data['street'];
                $post_data['address_form']['address_number'] = $post_data['addressnr'];
                $post_data['address_form']['address_zipcode'] = $post_data['zip'];
                $post_data['address_form']['address_city'] = $post_data['city'];
                $c_model = new Company();
                $companyId = $c_model->save($post_data);
            }

			$userRow = \Users::addPotentialTenantFromWebsite($post_data);
			$output = $postUserAddHandler($userRow);

            if ($post_data['form_type'] == 'company') {
                $company = $c_model->getById($companyId);
                $company->contractor_id = $userRow['user']['id'];
                $company->save();

                $empModel = new Employee();
                $contractor = $empModel->createRow(array(
                    'role' 		=> 'contractor',
                    'company' 	=> $company->id,
                ));
                $contractor->user = $userRow['user']['id'];
                $contractor->save();
            }


			$userId = (isset($output['user']) && isset($output['user']['id']) && intval($output['user']['id']) > 0) ?
				intval($output['user']['id']) : false;

			if (Settings::get('modules_rental') && $userId && !empty($rentalFields)) {
				Controllers\Api\Post\AddPotentialTenant::saveRentalModuleSpecificFields($userId, $rentalFields);
			}

            if (
                Settings::get('modules_rental') &&
                $userId &&
                isset($post_data['living_situation'])
            ) {
                $setRentalProfileService = new SetRentalProfile(
                    new Users()
                );

                $params = $post_data;
                $params['user_id'] = $userId;
                unset($params['rent_price']);


                $setRentalProfileService->execute($params);
            }

			if ($userId) {
				// add successful registration to the error log
				$errorPostDate = $post_data;
				$errorPostDate['hopibon_password'] = sha1($errorPostDate['hopibon_password']);
				$errorPostDate['hopibon_confirm_password'] = sha1($errorPostDate['hopibon_confirm_password']);
				error_log('Roomselector register form: successful registration, form data: '
					. json_encode($errorPostDate));
			}

			if (Settings::get('general_company_shortname') === 'HB' && intval($userId) > 0) {
				$loyalty4G = new Loyalty4G($userId);
				$loyalty4G->rewardUser(Loyalty4G_Object_RewardType::REGISTER);
			}


            $addr = [
                'to' => [
                    'email' => Settings::get('general_email'),
                    'name' => 'Klantenservice - ' . Settings::get('general_company'),
                ]
            ];

			/*
			new EmailOutgoing([
				'to' => $addr['to'],
				'data' => $post_data,
				'subject' => 'Voorlopige inschrijving',
				'template' => 'subscriptions/confirm_nl.phtml',
			]);
			*/

            $shouldCheckBranchLocation =
                Settings::get('modules_rental') &&
                Settings::get('modules_user_project') &&
                ($output['user']['branch_location'] > 0 );

            if ($shouldCheckBranchLocation) {
                    $emailAccountsModel = new EmailAccounts();
                    $branchelocationsModel = new BranchLocations();
                    $branchelocationRow = $branchelocationsModel->getById($output['user']['branch_location']);

                    if (is_numeric($branchelocationRow['mailaccount'])) {
                        $emailAccountRow = $emailAccountsModel->getById($branchelocationRow['mailaccount']);
                    }
            }
            
            $mailaccount = (is_numeric($emailAccountRow['id']) ? $emailAccountRow['id']  : Settings::get('modules_rental_website_default_email_account'));

			$informLeadService = new InformLeadService(
				Settings::get('modules_rental_website_enabled'),
                $mailaccount,
				new Users(),
				new EmailAccount(),
				new CRMEmailAddress()
			);

			if ($informLeadService->leadShouldBeInformed()) {
				$informLeadService->execute(['userId' => $userId]);
			}

			// NOTE: the userId var is passed back as uoId te maintain compatibility with the roomselector
			// the id is only used as a unique id for the iDeal transaction so this should cause no problems

            $returnData = [
                'success' => !!$userId,
				'uoId' => $userId
            ];

			if (!$userId) {
			    $returnData['errors'] = 'Lead is door interne validatie niet aangemaakt';
			}
			
			$this->render($returnData);
		}

		private function registerLeadValidateForm($post_data)
		{
			$errors = [];

			if(isset($post_data['phone1'])){
				if (empty($post_data['phone1'])) {
					$errors['phone1'] = 'Het Mobiele telefoonnummer is een verplicht veld';
					$errors['phone2'] = 'Het Mobiele telefoonnummer is een verplicht veld';
				}
			} else {
				if (empty($post_data['phone2'])) {
					$errors['phone2'] = 'Het Mobiele telefoonnummer is een verplicht veld';
				}
			}

			if (empty($post_data['email'])) {
				$errors['email'] = 'Het E-mailadres is een verplicht veld';

			} elseif (Settings::get('general_company_shortname') !== 'livable') {
				$validate = new ValueObject_UniqueEmailAddress($post_data['email']);
				if (!$validate->isUnique()) {
					$errors['email'] = 'Het opgegeven e-mailadres is al bekend in ons systeem.';
				}
			}


            if (
                isset($post_data['partner']) &&
                isset($post_data['partner']['email']) &&
                Settings::get('general_company_shortname') !== 'livable'
            ) {
                $validate = new ValueObject_UniqueEmailAddress($post_data['partner']['email']);
                if (!$validate->isUnique()) {
                    $errors['partner']['email'] = 'Het opgegeven e-mailadres is al bekend in ons systeem.';
                }
            }

			if (isset($post_data['password'])) {
                if (empty($post_data['password'])) {
                    $errors['password'] = 'Het wachtwoord is een verplicht veld';
                }

                if (!preg_match(Users::PASSWORD_PATTERN, $post_data['password'])) {
                    $errors['password'] = 'Uw wachtwoord voldoet niet aan de wachtwoord vereisten. Het wachtwoord moet 8 tekens lang zijn en tenminste een kleine letter, hoofdletter en een cijfer bevatten.';
                }
            }

            if (isset($post_data['username'])) {
                if (!empty($post_data['username'])
                    && (new Users())->hasMatchingRow(['username' => $post_data['username']])) {
                    $errors['username'] = 'Deze gebruikersnaam is al bekend in ons systeem.';
                }
			}

			return $errors;
		}

		public function makeRentalDealAction()
		{
			/** @noinspection PhpUndefinedMethodInspection */
			$post_data = $this->getRequest()->getPost();

            RentalWebsiteLogger::instance()->log(
                'api/make-rental-deal',
                'Request',
                $post_data
            );

			$makeRentalDealService = (new MakeRentalDealServiceFactory())->build(
				MakeRentalDealServiceFactory::CREATE_WEBSITE_RENTAL_DEAL,
				MakeRentalDealServiceFactory::FOR_LOGGED_IN_USER,
				MakeRentalDealServiceFactory::ALLOW_ONLY_ON_CURRENTLY_PUBLISHED_OBJECTS
			);

			try {
				$output = $makeRentalDealService->execute($post_data);

				if (!$output) {
                    CORSHelper::outputJSONResponse(null, 403);
                }

				CORSHelper::outputJSONResponse([
					'success' => true,
					'rentalDealsId' => $output['rental_deals']['id'],
				]);
			} catch (Exception $e) {
                RentalWebsiteLogger::instance()->log(
                    'api/make-rental-deal',
                    'Exception',
                    [
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]
                );

				CORSHelper::outputJSONResponse(null, 500);
			}
		}

		public function getRentalProfileAction() {
			if (!loginManager::check()) {
                RentalWebsiteLogger::instance()->log(
                    'api/get-rental-profile',
                    'user not logged in'
                );
				CORSHelper::outputJSONResponse(null, 401);
			}

			$getRentalProfileService = new GetRentalProfile(
				new Users()
			);

			$profile = $getRentalProfileService->execute([
				'user_id' => loginManager::data()->id
			]);

			if (empty($profile)) {
                RentalWebsiteLogger::instance()->log(
                    'api/get-rental-profile',
                    'user row not found for user_id: ' . loginManager::data()->id
                );
            }

			CORSHelper::outputJSONResponse($profile);
		}

		public function setRentalProfileAction() {
			if (!loginManager::check()) {
                RentalWebsiteLogger::instance()->log(
                    'api/set-rental-profile',
                    'user not logged in'
                );
				CORSHelper::outputJSONResponse(null, 401);
			}

			$setRentalProfileService = new SetRentalProfile(
				new Users()
			);

			$params = $this->getAllParams();
			$params['user_id'] = loginManager::data()->id;

            RentalWebsiteLogger::instance()->log(
                'api/set-rental-profile',
                'Request',
                $params
            );

			$setRentalProfileService->execute($params);

			CORSHelper::outputJSONResponse();
		}

		public function registerViewingAction()
		{
			/** @noinspection PhpUndefinedMethodInspection */
			$post_data = $this->getRequest()->getPost();

			$makeRentalDealService = (new MakeRentalDealServiceFactory())->build(
				MakeRentalDealServiceFactory::CREATE_WEBSITE_RENTAL_DEAL,
				MakeRentalDealServiceFactory::FOR_USER_WITH_GIVEN_EMAIL_ADDRESS,
				MakeRentalDealServiceFactory::ALLOW_ONLY_ON_CURRENTLY_PUBLISHED_OBJECTS
			);

			try {
				$output = $makeRentalDealService->execute(
					array_only_these_keys($post_data, [
						'object',
						'email',
						'day',
						'time',
					])
				);

				CORSHelper::outputJSONResponse([
					'status' => 'success',
					'message' => 'Viewing was succesfully created',
					'unique_id' => $output['rental_deals']['id'],
				]);
			} catch (Exception $e) {
				/** @noinspection ForgottenDebugOutputInspection */
				error_log($e->getMessage());
				CORSHelper::outputJSONResponse([
					'status' => 'failure',
					'message' => $e->getMessage(),
				], 500);
			}
		}

		public function checkVidiiCodeAction()
		{
			$vidiiAccessCode = $this->getParam('code');
			$service = new CheckVidiiCode(new VidiiAccessCodes());

			$isValid = $service->execute(['vidii_access_code' => $vidiiAccessCode]);

			CORSHelper::outputJSONResponse([
				'code' => $isValid ? 'valid' : 'invalid'
			]);
		}
}
