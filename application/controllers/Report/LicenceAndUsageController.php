<?php

class Report_LicenceAndUsageController extends GlobalController {

    /**
     * @var Controllers_Report_LicenceAndUsage $model;
     */
    private $model;

    public function preDispatch()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Licentie en verbruik', 'report_licence-and-usage');

        $this->model = new Controllers_Report_LicenceAndUsage();
    }

    public function indexAction(){
        $this->view->data = $this->model->get();
    }

    public function activeObjectsAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Actieve objecten');

        $data = $this->model->get();

        //p($data,'die');

        $this->view->ListView($data['active_objects'], [])

            ->setTypes([
                'project_name' => [
                    'title' => 'Project',
                    'width' => 'xxxlarge truncate',
                ],
                'objectgroup_name' => [
                    'title' => 'Objectgroep',
                    'width' => 'xxxlarge truncate',
                ],
                'object' => [
                    'title' => 'Object',
                    'width' => 'xxxlarge truncate',
                ]
            ])

            ->addFormat('objectgroup_name', function($value, $row){
                return   '<a href="objectgroup/details/id/' . $row['objectgroup_id'] . '" target="_blank">' . $value . '</a>';
            })

            ->addFormat('object', function($object_id, $row){
                return $object_id > 0 ? '<a href="support/show/type/object/id/' . $object_id . '" target="_blank">' . $row['rendered_address'] . '</a>' : '-';
            })

            ->setFilters([
                'project_name' => [
                    'title' => 'Project',
                    'renderSeparately' => true,
                    'order_by_value' => 'ASC',
                    ],
                'objectgroup_name' => [
                    'title' => 'Objectgroep',
                    'renderSeparately' => true,
                    'order_by_value' => 'ASC',
                ],
                'object' => ['type' => 'input']
            ])

            ->addLegend('listview-legend')

            ->setOptions([
                'item_title' => 'Actieve objecten'
            ])

            ->render($this);
    }

    public function activeUsersAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Actieve admin gebruikers');

        $data = $this->model->get();

        $this->view->ListView($data['active_users'], [])

            ->setTypes([
                'user' => [
                    'title' => 'Gebruiker',
                    'width' => 'xxxlarge truncate',
                ],
                'lastlogin' => [
                    'title' => 'Laatste login',
                    'width' => 'xxxlarge truncate',
                ]
            ])

            ->addFormat('user', function($user_id, $row){
                return $user_id > 0 ? '<a href="user/admin-edit/id/' . $user_id . '" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
            })


            ->setFilters([
                'user' => ['type' => 'input']
            ])

            ->addLegend('listview-legend')

            ->setOptions([
                'item_title' => 'Actieve admin gebruikers'
            ])

            ->render($this);
    }

    public function personalPageUsersAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Persoonlijke pagina gebruikers');

        $data = $this->model->get();

        $this->view->ListView($data['personal_page_users'], [])

            ->setTypes([
                'user' => [
                    'title' => 'Gebruiker',
                    'width' => 'xxxxlarge truncate',
                ],
                'username' => [
                    'title' => 'Gebruikersnaam',
                    'width' => 'medium truncate',
                ],
                'login_date' => [
                    'title' => 'Inlogdatum',
                    'width' => 'medium truncate',
                    'type' => 'date'
                ]
            ])

            ->addFormat('login_date', 'date')

            ->addFormat('user', function($user_id, $row){
                return $user_id > 0 ? '<a href="support/show/type/user/id/' . $user_id . '" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
            })


            ->setFilters([
                'user' => ['type' => 'input']
            ])

            ->addLegend('listview-legend')

            ->setOptions([
                'item_title' => 'Persoonlijke pagina gebruikers'
            ])

            ->render($this);
    }

    public function inspectionsAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Inspecties ' . strftime('%B %Y', strtotime('-1 month')));

        $data = $this->model->get();

        $i_model = new Inspection();
        $type_labels = $i_model->type_labels;

        $this->view->ListView($data['inspections'], [])

            ->setTypes([
                'date_done' => [
                    'title' => 'Datum afgerond',
                    'width' => 'large truncate',
                    'type' => 'date'
                ],
                'user' => [
                    'title' => 'Huurder',
                    'width' => 'xxlarge truncate',
                ],
                'object' => [
                    'title' => 'Object',
                    'width' => 'xxxxlarge truncate',
                ],
                'type' => [
                    'title' => 'Inspectie type',
                    'width' => 'small truncate',
                ],
            ])

            ->addFormat('type', function($value) use ($type_labels) {
                return $type_labels[$value] ?: '-';
            })

            ->addFormat('date_done', 'date')

            ->addFormat('user', function($user_id, $row){
                return $user_id > 0 ? '<a href="support/show/type/user/id/' . $user_id . '" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
            })

            ->addFormat('object', function($object_id, $row){
                return $object_id > 0 ? '<a href="support/show/type/object/id/' . $object_id . '" target="_blank">' . $row['rendered_address'] . '</a>' : '-';
            })

            ->setFilters([
                'date_done' => ['type' => 'date_range'],
                'type' => [],
                'user' => ['type' => 'input'],
                'object' => ['type' => 'input'],
            ])

            ->addLegend('listview-legend')

            ->setOptions([
                'item_title' => 'Inspecties'
            ])

            ->render($this);
    }

    public function creditChecksAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Credit checks ' . strftime('%B %Y', strtotime('-1 month')));

        $data = $this->model->get();

        $this->view->ListView($data['credit_checks'], [])

            ->setTypes([
                'date' => [
                    'title' => 'Datum',
                    'width' => 'large truncate',
                    'type' => 'date'
                ],
                'user' => [
                    'title' => 'Huurder',
                    'width' => 'xxlarge truncate',
                ]
            ])

            ->addFormat('date', 'date')

            ->addFormat('user', function($user_id, $row){
                return $user_id > 0 ? '<a href="support/show/type/user/id/' . $user_id . '" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
            })

            ->setFilters([
                'date' => ['type' => 'date_range'],
                'user' => ['type' => 'input']
            ])

            ->addLegend('listview-legend')

            ->setOptions([
                'item_title' => 'Credit checks'
            ])

            ->render($this);
    }

    public function getExcelExportAction()
    {
        $year = $this->getParam('year');
        $month = $this->getParam('month');

        $this->view->extraWidth = true;

        $this->view->Breadcrumbs()
            ->addCrumb('Licenties en verbruiken per klant');

        if ($_SERVER['HTTP_HOST'] === 'support.omniboxx.nl') {
            $this->model->setForAllDatabases(true);
            $raw_data = $this->model->get($year, $month);

            $this->setParam('view_template', 'excel');
        } else {
            $raw_data = ['klant' => $this->model->get()];
        }

        $formatted_data = [];

        foreach ($raw_data as $customer_id => $customer_data) {
            $formatted_data[$customer_id] = $customer_data;

            $formatted_data[$customer_id]['customer'] = $customer_id;

            $count_items = [
                'active_objects',
                'active_users',
                'personal_page_users',
                'inspections',
                'inspections_previous',
                'credit_checks',
                'credit_checks_previous'
            ];

            foreach ($count_items as $count_item) {
                $formatted_data[$customer_id][$count_item] = count($customer_data[$count_item]);
            }
        }

        $listview = $this->view->ListView($formatted_data);

        $listview
            ->setTypes([
                'customer' => [
                    'title' => 'Klant',
                    'width' => 'small truncate',
                ],
                'active_objects' => [
                    'title' => 'Actieve objecten',
                    'width' => 'small truncate',
                ],
                'licenced_objects' => [
                    'title' => 'Licentie objecten',
                    'width' => 'small truncate',
                ],
                'active_users' => [
                    'title' => 'Actieve gebruikers',
                    'width' => 'small truncate',
                ],
                'licenced_users' => [
                    'title' => 'Licentie gebruikers',
                    'width' => 'small truncate',
                ],
                'licenced_tenant_login' => [
                    'title' => 'Persoonlijke pagina module',
                    'width' => 'small truncate',
                ],
                'licenced_personal_page' => [
                    'title' => 'Persoonlijke pagina module aantal',
                    'width' => 'small truncate',
                ],
                'personal_page_users' => [
                    'title' => 'Persoonlijke pagina gebruikers',
                    'width' => 'small truncate',
                ],
                'inspections' => [
                    'title' => 'Inspecties ' . strftime('%B', strtotime('-1 month')),
                    'width' => 'medium truncate',
                ],
                'credit_checks' => [
                    'title' => 'Credit checks ' . strftime('%B', strtotime('-1 month')),
                    'width' => 'medium truncate',
                ]
            ])

            ->addFormat(['licenced_objects', 'licenced_users', 'licenced_personal_page', 'personal_page_users'], function($value) {
                return $value > 0 ? $value : '-';
            })

            ->addFormat(['licenced_tenant_login'], 'bool')

            ->addTotals([
                'active_objects',
                'licenced_objects',
                'active_users',
                'licenced_users',
                'licenced_personal_page',
                'personal_page_users',
                'inspections',
                'credit_checks'
            ])

            ->setOptions([
                'item_title' => 'Klanten'
            ])

            ->render($this);
    }
}
