<?

	class ServiceChargesController extends GlobalController {

		public function preDispatch() {
			$this->view->type = $this->_getParam('type');
			$this->view->mapTo = $this->_getParam('mapTo');
		}

		
		
		/**
		 * List
		 * @ACL
		 */
		public function overviewAction() {
			$this->view->extraWidth = true;
			
			$this->view->Breadcrumbs()
				->addCrumb('Financieringen per project');

			// get current project
			$this->view->project = $filter_project = $this->_getParam('project') && $this->_getParam('project') != 'false' ? $this->_getParam('project') : false; 
			
			// get all projects
			foreach(ObjectsFinance::getList(false) as $finance)
				$projects[$finance['project_id']] = $finance['project'];

			// assign project list to view
			$this->view->projects = $projects;

			// create a new list view and assign to view
			$this->view->list = $this->view->modelListView('ObjectsFinance', array('project' => $filter_project))
		
				// specify list columns
				->setTypes(array('project', 'object', 'finance_company',  'initial_value',  'yearly_payoff', 'current_value', 'spacer', 'interest', 'interest_calc', 'spacer2', 'interest_type', 'base_component' ,'markup_component', 'duration_years', 'end_date'))

				// add formatters for types
				->addFormat(array('initial_value', 'payoff', 'yearly_payoff', 'current_value', 'interest_calc'), 'money')
				->addFormat('interest', 'percentage')
				->addFormat('interest_type', 'ucfirst')
				->addFormat('duration_years', function($value){ return $value . ' jaar'; })
				->addFormat('end_date', 'date')

				->addFormat('object', function($value, $item){
					if($item['type'] == 'object')
						return $value;

					$h = new Hierarchy($item['map_to'], $item['type']);

					$title = '';
					foreach($h->_data['objects'] as $object)
						if($address = Address::get($object, 'object'))
							$title .= '- ' . $address->address . ' ' . $address->number . '<br />';

					return '<div title="' . $title . '">Gehele project</div>';
 					p($h, 'die');
				})
				
				// set options
				->setOptions(array(
					'show_title' => false,
					'render_to_controller' => false,
					'noEditButton' => true,
					'item_title' => 'Financiering',
					'item_has_details' => true,
				))

				// enable and specify totals
				->addTotals(array('initial_value', 'payoff', 'yearly_payoff', 'current_value' , 'interest_calc'))
				
				->addButtons(array(
					'delete' => 'Verwijderen'
				))

				// render the list and return
				->render($this);
		}

		public function budgetAction(){
			$this->view->extraWidth = true;

			$this->view->headScriptHashed()->appendFile('media/javascript/budgets.js');	
			$this->view->headLink()->appendStylesheet('media/style/budgets.css');	

			$this->view->Breadcrumbs()
				->addCrumb('Budget- en prognoseinvoer');

			$budget_mapping = 'objectgroup';	
			$budget_start_year = 2013;		

			$b_model = new Budgets();
			$b_model->budget_mapping = $budget_mapping;

			$data = array();
			foreach(range($budget_start_year, date('Y')+1) as $year)
				$data = array_merge($data, $b_model->getList($year));

			$this->view->list = $this->view->listView($data)

				->setTypes(array(
					'component_id' => 	array('column' => 'component_id', 'width' => 'hidden component_id'),
					'project_id' => 	array('column' => 'project_id', 'width' => 'hidden project_id'),
					'objectgroup_id' => array('column' => 'objectgroup_id', 'width' => 'hidden objectgroup_id'),
					'year' =>			array('column' => 'year', 'width' => 'hidden year'),
					'year_label' =>		array('column' => 'year', 'title' => 'Jaar', 'width' => 'xxsmall', 'group_equal_rows' => true),
					'corporation' => 	array('column' => 'corporation', 'title' => 'B.V.', 'width' => 'large ellipsis', 'group_equal_rows' => true),
					'project' => 		array('column' => 'project', 'title' => 'Project', 'width' => 'large ellipsis', 'group_equal_rows' => true),
					'objectgroup' => 	array('column' => 'objectgroup', 'title' => 'Objectgroep', 'width' => $budget_mapping == 'objectgroup' ? 'large ellipsis' : 'hidden', 'group_equal_rows' => true),
					'group_label' => 	array('title' => 'Groep label', 'width' => 'large ellipsis', 'group_equal_rows' => true),
					'component' => 		array('column' => 'component', 'title' => 'Component', 'width' => 'large ellipsis'),
					'prognosis' => 		array('column' => 'prognosis', 'title' => 'Prognose', 'width' => 'medium input prognosis right'),
					'budget' => 		array('column' => 'budget', 'title' => 'Budget', 'width' => 'medium input budget right')
				))

				->addFormat(array('prognosis', 'budget'), 'money_input')


				->setOptions(array(
					'noEditButton' => true,
					'item_title' => 'Budget',
					'paginator' => false
				))

				->setFilters(array(
					'year' => array('default' => date('Y')),
					'corporation' => array(),
					'project' => array(),
					'objectgroup' => array(),
					'group_label' => array(),
					'component' => array(),
				))				

				// render the list and return
				->render($this);			
		}

		public function saveBudgetValueAction(){
			$this->disableView();

			foreach(array('year', 'component_id', 'project_id', 'objectgroup_id') as $attribute)
				if(!$this->_getParam($attribute)) return;

			$attributes = array(
				'year' => $this->_getParam('year'),
				'component' => $this->_getParam('component_id'),
				'type' => 'objectgroup',
				'map_to' => $this->_getParam('objectgroup_id')
			);

			$model = new Budgets();

			if(!($row = $model->matchRow($attributes)))
				$row = $model->createRow($attributes);

			foreach(array('prognosis', 'budget') as $input_type)
				if(!is_null($this->_getParam($input_type)))
					$row->$input_type = str_replace(',', '.', ((string) new StringFormat($this->_getParam($input_type), 'money_db'))/100);

			$row->save();
		}

		protected function getYearsForOverview($data){
			$years = [date('Y')];

			foreach($data as $cost)
				if(!in_array($cost['year'], $years))
					if($cost['year'] > 2015)
						$years[] = $cost['year'];

			return $years;
		}

		protected function getProjectsForOverview($data){
			return array_combine(
				array_column($data, 'project_id'),
				array_column($data, 'project')
			);
		}

		protected function buildYearFilterOptions($years){
			$year_filter_options = [];

			foreach($years as $year)
				$year_filter_options[$year] = ['title' => $year];

			return $year_filter_options;
		}

		protected function buildProjectFilterOptions($projects){
			$project_filter_options = [];

			foreach($projects as $project_id => $project_name)
				$project_filter_options[$project_id] = ['title' => $project_name];

			return $project_filter_options;

		}

		public function viewCostsGroupedAction()
		{

			$this->view->headLink()->appendStylesheet('media/style/budgets.css');

			$this->view->Breadcrumbs()
				->addCrumb('Overzicht servicekosten');

			if (!$this->_getParam('sorting'))
				$this->_setParam('sorting', json_encode(['group_label' => 'ASC']));

			$costs_overview_query = $this->getCostsOverviewQuery()
				->group('c.id');

			$all_costs_data = db()->fetchAll($costs_overview_query);
			$years = $this->getYearsForOverview($all_costs_data);
			$year_filter_options = $this->buildYearFilterOptions($years);
			$projects = $this->getProjectsForOverview($all_costs_data);
			$project_filter_options = $this->buildProjectFilterOptions($projects);

            if (
                !$this->isAjaxRequest &&
                $this->getParam('year') === null &&
                !in_array($this->getParam('view_template'), ['excel', 'csv', 'print'])
            ) {
				$year = max($years) ?: date('Y') - 1;
				$this->setParam('year', $year);
			} else {
				$year = $this->getParam('year');
			}

			if (is_null($this->getParam('project')) && count($projects) > 0){
				$project_id = end(array_keys(array_reverse($projects, true)));
				$this->setParam('project', $project_id);
			} else {
				$project_id = $this->getParam('project');
			}

			$select = $this->getCostsOverviewQuery($year, $project_id)
				->group('comp.name')
				->group('p.id');

			$data = db()->fetchAll($select);

			// group werkt niet
			// doorlink naar kosten en bewerken van individueel
			
			//p($data,'die');
			foreach($data as $data_key => $data_item){

				if(!($data_item['has_saved_amounts'] > 0)){
					if($data_item['cost_owner'] == 'renter'){
						$data[$data_key]['renter_amount'] = $data_item['total'];
					} else {
						$data[$data_key]['owner_amount'] = $data_item['total'];
					}
				}
			}

			$this->view->list = $this->view->listView($data)

				->setTypes([
					'cost_id' => 		['width' => 'hidden cost_id'],
					'objectgroup_id' => ['width' => 'hidden objectgroup_id'],
 					'total_amount' => 	['width' => 'hidden total_amount'],
 					'group_label' => 	['title' => 'Groep label', 'width' => 'xxlarge ellipsis', 'group_equal_rows' => true],
					'costs_title' => 	['title' => 'Servicekosten titel', 'width' => 'xxlarge ellipsis'],
					'component' => 		['title' => 'Component', 'width' => 'xxlarge ellipsis'],
 					'total' => 			['title' => 'Totaal', 'width' => 'large right'],
 					'spacer' => 		['width' => 'xxxxxsmall'],
 					'spacer2' => 		['width' => 'xxxxsmall'],
					'edit' => 			['title' => 'Details', 'width' => 'xxsmall'],
				])


				->addFormat('total', 'money')
				->addFormat(['renter_amount', 'owner_amount'], 'money_input')

				->addFormat('date', function($value){
					return '<input class="DatePicker" value="' . date('d-m-Y', strtotime($value)) . '" /><div class="datepicker_container"><span /></div>';
				})

				->addFormat('rato', function($value){
					$titles = ['even' => 'Gelijk', 'key' => 'Verdeelsleutel', 'size' => 'Oppervlakte'];

					if(isset($titles[$value]))
						return $titles[$value];

					return '-';
				})

				->addFormat('edit', function($value, $row){
					return '<a class="button info" target="_blank" href="service-charges/view-costs/component_id/' . $row['component_id'] . '/project/' . $row['project_id'] . '/year/' . $row['year'] . '/"></a>';
				})

				


				->setOptions([
					'item_title' => 'Servicekosten',
					'paginator' => false
				])

				->setFilters([
					'year'     => [
						'renderSeparately' => true,
						'order_by_value' => 'DESC',
						'title' => 'Jaar',
						'custom_options' => $year_filter_options,
						'hideCount' => true,
						'preApplied' => true,
						'custom_options_only' => true
					],
 					'project'     => [
						'renderSeparately' => true,
						'order_by_title' => 'ASC',
						'title' => 'Project',
						'custom_options' => $project_filter_options,
						'hideCount' => true,
						'preApplied' => true,
						'custom_options_only' => true
				   ],
					'objectgroup' => [],
					'group_label' => [],
					'costs_title' => [],
					'rato' => [],
					'component' => [],
				])

				->addTotals(array('total'))

				// render the list and return
				->render($this);
		}

		public function viewCostsAction(){
			$this->view->extraWidth = true;

			$this->view->headLink()->appendStylesheet('media/style/budgets.css');

			$this->view->Breadcrumbs()
				->addCrumb('Overzicht servicekosten');

			$costs_overview_query = $this->getCostsOverviewQuery()
				->group('c.id');

			$all_costs_data = db()->fetchAll($costs_overview_query);
			$years = $this->getYearsForOverview($all_costs_data);
			$year_filter_options = $this->buildYearFilterOptions($years);
			$projects = $this->getProjectsForOverview($all_costs_data);
			$project_filter_options = $this->buildProjectFilterOptions($projects);

            if (
                !$this->isAjaxRequest &&
                $this->getParam('year') === null &&
                !in_array($this->getParam('view_template'), ['excel', 'csv', 'print'])
            ) {
				$year = max($years) ?: date('Y') - 1;
				$this->setParam('year', $year);
			} else {
				$year = $this->getParam('year');
			}

			if (is_null($this->getParam('project')) && count($projects) > 0){
				$project_id = end(array_keys(array_reverse($projects, true)));
				$this->setParam('project', $project_id);
			} else {
				$project_id = $this->getParam('project');
			}

			$select = $this->getCostsOverviewQuery($year, $project_id)
				->group('c.id');

			$data = db()->fetchAll($select);

			// group werkt niet
			// doorlink naar kosten en bewerken van individueel
			
			//p($data,'die');
			foreach($data as $data_key => $data_item){

				if(!($data_item['has_saved_amounts'] > 0)){
					if($data_item['cost_owner'] == 'renter'){
						$data[$data_key]['renter_amount'] = $data_item['total'];
					} else {
						$data[$data_key]['owner_amount'] = $data_item['total'];
					}
				}
			}

			$this->view->list = $this->view->listView($data)

				->setTypes([
					'cost_id' => 		['width' => 'hidden cost_id'],
					'objectgroup_id' => ['width' => 'hidden objectgroup_id'],
					'year' =>			['width' => 'hidden year'],
					'total_amount' => 	['width' => 'hidden total_amount'],
					'invoicedate' =>	['title' => 'Factuurdatum', 'width' => 'small'],
					'title' =>			['title' => 'Factuur#', 'width' => 'small'],
 					'project_id' =>		['title' => 'Project', 'width' => 'large ellipsis'],
					'companyname' => 	['title' => 'Leverancier', 'width' => 'medium ellipsis'],
					'group_label' => 	['title' => 'Groep label', 'width' => 'medium ellipsis'],
					'costs_title' => 	['title' => 'Servicekosten titel', 'width' => 'medium ellipsis'],
					'component_id' => 	['title' => 'Component', 'width' => 'medium ellipsis'],
					'total' => 			['title' => 'Totaal', 'width' => 'medium right'],
					'renter_amount' => 	['title' => 'Huurder', 'width' => 'medium right input renter_amount'],
					'owner_amount' => 	['title' => 'Belegger', 'width' => 'medium right input owner_amount'],
					'spacer' => 		['width' => 'xxxxxsmall'],
					//'date' => 			['title' => 'Boekdatum', 'width' => 'large'],
					'spacer2' => 		['width' => 'xxxxsmall'],
					'edit' => 			['title' => 'Acties', 'width' => 'xxsmall']
				])


				->addFormat('total', 'money')
				->addFormat(['renter_amount', 'owner_amount'], function($value, $row){ 
					if(!isset($row['id']))
						return '&euro; ' . new StringFormat($value, 'money');

					$value = abs($value) > 0 ? (string) new StringFormat($value, 'money_for_number_input') : false;

					return '&euro; <input type="number" step="0.01" max="' . $row['total_amount'] . '" min="0" pattern="^\s*-?((\d{1,3}(\.(\d){3})*)|\d*)(,\d{1,2})?\s?(\u20AC)?\s*$" placeholder="0,00" ' . ($value !== false ? 'value="' . $value . '"' : '') . ' autocomplete="off" />';
				})

				->addFormat('date', function($value){
					return '<input class="DatePicker" value="' . date('d-m-Y', strtotime($value)) . '" /><div class="datepicker_container"><span /></div>';
				})

				->addFormat('rato', function($value){
					$titles = ['even' => 'Gelijk', 'key' => 'Verdeelsleutel', 'size' => 'Oppervlakte'];

					if(isset($titles[$value]))
						return $titles[$value];

					return '-';
				})

				->addFormat('edit', function($value, $row){
					return '<a class="button edit" target="_blank" href="costs/edit/id/' . $row['id'] . '/">Bewerken</a>';
				})

				->addFormat('project_id', function($value, $row){
					return $row['project'];
				})
				->addFormat('component_id', function($value, $row){
					return $row['component'];
				})


				->setOptions([
					'item_title' => 'Servicekosten',
					'paginator' => false
				])

				->setFilters([
					'year'     => [
						'renderSeparately' => true,
						'order_by_value' => 'DESC',
						'title' => 'Jaar',
						'custom_options' => $year_filter_options,
						'hideCount' => true,
						'preApplied' => true,
						'custom_options_only' => true
					],
					'project'     => [
						'renderSeparately' => true,
						'order_by_title' => 'ASC',
						'title' => 'Project',
						'custom_options' => $project_filter_options,
						'hideCount' => true,
						'preApplied' => true,
						'custom_options_only' => true
					],
					'invoicedate' => ['type' => 'date_range'],
					'title' => [],
					'companyname' => [],
					'objectgroup' => [],
					'group_label' => [],
					'costs_title' => [],
					'rato' => [],
					'component_id' => []
				])

				->addTotals(array('total', 'renter_amount', 'owner_amount'))

                ->addLegend('servicecharges-legend')
				// render the list and return
				->render($this);
		}

		protected function getCostsOverviewQuery($year = false, $project_id = false){
			$select = db()->select()
				->from(['c' => 'costs'], [
                    'id',
                    'cost_id' => 'id',
                    'component_id' => 'component',
                    'year' => 'YEAR(COALESCE(c.fiscal_period_date, c.date))',
                    'rato',
                    'date',
                    'cost_owner'
                ])
				->joinLeft(['ic' => 'invoice_custom'], 'c.purchase_invoice = ic.id', ['invoicedate','title'])
				->joinLeft(['cmp' => 'company'], 'ic.contact = cmp.id', ['companyname' => 'name'])
				->joinLeft(['co' => 'costs_objects'], 'co.cost = c.id', ['total' => 'ROUND(SUM(co.amount)/100, 2)', 'total_amount' => 'ROUND(SUM(co.amount)/100, 2)'])
				->joinLeft(['o' => 'objects'], 'o.id = co.object', false)
				->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['objectgroup_id' => 'id', 'objectgroup' => 'description'])
				->joinLeft(['csd' => 'costs_service_divide'], 'csd.cost_id = c.id AND csd.objectgroup_id = og.id', ['has_saved_amounts' => 'id', 'renter_amount' => 'ROUND(csd.renter_amount/100, 2)', 'owner_amount' => 'ROUND(csd.owner_amount/100, 2)'])
				->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project_id' => 'id', 'project' => 'name'])
				->joinLeft(['comp' => 'components'], 'comp.id = c.component', ['component' => 'name', 'group_label', 'costs_title'])
				->where('comp.cost_type = ?', 'opex')
				->where('c.cost_owner = ?', 'renter')
				->where('p.id IS NOT NULL');

			if($year > 0)
				$select->where('YEAR(COALESCE(c.fiscal_period_date, c.date)) = ?', $year);

			if($project_id > 0)
				$select->where('p.id = ?', $project_id);

            $select->order('ic.invoicedate DESC');

			return $select;
		}

		public function saveCostsDateAction(){
			$this->disableView();

			foreach(array('cost_id', 'date') as $attribute)
				if(!$this->_getParam($attribute)) return;

			$c_model = new Costs();

			$c_model
				->getById($this->getParam('cost_id'))
				->setFromArray([
					'fiscal_period_date' => date('Y-m-d', strtotime($this->getParam('date')))
				])
				->save();
		}

		public function saveServiceDivideValueAction(){
			$this->disableView();

			foreach(array('cost_id', 'objectgroup_id') as $attribute)
				if(!$this->_getParam($attribute)) return;

			$attributes = array(
				'cost_id' => $this->_getParam('cost_id'),
				'objectgroup_id' => $this->_getParam('objectgroup_id')
			);

			$model = new CostsServiceDivide();

			if(!($row = $model->matchRow($attributes)))
				$row = $model->createRow($attributes);

			foreach(array('renter_amount', 'owner_amount') as $input_type)
				if(!is_null($this->_getParam($input_type)))
					$row->$input_type = str_replace(',', '.', ((string) new StringFormat($this->_getParam($input_type), 'money_db'))/100) * 100;

			$row->save();
		}


		public function reportAction(){
			$this->view->headLink()->appendStylesheet('media/style/finance/report.css');

			$this->view->Breadcrumbs()
				->addCrumb('Overzicht');

			$b_model = new Budgets();
			$b_model->budget_mapping = 'objectgroup';

			$totals = array('budget' => 0, 'prognosis' => 0, 'cost' => 0, 'remaining' => 0);

			$data = array(
				'components' => array(),
				'totals' => $totals
			);

			$co_model = new CostsObjects();
			$costs = $co_model->getList(array('year' => 2013, 'project' => 320));

			foreach($b_model->getList(2013, 320) as $budget){

				if(!isset($budget['budget'])) continue;

				if(!isset($data['components'][$budget['component_id']]))
					$data['components'][$budget['component_id']] = array(
						'title' => $budget['component'],
						'items' => array(),
						'totals' => $totals
					);

				foreach($costs as $cost_object)
					if($cost_object->costrow->component == $budget['component_id'] && $cost_object->objectrow->objectgroup == $budget['objectgroup_id'])
						if(isset($budget['cost']))
							$budget['cost'] += $cost_object->amount/100;
						else
							$budget['cost'] = $cost_object->amount/100;

				$budget['remaining'] = $budget['budget'] - $budget['cost'];

				$data['components'][$budget['component_id']]['items'][$budget['objectgroup_id']] = $budget;

				foreach($data['components'][$budget['component_id']]['totals'] as $total_key => &$total){
					$total += $budget[$total_key];
					$data['totals'][$total_key] += $budget[$total_key];
				}
			}

			$this->view->tables = array(
				'payment' => array(
					'title' => 'Inkomsten',
					'columns' => array('budget' => 'Contract', 'prognosis' => 'Prognose', 'zero1' => 'Betaald', 'zero2' => 'Betaald', 'zero3' => 'Budget resterend')
				),
				'cost' => array(
					'title' => 'Kosten',
					'columns' => array('budget' => 'Budget', 'prognosis' => 'Prognose', 'cost' => 'Huidige kosten', 'remaining' => 'Budget resterend')
				)
			);

			$this->view->data = $data;
		}

		public function exportAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Export');

			$c_model = new Corporations();
			$data = $c_model->getTotalsList($this->getAllUrlParams(),'commercial');


			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('address' => 'ASC')));

			$types = array(
				'corporation' => array('title' => 'Entiteit', 'width' => 'medium truncate', 'group_equal_rows' => true),
				'address' => array('title' => 'Adres', 'width' => 'medium truncate'),
				'grouping_label' => array('title' => 'Label', 'width' => 'medium truncate'),
				'count' => array('title' => 'Aantal appartementen', 'width' => 'medium right'),
				'gross' => array('title' => 'Huur inclusief GWL', 'width' => 'large right'),
				'net' => array('title' => 'Huur exclusief GWL', 'width' => 'large right'),
			);

			$this->view->ListView($data)

				->setTypes($types)

				->addFormat('corporation', function($value, $item){ return $item['corporation_name']; })

				->addFormat(array('gross', 'net'), 'money')

				->addTotals(array('count', 'gross', 'net'))

				->setFilters(array('corporation' => array()))

				->setOptions(array(
				))

				->render($this);
		}
		
		/**
		 * List
		 * @ACL
		 */
		public function listAction() {
			$type  = $this->_getParam('type');
			$mapTo = $this->_getParam('mapTo');
			
 			//get all relevant ids
			$h = new Hierarchy($mapTo, $type);
			
			//start
			$db = Zend_Db_Table::getDefaultAdapter();
			$select = $db->select()
				->from(array('of' => 'objects_finance'), array('*'))
				->where('of.map_to = ?', $this->_getParam('mapTo'));

			//order DESC
			//$select = $select->order('sc.name DESC');

			//echo !empty($h->_data['object']) && $this->_getParam('object');
			//p($select); exit;

			//pagination
			$paginator = Zend_Paginator::factory($select);
			$paginator->setCurrentPageNumber($this->_getParam('page'));
			$paginator->setItemCountPerPage(15);

			//print_r($paginator);
			$this->view->finances = $paginator;
			$this->view->mark = $this->_getParam('mark');
			$this->view->deleted = $this->_getParam('deleted');

		}

		

		/**
		 * Add
		 * @ACL
		 */
		public function addAction() {
			$finance = new Form($this, 'support/finance/add');


			if ($this->getRequest()->isPost() && $finance->isValid($this->getRequest()->getPost())) {
				//die("Type: " . $this->_getParam('type') . " MapTo: " . $this->_getParam('mapTo'));
				$sc = new ObjectsFinance();
				$c = $sc->createRow();

				$data = $finance->getValues();

				//p($data,'die');	
				$c->finance_company = $data['finance_company'];
				$c->contract = $data['contract'];
				$c->initial_value = $data['initial_value'];
				$c->payoff = $data['payoff'];
				$c->current_value = $data['current_value'];
				$c->interest = $data['interest'];
				$c->interest_type = $data['interest_type'];
				$c->interest_duration = $data['interest_duration'];
				$c->base_component = $data['base_component'];
				$c->markup_component = $data['markup_component'];
				$c->duration_years = $data['duration_years'];
				$c->end_date = $data['end_date'];
 				$c->pawn = $data['pawn'];
				$c->map_to = $this->_getParam('mapTo');
				$c->type = $data['type'];
				$c->save();

				$this->_forward('list', null, null, array(
					'mark' => array(
						'id' => $c->id,
						'type' => $cm->type
					),
					'mapTo' => $this->_getParam('mapTo'),
					'type' => $this->_getParam('type')
				));
			}

			$this->view->finance = $finance;

		}

		/**
		 * Edit
		 * @ACL
		 */
		public function editAction() {

			$finance = new Form($this, 'support/finance/add');
			$finance->setAction('finance/edit/id/' . $this->_getParam('id'));
			$finance->submit->setLabel('Bewerken');

			//data ophalen
			$db = Zend_Db_Table::getDefaultAdapter();
			$select = $db->select()
				->from(array('of' => 'objects_finance'), array('*'))
				->where('of.id = ?', $this->_getParam('id'));
			$financedata = $db->fetchRow($select);

			//formulier invullen
			$finance->populate($financedata);
			if ($this->getRequest()->isPost() && ($finance->isValid($this->getRequest()->getPost()))) {

				$table = new ObjectsFinance();
				$c = $table->fetchRow('id = \'' . $this->_getParam('id') . '\'');

				$data = $finance->getValues();

				$c->finance_company = $data['finance_company'];
				$c->contract = $data['contract'];
				$c->initial_value = $data['initial_value'];
				$c->payoff = $data['payoff'];
				$c->current_value = $data['current_value'];
				$c->interest = $data['interest'];
				$c->interest_type = $data['interest_type'];
				$c->interest_duration = $data['interest_duration'];
				$c->base_component = $data['base_component'];
				$c->markup_component = $data['markup_component'];
				$c->duration_years = $data['duration_years'];
				$c->end_date = $data['end_date'];
 				$c->pawn = $data['pawn'];
				$c->save();

				$this->_forward('show', null, null, array(
					'id' => $c->id,
					'mapTo' => $this->_getParam('mapTo'),
					'type' => $this->_getParam('type')
				));
			}
			else {
				$this->view->data = $financedata;
				$this->view->finance = $finance;
 				$this->view->edit = true;
				$this->render('add');
			}

		}

		/**
		 * Delete
		 * @ACL
		 */
		public function deleteAction() {

			if ($this->_getParam('id')) {
				$db = Zend_Db_Table::getDefaultAdapter();
				db()->delete('objects_finance', "`id` = " . $this->_getParam('id'));
			}
			
			$this->_forward('overview');
		}

		/**
		 * Show contacts
		 * @ACL
		 */
		public function showAction() {

			$db = Zend_Db_Table::getDefaultAdapter();
			$select = $db->select()
				->from(array('of' => 'objects_finance'), array('*'))
				->where('of.id = ?', $this->_getParam('id'));
			$finance = $db->fetchRow($select);

			$this->view->finance = $finance;

		}

	}

?>
