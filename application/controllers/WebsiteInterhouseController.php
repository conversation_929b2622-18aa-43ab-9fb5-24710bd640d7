<?
class WebsiteInterhouseController extends GlobalController {

	public function preDispatch()
	{
        if(!is_cli_call())
		    $this->disableView();
        else
            $this->_helper->viewRenderer->setNoRender(true);

		if (Settings::get('general_company_shortname') != 'interhouse')
			$this->_redirect('dashboard/index');
	}

	public function exportAction(){
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 60 * 60 * 2); // 2 hours

        $website_export = new Controllers\WebsiteInterhouse\Export($this->_getParam('number'));
        $website_export->execute();

		//echo '<pre>' . htmlentities($website_export->getExportXml()) . '</pre>'; die();

		ob_clean();
		header('Content-type: text/xml');
		header('Content-disposition: attachment; filename="website_' . $this->_getParam('number') . '.xml"');
		echo $website_export->getExportXml();
    }

    public function getPhotoAction(){
        $file_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
        ];

        $photo_path = substr($_SERVER['REQUEST_URI'], strlen('/website-interhouse/get-photo'));
        $photo_path = str_replace('../', '', $photo_path);

        $full_path = Main::app()->getDir('website_interhouse_export') . 'photos' . $photo_path;

        $render_error = function(){
            $this->getResponse()
                ->setHttpResponseCode(404)
                ->sendResponse();
            exit();
        };

        if(!is_readable($full_path))
            $render_error();

        if($file_type = $file_types[pathinfo($full_path, PATHINFO_EXTENSION)]) {
            header("Content-Type: " . $file_type);
            echo file_get_contents($full_path);
        } else {
            $render_error();
        }
    }

}