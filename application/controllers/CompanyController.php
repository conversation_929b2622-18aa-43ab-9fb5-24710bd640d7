<?
	class CompanyController extends GlobalController {


		/**
		 * Redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function indexAction() {
			$this->_helper->redirector('list', 'company');
		}

		/**
		 * Lists all Companies using a Listview
		 * 
		 * @return null
		 */
		public function listAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Bedrijven overzicht', '');

			$types = CompanyType::getListKeyedById();
			$type_ids = [];
			foreach($types as $type)
				$type_ids[] = $type['id'];

			$data = Company::getList(['type' => $type_ids]);
			$relation_types = RelationType::getList([]);

			foreach ($data as &$company){
				$company['typeName'] = intval($company['type']) > 0? $types[$company['type']]['name'] : '-';
				$company['relationType'] = 'Algemeen';

				if(intval($company['relation_type']) > 0)
					foreach($relation_types as $relation_type)
						if($relation_type['id'] == $company['relation_type'])
							$company['relationType'] = $relation_type['name'];
			}

			$this->view->listView($data)

				->setTypes(array(
					'name' => array(
						'title' => 'Naam',
						'width' => 'xxxlarge truncate',
					),
					'typeName' => array(
						'title' => 'Type',
						'width' => 'medium truncate',
					),
					'relationType' => array(
						'title' => 'Relatie type',
						'width' => 'medium truncate',
					),
					'employeeCount' => array(
						'title' => 'Aantal<br>medewerkers',
						'width' => 'xxsmall truncate',
					),
					'project' => [
						'title' => 'Project',
						'width' => 'small truncate',
					]
				))

				->addFormat('project', function($value, $item){
					return $value > 0 ? $item['project_name'] : '-';
				})

				->setFilters(array(
					'name' => array('type' => 'input'),
					'typeName' => ['order_by_title' => 'ASC'],
					'relationType' => ['order_by_title' => 'ASC'],
					'project' => ['order_by_title' => 'ASC'],
				))

				->setOptions(array(
					'item_title' => 'Bedrijf',
				))

				->addButtons(array(
					'add' => 'Toevoegen',
					'edit' => 'Bewerken',
				),
				array(
					'id' => 'id',
					'redirect' => 'true'
				))

				->render($this);
		}


        public function listTechnicalAction() {
            $this->view->Breadcrumbs()
                ->addCrumb('Technische partijen overzicht');
            $this->view->extraWidth = true;

            $types = CompanyType::getListKeyedById();
            $type_ids = [];

            foreach($types as $type){
                if($type['id'] == 2) {
                    $type_ids[] = $type['id'];
                }

            }


            $data = Company::getList(['type' => $type_ids]);
            $relation_types = RelationType::getList([]);

            foreach ($data as &$company){
                $company['typeName'] = intval($company['type']) > 0? $types[$company['type']]['name'] : '-';
                $company['relationType'] = 'Algemeen';

                if(intval($company['relation_type']) > 0)
                    foreach($relation_types as $relation_type)
                        if($relation_type['id'] == $company['relation_type'])
                            $company['relationType'] = $relation_type['name'];
            }

            $this->view->listView($data)

                ->setTypes(array(
                    'id' => array(
                        'title' => 'ID',
                        'width' => 'hidden',
                    ),

                    'name' => array(
                        'title' => 'Naam',
                        'width' => 'xxxlarge truncate',
                    ),

                    'address' => array(
                        'title' => 'Straat',
                        'width' => 'hidden',
                    ),

                    'number' => array(
                        'title' => 'Nummer',
                        'width' => 'hidden',
                    ),

                    'zipcode' => array(
                        'title' => 'Postcode',
                        'width' => 'hidden',
                    ),

                    'city' => array(
                        'title' => 'Plaats',
                        'width' => 'hidden',
                    ),

                    'relationType' => array(
                        'title' => 'Relatie type',
                        'width' => 'medium truncate',
                    ),
                    'kvk' => array(
                        'title' => 'KVK',
                        'width' => 'small truncate',
                    ),
                    'tax' => array(
                        'title' => 'BTW nummer',
                        'width' => 'medium truncate',
                    ),
                    'iban' => array(
                        'title' => 'IBAN',
                        'width' => 'medium truncate',
                    ),
                    'email' => array(
                        'title' => 'Email',
                        'width' => 'xxlarge truncate',
                    ),
                    'phone1' => array(
                        'title' => 'Tel',
                        'width' => 'small truncate',
                    ),
                    'phone2' => array(
                        'title' => 'Tel alternatief',
                        'width' => 'small truncate',
                    ),
                ))

                ->addFormat('name', function($value, $item){
                    return '<a href="company/edit/id/' . $item['id'] . '/redirect/true" target="tab">' .  $value . '</a>';
                })

                ->setFilters(array(
                    'name' => array('type' => 'input'),
                    'typeName' => array(),
                    'relationType' => [],
                ))

                ->setOptions(array(
                    'item_title' => 'Bedrijf',
                ))



                ->render($this);
        }

		public function performanceAction(){

			$solved_status = SupportStatusTypes::getByKey('solved');

			$select = db()->select()
				->from(array('sc' => 'support_complaints'), array('id'))
				->joinLeft(array('scd' => 'support_complaints_deadline'), 'scd.complaint = sc.id AND current = "1"', array('deadline' => 'date'))
				->joinLeft(array('ss' => 'support_status'), 'ss.map_to = sc.id AND ss.`type` = "complaint" AND `status` = ' . $solved_status['id'], array('solved' => 'IFNULL(ss.`date`, CURRENT_TIMESTAMP())'))
				->where('sc.inform_contact = ?', loginManager::data()->info['company_id'])
				//->where('')
				;

			$complaints = db()->fetchAll($select);
			$solved_on_time = 0;
			foreach($complaints as $complaint){
				$complaint['solved'] = strtotime($complaint['solved']);
				$complaint['deadline'] = strtotime($complaint['deadline']);

				if($complaint['solved'] <= $complaint['deadline'])
					$solved_on_time++;
			}
			
			$this->view->number_of_complaints = count($complaints);
			$this->view->solved_on_time = $solved_on_time;	
		}


        /**
         * @Inject
         * @var \ReceptionTelephoneSupport\Application\Service\CompanyInstruction\UpdateReceptionInstructionsByCompanyService
         */
        private $updateReceptionInstructionsByCompanyService;

        /**
         * @Inject
         * @var \ReceptionTelephoneSupport\Application\Service\CompanyInstruction\GetReceptionInstructionsByCompanyService
         */
        private $getReceptionInstructionsByCompanyService;

		/**
		 * Edit the Company indicated by the id parameter
		 * using an EditView
		 * 
		 * @return null
		 */
		public function editAction() {
            ini_set('memory_limit', '4096M');
            
			$this->view->Breadcrumbs()
				->addCrumb('Bedrijven overzicht', 'company/list/')
				->addCrumb('Bedrijf bewerken', '');
			$companyId = intval($this->_getParam('id')) > 0? intval($this->_getParam('id')) : null;
			$ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) ? $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest' : false;

			if ($companyId) {
				$companyModel = new Company();
				// this is to have these variables available inside the form
				$this->companyid = $params['id'] = $companyId;
				$this->deletionAllowed = $companyModel->deleteAllowed($companyId);
				$this->view->uoid = $this->uoid = $this->_getParam('uoid');
			}

			$this->view->form = $this->view->EditView($this, 'Company')
				->setOptions(array(
					'redirect_to' => $ajax ? false : 'breadcrumbs',
                    'loadHandler' => function($editView, $params)  {
					    if (Settings::get('module_reception_telephone_support_enabled') && isset($params['id'])) {


                            $companyId = $params['id'];

                            $receptionInstructionsModel = $this->getReceptionInstructionsByCompanyService->execute($companyId);

                            if ($receptionInstructionsModel) {
                                $editView->data['reception_instructions_support_form'] = [];
                                $editView->data['reception_instructions_support_form']['how_to_pickup_the_phone'] = $receptionInstructionsModel->getRepresentationInstructionsPickupPhone();
                                $editView->data['reception_instructions_support_form']['description_of_product_or_service'] = $receptionInstructionsModel->getDescriptionCompanyProductService();
                            }
                        }
                    },
					'postHandler' => function($post, $params) use ($ajax) {
						$isNew = (empty($params['company_form']['id']) && intval($params['company_form']['id']) <= 0);
						$c_model = new Company();
						$c_model->save_return_type = 'inserted_id';

						$companyId = $c_model->save($post, $params);

						if ($companyId) {
                            if (Settings::get('module_reception_telephone_support_enabled')) {

                                $postValue = $post['reception_instructions_support_form'];

                                $this->updateReceptionInstructionsByCompanyService->execute(
                                    [
                                        'representation_instructions_pickup_phone' =>
                                            $postValue['how_to_pickup_the_phone'],
                                        'description_company_product_service' =>
                                            $postValue['description_of_product_or_service']
                                    ]
                                    , $companyId
                                );
                            }
                                $userModel = new Users();
                                $companyRow = $c_model->getById($companyId);
                                $userRow = $userModel->getById(($companyRow->dummy_user));
                                $userRow->olddebtorcode = $post['company_form']['olddebtorcode'];
                                $userRow->save();
                        }


                        if(isset($_FILES['hospitality_logo']) && $_FILES['hospitality_logo']['name'] != ""){
                            $destination = Main::app()->getDir('hospitality') . $companyId . '/logo/' . $_FILES['hospitality_logo']['name'];
                            createFolder($destination);
                            move_uploaded_file($_FILES['hospitality_logo']['tmp_name'], $destination);
                            $data = ['hospitality_logo' => $_FILES['hospitality_logo']['name']];
                            $where = ['id = ?' => $companyId];
                            
                            if (is_numeric($companyId))
                                db()->update('company', $data, $where);
                        }

						if($ajax && $companyId || $isNew){
							$this->getHelper('Redirector')->goToUrl('company/edit/id/' . $companyId . '/');
						} else {
							return $companyId ? true : false;
						}
					}
				))
				->render($this);
		}

		public function findContractantsAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$q = $this->_getParam('value');
			$user = new User();

			if(!isset($q) || strlen($q) < 2 ) {
				echo json_encode(array());
				return;
			}

			$results = $user->find(array(
				'name' => $q,
				'verbose' => true,
				'limit' => 20,
			));

			$output = array();
			foreach ($results as $i => $res) {
				$output[] = array(
					'id'			=> $res['id'],
					'name'			=> $res['buildname'],

					'gender'		=> $res['gender'],
					'initials'		=> $res['initials'],
					'firstname'		=> $res['firstname'],
					'middlename'	=> $res['middlename'],
					'lastname'		=> $res['name'],
					'BSN'			=> $res['bsn'],
					'bdate'			=> strtotime($res['bdate']) > 0? date('d-m-Y', strtotime($res['bdate'])): '',
					'email_address'	=> $res['email'],
					'phone_primary'	=> $res['phone1'],
				);
			}

			echo json_encode($output);
		}

		/**
		 * Deletes the Company indicated by the id parameter
		 * and redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function deleteAction() {
			$this->disableView();
			$c = new Company();

			if($c->delete($this->_getParam('id')) !== false)
				$this->_helper->redirector('list', 'company');
			else
				$this->_helper->redirector('edit', 'company', array('id', $this->_getParam('id')));
		}


		public function welcomeAction() {
			

			// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
			$this->view->activeMainTab = 'welcome';
		}

		public function complaintsAddAction(){
            $this->view->headLink()->appendStylesheet('media/style/datepicker/vlaCal-v2.1.css');
            $this->view->headLink()->appendStylesheet('media/style/autocompleter.css');

			$map_to_multi_search = $this->_getParam('map_to_multi_search');
			$id = $this->_getParam('id');

            if ($_POST && isset($_POST['title'])) {
                $complaintController =
                    new ComplaintController(
                        $this->getRequest(),
                        $this->getResponse()
                    );
                if($id){
                    $complaintController->editAction();
                } else {
                    $complaintController->addAction();
                }
                // if the editAction or addAction dont redirect, reshow the form with errors:
                $this->render('complaint/add', null, true);
            } elseif ($map_to_multi_search || $id) {
                $this->findActiveComplaintsInSameArea();

                $this->view->mapToMultiSearch = $this->_getParam('map_to_multi_search');
                $this->view->id = $id;
                $this->view->mapTo = $this->_getParam('mapTo');
                $this->view->type = $this->_getParam('type');
			} else {
				$this->view->complaint = new Form($this, 'support/complaint/add-direct');
				$this->render('complaint/add-direct', null, true);
			}
		}

        private function findActiveComplaintsInSameArea()
        {
            try {
                $findZipcodeByTypeAndMapToService =
                    new \application\models\Controllers\Complaint\FindZipcodesByTypeAndMapToService();

                $zipcodesForTypeAndMapTo = $findZipcodeByTypeAndMapToService->execute(
                    $this->getParam('type'),
                    $this->getParam('mapTo')
                );

                $findActiveComplaintsInSameAreaService =
                    new \application\models\Controllers\Complaint\FindActiveComplaintsInSameAreaService(
                        new Objectusers()
                    );

                $activeComplaintsInSameArea = $findActiveComplaintsInSameAreaService->execute(
                    $zipcodesForTypeAndMapTo
                );

            } catch (Exception $exception) {
                return;
            }

            if ($this->getParam('id')) {
                $activeComplaintsInSameArea = array_filter(
                    $activeComplaintsInSameArea,
                    function ($activeComplaint) {
                        return $activeComplaint['id'] != $this->getParam('id');
                    }
                );
            }

            $this->view->activeComplaintsInSameArea = $activeComplaintsInSameArea;
        }

		/**
		 * Returns al employee roles that can still be assigned to employees
		 * of a given company as a json array. One entry in the array is always
		 * company_id with the id as its value.
		 */
		public function getAvailableEmployeeRolesAction() {
			$roles = Company::getAvailableEmployeeRoles(intval($this->_getParam('company_id')));
			$roles['company_id'] = intval($this->_getParam('company_id'));

			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			echo json_encode($roles);
		}

		public function dashboardAction() {
			$this->view->headLink()->appendStylesheet('media/style/complaint/unfinished-work.css');
			$this->view->closed_only = $this->_getParam('closed_only');
		}

        public function processingAction() {
            $this->view->headLink()->appendStylesheet('media/style/complaint/unfinished-work.css');
            $this->view->closed_only = $this->_getParam('closed_only');
        }

        public function appointmentAction() {
            $this->view->headLink()->appendStylesheet('media/style/complaint/unfinished-work.css');
            $this->view->closed_only = $this->_getParam('closed_only');
        }

        public function employeeInstructionsAction() {

        }

        public function contactAction()
        {
            $contact = new Form($this, 'company/contact');
            $contact->setAction('company/contact');
            $this->view->contact = $contact;

            if ($this->getRequest()->isPost() && $contact->isValid($_POST))
            {
                $data = $_POST;
                $to = EmailAccount::getDefault();
                new EmailOutgoing(array(
                    'from' => array('email' => $to['address'], 'name' => $to['name']),
                    'to' => array('email' => $to['address'], 'name' => $to['name']),
                    'subject' => "Vraag/opmerkingen via bedrijven portal",
                    'data' => $data,
                    'template' => 'company/contact.phtml',
                ));
                $this->render('thanks');
            } else
            {
                $this->view->activeMainTab = 'contact';
            }
        }

		function documentsAction() {
			$params    = $this->getAllParams();
			$cdModel   = new Controllers_Company_Documents();
			$documents = $cdModel->getDocuments( $params );

			$this->view->listView = $this->view->listView( $documents )
                   ->setTypes( [
                       'category'    => [
                           'title' => 'Categorie',
                           'width' => 'large truncate',
                       ],
                       'subcategory' => [
                           'title' => 'Document',
                           'width' => 'large truncate',
                       ],
                       'title'       => [
                           'title' => 'Titel',
                           'width' => 'xxlarge truncate',
                       ],
                       'open'        => [
                           'title' => 'Open',
                           'width' => 'xxsmall',
                       ],
                   ] )
                   ->addFormat( 'project', function ( $value ) {
                       return $value;
                   } )
                   ->addFormat( 'category', function ( $value ) {
                       return $value;
                   } )
                   ->addFormat( 'subcategory', function ( $value ) {
                       return $value;
                   } )
                   ->addFormat( 'title', function ( $value ) {
                       return $value;
                   } )
                   ->addFormat( 'open', function ( $value ) {
                       return "<a target='_blank' class='roundedButton icon view' href='document/download/id/$value'></a>";
                   } )
                   ->setFilters( [
                       'project'     => [
                           'renderSeparately' => true,
                           'title'            => 'Project'
                       ],
                       'category'    => [ ],
                       'subcategory' => [ ],
                       'title'       => [ 'type' => 'input' ],
                   ] )
                   ->setOptions( [
                       'item_title'           => 'Documenten',
                       'show_title'           => false,
                       'render_to_controller' => false,
                   ] )
                   ->render( $this );
		}
	}
