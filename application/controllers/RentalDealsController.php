<?

use Object\EnableObjectPublicationService;

class RentalDealsController extends GlobalController {


		public function detailsAction(){

			$this->view->id = $id = $this->_getParam('id');

			$details = new Controllers_RentalDeals_Details($id);

			$this->view->statusses = $details->getStatusses();
			$this->view->data = $data = $details->getData();
			$this->view->available_date = Settings::get('modules_rental_contract_deals') ? false : $details->getAvailableDate($data['object_id']);
			$this->view->internalUsers = $details->getInternalUsers();
		}

		public function editAction(){
			$this->view->Breadcrumbs()->addCrumb('Deal toevoegen');

			$model = new RentalDeals();

			$this->view->form = $this->view->EditView($this, 'RentalDeals', ['id' => $this->_getParam('id')])
				->setOptions([
					'show_title' => !$this->isAjaxRequest,
					'postHandler' => function($post, $params) use ($model){



						$match_attributes = ['archived' => false, 'cancelled' => false];

						if(Settings::get('modules_rental_contract_deals')) {
							$match_attributes['contract_id'] = $post['rental_deals']['contract_id'];
						} else {
							$match_attributes['object_id'] = $post['rental_deals']['object_id'];
							$match_attributes['user_id'] = $post['rental_deals']['user_id'];
						}

						if($post['rental_deals']['force'] !== '1'){

                            if($model->matchRow($match_attributes)) {
                                echo '<ul class="errors"><li>Deze deal bestaat reeds!</li></ul>';
                                return false;
                            }
                            if (Settings::get('modules_user_project')) {
                                $branchelocation = new BranchLocation();
                                $branchelocationRow = $branchelocation->findByObjectId($match_attributes['object_id']);
                            } else {
                                $branchelocationRow['double_registrant_workflow_allowed'] = 0;
                            }

                            if(Settings::get('modules_rental_workflow_deals') &&  $branchelocationRow['double_registrant_workflow_allowed'] != 1 && Settings::get('modules_rental_website_deal_viewing_inform_current_tenant')) {
                                if ($model->getNumberOfActiveDealsForUserId($match_attributes['user_id'])) {
                                    echo '<ul class="errors"><li>Er bestaat reeds een actieve deal voor deze kandidaat</li></ul>';
                                    return false;
                                }
                                else if ($model->getNumberOfActiveDealsForObjectId($match_attributes['object_id'])) {
                                    echo '<ul class="errors"><li>Er bestaat reeds een actieve deal voor dit object</li></ul>';
                                    return false;
                                }
                            }
                        }

						$result = $model->save($post, $params);

						if(Settings::get('modules_rental_workflow_deals')){
                            $this->redirect('rental-deals_workflow/list/');
                        } else {
                            die($result);
                        }
					}
				])
				->render($this);
		}

        public function createMissingUserObjectsForDealsAction()
        {
            $this->disableView();

            $select = db()->select()
                ->from('rental_deals', ['id'])
                ->where('cancelled = ?', false)
                ->where('archived = ?', false)
                ->where('concept_deal_uo_id IS NULL');

            $rentalDealModel = new RentalDeals();
            $service = new \Controllers\RentalDeals\CreateConceptUserObjectServiceForDealService(
                $rentalDealModel
            );

            foreach (db()->fetchCol($select) as $dealId) {
                $service->execute($dealId);
            }
        }

        public function cancelAction(){

			$this->view->id = $id = $this->_getParam('id');

			$cancel = new Controllers_RentalDeals_Cancel($id);

			$this->view->statusses = $cancel->getStatusses();

			if($_POST) {
				$cancel->cancel($id, $_POST);
                $rd = new RentalDeals();
                $dealData = $rd->getById($id);

                if (Settings::get('modules_rental_website_enabled') && Settings::get('general_company_shortname') == 'MVGM') {
                    $enableObjectPublicationService = new EnableObjectPublicationService(
                        new \ObjectsPublish()
                    );
                    $enableObjectPublicationService->execute($dealData['object_id']);
                }
                
                $this->disableView();
			}
		}

		public function makeConceptAction(){
			$this->view->id = $id = $this->_getParam('id');

			$model = new RentalDeals();
			$row = $model->getById($this->getParam('id'));

			if($_POST) {
				$uo_model = new Objectusers();
                $u_model = new Users();

				$empty_period_select = $uo_model->select()
					->where('object = ?', $row->object_id)
					->where('customer = 0')
					->order('from DESC');

				foreach($uo_model->fetchAll($empty_period_select) as $empty_uo_row){
					if($empty_uo_row->till && strtotime($empty_uo_row->till) < strtotime($_POST['from'])) continue;

					$empty_uo_row->till = date('Y-m-d', strtotime('-1 days', strtotime($_POST['from'])));

					// empty uo row no longer needed, delete it
					if(strtotime($empty_uo_row->till) <= strtotime($empty_uo_row->from))
						$uo_model->delete('id = ' . $empty_uo_row->id);
					else
						$empty_uo_row->save();
				}

				$uo_id = $uo_model
					->createRow([
						'object' => $row->object_id,
						'customer' => $row->user_id,
						'from' => date('Y-m-d', strtotime($_POST['from'])),
						'finaldate' => 0,
						'services' => 'rent_deal'
					])
					->save();

				$row->concept_uo_deal = true;
				$row->concept_deal_uo_id = $uo_id;
				$row->save();

                $u_row = $u_model->getById($row->user_id);

                if($u_row->type == 'registrant') {
                    $u_row
                        ->setFromArray([
                            'type' => 'tenant',
                            'profile_completion' => 2
                        ])
                        ->save();
                }
			} else {

				$details = new Controllers_RentalDeals_Details($id);
				$data = $details->getData();
				$this->view->available_date = $details->getAvailableDate($data['object_id']);

				$a_model = new Address();
				$this->view->address = $a_model->buildname($row->object_id, 'object');
				$this->view->deal = $row;
			}
		}

		public function makeFinalAction(){
			$this->disableView();

			$model = new RentalDeals();
			$row = $model->getById($this->getParam('id'));
			$row->final_deal = true;
			$row->save();

			$uo_model = new Objectusers();
			$u_model = new Users();

			$uo_row = $uo_model->getById($row->concept_deal_uo_id);

			$uo_row
				->setFromArray(['finaldate' => true])
				->save();

			$empty_period_select = $uo_model->select()
				->where('object = ?', $row->object_id)
				->where('customer = 0')
				->order('from DESC');

			$u_row = $u_model->getById($uo_row->customer);

			if($u_row->type == 'registrant')
				$u_row
					->setFromArray(['type' => 'tenant'])
					->save();

			foreach($uo_model->fetchAll($empty_period_select) as $empty_uo_row){
				if(!$empty_uo_row->till) continue;

				$empty_uo_row->finaldate = true;
				$empty_uo_row->save();
			}
		}

		public function archiveAction(){
			$this->disableView();

			$model = new RentalDeals();
			$row = $model->getById($this->getParam('id'));
			$row->archived = true;
			$row->save();
		}

		public function setAssignedUserAction(){
			$this->disableView();

			$model = new RentalDeals();
			$row = $model->getById($this->getParam('id'));
			$row->assigned_user_id = $this->getParam('user');
			$row->save();
		}

		public function saveNoteForStatusAction(){
			$this->disableView();

			$model = new RentalDealsStatus();

			$attributes = [
				'deal_id' => $this->getParam('id'),
				'type_id' => $this->getParam('type')
			];

			$row = $model->matchRow($attributes);
			$row = $row ?: $model->createRow($attributes);

			$row
				->setFromArray(['note' => $this->getParam('note')])
				->save();
		}

		public function toggleStatusAction(){
			$this->disableView();

			$deal_model = new RentalDeals();

			$data = [];

			if($toggle =  $this->getParam('toggle')){
			    $data['done'] = $toggle;
            }

            if($note =  $this->getParam('note')){
			    if(trim($note) !== '') {
                    $data['note'] = $note;
                }
            }

            if(!is_null($this->getParam('assigned_user_id'))) {
			    $data['assigned_user_id'] = $this->getParam('assigned_user_id');
            }

			$deal_model->saveStatus(
                $this->getParam('id'),
                $this->getParam('type'),
                $data
            );

		}

		public function saveContactDateAction(){
			$this->disableView();

			$model = new RentalDeals();

			if($this->getParam('contact-date') === ''){
                $contact_date = nullValue();
            } else {
			    $contact_date = date('Y-m-d', strtotime($this->getParam('contact-date')));
            }

			$modelRow = $model->getById($this->getParam('id'));

			$modelRow
				->setFromArray([
                    'contact-date' => $contact_date,
                    'assigned_user_id' => nullValue(),
                ])
				->save();

			$model->saveStatus($modelRow->id, $modelRow->next_status_id, ['assigned_user_id' => '']);
		}

		public function notesListAction(){
			$model = new Controllers_RentalDeals_Notes($this->getParam('deal'));
			$this->view->list = $model->getList();
		}

		public function deleteNoteAction(){
			$this->disableView();

			if(!($id = $this->getParam('id'))) return;


			$model = new RentalDealsNotes();
			$model->delete('id = ' . $id);
		}

		public function saveNoteAction(){
			$this->disableView();

			$note = trim($this->getParam('note'));
			$deal = $this->getParam('deal');
			$id = $this->getParam('id');

			if(!(strlen($note) > 0)) return;

			$model = new RentalDealsNotes();

			if($id > 0)
				$row = $model->getById($id);
			else
				$row = $model->createRow();

			$row
				->setFromArray([
					'rental_deals_id' => $deal,
					'note' => $note
				])
				->save();
		}

        public function unsetPublishStatusOverrideAction()
        {
            $this->disableView();
            $rentalDealModelRowId = $this->getParam('id');

            $unsetPublishStatusOverrideService = new \Controllers\RentalDeals\UnsetPublishStatusOverrideService(
                new RentalDeals(),
                new ObjectsPublish()
            );

            $unsetPublishStatusOverrideService->execute($rentalDealModelRowId);
        }
	}
