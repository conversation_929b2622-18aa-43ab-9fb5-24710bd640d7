<?php

use ReceptionTelephoneSupport\Domain\Model\CompanyInstruction\CompanyNotFoundException;

class ReceptionTelephoneSupport_VoipController extends GlobalController
{

    /**
     * This is necessary to fix the menu in the talent portal
     */

    public function preDispatch()
    {
        parent::preDispatch();
        $this->renderWithNewLayout();

        $this->view->first = $this->firstLogin = loginManager::data()->info['first'] == '1';

        $this->loadTenantMenuOptions();
    }

    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\ViewIncomingCallService
     */
    private $viewIncomingCallService;

    public function incomingCallAction()
    {
        $incomingCalledPhoneNumber = $this->getParam('called');
        $incomingCallerPhoneNumber = $this->getParam('caller');

        if (Settings::get('module_reception_telephone_redirect_support_page')) {
            $userWithPhoneNumber = PhoneNumber::getUserIdFromPhoneNumber($incomingCalledPhoneNumber);
            if ($userWithPhoneNumber['type'] == 'user') {
                $this->_redirect('support/show', ['id' => $userWithPhoneNumber['map_to'] , 'type' => 'user']);
            } else if ($userWithPhoneNumber['type'] == 'company') {
                $dummyuser = Company::findDummyUser($userWithPhoneNumber['map_to']);
                $this->_redirect('support/show', ['id' => $dummyuser , 'type' => 'user']);
            }
            return;
        }

        try {
            $data = $this->viewIncomingCallService->execute($incomingCalledPhoneNumber);

            $this->view->companyModel = $data['companyModel'];
            $this->view->receptionInstructionsModel = $data['receptionInstructionsModel'];
            $this->view->employeeModelList = $data['employeeModelList'];
            $this->view->employeeInstructionsList = $data['employeeInstructionsList'];
            $this->view->mailTemplateId = $data['mailTemplateId'];


            $this->view->Breadcrumbs()
                ->addCrumb('Binnenkomend telefoongesprek')
                ->addCrumb($incomingCalledPhoneNumber)
                ->addCrumb($data['companyModel']->getFullName());

            $this->view->incomingCalledPhoneNumber = $incomingCalledPhoneNumber;
            $this->view->incomingCallerPhoneNumber = $incomingCallerPhoneNumber;

        } catch (CompanyNotFoundException $companyNotFoundException) {
            $this->view->incomingCalledPhoneNumber = $incomingCalledPhoneNumber;
            $this->view->Breadcrumbs()
                ->addCrumb('Binnenkomend telefoongesprek')
                ->addCrumb($incomingCalledPhoneNumber)
                ->addCrumb('Error');

            // render oder phtml
            $this->render('incoming-call-error');
            return;
        }

        $this->view->headScriptHashed()
            ->appendFile('media/javascript/reception_telephone_support/loader.js')
            ->appendFile('media/javascript/reception_telephone_support/employee_select_switch.js')
            ->appendFile('media/javascript/reception_telephone_support/switch_items_elements.js')
            ->appendFile('media/javascript/reception_telephone_support/send_mail.js');

        return;
    }


    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\EmployeeInstructionsZendFormGenerateService
     */
    private $employeeInstructionsZendFormGenerateService;

    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\ViewEmployeeInstructionsService
     */
    private $viewEmployeeInstructionsService;

    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\UpdateEmployeeInstructionsService
     */
    private $updateEmployeeInstructionsService;

    public function employeeInstructionsAction()
    {
        $userId = loginManager::data()->id;
        $form = new Form($this, 'ReceptionTelephoneSupport/employeeInstructions');
        $form->setAction('')
            ->setMethod('post');

        try {
            $form = $this->employeeInstructionsZendFormGenerateService->execute($userId, $form);
        } catch (Exception $exception) {
            echo $exception->getMessage();
            die('Cant build form fields');
        }

        $form->addElement('submit', 'submit', array(
            'label' => 'Update'
        ));

        $viewData = $this->viewEmployeeInstructionsService->execute($userId);

        $form->populate($viewData['formData']);

        $this->view->employeeList = $viewData['employeeList'];

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
            $data = $this->getRequest()->getPost();

            $this->updateEmployeeInstructionsService->execute($data, $userId);
        }

        $this->view->form = $form;
    }

    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\CompanyInstruction\ViewCompanyInstructionService
     */
    private $viewCompanyInstructionService;

    /**
     * @Inject
     * @var \ReceptionTelephoneSupport\Application\Service\CompanyInstruction\UpdateReceptionInstructionsService
     */
    private $updateReceptionInstructionsService;

    public function receptionInstructionsAction()
    {
        $form = new Form($this, 'ReceptionTelephoneSupport/receptionInstructions');
        $userId = loginManager::data()->id;

        $receptionRepresentationCompanyInstructionsData = $this->viewCompanyInstructionService->execute($userId);
        if ($receptionRepresentationCompanyInstructionsData) {
            $form->populate($receptionRepresentationCompanyInstructionsData);
        }

        if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
            $data = $this->getRequest()->getPost();

            $this->updateReceptionInstructionsService->execute($data, $userId);
        }

        $this->view->form = $form;
    }
}

