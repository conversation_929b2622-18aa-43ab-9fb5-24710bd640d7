<?php

use Accounting\Infrastructure\Twinfield\TwinfieldOAuthConnection;
use MailServerConnection\Application\Service\HandleCodeServiceFactory;

class SettingsController extends GlobalController {
	
		public function init(){
			$this->view->Breadcrumbs()->addCrumb('Instellingen', 'settings/');
			
			return parent::init();
		}
		
		public function indexAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Software Instellingen overzicht');
				

		}

		public function publicAction(){
			
			$this->view->Breadcrumbs()
				->addCrumb('Software Instellingen overzicht');
				

		}
		
		public function editAction(){
			$this->view->Breadcrumbs()->addCrumb('Instellingen bewerken');
			
			$this->view->EditView($this, 'settings', array())
							
				->setOptions(array(
					'redirect_to' => loginManager::data()->rights == 'admin' ? 'breadcrumbs' : false
				))
				
				->render($this);
		}


        public function layoutAction(){

            $this->view->headScriptHashed()->appendFile('media/javascript/image-upload.js');
		    $this->view->Breadcrumbs()->addCrumb('Briefpapier instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/layout',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }


        public function invoiceRemindsAction(){
            $this->view->Breadcrumbs()->addCrumb('Instellingen herinneringen en aanmaningen');
            $this->view->EditView(
                $this,
                'settings/invoicereminds',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function serviceChargesAction(){
            $this->view->Breadcrumbs()->addCrumb('Instellingen servicekosten');
            $this->view->EditView(
                $this,
                'settings/servicecharges',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function investorProvisionsAction() {
            $this->view->headScriptHashed()->appendFile('media/javascript/image-upload.js');
            $this->view->Breadcrumbs()->addCrumb('Instellingen beleggerafekeningen');
            $this->view->EditView(
                $this,
                'settings/investorprovisions',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function invoiceAction(){


            $this->view->Breadcrumbs()->addCrumb('Factuur instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/invoice',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function purchaseInvoiceAction(){


            $this->view->Breadcrumbs()->addCrumb('Inkoopfactuur instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/purchase-invoice',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function indexationAction(){


            $this->view->Breadcrumbs()->addCrumb('Indexatie instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/indexation',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }


        public function collectionAction(){

		    $this->view->Breadcrumbs()->addCrumb('Factuur instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/collection',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function complaintsAction(){

            $this->view->Breadcrumbs()->addCrumb('Klachten en storingen instellingen bewerken');
            $this->view->EditView(
                $this,
                'settings/complaints',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

        public function rentalGeneralsettingsAction(){

            $this->view->Breadcrumbs()->addCrumb('Algemene verhuur instellingen');
            $this->view->EditView(
                $this,
                'settings/rental/generalsettings',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }



        public function rentalMediapartnersAction(){

		    $this->view->Breadcrumbs()->addCrumb('Mediapartners');
            $this->view->EditView(
                $this,
                'settings/rental/mediapartners',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }

		public function financialSystemAction()
		{
		    $this->renderOldTwinfieldVersionNotification();

            $this->view->headScriptHashed()->appendFile('media/javascript/image-upload.js');
            $this->view->Breadcrumbs()->addCrumb('Financieel systeem instellingen bewerken');

            $this->view->EditView(
                $this,
                'settings/financialsystemsettings',
                [
                    'modeloverride' => 'Settings',
                ]
			)
			->render($this);
		}

        private function renderOldTwinfieldVersionNotification()
        {
            if (loginManager::data()->id != Users::OMNIBOXX_ADMIN_USER_ID) {
                return;
            }

            if (loginManager::data()->rights !== 'admin' || !DEBUGGING) {
                return;
            }

            if (Settings::get('financial_export_system') !== 'twinfield') {
                return;
            }

            $this->render('twinfield-old-version-notification');
        }

    public function mailChimpAction()
    {
        $this->view->EditView(
            $this,
            'settings/mailchimp',
            [
                'modeloverride' => 'Settings',
            ]
        )
            ->render($this);
    }


    public function parariusAction()
    {
        $this->view->EditView(
            $this,
            'settings/pararius',
            [
                'modeloverride' => 'Settings',
            ]
        )
            ->render($this);
    }

    public function zenvoicesAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Algemene Zenvoices instellingen');
        $this->view->EditView(
            $this,
            'settings/zenvoices',
            [
                'modeloverride' => 'Settings',
            ]
        )
            ->addLegend('zenvoices-legend')
            ->render($this);
    }

    public function wonen31Action()
    {
        $this->view->Breadcrumbs()->addCrumb('Algemene Wonen31 instellingen');
//        $this->
        $this->view->EditView(
            $this,
            'settings/wonen31',
            [
                'modeloverride' => 'Settings',
            ]
        )
            ->addLegend('wonen31-settings-legend')
            ->render($this);
    }

		public function oauthAction() {
			$this->view->Breadcrumbs()->addCrumb('Externe koppelingen beheren');

			$params = $this->getAllParams();
			unset($params['controller'], $params['action'], $params['module']);

			switch ($params['redirect']) {
				case 'exact_online':
					header('Location: '. ExactXML::getAuthUrl());
					break;

                case 'multivers':
                    $multivers = new MultiversClient();
                    $urlMultiverse = $multivers->getAuthUri();
                    $urlMultiverse = str_replace('%3Fredirect%3Dmultivers', '', $urlMultiverse);
                    header('Location: '. $urlMultiverse);
                    break;
                case 'twinfield':
                    TwinfieldOAuthConnection::redirectForAuthorization();
                    break;
			}

			if(!empty($params['code'])) {

				switch ($params['service']) {
                    case 'multivers':
                        $multivers = new MultiversClient();
                        $token = $multivers->requestAccessToken($params['code']);
                        $this->view->multiverseAccessTokenSuccesful = (!empty($token) && is_object($token));
                        break;

                    case 'twinfield':
                        (new TwinfieldOAuthConnection())->acquireAccessTokenByCode($params['code']);
                        $this->view->twinfieldAccessTokenSuccesful = true;
                        break;

                    case 'email_account':
                        $code = $this->getParam('code');
                        $emailAccountId = $this->getParam('email_account_id');

                        $handleCodeService = (new HandleCodeServiceFactory())->build($emailAccountId);

                        try {
                            $handleCodeService->execute($emailAccountId, $code);
                        } catch(Exception $e) {
                            error_log($e->getMessage());
                            error_log($e->getTraceAsString());
                            $this->redirect('email/modern-auth/email_account_id/' . $emailAccountId . '/has_error/1');
                            break;
                        }

                        $this->redirect('email/modern-auth/email_account_id/' . $emailAccountId);
                        break;

					default:
						$exactAccessToken = ExactXML::getAccessToken($params['code']);
						$this->view->exactAccessTokenSuccesful = (!empty($exactAccessToken) && is_object($exactAccessToken));
						break;

				}
			}

			if (Settings::get('financial_export_system') === 'twinfield') {
                $this->view->isTwinfieldConnectionActive = (new TwinfieldOAuthConnection())->isConnectionActive();
            }
		}

		public function refreshAction(){
			$this->disableView();

			Settings::updateSessionStorage();
			$this->_redirect('settings/index');
		}
	}
