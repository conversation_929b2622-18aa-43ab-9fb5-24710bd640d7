<?php

use TiaraWonen2\Application\Service\Error\ViewErrorsService;

class Funda_ErrorController  extends \GlobalController
{
    public function listAction()
    {
        $omniboxxEntityType = $this->getParam('type');
        $omniboxxEntityId = $this->getParam('id');

        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb(ucfirst($omniboxxEntityType))->addCrumb('Errors');

        $data = (new ViewErrorsService())->execute($omniboxxEntityType, $omniboxxEntityId);

        $types = [
            'created_at' => ['title' => 'Gemaakt op', 'width' => 'medium'],
            'description' => ['title' => 'Omschrijving', 'width' => 'xxxxxxxlarge'],
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->addLegend('funda-error-legend')
            ->addFormat('description', function ($value) {

                $data = json_decode($value, true);

                $string = "";

                if (is_array($data)) {
                    foreach ($data as $error) {

                        if (isset($error['Messages']) && is_array($error['Messages'])) {
                            foreach ($error['Messages'] as $message) {
                                $string .= "Veldnaam: {$error['FieldName']}: - Melding: $message <br>";
                            }
                        } else {
                            $string .= "$error <br>";
                        }

                    }

                    return $string;
                }

                return $value;
            })
            ->render($this);
    }
}
