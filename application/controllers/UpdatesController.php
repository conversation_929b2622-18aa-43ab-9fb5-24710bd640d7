<?php

class UpdatesController extends GlobalController {

    /**
     * @var \Controllers\Updates\AbstractUpdatesHelper;
     */
    private $updateHelper;

    public function preDispatch()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Omniboxx updates', '');

        $updateClassName = 'Controllers\Updates\\' . $this->getParam('update');
        $this->updateHelper = new $updateClassName();
        $this->view->update = $this->getParam('update');
        $this->view->update_name = $this->updateHelper->getUpdateName();
    }

    public function markAsApprovedAction(){
        $this->disableView();

        $this->updateHelper->markAsApproved();
    }

    public function notifyAction(){
        if($this->getParam('redirect')) {
            if ($this->updateHelper->isApproved() && $_SESSION['UPDATES_CONTROLLER_HTTP_REFERER']) {
                header('Location: ' . $_SESSION['UPDATES_CONTROLLER_HTTP_REFERER']);
            }
        } else {
            $this->updateHelper->markAsNotified();
        }
    }
}
