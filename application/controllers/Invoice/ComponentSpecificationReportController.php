<?php

class Invoice_ComponentSpecificationReportController extends GlobalController {

    /**
     * @var \Controllers\Invoice\ComponentSpecificationReport $data_model;
     */
    private $data_model;

    public function preDispatch()
    {
        if(is_null($this->getParam('year'))){
            $this->setParam('year', date('Y'));
        }

        $this->data_model = new \Controllers\Invoice\ComponentSpecificationReport($this->getParam('year'));
        $isInitializing = $this->view->pageInfo['action'] === 'index' && !$this->isAjaxRequest;
        $this->handleCorporationsFilterValue($isInitializing);
        $this->handleProjectsFilterValue($isInitializing);
        $this->handleObjectgroupFilterValue($isInitializing);

        $this->data_model->setStartPeriod($this->getParam('start_period'));
        $this->data_model->setEndPeriod($this->getParam('end_period'));

        $ledger = $this->getParam('ledger');
        $this->data_model->setLedger($ledger);
    }

    public function indexAction(){
        $this->view->headScriptHashed()->appendFile('media/javascript/components/corporation_project_filters.js');

        $this->view->Breadcrumbs()
            ->addCrumb('Omzet specificatie');

        $data = $this->data_model->getData();

        $this->view->listView($data)

            ->setTypes([
                'component' => ['column' => 'component', 'title' => 'Component', 'width' => 'xlarge ellipsis'],
                'ledger' => ['column' => 'ledger', 'title' => 'Grootboek', 'width' => 'medium ellipsis'],
                'value_excl' => ['column' => 'value_excl', 'title' => 'Excl. BTW', 'width' => 'large right'],
                'value_tax' => ['column' => 'value_tax', 'title' => 'BTW', 'width' => 'medium right'],
                'value_incl' => ['column' => 'value_incl', 'title' => 'Incl. BTW', 'width' => 'large right'],
                'spacer' => ['title' => '', 'width' => 'xxxsmall'],
                'details_button' => ['title' => 'Facturen', 'width' => 'small']
            ])

            ->addFormat(['value_excl', 'value_tax', 'value_incl'], 'money')

            ->addFormat('component', function($value, $item){
                return '<a target="_blank" href="components/edit/id/' . $item['component'] . '">' . $item['component_name'] . '</a>';
            })

            ->addFormat('details_button', function($value, $item){
                $url_parameters = [
                    'year' => $this->data_model->getYear(),
                    'ledger' => $item['ledger'],
                    'component' => $item['component'],
                ];

                if(count($this->data_model->getCorporations()) > 0){
                    $url_parameters['corporations'] = implode(',', $this->data_model->getCorporations());
                }

                if(count($this->data_model->getProjects()) > 0){
                    $url_parameters['projects'] = implode(',', $this->data_model->getProjects());
                }

                if (count($this->data_model->getObjectGroups()) > 0) {
                    $url_parameters['objectgroups'] = implode(',', $this->data_model->getObjectGroups());
                }

                if($this->data_model->getStartPeriod() > 1){
                    $url_parameters['start_period'] = $this->data_model->getStartPeriod();
                }

                if($end_period = $this->data_model->getEndPeriod()){
                    if($end_period < 12) {
                        $url_parameters['end_period'] = $end_period;
                    }
                }

                $url_parameter_string = '';

                foreach($url_parameters as $url_parameter_name => $url_parameter_value){
                    $url_parameter_string .= $url_parameter_name . '/' . urlencode($url_parameter_value) . '/';
                }

                return '<a target="_blank" href="invoice_component-specification-report/invoices/' . $url_parameter_string . '">
                    <i class="fa fa-list-ul"></i>
                </a>';
            })

            ->setFilters(
                $this->getReportFilters()
            )

            ->addTotals(['value_excl', 'value_tax', 'value_incl'])

            ->setOptions([
                'noEditButton' => true,
                'item_title' => 'Component',
                'paginator' => false
            ])

            ->render($this);
    }

    private function invoicesBuildPageTitle(){

        $ledger = $this->data_model->getLedger();
        $corporations = $this->data_model->getCorporations();
        $projects = $this->data_model->getProjects();

        if((!$ledger) || count($corporations) == 0){
            die();
        }

        if(count($corporations) == 1 ){
            $corporation_name_select = db()->select()
                ->from('corporations', ['name'])
                ->where('id = ?', end($corporations));

            $corporations_name =  db()->fetchOne($corporation_name_select);
        } else {
            $corporations_name = 'meerdere entiteiten';
        }

        if($this->data_model->getStartPeriod() == 1 && $this->data_model->getEndPeriod() == 12){
            $period_name = ' jaar ' . $this->data_model->getYear();
        } else {
            $period_name = ' periode ' .
                str_pad($this->data_model->getStartPeriod() ?: 1, 2, '0', STR_PAD_LEFT) .
                ' t/m ' .
                str_pad($this->data_model->getEndPeriod() ?: 12, 2, '0', STR_PAD_LEFT) .
                ' van ' .
                $this->data_model->getYear();
        }

        if(count($projects) == 1){
            $projects_name_select = db()->select()
                ->from('projects', ['name'])
                ->where('id = ?', end($projects));

            $projects_name = ' - project: ' .  db()->fetchOne($projects_name_select);
        } elseif(count($projects) > 1) {
            $projects_name = ' - (meerdere projecten)';
        } else {
            $projects_name = '';
        }


        $this->view->Breadcrumbs()
            ->addCrumb('Omzet specificatie')
            ->addCrumb('Facturen ' . $corporations_name . ' - ' . $period_name . $projects_name . ($ledger ? ' - grootboek ' . $ledger : ''));
    }

    public function invoicesAction(){
        //$this->invoicesBuildPageTitle();

        $this->data_model->setInvoiceGrouping(true);
        $data =  $this->data_model->getData();

        $this->view->listView($data)
            ->setTypes([
                'invoice' => ['title' => 'Factuur', 'width' => 'large ellipsis'],
                'start' => ['title' => 'Boekdatum', 'width' => 'small'],
                'recipient' => ['title' => 'Ontvanger', 'width' => 'xlarge ellipsis'],
                'component' => ['column' => 'component', 'title' => 'Component', 'width' => 'xsmall ellipsis'],
                'value_excl' => ['column' => 'value_excl', 'title' => 'Excl. BTW', 'width' => 'small right'],
                'value_tax' => ['column' => 'value_tax', 'title' => 'BTW', 'width' => 'small right'],
                'value_incl' => ['column' => 'value_incl', 'title' => 'Incl. BTW', 'width' => 'small right']
            ])

            ->addFormat('start', 'date')

            ->addFormat('invoice', function($value, $item){
                return '<a href="invoice/export/id/' . $value.'" target="_blank">' . $item['identifier'] . '</a>';
            })

            ->addFormat('recipient', function($value, $item){
                if($item['user_recipient_id'] > 0){
                    if($item['user_recipient_lastname'] != '') {
                        return '<a target="_blank" href="support/show/type/user/id/' . $item['user_recipient_id'] . '/">' . $item['user_recipient_lastname'] . '</a>';
                    }else {
                        return '<a target="_blank" href="support/show/type/user/id/' . $item['user_recipient_id'] . '/">' . $item['user_recipient_name'] . '</a>';
                    }
                } elseif($item['investor_recipient_id'] > 0) {
                    return '<a target="_blank" href="support/show/type/investor/id/' . $item['investor_recipient_id'] . '/">' . $item['investor_recipient_name'] . '</a>';
                }
            })

            ->addFormat('component', function($value, $item){
                return '<a target="_blank" href="components/edit/id/' . $item['component'] . '">' . $item['component_name'] . '</a>';
            })

            ->addFormat(['value_excl', 'value_tax', 'value_incl'], 'money')

            ->addTotals(['value_excl', 'value_tax', 'value_incl'])

            ->setFilters([
                'start' => [],
                'invoice' => ['type' => 'input'],
                'recipient' => ['type' => 'input'],
                'component' => []
            ])

            ->setOptions([
                'noEditButton' => true,
                'item_title' => 'Factuur',
                'paginator' => false
            ])

            ->render($this);
    }

    private function getReportFilters(){
        $month_filter_options = [];
        $corporation_filter_options = [];
        $project_filter_options = [];
        $objectgroupFilterOptions = [];

        foreach($this->data_model->getPeriodLabels() as $month_id => $month_label) {
            $month_filter_options[$month_id] = ['value' => $month_id, 'title' => $month_label];
        }

        foreach($this->data_model->getCorporationLabels() as $corporation_id => $corporation_label) {
            $corporation_filter_options[$corporation_id] = ['value' => $corporation_id, 'title' => $corporation_label];
        }
        $corporationValue = $this->getParam('corporations') ?: [$this->data_model->getDefaultCorporation()];

        foreach($this->data_model->getProjectLabels($corporationValue) as $project_id => $project_label) {
            $project_filter_options[$project_id] = ['value' => $project_id, 'title' => $project_label];
        }

        $projectValue = $this->getParam('projects') ?: [$this->data_model->getDefaultProject($project_filter_options)];
        foreach ($this->data_model->getObjectgroupLabels($projectValue) as $objectgroupId => $objectgroupValue) {
            $objectgroupFilterOptions[$objectgroupId] = [
                'value' => $objectgroupId,
                'title' => $objectgroupValue
            ];
        }


        foreach($this->data_model->getYearLabels() as $year_id => $year) {
            $year_filter_options[$year_id] = ['value' => $year_id, 'title' => $year];
        }

        return [
            'corporations' => [
                'type' => 'select_multiple',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => $corporationValue,
                'options' => $corporation_filter_options,
                'title' => 'Juridische entiteit',
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'projects' => [
                'type' => 'select_multiple',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => $projectValue,
                'options' => $project_filter_options,
                'title' => 'Project',
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'objectgroups' => [
                'type' => 'select_multiple',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => [$this->data_model->getDefaultObjectgroup($objectgroupFilterOptions)],
                'options' => $objectgroupFilterOptions,
                'title' => 'Objectgroup',
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'year' => [
                'type' => 'select',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => date('Y'),
                'title' => 'Jaar',
                'options' => $year_filter_options,
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'start_period' => [
                'type' => 'select',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => 1,
                'options' => $month_filter_options,
                'title' => 'Periode van',
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'end_period' => [
                'type' => 'select',
                'renderSeparately' => true,
                'comparison_operator' => 'ignore',
                'default' => 12,
                'options' => $month_filter_options,
                'title' => 'Periode tot',
                'show_all_disabled' => true,
                'preApplied' => true
            ],
            'component' => [
            ],
            'ledger' => [
                'preApplied' => true
            ]
        ];
    }

    private function handleCorporationsFilterValue($isInitializing){
        $corporations = $isInitializing && $this->getParam('view_template') != 'excel' ? [$this->data_model->getDefaultCorporation()] : null;

        if($corporations_param = $this->getParam('corporations')) {
            if(is_array($corporations_param)){
                $corporations = $corporations_param;
            } elseif (strpos($corporations_param, ',') !== false) {
                $corporations = explode(',', $corporations_param);
            } else {
                $corporations = [$corporations_param];
            }
        }

        $this->data_model->setCorporations($corporations);
        $this->setParam('corporations', $corporations);
    }

    private function handleProjectsFilterValue($isInitializing){
        $default_project = NULL;
        if($projects = Project::getMenuList())
            $default_project = array_keys($projects)[0];

        $projects = $isInitializing && $this->getParam('view_template') != 'excel'  ? [$default_project] : null;
        if($projects_param = $this->getParam('projects')) {
            if(is_array($projects_param)){
                $projects = $projects_param;
            } elseif (strpos($projects_param, ',') !== false) {
                $projects = explode(',', $projects_param);
            } else {
                $projects = [$projects_param];
            }
        }

        $this->data_model->setProjects($projects);
        $this->setParam('projects', $projects);

    }

    private function handleObjectgroupFilterValue($isInitializing)
    {
        $defaultObjectgroup = null;
        $projectFilterValue = null;

        $projectList = Project::getMenuList();
        $projectFilterValues = $this->data_model->getProjects();

        if ($projectFilterValues) {
            $projectFilterValue = $projectFilterValues[0];
        } elseif ($projectList) {
            $projectFilterValue = array_keys($projectList)[0];
        }

        if ($projectFilterValue) {
            if (count($projectList[$projectFilterValue]['objectgroups']) > 0) {
                $defaultObjectgroup = array_keys($projectList[$projectFilterValue]['objectgroups'])[0];
            }
        }

        $objectgroups = $isInitializing && $this->getParam('view_template') != 'excel' ? [$defaultObjectgroup] : null;
        if ($objectgroupParam = $this->getParam('objectgroups')) {
            if (is_array($objectgroupParam)) {
                $objectgroups = $objectgroupParam;
            } elseif (strpos($objectgroupParam, ',') !== false) {
                $objectgroups = explode(',', $objectgroupParam);
            } else {
                $objectgroups = [$objectgroupParam];
            }
        }

        $this->data_model->setObjectGroups($objectgroups);
        $this->setParam('objectgroups', $objectgroups);
    }

}
