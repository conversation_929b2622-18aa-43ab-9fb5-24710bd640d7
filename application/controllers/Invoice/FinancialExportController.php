<?php

use Accounting\Infrastructure\Domain\Factory\Transport\ErrorMessageParserFactory;

class Invoice_FinancialExportController extends GlobalController
{
	/** @var $model Controllers\Invoice\FinancialExport */
	private $model;

	public function preDispatch()
	{
		$this->view->Breadcrumbs()
			->addCrumb('Facturen')
			->addCrumb('Uitwisseling financieel systeem');

		$this->model = new Controllers\Invoice\FinancialExport();

		if(is_null($this->getParam('year')))
			$this->setParam('year', date('Y'));

		if(is_null($this->getParam('corporation_with_missing_administration')))
			$this->setParam('corporation_with_missing_administration', false);


		if(!is_null($this->getParam('run')))
			$this->model->setRun($this->getParam('run'));

		if(!is_null($this->getParam('xml')))
			$this->model->setXml($this->getParam('xml'));
	}



	public function listAction()
	{
        ini_set('max_input_time', 1000);
        ini_set('memory_limit', '10G');
        ini_set('max_execution_time', 0);

        if(is_null($this->getParam('invoice_year')))
            $this->setParam('invoice_year', date('Y'));

		$this->view->Breadcrumbs()->addCrumb('Lijst');

        $this->view->isInvoiceExchange = $this->getParam('invoice-exchange');

		if(is_null($this->getParam('status')) && !$this->isAjaxRequest){
		    $this->setParam('status', 'new||partial');
        }

		if (Settings::get('financial_export_system') === 'hb') {
            $select = $this->model->getQuery()
                ->group('ir.id');
        }
		else {
            $select = $this->model->getQuery()
                ->group('grouping_key');
        }

		$data = db()->fetchAll($select);

		$data = $this->model->addInvoiceCustomTypes($data);
		$data = $this->model->setListStatusFromData($data);
 		$years = $this->model->getYearsFromData($data);
		$corporations = $this->model->getCorporationList();

		$buildExportConfirmElement = function () {
			global $environment;
			$output = '';

			if (!$environment || $environment->getEnvironment() !== $environment::PRODUCTION) {
				$systemLabel = FinancialExportSystems::getLabelForActiveSystem();

				$output = "WAARSCHUWING: u werkt in een test omgeving die mogelijk een kopie is van uw \\n
					productie omgeving. Tenzij dit expliciet en zorgvuldig zo ingesteld is, heeft deze actie\\n 
					waarschijnlijk effect op uw PRODUCTIE {$systemLabel} gegevens. Dit is meestal onwenselijk en \\n
					vrijwel altijd kostbaar om terug te draaien. \\n\\n
					Klik alleen op OK/Akkoord als u zeker bent over deze handeling en als u de gevolgen er van  
					accepteert.\\n\\n
					Uw keuze wordt vastgelegd. \\n\\n
					Neem bij enige twijfel contact op met onze klantenservice.";

				$output = str_replace(["\t", "\n"], '', $output);
				$output = "onClick='return confirm(\"{$output}\");'";
			}

			return $output;
		};

		$buildExportConfirmParam = function () {
			global $environment;
			return !$environment || $environment->getEnvironment() !== $environment::PRODUCTION?
				'userConfirmedExportFromNonProductionEnvironment/yes/': '';
		};

		$this->view->list = $this->view->listView($data)

			->setTypes([
				'invoice_year' => ['title' => 'Jaar', 'width' => 'hidden'],
                'run' => ['title' => 'ID', 'width' => 'xxxsmall'],
				'type' => ['title' => 'Type', 'width' => 'xsmall'],
				'date' => ['title' => 'Datum', 'width' => 'small', 'type' => 'date'],
				'corporation_with_missing_administration' => ['title' => '', 'width' => 'hidden'],
				'corporation' => ['title' => 'Juridische entiteit', 'width' => 'xxlarge truncate'],
				'administration' => ['title' => 'Amd.code', 'width' => 'xxsmall'],
				'status' => ['title' => 'Status', 'width' => 'small'],
				'invoice_count' => ['title' => 'Aantal', 'width' => 'xxsmall'],
				'exported_count' => ['title' => 'Uitgewisseld', 'width' => 'small'],
				'actions' => ['title' => '', 'width' => 'small'],
			])

			->addFormat('corporation', function($value, $item){
				return $value > 0 ? $item['corporation_name'] : '-';
			})

			->addFormat('actions', function ($value, $item) use ($buildExportConfirmElement, $buildExportConfirmParam) {
                if (Settings::get('financial_export_system') === 'hb') {
                    $item_id = "run/{$item['run']}";
                } else {
                    $item_id = $item['xml'] > 0 ? "xml/{$item['xml']}" : "run/{$item['run']}";
                }

				$buttons = "<a href='invoice_financial-export/invoice-list/{$item_id}' title='Facturen lijst weergeven' 
					class='fa fa-file-alt forceAutoHint' noHintButton='1' aria-hidden='true'></a>";

                if ($item['status'] !== 'done') {
				    if (Settings::get('financial_export_system') === 'hb') {
                        $export_url = "invoice/export-xml/id/{$item['run']}/";
                    } else {
                        if ($item['xml'] > 0) {
                            $export_url = "invoice/download-xml/id/{$item['xml']}/";
                        } else {
                            $export_url = "invoice/export-xml/id/{$item['run']}/";


                            $export_url = "invoice_financial-export/export-invoice-run/id/{$item['run']}/";
                        }
                    }

                    $export_url .= $buildExportConfirmParam();

                    $buttons = '';
                    if (FinancialExportSystems::activeSystemUsesOnlineExport()) {
                        $buttons .= "<a href='{$export_url}'  title='Facturen uitwisselen' 
						class='fa fa-upload forceAutoHint' noHintButton='1' aria-hidden='true' target='_blank'
						{$buildExportConfirmElement()}></a>";
                    }
                }

				$buttons .= "<a href='invoice_financial-export/invoice-list/{$item_id}' title='Facturen lijst weergeven' 
					class='fa fa-file-alt forceAutoHint' noHintButton='1' aria-hidden='true'></a>";



                $no_sync_url = $item['xml'] > 0 ?
                    "invoice_financial-export/invoice-set-financial-no-sync/xml/{$item['xml']}/":
                    "invoice_financial-export/invoice-set-financial-no-sync/run/{$item['run']}/";


                $buttons .= "<a href='{$no_sync_url}' title='Facturen negeren voor uitwisseling' 
						class='fa fa-times-circle forceAutoHint financial_no_sync_button' noHintButton='1' aria-hidden='true'></a>";

				return $buttons;
			})

			->addFormat('corporation_with_missing_administration', 'bool')

			->addFormat('type', function($value){
				$statusses = [
					'advance' => 'Prolongatie',
					'custom' => 'Handmatig',
					'purchase' => 'Inkoop',
					'investor_provision' => 'Beleggerafrk.'
				];

				return isset($statusses[$value]) ? $statusses[$value] : '-';
			})

			->addFormat('status', function($value){
				$statuses = [
					'done' => 'Uitgewisseld',
					'new' => 'Niet uitgewisseld',
					'partial' => 'Gedeeltelijk'
				];

				return isset($statuses[$value]) ? $statuses[$value] : '-';
			})

			->addFormat('date', 'date')

			->setOptions([
				'item_title' => 'Facturen batch',
			])

			->setFilters([
				'type' => ['title' => 'Type'],
				'invoice_year' => ['title' => 'Jaar', 'renderSeparately' => true, 'order_by_value' => 'ASC', 'custom_options' => $years, 'hideCount' => true, 'custom_options_only' => true, 'show_all_disabled' => true],
				'corporation' => ['title' => 'Juridische entiteit', 'renderSeparately' => true, 'order_by_value' => 'ASC', 'custom_options' => $corporations, 'custom_options_only' => true, 'hideCount' => true],
				'corporation_with_missing_administration' => ['title' => 'Zonder adminstratie', 'renderSeparately' => true, 'show_all_disabled' => true, 'custom_options' => [0 => ['title' => 'Nee'], 1 => ['title' => 'Ja']]],
                'identifier' => ['title' => 'Factuurnummer', 'renderSeparately' => true, 'type' => 'input'],
				'date' => [],
				'administration' => [],
				'status' => [
                    'hideCount' => true,
                    'order_by_value' => 'ASC',
				    'custom_options' => [
                        'new||partial' => ['title' => 'Niet- of gedeeltelijk uitgewisseld']
                    ]
                ]
			]);

        if (Settings::get('financial_export_system') == 'exactglobeaxians') {
            $this->view->list->addLegend('list-legend-axians');
        } else {
            $this->view->list->addLegend('list-legend');
        }



        $this->view->list->render($this);
	}

    public function invoiceSetFinancialNoSyncAction()
    {
        $this->disableView();

        if (!$this->getParam('run') && !$this->getParam('xml')) {
            $this->redirect('invoice_financial-export/list');
        }

        $i_model = new Invoices();

        $select = db()->select()
            ->from(['i' => 'invoices'], ['id'])
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', false)
            ->where('i.financial_no_sync = ?', false)
            ->where('i.financial_invoice_id IS NULL');

        if ($run_id = $this->getParam('run')) {
            $select->where('i.run = ?', $run_id);
        }

        if ($xml_id = $this->getParam('xml')) {
            $select->where('ic.xml = ?', $xml_id);
        }

        $invoice_model_select = $i_model->select()
            ->where('id IN (' . implode_for_where_in(db()->fetchCol($select)) . ')');

        foreach ($i_model->fetchAll($invoice_model_select) as $invoice) {
            $invoice
                ->setFromArray(['financial_no_sync' => true])
                ->save();
        }

        $this->redirect('invoice_financial-export/list');
	}


	public function exportDashboardAction() {

        $select = $this->model->getQuery('nosync')
            ->group('i.id');

        $data = db()->fetchAll($select);


        $this->view->data = $data;
        $this->view->number_of_invoices = count($data);

        $this->render('dashboard/index');
    }


	public function invoiceListAction()
	{
		$this->view->Breadcrumbs()
			->addCrumb('Lijst', 'invoice_financial-export/list')
			->addCrumb('Details batch');

		$errorMessageParser = (new ErrorMessageParserFactory())
			->build(Settings::get('financial_export_system'));

		$select = $this->model->getQuery()
			->group('i.id');

		$data = db()->fetchAll($select);

		$data = $this->model->addInvoiceCustomTypes($data);
		$data = $this->model->setInvoiceStatusFromData($data);
		$data = $this->model->addInvoiceRowsInvoiceIdToData($data);
		$data = $this->model->addInvoiceRowsToData($data);
		$data = $this->model->addCustomInvoiceRowsToData($data);
		$model = $this->model;

		$this->view->list = $this->view->listView($data)

			->setTypes([
				'identifier' => ['title' => 'Factuur', 'width' => 'xlarge'],
				'status' => ['title' => 'Status', 'width' => 'small'],
				'invoice_rows' =>  ['title' => 'Regels', 'width' => 'medium'],
				'error_message' => ['title' => 'Melding', 'width' => 'small'],
                'export_source' => ['title' => '', 'width' => 'small'],
                'action' => ['title' => '', 'width' => 'small']
			])

			->addFormat('identifier', function($value, $item){
				if($item['customid'] > 0){
					$url = 'invoice/edit-custom/id/' . $item['customid'];
				} else {
					$url = 'invoice/export/id/' . $item['id'];
				}

				if($item['is_purchase'])
					$value = $item['purchase_identifier'];

				return '<a href="' . $url . '" target="_blank"><i class="fa fa-file-pdf-o" aria-hidden="true"></i>' . $value . '</a>';
			})

			->addFormat('invoice_rows', function($value, $item) use ($model){
				return $model->renderInvoiceRowsColumn($item);
			})

			->addFormat('error_message', function($value, $item) use ( $errorMessageParser ){
				$error_messages = $errorMessageParser->parseErrorMessage($value);

				if(count($error_messages) == '0' || (is_string($error_messages) && trim($error_messages) == false)){
					if(count($item['invoice_rows']) == '0'){
						$error_messages[] = 'Bij deze factuur kunnen geen factuurregels opgehaald worden. Als de factuur daadwerkelijk geen rijen heeft dan kan deze niet uitgewisseld worden.';
					} else {
						return '';
					}
				}

				return '
					<div class="error_hint" title="<b>Foutmelding:</b><br />' . implode('<br />', $error_messages) . '">&nbsp;</div>
					<a title="Volledige foutmelding weergeven" href="invoice_financial-export/download-error-message/invoice/' . $item['id'] . '/" class="fa fa-download forceAutoHint" noHintButton="1" target="_blank" aria-hidden="true"></a>
					';
			})

            ->addFormat('export_source', function($value, $item){
                if($value && loginManager::data()->info['type'] == 'admin') {
                    return '<a title="Gebruikte uitwisseling broncode inzien" href="invoice_financial-export/download-source-export-code/invoice/' . $item['id'] . '/" class="fa fa-code forceAutoHint" noHintButton="1" target="_blank" aria-hidden="true"></a>';
                } else {
                    return '';
                }
            })

			->addFormat('status', function($value){
				$statuses = [
					'done' => 'Uitgewisseld',
					'not_done' => 'Niet uitgewisseld'
				];

				return isset($statuses[$value]) ? $statuses[$value] : '-';
			})

            ->addFormat('action', function ($value, $dataRow) {
                if ($dataRow['status'] !== 'not_done') {
                    return '';
                }

                if($dataRow['customid'] > 0){
                    return '<a href="/accounting_edit-custom-invoice/view/custom_invoice_id/'.$dataRow['customid'].'" class="fa fa-edit" target="_blank"></a>';
                } else {
                    return '<a href="/accounting_edit-invoice/view/invoice_id/'.$dataRow['id'].'" class="fa fa-edit" target="_blank"></a>';
                }
            })

			->setOptions([
				'item_title' => 'Facturen batch',
			])

			->setFilters([
				'identifier' => ['type' => 'input'],
				'status' => []
			])

			->render($this);
	}

	public function downloadErrorMessageAction(){
		$this->disableView();

		$this->model->echoErrorMessageForInvoice($this->getParam('invoice'));


	}

    public function downloadSourceExportCodeAction(){
        $this->disableView();

        $this->model->downloadSourceExportCodeForInvoice(
            $this->getParam('invoice')
        );
    }

    public function testConnectionAction()
    {
        $helper = new \Controllers\Invoice\FinancialExport\TestConnectionControllerHelper();

        try {
            $this->view->errorList = $helper->getConnectionErrorList();
        } catch (\Invoice\FinancialExport\TestConnection\Exceptions\UnsupportedSystemException $exception){
            die($exception->getMessage());
        }

        $this->view->label = $helper->getLabel();
    }

    public function exportInvoiceRunAction()
    {
        $this->disableView();

        $invoiceRunId = $this->getParam('id');
        $userConfirmedExportFromNonProductionEnvironment = $this->getParam('userConfirmedExportFromNonProductionEnvironment');

        $url = "export-xml/id/$invoiceRunId";

        if ($userConfirmedExportFromNonProductionEnvironment) {
            $url .= '/userConfirmedExportFromNonProductionEnvironment/yes';
        }

        startWorker($url, 'invoice', 'direct', true);

        $this->redirect('invoice_financial-export/list/invoice-exchange/1');
    }
}
