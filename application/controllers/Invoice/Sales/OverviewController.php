<?php

class Invoice_Sales_OverviewController extends GlobalController {

    public function finalizedListAction()
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '20048M');

        $this->view->Breadcrumbs()
            ->addCrumb('Verkoopfacturen')
            ->addCrumb('Verkoopfacturen overzicht');

        $this->view->extraWidth = true;

        if (is_null($this->getParam('identifier_year')) && !$this->isAjaxRequest) {
            $this->setParam('identifier_year', date('Y'));
        }

        $makeFinalizedListService = new \Invoice\Sales\Overview\MakeFinalizedListService();
        $list = $makeFinalizedListService->execute(
            $this->getParam('identifier_year')
        );

        $years = $makeFinalizedListService->getYearFilterValues();

        $yearFilterOptions = [];
        foreach($years as $year){
            $yearFilterOptions[$year] = ['title' => $year];
        }

        //p($list,'die');

        if (Settings::get('RIVA_sales_finalized_custom_overview')) {
            $types = [
                'administration' => ['title' => 'Bedrijf', 'width' => 'medium'],
                'identifier_year' => ['title' => 'Boekjaar', 'width' => 'medium'],
                'verkoopboek' => ['title' => 'Dagboek', 'width' => 'medium'],
                'period_deferred' => ['title' => 'Periode', 'width' => 'medium'],
                'balance' => ['title' => 'Teken tegenrek.', 'width' => 'medium'],
                'balance_ledger' => ['title' => 'Tegenrekening', 'width' => 'medium'],
                'identifier_fake' => ['title' => 'Stuknummer', 'width' => 'small '],
                'expire_date' => ['title' => 'Boekdatum', 'width' => 'medium right'],
                'ledger' => ['title' => 'Rekening', 'width' => 'medium right'],
                'cost_center' => ['title' => 'Kostenplaats', 'width' => 'medium right'],
                'cost_carrier' => ['title' => 'Kostendrager', 'width' => 'medium right'],
                'identifier' => ['title' => 'Factuurnr', 'width' => 'small right'],
                'Factbet' => ['title' => 'Factbet', 'width' => 'small right'],
                'Afw.per' => ['title' => 'Afw.per', 'width' => 'small right'],
                'per tm' => ['title' => 'per tm', 'width' => 'small right'],
                'Afw. Bedrijf' => ['title' => 'Afw. Bedrijf', 'width' => 'small right'],
                'value' => ['title' => 'Bedrag', 'width' => 'small right'],
                'description' => ['title' => 'Omschrijving', 'width' => 'xxlarge truncate'],
                'identifier_full' => ['title' => 'Factuurnr', 'width' => 'small right'],
            ];

            $this->view->ListView($list)
                ->setTypes($types)
                ->addFormat('start', 'date')
                ->addFormat('identifier_year', function ($value) {
                    return substr($value,-2);
                })
                ->addFormat('identifier_fake', function ($value, $row) {
                    return substr($row['identifier_year'],-2).$row['identifier'];
                })
                ->addFormat('verkoopboek', function ($value, $row) {
                    return '29';
                })
                ->addFormat('expire_date', 'date')
                ->addFormat('balance', function ($value, $row) {
                    return 'R';
                })
                ->addFormat('balance_ledger', function ($value, $row) {
                    return '29999';
                })
                ->addFormat('Factbet', function ($value, $row) {
                    return '1';
                })

                ->addFormat('identifier', function ($value, $row) {
                    return substr($row['identifier_year'],-2).$value;
                })

                ->addFormat('value', 'money')

                ->setFilters([
                    'identifier_year' => [
                        'renderSeparately' => true,
                        'title' => 'Jaar',
                        'hideCount' => true,
                        'preApplied' => true,
                        'custom_options' => $yearFilterOptions,
                        'custom_options_only' => true,
                    ],
                    'identifier_full' => [
                        'renderSeparately' => true,
                        'title' => 'factuurnummer',
                        'type' => 'input'
                    ],
                    'corporation_name' => [
                        'renderSeparately' => true,
                        'title' => 'Juridische entiteit',
                        'order_by_value' => 'ASC',
                    ],
                    'project_name' => [
                        'renderSeparately' => true,
                        'title' => 'Project',
                        'order_by_value' => 'ASC',
                    ],
                    'date' => ['type' => 'date_range',
                        'renderSeparately' => true,
                        'title' => 'Factuurdatum',
                    ]

                ])
                ->setOptions(array('custom_class'=> 'commercialtenantlist'))
                ->render($this);

        } else {
            $types = [
                'invoice' => ['title' => 'Unieke OB ID', 'width' => 'xxsmall truncate'],
                'corporation_name' => ['title' => 'Jurische entiteit', 'width' => 'xxlarge truncate'],
                'project_name' => ['title' => 'Project', 'width' => 'small truncate'],
                'user_recipient_name' => ['title' => 'Ontvanger', 'width' => 'xxlarge truncate'],
                'identifier_year' => ['title' => 'Jaar', 'width' => 'medium'],
                'verkoopboek' => ['title' => 'Dagboek', 'width' => 'hidden'],
                'period' => ['title' => 'Periode', 'width' => 'medium'],
                'date' => ['title' => 'Factuurdatum', 'width' => 'small'],
                'ledger' => ['title' => 'Rekening', 'width' => 'medium'],
                'cost_center' => ['title' => 'Kostenplaats', 'width' => 'medium'],
                'cost_carrier' => ['title' => 'Kostendrager', 'width' => 'medium'],
                'identifier_full' => ['title' => 'Factuurnummer volledig', 'width' => 'medium right'],
                'debtor_financial_code' => ['title' => 'Deb. code financieel', 'width' => 'medium right'],
                'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
                'invoicetitle' => ['title' => 'Factuurtitel', 'width' => 'xxlarge truncate'],
                'description' => ['title' => 'Omschrijving', 'width' => 'xxlarge truncate'],
                'value' => ['title' => 'Bedrag', 'width' => 'small'],
                'vat_percentage' => ['title' => '% BTW', 'width' => 'small'],
                'vat_code' => ['title' => 'BTW code', 'width' => 'small'],
            ];
            $this->view->ListView($list)
                ->setTypes($types)
                ->addFormat('date', 'date')
                ->addFormat('company', function ($value, $row) {
                    if(!$value){
                        return '-';
                    }

                    return '<a href="/company/edit/id/' . $value . '/" target="_blank">' . $row['companyName'] . '</a>';
                })
                ->addFormat('project', function ($value, $row) {
                    if(!$value){
                        return '-';
                    }

                    return '<a href="/project/details/id/' . $value . '/" target="_blank">' . $row['projectName'] . '</a>';
                })

                ->addFormat('invoicetitle', function ($value, $row) {
                    if($value){
                        return $value;
                    } else {
                        return $row['invoicetitlecustom'];
                    }

                })

                ->addFormat(['value'], 'money')

                ->setFilters([
                    'identifier_year' => [
                        'renderSeparately' => true,
                        'title' => 'Jaar',
                        'hideCount' => true,
                        'preApplied' => true,
                        'custom_options' => $yearFilterOptions,
                        'custom_options_only' => true,
                    ],
                    'corporation_name' => [
                        'renderSeparately' => true,
                        'title' => 'Juridische entiteit',
                        'order_by_value' => 'ASC',
                    ],

                    'identifier_full' => [
                        'renderSeparately' => true,
                        'title' => 'Factuurnummer',
                        'type' => 'input',
                        'order_by_value' => 'ASC',
                    ],



                    'project_name' => [
                        'renderSeparately' => true,
                        'title' => 'Project',
                        'order_by_value' => 'ASC',
                    ],
                    'date' => ['type' => 'date_range',
                        'renderSeparately' => true,
                        'title' => 'Factuurdatum',
                    ]

                ])

                ->render($this);
        }

    }

}
