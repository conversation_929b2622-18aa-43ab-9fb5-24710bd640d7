<?php

class Invoice_Purchase_OverviewController extends GlobalController {

    public function validatedListAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Inkoopfacturen')
            ->addCrumb('Gevalideerde inkoopfacturen overzicht');

        $this->view->extraWidth = true;

        if (is_null($this->getParam('year')) && !$this->isAjaxRequest) {
            $this->setParam('year', date('Y'));
        }

        $makeValidatedListService = new \Invoice\Purchase\Overview\MakeValidatedListService();
        $list = $makeValidatedListService->execute();

        $types = [
            'year' => ['title' => 'Jaar', 'width' => 'hidden'],
            'date' => ['title' => 'Factuurdatum', 'width' => 'small'],
            'customId' => ['title' => 'Factuurnummer', 'width' => 'medium right'],
            'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
            'pdf' => ['title' => 'PDF', 'width' => 'xxxsmall'],
            'company' => ['title' => 'Leverancier', 'width' => 'small truncate'],
            'project' => ['title' => 'Project', 'width' => 'small truncate'],
            'objects' => ['title' => 'Objecten', 'width' => 'large truncate'],
            'description' => ['title' => 'Omschrijving', 'width' => 'small truncate'],
            'amount' => ['title' => 'Bedrag', 'width' => 'small'],
            'validation_user' => ['title' => 'Validator', 'width' => 'small truncate'],
        ];

        $allInvestorNames = [];
        if(Settings::get('for_third_party')){
            $types['investors'] = ['title' => 'Belegger', 'width' => 'xxlarge'];
            $types['provision_invoice_status'] = ['title' => 'Afrekenstatus', 'width' => 'medium'];
            $types['provision_invoices'] = ['title' => 'Afrekening(en)', 'width' => 'xxlarge'];
            $allInvestorNames = $makeValidatedListService->getAllInvestorNames($list);
            $allInvestorProvisionTitles = $makeValidatedListService->getAllInvestorProvisionTitles($list);
        }

        if (FinancialExportSystems::activeSystemUsesInteractionOverview()){
            $types['financial_invoice'] = ['title' => 'Uitgewisseld financieel pakket', 'width' => 'large'];
        }

        $allObjectAddresses = $makeValidatedListService->getAllObjectAddresses($list);

        $this->view->ListView($list, [])
            ->setTypes($types)
            ->addFormat('date', 'date')
            ->addFormat('customId', function ($value, $row) {
                return
                    '<a href="/invoice/edit-purchase/id/' . $value . '/" target="_blank">' . $row['identifier'] . '</a>';
            })
            ->addFormat('pdf', function($value, $row){
                return
                    '<a href="/invoice/edit-purchase/id/' . $row['customId'] . '/" target="_blank">' .
                        '<i class="fa fa-file-pdf" style="font-size: 18px;" aria-hidden="true"></i>' .
                    '</a>';
            })
            ->addFormat('company', function ($value, $row) {
                if(!$value){
                    return '-';
                }

                return '<a href="/company/edit/id/' . $value . '/" target="_blank">' . $row['companyName'] . '</a>';
            })
            ->addFormat('project', function ($value, $row) {
                if(!$value){
                    return '-';
                }

                return '<a href="/project/details/id/' . $value . '/" target="_blank">' . $row['projectName'] . '</a>';
            })
            ->addFormat('objects', function ($value, $row) use ($allObjectAddresses) {
                if(!$value){
                    return '-';
                }

                $address = isset($allObjectAddresses[$value]) ? $allObjectAddresses[$value] : '-';

                return '<a href="/object/edit/id/' . $value . '/" target="_blank">' . $address . '</a>';
            })
            ->addFormat('investors', function ($value, $row) use ($allInvestorNames) {
                if(!$value){
                    return '-';
                }

                $name = isset($allInvestorNames[$value]) ? $allInvestorNames[$value] : '-';

                return '<a href="/support/show/type/investor/id/' . $value . '/" target="_blank">' . $name . '</a>';
            })
            ->addFormat('validation_user', function($value, $row){
                if(!$value){
                    return '-';
                }

                return '<a href="/user/admin-edit/id/' . $value . '/" target="_blank">' . $row['validationUserName'] . '</a>';
            })
            ->addFormat('provision_invoice_status', function($value){
                if($value === 'done'){
                    return 'Volledig afgerekend';
                } elseif($value === 'partial'){
                    return 'Gedeeltelijk afgerekend';
                }
                return 'Niet afgerekend';
            })
            ->addFormat('amount', 'money')
            ->addFormat('provision_invoices', function($value, $row) use ($allInvestorProvisionTitles){
                if(!isset($allInvestorProvisionTitles[$value])){
                    return '-';
                }

                if(count($row['provision_invoices']) > 1){
                    $invoiceTitle = $allInvestorProvisionTitles[$value]['abbreviation'] . ' - ' . $allInvestorProvisionTitles[$value]['invoiceIdentifier'];
                } else {
                    $invoiceTitle = $allInvestorProvisionTitles[$value]['invoiceIdentifier'];
                }

                return
                    '<a href="/invoice/export/id/' . $value . '/" target="_blank">' .
                    '<i class="fa fa-file-pdf" style="font-size: 18px;" aria-hidden="true"></i>' .
                    $invoiceTitle .
                    '</a>';
            })
            ->addFormat('financial_invoice', function($value, $row){
                if(!$value){
                    if($row['financial_no_sync']){
                        return 'Uitgeschakeld';
                    } else {
                        return 'Nog niet uitgewisseld';
                    }
                } else {
                    return 'Administratie: ' . $row['administration'] . ' - id: ' . $value;
                }
            })
            ->setFilters([
                'year' => [
                    'renderSeparately' => true,
                    'title' => 'Jaar',
                ],
                'date' => ['type' => 'date_range'],
                'customId' => ['type' => 'input'],
                'company' => ['type' => 'input'],
                'project' => ['type' => 'input'],
                'objects' => ['type' => 'input'],
                'description' => ['type' => 'input'],
                'amount' => ['type' => 'range'],
                'validation_user' => ['type' => 'input'],
                'investors' => ['type' => 'input'],
                'provision_invoice_status' => ['type' => 'select'],
                'provision_invoices' => ['type' => 'input'],
                'financial_invoice' => ['type' => 'input'],
            ])
            ->setOptions([
                'item_title' => 'Inkoopfactuur',
            ])
            ->render($this);
    }
    
}
