<?

	class RemindsController extends GlobalController {

		public function preDispatch(){
			if($this->view->Breadcrumbs()->count() == 0)
				$this->view->Breadcrumbs()
					->addCrumb('Financieel')
					->addCrumb('Betalingsherinneringen- en aanmaningenprocedure', 'reminds/');
		}

		public function projectListAction(){
            $this->view->extraWidth = true;

			if($this->_getParam('status') == '99')
				$this->_redirect('reminds/ca-list');	

			// update invoice reminds
			$ir = new InvoiceReminds();
			$ir->updatePenaltyAmounts();
			$ir->add();
			$ir->update();

			$this->view->Breadcrumbs()
				->addCrumb('Betalingsherinneringen en aanmaningen', 'reminds/project-list/');

			$model = new InvoicesReminds();

			$listParams = array('send' => false, 'payed' => false, 'no_ca' => true, 'final' => false);
			$filters = array('project' => false, 'status' => false, 'corporation' => false);

			foreach($filters as $filter_name => &$filter_value)
				if($filter_value = $this->_getParam($filter_name))
					$listParams[$filter_name] = $filter_value;

			$projects = (array) $model->getProjectList($listParams);
            $corporations = (array) $model->getCorporationList($listParams);



            $this->view->corporations = $corporations;
			$this->view->all_projects = $projects;	

			if(isset($filters['project']))
				$projects = array($projects[$filters['project']]);

			$this->view->filters = $filters;
			$this->view->projects = $projects;
			$this->view->lastUpdates = Transaction::lastUpdate();
			$this->view->listParams = $listParams;
		}

		public function updatePenaltyAmountsAction(){
			$this->disableView();

			$ir = new InvoiceReminds();

			$ir->updatePenaltyAmounts();
		}

		public function remindsDashboardAction(){
		    $this->render('dashboard/index');
        }

		public function projectListviewAction(){

			$params = array();

			foreach(array('project', 'send', 'payed', 'no_ca', 'final', 'kaas') as $param_name)
				if(!is_null($this->_getParam($param_name)))
					$params[$param_name] = $this->_getParam($param_name);

			$controller = $this;

			$this->view->ModelListView('InvoicesReminds', array_merge(array('url-override' => 'reminds/project-listview/'), $params))
                ->setTypes([
                    'penalty',
                    'finalize',
                    'user',
                    'invoice',
                    'expire_date',
                    'unpayed',
                    'unpayedTotal',
                    'interest',
                    'interest_explained',
                    'penalty_amount',
                    'penalty_data',
                    'blocked',
                    'retry',
                    'enabled',
                    'notes'
                ])

				->addFormat('user', function($value, $item){
					if($item['investor'] > 0){
						return '<a href="support/show/id/' . $item['investor'] . '/type/investor" target="_blank">' . Investors::getName($item['investor']) . '</a>';
					} elseif($item['relation'] > 0){
						return Relations::getName($item['relation']);
					} else {
						return '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . '</a>';
					}
				})

				->addFormat('invoice', function($value, $item) use ($controller) {
					if(!$value) return;

					if(isset($item['transactions']))
						if(count($item['transactions']) > 1)
							return '<span hintOffset="{\'x\': 56, \'y\': 0}" title="' . $controller->renderMultipleInvoicesTitle($item['transactions']) . '">Meerdere</span>';

					return '<a href="invoice/export/id/' . $value . '/" target="_tab">' . Invoice::getIdentifier($value) . '</a>';
				})

				->addFormat('penalty', function($value){
					$ir = new InvoiceReminds();
					return '<span penalty="' . $ir->fines[$value]['shortcut'] . '">' . strtoupper(str_replace('1', '', $ir->fines[$value]['shortcut'])) . '</span>';	
				})

				->addFormat('notes', function($value){
					if(!$value) return '';

					return '<span hintOffset="{\'x\': -10, \'y\': 0}" title="' . $value . '">&nbsp;</span>';
				})

				->addFormat('expire_date', 'relativeDate')

				->addFormat(array('finalize'), 'checkbox')

				->addFormat('retry', function($value, $item){
					$disabled = $item['payment'] == 'ideal' || $item['penalty'] != '1';
					$value = ($value  === true || $value === 'true' || $value === '1') && !$disabled ? 'checked="checked"' : ''; 
					return '<input class="retry" type="checkbox" ' . $value . ($disabled ? ' disabled="disabled"' : '') . '></input>';
				})

				->addFormat('blocked', function($value){ return $value ? $value : '-'; })

				->addFormat(['interest', 'penalty_amount'], 'money')

				->addFormat('penalty_data', function($penalty_data){
					return '<span style="width:20px; display: inline-block;" title="' . htmlentities($penalty_data) . '">&nbsp;</span>';
				})

				->addFormat('interest_explained', function($interest_explained){
					return '<span style="width:20px; display: inline-block;" title="' . htmlentities($interest_explained) . '">&nbsp;</span>';
				})

				->addFormat(['unpayed', 'unpayedTotal'], function($value){
					if(is_null($value)) return;

					return ($value < 1 ? '<span class="low_amount">' : '') . (is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-') . ($value < 1 ? '</span>' : '');
				})

				->addFormat('enabled', function($value, $item){
					$ids = [];
					
					foreach((array) $item['transactions'] as $transaction)
						$ids[] = $transaction['transaction'];

					return '<a href="transaction/toggle-remind-enabled/id/' . implode(',', $ids) . '/toggle/false/redirect/true/">Uitschakelen</a>';
				})

				//->setFilters(array('user' => array(), 'invoice' => array('type' => 'input')))

				->setOptions(array(
					'show_title' => false,
					'paginator' => false,
					'show_view_template_buttons' 	=> true,
				))

				->render($this);
		}

		protected function renderMultipleInvoicesTitle($transactions){
			$html = "<ul class='multiple_invoices_list'>";

			foreach($transactions as $transaction){
				$html .= '<li><dl>';
					$html .= '<dt><b>Factuur: </b></dt>';
					$html .= '<dd><b>' . Invoice::getIdentifier($transaction['invoice']) . '</b></dd>';
					$html .= '<dt>Vervallen: </dt>';
					$html .= '<dd>' . date('d-m-Y', strtotime($transaction['expire_date'])) . '</dd>';
					$html .= '<dt>Openstaand: </dt>';
					$html .= '<dd>&euro; ' . new StringFormat($transaction['unpayed'], 'money') . '</dd>';
					$html .= '<dt>Boete: </dt>';
					$html .= '<dd>&euro; ' . new StringFormat($transaction['penalty_amount'], 'money') . '</dd>';
					$html .= '<dt>Rente: </dt>';
					$html .= '<dd>&euro; ' . new StringFormat($transaction['interest'], 'money') . '</dd>';
					$html .= '<dt>Storno: </dt>';
					$html .= '<dd>' . ($transaction['blocked'] ? 'Ja' : '-') . '</dd>';
				$html .= '</dl></li>';
			}

			$html .= '</ul>';

			return $html;
		}


		public function caListAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Incassobureau', 'reminds/ca-list/');

			$ir = new InvoiceReminds();
			$p = new Project();
			$t = new Transaction();

			if ($this->getRequest()->isPost()) {
				$post = $this->getRequest()->getPost();
				$irM = new InvoicesReminds();

				$select = $irM->select()
					->where('penalty = ?', 99)
					->where('send = ?', false)
					->where('payed = ?', false);

				foreach($irM->fetchAll($select) as $row){
					$row->deleted = false;
					$row->save();
				}

				foreach (json_decode($post['invoiceids']) as $item) {
					$row = $irM->fetchRow($irM->select()->where('id = ?', $item));

					$row->deleted = true;
					$row->save();
				}
				$this->_redirect('reminds/ca-list');	
			} else {
				$ir->add();
			}

			$this->view->filters =		$ir->filters;
			$this->view->lastUpdates = 	$t->lastUpdate();
			$this->view->reminds = 		$ir->getProjectList(99);
			$this->view->fines = 		$ir->fines;
			
			$this->render('ca-list');
		}

        public function sendCaListAction() {
            $this->view->Breadcrumbs()
                ->addCrumb('Verzonden incassodossiers', '');

            $ir = new InvoiceReminds();
            $data = 		$ir->getSendList(99);

            //p($data,'die');

            $this->view->listView($data)

                ->setTypes(array(
                    'projectname' => array(
                        'title' => 'Project',
                        'width' => 'xxlarge truncate',
                    ),
                    'ca_name' => array(
                        'title' => 'Incassobureau',
                        'width' => 'xxxlarge truncate',
                    ),
                    'tenantname' => array(
                        'title' => 'Huurder',
                        'width' => 'xxxlarge truncate',
                    ),
                    'senddate' => array(
                        'title' => 'Datum',
                        'width' => 'small truncate',
                    ),
                ))

                ->setFilters([
                    'projectname' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Project', 'order_by_value' == 'ASC'],
                    'ca_name' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Incassobureau', 'order_by_value' == 'ASC'],
                ])

                ->render($this);
        }



		public function caSettingsAction(){
			$this->view->EditView($this, 'ca-settings', array('modeloverride' => 'Settings', 'url-override' => 'reminds/ca-settings/'))
				->setOptions(array(
					'show_title' => false
				))
				->render($this);			
		}
		
		public function oldListAction() {
			$ir = new InvoiceReminds();
			$p = new Project();
			$t = new Transaction();

			$next = $this->_getParam('override') ? $this->_getParam('override')  : $ir->getNext();

			if ($this->getRequest()->isPost()) {
				$post = $this->getRequest()->getPost();
				$irM = new InvoicesReminds();

				$select = $irM->select()
					->where('penalty = ?', $next)
					->where('send = ?', false)
					->where('payed = ?', false);

				foreach($irM->fetchAll($select) as $row){
					$row->deleted = false;
					$row->save();
				}

				foreach (json_decode($post['invoiceids']) as $item) {
					$row = $irM->fetchRow($irM->select()->where('id = ?', $item));

					$row->deleted = true;
					$row->save();
				}
				$this->_redirect('reminds/project-list',  array('override' => $this->_getParam('override')));	
			}

			$this->view->filters =		$ir->filters;
			$this->view->lastUpdates = 	$t->lastUpdate();
			$this->view->reminds = 		$ir->getProjectList($next);
			$this->view->fines = 		$ir->fines;
			$this->view->next = 		$next;
			$this->view->postponeOpts =	array(
				0 => "0 dagen",
				1 => "1 dagen",
				2 => "2 dagen",
				3 => "3 dagen",
				4 => "4 dagen",
			);
			
			$this->render('old-list');
		}

		public function changeStatusAction(){
			$this->view->Breadcrumbs()->disable();

			$this->view->transaction = $this->_getParam('transaction');

			$editView = $this->view->EditView($this, 'remindsStatus',
				array(
					'transaction' => $this->_getParam('transaction'),
					'modeloverride' => 'InvoicesReminds'
				))
				
				->setOptions(array(
					'render_to_controller' => false					
				));
				
			$this->view->form = $editView->render($this);
			$this->view->data = $editView->data;
		}
		
		public function caExportAction(){
			$this->disableView();
			
			$ir = new InvoiceReminds();
			
			$ir->finalizeCAs($this->_getParam('user'), $this->_getParam('investor'), $this->_getParam('relation'));
		}


        public function allOverviewAction(){
            $this->disableView();

            $ir = new InvoiceReminds();

            $ir->showdebtorPayments($this->_getParam('user'), $this->_getParam('investor'), $this->_getParam('relation'));
        }

		
		public function caMarkSendAction(){
			$this->disableView();
			
			if(!$this->_getParam('user')) return;
			
			foreach(InvoicesReminds::forUser($this->_getParam('user'), 99) as $remind){
				$remind->send = true;
				$remind->save();
			}
			
			$this->_redirect('reminds/ca-list');
		}
			
		public function updatePostponesAction() {
			$this->disableView();
			
			if (!$this->getRequest()->isPost())
				die("Invalid request");
				
			if (!acl()->hasRole('admin'))
				die("Niet voldoende rechten");
			
			$ir = new InvoicesReminds();
			
			foreach ($_POST as $reminderId => $postponeDays) {
				$reminder = $ir->fetchRow($ir->select()->where('id = ?', $reminderId));
				$reminder->postpone_days = $postponeDays;
				$reminder->save();
			}
		}

		public function toggleRetryAction(){
			$this->disableView();

			if(!($ids = $this->_getParam('id')))
				return;

			$ir = new InvoiceReminds();

			$due_list = $ir->getDueList([$ids]);

			$ir = new InvoicesReminds();

			foreach($due_list as $due_item)
				$ir
					->getById($due_item['id'])
					->setFromArray(['retry' => $this->_getParam('toggle') == 'true'])
					->save();
		}
		
		public function markAsFinalAction(){
			$this->disableView();

			if(!($ids = $this->_getParam('selected')))
				return;


			$ir = new InvoiceReminds();
			$due_list = $ir->getDueList(json_decode($ids, true));

			$ids = [];

			foreach($due_list as $due_item)
				$ids[] = $due_item['id'];

			$ir = new InvoicesReminds();
			foreach($ir->fetchAll($ir->select()->where('id IN (' . implode_for_where_in($ids) . ')')) as $row){

				$row->final = date('Y-m-d');
				$row->status = 'ready_for_finalize';
				$row->save();
			}

			startWorker('finalize-penalties', 'cronjob', 'low');
		}

		public function finalizeLockedAction(){
			$this->disableView();

			include 'library/PIDLock.php';
			$pid_lock = new PIDLockStatus('finalize-penalties');

			if($pid_lock->isLocked())
				echo json_encode('1');
		}

		public function previewAction(){
			//this calls previewlistaction check there for the real magic
            $this->view->Breadcrumbs()
				->addCrumb('Betalingsherinneringen en aanmaningen overzicht')
				->addCrumb('Verzending preview');

			if(!($ids = $this->_getParam('selected')))
				return;

			$this->view->ids = $ids;
		}

		public function previewListAction(){
			if(!($ids = $this->_getParam('selected')))
				return;

			$ir = new InvoiceReminds();

			$preview_list = $ir->getDueList($ids);
			$this->view->preview_html = $ir->finalizePenalties($preview_list, true);
		}

		public function finalizeListAction(){

			$this->view->Breadcrumbs()->addCrumb('Betalingsherinneringen en aanmaningen overzicht');

			$ir_model = new InvoicesReminds();

			$select = db()->select()
				
				->from(array('irem' => 'invoice_reminds'), 	array('status', 'date' => 'final', 'penalty', 'send_method' => 'IF(irem.post > 0 && irem.email > 0, "Post en E-mail", IF(irem.post > 0, "Post", IF(irem.email > 0, "E-mail", "-")))'))
				
				->joinLeft(array('t' => 'transactions'),
					't.id = irem.transaction',				array('name' => 'user'))
				
				->joinLeft(array('i' => 'invoices'),
					'i.id = t.invoice',						array('factuur' => 'CONCAT(ir.identifier, ".", COALESCE(IF(CHAR_LENGTH(i.identifier) < 4, LPAD(i.identifier,4,"0"), i.identifier), "[xxxx]"))', 'id'))
					
				->joinLeft(array('ir' => 'invoices_run'),
					'ir.id = i.run',						array())
			
				->where('irem.penalty != ?', 99)
				->where('irem.final IS NOT NULL')
				->order('irem.final DESC');
			
			$data = db()->fetchAll($select);

			$this->view->ListView($data)
				->setTypes(array(
					'penalty' => 	array('title' => 'Type', 'width' => 'medium'),
					'name' => 	array('title' => 'Klant', 'width' => 'xxlarge truncate'),
					'factuur' => 	array('title' => 'Factuur', 'width' => 'medium button pdf'),
					'date' => 	array('title' => 'Verzenddatum', 'width' => 'medium'),
					'status' => ['title' => 'Status', 'width' => 'medium'],
					'send_method' => 	array('title' => 'Verzendmethode', 'width' => 'medium'),
				))

				->addFormat('factuur', function($value, $item){
					return '<a target="_blank" href="invoice/export/id/' . $item['id'] . '/">' . ($value ? $value : '-') . '</a>';
				})

				->addFormat('status', function($value) use ($ir_model){
					if(isset($ir_model->statusses[$value]))
						return $ir_model->statusses[$value];

					return '-';
				})

				->addFormat('penalty', function($value){
					if($value == '1')
						return 'Herinnering';

					if($value == '2')
						return 'Aanmaning';

					if($value == '3')
						return '2e aanmaning';

					return '-';
				})


				->addFormat('date', function($value){ 
					$datetime = new DateTime($value);
					return $datetime->format('d-m-Y');
				})

				->addFormat('name', 'user_support_link')

				->addFormat('post', function($value){
					return $value;
				})

				->setFilters(array(
					'penalty' => array(),
					'name' => ['type' => 'input'],
					'factuur' => array('type' => 'input'),
					'date' => ['type' => 'date_range'],
					'status' => array(),
					'send_method' => array()
				))

				->render($this);
		}

		public function viewFinalAction(){
			$this->disableView();
			
			$ir = new InvoiceReminds();
			
			$last = db()->fetchOne(db()->select()->from('invoice_reminds', array('final'))->where('send = ?', 1)->order('final DESC'));
			
			$select = db()->select()
				
				->from(array('irem' => 'invoice_reminds'), 	array('penalty'))
				
				->joinLeft(array('t' => 'transactions'),
					't.id = irem.transaction',				array('user', 'amount', 'payed', 'closed'))
				
				->joinLeft(array('i' => 'invoices'),
					'i.id = t.invoice',						array('id', 'identifier'))
					
				->joinLeft(array('ir' => 'invoices_run'),
					'ir.id = i.run',						array('ir_identifier' => 'identifier', 'period', 'periodvalue', 'start'))
				
				->where('irem.send = ?', 1)
				
				->where('irem.final = ?', $last);
			
			echo 'Verzonden: ' . date('d-m-Y', strtotime($last)) . '<pre>';
			
			foreach(db()->fetchAll($select) as $remind){
				$remind['identifier'] = $remind['ir_identifier'] . '.' . $remind['identifier'];
				$remind['period'] = Invoice::translatePeroid($remind['period'], $remind['periodvalue']) . ' ' . date('Y', strtotime($remind['start']));
				$remind['user'] = User::buildname(false, $remind['user']);
				unset($remind['ir_identifier'], $remind['periodvalue'], $remind['start']);
				
				print_r(array(
					'Naam' => $remind['user'],
					'Factuurnummer' => '<a href="https://www.mijnvaanster.nl/invoice/export/id/' . $remind['id'] . '/" target="_blank">' . $remind['identifier'] . '</a>',
					'Periode' => $remind['period'],
					'Type' => $ir->fines[$remind['penalty']]['title'],
					'Bedrag' => '&euro; ' . new StringFormat($remind['amount']/100, 'money'),
					'Betaald' => $remind['payed'] > 0 ? '&euro; ' . new StringFormat($remind['payed']/100, 'money') : '-',
					'Gesloten' => $remind['closed'] ? 'Ja' : 'Nee'
				));
			}
			
		}		
		
		public function overviewAction() {
			
			$ir = new InvoiceReminds();
			$p = new Project();
			$t = new Transaction();
			
			$this->view->filters =		$ir->filters;
			$this->view->lastUpdates = 	$t->lastUpdate();
			$this->view->reminds = 		$ir->getGroupedList($unsendOnly);
			$this->view->projects = 	$ir->getProjects();
			$this->view->years = 		$ir->getYears();
			$this->view->fines = 		$ir->fines;
			$this->view->next = 		$ir->getNext();
		}
		
		public function josefAction() {
			
			$ir = new InvoiceReminds();
			$p = new Project();
			$t = new Transaction();
			
			$this->view->remind = 		$this->_getParam('remind') ? $this->_getParam('remind') : $ir->getNext();;
			$this->view->reminds = 		$ir->getJosefList();
		}
		
		public function testAction(){
			$this->disableView();
			
			$r = new InvoiceReminds();	
			$r->finalize();
			
		}
		
		public function listAction(){
			$this->view->Breadcrumbs()->addCrumb('Herinneringen');
			
			$type = false;
			
			if ($this->_getParam('run') && $this->_getParam('period') && $this->_getParam('penalty')){
				$type = 'normal';
			} elseif ($this->_getParam('penalty') && $this->_getParam('user')) {
				$type = 'ca';
			}
			
			if ($type === false)
				die();
				
									
			$ir = new InvoiceReminds();
			$this->view->reminds = $ir->getList($type, $this->_getAllParams());
		}
		
		public function exampleRemindMailAction(){
			$this->disableView();
			
			$ir = new InvoiceReminds();
			$ir->exampleMail();
		}

		public function deleteRemindAction() {

			if (!$this->_getParam('id'))
				die();

			$ir = new InvoicesReminds();
			$row = $ir->fetchRow($ir->select()->where('id = ?', $this->_getParam('id')));

			$select = $ir->select()
				->where('transaction = ?', $row->transaction)
				->where('send = ?', false);

			foreach($ir->fetchAll($select) as $remind)
				$remind->delete();
		}
	}
