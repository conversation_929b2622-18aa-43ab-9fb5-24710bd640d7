<?

class Inspection_TypesController extends GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Inspectie types');

        $this->view->modelListView('InspectionTypes')
            ->setTypes(['description_nl', 'origin'])
            ->addFormat('origin', function ($value) {
                return $value == 'system' ? 'Systeem' : 'Gebruiker';
            })
            ->setOptions([
                'item_title' => 'Inspectietype',
                'disabled_buttons_test' => function ($row, $button) {

                    if ($button !== 'delete') {
                        return false;
                    }

                    if ($row['origin'] == 'Systeem') {
                        return true;
                    } else {
                        return false;
                    }
                }
            ])
            ->addButtons(
                [
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen'
                ],
                [
                    'id' => 'id',
                ]
            )
            ->render($this);
    }

    /**
     * Deletes the InspectionType indicated by the id parameter
     * and redirects to the 'list' action
     *
     * @return null
     */
    public function deleteAction()
    {
        $this->disableView();
        $id = $this->_getParam('id');
        $it = new InspectionTypes();

        if (intval($id) < 1) {
            $this->_helper->redirector('list', 'inspection_types');
        }

        $it = $it->markAsArchive($id);

        if (isset($it)) {
            $it->delete();
        }

        $this->_helper->redirector('list', 'inspection_types');
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Inspectie Types', 'inspection_types/list/')
            ->addCrumb('Inspectietype ' . ($this->_getParam('id') ? 'bewerken' : 'toevoegen'), '');

        $this->id = $this->getParam('id');

        $this->view->form = $this->view->EditView(
            $this,
            'InspectionTypes',
            ['id' => $this->_getParam('id')]
        )
        ->render($this);

    }
}
