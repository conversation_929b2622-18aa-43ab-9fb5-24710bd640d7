<?php

use DbTable\ItemProjectModel;

abstract class Hospitality_AbstractItemController extends GlobalController
{
    public $type;
    protected $names = [
        'activity' => 'Activiteit',
        'news' => 'Nieuws'
    ];

    protected $pluralNames = [
        'activity' => 'Activiteiten',
        'news' => 'Nieuws'
    ];

    public function indexAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Hospitality')->addCrumb($this->names[$this->type] . 'overzicht');

        $data = (new \Hospitality\Item())->fetchAll(['type = ?' => $this->type])->toArray();
        $data = $this->markItemExpired($data);

        $types = [
            'title' => ['title' => 'Titel', 'width' => 'xlarge'],
            'short_description' => ['Korte omschrijving' => 'Huurder', 'width' => 'xlarge truncate'],
            'publish_date' => ['title' => 'Publiceer datum', 'width' => 'small'],
            'is_published' => ['title' => 'Gepubliceerd', 'width' => 'xsmall'],
            'period_from' => ['title' => 'Van', 'width' => 'xsmall'],
            'period_till' => ['title' => 'Tot', 'width' => 'xsmall'],
            'hide_expired' => ['title' => '', 'width' => 'hidden']
        ];

        $filters = [
            'hide_expired' => [
                'renderSeparately' => true,
                'title' => 'Verberg verlopen items',
                'hideCount' => true,
                'custom_options' => [
                    0 => ['title' => 'ja']
                ],
                'custom_options_only' => true
            ],
            'title' => ['type' => 'input'],
            'short_description' => ['type' => 'input'],
            'is_published' => [],
            'period_from' => [],
            'period_till' => []
        ];

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('publish_date', 'date')
            ->addFormat('is_published', function ($value) {
                return $value ? 'ja' : 'nee';
            })
            ->addFormat('period_from', 'date')
            ->addFormat('period_till', 'date')
            ->addFormat('short_description', function ($value) {
                return "<i class='fa fa-info-circle tooltip' style='cursor: pointer' aria-hidden='true' title='{$value}' rel=' '></i>$value";
            })
            ->addButtons(
                [
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen'
                ]
            )
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function portalItemsAction()
    {
        $this->view->assign('project_id', $this->getParam('project_id'));
    }
    
    public function portalListAction()
    {
        $projectId = $this->getParam('project_id');

        if (!$projectId) {
            return;
        }

        echo '<style> .item-title { width: 100%;} </style>';

        $this->view->Breadcrumbs()->addCrumb($this->pluralNames[$this->type]);

        $data = db()->select()
            ->from(['ip' => 'hospitality_item_project'], false)
            ->where('ip.project_id = ?', $projectId)
            ->joinLeft(['i' => 'hospitality_item'], 'i.id = ip.item_id', ["*"])
            ->where('i.type = ?', $this->type)
            ->where('i.is_published = ?', 1)
            ->where('publish_date >= ?', (new Datetime())->format('Y-m-d'))
            ->query()
            ->fetchAll();

        if (empty($data)) {
            return;
        }

        $types = [
            'title' => ['width' => 'item-title'],
        ];

        $filters = [
            'title' => ['type' => 'input'],
            'short_description' => ['type' => 'input'],
            'is_published' => [],
            'period_from' => [],
            'period_till' => []
        ];

        $this->view->ListView($data, ['url-override' => 'hospitality_'.$this->type.'-item/portal-list/', 'project_id' => $projectId])
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('title', function ($title, $row) {
                return "<div class='item'><h1>$title</h1>
                        <p>{$row['short_description']}</p>
                        <div style='text-align: right'>
                            <a 
                            href='#' 
                            class='button-detail' 
                            data-id='{$row['id']}' 
                            data-type='{$row['type']}'
                            >Lees meer</a>
                        </div>
                        <hr>
                        </div>";
            })
            ->addDetails('hospitality_' . $this->type . '-item/portal-detail/id/', ['style' => 'popup'])
            ->addFormat('details', function ($details, $item) {
                return '';
            })
            ->setOptions([
                'paginator_items_per_page' => 3,
            ])
            ->render($this);
    }

    public function portalDetailAction()
    {
        $itemId = $this->getParam('id');

        if (!$itemId) {
            die;
        }

        $item = (new \Hospitality\Item())->fetchRowById($itemId);
        $this->view->assign('item', $item);
    }

    private function markItemExpired($data)
    {
        foreach ($data as &$item) {
            $now = new DateTime();
            $publishDate = new DateTime($item['publish_date']);
            $item['hide_expired'] = $publishDate < $now;
        }

        return $data;
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Hospitality');

        $activityItemId = $this->getParam('id');

        $crumbTitle = $activityItemId ? 'Bestaande item bewerken' : 'Nieuwe item aanmaken';
        $this->view->Breadcrumbs()->addCrumb($crumbTitle);

        $this->view->EditView($this, 'Hospitality/Item', [])
            ->setOptions([
                'redirect_to' => 'referer',
                'postHandler' => function ($post, $params) {
                    $model = new \Hospitality\Item();
                    $itemId = $model->save($post, $params);

                    if (!empty($_FILES['image']) && $itemId) {
                        $image = $_FILES['image'];
                        $imagePath = Main::app()->getDir('hospitality') . 'items/' . $itemId . '/images/' . $image['name'];
                        createFolder($imagePath);
                        $imagePath = checkFilenameAvailability($imagePath);
                        $isSuccess = move_uploaded_file($image['tmp_name'], $imagePath);

                        if ($isSuccess) {
                            $path_parts = pathinfo($imagePath);
                            $filename = $path_parts['basename'];
                            $model->update(['image' => $filename], ['id = ?' => $itemId]);
                        }
                    }

                    $this->redirect('hospitality_' . $post['item']['type'] . '-item/index/hide_expired/0');
                }
            ])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $itemId = $this->getParam('id');

        if (!$itemId) {
            die();
        }

        $model = new \Hospitality\Item();
        $item = $model->fetchRowById($itemId);

        $model->delete('id = "' . $itemId . '"');
        (new ItemProjectModel())->delete('item_id = "' . $itemId . '"');

        $this->redirect('hospitality_' . $item['type'] . '-item/index/hide_expired/0');
    }
}
