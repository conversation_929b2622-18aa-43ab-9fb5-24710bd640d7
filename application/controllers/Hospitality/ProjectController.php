<?php

use Hospitality\Projects;

class Hospitality_ProjectController  extends GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Hospitality diensten');

        $this->view->headScriptHashed()->appendFile('media/javascript/hospitality_project/list.js');
        $this->view->headLink()->appendStylesheet('media/style/incoming-mail-links/main.css');

        $params = ['url-override' => 'hospitality_project/list'];
        foreach (['view_template', 'type'] as $param)
            if (!is_null($this->_getParam($param)))
                $params[$param] = $this->_getParam($param);

        $params['project_id'] = $this->getParam('project_id');

        $this->view->modelListView('Hospitality/Projects', $params)
            ->setTypes(['category_id', 'products'])
            ->addFormat('category_id', function ($value, $row) {
                return '<i class="fa fa-pen" aria-hidden="true"></i> ' . $row['category_name'];
            })
            ->addFormat('products', function ($value) {
                $productCount = count(array_filter(explode(',', $value)));
                return "Aantal: $productCount";
            })
            ->setOptions([
                'item_title' => 'Product',
                'paginator_items_per_page' => 5,
                'editAction'
            ])
            ->addButtons([
                'add' => 'Toevoegen',
                'delete' => 'Verwijderen'
            ])
            ->render($this);
    }

    public function editAction()
    {
        $this->view->EditView($this, 'Hospitality/Projects', [])
            ->setOptions([
                'redirect_to' => 'referer'
            ])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        if (is_numeric($this->_getParam('id')))
            (new Projects())->delete(['id = ?' => $this->getParam('id')]);
    }
}
