<?php

class Hospitality_ProductController extends GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Hospitality producten');

        $this->view->headScriptHashed()->appendFile('media/javascript/hospitality_product/list.js');
        $this->view->headLink()->appendStylesheet('media/style/incoming-mail-links/main.css');

        $params = ['url-override' => 'hospitality_product/list'];
        foreach (['view_template', 'type'] as $param)
            if (!is_null($this->_getParam($param)))
                $params[$param] = $this->_getParam($param);

        $userId = $this->getParam('user_id');
        $company = Company::getByDummyUserId($userId);
        $params['company_id'] = $company->id;

        $this->view->modelListView('Hospitality/Products', $params)
            ->setTypes(['title', 'short_description', 'category', 'name', 'rate', 'active'])
            ->addFormat('short_description', function ($value) {
                return "<span class='popover' style='cursor: pointer' rel='$value' aria-hidden='true'>$value</span>";
            })
            ->addFormat('rate', 'money')
            ->addFormat('category', function ($value, $row) {
                return $row['category_name'];
            })
            ->addFormat('active', function ($value) {
                return $value == '1' ? 'ja' : 'nee';
            })
            ->setOptions([
                'item_title' => 'Product',
                'paginator_items_per_page' => 5,
                'editAction'
            ])
            ->addButtons([
                'add' => 'Toevoegen'
            ])
            ->render($this);
    }

    public function editAction()
    {
        $this->view->EditView($this, 'Hospitality/Products', [])
            ->setOptions([
                'redirect_to' => 'referer'
            ])
            ->render($this);
    }
}