<?php

class User_TrustedDevicesController extends GlobalController
{
    public function indexAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Vertrouwde apparaten');

        $this->view->extraWidth = true;

        $data = (new \DbTable\TrustedDevices())->fetchAll([
            'user_id  = ?' => LoginManager::data()->id,
            'is_deleted = ?' => 0
            ],
            [
            'last_two_fa_check DESC'
            ]
        );

        $this->view->list = $this->view->listView($data)
            ->setTypes([
                'device' => ['title' => 'Apparaat', 'width' => 'xxxxxlarge truncute'],
                'ip_address' => ['title' => 'IP Adres', 'width' => 'large right'],
                'last_two_fa_check' => ['title' => 'Laatste 2FA controle', 'width' => 'medium right'],
                'actions' => ['title' => 'Acties', 'width' => 'medium right'],
            ])
            ->addFormat('device', function ($value, $row) {
                if (empty($row['device_type']) || empty($row['os_name']) || empty($row['browser_name'])) {
                    return $row['user_agent'];
                }

                return ucfirst($row['device_type']) . ' : ' . ucfirst($row['os_name']) . ' : ' . ucfirst($row['browser_name']);
            })
            ->addFormat('actions', function ($value, $items) {
                return "<a href='/user_trusted-devices/delete/trusted_device_id/{$items['id']}'>Verwijderen <i class='fa fa-trash-alt'> </i></a>";
            })
            ->render($this);
    }

    public function deleteAction()
    {
        $trustedDeviceId = $this->getParam('trusted_device_id');
        $trustedDeviceRow = (new \DbTable\TrustedDevices())->fetchRowById($trustedDeviceId);

        if (null === $trustedDeviceRow) {
            throw new RuntimeException('Could not find trusted device');
        }

        $trustedDeviceRow->is_deleted = 1;
        $trustedDeviceRow->save();
        $this->redirect('/user_trusted-devices');
    }
}
