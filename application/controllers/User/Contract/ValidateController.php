<?php

//Investor_Vat_OverviewController
class User_Contract_ValidateController extends GlobalController
{
    public function preDispatch()
    {
        parent::preDispatch();

        $this->view->Breadcrumbs()
            ->addCrumb('Contract validatie');
    }

    public function dashboardAction()
    {

        $list = new \Controllers\User\Contract\ValidateList();
        $list->setUnvalidatedOnly(true);

        $this->view->count = count($list->get());
    }

    public function listAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Contract validatie lijst');

        if(is_null($this->getParam('is_validated')) && !$this->isAjaxRequest){
            $this->setParam('is_validated', false);
        }

        if (is_null($this->getParam('deleted')) && !$this->isAjaxRequest) {
            $this->setParam('deleted', false);
        }

        $list = new \Controllers\User\Contract\ValidateList();

        $this->view->list = $this->view->listView($list->get(), ['url-override' => 'user_contract_validate/list'])

            ->setTypes([
                'project_id' => ['title' => 'Project', 'width' => 'hidden'],
                'is_validated' => ['title' => 'Gevalideerd', 'width' => 'hidden'],
                'validate_user_id' => ['title' => 'Account manager', 'width' => 'xlarge truncate'],
                'object_id' => ['title' => 'Object', 'width' => 'xxlarge truncate'],
                'user_id' => ['title' => 'Huurder', 'width' => 'xxlarge truncate'],
                'preview' => ['title' => 'Contract preview', 'width' => 'button small pdf'],
                'validate_timestamp' => ['title' => 'Validatie datum', 'width' => 'xsmall date'],
                'deleted' => ['title' => '', 'width' => 'hidden'],
            ])

            ->addFormat(['is_validated', 'deleted'], 'bool')

            ->addFormat('validate_timestamp', 'date')

            ->addFormat('validate_user_id', function($value, $row){
                if($value > 0){
                    return '<a href="user/admin-edit/id/' . $value . '/" target="_blank">' . $row['validate_user_name'] . '</a>';
                } else {
                    return '-';
                }
            })

            ->addFormat('user_id', function($value, $row){
                if($value > 0){
                    return '<a href="support/show/type/user/id/' . $value . '/" target="_blank">' . $row['user_name'] . '</a>';
                } else {
                    return '-';
                }
            })

            ->addFormat('object_id', function($value, $row){
                if($value > 0){
                    return '<a href="support/show/type/object/id/' . $value . '/" target="_blank">' . $row['object_address'] . '</a>';
                } else {
                    return '-';
                }
            })

            ->addFormat('project_id', function($value, $row){
                if($value > 0){
                    return '<a href="project/details/id/' . $value . '/" target="_blank">' . $row['project_name'] . '</a>';
                } else {
                    return '-';
                }
            })

            ->addFormat('preview', function($value, $row){
                return '<a href="user_contract_validate/preview/id/' . $row['id'] . '/">Contract PDF</a>';
            })

            ->setOptions([
                'item_title' => 'Validatie contract',
            ])



            ->addActions([
                'disabled_check' => function($row){
                    return $row['is_validated'] == 'Nee' && $row['deleted'] == 'Nee';
                },
                'buttons' => [
                    [
                        'title' => 'Contract(en) valideren',
                        'icon' => 'page_go',
                        'params' => [],
                        'action' => 'user_contract_validate/validate/',
                        'confirm' => true
                    ],
                    [
                        'title' => 'Contract(en) verwijderen',
                        'icon' => 'delete',
                        'params' => [],
                        'action' => 'user_contract_validate/delete/',
                        'confirm' => true
                    ]
                ]
            ])

            ->setFilters([
                'project_id' => ['renderSeparately' => true, 'title' => 'Project'],
                'is_validated' => ['renderSeparately' => true, 'title' => 'Gevalideerd'],
                'deleted' => ['renderSeparately' => true, 'title' => 'Verwijderd'],
                'validate_user_id' => [],
                'user_id' => ['type' => 'input'],
                'object_id' => ['type' => 'input'],
                'validate_timestamp' => ['type' => 'date_range', 'renderSeparately' => true, 'title' => 'Validatie datum']
            ])

            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $ids = $this->getParam('ids');

        if (!$ids) {
            return;
        }

        $deleteService = new \User\Contract\Validate\DeleteService(
            new Objectusers(),
            new Users(),
            new UserContactValidate()
        );

        foreach ($ids as $id) {
            $deleteService->execute($id);
        }
    }


    public function previewAction()
    {
        $this->disableView();

        $createContractPdfService = new \Controllers\User\Contract\CreateContractPdfService(
            new Pdf(),
            new \Controllers\User\Contract\AddInvestorSignatureToTemplateVariablesService(
                Main::app()->getDir('investor_signature')
            )
        );
        $createContractDocumentsPdfService = new \Controllers\User\Contract\CreateContractDocumentsPdfService(
            new Pdf(),
            new \Controllers\User\Contract\AddInvestorSignatureToTemplateVariablesService(
                Main::app()->getDir('investor_signature')
            )
        );

        $contractSignPreviewService = new \Controllers\User\Contract\ContractSignPreviewService(
            $createContractPdfService,
            $createContractDocumentsPdfService
        );
        $contractSignPreviewService->execute($this->getParam('id'));
    }

    public function validateAction()
    {
        $this->disableView();

        foreach($this->getParam('ids') as $validate_id) {

            $createContractPdfService = new \Controllers\User\Contract\CreateContractPdfService(
                new Pdf(),
                new \Controllers\User\Contract\AddInvestorSignatureToTemplateVariablesService(
                    Main::app()->getDir('investor_signature')
                )
            );
            $createContractDocumentsPdfService = new \Controllers\User\Contract\CreateContractDocumentsPdfService(
                new Pdf(),
                new \Controllers\User\Contract\AddInvestorSignatureToTemplateVariablesService(
                    Main::app()->getDir('investor_signature')
                )
            );

            $contractSignValidateService = new \Controllers\User\Contract\ContractSignValidateService(
                new UserContactValidate(),
                $createContractPdfService,
                $createContractDocumentsPdfService,
                new User(),
                new SupportDocuments()
            );

            $contractSignValidateService->execute($validate_id);
        }
    }

    public function investorsListAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Contract validatie investeerders lijst');

        $colums = [
            'investor',
            'rendered_name',
            'edit'
        ];

        $this->view->modelListView('Investors')
            ->setTypes($colums)
            ->addFormat('investor', function ($value, $item) {
                return $item['name'];
            })
            ->addFormat('edit', function ($value, $item) {
                return '<a href="user_contract_validate/investor-edit/investor-id/' . $item['id'] . '" class="button edit" title="Aanpassen">&nbsp;</a>';
            })
            ->setFilters([
                'investor' => ['type' => 'input'],
                'rendered_name' => ['type' => 'input']
            ])
            ->render($this);

    }

    public function investorEditAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Contract validatie investeerders lijst', 'user_contract_validate/investors-list')
            ->addCrumb('Handtekening upload', '');

        $params = $this->getAllUrlParams();

        $contactForm = new Form($this, 'user/contract/investor-edit');
        $contactForm->setAction('user_contract_validate/investor-edit/investorId/'. $params['investorId']);
        $this->view->contactForm = $contactForm;

        if (!$this->getRequest()->isPost()) {
            return;
        }

        if (!isset($_FILES['signature'])) {
            return;
        }

        $postData = $this->getRequest()->getPost();

        $uploadFileTempFilePath = $_FILES['signature']['tmp_name'];
        $investorSignatureFolderPath = \Main::app()->getDir('investor_signature');
        $investorSignatureFileName = $params['investorId'];
        $moveUploadTo = $investorSignatureFolderPath . $investorSignatureFileName . '.jpg';
        createFolder($moveUploadTo);

        if (!move_uploaded_file($uploadFileTempFilePath, $moveUploadTo)) {
            throw new \Exception('Error not able to upload file for '. $params['investorId'] . ' to ' . $moveUploadTo);
        }

        $this->redirect('user_contract_validate/investors-list');

    }
}
