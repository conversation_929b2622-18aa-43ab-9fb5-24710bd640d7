<?php

use KpiRapport\Exploitation\ViewExploitationCostsService;
use KpiRapport\Exploitation\ViewExploitationRentalIncomeService;
use KpiRapport\KpiBaseController;

class KpiRapport_ExploitationController extends KpiBaseController
{
    public function rentalIncomeAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Exploitatie - Huuromzet');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'small'],
            'rental_income' => ['title' => 'Huuromzet', 'width' => 'large right'],
            'rental_income_ytd' => ['title' => 'Year to date', 'width' => 'large right']
        ];

        $filters = [
            'start_period' => ['title' => 'Maand', 'type' => 'select'],
        ];

        $formats = [
            'start_period' => function ($value, $item) {
                return $value ? strftime('%B %Y', strtotime($value)) : false;
            },
            'rental_income' => function ($value, $item) {
                $isProjection = $item['rental_income_projection'] ? 'style="color: #337AB7; font-weight: bold;"' :  '';
                $rentalIncome = is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-';
                return "<span $isProjection>$rentalIncome</span>";
            },
            'rental_income_ytd' => 'money'
        ];

        $options = [];
        $totals = ['rental_income'];

        $data = (new ViewExploitationRentalIncomeService($this->startMonth, $this->endMonth))->execute(true);
        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals, $options);
    }

    public function costsAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Exploitatie - Kosten');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'object_group' => ['title' => 'Objectgroup', 'width' => 'large','group_equal_rows' => true],
            'object' => ['title' => 'Object', 'width' => 'xxxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'large'],
			'component' => ['title' => 'Component', 'width' => 'large'],
            'costs' => ['title' => 'Kosten', 'width' => 'large right'],
            'costs_ytd' => ['title' => 'Cumulatief', 'width' => 'large right']
        ];

        $filters = [
            'component' => ['title' => 'Maand', 'type' => 'select'],
			'start_period' => ['title' => 'Maand', 'type' => 'select']
        ];

        $formats = [
            'start_period' => function ($value, $item) {
                return strftime('%B %Y', strtotime($value));
            },
			'component' => function($value, $item){
        		return $value > 0 ? $item['component_name'] : '-';
			},
            'costs' => 'money',
			'costs_ytd' => 'money'
        ];

        $totals = ['costs'];

        $data = (new ViewExploitationCostsService($this->startMonth, $this->endMonth))
			->setGroupedPerComponent(true)
            ->excludeCapexComponents()
			->execute();
        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals);
    }
}