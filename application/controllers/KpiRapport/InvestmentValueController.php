<?php

use <PERSON>pi<PERSON>apport\KpiBaseController;
use KpiRapport\ViewInvestmentValueService;

class KpiRapport_InvestmentValueController extends KpiBaseController
{
    public function listAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Beleggingswaarde');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'rental_income' => ['title' => 'Huuromzet', 'width' => 'large right'],
            'rental_value_factor' => ['title' => 'Factor', 'width' => 'large right'],
            'invest_value' => ['title' => 'Beleggingswaarde', 'width' => 'large right'],
        ];

        $filters = [];

        $formats = [
            'rental_income' => function ($value, $item) {
                $isProjection = $item['rental_income_projection'] ? 'style="color: #337AB7; font-weight: bold;"' :  '';
                $rentalIncome = is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-';
                return "<span $isProjection>$rentalIncome</span>";
            },
            'invest_value' => 'money',
        ];

        $totals = ['rental_income', 'invest_value'];

        $data = (new ViewInvestmentValueService($this->startMonth, $this->endMonth))->execute();
        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals);
    }
}