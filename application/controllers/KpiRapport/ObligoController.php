<?php

use <PERSON><PERSON><PERSON>apport\KpiBaseController;
use <PERSON>piRapport\ViewObligoService;

class KpiRapport_ObligoController extends KpiBaseController
{
    public function totalOutstandingBondAction()
    {
		$this->view->extraWidth = false;

		$this->view->Breadcrumbs()->addCrumb('Obligo - Aflossing');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxxlarge', 'group_equal_rows' => true],
			'object_group' => ['title' => '', 'width' => 'hidden'],
			'object' => ['title' => '', 'width' => 'hidden'],
			'payoff' => ['title' => 'Maandelijkse aflossing', 'width' => 'xxxlarge right'],
			'yearly_payoff' => ['title' => 'Jaarlijkse aflossing', 'width' => 'xxxlarge right'],
        ];

        $filters = [
			'start_month' => ['preApplied' => true],
			'end_month' => ['preApplied' => true],
			'object_group' => ['preApplied' => true],
			'project' => ['title' => 'Project', 'type' => 'select']
		];

        $formats = [
			'yearly_payoff' => 'money',
            'payoff' => 'money',
        ];

		$this->startMonth = new \DateTime(date('d-m-Y'));
		$this->endMonth = (new \DateTime(date('d-m-Y')))->modify("+ 1 month");

		$totals = ['payoff', 'yearly_payoff'];

        $data = (new ViewObligoService($this->startMonth, $this->endMonth))->execute();

        $data = array_filter($data, function ($item) {
            return $item['is_project_row'] == "1";
        });

        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals);
    }
}
