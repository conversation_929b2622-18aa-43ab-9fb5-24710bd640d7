<?php

use KpiRapport\KpiBaseController;
use KpiRapport\Returns\ViewReturnIndirectReturnService;

class  KpiRapport_ReturnController extends KpiBaseController
{
    public function directReturnAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Rendement - Direct rendement');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'small'],
            'rental_income' => ['title' => 'Huuromzet', 'width' => 'small right'],
            'opex_costs' => ['title' => '(opex) Kosten', 'width' => 'small right'],
            'purchase_price' => ['title' => 'Aankoopsom', 'width' => 'small right'],
            'costs' => ['title' => ' (capex) Kosten', 'width' => 'small right'],
            'purchase_costs' => ['title' => 'Aanschafkosten', 'width' => 'small right'],
            'direct_return' => ['title' => 'Direct rendement', 'width' => 'small right']
        ];

        $filters = [];

        $formats = [
            'start_period' => function ($value, $item) {
                return $value ? strftime('%B %Y', strtotime($value)) : false;
            },
            'rental_income' => function ($value, $item) {
                $isProjection = $item['rental_income_projection'] ? 'style="color: #337AB7; font-weight: bold;"' :  '';
                $rentalIncome = is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-';
                return "<span $isProjection>$rentalIncome</span>";
            },
            'opex_costs' => 'money',
            'purchase_costs' => 'money',
            'purchase_price' => 'money',
            'costs' => 'money',
            'direct_return' => function ($value) {
                return $value ? round($value * 100, 2) . '%' : false;
            }
        ];

        $options = [
            'paginator' => false
        ];
        
        $totals = [
            'rental_income',
            'opex_costs',
            'purchase_price',
            'costs',
            'purchase_costs',
            'direct_return' => ['sum_type' => 'average']
        ];

        $data = (new \KpiRapport\Returns\ViewReturnDirectReturnService(
            $this->startMonth,
            $this->endMonth)
        )->execute();

        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals, $options);
    }

    public function freeCashFlowAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Rendement - Free cash flow');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'small'],
            'rental_income' => ['title' => 'Huuromzet', 'width' => 'large right'],
            'costs' => ['title' => 'Kosten', 'width' => 'large right'],
            'payoff' => ['title' => 'Aflossing', 'width' => 'large right'],
            'free_cash_flow' => ['title' => 'Free cash flow', 'width' => 'large right']
        ];

        $filters = [];

        $formats = [
            'start_period' => function ($value, $item) {
                return $value ? strftime('%B %Y', strtotime($value)) : false;
            },
            'rental_income' => function ($value, $item) {
                $isProjection = $item['rental_income_projection'] ? 'style="color: #337AB7; font-weight: bold;"' :  '';
                $rentalIncome = is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-';
                return "<span $isProjection>$rentalIncome</span>";
            },
            'costs' => 'money',
            'payoff' => 'money',
            'free_cash_flow' => 'money'
        ];

        $totals = ['rental_income', 'costs', 'payoff', 'free_cash_flow'];

        $data = (new \KpiRapport\Returns\ViewReturnFreeCashFlowService(
            $this->startMonth,
            $this->endMonth
        ))->execute();

        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals);
    }

    public function indirectReturnAction()
    {
		$this->view->Breadcrumbs()->addCrumb('Rendement - Indirect rendement');

        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'small'],
            'invest_value' => ['title' => 'Beleggingswaarde', 'width' => 'large right'],
            'total_purchase_price' => ['title' => 'Totale Aanschafkosten', 'width' => 'large right'],
            'indirect_return' => ['title' => 'Indirect rendement', 'width' => 'large right'],
        ];

        $filters = [];

        $formats = [
            'start_period' => function ($value, $item) {
                return $value ? strftime('%B %Y', strtotime($value)) : false;
            },
            'payoff' => 'money',
            'rental_income' => function ($value, $item) {
                $isProjection = $item['rental_income_projection'] ? 'style="color: #337AB7; font-weight: bold;"' :  '';
                $rentalIncome = is_numeric($value) ? '&euro; ' . new StringFormat($value, 'money') : '-';
                return "<span $isProjection>$rentalIncome</span>";
            },
            'invest_value' => 'money',
            'total_purchase_price' => 'money',
            'indirect_return' => function ($value, $row) {
                return !$row['ignore_for_totals'] ? round($value, 2) . ' %' : null;
            },
        ];

        $options = [
            'paginator' => false
        ];

		$totals = ['invest_value', 'total_purchase_price', 'indirect_return' => ['sum_type' => 'average']];

        $data = (new ViewReturnIndirectReturnService(
            $this->startMonth,
            $this->endMonth
        ))->execute();

        $this->view->list = $this->buildListView($data, $types, $filters, $formats, $totals, $options);
    }
}
