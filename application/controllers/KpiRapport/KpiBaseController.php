<?php

namespace KpiRapport;

class KpiBaseController extends \GlobalController
{
	protected $startMonth;
	protected $endMonth;

    public function preDispatch()
    {
        $this->view->extraWidth = true;
        $months = $this->makeStartMonthSelect();

        // Set default start month
        if (is_null($this->getParam('start_month')))
            $this->setParam('start_month', end(array_keys($months)));

        // Set default end month
        if (is_null($this->getParam('end_month')))
            $this->setParam('end_month', 11);

        $startMonth = $this->getParam('start_month');
        $endMonth = $this->getParam('end_month') + 1;

        $this->startMonth = new \DateTime($startMonth);
        $this->endMonth = (new \DateTime($startMonth))->modify("+ {$endMonth} month");
    }

    protected function getTypes($additionalTypes = [])
    {
        $types = [
            'start_month' => ['title' => 'Begin maand', 'width' => 'hidden'],
            'end_month' => ['title' => 'Eind maand', 'width' => 'hidden'],
            'project' => ['title' => 'Project', 'width' => 'xxlarge'],
            'object_group' => ['title' => 'Objectgroup', 'width' => 'large'],
            'object' => ['title' => 'Object', 'width' => 'xxxlarge']
        ];

        return array_merge($types, $additionalTypes);
    }

    protected function getFilters($additionalFilters = [])
    {
        $months = $this->makeStartMonthSelect();
        $endMonth = $this->makeEndMonthSelect();

        $filter = [
            'project' => [
                'title' => 'Project',
                'renderSeparately' => true,
                'type' => 'select'
            ],
            'start_month' => [
                'title' => 'Begin maand',
                'renderSeparately' => true,
                'type' => 'select',
                'custom_options' => $months,
                'hideCount' => true,
                'preApplied' => true,
                'custom_options_only' => true
            ],
            'end_month' => [
                'title' => 'Eind maand',
                'renderSeparately' => true,
                'type' => 'select',
                'custom_options' => $endMonth,
                'hideCount' => true,
                'preApplied' => true,
                'custom_options_only' => true
            ],
            'object_group' => [
                'title' => 'Objectgroep',
                'renderSeparately' => true,
                'type' => 'select'
            ],
            'object' => [
                'title' => 'Object',
                'type' => 'input'
            ],
        ];

        return array_merge($filter, $additionalFilters);
    }

    protected function getOptions($additionalOptions = []){
		$options = [
			'paginator' => false
		];

		return array_merge($options, $additionalOptions);
	}

    protected function getFormats($listView)
    {
        $listView->addFormat('project', function ($value, $item) {
            return $item['project_name'];
        });

        $listView->addFormat('object', function ($value, $item) {
            return $value > 0 ? '<a target="_blank" href="/object/edit/id/' . $value . '/">' . $item['rendered_address'] . '</a>' : '-';
        });

        $listView->addFormat('object_group', function ($value, $item) {
            return $item['object_group_name'];
        });

        return $listView;
    }

    protected function buildListView($data = [], $types = [], $filters = [], $formats = [], $totals = [], $options = [])
    {
        $types = $this->getTypes($types);
        $filters = $this->getFilters($filters);
        $options = $this->getOptions($options);

        /** @var \Zend_View_Helper_ListView $listView */
        $listView = $this->view->ListView($data)
            ->setTypes($types);

        $listView = $this->getFormats($listView);

        foreach ($formats as $type => $callback)
            $listView->addFormat($type, $callback);

        $listView->setFilters($filters);

        $listView->addTotals($totals);
        $listView->setOptions($options);

        return $listView->render($this);
    }

    private function makeStartMonthSelect()
    {
        $start = new \DateTime('first day of -12 month');

        if ($start < new \DateTime('2017-01-01'))
            $start->setDate(2017,1,1);

        $end = new \DateTime('first day of next month');
        $interval = \DateInterval::createFromDateString('+ 1 month');
        $periods = new \DatePeriod($start, $interval, $end);

        $months = [];
        foreach ($periods as $period)
            $months[$period->format('d-m-Y')] = ['count' => 0, 'title' => $period->format('F Y')];

        return array_reverse($months, true);
    }

    private function makeEndMonthSelect()
    {
        $endMonth = [];
        foreach (range(0, 11) as $offset)
            $endMonth[$offset] = ['count' => 0, 'title' => '+' . $offset];

        return $endMonth;
    }
}