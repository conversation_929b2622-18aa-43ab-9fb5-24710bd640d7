<?php

use <PERSON>piRapport\KpiBaseController;

class KpiRapport_VacancyController extends KpiBaseController
{
    public function listAction()
    {
        $types = [
            'project' => ['title' => 'Project', 'width' => 'xxlarge', 'group_equal_rows' => true],
            'start_period' => ['title' => 'Maand', 'width' => 'small'],
            'rental_income' => ['title' => 'Huuromzet', 'width' => 'large right'],
            'costs' => ['title' => 'Kosten', 'width' => 'large right'],
            'initial_value' => ['title' => 'Hoofdsom', 'width' => 'large right'],
            'direct_return' => ['title' => 'Direct rendement', 'width' => 'large right']
        ];

        $filters = [];

        $formats = [
            'start_period' => function ($value, $item) {
                return $value ? strftime('%B %Y', strtotime($value)) : false;
            },
            'rental_income' => 'money',
            'costs' => 'money',
            'initial_value' => 'money',
            'direct_return' => function($value){
                return $value ? round($value * 100, 2) . '%' : false;
            }
        ];

        $options = [
            'paginator' => false
        ];

        $data = (new ViewInvestmentValueService($this->startMonth, $this->endMonth))->execute();
        $this->view->list = $this->buildListView($data, $types, $filters, $formats, [], $options);
    }
}