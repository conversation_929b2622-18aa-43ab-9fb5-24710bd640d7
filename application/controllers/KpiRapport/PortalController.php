<?php


class KpiRapport_PortalController extends GlobalController
{
    private $investorId;
    private $projectIds;
    private $year;
    private $combination_id;
    private $data;
    private $isIncludePreviousYear = false;
    private $disableCache = false;

    public function preDispatch()
    {
        $this->appendJqueryFiles($this->view);

        $this->view->headScriptHashed()
            ->appendFile('media/javascript/kpi-rapport_portal/main.js')
            ->appendFile('media/javascript/chart/chart.bundle.2.6.0.js')
            ->appendFile('media/javascript/multiSelectFilter.js')
            ->appendFile('media/javascript/amcharts/core.js')
            ->appendFile('media/javascript/amcharts/charts.js')
            ->appendFile('media/javascript/amcharts/themes/animated.js');

        $this->view->headLink()->appendStylesheet('media/style/kpi-rapport_portal/main.css');
        $this->view->headLink()->appendStylesheet('media/style/kpi-rapport_portal/dashboard.css');

        $this->investorId = loginManager::data()->info['investor_company_id'];
        $this->projectIds = $this->getParam('project', false) ? explode(',', $this->getParam('project')) : [];
        $this->combination_id = $this->getParam('combination_id');
        $this->isIncludePreviousYear =  $this->getParam('previous_year') === 'true';

        $this->setYear();
        $this->loadCachedData();
        $this->loadDashboardData();
    }

    private function setYear(){
        $defaultYear = isset($_SESSION['kpi_year']) && is_numeric($_SESSION['kpi_year']) ? $_SESSION['kpi_year'] : date('Y');
        $year = $this->getParam('year', $defaultYear);
        $_SESSION['kpi_year'] = $this->view->year = $this->year = $year;
    }

    private function loadCachedData(){
        $non_cached_actions = [
            'generating',
            'check-cache-generating-status'
        ];

        if (in_array($this->view->pageInfo['action'], $non_cached_actions)) {
            return;
        }

        $portal_data_class = new \KpiRapport\PortalData(
            $this->investorId,
            $this->combination_id,
            $this->year
        );

        if ($this->disableCache === true) {
            $portal_data_class->buildCache(
                $portal_data_class->getCacheId($this->investorId, $this->year)
            );
        }

        $this->data = $portal_data_class->getData();

        if ($this->data === false) {
            $portal_data_class->buildCacheAsync();
            $this->redirect('kpi-rapport_portal/generating/year/' . $this->year . '/');
        }
    }

    private function loadDashboardData()
    {
        if (!$this->data) {
            return;
        }

        $dashboard_data_class = new \KpiRapport\DashboardData();
        $this->view->dashboard_data = $dashboard_data_class->get($this->data);
    }

    public function setYearAction()
    {
        $this->disableView();

        // setting the year is done in pre-dispatch

        if($referer = $_SERVER['HTTP_REFERER']) {
            header('Location: ' . $referer);
        } else {
            header('Location: /kpi-rapport_portal/dashboard');
        }
    }

    public function generatingAction(){}
    
    public function checkCacheGeneratingStatusAction(){
        $this->disableView();

        $parameters = [
            'investor_id' => loginManager::data()->info['investor_company_id'],
            'year' => $this->getParam('year') ?: date('Y')
        ];

        include 'library/PIDLock.php';
        $pid_lock = new PIDLockStatus('kpi-portal-cache', $parameters);

        if (!$pid_lock->isLocked()) {
            echo json_encode('1');
        }
    }

    public function tabMenuAction()
    {
        $this->view->actionName = $this->getParam('actionName');
    }

    public function dashboardAction()
    {
    }

    private function needsInvestorCombinationDetailsPage(){

        if (!is_null($this->combination_id)) {
            return false;
        }

    	$select = db()->select()
			->from(['investor_combination_link'], ['count' => 'COUNT(id)'])
			->where('investor_id = ?', loginManager::data()->info['investor_company_id'])
			->group('investor_id');

    	$investor_combination_count = db()->fetchOne($select);

    	return $investor_combination_count > 0;
	}

    public function directReturnAction()
    {
    	$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

		$raw_data_class = new \KpiRapport\Returns\ViewReturnDirectReturnService(
			new DateTime('01-01-' . $this->year),
			new DateTime('31-12-' . $this->year)
		);

		$raw_data = $raw_data_class->execute();

        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute();
        $service = new \KpiRapport\Charts\ViewNarChartService($data, $this->investorId);


        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\DirectReturnIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$totalReturn = array_sum(
			array_column($data, 'direct_return')
		);

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

		$portal_chart_formatted_data = [];

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

		foreach ($data as $data_item) {
			if ($is_investor_combination_page && !$data_item[$grouping_value]) {
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}

			if (!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
				$portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
                    'classes' => $data_item['classes'],
					'direct_return_total' => 0,
					'direct_return' => 0,
					'project_count' => 0,
				];
			}

			$portal_chart_formatted_data[$data_item[$grouping_value]]['direct_return_total'] += $data_item['direct_return']/100;
			$portal_chart_formatted_data[$data_item[$grouping_value]]['project_count']++;
			$portal_chart_formatted_data[$data_item[$grouping_value]]['direct_return'] = $portal_chart_formatted_data[$data_item[$grouping_value]]['direct_return_total'] / $portal_chart_formatted_data[$data_item[$grouping_value]]['project_count'];
		}

		$this->view->data = [
			'projects' => array_column($data, 'project_name'),
			'direct_return' => array_column($data, 'direct_return'),
			'average_direct_return' => $totalReturn / count($data),
			'years' => $this->getYearRange(),
			'share_percentages' => array_column($data, 'share_percentage', 'project'),
			'data' => array_values($portal_chart_formatted_data)
		];

        $this->view->selectProjects = $this->makeSelectProjects($raw_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function rentalIncomeAction()
    {
		$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

        $raw_data_class = new \KpiRapport\Exploitation\ViewExploitationRentalIncomeService(
            new DateTime('01-01-' . $this->year),
            new DateTime('31-12-' . $this->year)
        );

        $raw_data = $raw_data_class->execute(true);

        $raw_data_class = new \KpiRapport\Exploitation\ViewExploitationRentalIncomeService(
            new DateTime('01-01-' . $this->year),
            new DateTime('31-12-' . $this->year)
        );
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewRentalIncomeChartService($data, $this->investorId);

        $all_project_data = $service->execute();

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $service = new \KpiRapport\Charts\ViewRentalIncomeChartService($data, $this->investorId);

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\IndirectReturnIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

        $portal_chart_formatted_data = [];
        foreach ($data as $data_item) {
			if ($is_investor_combination_page && !$data_item[$grouping_value]) {
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}

			if(!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
				$portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
                    'rental_income' => 0,
					'rental_income_payed' => 0,
                    'rental_income_unpayed' => 0,
                    'has_projected_rental_income' => false,
				];
			}

            $portal_chart_formatted_data[$data_item[$grouping_value]]['rental_income'] += (float)$data_item['rental_income'];
			$portal_chart_formatted_data[$data_item[$grouping_value]]['rental_income_payed'] += (float)$data_item['rental_income_payed'];
            $portal_chart_formatted_data[$data_item[$grouping_value]]['rental_income_unpayed'] += (float)$data_item['rental_income_unpayed'];

            if ($data_item['rental_income_projection'] === true) {
                $portal_chart_formatted_data[$data_item[$grouping_value]]['has_projected_rental_income'] = true;
            }
        }


        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'rental_income' => array_column($data, 'rental_income'),
            'rental_income_payed' => array_column($data, 'rental_income_payed'),
            'rental_income_unpayed' => array_column($data, 'rental_income_unpayed'),
            'data' => array_values($portal_chart_formatted_data),
            'years' => $this->getYearRange(),
            'share_percentages' => array_column($data, 'share_percentage')
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function costsAction()
    {
        $raw_data_class = new \KpiRapport\Exploitation\ViewExploitationCostsService(
            new DateTime('01-01-' . $this->year),
            new DateTime('01-01-' . ($this->year + 1))
        );

		$this->view->is_investor_combination_page = $this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

        $raw_data_class->excludeCapexComponents();
        $raw_data_class->setGroupedPerComponent(true);
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewCostsChartService($data, $this->investorId);

        $all_project_data = $service->execute();

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute() ?: [];

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\CostIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }


        $portal_chart_formatted_data = [];

		foreach ($data as $dataItem) {

            $grouping_value = $dataItem[$is_investor_combination_page ? 'investor_combination_id' : 'project'];
            $grouping_title = $dataItem[$is_investor_combination_page ? 'investor_combination_name' : 'project_name'];

            if ($is_investor_combination_page && is_null($grouping_value)) {
                $grouping_value = $dataItem[$grouping_value] = 0;
                $grouping_title = $dataItem[$grouping_title] = 'Volledig eigendom';
            }

			if (!isset($portal_chart_formatted_data[$grouping_value])) {
				$portal_chart_formatted_data[$grouping_value] = [
                    'id' => $grouping_value,
					'name' => $grouping_title,
					'total' => 0,
					'data' => []
				];
			}

            $dataItem['component_totals'] = (array) $dataItem['component_totals'];

            foreach ($dataItem['component_totals'] as $component_name => $component_value) {
                if (abs($component_value) <= 0) {
                    continue;
                }

                if (!isset($portal_chart_formatted_data[$grouping_value]['data'][$component_name])) {
                    $portal_chart_formatted_data[$grouping_value]['data'][$component_name] = [
                        'component_name' => $component_name,
                        'value' => (float)0
                    ];
                }

                $portal_chart_formatted_data[$grouping_value]['data'][$component_name]['value'] += $component_value;

				uasort($portal_chart_formatted_data[$grouping_value]['data'], function ($item, $item_compare) {
					return $item['value'] < $item_compare['value'];
				});
            }

			$portal_chart_formatted_data[$grouping_value]['total'] = array_sum(
				array_column($portal_chart_formatted_data[$grouping_value]['data'], 'value')
			);

			foreach ($portal_chart_formatted_data[$grouping_value]['data'] as $component_name => $cost) {
				$percentage = $cost['value'] / $portal_chart_formatted_data[$grouping_value]['total'] * 100;

				if ($percentage > 2) {
					continue;
				}

				if (!isset($portal_chart_formatted_data[$grouping_value]['data']['other'])) {
					$portal_chart_formatted_data[$grouping_value]['data']['other'] = [
						'component_name' => 'Overige kosten',
						'value' => (float) 0
					];
				}

				$portal_chart_formatted_data[$grouping_value]['data']['other']['value'] += $cost['value'];
				unset($portal_chart_formatted_data[$grouping_value]['data'][$component_name]);
			}

			$portal_chart_formatted_data[$grouping_value]['total'] = array_sum(
				array_column($portal_chart_formatted_data[$grouping_value]['data'], 'value')
			);

        }

        foreach ($portal_chart_formatted_data as $group_key => $group_item) {
			$portal_chart_formatted_data[$group_key]['data'] = array_values($group_item['data']);
		}

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'component_totals' => array_column($data, 'component_totals'),
            'years' => $this->getYearRange(),
            'component_types' => $data['component_types'],
            'data' => $portal_chart_formatted_data,
            'share_percentages' => array_column($data, 'share_percentage')
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function freeCashFlowAction()
    {
		$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();
        $year = $this->year + 1;

        $raw_data_class = new \KpiRapport\Returns\ViewReturnFreeCashFlowService(
            new DateTime('01-01-' . $this->year),
            new DateTime('01-01-' . $year)
        );

        $raw_data = $raw_data_class->execute(true);

        $raw_data_class = new \KpiRapport\Returns\ViewReturnFreeCashFlowService(
            new DateTime('01-01-' . $this->year),
            new DateTime('01-01-' . $year)
        );
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewFreeCashFlowService($data, $this->investorId);

        $all_project_data = $service->execute();

        $service = new \KpiRapport\Charts\ViewFreeCashFlowService($data, $this->investorId);

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\FreeCashFlowIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

        $portal_chart_formatted_data = [];

        foreach ($data as $data_item) {
			if($is_investor_combination_page && !$data_item[$grouping_value]){
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}

            if (!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
                $portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
                    'current_value' => (float) 0,
                    'costs' => (float) 0,
                    'free_cash_flow' => (float) 0,
					'rental_income' => (float) 0,
                ];
            }

            $portal_chart_formatted_data[$data_item[$grouping_value]]['current_value'] += $data_item['current_value'];
            $portal_chart_formatted_data[$data_item[$grouping_value]]['costs'] += $data_item['costs'];
			$portal_chart_formatted_data[$data_item[$grouping_value]]['free_cash_flow'] += $data_item['free_cash_flow'];
			$portal_chart_formatted_data[$data_item[$grouping_value]]['rental_income'] += $data_item['rental_income'];
        }

        ksort($portal_chart_formatted_data);

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'rental_income' => array_column($data, 'rental_income'),
            'free_cash_flow' => array_column($data, 'free_cash_flow'),
            'free_cash_flow_percentage' => array_column($data, 'free_cash_flow_percentage'),
            'current_value' => array_column($data, 'current_value'),
            'current_value_percentage' => array_column($data, 'current_value_percentage'),
            'costs' => array_column($data, 'costs'),
            'costs_percentage' => array_column($data, 'costs_percentage'),
            'years' => $this->getYearRange(),
            'share_percentages' => array_column($data, 'share_percentage'),
            'data' => array_values($portal_chart_formatted_data)
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function indirectReturnAction()
    {
		$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

        $raw_data_class = new \KpiRapport\Returns\ViewReturnIndirectReturnService(
            new DateTime('01-01-' . $this->year),
            new DateTime('31-12-' . $this->year)
        );

        $raw_data = $raw_data_class->execute(true);
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewIndirectReturnService($data, $this->investorId);

        $all_project_data = $service->execute();

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\IndirectReturnIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

        foreach($data as $data_item){
			if($is_investor_combination_page && !$data_item[$grouping_value]){
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}


			if (!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
				$portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
					'indirect_return_total' => 0,
					'indirect_return' => 0,
					'project_count' => 0,
				];
			}

			$portal_chart_formatted_data[$data_item[$grouping_value]]['indirect_return_total'] += $data_item['indirect_return']/100;
			$portal_chart_formatted_data[$data_item[$grouping_value]]['project_count']++;
			$portal_chart_formatted_data[$data_item[$grouping_value]]['indirect_return'] = $portal_chart_formatted_data[$data_item[$grouping_value]]['indirect_return_total'] / $portal_chart_formatted_data[$data_item[$grouping_value]]['project_count'];

		}

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'indirect_return' => array_column($data, 'indirect_return'),
            'years' => $this->getYearRange(),
            'share_percentages' => array_column($data, 'share_percentage', 'project'),
            'data' => array_values($portal_chart_formatted_data)
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function investValueAction()
    {
		$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

        $raw_data_class = new \KpiRapport\ViewInvestmentValueService(
            new DateTime('01-01-' . $this->year),
            new DateTime('31-12-' . $this->year)
        );

        $raw_data = $raw_data_class->execute(true);
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewInvestValueChartService($data, $this->investorId);

        $all_project_data = $service->execute();

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\InvestValueIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

        $portal_chart_formatted_data = [];

        foreach($data as $data_item){
			if($is_investor_combination_page && !$data_item[$grouping_value]){
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}

            if (!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
                $portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
                    'rental_income' => (float) 0,
                    'invest_value' => (float) 0,
                    'rental_value_factor' => (float) 0
                ];
            }

            $portal_chart_formatted_data[$data_item[$grouping_value]]['rental_income'] += $data_item['rental_income'];
            $portal_chart_formatted_data[$data_item[$grouping_value]]['invest_value'] += $data_item['invest_value'];
            $portal_chart_formatted_data[$data_item[$grouping_value]]['rental_value_factor'] += $data_item['rental_value_factor'];
            $portal_chart_formatted_data[$data_item[$grouping_value]]['project_name'] = $data_item[$grouping_title] . ' (' . $portal_chart_formatted_data[$data_item[$grouping_value]]['rental_value_factor'] . ')';
        }

        ksort($portal_chart_formatted_data);

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'rental_income' => array_column($data, 'rental_income'),
            'rental_value_factor' => array_column($data, 'rental_value_factor'),
            'invest_value' => array_column($data, 'invest_value'),
            'years' => $this->getYearRange(),
            'share_percentages' => array_column($data, 'share_percentage'),
            'data' => array_values($portal_chart_formatted_data),
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    public function obligoAction()
    {
		$this->view->is_investor_combination_page = $is_investor_combination_page = $this->needsInvestorCombinationDetailsPage();

        $raw_data_class = new \KpiRapport\ViewObligoService(
            new DateTime('01-01-' . $this->year),
            new DateTime('31-12-' . $this->year)
        );

        $raw_data = $raw_data_class->execute(true);
        $raw_data_class->setInvestorCombinationId($this->combination_id);
        $data = $raw_data_class->execute(true);

        $service = new \KpiRapport\Charts\ViewObligoChartService($data, $this->investorId);

        $all_project_data = $service->execute();

        if ($this->projectIds) {
            $service->whereProject($this->projectIds);
        }

        $data = $service->execute();

        if ($this->isIncludePreviousYear) {
            $includePreviousYearService = new \KpiRapport\Charts\PreviousYear\ObligoIncludePreviousYearService();
            $data = $includePreviousYearService->includePreviousYear(
                $data,
                $this->year,
                $this->investorId
            );
        }

		$grouping_value = $is_investor_combination_page ? 'investor_combination_id' : 'project';
		$grouping_title = $is_investor_combination_page ? 'investor_combination_name' : 'project_name';

        $portal_chart_formatted_data = [];
        foreach ($data as $data_item) {
			if($is_investor_combination_page && !$data_item[$grouping_value]){
				$data_item[$grouping_value] = 0;
				$data_item[$grouping_title] = 'Volledig eigendom';
			}

            if(!isset($portal_chart_formatted_data[$data_item[$grouping_value]])) {
                $portal_chart_formatted_data[$data_item[$grouping_value]] = [
                    'id' => $data_item[$grouping_value],
					'name' => $data_item[$grouping_title],
                    'current_value' => (float) 0,
                ];
            }

            $portal_chart_formatted_data[$data_item[$grouping_value]]['current_value'] += $data_item['current_value'];
        }

        ksort($portal_chart_formatted_data);

        $this->view->projectIds = $this->projectIds;
        $this->view->combination_id = $this->combination_id;

        $this->view->data = [
            'projects' => array_column($data, 'project_name'),
            'current_value' => array_column($data, 'current_value'),
            'years' => $this->getYearRange(),
            'share_percentages' => array_column($data, 'share_percentage'),
            'data' => array_values($portal_chart_formatted_data),
        ];

        $this->view->selectProjects = $this->makeSelectProjects($all_project_data);
        $this->view->selectCombinations = $this->makeSelectCombinations($raw_data);
    }

    private function getYearRange()
    {
        $nextYear = date('Y') + 1;
        $years = range(2017, $nextYear);
        return array_combine($years, $years);
    }

    private function makeSelectProjects($modifiedData)
    {
        if (!is_array($modifiedData)) {
            return [];
        }

        $previousYear = $this->year - 1;

        if ($this->isIncludePreviousYear) {
            foreach ($modifiedData as $key => $item) {
                $modifiedData[$key]['project_name'] = str_replace([$this->year . ' - ', $previousYear . ' - '], '', $item['project_name']);
            }
        }

        return array_column($modifiedData, 'project_name', 'project');
    }

    private function makeSelectCombinations($modifiedData)
    {
        $combinations = array_filter(
            array_unique(
                array_column(
                    $modifiedData,
                    'investor_combination_name',
                    'investor_combination_id'
                )
            )
        );

        asort($combinations);

        return $combinations;
    }
}
