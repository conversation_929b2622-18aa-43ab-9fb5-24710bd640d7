<?

class SchedulesController extends ActivitiesController {
	
	public function preDispatch() {
		$this->view->Breadcrumbs()->addCrumb("Beschikbaarheidskalender", 'schedules/list');

		$holidays = new Holidays();
		$this->view->holiday_list = $this->holiday_list = $holidays->getList();
	}
	
	public function listAction() {
		$s_model = new Schedules();
		
		
		$this->view->userOpts   = $this->getUserOptions();
		$this->view->userId     = $userId;
		$this->view->schedules = $s_model->fetchAll();
	}

	public function editAction() {
		$this->view->Breadcrumbs()->addCrumb("Bewerken", 'schedules/edit');
		$s_model = new Schedules();
		
		if ($id = $this->_getParam('id')) {
			$schedule = $s_model->fetchRowById($id);
		} else {
			$schedule = $s_model->createRow();
		}
		
		if ($this->getRequest()->isPost()) {
			$intervalType = $_POST['interval-type'];
			
			$daysMask          = new Zend_Db_Expr("NULL");
			$type              = new Zend_Db_Expr("NULL");
			$dayOfMonth        = new Zend_Db_Expr("NULL");
			$dayOfWeek         = new Zend_Db_Expr("NULL");
			$nthWeekDayOfMonth = new Zend_Db_Expr("NULL");
			$month             = new Zend_Db_Expr("NULL");

			$title  = $_POST['title'];
			$description  = $_POST['description'];
			$notification_additional_email = $_POST['notification_additional_email'];
			$userId = $_POST['user'];
			
			switch ($intervalType) {
				case 'weekly':
					$interval = $_POST['interval-weekly'];
					$daysMask = $s_model->getDaysMask($_POST['days']);
					
					break;
				case 'monthly':
					$type = $_POST['pattern-type-monthly'];
					if ($type == 'day-of-month') {
						$dayOfMonth = $_POST['day-of-month-monthly'];
						$interval   = $_POST['interval-monthly-day-of-month'];
						
					} else {
						$nthWeekDayOfMonth = $_POST['day-number-monthly'];
						$dayOfWeek = $_POST['week-day-monthly'];
						$interval  = $_POST['interval-monthly-week-day'];
					}
					break;
				case 'yearly':
					$interval = 1;
					$type = $_POST['pattern-type-yearly'];
					if ($type == 'day-of-month') {
						$month = $_POST['month-day-of-month'];
						$dayOfMonth = $_POST['day-of-month-yearly'];
						
					} else {
						$month = $_POST['month-week-day'];
						$nthWeekDayOfMonth = $_POST['day-number-yearly'];
						$dayOfWeek = $_POST['week-day-yearly'];
					}
					break;
			}
			
			
			$startDate = $this->reverseDate($_POST['start-date']);
			
			$endDate  = new Zend_Db_Expr("NULL");
			$endTimes = new Zend_Db_Expr("NULL");
			$endType = $_POST['end-type'];
			switch ($endType) {
				case 'times':
					$endTimes = $_POST['end-times'];
					break;
				case 'date':
					$endDate = $this->reverseDate($_POST['end-date']);
					break;
			}

			$holidays = array();

			foreach((array) array_keys($_POST['holidays']) as $holiday)
				$holidays[] = $holiday;			
			
			$schedule->title                 = $title;
			$schedule->description           = $description;
			$schedule->notification_additional_email = $notification_additional_email;
			$schedule->user_id               = $userId;
			
			$schedule->interval_type         = $intervalType;
			$schedule->interval              = $interval;
			$schedule->week_days_mask        = $daysMask;
			$schedule->day_of_month          = $dayOfMonth;
			$schedule->day_of_week           = $dayOfWeek;
			$schedule->nth_week_day_of_month = $nthWeekDayOfMonth;
			$schedule->month                 = $month;
			$schedule->start_date            = $startDate;
			$schedule->end_date              = $endDate;
			$schedule->end_times             = $endTimes;

			$schedule->holidays 			 = json_encode($holidays);
			
			if (is_numeric($endTimes))
				$s_model->setEndDate($schedule);
			
			$schedule->save();
			
			$this->_redirect('schedules/list', array('user' => $userId));
		}
		
		$userInfo = Zend_Auth::getInstance()->getIdentity();
		$loggedInUserId = $userInfo->id;
		
		if ($schedule->end_times) {
			$endType = 'times';
		} else if ($schedule->end_date) {
			$endType = 'date';
		} else {
			$endType = 'none';
		}

		$eu = new EmailUser('to', array('id' => loginManager::data()->id));

		$this->view->title             = $schedule['title'];
		$this->view->description       = $schedule['description'];
		$this->view->notification_additional_email = $schedule['notification_additional_email'] ? $schedule['notification_additional_email'] : $eu->params['email'];
		$this->view->userId            = $this->trueVal($schedule->user_id, $loggedInUserId);
		
		$this->view->days              = $s_model->getDayNums($schedule->week_days_mask);
		$this->view->intervalType      = $this->trueVal($schedule->interval_type,  'weekly');
		$this->view->interval          = $this->trueVal($schedule->interval,              1);
		$this->view->patternType       = $schedule->day_of_week ? 'week-day' : 'day-of-month';
		$this->view->dayOfMonth        = $this->trueVal($schedule->day_of_month,          1);
		$this->view->dayOfWeek         = $this->trueVal($schedule->day_of_week,           1);
		$this->view->month             = $this->trueVal($schedule->month,                 1);
		$this->view->nthWeekDayOfMonth = $this->trueVal($schedule->nth_week_day_of_month, 1);
		
		$this->view->startDate         = $this->reverseDate($schedule->start_date);
		$this->view->endType           = $endType;
		$this->view->endDate           = $this->reverseDate($schedule->end_date);
		$this->view->endTimes          = $this->trueVal($schedule->end_times,             1);
		
		$this->view->userOpts          = $this->getUserOptions();
		$this->view->monthOpts         = $this->getMonthOptions();
		$this->view->dayOpts           = $this->getDayOptions();
		$this->view->ordinalOpts       = $this->getOrdinalOptions();

		$this->view->holidays      	   = (array) json_decode($schedule['holidays'], true);
	}

	public function addHolidayAction(){
		$this->view->Breadcrumbs()->addCrumb('Feestdag toevoegen');

		if($_POST){
			$model = new SchedulesHolidays();

			$model->createRow([
				'name' => $_POST['name'],
				'date' => date('Y-m-d', strtotime($_POST['date']))
			])->save();

			$this->redirect('schedules/list');
		}

	}
	
	
	protected function deleteAction() {
		return;
		/*
		$s_model = new Schedules();
		$schedule = $s_model->fetchRowById($this->_getParam('id'));
		$userId = $schedule->user_id;
		
		$schedule->delete();
		
		$this->_redirect('schedules/list', array('user' => $userId));
		*/
	}
}

?>
