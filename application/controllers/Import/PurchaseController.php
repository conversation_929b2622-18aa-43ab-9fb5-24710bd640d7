<?php


class Import_PurchaseController extends GlobalController {

	public function indexAction(){

		$this->view->Breadcrumbs()->addCrumb('Inkoopfacturen import');
		$form = new Form($this, 'import/upload');

		if(count($_FILES) > 0){
			$source_files = [file_get_contents($_FILES['import_file']['tmp_name'])];

			$importer_class_name = 'Import\\Purchase\\Implementations_' . ucfirst(Settings::get('general_company_shortname'));
			if(class_exists($importer_class_name)) {
				$importer = new $importer_class_name($source_files);
				$importer->execute();
			} else
				die('Import is nog niet geimplementeerd. Neem contact op met onze klantenservice.');
		}

		$this->view->form = $form;
	}
}