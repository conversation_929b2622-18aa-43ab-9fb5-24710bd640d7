<?

	class TagController extends GlobalController {

		public function indexAction() {

			$db = Zend_Db_Table::getDefaultAdapter();
			$select = $db->select()
				->from(array('st' => 'support_tags'), array('*'))
				->where('MATCH (st.tag) AGAINST (? IN BOOLEAN MODE)', $this->_getParam('search') . '*');
			$tags = $db->fetchAll($select);

			foreach ($tags as $tag) {
				$response[] = array($tag['id'], $tag['tag'], null, null);
			}

			$this->_helper->json($response);

		}

	}

?>