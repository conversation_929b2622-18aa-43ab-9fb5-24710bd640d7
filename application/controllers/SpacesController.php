<?

	class SpacesController extends GlobalController {

		public function preDispatch(){

			$this->view->Breadcrumbs()->addCrumb('Ruimtes');

			return parent::preDispatch();
		}

		public function listAction() {
			$this->view->Breadcrumbs()->addCrumb('Ruimte lijst');

			$this->view->modelListView('ObjectsSpacesTypes')

				->setTypes(array('name_nl', 'is_default'))

				->addFormat('is_default', 'bool')
				
				->setOptions(array(
					'item_title' => 'Ruimte',
					'disabled_buttons_test' => function($row, $button){
						return false;
					}
				))

				->addButtons(array(
					'add' => 'Toevoegen',
					'edit' => 'Bewerken',
	 				'details' => 'Onderdelen',
					'delete' => 'Verwijderen'
				))

				->render($this);
		}

		public function detailsAction(){
			$ostModel = new ObjectsSpacesTypes();
			$this->view->ostRow = $ostModel->getById($this->_getParam('id'));


			$this->view->Breadcrumbs()
				->addCrumb('Ruimte lijst', 'spaces/list')
				->addCrumb('Onderdelen lijst ruimte - ' . $this->view->ostRow->name_nl, 'spaces/details/id/' . $this->_getParam('id') . '/');
		}

		public function editAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Ruimte lijst', 'spaces/list')
				->addCrumb('Ruimte bewerken');

			$this->view->form = $this->view->EditView($this, 'ObjectsSpacesTypes')
				->setOptions([])
				->render($this);			
		}

		public function deleteAction(){
			$this->disableView();

			$ostModel = new ObjectsSpacesTypes();
			$ostRow = $ostModel->getById($this->_getParam('id'));
			
			$ostRow->delete();
			
			$this->_redirect('spaces/list');
		}
	}