<?php

class AclController extends GlobalController
{
    public function preDispatch()
    {
        if (!isAllowed('rights:edit')) {
            die('Geen toegang.');
        }
    }

    public function indexAction()
    {
        if (!$this->getRequest()->isPost()) {
            return;
        }

        $data = $this->_getAllParams();
        $acl = acl();

        $resource = $data['resource'];
        $privilege = $data['privilege'];

        list($privilegeType, $privilegeName) = $this->split($privilege);
        list($resourceType, $resourceName) = $this->split($resource);

        $acl->clear();
        $acl->extendRights($resourceType, $resourceName);
        $rights = $acl->getRights($resourceType, $resourceName, $privilegeType, $privilegeName);

        $this->view->roles = $rights['roles'];
        $this->view->resources = $rights['resources'];
        $this->view->resource = [
            'type' => $resourceType,
            'resource' => $resourceName
        ];
        $this->view->access = [
            'type' => $privilegeType,
            'privilege' => $privilegeName
        ];
    }

    public function listAction()
    {
        $ignore = ['.', '..'];

        $controllers = [];
        if ($handle = opendir('application/controllers')) {
            while (false !== ($file = readdir($handle))) {
                if (!in_array($file, $ignore, false) && end(explode('.', $file)) === 'php') {
                    $controllers[] = $file;
                }
            }
            closedir($handle);
        }

        asort($controllers);
        array_merge($controllers);

        $actions = [];
        foreach ($controllers as $controller) {
            $controllerName = strtolower(substr($controller, 0, -14));

            $filePath = 'application/controllers/' . $controller;
            @$content = file_get_contents($filePath);

            preg_match_all('/\/\*(?P<comment>.*)\*\/.*function(?P<action>.*)Action.?\(/s', $content, $block);

            $i = 0;
            foreach ($block['comment'] as $comment) {
                preg_match_all('/@ACL:?.*?(?P<description>.*)/', $comment, $matches);
                if ($matches['description']) {
                    foreach ($matches['description'] as $description) {
                        $filter = new Zend_Filter_Word_CamelCaseToDash();
                        $action = strtolower(trim($block['action'][$i]));

                        $actions[$controllerName]['actions'][] = [
                            'name' => $action,
                            'description' => trim($description)
                        ];
                        $i++;
                    }
                }
            }
        }

        $this->view->controllers = $actions;
    }


    public function overviewAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Overzicht rechten');
        $this->view->extraWidth = true;

        $aclOverviewData = (new \Controllers\Acl\Overview())->execute();

        $this->view->ListView($aclOverviewData)
            ->setTypes([
                'resource_name' => ['title' => 'Resource', 'width' => 'xlarge truncate'],
                'privilege' => ['title' => 'Actie', 'width' => 'medium'],
                'role_name' => ['title' => 'Rechten type', 'width' => 'large truncate'],

            ])
            ->setFilters([
                'resource_name' => [
                    'title' => 'Resource',
                    'renderSeparately' => true,
                    'order_by_value' => 'ASC',
                ]
            ])
            ->render($this);
    }


    public function allowallAction()
    {
        $this->disableView();

        $ignore = ['.', '..'];

        $controllers = [];
        if ($handle = opendir('application/controllers')) {
            while (false !== ($file = readdir($handle))) {
                if (!in_array($file, $ignore, false) && end(explode('.', $file)) === 'php') {
                    $controllers[] = $file;
                }
            }
            closedir($handle);
        }

        asort($controllers);
        array_merge($controllers);

        $actions = [];
        foreach ($controllers as $controller) {
            $controllerName = strtolower(substr($controller, 0, -14));

            $filePath = 'application/controllers/' . $controller;
            @$content = file_get_contents($filePath);

            preg_match_all('/public function(?P<action>.*)Action/', $content, $block);
            $i = 0;
            foreach ($block['action'] as $action) {
                if (!strpos($action, '*')) {
                    $filter = new Zend_Filter_Word_CamelCaseToDash();
                    $action = strtolower($filter->filter($action));
                    $actions[$controllerName]['actions'][] = [
                        'name' => trim($action)
                    ];
                    $i++;
                }
            }
        }

        foreach ($actions as $controller => $action) {
            $c = 'controller:' . $controller;
            foreach ($action['actions'] as $ac) {
                $a = 'action:' . $ac['name'];
                #acl()->allow('internal', $c, array($a), 1);
            }
        }

        $this->view->controllers = $actions;
    }

    public function saveAction()
    {
        $this->disableView();

        if (!$this->getRequest()->isPost()) {
            return;
        }

        $postData = $this->getRequest()->getPost();
        $data = $postData['data'];
        $rows = $postData['rows'];
        $acl = acl();

        $resource = $data['resource'];
        $privilege = $data['privilege'];

        list($privilegeType, $privilegeName) = $this->split($privilege);
        list($resourceType, $resourceName) = $this->split($resource);

        $acl->removeResource($privilegeName, $privilegeType, $resourceName, $resourceType);
        if (count($rows) > 0) {
            foreach ($rows as $row) {
                $allow = $row['write'] === '1';
                $acl->allow($row['role'], $resource, array($privilege), $allow);
            }
        }

        $this->_forward('index', null, null, $data);
    }

    public function initAction()
    {
        acl()->initDb();
    }

    private function split($resource)
    {
        return explode(':', $resource);
    }
}
