<?php


use Financial\Application\Contact\GetAllFinancialContactErrorLogService;

class Financial_ContactErrorLogController extends GlobalController
{
    public function preDispatch()
    {
        parent::preDispatch();
        $this->renderWithNewLayout();
    }

    /**
     * @Inject
     * @var GetAllFinancialContactErrorLogService
     */
    private $getAllFinancialContactErrorLogService;

    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Financieel');
        $this->view->Breadcrumbs()->addCrumb('Contact error log');

        $data = $this->getAllFinancialContactErrorLogService->execute();

        $types = [
            'error_id' => ['width' => 'hidden'],
            'user_rendered_name' => ['title' => 'Contact', 'width' => 'large'],
            'financial_system_name' => ['title' => 'Financieel systeem', 'width' => 'medium'],
            'administration' => ['title' => 'Administratie', 'width' => 'medium'],
            'financial_contact_identifier' => ['title' => 'Debiteur nummer', 'width' => 'medium'],
//            'financial_contact_created_at' => ['title' => 'Created_at', 'width' => 'xxxlarge'],
            'error_message' => ['title' => 'Error', 'width' => 'xxxlarge truncate'],
            'error_created_at' => ['title' => 'Error datum', 'width' => 'medium']
        ];
        $filters = [];

        $data = array_reverse($data);
        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->render($this);
    }

}
