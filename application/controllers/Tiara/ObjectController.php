<?php

use DbTable\TiaraFormState;
use DbTable\TiaraQueue;
use TiaraWonen\Application\Service\Object\CreateTiaraObjectService;
use TiaraWonen\Application\Service\Object\HaveObjectAProjectService;
use TiaraWonen\Application\Service\Object\LoadTiaraObjectObjectService;
use TiaraWonen\Application\Service\Object\LoadTiaraProjectIdByOmniboxxObjectService;
//use TiaraWonen\Application\Service\Object\PrePopulateObjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Object\PrePopulateCreateObjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Object\PrePopulateUpdateObjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Object\RemoveTiaraEntityObjectService;
use TiaraWonen\Application\Service\Object\SufPopulateCreateObjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Object\UpdateTiaraObjectService;
use TiaraWonen\Application\Service\Object\ViewObjectListService;
use TiaraWonen\Application\Service\SaveTiaraFormStateService;
use TiaraWonen\Model\Wonen\TiaraValidationException;
use TiaraWonen\Model\Wonen\Validatie\ValidationException;
use TiaraWonen\Model\Wonen\WoonObjecten\WijzigWoonObject;
//use TiaraWonen\Render\SetFormDataService;
use TiaraWonen\ViewFormService;
use TiaraWonen\Infrastructure\Service\TiaraXmlErrorException;
use TiaraWonen\Model\Wonen\WoonObjecten\AfmeldenWoonObject;
use TiaraWonen\Model\Wonen\WoonObjecten\WoonObject;
use TiaraWonen\NamespaceIterator;
use TiaraWonen\Render\RenderRootPanelService;
use TiaraWonen\TiaraViewExtractJob;
//use application\models\TiaraWonen\Application\Service\LoadFormStateDataService;
use application\models\TiaraWonen\Application\Service\Object\LoadFormStateDataObjectService;
use application\models\TiaraWonen\Application\Service\Object\ManipulateDataArrayService;
use TiaraWonen\Model\Wonen\Validatie\Validator;

class Tiara_ObjectController extends \GlobalController
{


    public function listAction()
    {
        $id = null;
        $mapTo = null;


        if(is_null($this->getParam('sales_status')) && !$this->isAjaxRequest){
            $this->setParam('sales_status', 'Closed');
        }

        if ($this->getParam('object_id')) {
            $id = $this->getParam('object_id');
            $mapTo = ViewObjectListService::MAP_TO_OBJECT;
        }

        if ($this->getParam('project_id')) {
            $id = $this->getParam('project_id');
            $mapTo = ViewObjectListService::MAP_TO_PROJECT;
        }

        if ($this->getParam('object_group_id')) {
            $id = $this->getParam('object_group_id');
            $mapTo = ViewObjectListService::MAP_TO_OBJECT_GROUP;
        }

        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb('Objecten');

        $data = (new ViewObjectListService())->execute($mapTo, $id);

        $types = [
            'rendered_address' => ['title' => 'Adres', 'width' => 'xxxxlarge'],
            'state' => ['title' => 'Status', 'width' => 'small'],
            'created_at' => ['title' => 'Aangepast', 'width' => 'large'],
            'error_count' => ['title' => 'Fouten', 'width' => 'medium'],
            'actions' => ['title' => 'Acties', 'width' => 'xxxxlarge'],
            'externalTiaraId' => ['title' => 'TiaraId', 'width' => 'medium'],
        ];

        $filters = [
            'rendered_address' => ['type' => 'input']
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('rendered_address', function ($value, $row) {
                return "<a href='object/edit-specifications/id/{$row['id']}'>$value</a>";
            })

            ->addFormat('externalTiaraId', function ($value, $row) {
                return $row['object_id'] ? : $value ;
            })

            ->addFormat('actions', function ($value, $row) {
                $incompatibleState = in_array($row['state'], [
                        TiaraQueue::STATE_PROCESSING,
                        TiaraQueue::STATE_QUEUED,
                        TiaraQueue::STATE_EXCEEDED_MAX_ATTEMPT_POLICY
                    ]
                );

                if ($incompatibleState) {
                    return '';
                }

                switch ($row['object_type_definition_tiara_type']) {
                    case 'appartment':
                        $addButton = "<a href='tiara_object/create-apartment/object_id/{$row['id']}'>
                            Aanmelden [Appartement] <i class='fa fa-plus-circle'></i>
                        </a>";

                        $updateButton = "<a href='tiara_object/update-apartment/object_id/{$row['id']}'>
                            Wijzigen [Appartement] <i class='fa fa-edit'></i>
                        </a>";
                        break;
                    case 'woonobject':
                        $addButton = "<a href='tiara_object/create-residential-building/object_id/{$row['id']}'>
                            Aanmelden [Woning] <i class='fa fa-plus-circle'></i>
                        </a>";

                        $updateButton = "<a href='tiara_object/update-residential-building/object_id/{$row['id']}'>
                            Wijzigen [Woning] <i class='fa fa-edit'></i>
                        </a>";
                        break;
                    default:
                        if ($row['object_type_name']) {
                            $addButton = "<a href='/object-type/list/' target='_blank'>
                                Tiara type is niet ingesteld <i class='fa fa-house-damage'></i>
                            </a>";
                        } else {
                            $addButton = "<a href='object/edit/id/{$row['id']}' target='_blank'>
                                Het object type moet ingesteld zijn <i class='fa fa-house-damage'></i>
                                <i class='fa fa-info-circle fa-lg test' aria-hidden='true' title='ga naar het object en kies onder het kopje \"objectdetails\" het juiste \"object type\"' rel='test'></i>
                            </a>";
                        }
                }




                $removeButton = "<a href='tiara_object/remove/object_id/{$row['id']}'>
                            Afmelden <i class='fa fa-ban'></i>
                        </a>";

                $addOpenhuizendagButton = "<a href='tiara_openhuizendagen/add/tiara_entity_id/{$row['entity_id']}' target='_blank'>
                            Openhuizendag aanmelden <i class='fa fa-plus-circle'></i>
                        </a>";

                switch ($row['tiara_entity_state']) {
                    case null:
                    case '':
                    case 'new':
                    case 'removed':
                        $html = $addButton;
                        break;
                    case 'added':
                    case 'mutated':
                        $html = $updateButton . $removeButton . $addOpenhuizendagButton;
                        break;
                    default:
                        $html = null;
                }

                return $html;
            })

//            ->addFormat('state', function ($value) {
//                if ($value) {
//                    return $value === TiaraQueue::STATE_PROCESSED ? '' : 'ja';
//                }
//            })

            ->addFormat('error_count', function ($value, $row) {
                $errorButton = "<a href='tiara_error/list/type/object/id/{$row['id']}'>
                           $value fout(en) <i class='fa fa-exclamation-triangle'></i>
                        </a>";

                return $value ? $errorButton : '0';
            })

            ->addLegend('tiara-object-legend')

            ->setFilters(array(
                'project_name' => array('renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'),
                'error_count' => array('renderSeparately' => true, 'title' => 'Fouten', 'order_by_title' => 'DESC'),
                'status' => array('renderSeparately' => true, 'title' => 'Acties', 'order_by_title' => 'ASC'),
                'rendered_address' => array('type' => 'input' , 'title' => 'Adres'),
                'externalTiaraId' => array('type' => 'select' , 'title' => 'Tiara ID','order_by_title' => 'DESC'),



            ))

            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function listArchivedAction()
    {
        $id = null;
        $mapTo = null;


        if(is_null($this->getParam('sales_status')) && !$this->isAjaxRequest){
            $salestatus = $this->setParam('sales_status', 'Closed');
        }


        if ($this->getParam('object_id')) {
            $id = $this->getParam('object_id');
            $mapTo = ViewObjectListService::MAP_TO_OBJECT;
        }

        if ($this->getParam('project_id')) {
            $id = $this->getParam('project_id');
            $mapTo = ViewObjectListService::MAP_TO_PROJECT;
        }

        if ($this->getParam('object_group_id')) {
            $id = $this->getParam('object_group_id');
            $mapTo = ViewObjectListService::MAP_TO_OBJECT_GROUP;
        }

        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb('Objecten');


        $data = (new ViewObjectListService())->execute($mapTo, $id, 'archived');

        $types = [
            'rendered_address' => ['title' => 'Adres', 'width' => 'xxxxlarge'],
            'state' => ['title' => 'Status', 'width' => 'small'],
            'created_at' => ['title' => 'Aangepast', 'width' => 'large'],
            'error_count' => ['title' => 'Fouten', 'width' => 'medium'],
            'actions' => ['title' => 'Acties', 'width' => 'xxxxlarge'],
            'externalTiaraId' => ['title' => 'TiaraId', 'width' => 'medium'],
        ];

        $filters = [
            'rendered_address' => ['type' => 'input']
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('rendered_address', function ($value, $row) {
                return "<a href='object/edit-specifications/id/{$row['id']}'>$value</a>";
            })



            ->addFormat('externalTiaraId', function ($value, $row) {
                return $row['object_id'] ? : $value ;
            })

            ->addFormat('actions', function ($value, $row) {
                $incompatibleState = in_array($row['state'], [
                        TiaraQueue::STATE_PROCESSING,
                        TiaraQueue::STATE_QUEUED,
                        TiaraQueue::STATE_EXCEEDED_MAX_ATTEMPT_POLICY
                    ]
                );

                if ($incompatibleState) {
                    return '';
                }



                $removeButton = "<a href='tiara_object/remove/object_id/{$row['id']}'>
                            Afmelden <i class='fa fa-ban'></i>
                        </a>";

                switch ($row['tiara_entity_state']) {
                    case 'added':
                    case 'mutated':
                        $html =  $removeButton ;
                        break;
                    default:
                        $html = null;
                }

                return $html;
            })

//            ->addFormat('state', function ($value) {
//                if ($value) {
//                    return $value === TiaraQueue::STATE_PROCESSED ? '' : 'ja';
//                }
//            })

            ->addFormat('error_count', function ($value, $row) {
                $errorButton = "<a href='tiara_error/list/type/object/id/{$row['id']}'>
                           $value fout(en) <i class='fa fa-exclamation-triangle'></i>
                        </a>";

                return $value ? $errorButton : '0';
            })

            ->addLegend('tiara-object-legend')

            ->setFilters(array(
                'project_name' => array('renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'),
                'error_count' => array('renderSeparately' => true, 'title' => 'Fouten', 'order_by_title' => 'DESC'),
                'actions' => array('renderSeparately' => true, 'title' => 'Acties', 'order_by_title' => 'DESC'),
                'rendered_address' => array('type' => 'input' , 'title' => 'Adres'),
                'externalTiaraId' => array('type' => 'select' , 'title' => 'Tiara ID','order_by_title' => 'DESC'),



            ))

            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    private function viewFormResourceNew()
    {
        $this->view->headLink()
            ->appendStylesheet('media/style/datepicker.css')
            ->appendStylesheet('media/style/tiara_form/menu_snake.css')
        ;

        $this->view->headScriptHashed()

            ->appendFile('media/javascript/tiara_form/validation/lib/dispatcher.js')
            ->appendFile('media/javascript/tiara_form/validation/lib/dispatcher_event.js')
            ->appendFile('media/javascript/tiara_form/validation/lib/element_helper.js')

            ->appendFile('media/javascript/tiara_form/validation/fields/field.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/checkbox_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/group_checkbox_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/date_picker_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/input_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/radio_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/select_element.js')
            ->appendFile('media/javascript/tiara_form/validation/fields/textarea_element.js')

            ->appendFile('media/javascript/tiara_form/validation/form_element.js')
//            ->appendFile('media/javascript/tiara_form/validation/input.js')
            ->appendFile('media/javascript/tiara_form/validation/validation.js')

            ->appendFile('media/javascript/tiara_form/menu_snake.js')
        ;
    }

    private function viewFormResource()
    {
//        $this->appendJqueryFiles($this->view);

        $this->view->headLink()
            ->appendStylesheet('media/style/tiara_form/style.css')
            ->appendStylesheet('media/style/datepicker.css')
            ->appendStylesheet('media/style/tags/TextboxList.css')
            ->appendStylesheet('media/style/tags/TextboxList.Autocomplete.css');

        $this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');

        $this->view->headScriptHashed()
            ->appendFile('media/javascript/tiara_form/woonObject_medeAanmelders.js')
            ->appendFile('media/javascript/tiara_form/class_helper.js')
            ->appendFile('media/javascript/tiara_form/element_helper.js')
            ->appendFile('media/javascript/tiara_form/loader.js')
            ->appendFile('media/javascript/tiara_form/elements/button.js')
            ->appendFile('media/javascript/tiara_form/elements/ul.js')
            ->appendFile('media/javascript/tiara_form/elements/span.js')
            ->appendFile('media/javascript/tiara_form/elements/li.js')
            ->appendFile('media/javascript/tiara_form/elements/form/fieldset.js')
            ->appendFile('media/javascript/tiara_form/elements/form/field_row.js')
            ->appendFile('media/javascript/tiara_form/elements/form/field.js')
//            ->appendFile('media/javascript/tiara_form/elements/form/validation/field_validation.js')
//            ->appendFile('media/javascript/tiara_form/elements/form/validation/required_validation.js')
            ->appendFile('media/javascript/tiara_form/elements/form/input.js')
            ->appendFile('media/javascript/tiara_form/elements/form/select.js')
            ->appendFile('media/javascript/tiara_form/elements/form/textarea.js')
            ->appendFile('media/javascript/tiara_form/elements/form/checkbox.js')
            ->appendFile('media/javascript/tiara_form/elements/form/submit.js')
            ->appendFile('media/javascript/tiara_form/elements/form/group_checkbox.js')
            ->appendFile('media/javascript/tiara_form/elements/form/radio.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/toggle.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/label.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_form.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_multi.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_panel.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_switch_option.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_tab.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_item.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_item_group.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_list.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_to.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/tree_nav_container.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/loader.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/item.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/wrapper.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/list.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/list/item.js')
            ->appendFile('media/javascript/tiara_form/dispatcher.js')
            ->appendFile('media/javascript/tiara_form/dispatcher_event.js')
            ->appendFile('media/javascript/tiara_form/form/form_container.js')
            ->appendFile('media/javascript/tiara_form/main_container.js')
            ->appendFile('media/javascript/tiara_form/main_container_loader.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_container.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option_container.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option_select.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_container.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_menu_item.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_panel.js')
            ->appendFile('media/javascript/tiara_form/multi/link_item.js')
            ->appendFile('media/javascript/tiara_form/multi/list.js')
            ->appendFile('media/javascript/tiara_form/multi/list_container.js')
            ->appendFile('media/javascript/tiara_form/multi/list_item.js')
            ->appendFile('media/javascript/tiara_form/multi/multi_container_list.js')
            ->appendFile('media/javascript/tiara_form/multi/multi_container.js')
            ->appendFile('media/javascript/tiara_form/panel_container.js')
            ->appendFile('media/javascript/tiara_form/go_to_tiara_form.js');



    }

    public function validateFormSegmentAction()
    {
        $this->disableView();

        $rawJsonData = file_get_contents('php://input');
        $postData = json_decode($rawJsonData, true);

        $namespaceScope = $postData['identification'];

        $zendForm = new \Form($this, '');
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->setDefaultTranslator($zendForm->formErrorTranslation()->getAdapter());

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());

        (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm);

        $formData = $postData['data'];

        $result = (object)[
            'succes' => false,
            'error' => false,
            'errors' => []
        ];

        if (!$zendForm->isValid($formData)) {
            $result->error = true;
            $result->errors = json_decode($zendForm->processAjax($formData));
        }

        $result->succes = true;

        echo json_encode($result);
    }


    private function getObjectTitleById($objectId)
    {
        $objectRow = (new Objects())->getById($objectId);

        if (!$objectRow) {
            return 'Object niet gevonden';
        }

        return $objectRow->rendered_address;
    }

    public function createDemoAction()
    {
        $this->renderWithNewLayout();
    }

    public function createApartmentAction()
    {
        $this->renderWithNewLayout();

        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object aanbieden', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb('Appartement: <i>' .$this->getObjectTitleById($objectId).'</i>');

        $tiaraViewState = ['production'];

        $namespaceScope = WoonObject::class;

        $postData = $this->getRequest()->getPost();

        $defaultData = (new PrePopulateCreateObjectTiaraFormPostDataService($objectId))->execute();

        if ((new HaveObjectAProjectService($objectId))->execute()) {
            $tiaraProjectID = (new LoadTiaraProjectIdByOmniboxxObjectService($objectId))->execute();
            if ($tiaraProjectID) {
                $defaultData['WoonObjectProjectInformatie']['ProjectID'] = $tiaraProjectID;
                $tiaraViewState[] = 'project';
            }
        }

        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        $formData = $defaultData;
//        $formData = $this->array_merge_recursive($backupPostData, $defaultData);

        if ($backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst']) {
            $formData['WoonObjectObjectDetails']['Aanbiedingstekst'] = $backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst'];
            $formData['WoonObjectObjectDetails']['AanbiedingstekstEngels'] = $backupPostData['WoonObjectObjectDetails']['AanbiedingstekstEngels'];
        }

        $sufData = (new SufPopulateCreateObjectTiaraFormPostDataService())->execute($objectId, $formData);
        $formData = $this->array_merge_recursive($formData, $sufData);

        $formData = $this->array_merge_recursive($formData, $postData);


        if ($postData) {
            $postData['WoonObjectSwitch'] = 'WoonObjectWonen';
            $postData['WoonObjectObjectDetailsAdresSwitch'] = 'WoonObjectObjectDetailsAdresNederlandsAdres';
            $postData['WoonObjectObjectDetailsCombinatieObjectSwitch'] = 'WoonObjectObjectDetailsCombinatieObjectBogObject';
            $postData['WoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenSwitch'] = 'WoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenWoonhuis';

            $postData['WoonObjectWonenSwitch'] = 'WoonObjectWonenAppartement';

            $postData = $this->array_merge_recursive($defaultData, $postData);

            $this->savePost($postData);
            try {
                (new CreateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }

        $this->viewFormResourceNew();
        $this->view->assign('formData', new formData($formData));
        $this->view->assign('objectId', $objectId);

    }

    public function createResidentialBuildingAction()
    {
        $this->renderWithNewLayout();

        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object aanbieden', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb('Woonhuis: <i>' .$this->getObjectTitleById($objectId).'</i>');

        $tiaraViewState = ['production'];

        $namespaceScope = WoonObject::class;



        $postData = $this->getRequest()->getPost();

        $defaultData = (new PrePopulateCreateObjectTiaraFormPostDataService($objectId))->execute();

        if ((new HaveObjectAProjectService($objectId))->execute()) {
            $tiaraProjectID = (new LoadTiaraProjectIdByOmniboxxObjectService($objectId))->execute();
            if ($tiaraProjectID) {
                $defaultData['WoonObjectProjectInformatie']['ProjectID'] = $tiaraProjectID;
                $tiaraViewState[] = 'project';
            }
        }

        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        $formData = $defaultData;

        if ($backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst']) {
            $formData['WoonObjectObjectDetails']['Aanbiedingstekst'] = $backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst'];
            $formData['WoonObjectObjectDetails']['AanbiedingstekstEngels'] = $backupPostData['WoonObjectObjectDetails']['AanbiedingstekstEngels'];
        }

        $sufData = (new SufPopulateCreateObjectTiaraFormPostDataService())->execute($objectId, $formData);
        $formData = $this->array_merge_recursive($formData, $sufData);

        $formData = $this->array_merge_recursive($formData, $postData);



        if ($postData) {

            $postData['WoonObjectSwitch'] = 'WoonObjectWonen';
            $postData['WoonObjectObjectDetailsAdresSwitch'] = 'WoonObjectObjectDetailsAdresNederlandsAdres';
            $postData['WoonObjectObjectDetailsCombinatieObjectSwitch'] = 'WoonObjectObjectDetailsCombinatieObjectBogObject';
            $postData['WoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenSwitch'] = 'WoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenWoonhuis';

            $postData['WoonObjectWonenSwitch'] = 'WoonObjectWonenWoonhuis';

            $postData = $this->array_merge_recursive($defaultData, $postData);

            $this->savePost($postData);
            try {
                (new CreateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }

//        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
//        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
//        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, $tiaraViewState);
//
//        if ($isTiaraObjectLoadFromDataBase) {
//            $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);
//        }

        $this->viewFormResourceNew();
        $this->view->assign('formData', new formData($formData));
        $this->view->assign('objectId', $objectId);


    }

    public function createAction()
    {
        $this->renderWithNewLayout();

        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object aanbieden', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb($this->getObjectTitleById($objectId));

        $tiaraViewState = ['production'];

        $namespaceScope = WoonObject::class;

        $tiaraObject = (new LoadTiaraObjectObjectService)->execute($namespaceScope, $objectId);

        $isTiaraObjectLoadFromDataBase = false;
        if ($tiaraObject) {
            $isTiaraObjectLoadFromDataBase = true;
        } else {
            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#1 Aanmelding van een Woonhuis.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#2 Aanmelding van een Appartement.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#3 Aanmelding van een Bouwgrond.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#4 Aanmelding van een Garagebox.xml';

//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#13 Aanmelding van een Woonhuis direct verkocht.xml';

//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#14 Aanmelding van een object Woonhuis dat van een derde is aangekocht of gehuurd.xml';

//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#16d Aanmelding bouwgrond.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#16e Aanmelding bouwgrond.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#16f Aanmelding bouwgrond.xml';
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#16g Aanmelding bouwgrond.xml';

//            $xmlString = file_get_contents($filepath);
//            $serializer = (new \TiaraWonen\Serializer\TiaraObjectSerializerFactory())->build();
//            $tiaraObject = $serializer->deserialize($xmlString, $namespaceScope, 'xml');

//            if (!$this->getRequest()->getPost()) {
//                $tiaraObject->postValidate();
//            }
//
//            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
//                $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
//                $this->view->assign('hasErrors', true);
//                $this->view->assign('errorMessage', $errorMessage);
//            }
        }

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'aanmaken', [
            'label' => 'Aanbieden voor publicatie'
        ]);

        $postData = $this->getRequest()->getPost();

        $defaultData = (new PrePopulateCreateObjectTiaraFormPostDataService($objectId))->execute();

        if ((new HaveObjectAProjectService($objectId))->execute()) {
            $tiaraProjectID = (new LoadTiaraProjectIdByOmniboxxObjectService($objectId))->execute();
            if ($tiaraProjectID) {
                $defaultData['WoonObjectProjectInformatie']['ProjectID'] = $tiaraProjectID;
                $tiaraViewState[] = 'project';
            }
        }

        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        $data = $this->array_merge_recursive($defaultData, $backupPostData);

        $sufData = (new SufPopulateCreateObjectTiaraFormPostDataService())->execute($objectId, $data);
        $data = $this->array_merge_recursive($data, $sufData);

        $data = $this->array_merge_recursive($data, $postData);

        $data = (new ManipulateDataArrayService())->execute($data);

        if ($postData) {
            $this->savePost();
            try {
                (new CreateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, $tiaraViewState);

        if ($isTiaraObjectLoadFromDataBase) {
            $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);
        }

//        list($renderedHtml, $jsScript) = (new ViewFormService(
//            $NamespaceIterator,
//            new RenderRootPanelService()
//        ))->execute($zendForm, $data);

        $this->viewFormResource();
        $this->view->assign('formData', new formData($data));



//        $this->view->assign('renderedHtml', $renderedHtml);
//        $this->view->assign('jsScript', $jsScript);


    }

    private function savePost($postData = false)
    {
        if (!$postData) {
            $postData = $this->getRequest()->getPost();
        }

        $objectId = $this->getParam('object_id');

        $omniboxxEntityType = TiaraFormState::OMNIBOXX_ENTITY_TYPE_OBJECT;

        try {
            (new SaveTiaraFormStateService())->execute($objectId, $omniboxxEntityType, $postData);
        } catch (Exception $exception) {
            error_log($exception->getMessage());
//            die($exception->getMessage());
        }
    }


    public function updateApartmentAction()
    {
//        Validator::markAsEditMode();

        $this->renderWithNewLayout();
        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object publicatie wijzigen', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb($this->getObjectTitleById($objectId));


        $namespaceScope = WijzigWoonObject::class;

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'wijzigen', [
            'label' => 'Publicatie wijzigen'
        ]);

        $tiaraObject = (new LoadTiaraObjectObjectService)->execute($namespaceScope, $objectId);


        $defaultData = (new PrePopulateUpdateObjectTiaraFormPostDataService($objectId))->execute();

        $postData = $this->getRequest()->getPost();
        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        //fix for update
        unset($backupPostData['WoonObject']['ObjectID']);

        //fix array key for update
        if (key_exists('WoonObject', $backupPostData)) {
            //convert from  WoonObject to WijzigWoonObject as exampel

            foreach ($backupPostData as $formName => $formValues) {
                if (array_key_exists('Wijzig' . $formName, $backupPostData)) {
                    continue;
                }
                if (is_array($formValues)) {
                    foreach ($formValues as $inputName => $inputValue) {
                        $backupPostData['Wijzig' . $formName][$inputName] = $inputValue;
                    }
                } else {
                    if (strpos($formName, 'Switch')) {
                        $formValues = 'Wijzig' .$formValues;
                    }
                    $backupPostData['Wijzig' . $formName] = $formValues;
                }

            }
            $backupPostData['WijzigWoonObjectObjectDetailsStatusVerhuurdSwitch'] = 'WijzigWoonObjectObjectDetailsStatusVerhuurdVolledigVerhuurd';
        }

        $backupPostData['WijzigWoonObjectObjectDetails']['DatumAanvaarding'] =  date('Y-m-d', strtotime($backupPostData['WijzigWoonObjectObjectDetails']['DatumAanvaarding']));

        $formData = $this->array_merge_recursive($backupPostData, $defaultData);

        if ($backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst']) {
            $formData['WijzigWoonObjectObjectDetails']['Aanbiedingstekst'] = $backupPostData['WijzigWoonObjectObjectDetails']['Aanbiedingstekst'];
            $formData['WijzigWoonObjectObjectDetails']['AanbiedingstekstEngels'] = $backupPostData['WijzigWoonObjectObjectDetails']['AanbiedingstekstEngels'];
        }

        if ($backupPostData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel']) {
            $formData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'] = $backupPostData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'];
        }


//        $formData = $this->array_merge_recursive($formData, $postData);

//        $formData = (new ManipulateDataArrayService())->execute($formData);

        if ($postData) {
//            $postData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'] = $postData['WijzigWoonObjectObjectDetailsHuur']['Huurprijs'] > 0 ? 'huurprijs' : 'prijs op aanvraag';
            $postData['WijzigWoonObjectSwitch'] = 'WijzigWoonObjectWonen';
            $postData['WijzigWoonObjectObjectDetailsAdresSwitch'] = 'WijzigWoonObjectObjectDetailsAdresNederlandsAdres';
            $postData['WijzigWoonObjectObjectDetailsCombinatieObjectSwitch'] = 'WijzigWoonObjectObjectDetailsCombinatieObjectBogObject';
            $postData['WijzigWoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenSwitch'] = 'WijzigWoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenWoonhuis';

            $postData['WijzigWoonObjectWonenSwitch'] = 'WijzigWoonObjectWonenAppartement';

            $defaultData = $this->array_merge_recursive($backupPostData, $defaultData);
            $postData = $this->array_merge_recursive($defaultData, $postData);

            $this->savePost($postData);
            try {
                (new UpdateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }


        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

//        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);

        $this->viewFormResourceNew();
        $this->view->assign('formData', new formData($formData));
        $this->view->assign('objectId', $objectId);

    }


    public function updateResidentialBuildingAction()
    {
//        Validator::markAsEditMode();

        $this->renderWithNewLayout();
        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object publicatie wijzigen', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb($this->getObjectTitleById($objectId));


        $namespaceScope = WijzigWoonObject::class;

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'wijzigen', [
            'label' => 'Publicatie wijzigen'
        ]);

        $tiaraObject = (new LoadTiaraObjectObjectService)->execute($namespaceScope, $objectId);


        $defaultData = (new PrePopulateUpdateObjectTiaraFormPostDataService($objectId))->execute();

        $postData = $this->getRequest()->getPost();
        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        //fix for update
        unset($backupPostData['WoonObject']['ObjectID']);

        //fix array key for update
        if (key_exists('WoonObject', $backupPostData)) {
            //convert from  WoonObject to WijzigWoonObject as exampel

            foreach ($backupPostData as $formName => $formValues) {
                if (array_key_exists('Wijzig' . $formName, $backupPostData)) {
                    continue;
                }
                if (is_array($formValues)) {
                    foreach ($formValues as $inputName => $inputValue) {
                        $backupPostData['Wijzig' . $formName][$inputName] = $inputValue;
                    }
                } else {
                    if (strpos($formName, 'Switch')) {
                        $formValues = 'Wijzig' .$formValues;
                    }
                    $backupPostData['Wijzig' . $formName] = $formValues;
                }

            }
            $backupPostData['WijzigWoonObjectObjectDetailsStatusVerhuurdSwitch'] = 'WijzigWoonObjectObjectDetailsStatusVerhuurdVolledigVerhuurd';
        }

        $backupPostData['WijzigWoonObjectObjectDetails']['DatumAanvaarding'] =  date('Y-m-d', strtotime($backupPostData['WijzigWoonObjectObjectDetails']['DatumAanvaarding']));

        $formData = $this->array_merge_recursive($backupPostData, $defaultData);

        if ($backupPostData['WoonObjectObjectDetails']['Aanbiedingstekst']) {
            $formData['WijzigWoonObjectObjectDetails']['Aanbiedingstekst'] = $backupPostData['WijzigWoonObjectObjectDetails']['Aanbiedingstekst'];
            $formData['WijzigWoonObjectObjectDetails']['AanbiedingstekstEngels'] = $backupPostData['WijzigWoonObjectObjectDetails']['AanbiedingstekstEngels'];
        }

        if ($backupPostData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel']) {
            $formData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'] = $backupPostData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'];
        }


//        $formData = $this->array_merge_recursive($formData, $postData);

//        $formData = (new ManipulateDataArrayService())->execute($formData);

        if ($postData) {
//            $postData['WijzigWoonObjectObjectDetailsHuur']['Prijsvoorvoegsel'] = $postData['WijzigWoonObjectObjectDetailsHuur']['Huurprijs'] > 0 ? 'huurprijs' : 'prijs op aanvraag';

            $postData['WijzigWoonObjectSwitch'] = 'WijzigWoonObjectWonen';
            $postData['WijzigWoonObjectObjectDetailsAdresSwitch'] = 'WijzigWoonObjectObjectDetailsAdresNederlandsAdres';
            $postData['WijzigWoonObjectObjectDetailsCombinatieObjectSwitch'] = 'WijzigWoonObjectObjectDetailsCombinatieObjectBogObject';
            $postData['WijzigWoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenSwitch'] = 'WijzigWoonObjectTransactiegegevensKoperinformatieAchtergelatenObjectWonenWoonhuis';

            $postData['WijzigWoonObjectWonenSwitch'] = 'WijzigWoonObjectWonenWoonhuis';

            $defaultData = $this->array_merge_recursive($backupPostData, $defaultData);
            $postData = $this->array_merge_recursive($defaultData, $postData);

            $this->savePost($postData);
            try {
                (new UpdateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }
//        else {
//
//            $tiaraObject->postValidate();
//
//            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
        $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
//                $this->view->assign('hasErrors', true);
//                $this->view->assign('errorMessage', $errorMessage);
//            }
//        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

//        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);

        $this->viewFormResourceNew();
        $this->view->assign('formData', new formData($formData));
        $this->view->assign('objectId', $objectId);

    }

    public function updateAction()
    {
        Validator::markAsEditMode();

        $this->renderWithNewLayout();
        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object publicatie wijzigen', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb($this->getObjectTitleById($objectId));


        $namespaceScope = WijzigWoonObject::class;

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'wijzigen', [
            'label' => 'Publicatie wijzigen'
        ]);

        $tiaraObject = (new LoadTiaraObjectObjectService)->execute($namespaceScope, $objectId);


        $defaultData = (new PrePopulateUpdateObjectTiaraFormPostDataService($objectId))->execute();

        $postData = $this->getRequest()->getPost();

        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        //fix for update
        unset($backupPostData['WoonObject']['ObjectID']);

        //fix array key for update
        if (key_exists('WoonObject', $backupPostData)) {
            //convert from  WoonObject to WijzigWoonObject as exampel

            foreach ($backupPostData as $formName => $formValues) {
                if (array_key_exists('Wijzig' . $formName, $backupPostData)) {
                    continue;
                }
                if (is_array($formValues)) {
                    foreach ($formValues as $inputName => $inputValue) {
                        $backupPostData['Wijzig' . $formName][$inputName] = $inputValue;
                    }
                } else {
                    if (strpos($formName, 'Switch')) {
                        $formValues = 'Wijzig' .$formValues;
                    }
                    $backupPostData['Wijzig' . $formName] = $formValues;
                }

            }
            $backupPostData['WijzigWoonObjectObjectDetailsStatusVerhuurdSwitch'] = 'WijzigWoonObjectObjectDetailsStatusVerhuurdVolledigVerhuurd';
        }

        $data = $this->array_merge_recursive($defaultData, $backupPostData);
        $data = $this->array_merge_recursive($data, $postData);

        $data = (new ManipulateDataArrayService())->execute($data);

        if ($postData) {
            $this->savePost();
            try {
                (new UpdateTiaraObjectService())->execute($namespaceScope, $objectId, $postData);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }
//        else {
//
//            $tiaraObject->postValidate();
//
//            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
                $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
//                $this->view->assign('hasErrors', true);
//                $this->view->assign('errorMessage', $errorMessage);
//            }
//        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);
//        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);

        list($renderedHtml, $jsScript) = (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm, $data);

        $this->viewFormResource();
        $this->view->assign('renderedHtml', $renderedHtml);
        $this->view->assign('jsScript', $jsScript);
    }

    protected function array_merge_recursive($baseArray, $mergeArray)
    {
        $newArray = [];
        foreach ($baseArray as $key => $item) {
            if (is_array($item)) {
                $newArray[$key] = $this->array_merge_recursive($item, $mergeArray[$key]);
            } else {
                $newArray[$key] = $item;
            }
        }

        if (!is_array($mergeArray)) {
            return $newArray;
        }
        foreach ($mergeArray as $key => $item) {
            if (is_array($item)) {
                $newArray[$key] = $this->array_merge_recursive($baseArray[$key], $item);
            } else {
                $newArray[$key] = $item;
            }
        }
        return $newArray;
    }

    public function removeAction()
    {
        $this->renderWithNewLayout();
        $objectId = $this->getParam('object_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object afmelden', '/tiara_object/list/object_id/'. $objectId)
            ->addCrumb($this->getObjectTitleById($objectId));


        $namespaceScope = AfmeldenWoonObject::class;

        $tiaraObject = (new LoadTiaraObjectObjectService)->execute($namespaceScope, $objectId);

        $tiaraEntity = (new LoadTiaraObjectObjectService)->load($objectId);


        $defaultData = (new \TiaraWonen\Application\Service\Object\PrePopulateRemoveObjectTiaraFormPostDataService($objectId))->execute();

        $postData = $this->getRequest()->getPost();
        $backupPostData = (new LoadFormStateDataObjectService())->execute($objectId);

        $data = $this->array_merge_recursive($defaultData, $backupPostData);
        $data = $this->array_merge_recursive($data, $postData);

        $data['AfmeldenWoonObjectTransactiegegevens']['DatumInvoer'] = date('Y-m-d', strtotime($tiaraEntity->created_at));
        $data['AfmeldenWoonObjectTransactiegegevensKoperinformatieHuishoudensituatie']['Verhuisredenen'] = ['andere reden'];

        if ($postData) {
            $this->savePost();


            try {
                (new RemoveTiaraEntityObjectService())->execute($namespaceScope, $objectId, $data);
                $this->redirect('tiara_object/list', ['object_id' => $objectId]);
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraXmlErrorException $e) {
                die($e->getMessage()); // dit lijkt niet meer gebruikt te worden
            }
        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

        $this->viewFormResourceNew();
        $this->view->assign('formData', new formData($data));
        $this->view->assign('objectId', $objectId);
    }
}


class formData {

    private $formData;

    public function __construct($formData)
    {
        $this->formData = $formData;
    }

    public function get($keyStr)
    {

        $keys = $this->getKeys($keyStr);
        $value = $this->getValueByKeys($keys, $this->formData);

        return $value;
    }

    public function isSelected($keyStr, $checkValue)
    {
        $keys = $this->getKeys($keyStr);
        $value = $this->getValueByKeys($keys, $this->formData);

        if ($value === $checkValue) {
            return 'selected="selected"';
        }
    }

    public function isChecked($keyStr, $checkValue)
    {
        $keys = $this->getKeys($keyStr);
        $value = $this->getValueByKeys($keys, $this->formData);

        if (in_array($checkValue, $value)) {
            return 'checked="checked"';
        }
    }

    private function getKeys($keyStr)
    {
        $keys = [];
        $keyParts = explode( '[', $keyStr);
        foreach ($keyParts as $keyPart) {
            $keys[] = trim($keyPart, ']');
        }
        return $keys;
    }

    private function getValueByKeys($keys, $array)
    {
        $keysReverse = array_reverse($keys);
        $key = array_pop($keysReverse);
        $value = $array[$key];

        if (!count($keysReverse)) {
            return $value;
        }

        return $this->getValueByKeys(array_reverse($keysReverse), $value);

    }
}
