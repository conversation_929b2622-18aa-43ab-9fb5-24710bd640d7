<?php

use TiaraWonen\Application\Service\Error\ViewErrorsService;

class Tiara_ErrorController  extends \GlobalController
{
    public function listAction()
    {
        $omniboxxEntityType = $this->getParam('type');
        $omniboxxEntityId = $this->getParam('id');

        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb(ucfirst($omniboxxEntityType))->addCrumb('Errors');

        $data = (new ViewErrorsService())->execute($omniboxxEntityType, $omniboxxEntityId);

        $types = [
            'created_at' => ['title' => 'Gemaakt op', 'width' => 'medium'],
            'code' => ['title' => 'Code', 'width' => 'small'],
            'description' => ['title' => 'Omschrijving', 'width' => 'xxxxxxxlarge'],
        ];

        $this->view->assign('extraWidth', true);
        
        $this->view->ListView($data)
            ->setTypes($types)
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->addLegend('tiara-error-legend')
            ->addFormat('description', function ($value) {
                return wordwrap($value, 200, "<br />\n");
            })
            ->render($this);
    }
}
