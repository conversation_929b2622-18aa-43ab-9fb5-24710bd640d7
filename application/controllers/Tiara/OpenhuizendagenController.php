<?php

use Openhuizendagen\Application\Service\AddOpenhuizendagRequest;
use Openhuizendagen\Application\Service\AddOpenhuizendagService;
use Openhuizendagen\Application\Service\RemoveOpenhuizendagRequest;
use Openhuizendagen\Application\Service\RemoveOpenhuizendagService;
use Openhuizendagen\Application\Service\ViewObjectOpenhuizenService;
use Openhuizendagen\Application\Service\ViewProjectOpenhuizenService;

class Tiara_OpenhuizendagenController extends \GlobalController
{
    public function objectListAction()
    {
        $data = (new ViewObjectOpenhuizenService())->execute();

        $types = [
            'rendered_address' => ['title' => 'Adres', 'width' => 'xxxlarge'],
            'start_date_time' => ['title' => 'Van', 'width' => 'large'],
            'end_date_time' => ['title' => 'Tot', 'width' => 'large'],
            'action' => ['title' => 'Aktie', 'width' => 'large'],
        ];

        $filters = [
            'rendered_address' => ['type' => 'input']
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('start_date_time', function ($value) {
                return (new DateTime($value))->format('d-m-Y H:i');
            })
            ->addFormat('end_date_time', function ($value) {
                return (new DateTime($value))->format('d-m-Y H:i');
            })
            ->addFormat('action', function ($value, $row) {
                return "<a href='tiara_openhuizendagen/remove/id/{$row['id']}'>
                            Afmelden <i class='fa fa-remove'></i>
                        </a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function projectListAction()
    {
        $data = (new ViewProjectOpenhuizenService())->execute();

        $types = [
            'name' => ['title' => 'Naam', 'width' => 'xxxlarge'],
            'start_date_time' => ['title' => 'Van', 'width' => 'large'],
            'end_date_time' => ['title' => 'Tot', 'width' => 'large'],
            'action' => ['title' => 'Aktie', 'width' => 'large'],
        ];

        $filters = [
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('start_date_time', function ($value) {
                return (new DateTime($value))->format('d-m-Y H:i');
            })
            ->addFormat('end_date_time', function ($value) {
                return (new DateTime($value))->format('d-m-Y H:i');
            })
            ->addFormat('action', function ($value, $row) {
                return "<a href='tiara_openhuizendagen/remove/id/{$row['id']}'>
                            Afmelden <i class='fa fa-remove'></i>
                        </a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function addAction()
    {
        $form = new Form($this, 'tiara/openhuizendag');

        $tiaraEntityId = $this->getParam('tiara_entity_id');

        $postData = $this->getRequest()->getPost();

        if ($this->getRequest()->isPost() && $form->isValid($postData)) {
            $data = $postData['openhuizendag'];

            $startDatumTijd = "{$data['date']} {$data['start_hour']}:{$data['start_minute']}";
            $endDatumTijd = "{$data['date']} {$data['end_hour']}:{$data['end_minute']}";

            try {
                (new AddOpenhuizendagService())->execute(new AddOpenhuizendagRequest(
                    $tiaraEntityId,
                    $startDatumTijd,
                    $endDatumTijd
                ));

                $this->redirect($_SERVER['HTTP_REFERER']);
            } catch (Exception $e) {
                $this->view->assign('errorMessages', $e->getMessage());
            }
        }

        $this->view->assign('form', $form);
    }

    public function removeAction()
    {
        $this->disableView();

        $openhuizendagId = $this->getParam('id');

        try {
            (new RemoveOpenhuizendagService())->execute(new RemoveOpenhuizendagRequest($openhuizendagId));
            $this->redirect($_SERVER['HTTP_REFERER']);
        } catch (Exception $e) {
            p($e->getMessage());
            die('sad');
        }
    }
}
