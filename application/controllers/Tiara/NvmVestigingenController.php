<?php

class Tiara_NvmVestigingenController extends \GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb('NVM vestigingen');

        $tiaraNvmLocationsModel = new DbTable\TiaraNvmLocations();
        $data = $tiaraNvmLocationsModel->fetchAll()->toArray();

        $types = [
            'vestigingsNummer' => ['title' => 'VestigingsNummer', 'width' => 'small truncate'],
            'naamVestigingRegel1' => ['title' => 'NaamVestigingRegel1', 'width' => 'xxxlarge truncate'],
            'emailAdres' => ['title' => 'EmailAdres', 'width' => 'xxlarge truncate'],
            'telefoonNummer' => ['title' => 'TelefoonNummer', 'width' => 'large truncate'],
            'homepageAdres' => ['title' => 'HomepageAdres', 'width' => 'xlarge truncate'],
            'Bedrijfssoort' => ['title' => 'Bedrijfssoort', 'width' => 'large truncate'],
            'action' => ['title' => 'Aktie', 'width' => 'small truncate'],
        ];

        $filters = [
            'vestigingsNummer' => ['type' => 'input'],
            'naamVestigingRegel1' => ['type' => 'input'],
            'emailAdres' => ['type' => 'input'],
            'telefoonNummer' => ['type' => 'input'],
            'homepageAdres' => ['type' => 'input'],
            'Bedrijfssoort' => [],
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('action', function ($value, $row) {
                return "<a href='tiara_nvm-vestigingen/view/nvm_location_id/{$row['id']}' target='_blank'>
                            <i class='fa fa-eye'></i>
                        </a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function viewAction()
    {
        $nvmLocationId = $this->getParam('nvm_location_id');
        $tiaraNvmLocationsModel = new DbTable\TiaraNvmLocations();
        $data = $tiaraNvmLocationsModel->fetchRowById($nvmLocationId);
        $this->view->assign('nvmLocationRow', $data->toArray());
    }
}
