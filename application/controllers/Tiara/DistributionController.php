<?php

/**
 * Class Tiara_DistributionController
 */
// @phpcs:ignore
class Tiara_DistributionController extends \GlobalController
{
    /**
     * Tiara Distribution index. Contains a form.
     *
     * @return void
     *
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     * @throws Zend_View_Exception
     */
    public function indexAction()
    {
        global $container;

        $form = new Form($this, 'TiaraDistribution');
        if ($this->getRequest()->isPost()) {
            $params = array_filter($this->getRequest()->getPost());
            try {
                if ($form->isValid($params)) {
                    $this->convertParams($params, $form);

                    /** @var \TiaraDistribution\Application\NVM\TiaraDistributionFetchNVMReportService $tiaraDistributionFetchNVMReportService */
                    $tiaraDistributionFetchNVMReportService = $container->get('tiaraDistributionFetchNVMReportService');
                    $request = new \TiaraDistribution\Application\NVM\TiaraDistributionFetchNVMReportRequest($params);
                    $tiaraDistributionFetchNVMReportService->execute($request);

                    $_SESSION['nvm_query_params'] = $params;

                    $results = $tiaraDistributionFetchNVMReportService->getResults();

                    $this->renderList($results['Zoekresultaten']['Object'])->render($this);
                } else {
                    $form->populate($params);
                    $this->view->assign('form', $form);
                }
            } catch (Zend_Form_Exception $e) {
                p($e->getMessage());
            }
        } else {
            if ($this->getRequest()->getParam('view_template') === 'excel') {
                /** @var \TiaraDistribution\Application\NVM\TiaraDistributionFetchNVMReportService $tiaraDistributionFetchNVMReportService */
                $tiaraDistributionFetchNVMReportService = $container->get('tiaraDistributionFetchNVMReportService');
                $request = new \TiaraDistribution\Application\NVM\TiaraDistributionFetchNVMReportRequest(
                    $_SESSION['nvm_query_params']
                );
                $tiaraDistributionFetchNVMReportService->execute($request);

                $results = $tiaraDistributionFetchNVMReportService->getResults();

                $this->renderList($results['Zoekresultaten']['Object'], ['view_template' => 'excel'])->render($this);
            }
            try {
                /** @var \TiaraDistribution\Application\TiaraDistributionAccessTiaraService $tiaraDistributionAccessTiaraService */
                $tiaraDistributionAccessTiaraService = $container->get('tiaraDistributionAccessTiaraService');
                $accessTiaraRequest = new \TiaraDistribution\Application\TiaraDistributionAccessTiaraRequest(
                    $this->getRequest()->getParams()
                );
                $tiaraDistributionAccessTiaraService->execute($accessTiaraRequest);
                $this->view->assign('form', $form);
            } catch (\Exception $e) {
                error_log($e->getMessage());
            }
        }
    }

    public function authenticateAccessTokenAction() {
        global $container;

        /** @var \TiaraDistribution\Application\TiaraDistributionAccessTiaraService $tiaraDistributionAccessTiaraService */
        $tiaraDistributionAccessTiaraService = $container->get('tiaraDistributionAccessTiaraService');
        $accessTiaraRequest = new \TiaraDistribution\Application\TiaraDistributionAccessTiaraRequest(
            $this->getRequest()->getParams()
        );

        $tiaraDistributionAccessTiaraService->execute($accessTiaraRequest);
        $this->redirect('tiara_distribution');
    }

    private function renderList($data, $params = []) {
        $this->view->extraWidth = true;
        return $this->view->ListView($data, $params)
            ->setTypes($this->getListviewTypes())
            ->addFormat('Koophuur', function ($value, $row) {
                if ($row['IsKoop'] === 'true' && $row['IsHuur'] === 'true') {
                    return 'Beide';
                }
                return $row['IsKoop'] === 'true' ? 'Koop' : 'Huur';
            })
            ->addFormat('Adres', function ($value, $row) {
                $number = $row['Huisnummer'] ?: '';
                $toevoeging = $row['Huisnummertoevoeging'] !== 'ong' ? $row['Huisnummertoevoeging'] : '';
                $address = $row['Straatnaam'] . ' ' . $number . $toevoeging;
                $trimmedAddress = trim($address);
                return !empty($trimmedAddress) ? $address : $row['Naam'];
            })
            ->addFormat('BrutoInhoud', function ($value, $row) {
                if ($row['BrutoInhoudVan'] === $row['BrutoInhoudTot']) {
                    return $row['BrutoInhoudVan'] . ' m3';
                }
                return $row['BrutoInhoudVan'] . ' m3 - ' . $row['BrutoInhoudTot'] . ' m3';
            })
            ->addFormat('Woonoppervlakte', function ($value, $row) {
                if ($row['WoonoppervlakteVan'] === $row['WoonoppervlakteTot']) {
                    return $row['WoonoppervlakteVan'] . ' m2';
                }
                return $row['WoonoppervlakteVan'] . ' m2 - ' . $row['WoonoppervlakteTot'] . ' m2';
            })
            ->addFormat('PerceelOppervlakte', function ($value) {
                return $value > 0 ? $value . ' m2' : '';
            })
            ->addFormat('IsActief', function ($value) {
                return $value === 'true' ? 'Ja' : 'Nee';
            })
            ->addFormat('HuidigeStatus', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('GarageSoorten', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('ObjectSoort', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('KoopConditie', function ($value, $row) {
                if ($row['IsKoop'] !== 'true') {
                    return 'nvt';
                }
                return ucfirst($value);
            })
            ->addFormat('HuurConditie', function ($value, $row) {
                if ($row['IsHuur'] !== 'true') {
                    return 'nvt';
                }
                return ucfirst($value);
            })
            ->addFormat('Bouwvorm', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('AppartementType', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('WoningType', function ($value) {
                return ucfirst($value);
            })
            ->addFormat('InitieleKoopprijs', function ($value, $row) {
                if ($row['IsKoop'] !== 'true') {
                    return 'nvt';
                }
                $from = $row['InitieleKoopPrijsVan'];
                $to = $row['InitieleKoopPrijsTot'];
                $initieleKoopprijsVan = abs($from) > 0 ? '&euro; ' . number_format($from, 0, '', '.') : 'Onbekend';
                $initieleKoopprijsTot = abs($to) > 0 ? '&euro; ' . number_format($to, 0, '', '.') : 'Onbekend';
                return $initieleKoopprijsVan === $initieleKoopprijsTot  ?
                    $initieleKoopprijsVan :
                    $initieleKoopprijsVan . ' - ' . $initieleKoopprijsTot;
            })
            ->addFormat('Koopprijs', function ($value, $row) {
                if ($row['IsKoop'] !== 'true') {
                    return 'nvt';
                }
                $from = $row['KoopprijsVan'];
                $to = $row['KoopprijsTot'];
                $koopprijsVan = abs($from) > 0 ? '&euro; ' . number_format($from, 0, '', '.') : 'Onbekend';
                $koopprijsTot = abs($to) > 0 ? '&euro; ' . number_format($to, 0, '', '.') : 'Onbekend';
                return $koopprijsVan === $koopprijsTot  ? $koopprijsVan : $koopprijsVan . ' - ' . $koopprijsTot;
            })
            ->addFormat('Huurprijs', function ($value, $row) {
                if ($row['IsHuur'] !== 'true') {
                    return 'nvt';
                }
                $from = $row['HuurprijsVan'];
                $to = $row['HuurprijsTot'];
                $huurprijsVan = abs($from) > 0 ? '&euro; ' . number_format($from, 0, '', '.') : 'Onbekend';
                $huurprijsTot = abs($to) > 0 ? '&euro; ' . number_format($to, 0, '', '.') : 'Onbekend';
                return $huurprijsVan === $huurprijsTot  ? $huurprijsVan : $huurprijsVan . ' - ' . $huurprijsTot;
            })
            ->addFormat('TransactiePrijs', function ($value) {
                return abs($value) > 0 ? '&euro; ' . number_format($value, 0, '', '.') : '-';
            })
            ->addFormat('AantalEenheden', function ($value, $row) {
                if (!$value) {
                    return '-';
                }
                return $value . ' (' . ($row['AantalVrijeEenheden'] ?: '0') . ' beschikbaar)';
            })
            ->addFormat('Aanmelddatum', function ($value) {
                return date('d-m-Y', strtotime($value));
            })
            ->setOptions(['paginator' => false]);
    }

    /**
     * Convert POST parameters to the correct formats for use in the Tiara API.
     *
     * @param array     $params POST parameters.
     * @param Zend_Form $form   Form object.
     *
     * @return void
     */
    private function convertParams(&$params, $form)
    {
        unset($params['aanmaken']);

        if (array_key_exists('dates', $params)) {
            $params['dates'] = array_filter($params['dates']);
            foreach ($params['dates'] as $key => $date) {
                $dateTime = DateTime::createFromFormat('Y-m-d', $date);
                $params[$key] = $dateTime->format('Y-m-d') . 'T00:00:00Z';
            }
            unset($params['dates']);
        }

        if (array_key_exists('multis', $params)) {
            foreach ($params['multis'] as $key => $values) {
                $params[$key] = implode(',', $values);
            }
            unset($params['multis']);
        }

        if (array_key_exists('checkboxes', $params)) {
            $params['checkboxes'] = array_filter($params['checkboxes']);
            foreach ($params['checkboxes'] as $key => $value) {
                if ((int)$value === 1) {
                    $params[$key] = 'true';
                }
                if ($value === 0) {
                    $params[$key] = 'false';
                }
            }
            unset($params['checkboxes']);
        }

        $energieKlasseOptions = $form->getElement('energieklassevan')
            ->getAttrib('options');

        if (array_key_exists('energieklassevan', $params)) {
            $params['energieklassevan'] = $energieKlasseOptions[$params['energieklassevan']];
        }

        if (array_key_exists('energieklassetot', $params)) {
            $params['energieklassetot'] = $energieKlasseOptions[$params['energieklassetot']];
        }
    }

    private function getListviewTypes() {
        return [
            'Koophuur' => ['title' => 'Koop/huur', 'width' => 'xxsmall truncate'],
            'Adres' => ['title' => 'Adres', 'width' => 'medium multiline'],
            'Woonplaats' => ['title' => 'Woonplaats', 'width' => 'xsmall truncate'],
            'Postcode' => ['title' => 'Postcode', 'width' => 'xxsmall truncate'],
            'KoopConditie' => ['title' => 'Koopconditie', 'width' => 'xsmall truncate'],
            'Koopprijs' => ['title' => 'Koopprijs', 'width' => 'medium truncate'],
            'InitieleKoopprijs' => ['title' => 'Originele koopprijs', 'width' => 'medium truncate'],
            'TransactiePrijs' => ['title' => 'Transactieprijs', 'width' => 'small truncate'],
            'IsActief' => ['title' => 'Actief', 'width' => 'xxxsmall truncate'],
            'Aanmelddatum' => ['title' => 'Aanmelddatum', 'width' => 'xsmall truncate'],
            'MakelaarNaam' => ['title' => 'Makelaar', 'width' => 'large multiline'],
            'ObjectSoort' => ['title' => 'Soort woning', 'width' => 'small truncate'],
            'AppartementType' => ['title' => 'Appartementtype', 'width' => 'small truncate'],
            'WoningType' => ['title' => 'Woningtype', 'width' => 'small multiline'],
            'HuidigeStatus' => ['title' => 'Status', 'width' => 'xxsmall truncate'],
            'Vestigingsnummer' => ['title' => 'Vestigingsnr', 'width' => 'xxsmall truncate'],
            'Huurprijs' => ['title' => 'Huurprijs', 'width' => 'medium truncate'],
            'HuurConditie' => ['title' => 'Huurconditie', 'width' => 'small multiline'],
            'Woonoppervlakte' => ['title' => 'Woonoppervlakte', 'width' => 'medium truncate'],
            'PerceelOppervlakte' => ['title' => 'Perceeloppervlakte', 'width' => 'small truncate'],
            'BrutoInhoud' => ['title' => 'Bruto inhoud', 'width' => 'medium truncate'],
            'AantalKamers' => ['title' => 'Aantal kamers', 'width' => 'small truncate'],
            'AantalEenheden' => ['title' => 'Eenheden', 'width' => 'small truncate'],
            'GarageSoorten' => ['title' => 'Garagesoort', 'width' => 'medium multiline'],
            'Bouwvorm' => ['title' => 'Bouwvorm', 'width' => 'xsmall truncate'],
        ];
    }
}
