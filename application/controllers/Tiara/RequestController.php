<?php

use TiaraWonen\Application\Service\Request\InternetplaatsingenService;
use TiaraWonen2\Application\Service\Request\NvmVestigingenService;
use TiaraWonen\Application\Service\Request\ZipCodeCheckService;

class Tiara_RequestController extends \GlobalController
{
    public function postCodeCheckAction()
    {
        $this->disableView();

        $postCode = $this->getParam('postal_code');
        $houseNumber = $this->getParam('house_number');

        $response = (new ZipCodeCheckService())->execute($postCode, $houseNumber, 'kenmerk_123');

        if ($response->getIsSuccess()) {
            echo json_encode($response);
        } else {
            echo '-1';
        }
    }

    public function nvmVestigingenAction()
    {
        $this->disableView();
        (new NvmVestigingenService())->execute();
    }

    public function nvmVestigingenSuggestionAction()
    {
        $this->disableView();

        $query = $this->getParam('query');

        $tiaraNvmLocations = new \DbTable\TiaraNvmLocations();
        $tiaraNvmLocationRows = $tiaraNvmLocations->fetchAll(
            'naamVestigingRegel1 LIKE "%' . $query . '%"',
            new \Zend_Db_Expr("case when naamVestigingRegel1 like '$query%' then 0 else 1 end, naamVestigingRegel1"),
            10
        );

        if ($tiaraNvmLocationRows->count() === 0) {
            echo '-1';
        } else {
            $result = [];
            foreach ($tiaraNvmLocationRows->toArray() as $nvm_location_row) {
                $result[] = $nvm_location_row[] = [
                    $nvm_location_row['vestigingsNummer'],
                    implode(' - ', [
                        $nvm_location_row['naamVestigingRegel1'],
                        ucfirst(strtolower($nvm_location_row['paVestigingsplaats'])),
                    ]),
                ];
            }
            $json = json_encode($result);

            echo $json;
        }
    }

    public function getNvmVestigingDataByNumberAction()
    {
        $this->disableView();

        $number = $this->getParam('number');

        $tiaraNvmLocations = new \DbTable\TiaraNvmLocations();
        $tiaraNvmLocationRow = $tiaraNvmLocations
            ->matchRow(['vestigingsNummer' => $number]);

        if($tiaraNvmLocationRow) {
            echo json_encode($tiaraNvmLocationRow->toArray());
        } else {
            echo '-1';
        }
    }

    public function internetplaatsingenAction()
    {
        $this->disableView();
//        (new InternetplaatsingenService())->execute(); // obsolete
    }
}
