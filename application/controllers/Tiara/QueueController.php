<?php

use TiaraWonen\Application\Service\Queue\ProcessTiaraQueueService;

class Tiara_QueueController extends \GlobalController
{
    /**
     * http://support.omniboxx.johan.loc/tiara_queue/process
     * @return void
     */
    public function processAction()
    {
        $this->disableView();
        $pidLock = new PIDLock('process-tiara-queue-service');
        try {
            (new ProcessTiaraQueueService())->execute();
        } finally {
            $pidLock->remove();
        }
    }
}
