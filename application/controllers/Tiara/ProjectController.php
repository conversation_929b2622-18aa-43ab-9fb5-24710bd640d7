<?php

use application\models\TiaraWonen\Application\Service\Project\LoadFormStateDataProjectService;
use DbTable\TiaraFormState;
use DbTable\TiaraQueue;
use TiaraWonen\Application\Service\Object\LoadTiaraObjectProjectService;
use TiaraWonen\Application\Service\PrePopulateProjectTiaraFormPostDataRemoveService;
use TiaraWonen\Application\Service\Project\PrePopulateCreateProjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Project\PrePopulateUpdateProjectTiaraFormPostDataService;
use TiaraWonen\Application\Service\Project\CreateTiaraProjectService;
use TiaraWonen\Application\Service\Project\RemoveTiaraEntityProjectService;
use TiaraWonen\Application\Service\Project\UpdateTiaraProjectService;
use TiaraWonen\Application\Service\Project\ViewProjectListService;
use TiaraWonen\Application\Service\SaveTiaraFormStateService;
use TiaraWonen\Model\Wonen\TiaraValidationException;
use TiaraWonen\Model\Wonen\Validatie\ValidationException;
use TiaraWonen\ViewFormService;
use TiaraWonen\Infrastructure\Service\TiaraXmlErrorException;
use TiaraWonen\Model\Wonen\Projecten\AfProject;
use TiaraWonen\Model\Wonen\Projecten\Project;
use TiaraWonen\Model\Wonen\Projecten\WijProject;
use TiaraWonen\NamespaceIterator;
use TiaraWonen\Render\RenderRootPanelService;
use TiaraWonen\TiaraViewExtractJob;

class Tiara_ProjectController extends \GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Funda')->addCrumb('Projecten');

        $projectId = $this->getParam('project_id') ?: false;
        $data = (new ViewProjectListService())->execute($projectId);

        $types = [
            'name' => ['title' => 'Naam', 'width' => 'xxxxlarge'],
            'state' => ['title' => 'Status', 'width' => 'small'],
            'created_at' => ['title' => 'Aangepast', 'width' => 'large'],
            'error_count' => ['title' => 'Fouten', 'width' => 'medium'],
            'actions' => ['title' => 'Acties', 'width' => 'xxxxlarge'],
            'project_id' => ['title' => 'Tiara ID', 'width' => 'medium']
        ];

        $filters = [
            'description' => ['type' => 'input']
        ];

        $this->view->assign('extraWidth', true);

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('actions', function ($value, $row) {
                $incompatibleState = in_array($row['state'], [
                        TiaraQueue::STATE_PROCESSING,
                        TiaraQueue::STATE_QUEUED,
                        TiaraQueue::STATE_EXCEEDED_MAX_ATTEMPT_POLICY
                    ]
                );

                if ($incompatibleState) {
                    return '';
                }

                $addButton = "<a href='tiara_project/create/project_id/{$row['id']}'>
                            Aanmelden <i class='fa fa-plus-circle'></i>
                        </a>";

                $updateButton = "<a href='tiara_project/update/project_id/{$row['id']}'>
                            Wijzigen <i class='fa fa-edit'></i>
                        </a>";

                $removeButton = "<a href='tiara_project/remove/project_id/{$row['id']}'>
                            Afmelden <i class='fa fa-remove'></i>
                        </a>";

                $addOpenhuizendagButton = "<a href='tiara_openhuizendagen/add/tiara_entity_id/{$row['entity_id']}' target='_blank'>
                            Openhuizendag aanmelden <i class='fa fa-plus-circle'></i>
                        </a>";

                switch ($row['tiara_entity_state']) {
                    case null:
                    case '':
                    case 'new':
                    case 'removed':
                        $html = $addButton;
                        break;
                    case 'added':
                    case 'mutated':
                        $html = $updateButton . $removeButton . $addOpenhuizendagButton;
                        break;
                    default:
                        $html = null;
                }

                return $html;
            })


            ->addFormat('error_count', function ($value, $row) {
                $errorButton = "<a href='tiara_error/list/type/project/id/{$row['id']}'>
                           $value fout(en) <i class='fa fa-exclamation-triangle'></i>
                        </a>";

                return $value ? $errorButton : '0';
            })

            ->addLegend('tiara-project-legend')

            ->setFilters(array(
                'name' => array('title' => 'Project','type' => 'input'),
                'error_count' => array('renderSeparately' => true, 'title' => 'Fouten', 'order_by_title' => 'DESC'),
                'state' => array('renderSeparately' => true, 'title' => 'Status', 'order_by_title' => 'DESC')
            ))

            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    private function viewFormResource()
    {
        $this->view->headLink()
            ->appendStylesheet('media/style/tiara_form/style.css')
            ->appendStylesheet('media/style/datepicker.css')
            ->appendStylesheet('media/style/tags/TextboxList.css')
            ->appendStylesheet('media/style/tags/TextboxList.Autocomplete.css');

        $this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');

        $this->view->headScriptHashed()
            ->appendFile('media/javascript/tiara_form/woonObject_medeAanmelders.js')
            ->appendFile('media/javascript/tiara_form/class_helper.js')
            ->appendFile('media/javascript/tiara_form/element_helper.js')
            ->appendFile('media/javascript/tiara_form/loader.js')
            ->appendFile('media/javascript/tiara_form/elements/button.js')
            ->appendFile('media/javascript/tiara_form/elements/ul.js')
            ->appendFile('media/javascript/tiara_form/elements/span.js')
            ->appendFile('media/javascript/tiara_form/elements/li.js')
            ->appendFile('media/javascript/tiara_form/elements/form/fieldset.js')
            ->appendFile('media/javascript/tiara_form/elements/form/field_row.js')
            ->appendFile('media/javascript/tiara_form/elements/form/field.js')
//            ->appendFile('media/javascript/tiara_form/elements/form/validation/field_validation.js')
//            ->appendFile('media/javascript/tiara_form/elements/form/validation/required_validation.js')
            ->appendFile('media/javascript/tiara_form/elements/form/input.js')
            ->appendFile('media/javascript/tiara_form/elements/form/select.js')
            ->appendFile('media/javascript/tiara_form/elements/form/textarea.js')
            ->appendFile('media/javascript/tiara_form/elements/form/checkbox.js')
            ->appendFile('media/javascript/tiara_form/elements/form/submit.js')
            ->appendFile('media/javascript/tiara_form/elements/form/group_checkbox.js')
            ->appendFile('media/javascript/tiara_form/elements/form/radio.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/toggle.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/label.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_form.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_multi.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_panel.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_switch_option.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_go_to_tab.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_item.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_item_group.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_list.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/nav_to.js')
            ->appendFile('media/javascript/tiara_form/tree_nav/tree_nav_container.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/loader.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/item.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/wrapper.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/list.js')
            ->appendFile('media/javascript/tiara_form/multi_fieldset/group/list/item.js')
            ->appendFile('media/javascript/tiara_form/dispatcher.js')
            ->appendFile('media/javascript/tiara_form/dispatcher_event.js')
            ->appendFile('media/javascript/tiara_form/form/form_container.js')
            ->appendFile('media/javascript/tiara_form/main_container.js')
            ->appendFile('media/javascript/tiara_form/main_container_loader.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_container.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option_container.js')
            ->appendFile('media/javascript/tiara_form/switch/switch_option_select.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_container.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_menu_item.js')
            ->appendFile('media/javascript/tiara_form/tabs/tab_panel.js')
            ->appendFile('media/javascript/tiara_form/multi/link_item.js')
            ->appendFile('media/javascript/tiara_form/multi/list.js')
            ->appendFile('media/javascript/tiara_form/multi/list_container.js')
            ->appendFile('media/javascript/tiara_form/multi/list_item.js')
            ->appendFile('media/javascript/tiara_form/multi/multi_container_list.js')
            ->appendFile('media/javascript/tiara_form/multi/multi_container.js')
            ->appendFile('media/javascript/tiara_form/panel_container.js');
    }

    public function validateFormSegmentAction()
    {
        $this->disableView();

        $rawJsonData = file_get_contents('php://input');
        $postData = json_decode($rawJsonData, true);

        $namespaceScope = $postData['identification'];

        $zendForm = new \Form($this, '');
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->setDefaultTranslator($zendForm->formErrorTranslation()->getAdapter());

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());

        (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm);

        $formData = $postData['data'];

        $result = (object)[
            'succes' => false,
            'error' => false,
            'errors' => []
        ];

        if (!$zendForm->isValid($formData)) {
            $result->error = true;
            $result->errors = json_decode($zendForm->processAjax($formData));
        }

        $result->succes = true;

        echo json_encode($result);
    }

    private function getProjectById($objectId)
    {
        $projectRow = (new \Projects())->getById($objectId);

        if (!$projectRow) {
            return 'Object niet gevonden';
        }

        return $projectRow->name;
    }

    public function createAction()
    {
        $this->renderWithNewLayout();
        $projectId = $this->getParam('project_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda project aanbieden')
            ->addCrumb($this->getProjectById($projectId));


        $namespaceScope = Project::class;

        $tiaraObject = (new LoadTiaraObjectProjectService())->execute($namespaceScope, $projectId);

        $isTiaraObjectLoadFromDataBase = false;
        if ($tiaraObject) {
            $isTiaraObjectLoadFromDataBase = true;
        } else {
//            $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#16 Aanmelding van een project met meerdere objecttypes en met meerdere kavels per objecttype.xml';
//
//            $xmlString = file_get_contents($filepath);
//            $serializer = (new \TiaraWonen\Serializer\TiaraObjectSerializerFactory())->build();
//            $tiaraObject = $serializer->deserialize($xmlString, $namespaceScope, 'xml');
//
//            if (!$this->getRequest()->getPost()) {
//                $tiaraObject->postValidate();
//            }
//
//            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
//                $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
//                $this->view->assign('hasErrors', true);
//                $this->view->assign('errorMessage', $errorMessage);
//            }
        }

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'aanmaken', [
            'label' => 'Aanbieden voor publicatie'
        ]);

        $defaultData = (new PrePopulateCreateProjectTiaraFormPostDataService())->execute($projectId);
        $backupPostData = (new LoadFormStateDataProjectService())->execute($projectId);

        $postData = $this->getRequest()->getPost();

        $data = $this->array_merge_recursive($defaultData, $backupPostData);
        $data = $this->array_merge_recursive($data, $postData);

        if ($postData) {
            $this->savePost();
            try {
                (new CreateTiaraProjectService())->execute($namespaceScope, $projectId, $data);
                $this->redirect('tiara_project/list');
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            }
        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

        if ($isTiaraObjectLoadFromDataBase) {
            $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);
        }

        list($renderedHtml, $jsScript) = (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm, $data);

        $this->viewFormResource();
        $this->view->assign('renderedHtml', $renderedHtml);
        $this->view->assign('jsScript', $jsScript);
    }

    protected function array_merge_recursive($baseArray, $mergeArray)
    {
        $newArray = [];
        foreach ($baseArray as $key => $item) {
            if (is_array($item)) {
                $newArray[$key] = $this->array_merge_recursive($item, $mergeArray[$key]);
            } else {
                $newArray[$key] = $item;
            }
        }

        foreach ($mergeArray as $key => $item) {
            if (is_array($item)) {
                $newArray[$key] = $this->array_merge_recursive($baseArray[$key], $item);
            } else {
                $newArray[$key] = $item;
            }
        }
        return $newArray;
    }

    public function savePost()
    {
        $projectId = $this->getParam('project_id');
        $postData = $this->getRequest()->getPost();
        $omniboxxEntityType = TiaraFormState::OMNIBOXX_ENTITY_TYPE_PROJECT;

        try {
            (new SaveTiaraFormStateService())->execute($projectId, $omniboxxEntityType, $postData);
        } catch (Exception $exception) {
            die($exception->getMessage());
        }
    }

    public function updateAction()
    {
        $this->renderWithNewLayout();
        $projectId = $this->getParam('project_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda project publicatie wijzigen')
            ->addCrumb($this->getProjectById($projectId));


        $namespaceScope = WijProject::class;

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'wijzigen', [
            'label' => 'Publicatie wijzigen'
        ]);

        $tiaraObject = (new LoadTiaraObjectProjectService())->execute($namespaceScope, $projectId);
        $backupPostData = (new LoadFormStateDataProjectService())->execute($projectId);
        $defaultData = (new PrePopulateUpdateProjectTiaraFormPostDataService($projectId))->execute();

        $postData = $this->getRequest()->getPost();

        //fix for update
//        unset($backupPostData['Project']['ProjectID']);

        //fix array key for update
        if (key_exists('Project', $backupPostData)) {
            foreach ($backupPostData as $formName => $formValues) {
                if (array_key_exists('Wij' . $formName, $backupPostData)) {
                    continue;
                }
                foreach ($formValues as $inputName => $inputValue) {
                    $backupPostData['Wij' . $formName][$inputName] = $inputValue;
                }
            }
        }

        $data = $this->array_merge_recursive($defaultData, $backupPostData);
        $data = $this->array_merge_recursive($data, $postData);

        if ($postData) {
            $this->savePost();
            try {
                (new UpdateTiaraProjectService())->execute($namespaceScope, $projectId, $data);
                $this->redirect('tiara_project/list');
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            }
        } else {
            $tiaraObject->postValidate();

            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
                $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $errorMessage);
            }
        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_EDIT_MODE, true);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

        list($renderedHtml, $jsScript) = (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm, $data);

        $this->viewFormResource();
        $this->view->assign('renderedHtml', $renderedHtml);
        $this->view->assign('jsScript', $jsScript);
    }

    public function removeAction()
    {
        $this->renderWithNewLayout();
        $projectId = $this->getParam('project_id');

        $this->view->Breadcrumbs()
            ->addCrumb('Funda object afmelden')
            ->addCrumb($this->getProjectById($projectId));

        $namespaceScope = AfProject::class;

        $zendForm = new \Form($this);
        $zendForm->allowBracketsInPostName();
        $zendForm->setMethod('post')
            ->setAttrib('class', 'form');

        $zendForm->addElement('submit', 'afmelden', [
            'label' => 'Afmelden'
        ]);

        $filepath = __DIR__ . '/../../../library/TiaraWonen/TestRun/#X Remove project.xml';
        $xmlString = file_get_contents($filepath);
        $serializer = (new \TiaraWonen\Serializer\TiaraObjectSerializerFactory())->build();
        $tiaraObject = $serializer->deserialize($xmlString, $namespaceScope, 'xml');



        $defaultData = (new PrePopulateProjectTiaraFormPostDataRemoveService($projectId))->execute();

        $postData = $this->getRequest()->getPost();

        $data = $this->array_merge_recursive($defaultData, $postData);

        if ($postData) {
            try {
                (new RemoveTiaraEntityProjectService())->execute($namespaceScope, $projectId, $data);
                $this->redirect('tiara_project/list');
            } catch (ValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            } catch (TiaraValidationException $e) {
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $e->getMessage());
            }
        } else {
            if (!$this->getRequest()->getPost()) {
                $tiaraObject->postValidate();
            }

            if (\TiaraWonen\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
                $errorMessage = \TiaraWonen\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
                $this->view->assign('hasErrors', true);
                $this->view->assign('errorMessage', $errorMessage);
            }
        }

        $NamespaceIterator = new NamespaceIterator($namespaceScope, new TiaraViewExtractJob());
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_TIARA_INSTANCE, $tiaraObject);
        $NamespaceIterator->addAttribute(\TiaraWonen\TiaraViewExtractJob::KEY_STATE, ['production']);

        list($renderedHtml, $jsScript) = (new ViewFormService(
            $NamespaceIterator,
            new RenderRootPanelService()
        ))->execute($zendForm, $data);

        $this->viewFormResource();
        $this->view->assign('renderedHtml', $renderedHtml);
        $this->view->assign('jsScript', $jsScript);
    }
}
