<?php

class Investor_Vat_OverviewController extends GlobalController
{
	public function preDispatch()
	{
		$this->view->Breadcrumbs()->addCrumb('Belegger BTW overzicht');
	}

	protected function setDefaultParams(){
		$current_quarter = dateToQuarter(strtotime('-3 months'));
		$current_quarter_start = quarterToDate($current_quarter['year'], $current_quarter['quarter']);

		if(is_null($this->getParam('start-date')))
			$this->setParam('start-date', date('d-m-Y', $current_quarter_start));

		if(is_null($this->getParam('end-date')))
			$this->setParam('end-date', date('d-m-Y', strtotime('-1 days', strtotime('+3 months', $current_quarter_start))));
	}

	public function indexAction(){
		$this->view->extraWidth = true;
		$this->setDefaultParams();

		$model = new Controllers_Investor_Vat_Overview();
		$model->setInvestorId($this->getParam('investor'));
		$model->setStartDate($this->getParam('start-date'));
		$model->setEndDate($this->getParam('end-date'));

		if(is_null($this->getParam('sorting')));
			$this->setParam('sorting', json_encode(['og_id' => 'ASC']));

		$data = $model->getListForInvestorInPeriod();

		$amount_columns = ['invoiced_ex_vat','invoiced_vat','invoiced_total','costs_ex_vat','costs_vat','costs_total','total_ex_vat','total_vat','total_total'];

		$this->view->list = $this->view->ListView($data, $this->getAllParams())
			->setTypes([
				'og_id' => ['title' => 'Objectgroep', 'width' => 'medium truncate', 'group_equal_rows' => true],
				'start' => ['title' => 'Periode', 'width' => 'xsmall', false],
				'invoiced_ex_vat' => ['title' => 'Gefactureerd excl. BTW', 'width' => 'medium right'],
				'invoiced_vat' => ['title' => 'Gefactureerd BTW', 'width' => 'medium right'],
				'invoiced_total' => ['title' => 'Gefactureerd incl. BTW', 'width' => 'medium right'],
				'costs_ex_vat' => ['title' => 'Kosten excl. BTW', 'width' => 'medium right'],
				'costs_vat' => ['title' => 'Kosten BTW', 'width' => 'medium right'],
				'costs_total' => ['title' => 'Kosten incl. BTW', 'width' => 'medium right'],
				'total_ex_vat' => ['title' => 'Totaal excl. BTW', 'width' => 'medium right'],
				'total_vat' => ['title' => 'Totaal BTW', 'width' => 'medium right'],
				'total_total' => ['title' => 'Totaal incl. BTW', 'width' => 'medium right'],
			])

			->addFormat($amount_columns, 'money')

			->addFormat('start', function($value){
				return ucfirst(strftime('%B %Y', strtotime($value)));
			})

			->addFormat('og_id', function($value, $row){
				if(!($value > 0)) return '-';

				return $row['og_name'];
			})

			->setOptions([
				'item_title' => 'Gegevens',
				'render_to_controller' => true
			])

			->addTotals($amount_columns)

			->setFilters([
				'start-date' => ['renderSeparately' => true, 'title' => 'Begindatum', 'type' => 'date', 'preApplied' => true],
				'end-date' => ['renderSeparately' => true, 'title' => 'Einddatum', 'type' => 'date', 'preApplied' => true],
				'investor' => ['renderSeparately' => true, 'title' => 'Belegger', 'custom_options' => $model->getAllInvestors(), 'hideCount' => true, 'preApplied' => true, 'custom_options_only' => true, 'order_by_title' => 'ASC'],
				'og_id' => [],
				'object' => ['type' => 'input'],
				'start' => [],
			])
			->render($this);
	}
}