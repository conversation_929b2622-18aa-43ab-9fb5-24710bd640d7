<?
class Investor_Combination_LinkController extends GlobalController
{

	public $investor_combination_id;

	public function preDispatch()
	{
		$id = $this->getParam('id');
        $this->id = $id;

		if($id && is_null($this->getParam('investor_combination_id')))
			$this->setParam('investor_combination_id', db()->fetchOne(db()->select()->from('investor_combination_link', ['investor_combination_id'])->where('id = ?', $id)));

		$this->investor_combination_id = $this->view->investor_combination_id = $investor_combination_id = $this->getParam('investor_combination_id');

		parent::preDispatch();
	}

	public function listAction(){
		$this->_helper->viewRenderer->setNoRender(true);

		$controller_model = new Controllers_Investor_Combination_Link_List($this->investor_combination_id);


		$isEditable = (new Investors\Combination\Service\IsEditableService())->check($this->investor_combination_id);

		$this->view->listView(
			$controller_model->getData(),
			['url-override' => 'investor_combination_link/list/investor_combination_id/id/' . $this->investor_combination_id . '/', ],
			['investor_combination_id' => $this->investor_combination_id]
		)

			->setTypes([
				'is_primary_investor' => [
					'title' => 'Primaire belegger',
					'width' => 'medium',
				],
				'investor' => [
					'title' => 'Belegger',
					'width' => 'xxlarge truncate',
				],
				'investor_abbreviation' => [
					'title' => 'Belegger afkorting',
					'width' => 'large truncate',
				],
				'percentage' => [
					'title' => 'Percentage',
					'width' => 'medium right',
				],
				'spacer' => [
					'title' => '',
					'width' => 'xxsmall',
				]
			])

			->addFormat('percentage', 'percentage')
			
			->addFormat('is_primary_investor', 'bool')

			->addFormat('investor', function($value, $item){
				return $value > 0 ? $item['investor_name'] : '';
			})

			->setFilters([
				'is_primary_investor' => [],
				'investor' => ['type' => 'input'],
				'investor_abbreviation' => ['type' => 'input']
			])

			->addButtons([
				'edit' => 'Bewerken',
				'delete' => 'Verwijderen',
			])

			->addTotals([
				'percentage'
			])

			->setOptions([
				'item_title' => 'Belegger',
				'show_title' => false,
				'show_view_template_buttons' => false,
				'disabled_buttons_test' => function($row, $button) use ($isEditable){
					return !$isEditable;
				}
			])

			->render($this)
		;
	}

	public function simpleListAction(){
		$this->_helper->viewRenderer->setNoRender(true);

		$controller_model = new Controllers_Investor_Combination_Link_List($this->investor_combination_id);

		$this->view->listView(
			$controller_model->getData(),
			['url-override' => 'investor_combination_link/list/investor_combination_id/id/' . $this->investor_combination_id . '/', ],
			['investor_combination_id' => $this->investor_combination_id]
		)

			->setTypes([
				'investor' => [
					'title' => 'Belegger',
					'width' => 'xxxlarge truncate',
				],
				'investor_abbreviation' => [
					'title' => 'Belegger afkorting',
					'width' => 'small truncate',
				],
				'percentage' => [
					'title' => 'Percentage',
					'width' => 'large right',
				]
			])

			->addFormat('investor', function($value, $item){
				return $value > 0 ? $item['investor_name'] : '';
			})

			->setOptions([
				'show_title' => false,
				'show_view_template_buttons' => false
			])

			->render($this)
		;
	}

	public function editAction(){
		$this->view->Breadcrumbs()
			->addCrumb('Belegger combinaties', 'investor_combination/list/')
			->addCrumb('Combinatie details', 'investor_combination/details/id/' . $this->investor_combination_id . '/')
			->addCrumb('Belegger toevoegen');

		$this->view->EditView(
			$this,
			'Investors_Combination_Link',
			['id' => $this->getParam('id')]
		)
			->setOptions([
				'postHandler' => function($post, $params){
					$model = new Investors_Combination_Link();
					$model->save($post, $params);

					$ic_model = new Investors_Combination();
					$ic_model->updateName($params['investor_combination_id']);
					$ic_model->updatePrimaryInvestor($params['investor_combination_id']);

					return true;
				}
			])
			->render($this);
	}

	public function deleteAction(){
		$this->disableView();

		$combination_link_model = new Investors_Combination_Link();

		$combination_link_model->delete('id = ' . $this->getParam('id'));

		$ic_model = new Investors_Combination();
		$ic_model->updateName($this->investor_combination_id);
		$ic_model->updatePrimaryInvestor($this->investor_combination_id);

		$ic_model
			->getById($this->investor_combination_id)
			->setFromArray(['concept' => true])
			->save();

		$this->_redirect('investor_combination/details', ['id' => $this->investor_combination_id]);
	}
}