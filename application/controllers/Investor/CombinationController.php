<?php

class Investor_CombinationController extends GlobalController
{
	public function preDispatch()
	{
		$this->view->Breadcrumbs()->addCrumb('Belegger combinatie');
	}

	private function getList()
	{
		$select = db()->select()
			->from(['il' => 'investor_combination'], ['id', 'name', 'concept'])
			->joinLeft(['og' => 'objectgroup'], 'og.investor_combination_id = il.id', ['og_id' => 'id'])
            ->joinLeft(['o' => 'objects'], 'o.investoroverride_combination_id = il.id', ['o_id' => 'id'])
			->group('il.id');
		;

		$list = db()->fetchAll($select);

		foreach($list as $item_key => $item) {
			if ($item['og_id'] > 0) {
                $list[$item_key]['status'] = 'attached_to_og';
                $list[$item_key]['listview_row_classes'] = ['status', 'green'];
            } elseif ($item['o_id'] > 0) {
                $list[$item_key]['status'] = 'attached_to_object';
                $list[$item_key]['listview_row_classes'] = ['status', 'green'];
			} elseif ($item['concept']) {
				$list[$item_key]['status'] = 'concept';
				$list[$item_key]['listview_row_classes'] = ['status', 'orange'];
			} else {
				$list[$item_key]['status'] = 'ready';
				$list[$item_key]['listview_row_classes'] = ['status', 'gray'];
			}
		}

		return $list;
	}

	public function listAction(){

		$this->view->list = $this->view->ListView($this->getList(), $this->getAllParams())
			->setTypes([
				'name' => ['title' => 'Combinatie', 'width' => 'xxxlarge truncate'],
				'status' => ['title' => 'Status', 'width' => 'xlarge']
			])

			->setOptions([
				'item_title' => 'Belegger combinatie',

				'disabled_buttons_test' => function($row, $button){
					if($button == 'delete')
						return $row['og_id'] > 0;

					return false;
				}
			])

			->addFormat('status', function($value){
				$statusses = [
                    'attached_to_object' => 'Gekoppeld aan object',
					'attached_to_og' => 'Gekoppeld aan objectgroep',
					'ready' => 'Volledig ingevoerd, niet gekoppeld',
					'concept' => 'Invoer incompleet',
				];

				return $statusses[$value] ?: $statusses['concept'];
			})


			->addButtons([
				'add' => 'Toevoegen',
				'details' => 'Details',
				'delete' => 'Verwijderen',
			])

			->setFilters([
				'name' => ['type' => 'input'],
				'status' => []
			])
			->render($this);
	}

	public function detailsAction(){
		$this->view->Breadcrumbs()
			->addCrumb('Belegger combinaties', 'investor_combination/list/')
			->addCrumb('Combinatie details');

		$id = $this->getParam('id');

		$model = new Investors_Combination();

		$this->view->isEditable = (new Investors\Combination\Service\IsEditableService())->check($id);
		$this->view->data = $model->getById($id);
		$this->view->id = $id;

	}

	public function editAction(){
		$this->id = $id = $this->getParam('id') ?: false;

		if($id === false){
			$model = new Investors_Combination();
			$new_combination_id = $model->createRow()->save();
			$this
				->getHelper('Redirector')
				->gotoUrl('investor_combination/details/id/' . $new_combination_id . '/');
		}

		$this->view->Breadcrumbs()
			->addCrumb('Belegger combinaties', 'investor_combination/list/')
			->addCrumb('Combinatie details', 'investor_combination/details/id/' . $id . '/')
			->addCrumb('Combinatie bewerken');

		$this->view->EditView(
			$this,
			'Investors_Combination',
			['id' => $this->getParam('id')]
		)
			->render($this);
	}

	public function deleteAction(){
		$this->disableView();

		$combination_link_model = new Investors_Combination_Link();
		$combination_model = new Investors_Combination();

		$combination_link_model->delete('investor_combination_id = ' . $this->getParam('id'));
		$combination_model->delete('id = ' . $this->getParam('id'));

		$this->_redirect('list');
	}
}