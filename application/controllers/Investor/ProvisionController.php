<?php

class Investor_ProvisionController extends GlobalController
{
	private $investor_id;

	public function preDispatch()
	{
		if(loginManager::data()->rights == 'investor'){
			$this->investor_id = loginManager::data()->info['investor_company_id'];
		} else {
			$this->investor_id = $this->_getParam('investor_id');
		}
	}

	public function supportListAction(){


        
		$this->view->Breadcrumbs()->addCrumb('Huurafrekeningen');
		$params['view_template'] = 'support';

		$list_model = new Controllers_Investor_Provision_List();
		$list_model->setInvestorId($this->investor_id);
		$data = $list_model->getList();

		$this->view->list = $this->view->listView($data, ['url-override' => 'investor_provision/support-list', 'investor_id' => $this->investor_id, 'view_template' => $this->_getParam('view_template')])

			->setTypes([
				'period' => ['title' => 'Afrekening', 'width' => 'small truncate'],
				'date' => ['title' => 'Datum', 'width' => 'xxsmall'],
				'amount' => ['title' => 'Bedrag', 'width' => 'xxsmall right'],
				'spacer' => ['title' => ' ', 'width' => 'xxxxsmall'],
				'closed' => ['title' => 'Betaald', 'width' => 'xsmall'],
				'invoice' => ['title' => 'Factuur', 'width' => 'small']
			])

			->addFormat('date', 'date')

			->addFormat('invoice', function($value, $row){
				if($row['invoice'] > 0)
					return
						'<a target ="_blank" href="invoice/export/id/' . $row['invoice'] . '">' .
						'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
						$row['identifier'] .
						'</a>';
				else
					return '-';
			})

			->addFormat('period', function ($value, $row) {
				return
					'<a target ="_blank" href="investor_provision/download/id/' . $row['id'] . '">' .
					'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
					ucfirst(strftime('%B %Y', strtotime($value))) .
					'</a>';
			})

			->addFormat('amount', 'money_from_int_db')

			->addFormat('closed', function($value, $item){
				if(is_null($value)) return '-';

				$value = $value  === true || $value === 'true' || $value === '1' || $item['unpayed'] <= 0;

				return $value ? 'Ja' : 'Nee';
			})

			->setOptions([
				'item_title' => 'Afrekening',
				'paginator_items_per_page' => 5,
				'show_title' => true,
			])

			->render($this);
	}

	public function listAction()
	{
		$this->view->headLink()->appendStylesheet('media/style/transaction/investor-provision.css');
		$this->view->headScriptHashed()->appendFile('media/javascript/investor/provision-overview.js');

		$this->view->Breadcrumbs()->addCrumb('Beheerinkomsten overzicht');

		$list_model = new Controllers_Investor_Provision_List();
		$list_model->setFetchProvisionOnly(true);


        if($this->getParam('period_filter_type') === 'period'){
            $list_model->setFilterPeriod(
                $this->getParam('month'),
                $this->getParam('year')
            );
        } elseif($this->getParam('period_filter_type') === 'invoicedate') {
            $list_model->setFilterInvoiceDate(
                $this->getParam('invoicedate_range_start'),
                $this->getParam('invoicedate_range_end')
            );
        }

		$data = $list_model->getList();

		$filter_years = $list_model->getFilterYearsFromData($data);
		$filter_months = $list_model->getFilterMonths();


		if(!$this->isAjaxRequest) {
            $this->setParam('year', date('Y'));
            $this->setParam('month', date('m'));
            $this->setParam('invoicedate_range_start', date('d-m-Y', mktime(0, 0, 0, date('m'), 1)));
            $this->setParam('invoicedate_range_end', date('d-m-Y', mktime(0, 0, 0, date('m') + 1, 0)));
        }


		$this->view->list = $this->view->listView($data, ['url-override' => 'investor_provision/list', 'investor_id' => $this->investor_id, 'view_template' => $this->_getParam('view_template')])

			->setTypes([
				'details-button' => ['title' => '', 'width' => 'xxxxsmall'],
				'investor' => ['title' => 'Belegger', 'width' => 'xlarge truncate'],
				'provision-pdf' => ['title' => 'Afrekening', 'width' => 'xlarge truncate'],
                'invoicedate' => ['title' => 'Factuurdatum', 'width' => 'small truncate'],
				'provision_amount' => ['title' => 'Beheerinkomsten', 'width' => 'large right'],
				'spacer' => ['title' => ' ', 'width' => 'xxxxsmall'],
				'invoice' => ['title' => 'Factuur', 'width' => 'xxlarge truncate'],
			])

			->addFormat('details-button', function($value, $row){
				return '<div class="details-button collapse closed' . ($row['has_data'] == '1' ? '' : ' disabled') . '">&nbsp;</div>';
			})

            ->addFormat('invoicedate', function($value){
                return date('d-m-Y', strtotime($value));
            })

			->addFormat('investor', function($value, $row){
				if($value > 0){
					return '<a href="support/show/type/investor/id/' . $value . '/">' . $row['investor_name'] . '</a>';
				} else {
					return '-';
				}
			})

			->addFormat('project', function($value, $row){
				if($value > 0){
					return $row['project_name'];
				} else {
					return '-';
				}
			})

			->addFormat('invoice', function($value, $row){
				if($row['invoice'] > 0)
					return
						'<a href="invoice/export/id/' . $row['invoice'] . '">' .
						'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
						$row['identifier'] .
						'</a>';
				else
					return '-';
			})

			->addFormat('provision-pdf', function ($value, $row) {
				return
					'<a href="investor_provision/download/id/' . $row['id'] . '">' .
					'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
					ucfirst(strftime('%B %Y', strtotime('01-' . $row['month'] . '-' . $row['year']))) .
					'</a>';
			})

			->addFormat('month', function ($value, $row) {
				return ucfirst(strftime('%B', strtotime('01-' . $value . '-' . date('Y'))));
			})

			->addFormat('provision_amount', 'money')

			->setFilters([
				'project' => ['renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'],
                'period_filter_type' => ['renderSeparately' => true, 'title' => 'Periode filter', 'custom_options' => ['invoicedate' => ['title' => 'Factuurdatum'], 'period' => ['title' => 'Factuurperiode']], 'custom_options_only' => true, 'show_all_disabled' => true, 'hideCount' => true, 'preApplied' => true],
				'year' => ['title' => 'Jaar', 'renderSeparately' => true, 'order_by_value' => 'DESC', 'custom_options' => $filter_years, 'hideCount' => true, 'custom_options_only' => true, 'show_all_disabled' => true, 'preApplied' => true],
                'invoicedate' => ['renderSeparately' => true, 'title' => 'Factuurdatum', 'type' => 'date_range', 'preApplied' => true],
				'month' => ['title' => 'Periode', 'renderSeparately' => true, 'order_by_key' => 'ASC', 'custom_options' => $filter_months, 'hideCount' => true, 'custom_options_only' => true, 'show_all_disabled' => true, 'preApplied' => true],
				'investor' => ['type' => 'input'],
				'provision_amount' => ['type' => 'range'],
				'invoice' => ['type' => 'input']
			])

			->addTotals(['provision_amount'])

			->setOptions([
				'item_title' => 'Beheerinkomsten'
			])

			->render($this);
	}

	public function invoiceAction()
	{
		$this->view->extraWidth = true;
		$this->view->headLink()->appendStylesheet('media/style/transaction/investor-provision.css');
		$this->view->headScriptHashed()->appendFile('media/javascript/investor/provision-overview.js');

		$this->view->Breadcrumbs()->addCrumb('Huurafrekeningen');

		$list_model = new Controllers_Investor_Provision_List();
		$data = $list_model->getList();
		$filter_years = $list_model->getFilterYearsFromData($data);

		if(is_null($this->getParam('year')))
			$this->setParam('year', $filter_years[0]);

		$this->view->list = $this->view->listView($data, ['url-override' => 'investor_provision/list', 'investor_id' => $this->investor_id, 'view_template' => $this->_getParam('view_template')])

			->setTypes([
				'details-button' => ['title' => '', 'width' => 'xxxxsmall'],
				'investor' => ['title' => 'Belegger', 'width' => 'xlarge truncate'],
				'provision-pdf' => ['title' => 'Afrekening', 'width' => 'xlarge truncate'],
				'amount' => ['title' => 'Bedrag', 'width' => 'medium right'],
				'spacer' => ['title' => ' ', 'width' => 'xxxxsmall'],
				'closed' => ['title' => 'Betaald', 'width' => 'medium'],
				'invoice' => ['title' => 'Factuur', 'width' => 'xlarge truncate']
			])

			->addFormat('details-button', function($value, $row){
				return '<div class="details-button collapse closed' . ($row['has_data'] == '1' ? '' : ' disabled') . '">&nbsp;</div>';
			})

			->addFormat('investor', function($value, $row){
				if($value > 0){
					return '<a href="support/show/type/investor/id/' . $value . '/">' . $row['investor_name'] . '</a>';
				} else {
					return '-';
				}
			})

			->addFormat('project', function($value, $row){
				if($value > 0){
					return $row['project_name'];
				} else {
					return '-';
				}
			})

			->addFormat('invoice', function($value, $row){
				if($row['invoice'] > 0)
					return
						'<a href="invoice/export/id/' . $row['invoice'] . '">' .
						'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
						$row['identifier'] .
						'</a>';
				else
					return '-';
			})

			->addFormat('provision-pdf', function ($value, $row) {
				return
					'<a href="investor_provision/download/id/' . $row['id'] . '">' .
					'<span class="fa fa-file-pdf-o" aria-hidden="true"></span>' .
					ucfirst(strftime('%B %Y', strtotime('01-' . $row['month'] . '-' . $row['year']))) .
					'</a>';
			})

			->addFormat('month', function ($value, $row) {
				return ucfirst(strftime('%B', strtotime('01-' . $value . '-' . date('Y'))));
			})

			->addFormat('amount', 'money_from_int_db')

			->addFormat('closed', function($value, $item){
				if(is_null($value)) return '-';

				$value = $value  === true || $value === 'true' || $value === '1' || $item['unpayed'] <= 0;

				return $value ? 'Ja' : 'Nee';
			})

			->setFilters([
				'project' => ['renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'],
				'year' => ['title' => 'Jaar', 'renderSeparately' => true, 'order_by_value' => 'DESC', 'custom_options' => $filter_years, 'hideCount' => true, 'custom_options_only' => true, 'show_all_disabled' => true],
				'month' => ['renderSeparately' => true, 'title' => 'Periode', 'order_by_value' => 'ASC', 'hideCount' => true],
				'investor' => ['type' => 'input'],
				'amount' => ['type' => 'range'],
				'closed' => [],
				'invoice' => ['type' => 'input']
			])

			->setOptions([
				'item_title' => 'Afrekening'
			])

			->render($this);
	}

	public function detailsAction(){
		$id = $this->getParam('id');
		$data['investor'] = [];

		$data_json = db()->fetchOne(db()->select()->from('investor_provision_send', ['data'])->where('id = ?', $id));
		$this->view->data = json_decode($data_json, true);
	}

	public function downloadAction(){
		$this->disableView();

		if(is_null($this->getParam('id'))) return;

		$model = new InvestorProvisionsSend();
		$model->downloadPdf($this->_getParam('id'));
	}

    public function getCurrentPeriodForObjectAction()
    {
        $this->disableView();

        $objectId = $this->getParam('object_id');

        if (!$objectId) {
            throw new InvalidArgumentException('Invalid argument for object_id: ' . var_export($objectId, true));
        }

        $service = new \Investors\Provision\GetCurrentPeriodForObjectService();
        $periodValue = $service->execute($objectId);

        echo json_encode($periodValue);
    }
}
