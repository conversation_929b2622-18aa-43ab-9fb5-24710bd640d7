<?php

class Investor_AbbreviationBuilder {

	private $investor_name;
	private $length = 4;
	private $abbreviation;

	public function __construct($investor_name)
	{
		$this->setInvestorName($investor_name);
		$this->filterInvestorName();
	}

	public function setLength($length)
	{
		$this->length = $length;
	}

	public function execute(){

		$this->addFirstCharactersForInvestorName();
		$this->addAdditionalCharactersFromInvestorName();
		$this->addAdditionalRandomCharacters();

		return $this->getAbbreviation();
	}


	private function getInvestorName()
	{
		return $this->investor_name;
	}

	private function setInvestorName($investor_name)
	{
		$this->investor_name = $investor_name;
	}

	private function getAbbreviationLength()
	{
		return $this->length;
	}

	private function filterInvestorName()
	{
		$investor_name = $this->getInvestorName();

		$investor_name = str_replace('.', ' ', $investor_name);
		$investor_name = str_replace('/', ' ', $investor_name);

		$this->setInvestorName($investor_name);
	}

	private function getInvestorNameParts(){
		return explode(' ', $this->getInvestorName());
	}

	private function addFirstCharactersForInvestorName(){
		$investor_name_parts = $this->getInvestorNameParts();

		$first_letters = array_map(function($investor_name_part){
			return $investor_name_part[0];
		}, $investor_name_parts);

		$this->addCharactersToAbbreviation($first_letters);
	}

	private function addAdditionalCharactersFromInvestorName(){
		$investor_name_parts = $this->getInvestorNameParts();

		$additional_letters = str_split(implode('', array_map(function($investor_name_part){
			return substr($investor_name_part, 1);
		}, $investor_name_parts)));

		$this->addCharactersToAbbreviation($additional_letters);
	}

	private function addAdditionalRandomCharacters()
	{
		while(!$this->abbreviationIsComplete()) {
			$this->addCharacterToAbbreviation(chr(rand(97, 122)));
		}
	}

	private function abbreviationIsComplete(){
		return strlen($this->abbreviation) >= $this->getAbbreviationLength();
	}

	private function addCharactersToAbbreviation($characters){
		foreach($characters as $character)
			$this->addCharacterToAbbreviation($character);
	}

	private function addCharacterToAbbreviation($character){

		if($this->abbreviationIsComplete()) return;

		if(!$this->isValidCharacter($character)) return;

		$this->abbreviation .= strtoupper($character);
	}

	private function isValidCharacter($character){
		return ctype_alpha($character);
	}

	private function getAbbreviation()
	{
		return $this->abbreviation;
	}
}