<?php

class Investor_ContactCategoriesController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Belegger portal contact categorieën', '/investor_contact-categories');

        return parent::preDispatch();
    }

    public function indexAction() {
        $this->_helper->redirector('list', 'investor_contact-categories');
    }

    public function listAction() {
        $this->view->Breadcrumbs()
            ->addCrumb('Contact categorieën overzicht', '');

        $model = new InvestorContactCategories();

        $this->view->listView($model->getList())

            ->setTypes([
                'name_nl' => [
                    'title' => 'Categorie',
                    'width' => 'xxxxlarge truncate',
                ],
                'address' => [
                    'title' => 'Email account',
                    'width' => 'xxxlarge truncate',
                ],
            ])
            ->setOptions([
                'item_title' => 'Categorie',
                'disabled_buttons_test' => function($row, $button){
                    if($button !== 'delete')
                        return false;

                    if(intval($row['id']) < 1)
                        return true;

                    return false;
                }
            ])

            ->addButtons(
                [
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen',
                ],
                [
                    'id' => 'id',
                ]
            )

            ->render($this);
    }

    public function deleteAction() {
        $this->disableView();
        $id = $this->_getParam('id');

        $model = new InvestorContactCategories();

        if(intval($id) < 1)
            $this->_helper->redirector('list', 'investor_contact-categories');

        $model
            ->getById($id)
            ->setFromArray(['deleted' => true])
            ->save();

        $this->_helper->redirector('list', 'investor_contact-categories');
    }

    public function editAction() {

        $this->view->Breadcrumbs()
            ->addCrumb('Categorie '.($this->_getParam('id') ? 'bewerken' : 'toevoegen') ,'');


        $this->view->EditView($this, 'InvestorContactCategories', ['id' => $this->_getParam('id')])
            ->render($this);
    }
}