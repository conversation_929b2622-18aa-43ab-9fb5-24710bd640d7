<?php

class Investor_Provision_SourceDataController extends GlobalController
{
    public function indexAction()
    {
        $select = db()->select()
            ->from(['o' => 'objects'], ['object' => 'id', 'object_address' => 'rendered_address'])
            ->joinLeft(['uo' => 'users_objects'], 'uo.object = o.id AND uo.role = "normal"', false)
            ->joinLeft(['u' => 'users'], 'u.id = uo.customer', ['user' => 'id', 'user_name' => 'rendered_name'])
            ->joinLeft(['t' => 'transactions'], 't.user = u.id', false)
            ->joinLeft(['i' => 'invoices'], 'i.id = t.invoice', [
                'invoice' => 'id',
                'invoice_identifier' => 'CONCAT(ir.identifier, ".", COALESCE(IF(CHAR_LENGTH(i.identifier) < 4, LPAD(i.identifier,4,"0"), i.identifier), "[xxxx]"))',
            ])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', ['expire_date', 'start'])
            ->joinLeft(['io' => 'invoice_objects'], 'io.invoice = i.id', false)
            ->joinLeft(['ic' => 'invoice_custom'], 'i.customid = ic.id', false)
            ->joinLeft(
                ['irows' => 'invoice_rows'],
                'irows.invoice = t.invoice AND ' .
                'irows.is_input = "1" AND ' .
                'COALESCE(irows.object, io.object, ic.object)',
                [
                    'invoice_row_description' => 'description',

                ]
            )
//            ->joinLeft(
//                ['o2' => 'objects'],
//                'o2.id = COALESCE(irows.object, io.object, ic.object)',
//                ['object' => 'id', 'object_address' => 'rendered_address']
//            )
            ->limit(1000)
            ->where('u.id IS NOT NULL')
            ;

        $data = db()->fetchAll($select);

        // object
        // huurder
        // factuurregels voor dat object, of handmatige facturen voor object
        // vervaldatum, boekperiode
        //


//        p($data);

        $this->view->ListView($data, [])
            ->setTypes([
                'object' =>  ['title' => 'Object', 'width' => 'xxsmall'],
                'user' =>  ['title' => 'Huurder', 'width' => 'xxsmall'],
                'invoice' => ['title' => 'Factuur', 'width' => 'xxsmall'],
                'invoice_row_description' => ['title' => 'Component', 'width' => 'xxsmall'],
            ])
            ->addFormat('object', function ($value, $item) {
                return $item['object_address'];
            })
            ->addFormat('invoice', function ($value, $item) {
                return $item['invoice_identifier'];
            })
            ->addFormat('user', function ($value, $item) {
                $counterHtml = '';
                if($item['numberOfActiveDealsForUser'] > 1){
                    $title = ucfirst(translate()->_('other_active_deals')) . ': ' .
                        ($item['numberOfActiveDealsForUser'] - 1);

                    $counterHtml =
                        '<div noHint="1" class="otherDealCounter" title="' . $title . '">' .
                        ($item['numberOfActiveDealsForUser'] - 1) .
                        '</div>';
                }

                return
                    '<a href="rental-deals_timeline/for-user/id/' . $item['user_id'] . '/"  >' .
                    $value . ' ' . $counterHtml .
                    '</a>';
            })

            /*
            ->addFormat('checksum', function ($value) {
                return '<span class="checksum">' . $value . '</span>';
            })
            ->addFormat('status_checksum', function ($value) {
                return '<span class="status_checksum">' . $value . '</span>';
            })
            ->addFormat('from', function ($value, $row) {
                return $value ? date('d-m-Y', strtotime($value)) : '-';

            })
            ->addFormat('contact-date', function ($value, $row) {
                if ($row['assigned_user_id'] !== loginManager::data()->id) {
                    return $value ? date('d-m-Y', strtotime($value)) : '-';
                } else {
                    $dom = new DOMDocument('1.0');

                    $select = $dom->createElement('input');
                    $select->setAttribute('class', 'contact_date');
                    $select->setAttribute('type', 'date');
                    $select->setAttribute('value', $value);
                    $dom->appendChild($select);
                    return $dom->saveHTML();
                }
            })
            */
            ->addDetails('rental-deals/details', ['style' => 'popup'])
            //->addLegend('status-text')
            ->setFilters([
                'object_id' => ['type' => 'json_array', 'preApplied' => true],
                'user_id' => ['type' => 'json_array', 'preApplied' => true],
                'object' => ['type' => 'input'],
                'investor_name' => [
                    'type' => 'select',
                    'title' => ucfirst(translate()->_('investor')),
                    'renderSeparately' => true,
                    'order_by_title' => 'ASC',
                ],
                'user' => ['type' => 'input'],
                'status_type_id' => [
                    'renderSeparately' => true,
                    'title' => ucfirst(translate()->_('workflow_step')),
                    'hideCount' => true,
                    'custom_options' => [],
                    'custom_options_only' => true,
                    'preApplied' => true,
                    'show_all_disabled' => true,
                    'noAjax' => true
                ],
            ])
            ->setOptions([
                'item_title' => 'Deal',
                'paginate' => false,
            ])
            ->render($this);

    }

}
