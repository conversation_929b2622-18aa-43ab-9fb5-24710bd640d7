<?php

use Investors\Provision\Period as Period;
use Investors\Provision\Preview as Preview;
use Investors\Provision\Preview\AddNewService as AddNewService;
use Investors\Provision\Preview\BulkRecalculateService;
use Investors\Provision\Preview\MarkForRecalculationService as MarkForRecalculationService;
use Investors\Provision\Preview\IndexActionHelper as IndexActionHelper;
use Investors\Provision\Preview\RemoveOldService;
use Investors\Provision\Preview\StatusTypes;
use Investors\Provision\Preview\UpdateListService as UpdateListService;

class Investor_Provision_PreviewController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Beleggerafrekeningen');
    }

    /**
     * @throws Exception
     */
    public function indexAction()
    {
        $this->view->extraWidth = true;
        $this->useParamCache('investor_provision_preview');

        $period = Period::fromUrlParameter($this->getParam('period') ?: 0);
        $this->setParam('period', $period->getDateString());
        $this->view->period = $this->getParam('period');

        if (is_null($this->getParam('list_filter_type')) && !$this->isAjaxRequest) {
            $this->setParam('list_filter_type', Preview\ListFilterTypes::DUE);
        }

        $listData = new IndexActionHelper();
        $data = $listData->getList($period,  $this->getParam('list_filter_type'));

        $amountColumns = ['due', 'payments', 'shared_amount', 'amount_for_provision', 'provision', 'discount', 'costs', 'total'];

        $periodOffsetValue = $period->getPeriodOffsetValue();
        $statusLabels = StatusTypes::getStatusLabels();
        $projectNames = $listData->getProjectNames($data);

        $this->view->ListView($data, [])
            ->setTypes([
                'provision_send_count' => ['title' => '', 'width' => 'xxxxsmall provision_send_count'],
                'investor' => ['title' => 'Belegger', 'width' => 'xxlarge truncate'],
                'calculated_at' => ['title' => 'Bijgewerkt', 'width' => 'medium'],
                'due' => ['title' => 'Te betalen', 'width' => 'xsmall right'],
                'payments' => ['title' => 'Ontvangsten', 'width' => 'xsmall right'],
                'shared_amount' => ['title' => 'Doorstorten', 'width' => 'xsmall right'],
                'amount_for_provision' => ['title' => 'Provisie grondslag', 'width' => 'xsmall right'],
                'provision' => ['title' => 'Provisie incl', 'width' => 'xsmall right'],
                'discount' => ['title' => 'Korting', 'width' => 'xsmall right'],
                'costs' => ['title' => 'Kosten', 'width' => 'xsmall right'],
                'total' => ['title' => 'Uit te keren', 'width' => 'xsmall right'],
                'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
                'period_shift_button' => ['title' => 'Doorschuiven', 'width' => 'xsmall'],
                'preview_buttons' => ['title' => 'Preview', 'width' => 'xsmall preview_buttons'],
                'period' => ['title' => 'Periode', 'width' => 'hidden'],
                'project' => ['title' => 'Project', 'width' => 'hidden'],
                'status' => ['title' => 'Status', 'width' => 'hidden'],
                'manage_type' => ['title' => 'Type beheer', 'width' => 'hidden'],
                'payment_day' => ['title' => 'Uitkeringsdag', 'width' => 'hidden'],
            ])
            ->addFormat('provision_send_count', function ($value) {
                $isFirst = $value == 0;
                return '
                    <div class="' . ($isFirst ? 'first' : '') . '" nohintbutton="1" title="' . ($isFirst ? 'Eerste afrekening' : 'Aanvullende uitkering') . '">
                        ' . ($value + 1) . '<sup>e</sup>
                    </div>                
                ';
            })
            ->addFormat($amountColumns, function($value){
                    return '&euro; ' .  new StringFormat($value, 'money');
                })
            ->addFormat('manage_type', function ($value) {
                if ($value === 'technical_and_financial') {
                    return 'Technisch en financieel beheer';
                }
                if ($value === 'technical') {
                    return 'Enkel technisch beheer';
                }
                if ($value === 'financial') {
                    return 'Enkel financieel beheer';
                }

                return '-';
            })
            ->addFormat('status', function ($value, $item) use ($statusLabels) {

                if(isset($statusLabels[$value])){
                    return $statusLabels[$value];
                }

                return '-';
            })
            ->addFormat('calculated_at', function ($value, $item) use ($statusLabels) {
                $loadingStatusses = [
                    StatusTypes::DUE_FOR_RECALCULATE,
                    StatusTypes::RECALCULATING,
                    StatusTypes::DUE_FOR_FINALIZE,
                    StatusTypes::FINALIZING,
                ];

                if (in_array($item['status'], $loadingStatusses)) {
                    return '<div class="spinnerBox"></div>' . $statusLabels[$item['status']] . '..';
                }

                if (!$value) {
                    return '-';
                }

                return strftime('%d %b om %H:%M', strtotime($value));
            })
            ->addFormat('period', 'date')
            ->addFormat('investor', function ($value, $item) {
                if (!$value) {
                    return '-';
                }

                $warningElement = '';
                if($item['warnings']) {
                    if(
                        count($item['warnings']['balance']) > 0 ||
                        $item['warnings']['missing_previous_period'] ||
                        $item['warnings']['unpaid_investor_invoices'] ||
                        $item['warnings']['has_no_objects'] ||
                        $item['warnings']['provision_for_empty_periods']
                    ){
                        $this->view->warnings = $item['warnings'];
                        $view = $this->view->render('transaction/provision/warnings.phtml');

                        $warningElement = '<span
                        nohintbutton="true"
                        class="fas fa-exclamation-triangle warnings"
                        title="' . $view . '"
                        ></span>';
                    }
                }

                return implode([
                    '<a href="support/show/type/investor/id/' . $value . '/" target="_blank">',
                    $item['investor_name'],
                    $warningElement,
                    '</a>'
                ]);
            })
            ->addFormat('period_shift_button', function($value, $item) use ($periodOffsetValue) {
                $projectId = $this->getParam('project');
                if(
                    ($periodOffsetValue > 0 || $item['provision_send_count'] > 0) &&
                    abs($item['payments']) > 0
                ){
                    return implode([
                        '<a href="transaction/investor-provision-shift/',
                        'investor/' . $item['investor'] . '/',
                        'period_offset/' . $periodOffsetValue . '/',
                        $projectId ? 'project_id/' . $projectId . '/' : '',
                        '" title="Doorstort handmatig doorschuiven naar huidige afrekenperiode voor deze belegger">',
                        '<i class="fas fa-fast-forward"></i>',
                        '</a>'
                    ]);
                } else {
                    return '<i class="fa fa-ellipsis-h"></i>';
                }
            })
            ->addFormat('preview_buttons', function ($value, $item) use ($period) {
                $projectId = $this->getParam('project');
                $baseUrl = implode([
                    'transaction/investor-provision-preview/investor/' . $item['investor'] . '/',
                    'period_offset/' . $period->getPeriodOffsetValue() . '/' .
                    ($projectId ? 'project-id/' . $projectId . '/' : ''),
                ]);

                $buttonString = '<a target="_blank" href="' . $baseUrl . 'stationery/full/"><i class="fas fa-file-pdf"></i></a> ';
                $buttonString .= '<a target="_blank" href="' . $baseUrl . '"><i class="fas fa-file-alt"></i></a>';

                return $buttonString;
            })
            ->addFormat('payment_day', function($value){
                return is_numeric($value) && $value > 0 ? $value : '-';
            })
            ->addFormat('project', function ($value) use ($projectNames) {
                if (!isset($projectNames[$value])) {
                    return '-';
                }

                return $projectNames[$value];
            })
            ->addActions([
                'disabled_check' => function ($row) use ($statusLabels) {
                    return
                        $row['status'] === $statusLabels[StatusTypes::READY] ||
                        loginManager::data()->id == Users::OMNIBOXX_ADMIN_USER_ID;
                },
                'buttons' => [
                    [
                        'title' => 'Bijwerken',
                        'icon' => 'refresh',
                        'action' => 'investor_provision_preview/bulk-recalculate'
                    ],

                    [
                        'title' => 'Direct versturen',
                        'icon' => 'tick',
                        'action' => 'investor_provision_preview/mark-for-finalize/direct/1/',
                        'params' => ['project'],
                        'confirm' => true,
                    ],

                    [
                        'title' => 'Concept facturen maken',
                        'icon' => 'tick',
                        'action' => 'investor_provision_preview/mark-for-finalize/',
                        'params' => ['project'],
                        'confirm' => true,
                    ]
                ]
            ])
            ->addDetails('investor_provision_preview/details/',
                [
                    'style' => 'collapse',
                    'filter_values_as_parameter' => ['project']
                ]
            )
            ->addLegend('index-legend')
            ->setFilters([
                'period' => [
                    'renderSeparately' => true,
                    'title' => 'Periode',
                    'custom_options' => $listData->getPeriodFilterValues(),
                    'hideCount' => true,
                    'preApplied' => true,
                    'custom_options_only' => true,
                ],
                'project' => [
                    'renderSeparately' => true,
                    'title' => 'Project',
                    'custom_options' => $listData->getProjectFilterValues($projectNames),
                ],
                'payment_day' => [
                    'renderSeparately' => true,
                    'title' => 'Uitkeringsdag',
                    'order_by_value' == 'ASC',
                ],
                'list_filter_type' => [
                    'renderSeparately' => true,
                    'title' => 'Afrekeningen tonen',
                    'custom_options' => [
                        Preview\ListFilterTypes::DUE => ['title' => 'Nu af te rekenen'],
                        Preview\ListFilterTypes::DUE_WITH_PAYMENTS => ['title' => 'Afrekeningen met ontvangsten'],
                        Preview\ListFilterTypes::DUE_WITH_PAYMENTS_HIGHER_THEN_DUE => ['title' => 'Afrekeningen met ontvangsten groter (of gelijk aan) te betalen'],
                    ],
                    'hideCount' => true,
                    'preApplied' => true,
                    'custom_options_only' => true,
                ],
                'manage_type' => [
                    'renderSeparately' => true,
                    'title' => 'Type beheer',
                ],
                'investor' => ['type' => 'input'],
            ])
            ->addTotals($amountColumns)
            ->setOptions([
                'item_title' => 'Beleggersafrekening',
                'paginator' => false,
                'auto_refresh' => true,
            ])
            ->render($this);

    }

    public function detailsAction()
    {
        $previewId = $this->getParam('id');
        $projectId = $this->getParam('project');

        $this->disableView();
        $investorProvisionPreviewModel = new InvestorProvisionPreview();
        $investorProvisionPreviewModelRow = $investorProvisionPreviewModel->getById($previewId);

        $transactionController = new TransactionController($this->_request, $this->_response);

        $transactionController->setParam('investor', $investorProvisionPreviewModelRow->investor_id);
        $transactionController->setParam(
            'period_offset',
            Period::fromDateString($investorProvisionPreviewModelRow->period)->getPeriodOffsetValue()
        );

        if ($projectId && Settings::get('investor_provision_filter_project')) {
            $transactionController->setParam('project-id', $projectId);
        }

        $transactionController->investorProvisionDetailsAction();
    }

    public function markForFinalizeAction(){
        $this->disableView();

        $ids = $this->getParam('ids');
        $project = $this->getParam('project');
        $direct = $this->getParam('direct');

        if(!is_array($ids)){
            return;
        }

        $markForFinalizeService = new Preview\MarkForFinalizeService(
            new InvestorProvisionPreview()
        );
        foreach($ids as $investorPreviewId){
            $markForFinalizeService->execute(
                $investorPreviewId,
                $project,
                $direct
            );
        }
    }

    public function finalizeAction()
    {
        $this->disableView();

        $investorPreviewId = $this->getParam('id');

        $pidLock = new PIDLock('InvestorProvisionPreviewFinalize', ['id' => $investorPreviewId]);

        $projectId = $this->getParam('project');
        $direct = $this->getParam('direct');

        if (!Settings::get('investor_provision_filter_project')) {
            $projectId = null;
        }

        $finalizeService = new Preview\FinalizeService(
            new InvestorProvisionPreview(),
            new Investors(),
            new Preview\UpdatePreviewsForInvestorService(
                new MarkForRecalculationService(
                    new InvestorProvisionPreview()
                )
            )
        );

        $finalizeService->execute(
            $investorPreviewId,
            $projectId,
            $direct
        );

        $pidLock->remove();
    }

    /**
     * @throws Exception
     */
    public function updateListAction()
    {
        $this->disableView();

        if (!is_cli_call()) {
            startWorker(
                'update-list/period/' . ($this->getParam('period') ?: 0) . '/',
                'investor_provision_preview',
                'direct',
                true
            );
            header('Location: ' . $_SERVER['HTTP_REFERER']);
            return;
        }


        $period = Period::fromUrlParameter($this->getParam('period') ?: 0);

        $pidLock = new PIDLock('InvestorProvisionPreviewUpdateList', ['period' => $period->getDateString()]);

        $markForRecalculationService = new MarkForRecalculationService(
            new InvestorProvisionPreview()
        );
        $investorProvisionPreviewModel = new InvestorProvisionPreview();

        $updateListService = new UpdateListService(
            new Investors(),
            new BulkRecalculateService($markForRecalculationService),
            new AddNewService($investorProvisionPreviewModel),
            new RemoveOldService($investorProvisionPreviewModel)
        );

        $updateListService->execute($period);

        $pidLock->remove();
    }

    /**
     * @throws Exception
     */
    public function bulkRecalculateAction()
    {
        $this->disableView();

        $ids = $this->getParam('ids');

        Logger::add(['investor','provision'], 'Recalculate', $ids);
        $markForRecalculationService = new MarkForRecalculationService(
            new InvestorProvisionPreview()
        );

        $bulkRecalculateService = new BulkRecalculateService($markForRecalculationService);
        $bulkRecalculateService->execute($ids);
        Logger::add(['investor','provision'], 'Done');
    }

    /**
     * @throws Exception
     */
    public function addAction()
    {
        $this->disableView();

        $investorId = $this->getParam('investor_id');
        $period = $this->getParam('period');

        $addNewService = new AddNewService(
            new InvestorProvisionPreview()
        );

        $addNewService->execute(
            $investorId,
            $period
        );

    }

    public function recalculateListAction()
    {
        $this->disableView();

        $ids = null;
        if ($this->getParam('ids') === null) {
            throw new InvalidArgumentException('Missing argument: ids');
        }

        $ids = explode(',', $this->getParam('ids'));

        if (!$ids || !is_array($ids)) {
            throw new InvalidArgumentException('Missing argument: ids');
        }

        foreach ($ids as $id) {
            startWorker(
                'recalculate/id/' . $id . '/',
                'investor_provision_preview',
                'direct',
                true
            );
        }
    }

    public function recalculateAction()
    {
        $this->disableView();

        if ($this->getParam('id') === null) {
            throw new InvalidArgumentException('Missing argument: id');
        }

        $id = $this->getParam('id');

        $pidLock = new PIDLock('InvestorProvisionPreviewUpdateListItem', ['id' => $id]);

        $investorProvisionPreviewModel = new InvestorProvisionPreview();

        $preview = new Preview(
            $investorProvisionPreviewModel,
            new Preview\Projects(new InvestorProvisionPreviewProjects()),
            new Preview\ListData(new InvestorProvisionPreviewListData()),
            new Investors()
        );

        $preview->calculate($id);

        $pidLock->remove();
    }

}
