<?php

use Sms\Application\Service\ViewBalanceService;
use Sms\Application\Service\SendSmsRequest;
use Sms\Application\Service\SendSmsService;
use Sms\Application\Service\ViewListService;

class SmsController extends GlobalController
{
    public function listAction()
    {
        if ($this->_getParam('ajax')) {
            $this->_helper->layout->disableLayout();
        }

        $userId = $this->_getParam('user');
        $page = $this->_getParam('page', 1);
        $limit = $this->_getParam('limit', 5);

        $viewListService = new ViewListService();

        if ($userId) {
            $smsSelectStatement = $viewListService->getSelectMessagesByUserStatement($userId);
        } else {
            $smsSelectStatement = $viewListService->getSelectMessagesStatement();
        }

        $paginator = Zend_Paginator::factory($smsSelectStatement);
        $paginator->setCurrentPageNumber($page);
        $paginator->setItemCountPerPage($limit);

        if (!$userId) {
            $viewListService->populateWithRecipients($paginator);
        }

        $this->view->messages = $paginator;
        $this->view->page = $page;
        $this->view->limit = $limit;
        $this->view->userId = $userId;
        $this->view->ajaxParam = $this->_getParam('ajax');
    }

    public function composeAction()
    {
        if (!Settings::get('sms_enabled')) {
            $this->_redirect('sms/not-enabled');
        }

        $this->view->headScriptHashed()->appendFile('media/javascript/RecipientSelector.js');
        $this->view->headLink()->appendStylesheet('media/style/sms.css');

        $userModel = new User();

        if ($userId = $this->_getParam('user')) {
            $userModel->user = $userId;

            $profile = $userModel->getProfile();
            if ($profile['general']['type'] == 'company') {
                $profile = $userModel->getCommercialProfile();
            }

            $name = trim($profile['general']['fullname']);
            $phone1 = trim($profile['general']['phone_home']);
            $phone2 = trim($profile['general']['phone_secondary']);

            if (substr($phone1, 0, 2) == '06') {
                $mobile = $phone1;
            } else {
                if (substr($phone2, 0, 2) == '06') {
                    $mobile = $phone2;
                } else {
                    $this->render('no_mobile_number');
                }
            }

            $user = [
                $userId,
                "{$name} ({$mobile})",
            ];

        } else {
            $user = false;
        }

        $this->view->hideHeader = true;
        $this->view->user = $user;
    }

    public function notEnabledAction()
    {
        $this->view->hideHeader = true;

        $editView = $this->view->EditView($this, 'sms-settings', ['modeloverride' => 'Settings'])
            ->setOptions([
                'render_to_controller' => false,
                'show_title' => false
        ]);

        $this->view->form = $editView->render($this);
    }

    public function sendAction()
    {
        $this->disableView();

        $subject = $this->getParam('subject');
        $message = $this->getParam('message');
        $userIds = $this->getParam('users');
        $phoneNumbers = $this->getParam('phone_numbers');
        $emailGroupIds = $this->getParam('email_groups');

        $sendSmsRequest = new SendSmsRequest($message, $subject);

        if (is_array($userIds)) {
            $sendSmsRequest->setUserIds($userIds);
        }

        if (is_array($phoneNumbers)) {
            $sendSmsRequest->setPhoneNumbers($phoneNumbers);
        }

        if (is_array($emailGroupIds)) {
            $sendSmsRequest->setEmailGroupIds($emailGroupIds);
        }

        (new SendSmsService())->execute($sendSmsRequest);
    }

    public function getCreditsAction()
    {
        $this->disableView();

        if (!Settings::get('sms_enabled')) {
            echo 0;
        }

        $balance = (new ViewBalanceService())->execute();

        if ($balance['payment'] === 'prepaid') {
            echo 'Beschikbaar balans: € ' . $balance['amount'];
        } else {
            echo 'Verbruikt balans: € ' . $balance['amount'];
        }
    }
}
