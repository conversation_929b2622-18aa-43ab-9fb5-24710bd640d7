<?php

use Ramsey\Uuid\Uuid;

class System_MigrationController extends GlobalController
{
    public function preDispatch() {
        $this->disableView();

        if (!$this->isCallerAuthorized()) {
            die('Caller is not authorized');
        }
    }

    private function isCallerAuthorized()
    {
        $authorizedIpAddresses = [
            '*************', // Omniboxx web server
            '*************', // Menno Utrecht - Terwijde
        ];

        return in_array($_SERVER['REMOTE_ADDR'], $authorizedIpAddresses, true);
    }

    public function testAction()
    {
        $this->disableView();
        error_log('System_migration test');
        $responseType = $this->getParam('response_type');

        switch ($responseType) {
            case '202':
                $case = 0;
                break;
            case 'exception':
                $case = 1;
                break;
            case 'die':
                $case = 2;
                break;
            case '200':
                $case = 3;
                break;
            default:
                $case = rand(0, 3);
                break;
        }

        try {
            switch ($case) {
                case 0:
                    echo 'ik echo iets met status code 202';
                    http_response_code(202);
                    break;
                case 1:
                    throw new \RuntimeException('ik gooi een exception.');
                    break;
                case 2:
                    die('ik gooi een die');
                    break;
                case 3:
                    echo 'ik echo iets met status code 200';
                    http_response_code(200);
                    break;
            }
        } catch (\Exception $exception) {
            echo $exception->getMessage();
            http_response_code(500);
        }
    }

    public function accountingSeparationSettingsAction()
    {
        $this->disableView();

        try {
            if (!Settings::get('financial_export_system')) {
                echo 'geen financieel export systeem enabled';
                http_response_code(200);
                return; // stop
            }

            Settings::set('financial_export_invoices', 1);

            $corporationRows = (new Corporations())->fetchAll();

            foreach ($corporationRows as $corporationRow) {
                if ($corporationRow->financial_export_enabled === 'no') {
                    continue;
                }

                $corporationRow->financial_export_invoices = 'yes';

                if (Settings::get('financial_import_third_party_purchase_invoices')) {
                    $corporationRow->financial_import_third_party_purchase_invoices = 'yes';
                }

                $corporationRow->save();
            }

            http_response_code(202);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
            http_response_code(500);
        }
    }

    public function installationIdentifierAction()
    {
        $this->disableView();

        if (Settings::get('installation_id')) {
            return;
        }

        try {
            $identifier = Uuid::uuid4()->toString();
            $settingRow = Settings::set('installation_id', $identifier);
            $settingRow->save();

        } catch (\Exception $exception) {
            echo $exception->getMessage();
            http_response_code(500);
        }
    }
}
