<?php

class BranchLocationsController extends GlobalController
{
    public function indexAction()
    {
        $this->_helper->redirector('list', 'branch-locations');
    }

    public function listAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Vestigingen  overzicht');

        $data = (new BranchLocations)->getList();

        $this->view->listView($data)
            ->setTypes([
                'description' => [
                    'title' => 'Omschrijving',
                    'width' => 'xxxxxlarge truncate',
                ],
            ])
            ->setOptions([
                'item_title' => 'Vestiging',
                'disabled_buttons_test' => function ($row, $button) {
                    if ($button !== 'delete') {
                        return false;
                    }

                    if (intval($row['id']) < 1) {
                        return true;
                    }

                    $snModel = new SupportNotes();
                    $notes = $snModel->fetchAll($snModel->select()->where('category = ?', $row['id']));
                    return (count($notes) > 0);
                }
            ])
            ->addButtons(
                [
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen',
                ],
                [
                    'id' => 'id',
                ]
            )
            ->render($this);
    }

    public function selectAction(){
        $this->disableView();

        (new BranchLocationsAdminUsers())
            ->setCurrentForUser(
                loginManager::data()->id,
                $this->getParam('id')
            );

        if ($referer = $_SERVER['HTTP_REFERER']) {
            header('Location: ' . $referer);
        }
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Vestigingen overzicht', 'branch-locations/list/')
            ->addCrumb('Vestiging ' . ($this->_getParam('id') ? 'bewerken' : 'toevoegen'), '');

        $this->view->form = $this->view->EditView(
            $this,
            'BranchLocations',
            [
                'id' => $this->_getParam('id')
            ]
        )
            ->render($this);
    }
}
