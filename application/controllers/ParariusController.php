<?
class ParariusController extends GlobalController {

	private $valid_ips = [
		'192.168.',
		''
	];

	public function preDispatch()
	{
        if (strpos($_SERVER['HTTP_REFERER'], 'onlyexpats') !== false ||
            strpos($_SERVER['HTTP_REFERER'], 'bot.casco.io')) {
            $this->getResponse()
                ->setHttpResponseCode(405)
                ->sendResponse();
            exit();
        }


        if(!is_cli_call())
		    $this->disableView();
        else
            $this->_helper->viewRenderer->setNoRender(true);

		if (!Settings::get('modules_rental'))
			$this->_redirect('dashboard/index');
//
//		if(!$this->validateIp())
//			$this->handleInvalidIp();
	}

	private function validateIp(){
		$ip_is_valid = false;
		$ip = $_SERVER['REMOTE_ADDR'];

		foreach($this->valid_ips as $valid_ip)
			if(stripos($ip, $valid_ip) === 0)
				$ip_is_valid = true;

		return $ip_is_valid;
	}

	private function handleInvalidIp(){
		$error_uniqid = uniqid();
		error_log($error_uniqid . ' - Pararius controller invalid ip address call: ' . $_SERVER['REMOTE_ADDR']);
//		$this->getResponse()
//			->setHttpResponseCode(401)
//			->setBody('Invalid IP address, please contact Omniboxx customer <NAME_EMAIL> and mention this error log code: ' . $error_uniqid)
//			->setHeader('Content-Type', 'text/json')
//			->sendResponse();
//		exit;
	}

	public function exportAction(){

        if (!\Settings::get('modules_rental_pararius_export')) {
            http_response_code(403);
            die();
        }

        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 60 * 60 * 2); // 2 hours

        $corporationNumber = $this->_getParam('number');

        try {
            $xmlStr = (new \Pararius\ParariusXmlFactory($corporationNumber))->execute();
        } catch (\Exception $exception) {
            http_response_code(500);
            die();
        }

        $export_filename = 'Pararius' . ($corporationNumber ? '_' . $corporationNumber : '') . '.xml';

        ob_clean();
        header('Content-type: text/xml');
        header('Content-disposition: attachment; filename="' . $export_filename . '"');
        echo $xmlStr;
    }

    public function exportLegacyAction()
    {
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 60 * 60 * 2); // 2 hours

        $pararius = new Controllers\Pararius\Export($this->_getParam('number'));
        $pararius->execute();

        //echo '<pre>' . htmlentities($pararius->getXml()) . '</pre>'; die();

        $export_filename = 'Pararius' . ($this->_getParam('number') ? '_' . $this->_getParam('number') : '') . '.xml';

        ob_clean();
        header('Content-type: text/xml');
        header('Content-disposition: attachment; filename="' . $export_filename . '"');
        echo $pararius->getExportXml();

    }

	public function getPhotoAction(){
		$file_types = [
			'jpg' => 'image/jpeg',
			'jpeg' => 'image/jpeg',
			'png' => 'image/png',
		];

		$photo_path = substr($_SERVER['REQUEST_URI'], strlen('/pararius/get-photo'));
		$photo_path = str_replace('../', '', $photo_path);

		$full_path = Main::app()->getDir('pararius_export') . 'photos' . $photo_path;
        $full_path = urldecode($full_path);

		$render_error = function(){
			$this->getResponse()
				->setHttpResponseCode(404)
				->sendResponse();
			exit();
		};

		if(!is_readable($full_path))
			$render_error();

		if($file_type = $file_types[pathinfo($full_path, PATHINFO_EXTENSION)]) {
			header("Content-Type: " . $file_type);
			echo file_get_contents($full_path);
		} else {
			$render_error();
		}
	}

}
