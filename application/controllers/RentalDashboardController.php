<?

use Controllers\User\RegistrationRequestAdditionalDocumentsService;
use RentalDeals\CancelConceptDeal\CancelConceptDeal;
use RentalDeals\CancelConceptDeal\CompositeSpecification;
use RentalDeals\CancelConceptDeal\SendDeniedDealMailService;
use RentalDeals\CancelConceptDeal\WebsiteDealSpecification;
use Controllers\RentalDeals\CreateConceptUserObjectServiceForDealService;
use RentalDeals\CancelConceptDeal\CancelOtherRentalDealsForSameObjectService;
use Controllers\RentalDeals\Workflow\ForwardToNextStatus;

include_once('Task.php');

	class RentalDashboardController extends GlobalController {

		protected $general_types = [
			'object_id' => ['title' => '', 'width' => 'hidden object_id'],
			'user_id' => ['title' => '', 'width' => 'hidden user_id']
		];

		protected $general_filters = [
			'object_id' => ['type' => 'json_array', 'preApplied' => true],
			'user_id' => ['type' => 'json_array', 'preApplied' => true]
		];

		protected $filtered_params = [];



		public function preDispatch()
		{

			foreach(['object_id', 'user_id'] as $param_name)
				if($this->_getParam($param_name))
					$this->filtered_params[$param_name] = json_decode($this->_getParam($param_name), true);

			parent::preDispatch();
		}

		protected function checkNumberOfAppliedFilters($filters){
			$number_of_applied_filters = 0;

			foreach($this->getAllParams() as $param_name => $param_value){
				if(isset($filters[$param_name]) && $param_value != '')
					$number_of_applied_filters++;
			}

			return $number_of_applied_filters;
		}

		public function objectsAction(){
 			$this->view->Breadcrumbs()
				->addCrumb('Objecten');

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['name' => 'DSC']));

			$preAppliedFilters = ['address', 'number', 'city'];

            foreach ($preAppliedFilters as $preAppliedFilter) {
                if (!is_null($this->getParam($preAppliedFilter))) {
                    $this->filtered_params[$preAppliedFilter] = $this->getParam($preAppliedFilter);
                }
            }

			$model = new Controllers_RentalDashboard_Objects();
			$model->setFilters($this->filtered_params);
			$filters = [
				'address' => ['type' => 'input', 'preApplied' => true],
				'number' => ['type' => 'input', 'preApplied' => true],
				'city' => ['type' => 'input', 'preApplied' => true],
				'status' => [],
			] + $this->general_filters;

			if($this->isAjaxRequest && $this->checkNumberOfAppliedFilters($filters) == 0)
				$list = [];
			else
				$list = $model->getList();

			$this->view->ListView($list, ['url-override' => 'rental-dashboard/objects'])

				->setTypes([
				    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
					'address' => ['title' => 'Adres', 'width' => 'medium truncate'],
					'number' => ['title' => 'Nummer', 'width' => 'xxxsmall right'],
					'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
					'city' => ['title' => 'Stad', 'width' => 'xsmall truncate'],
					'status' => ['title' => 'Status', 'width' => 'xxsmall'],
				] + $this->general_types)

				->addFormat('status', function($status_value) use ($model){
					$statuses = $model->getStatuses();

					$status_value = $status_value ?: 'none';

					if(isset($statuses[$status_value]))
						return $statuses[$status_value];

					return '-';
				})

				->addDetails('rental-dashboard/object-details', ['style' => 'popup'])

				->setFilters($filters)

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'show_view_template_buttons' => false,
					'item_title' => 'Object',
					'paginator_items_per_page' => 15,
					'title_icon' => 'fa-home',
				))

				->render($this);

		}

		public function objectDetailsAction(){
			$model = new Controllers_RentalDashboard_Objects();
			$this->view->data = $model->getDetails($this->getParam('id'));
		}

		public function usersAction(){

			$this->view->Breadcrumbs()
				->addCrumb('Huurders');

			$model = new Controllers_RentalDashboard_Users();
			$model->setFilters($this->filtered_params);

			$filters = [
				'name' => ['type' => 'input'],
				'status' => []
			] + $this->general_filters;

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['name' => 'DSC']));

			if($this->isAjaxRequest && $this->checkNumberOfAppliedFilters($filters) == 0)
				$list = [];
			else
				$list = $model->getList();
			
			$this->view->ListView($list, ['url-override' => 'rental-dashboard/users'])

				->setTypes([
				    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
					'name' => ['title' => 'Huurder', 'width' => 'xxxlarge truncate'],
					'status' => ['title' => 'Status', 'width' => 'xsmall truncate']
				] + $this->general_types)

				->addDetails('rental-dashboard/user-details', ['style' => 'popup'])

				->addFormat('status', function($status_value) use ($model){
					$statuses = $model->getStatuses();

					$status_value = $status_value ?: 'false';

					if(isset($statuses[$status_value]))
						return $statuses[$status_value];

					return '-';
				})

				->setFilters($filters)

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'show_view_template_buttons' => false,
					'item_title' => 'Huurder',
					'paginator_items_per_page' => 15,
					'title_icon' => 'fa-users',
				))

				->render($this);

		}

		public function userDetailsAction(){
			$model = new Controllers_RentalDashboard_Users();
			$this->view->data = $model->getDetails($this->getParam('id'));
		}

		public function contractsAction(){

			$this->view->Breadcrumbs()
				->addCrumb('Contracten');

			$this->view->ListView([], ['url-override' => 'rental-dashboard/contracts'])

				->setTypes([
				    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
					'address' => ['title' => 'Adres', 'width' => 'small'],
					'status' => ['title' => 'Looptijd', 'width' => 'small'],
				] + $this->general_types)


				->setFilters([
					'address' => []
				] + $this->general_filters)

				->setOptions(array(
					'show_view_template_buttons' => false,
					'item_title' => 'Contract',
					'paginator_items_per_page' => 5
				))

				->render($this);

		}



		public function investorsAction(){

			$this->view->Breadcrumbs()
				->addCrumb('Beleggers');

			$model = new Controllers_RentalDashboard_Investors();

			$this->_setParam('user_id', false);
			unset($this->filtered_params['user_id']);

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['name' => 'DSC']));

			$model->setFilters($this->filtered_params);

			$filters = [
				'name' => ['type' => 'input'],
                'user_name' => ['type' => 'input']
			] + $this->general_filters;

			if($this->isAjaxRequest && $this->checkNumberOfAppliedFilters($filters) == 0)
				$list = [];
			else
				$list = $model->getList();

			$this->view->ListView($list, ['url-override' => 'rental-dashboard/investors'])

				->setTypes([
				    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
					'name' => ['title' => 'Naam', 'width' => 'large truncate'],
					'user_name' => ['title' => 'Contactpersoon', 'width' => 'large truncate']
				] + $this->general_types)

				->addDetails('rental-dashboard/investor-details', ['style' => 'popup'])

				->setFilters($filters)

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'show_view_template_buttons' => false,
					'item_title' => 'Verhuurder',
					'paginator_items_per_page' => 15,
					'title_icon' => 'fa-user',
				))

				->render($this);

		}

		public function investorDetailsAction(){
			$model = new Controllers_RentalDashboard_Investors();
			$this->view->data = $model->getDetails($this->getParam('id'));
		}

		public function leadsAction(){
            $this->view->headScriptHashed()->appendFile('media/javascript/rental-dashboard/details.js');

            ini_set('memory_limit', '2000M');
            
            $this->view->Breadcrumbs()
				->addCrumb(ucfirst(translate()->_('new_leads')));

			$model = new Controllers_RentalDashboard_Leads();

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['date' => 'DSC']));

			$filters = [
					'name' => ['type' => 'input'],
					'status' => []
				] + $this->general_filters;


            $render_small = $this->getParam('render_small');
            if (!$render_small) {
                $this->general_types = [
                    'email' => ['title' => ucfirst(translate()->_('email')), 'width' => 'xlarge truncate'],
                    'phone_number' => ['title' => ucfirst(translate()->_('phone')), 'width' => 'large truncate'],
                    'bdate' => ['title' => 'Geboortedatum', 'width' => 'hidden'],
                    'birthplace' => ['title' => 'Geboorteplaats', 'width' => 'hidden'],
                    'nationality' => ['title' => 'Nationaliteit', 'width' => 'hidden'],
                    'number' => ['title' => 'Nummer', 'width' => 'hidden'],
                    'comment' => ['title' => 'Opmerking', 'width' => 'hidden'],
                    'marital' => ['title' => 'Burgerlijke staat', 'width' => 'hidden'],
                    'adults' => ['title' => 'Volwassenen', 'width' => 'hidden'],
                    'kids' => ['title' => 'Kinderen', 'width' => 'hidden'],
                    'partner' => ['title' => 'Partner', 'width' => 'hidden'],
                    'last_report_contact' => ['title' => 'Laatste contact', 'width' => 'hidden'],
                    'identification' => ['title' => 'Identificatie', 'width' => 'hidden'],
                    'chamberofcommerce' => ['title' => 'Kamer van Koophandel', 'width' => 'hidden'],
                    'bankaccountname' => ['title' => 'Rekeninghouder', 'width' => 'hidden'],
                    'code' => ['title' => 'Code', 'width' => 'hidden'],
                    'free_field_1' => ['title' => 'Vrij veld 1', 'width' => 'hidden'],
                    'free_field_2' => ['title' => 'Vrij veld 2', 'width' => 'hidden'],
                    'BSN' => ['title' => 'BSN', 'width' => 'hidden'],
                    'identication_type' => ['title' => 'Type identificatie', 'width' => 'hidden'],
                    'identication_valid_till' => ['title' => 'Geldig tot', 'width' => 'hidden'],
                    'language' => ['title' => 'Taal', 'width' => 'hidden'],
                    'created_on' => ['title' => 'Aangemaakt op', 'width' => 'hidden'],
                    'rent_price' => ['title' => 'Huurprijs', 'width' => 'hidden'],
                    'rent_price_range' => ['title' => 'Huurprijs range', 'width' => 'hidden'],
                    'object_min_size' => ['title' => 'Minimale grootte', 'width' => 'hidden'],
                    'cities' => ['title' => 'Steden', 'width' => 'hidden'],
                    'allowed_cities' => ['title' => 'Toegestane steden', 'width' => 'hidden'],
                    'bedrooms' => ['title' => 'Slaapkamers', 'width' => 'hidden'],
                    'bouwvorm' => ['title' => 'Bouwvorm', 'width' => 'hidden'],
                    'toilets' => ['title' => 'Toiletten', 'width' => 'hidden'],
                    'bathrooms' => ['title' => 'Badkamers', 'width' => 'hidden'],
                    'object_type' => ['title' => 'Objecttype', 'width' => 'hidden'],
                    'interior' => ['title' => 'Interieur', 'width' => 'hidden'],
                    'kitchen' => ['title' => 'Keuken', 'width' => 'hidden'],
                    'parking_spots_type' => ['title' => 'Parkeerplaats type', 'width' => 'hidden'],
                    'smoking' => ['title' => 'Roken', 'width' => 'hidden'],
                    'animals' => ['title' => 'Huisdieren', 'width' => 'hidden'],
                    'garden' => ['title' => 'Tuin', 'width' => 'hidden'],
                    'garden_situation' => ['title' => 'Tuinsituatie', 'width' => 'hidden'],
                    'elevator' => ['title' => 'Lift', 'width' => 'hidden'],
                    'windows' => ['title' => 'Ramen', 'width' => 'hidden'],
                    'type_build' => ['title' => 'Bouwtype', 'width' => 'hidden'],
                    'mailing' => ['title' => 'Mailing', 'width' => 'hidden'],
                    'daily_mailing' => ['title' => 'Dagelijkse mailing', 'width' => 'hidden'],
                    'rent_from' => ['title' => 'Huur vanaf', 'width' => 'hidden'],
                    'period_search' => ['title' => 'Zoekperiode', 'width' => 'hidden'],
                    'neighbourhood' => ['title' => 'Buurt', 'width' => 'hidden'],
                    'profession' => ['title' => 'Beroep', 'width' => 'hidden'],
                    'school' => ['title' => 'School', 'width' => 'hidden'],
                    'study_level' => ['title' => 'Studieniveau', 'width' => 'hidden'],
                    'school_input' => ['title' => 'School input', 'width' => 'hidden'],
                    'school_location' => ['title' => 'Schoollocatie', 'width' => 'hidden'],
                    'study_direction' => ['title' => 'Studierichting', 'width' => 'hidden'],
                    'study_direction_input' => ['title' => 'Studierichting input', 'width' => 'hidden'],
                    'income_amount' => ['title' => 'Inkomen', 'width' => 'hidden'],
                    'partner_income_amount' => ['title' => 'Partnerinkomen', 'width' => 'hidden'],
                    'source_of_income' => ['title' => 'Inkomensbron', 'width' => 'hidden'],
                    'living_situation' => ['title' => 'Woonsituatie', 'width' => 'hidden'],
                    'family_situation' => ['title' => 'Gezinssituatie', 'width' => 'hidden'],
                ];
            }

            $data = $model->getList();
			$this->view->ListView($data, ['url-override' => 'rental-dashboard/leads'])

				->setTypes([
                        'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
						'name' => ['title' => ucfirst(translate()->_('name')), 'width' => $render_small ? 'xxlarge truncate' : 'xxxlarge truncate'],
                        'date' => ['title' => ucfirst(translate()->_('added')), 'width' => $render_small ? 'medium' : 'large', 'order_using_raw' => true],
					] + $this->general_types)

				->addDetails('rental-dashboard/lead-details', ['style' => 'popup'])

                ->addFormat('name', function($value, $item){
                    return '<a href="support/show/id/' . $item['user_id'] . '/type/user" >' . $value . '</a>';
                })

                ->addFormat('status', function($value){
					return 'Nieuw';
				})

                ->addFormat('date', function($value, $item){
                    return date('d-m-Y H:m:s', strtotime($value));
                })

				->setFilters($filters)

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'item_title' => 'Lead',
                    'paginator_items_per_page' => $this->getParam('limit') ?: 50,
					'title_icon' => 'fa-male'
				))

				->render($this);


			$this->view->Breadcrumbs()
				->addCrumb('Nieuwe leads');
		}

		public function leadDetailsAction(){
			$model = new Controllers_RentalDashboard_Leads();
            $this->view->isDeleteAllowed =(new Users())->deleteAllowed($this->_getParam('id'));
			$this->view->data = $model->getDetails($this->getParam('id'));
		}

		public function dealsAction(){
            ini_set('memory_limit', '2000M');

			$this->view->Breadcrumbs()
				->addCrumb('Lopende deals');

			$render_small = $this->getParam('render_small');

			$ignore_created_user_and_status = false;
			if(isset($this->filtered_params['object_id']))
				$ignore_created_user_and_status = count($this->filtered_params['object_id']) > 0;

			if($ignore_created_user_and_status) {
                $this->setParam('assigned_user_id', null);
            }

			if($ignore_created_user_and_status)
				$this->setParam('combined_status', null);
			elseif(is_null($this->_getParam('combined_status')) && !$this->isAjaxRequest)
				$this->setParam('combined_status', 'current');

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['name' => 'DSC']));

			$params = ['url-override' => 'rental-dashboard/deals', 'render_small' => $render_small, 'assigned_user_id' => $this->_getParam('assigned_user_id'), 'combined_status' => $this->_getParam('combined_status')];

			if($ignore_created_user_and_status)
				unset($params['assigned_user_id'], $params['combined_status']);

			$preAppliedFilters = [
                'contract',
                'object',
                'user',
                'object_id',
                'user_id',
                'assigned_user_id',
                'combined_status',
            ];

            foreach ($preAppliedFilters as $preAppliedFilter) {
                if (!is_null($this->getParam($preAppliedFilter))) {
                    $this->filtered_params[$preAppliedFilter] = $this->getParam($preAppliedFilter);
                }
            }

            $model = new Controllers_RentalDashboard_Deals();
            $model->setFilters(array_merge(
                $this->filtered_params,
                [
                    'concept_deal' => 'no',
                    'website_deal' => 'no',
                ]
            ));


			$types = [
			    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
				'contact-date' => ['title' => '&nbsp;', 'width' => 'xxxxsmall'],
				'user' =>	['title' => 'Kandidaat', 'width' => 'xxlarge'],
				'status' =>	['title' => 'Status', 'width' => $render_small ? 'large' : 'xxxlarge truncate'],
				'assigned_user_id' => ['title' => '', 'width' => 'hidden'],
				'cancelled' => ['title' => '', 'width' => 'hidden'],
			] + $this->general_types;

			if(!Settings::get('modules_rental_contract_deals'))
				array_add_after($types, 'contact-date', ['object' => ['title' => 'Adres', 'width' => 'xxxlarge truncate']]);
			else
				array_add_after($types, 'contact-date', ['contract' => ['title' => 'Contract', 'width' => 'large truncate']]);

			$this->view->ListView($model->getList(), $params)

				->setTypes($types)

				->addDetails('rental-deals/details', ['style' => 'popup'])

				->addFormat('assigned_user_id', function($assigned_user_id){
					if(!($assigned_user_id > 0)) return;

					return db()->fetchOne(db()->select()->from('users', ['rendered_name'])->where('id = ?', $assigned_user_id));
				})

				->addFormat('combined_status', function($value){
					if($value == 'cancelled')
						return 'Geannuleerd';

					if($value == 'archived')
						return 'Gearchiveerd';

					return 'Actueel';
				})

				->addFormat('contact-date', function($value){

					$days_between = days_from_today($value);

					if(!$value)
						$color = 'white';
					elseif($days_between < 0)
						$color = 'gray';
					elseif($days_between == 0)
						$color = 'red';
					elseif($days_between < 5)
						$color = 'orange';
					else
						$color = 'green';

					return '<span noHintButton="1" title="' . date('d-m-Y', strtotime($value)) . '" class="contact-date ' . $color . '">' . (!$value ? '-' : ($days_between == 0 ? '0 !' : $days_between)) . '</span>';
				})

				->addFormat('status', function($value, $row) use ($render_small){
				    if($render_small){
					    $label = $value > 10? "{$value}%" : '';
                    } else {
                        return $value > 0 ? ($value . '% : ' . $row['current_status']) : '0%';
                    }
					return "<div class='statusbar'><div style='width: {$value}%;'>{$label}</div></div>";
				})

                ->setFilters(
                    [
                        'contract' => ['type' => 'input', 'preApplied' => true],
                        'object' => ['type' => 'input', 'preApplied' => true],
                        'user' => ['type' => 'input', 'preApplied' => true],
                        'assigned_user_id' => [
                            'renderSeparately' => true,
                            'title' => '',
                            'hideCount' => true,
                            'custom_options' => $model->getAdminUsers(),
                            'custom_options_only' => true,
                            'preApplied' => true
                        ],
                        'combined_status' => [
                            'renderSeparately' => true,
                            'title' => '',
                            'hideCount' => true,
                            'custom_options' => $model->getCombinedStatusses(),
                            'custom_options_only' => true,
                            'preApplied' => true
                        ]
                    ] + $this->general_filters
                )

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'show_view_template_buttons' => false,
					'item_title' => 'Deal',
					'paginator_items_per_page' => 15,
					'title_icon' => 'fa-tasks',
				))

				->render($this);

		}


        public function conceptDealsAction(){
            ini_set( 'max_execution_time', '-1' );
            ini_set('memory_limit', '-1');

            $this->view->headLink()->appendStylesheet('media/style/rental-dashboard/website-deals.css');

            $this->view->Breadcrumbs()
                ->addCrumb('Vidii toewijzingen');

            if(is_null($this->_getParam('combined_status')))
                $this->setParam('combined_status', 'current');

            $params = ['url-override' => 'rental-dashboard/concept-deals', 'combined_status' => $this->_getParam('combined_status')];

            $model = new Controllers_RentalDashboard_Deals();
            $model->setOrders([
                'o.rendered_address ASC'
            ]);

            $filters = [
                'vidii_deal' => 'yes',
				'combined_status' => $params['combined_status'],
				'object' => $this->getParam('object')
            ];

            $model->setFilters($filters);

            $data = $model->getList();

            $data_per_object = [];
            foreach($data as $data_item){

                unset($data_item['listview_row_classes']);

                if(!isset($data_per_object[$data_item['object_id']])) {
                    $data_item['proposition_counter'] = 0;
                    $data_item['id'] = $data_item['object_id'];
                    $data_per_object[$data_item['object_id']] = $data_item;
                }

                if($data_per_object[$data_item['object_id']]['combined_status'] === 'current' || $data_item['combined_status'] === 'current'){
                    $data_per_object[$data_item['object_id']]['combined_status'] = 'current';
                }

                if($data_item['combined_status'] === $params['combined_status']) {
                    $data_per_object[$data_item['object_id']]['proposition_counter']++;
                }
            }
            $data = $data_per_object;

            $this->view->ListView($data, $params)

                ->setTypes( [
                    'spacer' =>	['title' => '&nbsp;', 'width' => 'xxxxxsmall'],
                    'object_id' =>	['title' => '', 'width' => 'hidden'],
                    'object' =>	['title' => 'Adres', 'width' => 'xxxlarge'],
                    'proposition_counter' =>	['title' => 'Aantal kandidaten', 'width' => 'xlarge right'],
                ])

                ->addLegend('concept-deals-legend')

                ->addFormat('object', function($value, $row) {
                    return $row['object_id'] > 0 ? '<a href="support/show/id/' . $row['object_id'] . '/type/object" target="_blank">' . $value . '</a>' : '-';
                })

                ->addFormat('combined_status', function($value){
                    $status_labels = [
                        'cancelled' => 'Geannuleerd',
                        'archived' => 'Geselecteerd',
                        'current' => 'Actueel',
                    ];
                    return $status_labels[$value];
                })

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'object' => ['type' => 'input', 'preApplied' => true],
                    'projectname' => ['renderSeparately' => true, 'title' => 'Project'],
                    'objectgroupname' => ['renderSeparately' => true, 'title' => 'Objectgroep'],
                    'combined_status' => ['renderSeparately' => true, 'title' => '']
                ])
                ->addDetails(
                    'rental-dashboard/concept-deal-propositions/noAjax/1/',
                    [
                        'style' => 'collapse',
                        'filter_values_as_parameter' => ['combined_status']
                    ]
                )

                ->setOptions([
                    'item_title' => 'Deal',
                    'paginator_items_per_page' => 15,
                    'title_icon' => 'fa-tasks',
                ])

                ->render($this);
            ;
        }


        public function extendedConceptDealsAction(){
            ini_set( 'max_execution_time', '-1' );
            ini_set('memory_limit', '-1');


            $this->view->Breadcrumbs()
                ->addCrumb('Vidii toewijzingen');

            if(is_null($this->_getParam('combined_status')))
                $this->setParam('combined_status', 'current');


            $model = new Controllers_RentalDashboard_Deals();
            $model->setOrders([
                'o.rendered_address ASC'
            ]);

            $filters = [
                'vidii_deal' => 'yes',
                'combined_status' => $params['combined_status'],
                'object' => $this->getParam('object')
            ];

            $model->setFilters($filters);

            $data = $model->getList();

            //p($data,'die');
            $this->view->extraWidth = true;

            $this->view->ListView($data, $params)

                ->setTypes( [
                    'spacer' =>	['title' => '&nbsp;', 'width' => 'xxxxxsmall'],
                    'object_id' =>	['title' => '', 'width' => 'hidden'],
                    'object' =>	['title' => 'Adres', 'width' => 'xxxlarge', 'group_equal_rows' => true],
                    'userid' =>	['title' => 'Kandidaat', 'width' => 'xxlarge'],
                    'user_deals' => ['title' => '', 'width' => 'xxxxsmall'],
                    'bdate' =>	['title' => 'Geb. datum', 'width' => 'small'],
                    'income_amount' => ['title' => 'Inkomen', 'width' => 'small'],
                    'partner_income_amount' => ['title' => 'Partnerinkomen', 'width' => 'small'],
                    'source_of_income' =>	['title' => 'Type Inkomen', 'width' => 'small'],
                    'living_situation' =>	['title' => 'Woonsituatie', 'width' => 'small'],
                    'created_on' => ['title' => 'Datum', 'width' => 'small'],
                    'number_of_tenants' => ['title' => 'Aantal personen', 'width' => 'small'],
                    'documents' =>	['title' => 'Documenten', 'width' => 'small'],
                    'assigned_user_id' => ['title' => '', 'width' => 'hidden'],
                    'cancelled' => ['title' => '', 'width' => 'hidden']
                ])

                ->addLegend('concept-deals-legend')

                ->addFormat('object', function($value, $row) {
                    return $row['object_id'] > 0 ? '<a href="support/show/id/' . $row['object_id'] . '/type/object" target="_blank">' . $value . '</a>' : '-';
                })

                ->addFormat(['source_of_income', 'living_situation'], 'ucfirst')

                ->addFormat('userid', function($value, $item){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . Users::getName($value) . '</a>' : '-';
                })

                ->addFormat('bdate', 'date')

                ->addFormat('income_amount', 'money_from_int_db')

                ->addFormat('partner_income_amount', 'money_from_int_db')

                ->addFormat('created_on', function($value){
                    return date('d-m-Y', strtotime($value));
                })



                ->addFormat('documents', function($value, $row){
                    return  '
                    <a href="document/download-all/type/user/map-to/' . $row['user_id'] . '/" target="_blank">
                        <i class="fa fa-download"></i>
                    </a>';
                })

                ->addFormat('combined_status', function($value){
                    $status_labels = [
                        'cancelled' => 'Geannuleerd',
                        'archived' => 'Geselecteerd',
                        'current' => 'Actueel',
                    ];
                    return $status_labels[$value];
                })

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'object' => ['type' => 'input', 'preApplied' => true],
                    'projectname' => ['renderSeparately' => true, 'title' => 'Project'],
                    'objectgroupname' => ['renderSeparately' => true, 'title' => 'Objectgroep'],
                    'combined_status' => ['renderSeparately' => true, 'title' => '']
                ])


                ->setOptions([
                    'item_title' => 'Deal',
                    'paginator_items_per_page' => 15,
                    'title_icon' => 'fa-tasks',
                ])

                ->render($this);
            ;
        }


        public function conceptDealPropositionsAction(){
            ini_set( 'max_execution_time', '-1' );
            ini_set('memory_limit', '-1');

            $this->view->extraWidth = true;
            $this->view->headLink()->appendStylesheet('media/style/rental-deals/details.css');

            $this->setParam('object_id', $this->getParam('id'));
            if(is_null($this->_getParam('combined_status'))) {
                $this->setParam('combined_status', 'current');
            }

            $params = ['url-override' => 'rental-dashboard/concept-deal-propositions', 'object_id' => $this->getParam('object_id'),  'combined_status' => $this->_getParam('combined_status')];

            $model = new Controllers_RentalDashboard_Deals();
            $model->setOrders([
                // order preferences: order by simular income, then prefer users with budget manager
                'CONCAT(ROUND(u.income_amount / 10), "_", IF(u.consent_to_contact_from_budget_manager = "yes", 1, 0)) DESC',
            ]);
            $model->setFilters([
                'object_id' => $this->getParam('object_id'),
                'vidii_deal' => 'yes',
				'combined_status' => $this->getParam('combined_status'),
            ]);

			$model->shouldShowAlternativeDealsForUsers = true;

            $data = $model->getList();

            $current_object = false;
            foreach($data as $item_key => $item){
                $data[$item_key]['is_best_proposition'] = $item['object'] != $current_object;

                if($data[$item_key]['is_best_proposition'] && is_null($this->getParam('is_best_proposition'))) {
                    $data[$item_key]['listview_row_classes'] = [ 'status', 'green' ];
                }

                $current_object = $item['object'];
            }

			$actions = [
				'buttons' => [
					['title' => 'Woning toewijzen', 'icon' => 'tick', 'action' => 'rental-dashboard/finish-concept-deal', 'confirm' => true],
					['title' => 'Uitloten', 'icon' => 'delete', 'action' => 'rental-dashboard/cancel-concept-deal', 'confirm' => true],
				]
			];

			if ($this->getParam('combined_status') === 'cancelled') {
				$actions = [
					'buttons' => [
						['title' => 'Woning toewijzen', 'icon' => 'tick', 'action' => 'rental-dashboard/finish-concept-deal', 'confirm' => true],
						['title' => 'Interesse mail', 'icon' => 'mail', 'action' => 'rental-dashboard/send-still-interested', 'confirm' => true],
					]
				];
			};

			$this->view->ListView($data, $params)
                ->setTypes([
                    'userid' =>	['title' => 'Kandidaat', 'width' => 'xxlarge'],
                    'user_deals' => ['title' => '', 'width' => 'xxxxsmall'],
                    'bdate' =>	['title' => 'Geb. datum', 'width' => 'small'],
                    'income_amount' => ['title' => 'Inkomen', 'width' => 'small'],
                    'partner_income_amount' => ['title' => 'Partnerinkomen', 'width' => 'small'],
                    'source_of_income' =>	['title' => 'Type Inkomen', 'width' => 'small'],
                    'living_situation' =>	['title' => 'Woonsituatie', 'width' => 'small'],
                    'created_on' => ['title' => 'Datum', 'width' => 'small'],
                    'number_of_tenants' => ['title' => 'Aantal personen', 'width' => 'small'],
                    'documents' =>	['title' => 'Documenten', 'width' => 'small'],
                    'assigned_user_id' => ['title' => '', 'width' => 'hidden'],
                    'cancelled' => ['title' => '', 'width' => 'hidden'],
                    'is_best_proposition' => ['title' => '', 'width' => 'hidden']
                ])

                ->addFormat(['source_of_income', 'living_situation'], 'ucfirst')

                ->addFormat('userid', function($value, $item){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . Users::getName($value) . '</a>' : '-';
                })

				->addFormat('user_deals', function($value){
				    $value = htmlentities($value);
					return "<a class='forceAutoHint' title=\"Inschrijvingen huurder: <br> {$value}\">
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							</a>";
				})

                ->addFormat('bdate', 'date')

                ->addFormat('income_amount', 'money_from_int_db')

                ->addFormat('partner_income_amount', 'money_from_int_db')

                ->addFormat('consent_to_contact_from_budget_manager', function($value){
                    return $value === 'yes' ? '<i class="fa fa-check"></i>' : '<i class="fa fa-times"></i>';
                })

                ->addFormat('created_on', function($value){
                    return date('d-m-Y', strtotime($value));
                })

                ->addFormat('number_of_tenants', function($value){
                    return $value > 0 ? $value : '-';
                })

                ->addFormat('documents', function($value, $row){
                    return  '
                    <a href="document/download-all/type/user/map-to/' . $row['user_id'] . '/" target="_blank">
                        <i class="fa fa-download"></i>
                    </a>';
                })

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'contract' => ['type' => 'input'],
                    'object' => ['type' => 'input'],
                    'userid' => ['type' => 'input'],
                    'source_of_income' => ['type' => 'select'],
                    'living_situation'  => ['type' => 'select'],
                    'number_of_tenants'  => ['type' => 'select'],
                    'income_amount' => ['type' => 'range'],
                    'partner_income_amount' => ['type' => 'range'],
                    'combined_status' => []
                ])

                ->setOptions([
                    'filter_sort_by_levenstein' => true,
                    'show_view_template_buttons' => false,
                    'item_title' => 'Deal',
                    'show_title' => false,
                    'paginator' => true
                ])
				->addActions($actions)
                ->render($this)
            ;
        }

        private function websiteDealsAddCounts($data, $combinedStatus = 'current'){
            $data_per_object = [];
            foreach ($data as $data_item) {

                unset($data_item['listview_row_classes']);

                if (!isset($data_per_object[$data_item['object_id']])) {
                    $data_item['id'] = $data_item['object_id'];
                    $data_per_object[$data_item['object_id']] = $data_item;
                }

                if (
                    $data_per_object[$data_item['object_id']]['combined_status'] === $combinedStatus ||
                    $data_item['combined_status'] === $combinedStatus
                ) {
                    $data_per_object[$data_item['object_id']]['combined_status'] = $combinedStatus;
                }
            }

            if(count($data_per_object) === 0) {
                return [];
            }

            $otherDealsSelect = db()->select()
                ->from(['rd' => 'rental_deals'], ['object_id', 'other_deal_counter' => 'COUNT(user_id)'])
                ->joinLeft(
                    ['o' => 'objects'],
                    'o.id = rd.object_id',
                    false
                )
                ->joinLeft(
                    ['og' => 'objectgroup'],
                    'og.id = o.objectgroup',
                    false
                )
                ->joinLeft(
                    ['u' => 'users'],
                    'u.id = rd.user_id',
                    false
                )
                ->where('rd.object_id IN (?)', array_keys($data_per_object))
                ->where('rd.vidii_deal = ?', 'no')
                ->where('rd.website_deal = ?', 'no')
                ->where('rd.cancelled = ?', false)
                ->where('rd.archived = ?', false)
                ->group('rd.object_id');

            (new Controllers_RentalDashboard_Deals())->applyProjectLimitationToSelect($otherDealsSelect);
            $otherDeals = db()->fetchPairs($otherDealsSelect);

            $propositionsSelect = db()->select()
                ->from(['rd' => 'rental_deals'], ['object_id', 'proposistion_counter' => 'COUNT(user_id)'])
                ->joinLeft(
                    ['o' => 'objects'],
                    'o.id = rd.object_id',
                    false
                )
                ->joinLeft(
                    ['og' => 'objectgroup'],
                    'og.id = o.objectgroup',
                    false
                )
                ->joinLeft(
                    ['u' => 'users'],
                    'u.id = rd.user_id',
                    false
                )
                ->where('rd.object_id IN (?)', array_keys($data_per_object))
                ->where('rd.website_deal = ?', 'yes')
                ->where('rd.cancelled = ?', false)
                ->group('rd.object_id');

            (new Controllers_RentalDashboard_Deals())->applyProjectLimitationToSelect($propositionsSelect);

            $propositions = db()->fetchPairs($propositionsSelect);

            foreach($data_per_object as $dataPerObjectKey => $dataItem){
                if ($dataItem['start_date'] != '' || $dataItem['report_date'] != '') {
                    if($dataItem['report_date']) {
                        $reportDateString = date('Y-m-d H:i:s', strtotime($dataItem['report_date']));
                    } else {
                        $reportDateString = date('Y-m-d 8:0:0', strtotime($dataItem['start_date']));
                    }
                    $currentDateString = date('Y-m-d H:i:s');
                    $workDaysSincePublished = getWorkDaysDiff($reportDateString, $currentDateString);
                    $numberOfWeekendDays = countWeekendDays($reportDateString, $currentDateString);
                    $dataItem['published_hours'] = floor($workDaysSincePublished * 24) ;
                    $dataItem['published_hours'] = $dataItem['published_hours'] - $numberOfWeekendDays * 24;
                } else {
                    $dataItem['published_hours'] = '--';
                }

                $propositionForObject = isset($propositions[$dataPerObjectKey]) ? $propositions[$dataPerObjectKey] : 0;
                $dataItem['proposition_counter'] = $propositionForObject;

                $otherDealsPerObject = isset($otherDeals[$dataPerObjectKey]) ? $otherDeals[$dataPerObjectKey] : 0;
                $dataItem['other_deals_count'] = $otherDealsPerObject;

                if ( $dataItem['other_deals_count'] > 0 ) {
                    $dataItem['other_deals'] = 'Ja';
                } else {
                    $dataItem['other_deals'] = 'Nee';
                }

                $data_per_object[$dataPerObjectKey] = $dataItem;
            }

            return $data_per_object;
        }

        public function websiteDealsAction(){
            $showCancelledOnly = $this->view->pageInfo['action'] === 'website-deals-cancelled';

            $this->view->Breadcrumbs()
                ->addCrumb($showCancelledOnly ? 'Geannuleerde website aanvragen' : 'Website aanvragen');

            $combinedStatus = $showCancelledOnly ? 'cancelled' : 'current';

            if (is_null($this->getParam('other_deals')) && !$this->isAjaxRequest) {
                $this->setParam('other_deals', 'Nee');
            }

            if (is_null($this->getParam('combined_status'))) {
                $this->setParam('combined_status', $combinedStatus);
            }

            if (is_null($this->getParam('sorting'))) {
                $this->setParam('sorting', json_encode(['object' => 'ASC']));
            }

            $action = $showCancelledOnly ? 'website-deals-cancelled' : 'website-deals';
            $params = [
                'url-override' => 'rental-dashboard/' . $action,
                'combined_status' => $this->getParam('combined_status')
            ];

            $websiteDealsDataService = new \Controllers\RentalDashboard\WebsiteDealsDataService(
                new Investors(),
                new Objectusers()
            );

            $data = $websiteDealsDataService->execute($showCancelledOnly);

            $listview = $this->view->ListView($data, $params)

                ->setTypes( [
                    'spacer' =>	['title' => '&nbsp;', 'width' => 'xxxxxsmall'],
                    'object_id' =>	['title' => '', 'width' => 'hidden'],
                    'object' =>	['title' => 'Adres', 'width' => 'xxxlarge'],
                    'proposition_counter' =>	['title' => 'Aantal kandidaten', 'width' => 'medium right'],
                    'other_deals' =>	['title' => 'Actuele deals', 'width' => 'medium right'],
                    'other_deals_count' =>	['title' => '# Actuele deals', 'width' => 'medium right'],
                    'avg_agreed' =>	['title' => 'AVG akkoord', 'width' => 'medium right'],
                    'current_tenant' =>	['title' => 'Bewoner', 'width' => 'medium right'],
                    'published_hours' =>	['title' => 'Uren gepubliceerd', 'width' => 'large right'],
                    'draw_allowed' =>	['title' => '', 'width' => 'hidden'],
                    'archive_project' => ['title' => '', 'width' => 'hidden'],
                ])

                ->addFormat('avg_agreed', 'bool')

                ->addFormat('current_tenant', 'bool')

                ->addFormat('object', function($value, $row) {
                    return $row['object_id'] > 0 ? '<a href="rental-deals_timeline/for-object/id/' . $row['object_id'] . '" >' . $value . '</a>' : '-';
                })

                ->addFormat('investor', function($value, $item){
                    if(!$value){
                        return '-';
                    }

                    return $item['investor_name'];
                })

                ->addFormat('project', function($value, $item){
                    if(!$value){
                        return '-';
                    }

                    return $item['project_name'];
                })

                ->addFormat('objectgroup', function($value, $item){
                    if(!$value){
                        return '-';
                    }

                    return $item['objectgroup_name'];
                })

                ->addFormat('other_deals_count', function($value){
                    return (int) $value;
                })

                ->addFormat('combined_status', function($value){
                    $status_labels = [
                        'cancelled' => 'Geannuleerd',
                        'archived' => 'Geselecteerd',
                        'current' => 'Actueel',
                    ];
                    return $status_labels[$value];
                })

                ->addFormat('published_hours', function ($value) {
                    return $value > 0 ? $value : '-';
                })
                ->addFormat(['draw_allowed', 'archive_project'], 'bool')

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'object' => ['type' => 'input'],
                    'investor' => ['renderSeparately' => true, 'title' => 'Belegger', 'order_by_title' => 'ASC'],
                    'project' => ['renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'],
                    'objectgroup_name' => ['renderSeparately' => true, 'title' => 'Objectgroep', 'order_by_title' => 'ASC'],
                    'other_deals' => ['renderSeparately' => true, 'title' => 'Actuele deals'],
                    'avg_agreed' => ['renderSeparately' => true, 'title' => 'AVG Akkoord'],
                    'current_tenant' => ['type' => 'input','title' => 'Huidige bewoner'],
                    'draw_allowed' => ['renderSeparately' => true, 'title' => 'Loting toegestaan'],
                    'archive_project' => ['renderSeparately' => true, 'title' => 'Archief project'],
                ])

                ->addDetails(
                    'rental-dashboard/website-deal-propositions/noAjax/1/showCancelledOnly/' . ($showCancelledOnly ? '1' : '0') . '/',
                    ['style' => 'collapse']
                )

                ->setOptions([
                    'show_view_template_buttons' => false,
                    'item_title' => 'Deal',
                    'paginator_items_per_page' => 15,
                    'title_icon' => 'fa-tasks',
                ])
                ;

            if (!$showCancelledOnly) {
                $listview->addLegend('concept-deals-legend');
            }

            $listview->render($this);
        }

        public function websiteDealsCancelledAction(){
		    $this->view->headLink()->appendStylesheet('media/style/rental-dashboard/website-deals.css');

            $this->websiteDealsAction();
        }

        public function websiteDealPropositionsAction(){
		    $showCancelledOnly = $this->getParam('showCancelledOnly') === '1';
            $combinedStatus = $showCancelledOnly ? 'cancelled' : 'current';
            $this->view->extraWidth = true;
            $this->view->headLink()->appendStylesheet('media/style/rental-deals/details.css');

            $this->setParam('object_id', $this->getParam('id'));
            if(is_null($this->_getParam('combined_status'))) {
                $this->setParam('combined_status', $combinedStatus);
            }

            $rentaldealsModel = new RentalDeals();
            $nubmerofactualdealsforobject =  $rentaldealsModel->getNumberOfActiveDealsForObjectId($this->getParam('id'));

            $params = ['url-override' => 'rental-dashboard/website-deal-propositions', 'object_id' => $this->getParam('object_id'), 'combined_status' => $this->_getParam('combined_status')];

            $model = new Controllers_RentalDashboard_Deals();
            $model->setOrders([
                // order preferences: order by simular income, then prefer users with budget manager
                'CONCAT(ROUND(u.income_amount / 10), "_", IF(u.consent_to_contact_from_budget_manager = "yes", 1, 0)) DESC',
            ]);
            $model->setFilters([
                'object_id' => $this->getParam('object_id'),
                'website_deal' => 'yes',
                'cancelled' => $showCancelledOnly ? '1' : '0',
            ]);

            $data = $model->getList();

            $current_object = false;
            $statusTemplateId = false;
            foreach($data as $item_key => $item){
                $data[$item_key]['is_best_proposition'] = $item['object'] != $current_object;

                if($data[$item_key]['is_best_proposition'] && is_null($this->getParam('is_best_proposition'))) {
                    $data[$item_key]['listview_row_classes'] = [ 'status', 'green' ];
                }

                $statusTemplateId = $item['status_template_id'];

                $current_object = $item['object'];
            }

            if ($statusTemplateId) {
                $statusTemplateRowSelect = db()->select()
                    ->from('rental_deals_status_templates')
                    ->where('id = ?', $statusTemplateId);

                $statusTemplateRow = db()->fetchRow($statusTemplateRowSelect);
            }

            $finishWebsiteDealButton = [
                'title' => 'Bezichtiging overslaan',
                'icon' => 'forward',
                'action' => 'rental-dashboard/finish-website-deal',
                'confirm' => true,
                'newWindow' => false,
            ];

            if ($nubmerofactualdealsforobject < 1) {
                $actions = [
                    'buttons' => [
                        [
                            'title' => 'Bezichtiging bevestigen (deal)',
                            'icon' => 'tick',
                            'action' => 'rental-dashboard/viewing-website-deal',
                            'confirm' => true,
                            'singleRowSelectionOnly' => true,
                            'newWindow' => true,
                        ],
                        [
                            'title' => 'Groepsbezichtiging bevestigen',
                            'icon' => 'tick',
                            'action' => 'rental-dashboard/group-viewing-website-deal',
                            'confirm' => true,
                            'newWindow' => true,
                        ],
                        $finishWebsiteDealButton,
                        [
                            'title' => 'Afwijzen',
                            'icon' => 'delete',
                            'action' => 'rental-dashboard/cancel-website-deal',
                            'confirm' => true
                        ],
                    ]
                ];
            } else {

                $actions = [
                    'buttons' => [

                        [
                            'title' => 'Groepsbezichtiging bevestigen',
                            'icon' => 'tick',
                            'action' => 'rental-dashboard/group-viewing-website-deal',
                            'confirm' => true,
                            'newWindow' => true,
                        ],
                        $finishWebsiteDealButton,
                        [
                            'title' => 'Afwijzen',
                            'icon' => 'delete',
                            'action' => 'rental-dashboard/cancel-website-deal',
                            'confirm' => true
                        ],
                    ]
                ];

                if (!isset($statusTemplateRow) || !$statusTemplateRow['finish_website_deal_always_allowed']) {
                    unset($actions['buttons'][1]);
                }
            }

            $types = [
                'userid' =>	['title' => 'Kandidaat', 'width' => 'xxlarge'],
                'bdate' =>	['title' => 'Geb. datum', 'width' => 'xsmall', 'type' => 'date'],
                'income_amount_as_float' => ['title' => 'Inkomen', 'width' => 'xsmall '],
                'source_of_income' =>	['title' => 'Bron inkomen', 'width' => 'large truncate'],
                'partner_income_amount_as_float' => ['title' => 'Parner Inkomen', 'width' => 'small '],
                'source_of_income_partner' =>	['title' => 'Bron inkomen', 'width' => 'small'],
                'spacer' =>	['title' => '', 'width' => 'xxxxsmall'],
                'living_situation' =>	['title' => 'Woonsituatie', 'width' => 'medium'],
                'created_on' => ['title' => 'Datum', 'width' => 'xxsmall right', 'type' => 'date'],
                'active_deals' => ['title' => 'Deals', 'width' => 'xxsmall right'],
                'spacer2' =>	['title' => '', 'width' => 'xxxxsmall'],
                'assigned_user_id' => ['title' => '', 'width' => 'hidden'],
                'cancelled' => ['title' => '', 'width' => 'hidden'],
                'is_best_proposition' => ['title' => '', 'width' => 'hidden']
            ];

            if ($showCancelledOnly) {
                $types = ['spaces' => ['title' => '', 'width' => 'xxxxsmall']] + $types;
            }

            $listview = $this->view->ListView($data, $params)
                ->setTypes($types)

				->addFormat(['source_of_income'], function($value, $item){

                    switch ($value) {
                        case 'Loondienst (onbepaald)':
                            $value=  'Loondienst (ob)';

                        case 'Loondienst (tijdelijk)':
                            $value= 'Loondienst (bp)';

                        case 'ondernemer':
                            $value= 'Ondernemer / ZZP';

                    }
                    return ucfirst($value. '<br/> ' .  ($item['profession'] ? '('.$item['profession'].')' : '(Beroep niet bekend)')  );


                })


                ->addFormat(['source_of_income_partner'], function($value, $item){

                    switch ($value) {
                        case 'Loondienst (onbepaald)':
                            return 'Loondienst (ob)';
                            break;
                        case 'Loondienst (tijdelijk)':
                            return 'Loondienst (bp)';
                            break;
                        case 'ondernemer':
                            return 'Ondernemer / ZZP';
                            break;
                    }
                    return ucfirst($value);


                })

                ->addFormat(['living_situation'], 'ucfirst')

                ->addFormat('userid', function($value, $item){

                    if ($item['profile_completion'] == 3) {
                        $profile_completion_preferred_asterix =  ' <b><sup>*</sup></b>' ;
                    } else if ($item['profile_completion'] == 0) {
                        $profile_completion_preferred_asterix =  ' <b><sup>**</sup></b>' ;
                    }

                    return $value > 0 ?
                        '<a  class="user_link" title="'. $item['phone_number'].'" href="rental-deals_timeline/for-user/id/' . $value . '" >' .
                        $item['user'] . $profile_completion_preferred_asterix .
                        '</a>'
                        : '<span class="user_link">-</span>';
                })

                ->addFormat('bdate', 'date')

                ->addFormat(['income_amount_as_float', 'partner_income_amount_as_float'], 'money')

                ->addFormat('consent_to_contact_from_budget_manager', function($value){
                    return $value === 'yes' ? '<i class="fa fa-check"></i>' : '<i class="fa fa-times"></i>';
                })

                ->addFormat('created_on', function($value){
                    return date('d-m-y', strtotime($value));
                })
                ->addFormat('active_deals', function($value, $item){

                    if ($item['show_payed_account'] == 'active') {
                        $payed_account_asterix = '  / <span style="color: green">&euro;<span>';
                    }

                    if ($item['show_payed_account'] == 'expired') {
                        $payed_account_asterix = '  / <span style="color: orange">&euro;<span>';
                    }

                    return  (RentalDeals::getNumberOfActiveDealsForUserId($item['user_id']). $payed_account_asterix);
                })

                ->addFormat('number_of_tenants', function($value){
                    return $value > 0 ? $value : '-';
                })

                ->addDetails(
                    'rental-dashboard/website-deal-proposition-objects/noAjax/1/',
                    ['style' => 'popup']
                )

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'contract' => ['type' => 'input'],
                    'object' => ['type' => 'input'],
                    'bdate' => ['type' => 'range'],
                    'userid' => ['type' => 'input'],
                    'source_of_income' => ['type' => 'select'],
                    'living_situation'  => ['type' => 'select'],
                     'income_amount_as_float' => [ 'type' => 'range'],
                    'partner_income_amount_as_float' => [ 'type' => 'range'],
                    'combined_status' => []
                ])

                ->setOptions([
                    'filter_sort_by_levenstein' => true,
                    'show_view_template_buttons' => false,
                    'item_title' => 'Deal',
                    'show_title' => false,
                    'paginator' => false
                ])
            ;

            if (!$showCancelledOnly) {
                $listview->addActions($actions);
            }

            $listview->render($this);
        }

        public function websiteDealPropositionObjectsAction(){

            $select = db()->select()
                ->from(['rd' => 'rental_deals'], false)
                ->joinLeft(['u' => 'users'], 'u.id = rd.user_id')
                ->where('rd.id = ?', $this->getParam('id'))
            ;

            $user_data = db()->fetchRow($select);

            $this->setParam('user_id', $user_data['id']);

            $this->view->Breadcrumbs()
                ->addCrumb('Website aanvragen - ' . $user_data['rendered_name']);



            if(is_null($this->_getParam('combined_status'))) {
                $this->setParam('combined_status', 'current');
            }

            $params = ['url-override' => 'rental-dashboard/website-deal-proposition-objects', 'user_id' => $this->getParam('user_id'), 'combined_status' => $this->_getParam('combined_status')];

            $model = new Controllers_RentalDashboard_Deals();
            $model->setOrders([
                // order preferences: order by simular income, then prefer users with budget manager
                'CONCAT(ROUND(u.income_amount / 10), "_", IF(u.consent_to_contact_from_budget_manager = "yes", 1, 0)) DESC',
            ]);
            $model->setFilters([
                'user_id_correct' => $this->getParam('user_id'),
                'website_deal' => 'yes',
                'vidii_deal' => 'no',
                'cancelled' => false,
                'archived' => false,
                'final_deal' => nullValue()
            ]);

            $data = $model->getList();

            $data = $this->websiteDealsAddCounts($data);

            $this->view->ListView($data, $params)

                ->setTypes( [
                    'object_id' =>	['title' => '', 'width' => 'hidden'],
                    'object' =>	['title' => 'Adres', 'width' => 'xxlarge truncate'],
                    'proposition_counter' =>	['title' => 'Aantal kandidaten', 'width' => 'large right'],
                    'other_deals_count' =>	['title' => 'Aantal actuele deals voor object', 'width' => 'large right'],
                    'spacer' =>	['title' => '', 'width' => 'xxxxsmall'],
                ])

                ->addFormat('object', function($value, $row) {
                    return $row['object_id'] > 0 ? '<a href="support/show/id/' . $row['object_id'] . '/type/object" target="_blank">' . $value . '</a>' : '-';
                })

                ->addFormat('other_deals_count', function($value) {
                    return $value > 0 ? $value : '-';
                })

                ->addFormat('combined_status', function($value){
                    $status_labels = [
                        'cancelled' => 'Geannuleerd',
                        'archived' => 'Geselecteerd',
                        'current' => 'Actueel',
                    ];
                    return $status_labels[$value];
                })

                ->setFilters([
                    'object_id' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
                    'object' => ['type' => 'input']
                ])

                ->setOptions([
                    'filter_sort_by_levenstein' => true,
                    'show_view_template_buttons' => false,
                    'item_title' => 'Deal',
                    'show_title' => true,
                    'paginator' => false
                ])

                ->render($this);
            ;
        }

        public function groupViewingWebsiteDealAction(){
		    $this->disableView();

		    $ids = $this->getParam('ids');
			$rental_deal_model = new RentalDeals();

		    foreach($ids as $id){
                $rentalDealModelRow = $rental_deal_model
                    ->getById($id);

                $statusTemplateRow = (new RentalDealsStatusTemplates())->getById(
                    $rentalDealModelRow->status_template_id
                );

                $rentalDealModelRowAttributes = [
                    'website_deal' => 'no'
                ];

                if ($statusTemplateRow->start_status_type_id_group_viewing) {
                    $rentalDealModelRowAttributes['next_status_id'] =
                        $statusTemplateRow->start_status_type_id_group_viewing;
                }

                $dealId = $rentalDealModelRow
					->setFromArray($rentalDealModelRowAttributes)
					->save();

                if ($rentalDealModelRowAttributes['next_status_id']) {
                    $rental_deal_model->setNextStatusId(
                        $dealId,
                        $rentalDealModelRowAttributes['next_status_id']
                    );
                }

                (new RentalDeals())->setContactDateAfterFinishingWebsiteDeal($dealId);
			}

		    $model = new CalendarEventsTypes();

		    $calendarEventsTypeAttributes = [
		        'name' => 'Groepsbezichtiging',
                'is_rental' => true,
            ];

		    $eventTypeRow = $model->matchRow($calendarEventsTypeAttributes);

		    if(!$eventTypeRow){
		        $eventTypeRow = $model->createRow($calendarEventsTypeAttributes);
                $eventTypeRow->save();
            }

		    $this->redirectDealToCreateNewCalendarEvent(
		        $ids,
                'Groepsbezichtiging ',
                $eventTypeRow->id
            );
        }

        public function finishWebsiteDealAction()
        {
            $this->disableView();

            $dealIds = $this->getParam('ids');
            $rentelDealsModel = new RentalDeals();

            foreach ($dealIds as $dealId) {
                $rentelDealsRow = $rentelDealsModel->getById($dealId);

                $rentelDealsRow
                    ->setFromArray([
                        'website_deal' => 'no'
                    ])
                    ->save();


                (new RentalDeals())->setToStartStatus($dealId);


                (new RentalDeals())->setContactDateAfterFinishingWebsiteDeal($dealId);

                if ($rentelDealsModel->dealShouldGetDirectConceptUserObject($rentelDealsRow)) {
                    (new CreateConceptUserObjectServiceForDealService($rentelDealsModel))
                        ->execute($dealId);
                }


                if (Settings::get('rental_deal_create_uo_concept_after_website_viewing')) {

                    $create_uo_service = new \Controllers\User\RegistrationCreateUserObjectService(
                        $rentelDealsRow['object_id'],
                        $rentelDealsRow['user_id']
                    );

                    $new_uo_id = $create_uo_service->execute();

                    $rentelDealsRow
                        ->setFromArray([
                            'concept_deal_uo_id' => $new_uo_id
                        ])
                        ->save();

                }


                //alleen bij doorzetten zonder bezichtiging.
                if (Settings::get('rental_deal_request_documents_after_website_viewing')) {

                    $service = new RegistrationRequestAdditionalDocumentsService();

                    try {
                        $service->execute($rentelDealsRow['user_id'], $rentelDealsRow['object_id']);
                    } catch (InvalidArgumentException $e) {
                        error_log($e);
                    }

                 }

            }
        }

        public function viewingWebsiteDealAsyncPartAction()
        {
            $this->disableView();
            
            $rentalDealsModelRowId = $this->getParam('deal_id');

            $rentalDealsModel = new RentalDeals();
            $rentalDealsModelRow = $rentalDealsModel->getById($rentalDealsModelRowId);

            $sendDeniedDealMailService = new SendDeniedDealMailService(
                new Objects(),
                new Email(),
                new User()
            );

            $cancelOtherRentalDealsForSameObjectService = new CancelOtherRentalDealsForSameObjectService(
                $sendDeniedDealMailService
            );

            $cancelOtherRentalDealsForSameObjectService->execute(
                $rentalDealsModelRow->user_id,
                $rentalDealsModelRow->object_id,
                $rentalDealsModelRow->id
            );
        }

        public function viewingWebsiteDealAction()
        {
            $deal_ids = $this->getParam('ids');

			if(count($deal_ids) != 1){
				die();
			}

            $websiteDealConfirmService = new Controllers_RentalDashboard_WebsiteDealConfirm();

            $inform_current_tenant = Settings::get('modules_rental_website_deal_viewing_inform_current_tenant');

            if($inform_current_tenant){
                $websiteDealConfirmService->setInformCurrentTenant(true);
            }

            $rentelDealsModel = new RentalDeals();
            foreach($deal_ids as $deal_id){
                $websiteDealConfirmService->execute($deal_id);

                $rentelDealsRow = $rentelDealsModel->getById($deal_id);
                if ($rentelDealsModel->dealShouldGetDirectConceptUserObject($rentelDealsRow)) {
                    (new CreateConceptUserObjectServiceForDealService($rentelDealsModel))
                        ->execute($deal_id);
                }

                startWorker('viewing-website-deal-async-part/deal_id/' . $deal_id . '/', 'rental-dashboard');
            }

            if(!$inform_current_tenant || !$websiteDealConfirmService->hasCurrentTenantId()) {
                $this->disableView();

                $model = new CalendarEventsTypes();

                $calendarEventsTypeAttributes = [
                    'name' => 'Bezichtiging',
                    'is_rental' => true,
                ];

                $eventTypeRow = $model->matchRow($calendarEventsTypeAttributes);

                if(!$eventTypeRow){
                    $eventTypeRow = $model->createRow($calendarEventsTypeAttributes);
                    $eventTypeRow->save();
                }

                $this->redirectDealToCreateNewCalendarEvent(
                    $deal_ids,
                    'Bezichtiging ',
                    $eventTypeRow->id
                );
            } else {
                $this->view->Breadcrumbs()
                    ->addCrumb('Website aanvragen', 'rental-dashboard/website-deals/')
                    ->addCrumb('Bezichtiging bevestigen');
            }

        }

        private function redirectDealToCreateNewCalendarEvent($dealIds, $title_prefix, $eventTypeId){

		    $userIds = [];
		    foreach($dealIds as $dealId) {
                $select = db()->select()
                    ->from(['rd' => 'rental_deals'])
                    ->joinLeft(['o' => 'objects'], 'o.id = rd.object_id', ['rendered_address'])
                    ->where('rd.id = ?', $dealId);

                if ($deal = db()->fetchRow($select)) {
                    $userIds[] = $deal['user_id'];
                }
            }

            if (!$deal) {
                die('Geen deal gevonden');
            }

            $url_parts = [
                'user' => implode(',', $userIds),
                'object' => $deal['object_id'],
                'event_type_id' => $eventTypeId,
                'map_to_type' => 'deal',
                'map_to_id' => $deal['id'],
                'summary' => urlencode(str_replace('/', '|', $title_prefix . $deal['rendered_address']))
            ];

            $url_part_string = '';

            foreach($url_parts as $url_part_key => $url_part_value){
                $url_part_string .= $url_part_key . '/' . $url_part_value . '/';
            }

            $this->redirect('calendar/dashboard/' . $url_part_string);
        }

		public function finishConceptDealAction()
		{
			$this->disableView();

			$deal_ids = $this->getParam('ids');

			foreach ($deal_ids as $deal_id) {

				$rental_deal_model = new RentalDeals();

				$rental_deal_model_instance = $rental_deal_model->getById($deal_id);

				if ($rental_deal_model_instance === null
					|| (Settings::get('general_company_shortname') !== 'MVGM'
						&& $rental_deal_model_instance->cancelled
					)
				) {
					continue;
				}

				$other_rental_deal_models_select = $rental_deal_model
					->select()
					->where('(user_id = "' . $rental_deal_model_instance->user_id . '") OR (object_id = "' . $rental_deal_model_instance->object_id . '")')
					->where('id != ?', $rental_deal_model_instance->id)
					->where('concept_deal = ?', 'yes')
					->where('cancelled = ?', 0)
					->where('archived = ?', 0);

				$other_rental_deal_models = $rental_deal_model->fetchAll($other_rental_deal_models_select);

				foreach ($other_rental_deal_models as $other_rental_deal_model) {
					$other_rental_deal_model
						->setFromArray([
                            'cancelled' => true,
                            'cancel_date' => date('Y-m-d H:i:s'),
                            'cancel_by_user' => loginManager::data()->id,
						])
						->save();
				}

				$rental_deal_model_instance
					->setFromArray([
						'archived' => true,
						'concept_uo_deal' => true,
					])
					->save();

				$other_deal_ids = array_column($other_rental_deal_models->toArray(), 'id');
				startWorker('finish-concept-deal-async-part/deal_id/' . $deal_id . '/other_deal_ids/' . implode(',', $other_deal_ids) . '/', 'rental-dashboard');
			}
		}

        public function finishConceptDealAsyncPartAction()
        {
            $this->disableView();

            $deal_id = $this->getParam('deal_id');
			$sendDeniedDealMailService = new SendDeniedDealMailService(
				new Objects(),
                new Email(),
                new User()
			);

			$rental_deal_model = new RentalDeals();

			$rental_deal_model_instance = $rental_deal_model->getById($deal_id);

			if ($this->_getParam('other_deal_ids') > 0) {

				$other_rental_deal_models_select = $rental_deal_model
					->select()
					->where('id IN (' . $this->getParam('other_deal_ids') . ')');

				$other_rental_deal_models = $rental_deal_model->fetchAll($other_rental_deal_models_select);
			} else {
				$other_rental_deal_models = [];
			}


			$cancelled_grouped_by_user_and_object = [];
			foreach($other_rental_deal_models as $other_rental_deal_model) {
				$cancelled_grouped_by_user_and_object[$other_rental_deal_model->user_id . '_' . $other_rental_deal_model->object_id] = $other_rental_deal_model;
			}

			foreach($cancelled_grouped_by_user_and_object as $other_rental_deal_model){
				try {
					$sendDeniedDealMailService->execute([
						'userId' => $other_rental_deal_model->user_id,
						'objectId' => $other_rental_deal_model->object_id
					]);
				} catch (InvalidArgumentException $e) {
				}
			}

            $create_uo_service = new \Controllers\User\RegistrationCreateUserObjectService(
                $rental_deal_model_instance->object_id,
                $rental_deal_model_instance->user_id
            );

			$new_uo_id = $create_uo_service->execute();

			$rental_deal_model_instance
				->setFromArray([
					'concept_deal_uo_id' => $new_uo_id
				])
				->save();

			$service = new Controllers\User\RegistrationRequestAdditionalDocumentsService();

			try {
                $service->execute(
                    $rental_deal_model_instance->user_id,
                    $rental_deal_model_instance->object_id
                );
			} catch ( InvalidArgumentException $e ) {
			}
        }

		public function cancelConceptDealAction()
		{
			$this->disableView();

			$dealIds = (array)$this->getParam('ids', []);

			$service = new CancelConceptDeal(
				new RentalDeals(),
				new CompositeSpecification([])
			);

			$service->execute(['dealIds' => $dealIds]);
		}

		public function sendStillInterestedAction() {
			$this->disableView();
			$params = ['ids' => $this->getParam('ids')];
			$userDetails = [];
			$rd = new RentalDeals();

			foreach ($params['ids'] as $id) {
				$rentalDealRow = $rd->getById($id);
				if (null === $rentalDealRow) {
					continue;
				}
				$userDetails[] = [
					'id' => $rentalDealRow->user_id,
					'object' => $rentalDealRow->object_id
				];
			}

			foreach ($userDetails as $details) {
				$object = (new Objects())->getById($details['object']);

				$corperationId = (new Corporation())->getCorporationFromObject($details['object'])['id'];
				$projectId = \Objects::getProject($details['object'])['id'];
				$from = (new Email())->getMailaccountForSend($corperationId, $projectId);

				if (null === $object || null === $from) {
					continue;
				}

				try {
					$naw = db()->select()
						->from(['u' => 'users'], ['name' => 'u.rendered_name'])
						->where('u.id = ?', $details['id'])
						->joinLeft(
							['ea' => 'crm_email_address'],
							"ea.map_to = u.id AND ea.type = 'user'",
							['email' => 'address']
						)
						->query()
						->fetch();
				} catch (Zend_Db_Statement_Exception $e) {
					continue;
				}

				$data = [
					'name' => User::buildnameMail(false, $details['id']),
					'object' => $object->rendered_address
				];

				new EmailOutgoing([
					'to' => ['email' => $naw['email']],
					'from' => [
						'email' => $from['address'],
						'name' => $from['name']
					],
					'subject' => 'Heeft u nog interesse?',
					'data' => $data,
					'template' => 'rental/still-interested.phtml'
				]);
			}
		}

		public function cancelWebsiteDealAction()
		{
			$this->disableView();

			$dealIds = (array)$this->getParam('ids', []);

			$service = new CancelConceptDeal(
				new RentalDeals(),
				new CompositeSpecification([
					new WebsiteDealSpecification()
				])
			);

			$service->execute(['dealIds' => $dealIds]);
		}

        public function sendDeniedDealMailAction()
        {
            $this->disableView();

            $dealId = $this->getParam('deal_id', []);

            $sendDeniedDealMailService = new \Controllers\RentalDashboard\SendDeniedDealMailForDealIdService(
                new RentalDeals(),
                new SendDeniedDealMailService(
                    new Objects(),
                    new Email(),
                    new User()
                )
            );

            $sendDeniedDealMailService->execute($dealId);
        }

		public function contactsAction(){

			$this->view->Breadcrumbs()
				->addCrumb('Contacten');

			if(is_null($this->getParam('sorting')))
				$this->setParam('sorting', json_encode(['name' => 'DSC']));

			$model = new Controllers_RentalDashboard_Contacts();
			$model->setFilters($this->filtered_params);

			$this->view->ListView($model->getList(), ['url-override' => 'rental-dashboard/contacts'])

				->setTypes([
				    'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
					'name' => ['title' => 'Naam', 'width' => 'xlarge'],
					'type' =>	['title' => 'Type', 'width' => 'small'],
					'map_to' =>	['title' => 'Gekoppeld', 'width' => 'small'],
				] + $this->general_types)

				->addDetails('rental-dashboard/contacts-details', ['style' => 'popup'])

				->addFormat('map_to', function($value){
					if($value == 'project') return 'Project';
					if($value == 'objectgroup') return 'Objectgroep';
					if($value == 'object') return 'Object';

					return '-';
				})


				->setFilters([
					'name' => ['type' => 'input'],
					'type' => [],
					'map_to' => [],
				] + $this->general_filters)

				->setOptions(array(
					'filter_sort_by_levenstein' => true,
					'show_view_template_buttons' => false,
					'item_title' => 'Contact',
					'paginator_items_per_page' => 15,
					'title_icon' => 'fa-wrench',
				))

				->render($this);

		}

		public function contactsDetailsAction(){
			$model = new Controllers_RentalDashboard_Contacts();
			$this->view->data = $model->getDetails($this->getParam('id'));
		}
	}
