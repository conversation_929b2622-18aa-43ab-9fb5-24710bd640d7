<?

class ProfileController extends GlobalController {
	public function preDispatch() {
		$action = $this->getRequest()->getActionName();
		
		$disabled = $this->getDisabledTabs();
		
		if (in_array($action, $disabled))
			$this->forwardToAllowedTab();
	}
	
	protected function forwardToAllowedTab() {
		$tabs = array_reverse($this->getTabs());
		$disabled = $this->getDisabledTabs();
		foreach ($tabs as $tabName => $tabDef) {
			if (!in_array($tabName, $disabled))
				$this->_redirect("profile/{$tabName}");
		}
	}

	public function userAction() {
		$userId = loginManager::data()->id;
		
		$usrModel = new User();
		$usrModel->user = $userId;
		
		$usrForm = new Form($this, 'profile/user');
		
		$profile = $usrModel->getProfile();
		$usrForm->setDefaults($profile['user']);
		
		if ($this->getRequest()->isPost() && $usrForm->isValid($this->getRequest()->getPost())) {
			$fData = $usrForm->getValues();
			if ($fData['password'] == $fData['password_check']) {
				if ($fData['password'] && $fData['password_check']) { 
					$usrTable = new Users();
					$usrRow = $usrTable->fetchRowById($userId);
					
					$usrTable->setField($usrRow, 'password', $fData['password']);
					$usrRow->save();
				}
				
				$this->setCompletion(1);
				$this->_redirect('profile/general');
			}
		}
		
		$this->view->form = $usrForm;
		
		$this->setTabsInView('user');
	}
	
	public function generalAction() {
		$userId = loginManager::data()->id;
		
		$generalForm = new Form($this, 'profile/general');
		
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById($userId);
		
		$addrTable = new Address();
		$addr = $this->getUserAddressByUserId($userId);
		
		$generalForm->setDefaults($usrRow->toArray());
		$generalForm->setDefaults($addr->toArray());
		$generalForm->setDefaults(array(
			'emailcheck' => $addr->email,
			'phone_home' => $addr->phone1,
			'phone_secondary' => $addr->phone2,
			'bdate'      => $this->reverseDate($usrRow->bdate),
		));
		
		if ($this->getRequest()->isPost() && $generalForm->isValid($this->getRequest()->getPost())) {
			
			$fData = $generalForm->getValues();
			$fData['bdate'] = $this->reverseDate($fData['bdate']);
			$usrTable->setFields($usrRow, $fData);
			$usrRow->save();
			
			$fData['phone1'] = $fData['phone_home'];
			$fData['phone2'] = $fData['phone_secundary'];
			$addrTable->setFields($addr, $fData);
			$addr->save();
			
			$this->setCompletion(2);
			$this->_redirect('profile/address');
		}
		
		
		
		$this->view->form = $generalForm;
		
		$this->setTabsInView('general');
	}
	
	public function addressAction() {
		$userId = loginManager::data()->id;
	
		$addressForm = new Form($this, 'profile/address');
		
		$uipTable  = new UserInvoicePreferences();
		$uipRow = $uipTable->getRowByUserId($userId);
		
		$addrTable = new Address();
		$objAddr = $this->getObjectAddressByUserId($userId);
		$usrAddr = $this->getUserAddressByUserId($userId);
		
		$addressForm->object_address->setDefaults($objAddr->toArray());
		$addressForm->user_address->setDefaults($usrAddr->toArray());
		if ($uipRow->address == $usrAddr->id) {
			$addressForm->user_address->setDefaults(array(
				'invoice' => 'user',
			));
		}
		
		if ($this->getRequest()->isPost() && $addressForm->isValid($this->getRequest()->getPost())) {
			$fData = $addressForm->getValues();
			
			if ($fData['user_address']['invoice'] == 'user') {
				//echo "<pre>"; var_dump($fData); echo "</pre>"; die();
				$addrTable->setFields($usrAddr, $fData['user_address']);
				$usrAddr->save();
				
				$uipRow->address = $usrAddr->id;
			} else {
				$uipRow->address = $objAddr->id;
			}
			
			$uipRow->save();
			
			$this->setCompletion(3);
			$this->_redirect('profile/meters');
		}
		
		$this->view->form = $addressForm;
		
		$this->setTabsInView('address');
	}
	
	public function metersAction() {
		$userId = loginManager::data()->id;
	
		$completionForm = new Form($this, 'profile/meters');
		$objModel = new Object();
		$objTable = new Objects();
		$uo = $this->getUserObject($userId);
		$object = $objTable->fetchRowByUserId($userId);
		$meters = $objModel->getMeters($object->id, false, 0, $uo->from);
		
		if ($this->getRequest()->isPost()) {
			$post = $this->getRequest()->getPost();
			
			$uo->from = $this->reverseDate($post['date']);
			$uo->save();
			$this->saveMeters($object->id, $uo->from, $post['meters']);
			
			$this->setCompletion(4);
			if (loginManager::data()->info['commercial']) {
				$this->_redirect('profile/contact');
			} else {
				$this->_redirect('profile/occupants');
			}
		}
		
		$this->view->form = $completionForm;
		$this->view->meters = $meters;
		
		$this->setTabsInView('meters');
	}
	
	protected function saveMeters($objectId, $date, $meters) {
		$userId = loginManager::data()->id;
	
		$consTable = new ObjectsConsumptions();
		foreach ($meters as $meterId => $value) {
			if (trim($value))
				$consTable->saveUnverifiedConsumption($meterId, $userId, $objectId, $date, $value, null, 'user');
		}
	}
	
	public function occupantsAction() {
		$userId = loginManager::data()->id;
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById($userId);
		
		$occupantsForm = new Form($this, 'profile/occupants');
		
		$occupantsForm->setDefaults($usrRow->toArray());
		
		if ($this->getRequest()->isPost() && $occupantsForm->isValid($this->getRequest()->getPost())) {
			$fData = $occupantsForm->getValues();
			
			$usrTable->setFields($usrRow, $fData);
			$usrRow->save();
			
			$this->setCompletion(5);
			$this->_redirect('profile/payment');
		}
		
		$this->view->form = $occupantsForm;
		
		$this->setTabsInView('occupants');
	}
	
	public function paymentAction() {
		$userId = loginManager::data()->id;
		//echo "<h1>{$userId}</h1>";
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById($userId);
		
		$uipTable = new UserInvoicePreferences();
		$uipRow = $uipTable->getRowByUserId($userId);
	
		$paymentForm = new Form($this, 'profile/payment');
		$paymentForm->user->setDefaults($usrRow->toArray());
		$paymentForm->invoice_preferences->setDefaults($uipRow->toArray());
		
		if ($this->getRequest()->isPost() && $paymentForm->isValid($this->getRequest()->getPost())) {
			$fData = $paymentForm->getValues();
			
			$usrTable->setFields($usrRow, $fData['user']);
			$usrRow->save();
			
			$uipTable->setFields($uipRow, $fData['invoice_preferences']);
			$uipRow->save();
			
			$this->setCompletion(6);
			$this->_redirect('profile/contract');
		}
		
		$this->view->form = $paymentForm;
		
		$this->setTabsInView('payment');
	}
	
	public function representativeAction() {
		$userId = loginManager::data()->id;
		
		$reprForm = new Form($this, 'profile/representative');
		
		$uoRow  = $this->getUserObject($userId);
		
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById($userId);
		//$usrRow = $this->getUserByUoRole($uoRow->object, 'legal');
		
		$reprForm->setDefaults($usrRow->toArray());
		
		if ($this->getRequest()->isPost() && $reprForm->isValid($this->getRequest()->getPost())) {
			$fData = $reprForm->getValues();
			
			$usrTable->setFields($usrRow, $fData);
			$usrRow->save();
			
			$this->setCompletion(3);
			$this->_redirect('profile/meters');
		}
		
		$this->view->form = $reprForm;
	
		$this->setTabsInView('representative');
	}
	
	public function contactAction() {
		$userId = loginManager::data()->id;
	
		$contactForm = new Form($this, 'profile/contact');
		
		$objectId = $this->getObjectId();
		//die($objectId);
		
		$usrTable = new Users();
		$uoRow = $this->getUserObjectByRole($objectId, 'emergency');
		$usrRow = $this->getUserByUoRole($objectId, 'emergency');
		//var_dump($usrRow->toArray());
		
		if ($usrRow->id != $userId) {
			$contactForm->userselect->setValue('custom');
			$contactForm->setDefaults($usrRow->toArray());
			
			$addrRow = $this->getUserAddressByUserId($usrRow->id);
			$contactForm->setDefaults(array(
				'phone' => $addrRow->phone1,
				'email' => $addrRow->email,
			));
		}
		
		if ($this->getRequest()->isPost() && $contactForm->isValid($this->getRequest()->getPost())) {
			$fData = $contactForm->getValues();
			if ($fData['userselect'] == 'contractee') {
				$uoRow->customer = $userId;
				$uoRow->save();
				
			} else {
				if ($usrRow->id == $userId)
					$usrRow = $usrTable->createRow();
					
				$usrTable->setFields($usrRow, $fData);
				$usrRow->save();
				
				$uoRow->customer = $usrRow->id;
				$uoRow->save();
				
				$addrRow = $this->getUserAddressByUserId($usrRow->id);
				$addrRow->phone1 = $fData['phone'];
				$addrRow->email  = $fData['email'];
				$addrRow->save();
			}
			
			$this->setCompletion(5);
			$this->_redirect('profile/payment');
		}
		
		$this->view->form = $contactForm;
		$this->setTabsInView('contact');
	}
	
	public function contractAction() {
		$this->testContracting();
		
		$user = new User();
		$user->user = loginManager::data()->id;

		$this->setTabsInView('contract');

		// get or generate contract
		$contractId = $this->_getParam('id');
		if (!is_numeric($contractId)) {
			$this->view->contracts = $user->getContract();

			$allsigned = true;
			$notsigned = false;
			if (is_array($this->view->contracts)) {
				foreach ($this->view->contracts as $i => $contract) {
					$this->view->contracts[$i]['filename'] = $contract['company'] ? 'Leveringsovereenkomst ' . $contract['company'] : 'Leveringsovereenkomst ' . $contract['address'] . ' ' . $contract['number'];
					if ($contract['signed'] == 0) {
						$allsigned = false;
						$notsigned = $contract['id'];
					}
					//$allsigned = $contract['signed'] == 0 ? false : $allsigned;
					//$notsigned = $contract['signed'] == 0 ? $contract['id'] : $notsigned; //$nonesigned;
				}
			}

			if ($allsigned && $this->firstLogin) {
				$user->endFirst(); // end first session
				$this->_helper->redirector('contract', 'profile');
			} elseif ($notsigned){
				$this->_helper->redirector('contract', 'profile', false, array('id' => $notsigned));					
			}

			// sign or export contract
		} else {
			//$contract = $user->getContract($this->_getParam('id'));
			//$user->exportContract($contract['object']);
			$contract = $user->getContract($contractId);
			
			if ($contract['company']) {
				$filename = "Leveringsovereenkomst {$contract['company']}";
			} else {
				$filename = "Leveringsovereenkomst {$contract['address']} {$contract['number']}";
			}
			
			$this->view->filename = $filename;

			if ($this->_getParam('signed')) {
				$contract = $user->addContract($contractId, $filename);
		
				$user->signContract($contract);
				$user->endFirst();
				$this->setCompletion(7);
				$this->_helper->redirector('signed', 'profile');
			}

			
			if ($contract['signed']) {
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);
				
				
				$this->view->contract = $user->exportContract($contract['object'], $filename);
			} elseif ($this->_getParam('preview')){
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);
				
				$this->view->contract = $user->generateContract($contractId, $filename, true);
			} else {
				$this->view->contract = $contract;
				$this->render('sign');
			}
		}
		
		
	}
	
	public function signedAction() {
			$this->view->commercial = loginManager::data()->info['commercial'];
		}
	
	protected function testContracting(){
		$select = db()->select()

			->from(array('u' => 'users'), false)

			->joinLeft(array('uo' => 'users_objects'), 'uo.customer = u.id AND (uo.till IS NULL OR uo.till >= NOW())', false)
																			
			->joinLeft(array('uor' => 'users_objects_register'), 'uor.user = u.id', false)

			->joinLeft(array('o' => 'objects'),
				'(o.id = uo.object AND uo.object > 0) OR
    			(o.id = uor.object AND uor.object > 0)', false)

			->joinLeft(array('og' => 'objectgroup'),
				'og.id = o.objectgroup', array('contracting'))

			->joinLeft(array('p' => 'projects'),
				'p.id = og.project', array('project_contracting' => 'contracting'))

			->where('u.id = ?', loginManager::data()->id);

		$data = db()->fetchRow($select);

		$contracting = isset($data['contracting']) ? $data['contracting'] : $data['project_contracting'];

		if(!$contracting){
			$u = new User();
			$u->user = loginManager::data()->id;
			$u->endFirst();

			$this->_helper->redirector('dashboard', 'user');
		} else {
			return true;
		}
	}
	
	protected function getObjectId() {
		$uoRow = $this->getUserObject(loginManager::data()->id);
		
		return $uoRow->object;
	}
	
	protected function getUserObject($userId) {
		$uoTable = new Objectusers();
		if($uoId = loginManager::data()->info['uo']) {
			return $uoTable->fetchRowByid($uoId);
			
		} else {
			$uoRegTable = new UsersObjectsRegister();
			$uoRegRow = $uoRegTable->fetchRow($uoRegTable->select()->where('user = ?', $userId));
			$uoRow = $uoTable->createRow();
			$uoRow->customer = $uoRegRow->user;
			$uoRow->object   = $uoRegRow->object;
			$uoRow->save();
			
			return $uoRow;
		}
	}
	
	protected function getUserByUoRole($objectId, $role) {
		$usrTable = new Users();
		
		$uoRow = $this->getUserObjectByRole($objectId, $role);
		
		if ($userId = $uoRow->customer) {
			$usrRow = $usrTable->fetchRowById($userId);
			
		} else {
			$usrRow = $usrTable->createRow();
			$usrRow->save();
			
			$uoRow->customer = $usrRow->id;
			$uoRow->save();
		}
		
		return $usrRow;
	}
	
	protected function getUserObjectByRole($objectId, $role) {
		$uoTable = new Objectusers();
		$select = $uoTable->select()
			->where('object = ?', $objectId)
			->where('role = ?', $role)
		;
		if (!$uoRow = $uoTable->fetchRow($select)) {
			$uoRow = $uoTable->createRow();
			$uoRow->object = $objectId;
			$uoRow->role   = $role;
		}
		
		return $uoRow;
	}
	
	protected function getUserAddressByUserId($userId) {
		$addrTable = new Address();
		$addr = $addrTable->get($userId, 'user');
		if (!$addr) {
			$addr = $addrTable->createRow();
			$addr->type = 'user';
			$addr->type_id = $userId;
		}
		
		return $addr;
	}
	
	protected function getObjectAddressByUserId($userId) {
		$addrTable = new Address();
		
		$ou = $this->getUserObject($userId);
		
		return $addrTable->get($ou->object, 'object');
	}
	
	protected function setCompletion($tabNum) {
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById(loginManager::data()->id);
		$usrRow->profile_completion = max($tabNum, $usrRow->profile_completion);
		$usrRow->save();
	}
	
	protected function getCompletion() {
		$usrTable = new Users();
		$usrRow = $usrTable->fetchRowById(loginManager::data()->id);
		
		return $usrRow->profile_completion;
	}
	
	protected function setTabsInView($activeTab) {
		if (loginManager::data()->info['commercial'])
			return $this->setCommercialTabsInView($activeTab);

		$this->view->activeTab = $activeTab;
		
		$completedCount = $this->getCompletion();
		$this->view->tabs = $this->getTabs();
		
		$completed = array();
		switch ($completedCount) {
			case 7:
				$completed[] = 'contract';
			case 6:
				$completed[] = 'payment';
			case 5:
				$completed[] = 'occupants';
			case 4:
				$completed[] = 'meters';
			case 3:
				$completed[] = 'address';
			case 2:
				$completed[] = 'general';
			case 1:
				$completed[] = 'user';
				break;
		}
		
		$disabled = $this->getDisabledTabs();
		
		$this->view->completed = $completed;
		$this->view->disabled  = $disabled;
	}
	
	protected function getTabs() {
		if (loginManager::data()->info['commercial'])
			return $this->getCommercialTabs();
	
		return array(
			'user' => array(
				'label' => "Inloggegevens",
				'url'   => 'profile/user',
			),
			'general' => array(
				'label' => "Algemeen",
				'url'   => 'profile/general',
			),
			'address' => array(
				'label' => "Adres",
				'url'   => 'profile/address',
			),
			'meters' => array(
				'label' => "Oplevering",
				'url'   => 'profile/meters',
			),
			'occupants' => array(
				'label' => "Gezinssituatie",
				'url'   => 'profile/occupants',
			),
			'payment' => array(
				'label' => "Betaalgegevens",
				'url'   => 'profile/payment',
			),
			'contract' => array(
				'label' => "Contract",
				'url'   => 'profile/contract',
			),
		);
	}
	
	protected function getDisabledTabs() {
		if (loginManager::data()->info['commercial'])
			return $this->getCommercialDisabledTabs();
			
		$completedCount = $this->getCompletion();
		
		$disabled = array();
		switch ($completedCount) {
			case 0:
				$disabled[] = 'general';
			case 1:
				$disabled[] = 'address';
			case 2:
				$disabled[] = 'meters';
			case 3:
				$disabled[] = 'occupants';
			case 4:
				$disabled[] = 'payment';
			case 5:
				$disabled[] = 'contract';
				break;
		}
		
		return $disabled;
	}
	
	protected function setCommercialTabsInView($activeTab) {
		$this->view->activeTab = $activeTab;
		
		$this->view->tabs = $this->getCommercialTabs();
		
		$completedCount = $this->getCompletion();
		
		$completed = array();
		switch ($completedCount) {
			case 7:
				$completed[] = 'contract';
			case 6:
				$completed[] = 'payment';
			case 5:
				$completed[] = 'contact';
			case 4:
				$completed[] = 'meters';
			case 3:
				$completed[] = 'representative';
			case 2:
			case 1:
				$completed[] = 'user';
				break;
		}
		
		$disabled = $this->getCommercialDisabledTabs();
		
		$this->view->completed = $completed;
		$this->view->disabled  = $disabled;
	}
	
	protected function getCommercialTabs() {
		return array(
			'user' => array(
				'label' => "Inloggegevens",
				'url'   => 'profile/user',
			),
			'representative' => array(
				'label' => "Vertegenwoordiger",
				'url'   => 'profile/representative',
			),
			'meters' => array(
				'label' => "Oplevering",
				'url'   => 'profile/meters',
			),
			'contact' => array(
				'label' => "Storingen",
				'url'   => 'profile/contact',
			),
			'payment' => array(
				'label' => "Betaalgegevens",
				'url'   => 'profile/payment',
			),
			'contract' => array(
				'label' => "Contract",
				'url'   => 'profile/contract',
			),
		);
	}
	
	protected function getCommercialDisabledTabs() {
		$completedCount = $this->getCompletion();
	
		$disabled = array();
		switch ($completedCount) {
			case 0:
			case 1:
				$disabled[] = 'representative';
			case 2:
				$disabled[] = 'meters';
			case 3:
				$disabled[] = 'contact';
			case 4:
				$disabled[] = 'payment';
			case 5:
				$disabled[] = 'contract';
				break;
		}
		
		return $disabled;
	}
	
	protected function reverseDate($date) {
		return implode('-', array_reverse(explode('-', $date)));
	}
}

?>
