<?
	class BrokerPublishController extends GlobalController {

        public function preDispatch(){
            $this->view->Breadcrumbs()->addCrumb('Belegger rapportage');
            parent::preDispatch();
        }

		public function indexAction() {
			$this->_helper->redirector('list', 'broker-publish');
		}

		public function listAction() {
            $this->view->Breadcrumbs()->addCrumb('Gepubliceerde rapportages');

			$bpModel = new BrokerPublish();
			$data = $bpModel->getList([]);
			$data = !empty($data) && is_object($data)? $data->toArray(): [];

			if(is_null($this->_getParam('sorting')))
				$this->_setParam('sorting', json_encode( [ 'corporation' => 'ASC' ] ));

			$this->view->listView($data)

				->setTypes([
					'corporation' => [
						'title' => 'Juridische entiteit',
						'width' => 'xlarge truncate',
					],
					'year' => [
						'title' => 'Jaar',
						'width' => 'medium truncate',
					],
					'published' => [
						'title' => 'Vastgezet',
						'width' => 'medium truncate',
					],
					'published_date' => [
						'title' => 'Op',
						'width' => 'small truncate',
					],
					'published_by' => [
						'title' => 'Door',
						'width' => 'medium truncate',
					],
					'edit_transcript' => [
						'title' => 'Verslag',
						'width' => 'xxsmall truncate',
					],
				])

				->addFormat('corporation', function($value) {
					return Corporations::getName($value)?: '-';
				})

				->addFormat('published', function($value) {
					return BrokerPublish::$publishedLabels[$value];
				})

				->addFormat('published_date', 'date')

				->addFormat('published_by', 'buildname')

				->addFormat('edit_transcript', function($value, $row) {
					$brokerPublishId = $row['id'];

					return "<a href='broker-publish/edit-transcript/id/{$brokerPublishId}'
						title='Bewerk hier het gespreksverslag.'>
						<i class='fa fa-comments fa-fw' aria-hidden='true'></i></a>";
				})

				->setFilters([
					'corporation' 	=> [],
					'year' 			=> [],
					'published' 	=> [],
					'published_date'=> ['type' => 'date_range'],
					'published_by' 	=> [],
				])

				->setOptions([
					'item_title' => 'Belegger rapport',
					'disabled_buttons_test' => function($row, $button){
						return ($button == 'edit' && !in_array($row['published'], ['Nee', 'Fout bij generatie', ]));
					},
				])

				->addButtons([
					'add' 		=> 'Toevoegen',
					'edit' 		=> 'Bewerken',
				])

				->addActions([
					'buttons' => [
                        	['title' => 'Verwijderen',			'icon' => 'delete',	'action' => 'broker-publish/delete',	'confirm' => true],
						['title' => 'Vastzetten/losmaken',	'icon' => 'locked',	'action' => 'broker-publish/publish',	'confirm' => true],
					]
				])	

				->addLegend('list-legend')

				->render($this)
			;
		}

		public function documentUploadAction(){
			$this->disableView(true);

			$this->view->id = $id = $this->_getParam('id');

			if($_POST){
				$bpdModel = new BrokerPublishDocs();
				$doc = Main::app()->getDir('document').'broker-publish-docs/' . $id . '/' . $_FILES['file']['name'];

				createFolder($doc);

				if(move_uploaded_file($_FILES['file']['tmp_name'], $doc)){

					$uniqid = uniqid();
					while(db()->fetchRow(db()->select()->from('broker_publish_docs')->where('hash = ?', $uniqid)))
						$uniqid = uniqid();

					$bpdModel->createRow()->setFromArray([
						'broker_publish' => $id,
						'filename' => basename($doc),
						'hash' => $uniqid
					])->save();
						
					$this->view->success = true;
				}
			}
		}

		public function getDocumentsAction(){
			$this->disableView();


			$id = $this->_getParam('id');

			$docs = db()->fetchPairs(db()->select()->from('broker_publish_docs', ['hash', 'filename'])->where('broker_publish = ?', $id));

			echo json_encode(count($docs) > 0 ? $docs : false);
		}

		public function deleteDocumentsAction(){
			$this->disableView();

			$bpdModel = new BrokerPublishDocs();

			$id = $this->_getParam('id');
			$hash = $this->_getParam('hash');

			$row = $bpdModel->matchRow(['broker_publish' => $id, 'hash' => $hash]);

			if(!$row) return;

			unlink(Main::app()->getDir('document').'broker-publish-docs/' . $row->broker_publish . '/' . $row->filename);

			$row->delete();
		}		

		public function editAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Belegger rapporten overzicht', 'broker-publish/list/')
				->addCrumb('Belegger rapport bewerken', '');

			$model = new BrokerPublish();

			$this->id = $this->_getParam('id');

			$this->files = $model->getFilesList($this->id);

			$this->view->EditView($this, 'BrokerPublish')
				->setOptions([
				])
				->render($this);
		}

		public function deleteAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$ids = (array) $this->_getParam('ids');

			foreach ($ids as $id)
				if(intval($id) > 0 && $bpRow = BrokerPublish::get($id))
					$bpRow->delete();
		}

		public function publishAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$ids = (array) $this->_getParam('ids');

			foreach ($ids as $id)
				if(intval($id) > 0 && $bpRow = BrokerPublish::get($id)) {
					if($bpRow->published !== 'yes') {
						$bpRow->published 		= 'generatable';
						$bpRow->published_date 	=  date("Y-m-d H:i:s");
						$bpRow->published_by 	= loginManager::data()->id;
						$bpRow->save();

                        $bp_model = new BrokerPublish();
                        $bp_model->linkFiles($id);
						
						startWorker('broker-publish');

					} else {
						BrokerPublish::unPublish($id);

                        $bp_model = new BrokerPublish();
                        $bp_model->unlinkFiles($id);
					}
				}

			$this->_helper->redirector('list', 'broker-publish');
		}

		public function editTranscriptAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Belegger rapporten overzicht', 'broker-publish/list/')
				->addCrumb('Gespreksverslag bewerken', '');

			$brokerPublishModel = new BrokerPublish();
			$brokerPublishId = $this->getParam('broker-publish-id');
			$brokerPublishId = $this->getParam('id');

			if(intval($brokerPublishId) <= 0 || !($brokerPublish = $brokerPublishModel->get($brokerPublishId))) {
				$this->redirect('broker-publish/list');
			}

			$transcriptForm = new Form($this, 'broker-publish/edit-transcript');
			if ($this->getRequest()->isPost() && $transcriptForm->isValid($this->getRequest()->getPost())) {

				if($brokerPublish->text_transcript != $transcriptForm->text_transcript->getValue()) {
					Journal::getForLinks([
						['type' => 'broker_publish', 'map_to' => $brokerPublishId, ],
					])->record('Het gespreksverslag bij deze beleggerrapportage is bijgewerkt', [
						'old_value' 	=> $brokerPublish->text_transcript,
						'new_value' 	=> $transcriptForm->text_transcript->getValue(),
					]);
				}

				$brokerPublish->text_transcript = $transcriptForm->text_transcript->getValue();
				$brokerPublish->save();
				$this->redirect('broker-publish/list');

			} else {
				$transcriptForm->populate($brokerPublish->toArray());
			}

			$this->view->transcriptForm = $transcriptForm;
		}

        public function financialFileUploadAction(){
            $this->view->Breadcrumbs()
				->addCrumb('Gepubliceerde rapportages', 'broker-publish/list')
                ->addCrumb('Uploaden financieel bestand');

            $this->view->id = $id = $this->_getParam('id');

            $form = new Form($this, 'broker-publish/financial-file-upload');

            if($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
                $data = $this->getRequest()->getPost();

                if(isset($_FILES['file'])){
                    $type_dir = $data['file_upload']['type'] == 'normal' ? 'financial_reports' : 'financial_project_reports';
                    $type_worker_name = $data['file_upload']['type'] == 'normal' ? 'financial-report-upload' : 'financial-report-project-upload';
                    $destination = Main::app()->getDir($type_dir) . $data['file_upload']['corporation'] . '/new/' . $_FILES['file']['name'];

                    createFolder($destination);
                    move_uploaded_file($_FILES['file']['tmp_name'], $destination);

                    startWorker($type_worker_name);

                    $this->view->uploaded = true;
                }
            }

            $this->view->form = $form;
        }

        public function deleteUploadAction(){
			$this->disableView();

			$id = $this->getParam('id');
            $type = $this->getParam('type');

            if($type == 'import') {
                $import_model = new BrokerFinancialReportImport();
                $value_model = new BrokerFinancialReportValue();
            } elseif($type == 'project_import') {
                $import_model = new BrokerFinancialReportProjectImport();
                $value_model = new BrokerFinancialReportProjectValue();
            }

            if(isset($import_model) && isset($value_model)) {
                $value_model->delete('import_id = ' . $id);
                $import_model->delete('id = ' . $id);
            }

            if($referer = $_SERVER['HTTP_REFERER'])
                header('Location: '. $referer);
        }
	}
?>
