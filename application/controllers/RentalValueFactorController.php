<?

	class RentalValueFactorController extends GlobalController {
	



        public function listAction(){
            $this->view->Breadcrumbs()->addCrumb('Huurwaarde factor');

            $this->view->modelListView('RentalValueFactor')

                ->setTypes(array('city', 'object_type' ,'value'))

                ->addFormat('is_default', 'bool')

                ->setOptions(array(
                    'item_title' => 'Huurwaarde factor',
                    'disabled_buttons_test' => function($row, $button){
                        return false;
                    }
                ))

                ->addButtons(array(
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen'
                ))

                ->render($this);
        }


        public function detailsAction(){
            $ostModel = new ObjectsSpacesTypes();
            $this->view->ostRow = $ostModel->getById($this->_getParam('id'));


            $this->view->Breadcrumbs()
                ->addCrumb('Ruimte lijst', 'spaces/list')
                ->addCrumb('Onderdelen lijst ruimte - ' . $this->view->ostRow->name_nl, 'spaces/details/id/' . $this->_getParam('id') . '/');
        }




        public function editAction(){
            $this->view->Breadcrumbs()
                ->addCrumb('Huurwaarde factoren', 'rental-value-factor/list')
                ->addCrumb('Huurwaarde bewerken');

            $this->view->form = $this->view->EditView($this, 'RentalValueFactor')
                ->setOptions([])
                ->render($this);
        }

        public function deleteAction(){
            $this->disableView();

            $ostModel = new RentalValueFactor();
            $ostRow = $ostModel->getById($this->_getParam('id'));

            $ostRow->delete();

            $this->_redirect('rental-value-factor/list');
        }
		


	}
