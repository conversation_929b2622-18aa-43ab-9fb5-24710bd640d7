<?

	include_once('Priority.php');

	class PriorityController extends GlobalController {

		public function indexAction() {
			$this->_forward('users');
		}

		/**
		 * 
		 * /
		public function manageAction() {

			$p = new Priority();
			$this->view->users = $p->history();
			$this->view->departments = $p->departments();

		}

		/**
		 * @ACL Week overzicht op manage pagina
		 */
		public function weekAction() {
			
			$this->disableView();

			$w = new PriorityWeek($this->_getParam('week'), $this->_getParam('year'));
			$week = $w->get();

			echo json_encode($week);

		}

		public function userAction() {
			$id = $this->_getParam('id') ? $this->_getParam('id') : loginManager::data()->id;
			$week = $this->_getParam('week') ? $this->_getParam('week') : date('W');
			$year = $this->_getParam('year') ? $this->_getParam('year') : date('Y');

			$pw = new PriorityWeek($week, $year);
			
			$this->view->user = $id;
			$this->view->tasks = $pw->forUser($id);
			$this->view->week = $pw->getWeek();
			$this->view->year = $pw->getYear();

		}

		public function usersAction() {
			

			$week = $this->_getParam('week') ? $this->_getParam('week') : date('W');
		
			$year = $this->_getParam('year') ? $this->_getParam('year') : date('Y');
			$department = (int) $this->_getParam('department') > 0 ? $this->_getParam('department') : null;

			// Niet te ver in de toekomst
			$p = new Priority();
			$chosenDate = weekToDate($year, $week);
			$currentDate = weekToDate(date('Y'), date('W'));
			$twoWeeks = 6048000;

			if ($chosenDate >= $currentDate + $twoWeeks) {
				$this->view->message = 'Kan niet verder dan 1 week in de toekomst.';
				$year = date('Y', strtotime("+1 week"));
				$week = date('W', strtotime("+1 week"));
			}

			$p = new Priority();
			$pw = new PriorityWeek($week, $year);

			$this->view->week = $week;
			$this->view->year = $year;
			$this->view->history = $p->history($userId = null, null, null, $limit = 5, $department);
			$this->view->selected = $p->history($userId = null, $week, $year, $limit = 1, $department);
			$this->view->tasks = $pw->tasks();
			$this->view->lastWeek = weekToDate($year, $week - 1);
			$this->view->departments = $p->departments();
			$this->view->department = $department;
			$this->view->users = $p->allUsers();

			$nextWeek = weekToDate($year, $week + 1);
			$this->view->nextWeek = ($nextWeek >= $currentDate + $twoWeeks) ? null : $nextWeek;

		}

		/**
		 * @ACL Opslaan van taken
		 */
		public function saveTaskAction() {
			$this->disableView();

			$data = $this->_getParam('task');
			$task = new PriorityTask();
			$task->edit($data);
			$task->save();

			echo json_encode($task->toArray());
		}

		/**
		 * @ACL Verwijderen van prioriteiten
		 * /
		public function deleteTaskAction() {
			$this->disableView();

			$data = $this->_getParam('task');

			$task = new PriorityTask($data);
			$task->delete();
		}

		/**
		 * 
		 */
		public function overviewAction() {

			
			$department = (int) $this->_getParam('department') > 0 ? $this->_getParam('department') : null;

			$p = new Priority();

			$history = $p->history($userId = null, date('W'), date('Y'), $limit = 45, $department);

			$this->view->history = $history;
			$this->view->departments = $p->departments();
			$this->view->department = $department;

		}

	}
