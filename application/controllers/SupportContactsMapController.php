<?

	class SupportContactsMapController extends GlobalController {

		public function preDispatch() {
			$this->view->type = $this->_getParam('type');
			$this->view->mapTo = $this->_getParam('mapTo');
		}

		/**
		 * List
		 * @ACL
		 */

        public function listAction(){
            $this->view->Breadcrumbs()
                ->addCrumb('Overzicht gekoppelde relaties');
            $this->view->extraWidth = true;


            $this->view->modelListView('SupportContactsMap')

                ->setTypes(['company_name', 'relation_type_name', 'map_to', 'type', 'city', 'project_name'])

                ->addFormat('company_name', function($value, $item){
                    return $item['dummy_user'] > 0 ? '<a href="support/show/id/' . $item['dummy_user'] . '/type/user" target="_blank">' . $value . '</a>' : '-';
                })


                ->addFormat('map_to', function($value, $item){
                    if ($item['type'] == 'user') {
                        return $item['user_id'] > 0 ? '<a href="support/show/id/' . $item['user_id'] . '/type/user" target="_blank">' . $item['rendered_name'] . '</a>' : '-';
                    } elseif ($item['type'] == 'object')  {
                        return $item['object_id'] > 0 ? '<a href="support/show/id/' . $item['object_id'] . '/type/object" target="_blank">' . $item['rendered_address'] . '</a>' : '-';
                    } elseif ($item['type'] == 'objectgroup'){
                        return $item['objectgroup_id'] > 0 ? '<a href="support/show/id/' . $item['objectgroup_id'] . '/type/object" target="_blank">' . $item['objectgroup_name'] . '</a>' : '-';
                    } elseif ($item['type'] == 'project'){
                        return $item['project_id'] > 0 ? '<a href="support/show/id/' . $item['project_id'] . '/type/project" target="_blank">' . $item['project_name'] . '</a>' : '-';
                    } else {
                        $value = '-';
					}

                    return trim($value) != '' ? $value : '-';
                })


                ->addFormat('type', function($value, $item) {
                    if ($value == 'user' || $value == 'company') {
                        $value = 'Huurder';
                    } elseif ($item['type'] == 'object')  {
                        return 'Object';
                    } elseif ($value == 'objectgroup'){
                        $value = 'Objectgroep';
                    } elseif ($value == 'project'){
                        $value = 'Project';
                    } elseif ($value == 'complaint'){
                        $value = 'Melding';
                    } elseif ($value == 'relation'){
                        $value = 'Relatie';
                    }

                    return $value;
                })

                ->setFilters([
                    'project_name' => ['type' => 'select', 'title' => 'Project' , 'renderSeparately' => true],
                    'relation_type_name' => ['type' => 'select', 'title' => 'Relatie type' , 'renderSeparately' => true],
                    'type' => ['type' => 'select'],
                    'map_to' => ['type' => 'input'],
                    'company_name' => ['type' => 'select']
                ])

                ->render($this);
        }



	}

?>
