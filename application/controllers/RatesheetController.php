<?

	class RatesheetController extends GlobalController {

		public function preDispatch() {
			//$this->maintenance('tim_administrator', '<PERSON><PERSON>');
			$lib = new Ratesheet();
		}



		public function doubleSizesAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$db = db();
			$rs = new RatesheetsSizes();

			$select = $db->select()
				->from(array('rs' => 'ratesheets_sizes'), array())
				->joinLeft(array('rs2' => 'ratesheets_sizes'),
				'rs2.id != rs.id AND
					rs2.ratesheet = rs.ratesheet AND
					rs2.field = rs.field AND
					rs2.type = rs.type AND
					rs2.from = rs.from AND
					rs2.till = rs.till', array('size' => 'id'))
				->joinLeft(array('rv' => 'ratesheets_values'), 'rv.size = rs2.id', array('id'))
				->where('rs2.id')
				->where('rv.id IS NULL');

			$rows = $db->fetchAll($select);

			foreach ($rows as $item) {
				$rs->delete('id = ' . $item['size']);

			}
		}

		public function getTypes()
		{
			return [
				'private' => ['singular' => 'particuliere', 'adjective' => 'particuliere'],
				'commercial' => ['singular' => 'commercieel', 'adjective' => 'commerciële']
			];
		}

		public function listAction(){
            $this->view->Breadcrumbs()
                ->addCrumb('Financieel')
                ->addCrumb('Componenttemplates');

			$this->view->types = RatesheetController::getTypes();

		}

		public function listviewAction(){

			$types = RatesheetController::getTypes();
			$type_titles = $types[$this->getParam('type')];

            if ($this->getParam('fromviewfile') == false) {
                $this->view->Breadcrumbs()
                    ->addCrumb('Financieel')
                    ->addCrumb(ucfirst($type_titles['adjective']).' component templates');
            }

			$filters = [
				'name' => ['type' => 'input'],
				'hide_inactive' => [
					'renderSeparately' => true,
					'title' => 'Inactieve verbergen',
					'show_all_disabled' => true,
					'custom_options' => [
						1 => ['title' => 'Ja'],
						0 => ['title' => 'Nee']
					],
					'custom_options_only' => true,
					'value' => 1,
					'hideCount' => true,
					'preApplied' => true
				],
				'project' => []
			];

			$this->view->modelListView('Ratesheets', ['type' => $this->getParam('type'), 'url-override' => 'ratesheet/listview'])

				->setTypes(['name', 'calculate', 'apply_order'])

				->addFormat('project', function($value, $row){ return $value ? $row['project_name'] : '-'; })

				->addFormat('calculate', function($value, $item){ return '<a class="button details" href="components/update/ratesheet/' . $item['id'] . '/">Update</a>'; })

				->addFormat('apply_order', function($value, $item){
					return '
					    <a
                            class="xxsmall fa fa-sort"
                            href="ratesheet/apply-order/ratesheet/' . $item['id'] . '/"
					    >
					    </a>
</a>';
				})

				->setOptions(array(
					'item_title' => ucfirst($type_titles['singular']) . (Settings::get('software_type') == 'real estate' ? ' component template' : ' tariefblad'),
					'show_title' => false,
					'paginator' => false,
					'disabled_buttons_test' => function($row, $button){
						if($button == 'edit') return false;

						return $row['is_used'];
					}
				))

				->addButtons(array(
                    'add' => 'Toevoegen',
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen'
				))

				->setFilters($filters)

				->render($this);
		}

		private function groupListByInactive($list){
			$grouped = ['Actief' => [], 'Inactief' => []];

			foreach($list as $ratesheet) {
				$active_key = $ratesheet['inactive'] ? 'Inactief' : 'Actief';
 				$grouped[$active_key][] = $ratesheet;
			}

			return $grouped;
		}

		public function getAction() {
			$ratesheets = new Ratesheet();
			$type = $_POST['Type'] ? $_POST['Type'] : false;
			$new = $this->_getParam('version') == '2' ? true : false;
			$project = $this->_getParam('project') && Settings::get('ratesheet_per_project') ? $this->_getParam('project') : false;

			$ratesheets = $ratesheets->getList($project, $type, $new);
			$this->view->ratesheets = $new ? $ratesheets : $this->groupListByInactive($ratesheets);

			$this->view->type = $type;
			$this->view->new = $new;
			$this->view->selected = $new === false?
				$this->_getParam('Component_template'): $this->_getParam('Tariefblad_indx');

			$this->_helper->layout->disableLayout();
		}

		/**
		 * editRateSheet
		 */
		public function editAction() {
			$ratesheets = new Ratesheet();
			$ratesheets->allowZeroValue = true;
			$ratesheets->allowVariableRates = Settings::get('meters') == 1;
			$ratesheet = array();

			if (!is_numeric($this->_getParam('id'))) {
				$type = $this->_getParam('type');
				$project = $this->_getParam('project');
			}
			else {
				$ratesheet = $ratesheets->getDetails($this->_getParam('id'));

				foreach($ratesheet['fixed'] as $array_key1 => $array_level1)
					foreach($array_level1['general'] as $array_key2 => $array_level2)
						foreach($array_level2['data'] as $value_id => $value){
							unset($ratesheet['fixed'][$array_key1]['general'][$array_key2]['data'][$value_id]);
							$ratesheet['fixed'][$array_key1]['general'][$array_key2]['data'][$value['order'] . '_' . $value_id] = $value;
						}

				$title = $ratesheet['general']['title'];
				$project = $ratesheet['general']['project'];
				$type = $ratesheet['general']['type'];
			}

			if(!isset($ratesheet['fixed']))
				$ratesheet['fixed']['huurcomponenten'] = array('title' => 'Huur');

			if(Settings::get('meters'))
				if(!isset($ratesheet['variable']))
					$ratesheet['variable']['variabel'] = ['title' => 'Variabel'];

			//create form
			$rateform = new Form($this, 'ratesheets');

			if ($this->getRequest()->isPost() && $rateform->isValid($this->getRequest()->getPost())) {
				$data = $rateform->getValues();	
				$data['rates'] = (array) Zend_Json::decode($data['rates']);
				
				$ratesheets->save($data, $this->_getParam('id'), $type, $project);

				$this->disableView();

				// used for javascript redirect
				die($_SERVER['HTTP_REFERER']);
			}

			// populate form
			$rateform->populate($ratesheet);

			//view
			$this->view->ratesheets = $ratesheet;
			$this->view->rateform = $rateform;
			$this->view->page = $type;
			$this->view->id = $this->_getParam('id');

			$typelabel = $this->view->typelabel = $type == 'private' ? 'Particuliere' : 'Commerciele';

			$this->view->Breadcrumbs()
				->addCrumb(Settings::get('software_type') == 'real estate' ? 'Componenttemplates' : 'tariefbladen', 'ratesheet/list/');


			$ratesheet_title = Settings::get('software_type') == 'real estate' ? ' component template' : 'tariefblad';
			$this->view->Breadcrumbs()
				->addCrumb(is_numeric($this->_getParam('id')) ? $typelabel . ' ' . $ratesheet_title . ' \'' . $title . '\' bewerken' : $typelabel . $ratesheet_title . ' toevoegen', 'project/');
		}

		public function printAction() {
			if (!$this->_getParam('id'))
				die();

			$r = new Ratesheet();
			$this->view->ratesheet = $r->getDetails($this->_getParam('id'));

		}

		public function pricesAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Prijslijst');

			$this->view->modelListView('Pricelists', ['url-override' => 'test/test'])

				->setTypes(array('name'))

				->addFormat('commercial_user', function($value){ return ($value == 1)? 'Ja': 'Nee'; })

				->addFormat('technical_contact', function($value){ return ($value == 1)? 'Ja': 'Nee'; })

				->setOptions(array(
					'item_title' => 'Prijslijst',
				))

				->addButtons(array(
					'add' => 'Toevoegen',
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen'
				),
				array(
					'id' => 'id',
					'redirect' => 'true'
				))

				->render($this);			
		}

		/**
		 * Delete ratesheet
		 */
		public function deleteAction() {
			$this->disableView();

			if (is_numeric($this->_getParam('id'))) {
				$ratesheet = new Ratesheet();
				$ratesheet->delete($this->_getParam('id'));
			}

			header('Location: ' . $_SERVER['HTTP_REFERER']);
		}

		public function applyOrderAction() {
			$this->addMinify('js', 'js_support');
			$this->addMinify('css', 'css_support');
			$ratesheetId = $this->_getParam('ratesheet', false);
			$service = new Controllers\Ratesheet\ApplyOrderService();

			if ($this->getRequest()->isPost()) {
				$service->execute(new \Controllers\Ratesheet\ApplyOrderRequest(
					$this->_getParam('componentIds', []),
					$this->_getParam('objectIds', [])

				))->onSuccess(function(){
					$this->redirect('ratesheet/list/');

				})->onFailure(function() use($ratesheetId) {
					$this->redirect("ratesheet/apply-order/ratesheet/$ratesheetId/error/true/");

				});
			}

			if (!$service->ratesheetIdParamIsValid($ratesheetId)) $this->redirect('ratesheet/list/');

			list($projects, $objectgroups, $objectsByObjectgroup) = $service->getObjectsEtc($ratesheetId);
			$this->view->components = $service->getComponentsNameByOrder($ratesheetId);
			$this->view->componentIds = $service->getComponentIdsByOrder($ratesheetId);
			$this->view->projects = $projects;
			$this->view->objectgroups = $objectgroups;
			$this->view->objectsByObjectgroup = $objectsByObjectgroup;
			$this->view->error = $this->_getParam('error', false);
		}
	}
