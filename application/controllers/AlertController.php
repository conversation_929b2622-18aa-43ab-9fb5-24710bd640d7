<?php

class AlertController extends GlobalController
{
    public $roomselector_url = 'https://ravelresidence.studentexperience.nl/';
    public $allowed_versions = ['1.0.7', '1.0.8', '1.0.9', '1.0.10', '1.0.11'];
    //public $roomselector_url = 'http://localhost/roomselector/';

    public $roomselector_url_language;

    public function preDispatch()
    {
        $this->_helper->layout->setLayout('main_mobile');

        $this->view->language = $language = User::getLanguage(false);

        if ($this->getParam('version')) {
            $version = $this->getParam('version');
            $_SESSION['alert-version'] = $version;
        } else {
            $version = $_SESSION['alert-version'];
        }
        $this->view->allowed_version = $version ? in_array($version, $this->allowed_versions, false) : false;

        $o_model = new Objects();

        if ($this->getParam('object')) {
            if ($this->getParam('object') > 0) {
                if ($project = $o_model->getProject($this->getParam('object'))) {
                    if ($project->id === '283') {
                        $this->roomselector_url = 'https://roomselector.studentexperience.nl/';
                    }
                }
            }
        }

        if (!in_array($_SERVER['HTTP_HOST'], ['ravel.omniboxx.nl', 'my.studentexperience.nl'])) {
            $this->roomselector_url = 'https://testroomselector.studentexperience.nl/';
        }

        $this->view->roomselector_url = $this->roomselector_url;
        $this->view->roomselector_url_language = $this->roomselector_url_language = 'language=' . $language;
        header('Access-Control-Allow-Origin', true);

        $this->loadTranslations();

        return parent::preDispatch();
    }

    protected function loadTranslations()
    {
        global $translate;

        $translate->addTranslation(
            [
                'content' => 'media/tmx/alert_app.tmx',
                'locale' => $this->view->language
            ]
        );
    }

    public function indexAction()
    {
        $this->view->list = (new ObjectsAlert())->getList(['status' => 'available']);
    }

    public function listAction()
    {
        $this->view->list = (new ObjectsAlert())->getList(['status' => 'available']);
    }

    public function objectDetailsAction()
    {
        $object_id = $this->_getParam('object');

        if (!($object_id > 0)) {
            $this->_redirect('alert/list');
        }

        $model = new ObjectsAlert();
        $this->view->object_details = end($model->getList(['object_id' => $object_id]));
    }

    public function parseRoomSelectorHeaders($page)
    {
        $page = str_replace('href=\'css/fullcalendar/', 'href=\'' . $this->roomselector_url . 'css/fullcalendar/', $page);
        $page = str_replace('src=\'script/', 'src=\'' . $this->roomselector_url . 'script/', $page);

        return $page;
    }

    public function subscribeAction()
    {
        $reserved = false;

        $this->view->object_id = $object_id = $this->_getParam('object');

        createQueue('object_' . $object_id, function () use ($object_id, &$reserved) {
            $ol_model = new ObjectsAlert();

            if ($ol_model->isAvailable($object_id)) {
                $ol_model->updateStatus($object_id, 'reserved');
                $reserved = true;
            }
        });

        $model = new ObjectsAlert();
        $object_details = end($model->getList(['object_id' => $object_id]));

        if (!$reserved) {
            $this->_redirect('alert/sorry');
        } else {
            $get = '&number=' . $object_details['build'] . '&futuredate=' . $object_details['date'] . '&object=' . $object_details['object_id'];

            $form = file_get_contents($this->roomselector_url . 'includes/form.php?' . $this->roomselector_url_language . $get);
            $form = str_replace('src="script', 'src="' . $this->roomselector_url . 'script', $form);
            $form = str_replace('src="fancybox', 'src="' . $this->roomselector_url . 'fancybox', $form);
            $form = str_replace('href="fancybox', 'href="' . $this->roomselector_url . 'fancybox', $form);
            $form = str_replace('href="css', 'href="' . $this->roomselector_url . 'css', $form);

            // REMOVE JS DATEPICKERS
            foreach (['#datepicker', '#datepicker2'] as $datepicker_id) {
                $datepicker_start_pos = strpos($form, "$( '" . $datepicker_id . "' )");

                $form_after_datepicker = substr($form, $datepicker_start_pos);
                $datepicker_end_pos = strpos($form_after_datepicker, "});");
                $form = substr($form, 0, $datepicker_start_pos) . substr($form_after_datepicker, $datepicker_end_pos + 3);
            }

            // REMOVE JS DATE VALIDATOR
            $date_val_start_pos = strpos($form, 'jQuery.validator.addMethod("dateNL"');

            $form_after_date_val = substr($form, $date_val_start_pos);
            $date_val_end_pos = strpos($form_after_date_val, '");');
            $form = substr($form, 0, $date_val_start_pos) . substr($form_after_date_val, $date_val_end_pos + 3);

            //$form = substr($form, strpos($form, '<div id="dialog-form">'));
            $form = str_replace('action="ideal.php"', 'action="alert/ideal/?object=' . $object_id . '"', $form);
            $form = str_replace('action="signContract.php"', 'action="alert/sign-contract/?object=' . $object_id . '"', $form);

            // TESTING:
            //$form = str_replace('data-validate="required" required', '', $form);
            //$form = str_replace('data-validate="required,email"', '', $form);

            $form_collapsed = '';
            foreach (explode("<br />", nl2br($form)) as $form_exploded_row) {
                if (strpos($form_exploded_row, 'id="datepicker"') !== false) {
                    $form_exploded_row = str_replace('type="text"', 'type="date"', $form_exploded_row);
                }

                $form_collapsed .= $form_exploded_row;
            }

            $form = $form_collapsed;
            $form = str_replace('class="ui-dialog-buttonset" style="margin-top:20px; float:left; clear:both"', 'style="display:none;"', $form);
            $form = str_replace('</form>', '<input type="submit" class="btn btn-positive" style="width: auto; float: right; margin-top: 50px;" data-transition="slide-in" value="Verzend"></form>', $form);
            $form = str_replace('<p>Inschrijven woning <span class="nummer"></span></p>', '', $form);
            $form = str_replace('<div class="explanation">', '<div class=""><p>', $form);
            $form = str_replace('<p>Register for student apartment  <span class="nummer"></span></p>', '', $form);

            $this->view->form = $form;
        }
    }

    public function idealAction()
    {
        $this->view->object_id = $object_id = $this->_getParam('object');

        $ol_model = new ObjectsAlert();

        if ($ol_model->isAvailable($object_id)) {
            return $this->_redirect('alert/sorry');
        }

        $curl_object = curl_init();
        $curl_options = [
            // IMPORTANT -> REMOVE DEBUGGING IDEAL BEFORE PRODUCTION!!
            CURLOPT_URL => $this->roomselector_url . 'ideal.php?debugging_ideal=1&' . $this->roomselector_url_language,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => http_build_query(['object' => $this->_getParam('object')] + $_POST, '', '&'),
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => false
        ];

        curl_setopt_array($curl_object, $curl_options);

        $page = curl_exec($curl_object);
        $page = str_replace("href='index.php'", 'href="alert/object-details/id/' . $object_id . '/?' . $this->roomselector_url_language . '"', $page);
        $page = str_replace('appointments/getAvailable.php', $this->roomselector_url . 'appointments/getAvailable.php', $page);
        $page = str_replace('appointments/selectAppointment.php', $this->roomselector_url . 'appointments/selectAppointment.php', $page);
        $page = str_replace("<button id='nextButton' type='button' disabled=disabled>Volgende</button>", '<button class="btn btn-positive" style="width: auto; float: right;" data-transition="slide-in" id="nextButton" type="button" disabled=disabled>' . ($this->roomselector_url_language == 'en' ? 'Send' : 'Verzend') . '</button>', $page);
        $page = str_replace('href="css', 'href="' . $this->roomselector_url . 'css', $page);
        $page .= '<link rel="stylesheet" href="media/style/alert/ideal.css" type="text/css">';

        $this->view->idealPage = $this->parseRoomSelectorHeaders($page);
    }

    public function signContractAction()
    {
        $this->view->object_id = $object_id = $this->_getParam('object');

        $ol_model = new ObjectsAlert();
        if ($ol_model->isAvailable($object_id)) {
            return $this->_redirect('alert/sorry');
        }

        $status = $ol_model->currentStatus($object_id);

        if ($status === 'reserved') {
            $ol_model->updateStatus($object_id, 'done');

            $curl_object = curl_init();
            $curl_options = [
                CURLOPT_URL => $this->roomselector_url . 'signContract.php?' . $this->roomselector_url_language,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => http_build_query(['object' => $this->_getParam('object')] + $_POST, '', '&'),
                CURLOPT_FOLLOWLOCATION => 1,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_SSL_VERIFYPEER => false
            ];

            curl_setopt_array($curl_object, $curl_options);

            $page = curl_exec($curl_object);
        } else {
            $this->view->disableRatchet = true;

            $uo_id = db()->fetchOne(db()->select()->from(['oa' => 'objects_alert'], ['departing_uo_id'])->where('oa.object_id = ?', $this->_getParam('object'))->order('modified DESC'));

            $curl_object = curl_init();
            $curl_options = [
                CURLOPT_URL => $this->roomselector_url . 'signContractBack.php?' . $this->roomselector_url_language,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => http_build_query(['object' => $this->_getParam('object'), 'uoId' => $uo_id], '',
                    '&'),
                CURLOPT_FOLLOWLOCATION => 1,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_SSL_VERIFYPEER => false
            ];

            curl_setopt_array($curl_object, $curl_options);

            $page = curl_exec($curl_object);
        }

        $page = str_replace('appointments/getAvailable.php', $this->roomselector_url . 'appointments/getAvailable.php', $page);
        $page = str_replace("<button id='nextButton' type='button' disabled=disabled>Volgende</button>", '<button class="btn btn-positive" style="width: auto; float: right;" data-transition="slide-in" id="nextButton" type="button" disabled=disabled>Volgende</button>',  $page);

        $this->view->idealPage = $this->parseRoomSelectorHeaders($page);
    }

    public function sorryAction()
    {
    }

    public function successAction()
    {
    }

    public function notificationTestAction()
    {
        $this->disableView();
    }
}
