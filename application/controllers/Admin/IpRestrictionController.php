<?php

class Admin_IpRestrictionController extends GlobalController
{
    public function listAction()
    {
        $data = (new Ips)->fetchAll(null, 'id DESC');

        $types = [
            'ip' => ['title' => 'Ip adres', 'width' => 'large'],
            'approved' => ['title' => 'Heeft toegang', 'width' => 'small'],
            'request_user' => ['title' => 'Aanvrager', 'width' => 'xlarge truncate'],
            'description' => ['title' => 'Omschrijving', 'width' => 'xxxlarge truncate'],
            'created_at' => ['title' => 'Aangemaakt op.', 'width' => 'large'],
            'updated_at' => ['title' => 'Gewijzigd', 'width' => 'large'],
            'edit' => ['title' => 'Aktie', 'width' => 'medium']
        ];

        $this->view->extraWidth = true;

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters([])
            ->addFormat('approved', 'bool')
            ->addFormat('request_user', 'buildname')
            ->addFormat('edit', function ($value, $row) {
                return "<a href='admin_ip-restriction/edit/ips_id/{$row['id']}'><i class='fa fa-edit'></i></a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Ip toegang');

        $this->view->EditView($this, 'Ips', ['id' => $this->_getParam('ips_id')])
            ->setOptions([
                'redirect_to' => 'referer'
            ])
            ->render($this);
    }
}
