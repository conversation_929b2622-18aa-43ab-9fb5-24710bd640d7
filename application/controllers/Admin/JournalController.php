<?php

use Admin\Journal\Application\Service\ViewJournalListService;

class Admin_JournalController extends GlobalController
{
    public function listAction()
    {
        $types = [
            'created_on' => ['title' => 'Datum', 'width' => 'medium'],
            'recorder' => ['title' => 'Uitvoerder', 'width' => 'xxxxlarge truncate'],
            'message' => ['title' => 'Omschrijving', 'width' => 'xxxxlarge truncate'],
            'count' => ['title' => 'Aantal', 'width' => 'medium'],
            'details' => ['title' => 'Details', 'width' => 'large']
        ];

        $filters = [
            'created_on' => [
                'title' => 'Periode',
                'renderSeparately' => true,
                'type' => 'date_range'
            ],
            'recorder' => [],
            'message' => []
        ];

        $this->view->extraWidth = true;

        $data = (new ViewJournalListService())->execute();

        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters($filters)
            ->addFormat('details', function($value, $row) {
                return "<a href='admin_journal/details/id/{$row['id']}' target='_blank'><i class='fa fa-eye' aria-hidden='true'></i></a>";
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->render($this);
    }

    public function detailsAction()
    {
        $id = $this->getParam('id');

        $journalRecord = db()->select()
            ->from(['jr' => 'journal_record'], ['recorder_id' => 'jr.recorder', 'jr.data', 'jr.message', 'jr.created_on'])
            ->joinLeft(['u' => 'users'], 'u.id = jr.recorder', 'u.rendered_name')
            ->where('jr.id = ?', $id)
            ->query()
            ->fetch();

        $this->view->journalRecord = $journalRecord;

        $this->view->journalRecordLinks = db()->select()
            ->from('journal_record_link')
            ->where('record = ?', $id)
            ->query()
            ->fetchAll();

        $this->view->extraWidth = true;
    }
}
