<?php

class Admin_UserController extends GlobalController
{
    public function listAction()
    {
        $types = [
            'id' => ['width' => 'hidden'],
            'name' => ['title' => 'Achternaam', 'width' => 'xlarge'],
            'firstname' => ['title' => 'Voornaam', 'width' => 'large'],
            'username' => ['title' => 'Gebruikersnaam', 'width' => 'large truncate'],
            'aclname' => ['title' => 'Rechten', 'width' => 'large truncate'],
            'created' => ['title' => 'Aangemaakt', 'width' => 'medium truncate'],
            'action' => ['title' => 'Aktie', 'width' => 'small truncate']
        ];

        $filters = [];

        $data = (new User())->getUsers(false, acl()->getInternalRights());


        $this->view->ListView($data)
            ->setTypes($types)
            ->setFilters([
                'name' => ['type' => 'input','title' => 'Achternaam', 'order_by_value' == 'ASC'],
                'firstname' => ['type' => 'input',  'title' => 'voornaam', 'order_by_value' == 'ASC'],
            ])
            ->addFormat('created', 'relativeDate')
            ->addFormat('action', function($value, $row) {
                return "<a href='admin_user/impersonate-user/target_user_id/{$row['id']}'>
                            <i class='fa fa-eye' aria-hidden='true'></i>
                            Inloggen
                        </a>";
            })
            ->render($this);
    }

    public function impersonateUserAction()
    {
        $this->disableView();

        $targetUserId = $this->getParam('target_user_id');

        (new \Admin\ImpersonateUserService())->execute(new \Admin\ImpersonateUserRequest($targetUserId));

        $this->_redirect('dashboard/index');
    }
}
