<?php

class Admin_ScriptsController extends GlobalController
{
    public function listDepositAction()
    {
    }

    public function listProvisionAction()
    {
    }

    public function listIndexationAction()
    {
    }

    public function documentsVisibilityAction()
    {
    }


    public function listLedgerAction()
    {
    }

    public function listUipAction()
    {
    }

    public function retryTwinfieldDocumentDownloadAction()
    {
        $this->disableView();
        startWorker('retry-twinfield-document-download', 'cronjob', 'low');
        echo 'Het achtergrond proces is gestart.';
    }

    public function objectTypePhotosAction(){
        $this->view->projects = db()->fetchPairs(db()->select()->from('projects', ['id', 'name'])->order('name'));
        $this->view->object_types = db()->fetchPairs(db()->select()->from('object_type', ['id', 'name'])->order('name'));

        if($_POST){

            $photo_ids = array_keys($_POST['photo']);
            $source_object_id = $_POST['source_object'];
            $target_object_type_id = $_POST['target_object_type'];
            $target_project_id = $_POST['target_project'];
            $clear_target_objects = $_POST['clear_target_objects'] === 'on';

            $target_object_id_select = db()->select()
                ->from(['o' => 'objects'], ['id'])
                ->where('o.id != ?', $source_object_id);

            if(is_numeric($target_object_type_id)) {
                $target_object_id_select->where('o.type = ?', $target_object_type_id);
            }

            if(is_numeric($target_project_id)) {
                $target_object_id_select
                    ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
                    ->where('og.project = ?', $target_project_id);
            }

            $target_object_ids = db()->fetchCol($target_object_id_select);

            $photos_model = new Photos();
            $photos_model->noMove = true;

            if($clear_target_objects) {
                $photos_model->delete('type = "object" AND map_to IN (' . implode_for_where_in($target_object_ids) . ')');
            }

            $source_photos_select = db()->select()
                ->from('photos')
                ->where('id IN (' . implode_for_where_in($photo_ids) . ')');

            $source_photos = db()->fetchAll($source_photos_select);

            foreach($source_photos as $source_photo){
                foreach($target_object_ids as $target_object_id){
                    unset($source_photo['id']);
                    $source_photo['map_to'] = $target_object_id;

                    $photos_model
                        ->createRow($source_photo)
                        ->save();
                }

            }

            die('klaar!');
        }
    }

    public function deleteRunsAction()
    {
        if($identifier = $_POST['identifier']){

            $identifier_sql_selector = 'CONCAT(ir.identifier, ".", COALESCE(IF(CHAR_LENGTH(i.identifier) < 4, LPAD(i.identifier,4,"0"), i.identifier), "[xxxx]"))';

            $select = db()->select()
                ->from(['i' => 'invoices'], false)
                ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run',  ['id'])
                ->where($identifier_sql_selector . ' = ?', trim($identifier));

            if($run_id = db()->fetchOne($select)){

                $select = db()->select()
                    ->from(['i' => 'invoices'], ['id', 'identifier' => $identifier_sql_selector])
                    ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run',  false)
                    ->where('i.run = ?', $run_id);

                $this->view->run_id = $run_id;
                $this->view->run_data = db()->fetchPairs($select);
            }

        } elseif($_POST['execute'] && $_POST['run_id'] > 0){
            $ir_lib = new InvoiceRun();
            $ic_lib = new InvoiceCustom();
            $inv_lib = new Investors();
            $run_id = $_POST['run_id'];

            $custom_ids_select = db()->select()
                ->from(['i' => 'invoices'], ['customid'])
                ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run',  false)
                ->where('i.run = ?', $run_id)
                ->where('i.customid > 0');

            $custom_ids = db()->fetchCol($custom_ids_select);

            Journal::getForLinks([
                ['type' => 'invoices_run', 'map_to' => $run_id, 'role' => 'removed_item', ],
            ])->record('Er is een factuurrun verwijderd via de admin tool.', [
                'invoices_run' 		=> $run_id,
            ]);

            $ir_lib->undo($run_id);
            $ir_lib->remove([$run_id]);

            foreach($custom_ids as $custom_id) {
                $inv_lib->resetProvisionInvoice($custom_id);
                $ic_lib->delete($custom_id);
            }

            die('KLAAR!');
        }
    }

    public function creditRunAction()
    {
        
    }
}
