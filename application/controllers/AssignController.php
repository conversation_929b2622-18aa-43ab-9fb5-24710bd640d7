<?

	class AssignController extends GlobalController {

		public function searchAction() {
			$this->_helper->viewRenderer->setNoRender(true);
			$forComplaint = $this->_getParam('forComplaint');

			$params = $this->getAllParams();
			if(!empty($params['value']) && empty($params['q']))
				$this->_setParam('q', $params['value']);

			$db = Zend_Db_Table::getDefaultAdapter();

			if(!isset($forComplaint) || $forComplaint !== 'false') {
				//get all relevant ids
				$h = new Hierarchy($this->_getParam('mapTo'), $this->_getParam('type'));

				//internal contacts
				$select = $db->select()
					->from(array('sc' => 'users'),
					array('*'))
                    ->where('sc.enddate IS NULL')
					->where("sc.type IN('".implode("','", acl()->getInternalRights())."') AND (`username` LIKE '%" . $this->_getParam('q') . "%' OR `firstname` LIKE '%" . $this->_getParam('q') . "%' OR `name` LIKE '%" . $this->_getParam('q') . "%')");

				$contacts = $db->fetchAll($select);

				foreach ($contacts as $contact)
					echo "<li>
							<span class=\"name\">{$contact['firstname']} {$contact['middlename']} {$contact['name']}</span>
							<span class=\"id hidden\">{$contact['id']}</span>
							<span class=\"type\">Intern</span>
							<span class=\"note\">{$contact['comment']}</span>
						</li>";
			}

            if($this->getParam('findEmployeesOnly')) {

                $select = db()->select()
                    ->from(['e' => 'employee'], ['role'])
					->joinLeft(['u' => 'users'], 'u.id = e.user', ['id', 'name' => 'rendered_name'])
					->joinLeft(['c' => 'company'], 'c.id = e.company', ['company' => 'name'])
                    ->where('u.name LIKE "%' . $this->getParam('q') . '%"');

                $contacts = db()->fetchAll($select);

                $contacts = orderArrayByLevenstein(
                	$contacts,
                    $this->getParam('q'),
					'name'
				);

                foreach ($contacts as $contact) {

                    $role_name = Employee::$roleNames[$contact['role']];
                    echo "<li>
							<span class=\"name\">{$contact['name']}</span><br />
							<small><span class=\"type\">{$role_name}</span> bij {$contact['company']}</small>
							<span class=\"id hidden\">{$contact['id']}</span>
						</li>";
                }

                return;
            }

			if((!isset($forComplaint) || $forComplaint !== 'true')) {
				//external contacts
				$select = $db->select()
					->from(array('sc' => 'company'), array('*'))
					->join(array('ct' => 'company_type'), 'ct.id = sc.type', false)
					->joinLeft(array('rt' => 'relation_type'), 'rt.id = sc.relation_type', ['relation_type' => 'name'])
					->joinLeft(array('e' => 'crm_email_address'),
						'e.map_to = sc.id AND e.type = \'company\'',
						array('email' => 'e.address')
					)
					->joinLeft(array('a' => 'address'),
						'a.type_id = sc.id AND a.type = \'company\'',
						array('a.address', 'a.number', 'a.zipcode', 'a.city')
					)

				;

				//selection
				if ($this->_getParam('all')) {

					$parts = '';
					foreach(array('sc.name', 'a.address', 'a.number', 'a.zipcode', 'a.city', 'email', 'sc.description') as $part)
						$parts .= ''.$part.' LIKE "%' . $this->_getParam('q') . '%" OR ';

					$select = $select->where(substr($parts, 0, -4));
				}
				else {
					$select = $select->joinLeft(array('scm' => 'support_contacts_map'),
						'scm.contact = sc.id', array());

					$wheres = array();
					//user
					if (!empty($h->_data['user']) && ($this->_getParam('user') || ($this->_getParam('type') == 'user' && !$this->_getParam('search')))) {
						$q = $h->_data['user'];
						$wheres[] = "(scm.map_to = {$q} AND scm.type = 'user')". $search;
					}
					
					//object
					if (!empty($h->_data['object']) && ($this->_getParam('object') || (($this->_getParam('type') == 'object' || $this->_getParam('type') == 'user') && !$this->_getParam('search')))) {
						$q = $h->_data['object'];
						$wheres[] = "(scm.map_to = {$q} AND scm.type = 'object')". $search;
					}

					//project
					if (!empty($h->_data['project']) && ($this->_getParam('project') || (($this->_getParam('type') == 'project' || $this->_getParam('type') == 'object' || $this->_getParam('type') == 'user') && !$this->_getParam('search')))) {
						$q = $h->_data['project'];
						$wheres[] = "(scm.map_to = {$q} AND scm.type = 'project')". $search;
					}

					if(count($wheres) > 0) {
						$statement = implode(' OR ', $wheres);
						$select = $select->where($statement);
					}
				}

				if(!empty($params['limit']) && intval($params['limit']) > 0)
					$select->limit($params['limit']);

				$contacts = $db->fetchAll($select);

				if(!empty($params['outputJson']) && $params['outputJson'] == 'true') {
					$output = [];

					foreach ($contacts as $contact)
						$output[] = [
							'name' 	=> $contact['name'],
							'id' 	=> $contact['id'],
							'type' 	=> $contact['type'],
							'note' 	=> $contact['note'],
						];

					echo json_encode($output);
					return;
				}

				foreach ($contacts as $contact)
					echo "<li>
							<span class=\"name\">{$contact['name']}</span>
							<span class=\"type\"> - " . ($contact['relation_type'] ?: 'Algemeen') . "</span>
							<span class=\"id hidden\">{$contact['id']}</span>
							<span class=\"type\"> - Extern</span>
							<span class=\"idType hidden\"> extern</span>
							<span class=\"note\">{$contact['note']}</span>
						</li>";
			}
		}
	}
?>
