<?

	class RentalDocumentsPartsController extends GlobalController {



        public function indexAction() {

            $this->_helper->redirector('list', 'rental-documents-parts');
        }


        public function listAction(){

            $rd = new RentalDocumentcheckParts();
            $data  =  $rd->getList();


            $partdescriptions 	= [
                'general' => 'Algemeen altijd aan te leveren',
                'privacy' => 'Privacy toelichting',
                'payroll_undetermined' => 'Loondienst onbepaald',
                'payroll_determined' => 'Loondienst bepaald',
                'business_owner' => 'Ondernemer',
                'freelancer' => 'ZZP',
                'retirement' => 'Pensioen',
                'social_benefits' => 'Uitkering',
                'student' => 'Student',
                'owner_occupied_home_sold' => 'Koopwoning verkocht',
                'owner_occupied_home_unsold' => 'Koopwoning niet verkocht',
                'rental_house' => 'Huurwoning',
                'home_living' => 'Thuiswonend',
            ];


            /**
             * @var $listView Zend_View_Helper_ListView
             */
            $this->view->ListView($data)

                ->setTypes([
                    'star' => ['title' => '', 'width' => 'xxxxsmall'],
                    'name' => ['title' => 'Omschrijving', 'width' => 'xxxlarge'],
                    'systemtype' =>  ['title' => 'Aanleveren bij', 'width' => 'xxlarge'],

                ])

                ->addFormat('star', function($value, $item){
                    if($item['source'] == 'system'){
                        return '<i style="color: #84B83D;" class="fa fa-star" title="Systeem default"></i>';
                    }
                })

                ->addFormat('edit_statusses', function($value, $item){
                    return '<a href="rental-deals_status-types/index/template_id/' . $item['id'] . '/"><i class="fa fa-list-ol"></i> Bewerken</a>';
                })

                ->addFormat('systemtype', function($value, $item) use ( $partdescriptions ){
                    return $partdescriptions[$item['type']];
                })

                ->addFormat('source', function($value, $item) {
                    return $value == 'user' ? 'Gebruiker' : 'Systeem';
                })

                ->setFilters([
                    'name' => ['type' => 'input'],
                    'source' => ['renderSeparately' => true, 'title' => 'Aangemaakt door'],

                ])
                ->setOptions([
                    'item_title' => 'Document onderdeel',
                    'disabled_buttons_test' => function($row, $button){
                        if($button == 'copy'){
                            return false;
                        }

                        if($button == 'edit'){
                            return false;
                        }

                        if($button == 'archive'){
                            if ($row['source'] == 'system') {
                                return true;
                            } else {
                                $rd = new RentalDocumentcheckParts();
                                return $rd->usedInSet($row['id'],$row['type']);
                            }
                        }

                        return $row['finalized'] == 'Ja';
                    }
                ])

                ->addButtons([
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'archive' => 'Archiveren'

                ])

                ->render($this);

        }

        public function editAction()
        {
            $this->view->Breadcrumbs()
                ->addCrumb('Document Parts', 'rental-documents-parts/list/')
                ->addCrumb('Part '.($this->_getParam('id') ? 'bewerken' : 'toevoegen') ,'');


            $this->id = $this->getParam('id');

            $this->view->form = $this->view->EditView($this, 'RentalDocumentcheckParts',
                array('id' => $this->_getParam('id')))
                ->render($this);

        }



	}
