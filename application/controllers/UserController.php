<?


use Controllers\Api\RentalWebsiteLogger;
use Controllers\User\SearchProfile;
use ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\EmployeeInstructionsZendFormGenerateService;
use TwoFactorAuthentication\Application\Service\TwoFactorAuthenticationService;
use TwoFactorAuthentication\Application\Service\EnableTimeOutService;
use TwoFactorAuthentication\Application\Service\ViewSetupService;
use TwoFactorAuthentication\Domain\Model\AuthenticationFailedException;


class
UserController extends GlobalController {

		
		public function preDispatch() {

            $this->view->first = $this->firstLogin = loginManager::data()->info['first'] == '1' ? true : false;


			if (loginManager::data()->rights === 'registrant') {
				$this->loadRegistrantMenuOptions();
			} else {
				$this->loadTenantMenuOptions();
			}
		}
		/**
		 * User
		 * Logged on: User settings
		 * Logged off: Login page
		 */

		public function checkIdentifiersAction() {
			$user = new User();
			$user->checkIdentifiers();
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

		}

		public function findLeadAction() {

 		    $form = new Form($this, 'user/findlead');

            if($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
                $users = new Users();
                if (!empty($_POST))
                    $foundusers =  $users->findLeads($_POST['name'], $_POST['email'], $_POST['username']);
                    $this->view->foundusers = $foundusers;
            }

            $this->view->form = $form;
        }


		public function makeFirstInvoiceAction() {
			$this->disableView();
			$u_lib = new User();
			$uo_model = new Objectusers();
			$i_lib = new Invoice();
			$crModel = new ContractRelationsUo();
			$period = Settings::get('general_company_shortname') != 'debazaar' ? 'monthly' : 'fourweekly';

			$userId = $this->_getParam('id') ;
			$uoID = $this->_getParam('uoid') ;
			$contract_id = $this->_getParam('contract') ;
			$uo_rows = array();
            $uip_values = [];

			if($contract_id > 0){

				if($crList = $crModel->getList(['contract' => $contract_id]))
					foreach($crList as $crRow)
						if($uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $crRow['map_to']))){
							$userId = $uo_row['customer'];
							$uoID = $uo_row['id'];
							$uo_rows[] = $uo_row;
							$object_id = $uo_row['object'];
                            $uip_values = UserInvoicePreferences::getForUsersObjects($uoID) ?: [];
						}					
			}


            if ($userId > 0 && count($uip_values) === 0) {
                $uip_values = UserInvoicePreferences::getForUser($userId) ?: [];
            }

            if ($period === 'fourweekly' && $uip_values['rate'] === '12') {
                $period = 'monthly';
            }

			if($userId > 0 && !($uoID > 0)){
				$uo = $uo_model->getCurrentForUser($userId);
				$uoID = $uo['id'];
			}

			$uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $uoID));
			$currentObjectId = $uo_row ? $uo_row['object']  :$this->_getParam('object');

			if(!$userId && $uo_row)
				$userId = $uo_row['customer'];



            $project = (new Projects())->getProjectDataForUserObjectId($uo_row['id']);
			$object_row = Objects::get($currentObjectId);


			if($contract_id > 0){	

				$schedule_id = false;

				if($currentObjectId > 0){
					$og_schedule_select = db()->select()
						->from(['o' => 'objects'], false)
						->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['schedule'])
						->where('o.id = ?', $currentObjectId);

					$schedule_id = db()->fetchOne($og_schedule_select);
				}

				$contract_row = db()->fetchRow(db()->select()->from('contracts')->where('id = ?', $contract_id));
				$schedule_id = $contract_row['schedule'] > 0 ? $contract_row['schedule'] : $schedule_id;
			}

			$uo = db()->fetchRow(db()->select()->from('users_objects')->where('customer = ?', $userId)->where('object = ?', $currentObjectId)->where('role = ?', 'normal')->order('from DESC'));

			if(!$uo) return;

			$first_normal_run_select = db()->select()
				->from(array('ir' => 'invoices_run'))
				->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
				->where('i.users_objects = ?', $uo['id'])
				->where('ir.type = ?', 'advance')
                ->where('ir.period = ?', $period)
				->order('ir.start ASC');

			$first_normal_run = db()->fetchRow($first_normal_run_select);

			if(!$first_normal_run && Settings::get('general_company_shortname') == 'debazaar'){
				$concept_run_select = db()->select()
					->from(array('ir' => 'invoices_run'))
					->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
					->where('ir.project = ?', $project['id'])
					->where('ir.type = ?', 'advance')
					->where('ir.status = ?', 0)
					->where('ir.period = ?', $period)
					->where('ir.run = ?', 0)
					->order('ir.start ASC');

				$first_normal_run = db()->fetchRow($concept_run_select);
			}

			if(count($uo_rows) == 0)
				$uo_rows = array($uo);

			$needs_second_invoice = false;

			$rows = array();

			$costCenter = (new \Object\GetCostCenterForObjectService())->execute($uo['object']);
			$costCarrier = (new Objects())->getCostCarrierForObjectId($uo['object']);

			foreach($uo_rows as $uo_row_id => $uo){

				$uo_rows[$uo_row_id]['needs_second_invoice'] = false;

				$invoice_lib = new Invoice();

				if($schedule_id > 0)
					$invoice_lib->period_days_using_schedule = $schedule_id;

				$from = strtotime($uo['from']);
				
				if($period == 'fourweekly'){
					$period_from = $i_lib->findFourweeklyPeriodByDate($from);
					$period_till = strtotime('-1 days', strtotime('+4 weeks', strtotime(date('Y', $period_from) . 'W' . date("W", $period_from))));
				} else {
					$period_from = $i_lib->findMonthlyPeriodByDate($from);
					$period_till = mktime(0, 0, 0, date('m', $from) + 1, 0, date('Y', $from));
				}

				$till = $first_normal_run ? strtotime('-1 days', strtotime($first_normal_run['start'])) : $period_till;

				// de begindatum van de normale run is groter dan de einddatum van de eerste nota
				if($till > $period_till){
					$needs_second_invoice = true;
					$uo_rows[$uo_row_id]['needs_second_invoice'] = true;
					$till = $period_till;
				}

				$invoice_lib->calcPeriod(mktime(0, 0, 0, date('m', $period_from), date('d', $period_from), date('Y', $period_from)), $till, $from, $till);
				$period_from_calc = $invoice_lib->period;
				$invoice_lib->override_days = $invoice_lib->period['days'] ?: 0;
				$invoice_lib->makeDummy($uo['customer'], $uo['object'], $from, $till, false);
				$invoice_lib->period['usage_days'] = $period_from_calc['usage_days'];
				$invoice_lib->period['total_days'] = $period_from_calc['total_days'];

                if(Settings::get('general_company_shortname') === 'debazaar'){
                    $object_address = 'UNIT ' .  db()->fetchOne(db()->select()->from('objects', ['build'])->where('id = ?', $uo['object']));
                } else {
                    $object_address = Address::buildname($uo['object'], 'object', false);
                }

				if($contract_id > 0){
					$invoice_lib->addHolidaysOutsidePattern();
					$invoice_lib->addContractDiscounts([
						'for_contract' => $contract_id,
						'objects' => [[
							'obj_id' => $uo['object'],
							'o_address' => $object_address,
							'o_number' => '',
							'build' => ''
						]]
					]);

					$contract_row = db()->fetchRow(db()->select()->from('contracts')->where('id = ?', $contract_id));
				}

				$address_specification = '';
				if(count($uo_rows) > 1)
					$address_specification = ' - ' . $object_address;

				$uo_rows[$uo_row_id]['address_specification'] = $address_specification;

				if($invoice_lib->period['days'] > 0)
					foreach($invoice_lib->getValues() as $object_id => $object)
						foreach($object as $value){

							$newRow = [
								'description' => $value['label'] . $address_specification,
								'component' => $value['component'],
								'price' => $value['value_excl'],
								'taxprice' => $value['value_incl'],
								'taxrate' => tax()->setLevel($value['tax_rate'])->get()->getRate(),
								'ledger' => $value['ledger'],
								'provision' => $value['provision'],
								'investor_payout' => $value['investor_payout']
                            ];

                            if ($costCenter) {
                                $newRow['cost_center'] = $costCenter;
                            }

                            if ($costCarrier) {
                                $newRow['cost_carrier'] = $costCarrier;
                            }

                            $rows[] = $newRow;
						}
			}

                $user = new User();
                $language = 'nl';
                $language = $user->getLanguage($userId, false, false, true);

			    $add_deposit = function(&$rows) use ($uo_rows, $object_row , $costCenter, $costCarrier, $language){
                $depositComponentId = (new \Components\GetOrCreateDepositComponentService(new Components()))->execute();
                $depositComponentRowSelect = db()->select()->from('components')->where('id = ?', $depositComponentId);
				$component_row = db()->fetchRow($depositComponentRowSelect);
                $componentDescription = $component_row['name'];

                if ($language == 'en') {
                    $componentDescription = $component_row['name_en'];
                } elseif ($language == 'fr'){
                    $componentDescription = $component_row['name_fr'];
                }

				foreach($uo_rows as $uo){
					$uo['deposit_amount'] = is_null($uo['deposit_amount']) && $object_row->deposit > 0 ? $object_row->deposit : $uo['deposit_amount'];

					$deposit_array = [
						'description' => $componentDescription . $uo['address_specification'],
						'price' => $uo['deposit_amount']/100,
						'taxprice' => $uo['deposit_amount']/100,
						'taxrate' => 0.0,
						'ledger' => Settings::get('financial_export_depositcode'),
						'component' => $component_row['id'],
					];

                    if ($costCenter) {
                        $deposit_array['cost_center'] = $costCenter;
                    }

                    if ($costCarrier) {
                        $deposit_array['cost_carrier'] = $costCarrier;
                    }

					if($component_row){
						$deposit_array['ledger'] = $component_row['ledger'];
						$deposit_array['taxrate'] = $component_row['tax_rate'];
						$deposit_array['component'] = $component_row['id'];
					}

					if($uo['deposit_amount'] > 0)
						$rows[] = $deposit_array;
				}
			};

			if(Settings::get('software_type') != 'energy' && Settings::get('general_company_shortname') != 'debazaar')
				$add_deposit($rows);

			$add_article = function(&$rows) use ($contract_row, $costCenter, $costCarrier, $project) {
				$article_select = db()->select()
					->from('components')
					->where('is_article = ?', true)
					->where('article_first_nota = ?', true)
                    ->where('project = 0 OR project IS NULL OR project = ?', $project['id'])
					->where('ABS(`article_price`) > ?', 0);

				if($contract_row)
					if($contract_row['type'] == 'periodical')
						$article_select->where('article_first_nota_periodical = ?', true);


				foreach(db()->fetchAll($article_select) as $article){
					$tax = tax()
						->setLevel($article['tax_rate'])->get()
						->setAmount($article['article_price']);

					$rows[] = array(
						'description' => $article['name'],
						'price' => $article['article_price'],
						'taxprice' => $tax->add(),
						'component' => $article['id'],
						'taxrate' => $tax->getRate(),
						'ledger' => $article['ledger'],
                        'cost_center' => $costCenter,
                        'cost_carrier' => $costCarrier,
					);

				}
			};

			if (Settings::get('general_company_shortname') !== 'debazaar') $add_article($rows);

			$period_value = $period === 'monthly' ? date('n', $from) : floor((date('W', $from) + 3) / 4);



            if ($language == 'nl') {

                $title = Settings::get('general_company_shortname') !== 'debazaar' ? '1e Nota ' : 'Factuur ';
                $title .= $invoice_lib->translatePeroid($period, $period_value) . ' ' . date('Y', $from);
                $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

                $description =  Settings::get('invoice_description_first_invoice') ? : 'Hierbij ' . $pronoun . ' de factuur voor huur en (eventuele) borg.' ;
            } else  {

                $title = Settings::get('general_company_shortname') !== 'debazaar' ? 'First invoice ' : 'Invoice ';
                $title .= $invoice_lib->translatePeroidEnglish($period, $period_value) . ' ' . date('Y', $from);
                $description = Settings::get('invoice_description_first_invoice_en') ? : 'This document contains your invoice for rent and deposit.';
            }

			$data = [
				'select' => [
					'type' => 'user',
					'project' => $project['id'],
					'attach' => 'user_' . $userId,
					'object' => $currentObjectId,
					'for_contract' => $contract_id
				],

				'total' => [
					'collection' => false
				],
				'general' => [
					'title' => $title,
					'is_first' => true,
					'description' => $description,
					'advance' => (Settings::get('software_type') == 'energy'),
					'credit' => false,
					'tax_rate' => '0', // globale taxrate voor factuur, ignore!
					'cost_center' => $object_row->cost_center

				],
				'corporation' => [
					'corporation' => $project['corporation']
				],
				'frequence' => [
					'repeat' => 0,
					'period' => 0,
					'number' => 1,
					'endpreset' => 7
				],
				'fiscal' => [
					'period' => $period == 'monthly' ? 'm_' . $period_value : 'f_' . $period_value,
					'year' => date("Y", $period_from)
				],
				'rows' => $rows
			];

			$ic = new InvoiceCustom();
			$p = new Post();
			$zip = false;


			if(count($data['rows']) > 0){
				$data['rows'] = json_encode($data['rows']);

				if(Settings::get('general_company_shortname') == 'debazaar')
					$data['general']['financial_export_invoice_title'] = $data['fiscal']['year'] . str_pad($period_value, 2, '0', STR_PAD_LEFT) . ' - ' . $object_row['build'];

				$invoice_custom_id = $ic->add(false, $data);

				if($this->_getParam('auto-finalize')){

					if($uip_values['bill'] == 'mail'){
						$zip = new ZipArchive();
						$post = $p->add('Handmatige facturen', strftime('%d %B om %H %M'));
						$zip->open($post, ZIPARCHIVE::CREATE);

						$ic->zip = $zip;
						$ic->post_model = $p;
						$ic->post = true;
					}

					$ic->finalize($invoice_custom_id);
				}
			}

			if($zip !== false)
				$zip->close();

			if(Settings::get('general_company_shortname') == 'debazaar'){
				$rows = array();

				$add_deposit($rows);
				$add_article($rows);

				$data['general']['title'] = ($uo['deposit_amount'] > 0 ? 'Waarborgsom' : 'Transactiekosten') . ' Unitnr ' . $object_row['build'];
				$data['general']['description'] = 'Hierbij ontvangt u de factuur voor de ' . ($uo['deposit_amount'] > 0 ? 'waarborgsom en de' : '') . ' transactiekosten';

				$data['general']['financial_export_invoice_title'] = 'WBS - ' . $object_row['build'];

				if(count($rows) > 0){
					$data['rows'] = json_encode($rows);
					$invoice_custom_id = $ic->add(false, $data);
				}
			}						

			$rows = array();

			if($needs_second_invoice === true){
				$period_till_first_invoice = $period_till;
				$initial_from = strtotime('+1 days', $period_till_first_invoice);
				foreach($uo_rows as $uo_row_id => $uo){

					if($uo['needs_second_invoice'] !== true) continue;

					$from = strtotime('+1 days', $period_till_first_invoice);
					$till = strtotime('-1 days', strtotime($first_normal_run['start']));

					// reset libs, just to be sure
					$invoice_lib = new Invoice();

					if($schedule_id > 0)
						$invoice_lib->period_days_using_schedule = $schedule_id;
					
					$ic = new InvoiceCustom();

					$period_increment = function($from) use ($period){
						if($period == 'monthly')
							return strtotime('+1 month', $from);
						else
							return strtotime('+4 weeks', $from);
					};

					$period_till = strtotime('-1 days', $period_increment($from));

					while (strtotime('-1 days', $period_increment($from)) <= $till) {
						$invoice_lib->calcPeriod($from, $period_till, $from, $till);
						$period_from_calc = $invoice_lib->period;
						$invoice_lib->override_days = $invoice_lib->period['days'] ?: 0;
						$invoice_lib->makeDummy($uo['customer'], $uo['object'], $from, $till, false);
						$invoice_lib->period['usage_days'] = $period_from_calc['usage_days'];
						$invoice_lib->period['total_days'] = $period_from_calc['total_days'];

                        if(Settings::get('general_company_shortname') === 'debazaar'){
                            $object_address = 'UNIT ' .  db()->fetchOne(db()->select()->from('objects', ['build'])->where('id = ?', $uo['object']));
                        } else {
                            $object_address = Address::buildname($uo['object'], 'object', false);
                        }

						if($contract_id > 0){
							$invoice_lib->addHolidaysOutsidePattern();
							$invoice_lib->addContractDiscounts([
								'for_contract' => $contract_id,
								'objects' => [[
									'obj_id' => $uo['object'],
									'o_address' => $object_address,
									'o_number' => '',
									'build' => ''
								]]
							]);

							$contract_row = db()->fetchRow(db()->select()->from('contracts')->where('id = ?', $contract_id));
						}

						$address_specification = '';
						if(count($uo_rows) > 1)
							$address_specification = ' - ' . $object_address ;

						$period_value = $period == 'monthly' ? date("n", $from) : floor((date("W", $from) + 3) / 4);
						$period_translate = $invoice_lib->translatePeroid($period, $period_value) . ' ' . date('Y', $from);
						
						if($invoice_lib->period['days'] > 0)
							foreach($invoice_lib->getValues() as $object_id => $object)
								foreach($object as $value){

                                    $newRow = [
										'description' => $value['label'] . $address_specification . ' - ' . $period_translate . '.',
										'price' => $value['value_excl'],
										'taxprice' => $value['value_incl'],
										'component' => $value['component'],
										'taxrate' => tax()->setLevel($value['tax_rate'])->get()->getRate(),
										'ledger' => $value['ledger'],
										'provision' => $value['provision'],
										'investor_payout' => $value['investor_payout']
                                    ];

                                    if ($costCenter) {
                                        $newRow['cost_center'] = $costCenter;
                                    }

                                    if ($costCarrier) {
                                        $newRow['cost_carrier'] = $costCarrier;
                                    }

									$rows[] = $newRow;
								}

						$uo['from'] = $from = $period_increment($from);
						$period_till = strtotime('-1 days', $period_increment($from));
					}
				}

                $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

				$data['general']['title'] = Settings::get('general_company_shortname') != 'debazaar' ? '2e Nota': 'Factuur ' . $invoice_lib->translatePeroid($period, $period_value) . ' ' . date('Y', $from) . ' - Unitnr. ' . $object_row['build'];
				$data['general']['description'] = 'Hierbij ' . $pronoun . ' de factuur voor huur';

				$data['fiscal']['period'] = $period == 'monthly' ? 'm_'. $period_value : 'f_'.$period_value;
				$data['fiscal']['year'] = date("Y", $initial_from);

				if(count($data['rows']) > 0){
					$data['rows'] = json_encode($rows);

					if(Settings::get('general_company_shortname') == 'debazaar')
						$data['general']['financial_export_invoice_title'] = $data['fiscal']['year'] . str_pad($period_value, 2, '0', STR_PAD_LEFT) . ' - ' . $object_row['build'];

					$invoice_custom_id = $ic->add(false, $data);

					if($this->_getParam('auto-finalize')){

						if($uip_values['bill'] == 'mail'){
							$zip = new ZipArchive();
							$post = $p->add('Handmatige facturen', strftime('%d %B om %H %M'));
							$zip->open($post, ZIPARCHIVE::CREATE);

							$ic->zip = $zip;
							$ic->post_model = $p;
							$ic->post = true;
						}

						$ic->finalize($invoice_custom_id);
					}
				}			
			}

            if (Settings::get('general_company_shortname') !== 'debazaar') {
                $uo_lib = new Objectusers();

                if (is_null($uo['deposit_amount']) && $object_row->deposit > 0) {
                    $uo_lib->save(
                        [
                            'deposit' => [
                                'amount' => $object_row['deposit'] / 100,
                            ]
                        ],
                        ['id' => $uo['id']]
                    );
                }
            }
            $this->_redirect('invoice/custom-list');
		}
		public function makeLastInvoiceAction() {
			$this->disableView();

            if (Settings::get('general_company_shortname') === 'debazaar') {
                $this->makeLastInvoiceNewAction();
                return;
            }

			$invoice_lib = new Invoice();
			$u_lib = new User();
			$uo_model = new Objectusers();


			$crModel = new ContractRelationsUo();


			$userId = $this->_getParam('id') ;
			$uoID = $this->_getParam('uoid') ;
			$contract_id = $this->_getParam('contract') ;
            // needs to be based on actual data not this shit
			$invoicePeriodType = Settings::get('general_company_shortname') != 'debazaar' ? 'monthly' : 'fourweekly';
			$uo_rows = array();
            $uip_values = [];

			if($contract_id > 0){

				if($crList = $crModel->getList(['contract' => $contract_id]))
					foreach($crList as $crRow)
						if($crRow['map_to'] > 0)
							if($uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $crRow['map_to']))){

								if(!$invoice_lib->needsLastInvoice(false, false, $uo_row['id']))
									continue;

								$userId = $uo_row['customer'];

								if(!($uoID > 0))
									$uoID = $uo_row['id'];

								$uip_values = UserInvoicePreferences::getForUsersObjects($uoID) ?: [];

								$uo_rows[] = $uo_row;
							}
			}

            if ($userId > 0 && count($uip_values) === 0) {
                $uip_values = UserInvoicePreferences::getForUser($userId) ?: [];
            }

            if ($invoicePeriodType === 'fourweekly' && $uip_values['rate'] === '12') {
                $invoicePeriodType = 'monthly';
            }

			if($userId > 0 && !($uoID > 0)){
				$uo = $uo_model->getCurrentForUser($userId);
				$uoID = $uo['id'];
			} elseif($uoID > 0){
                $uo = $uo_model->getById($uoID);
                $userId = $uo->customer;
			}

			$uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $uoID));
            $depositComponentId = (new \Components\GetOrCreateDepositComponentService(new Components()))->execute();
            $depositComponentRowSelect = db()->select()->from('components')->where('id = ?', $depositComponentId);
            $deposit_component_row = db()->fetchRow($depositComponentRowSelect);
            $deposit_uos = [];

			if(count($uo_rows) == 0)
				$uo_rows = array($uo);

			$rows = array();

			foreach($uo_rows as $uo_row){
				$currentObjectId = $uo_row ? $uo_row['object']  :$this->_getParam('object');

				$schedule_id = false;

				if($currentObjectId > 0){
					$og_schedule_select = db()->select()
						->from(['o' => 'objects'], false)
						->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['schedule'])
						->where('o.id = ?', $currentObjectId);

					$schedule_id = db()->fetchOne($og_schedule_select);
				}

				$contract_row = $contract_id > 0 ? db()->fetchRow(db()->select()->from('contracts')->where('id = ?', $contract_id)) : false;
				$schedule_id = $contract_row['schedule'] > 0 ? $contract_row['schedule'] : $schedule_id;				

				$invoice_lib = new Invoice();

				if($schedule_id > 0)
					$invoice_lib->period_days_using_schedule = $schedule_id;			

                $project = (new Projects())->getProjectDataForUserObjectId($uo_row['id']);
				$object_row = Objects::get($currentObjectId);
			

				$uo = db()->fetchRow(db()->select()->from('users_objects')->where('customer = ?', $userId)->where('object = ?', $currentObjectId)->where('role = ?', 'normal')->order('from DESC'));

				if(!$uo) continue;	

				if(!$uo['till']) continue;

				if(Settings::get('general_company_shortname') == 'debazaar'){
					$last_normal_run_select = db()->select()
						->from(array('ir' => 'invoices_run'))
						->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
						->joinLeft(array('io' => 'invoice_objects'), 'io.invoice = i.id', false)
						->where('io.object = ?', $uo['object'])
						->where('i.for_contract = ?', $contract_id)
						->where('ir.type = ?', 'advance')
						->where('ir.status = ?', 2)
						->order('ir.start DESC');
				} else {
					$last_normal_run_select = db()->select()
						->from(array('ir' => 'invoices_run'))
						->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
						->where('i.users_objects = ?', $uo['id'])
						->where('ir.type = ?', 'advance')
						->where('ir.status = ?', 2)
						->order('ir.start DESC');
				}

				$last_normal_run = db()->fetchRow($last_normal_run_select);

				if(!$last_normal_run) continue;



				$endDateLastInvoiceRun = strtotime($last_normal_run['end']);

				if($invoicePeriodType == 'fourweekly'){
                    $tenantEndDate = strtotime('+1 days', strtotime($uo['till']));
					$periodFrom = $invoice_lib->findFourweeklyPeriodByDate($tenantEndDate);
				} else {
                    $tenantEndDate = strtotime($uo['till']);
                    $periodFrom = $invoice_lib->findMonthlyPeriodByDate($tenantEndDate);
				}		


				$period_increment = function($periodFrom) use ($invoicePeriodType){
					if($invoicePeriodType == 'monthly')
						return strtotime('+1 month', $periodFrom);
					else
						return strtotime('-1 days', strtotime('+4 weeks', strtotime(date('Y', $periodFrom) . 'W' . date("W", $periodFrom))));
				};				

				$period_till = $period_increment($periodFrom);


				//p(date('l dS \o\f F Y h:i:s A', $period_till),'die');

				while ($periodFrom <= $endDateLastInvoiceRun) {
                    $period_till = strtotime('-1 days', $period_till);

                    $periodFromTimestamp = mktime(0, 0, 0, date('m', $periodFrom), date('d', $periodFrom), date('Y', $tenantEndDate));
                    $usageStartDate = max($tenantEndDate, $periodFromTimestamp);

					$invoice_lib->calcPeriod($periodFromTimestamp, $period_till, $usageStartDate, $period_till);
					$period_from_calc = $invoice_lib->period;
					/*
					p('---------');
                    p(date('l dS \o\f F Y h:i:s A', $periodFromTimestamp));
                    p(date('l dS \o\f F Y h:i:s A', $period_till));
                    p(date('l dS \o\f F Y h:i:s A', $usageStartDate));
                    p(date('l dS \o\f F Y h:i:s A', $period_till));
					*/

					$invoice_lib->override_days = $invoice_lib->period['days'];
					$invoice_lib->override_days = $invoice_lib->period['days'];
					$invoice_lib->makeDummy($userId, $currentObjectId, $tenantEndDate, $period_till, false);
					$invoice_lib->period['usage_days'] = $period_from_calc['usage_days'];
					$invoice_lib->period['total_days'] = $period_from_calc['total_days'];

					//p($invoice_lib->period,'die');

					if(Settings::get('general_company_shortname') === 'debazaar'){
                        $object_address = 'UNIT ' .  db()->fetchOne(db()->select()->from('objects', ['build'])->where('id = ?', $uo['object']));
                    } else {
                        $object_address = Address::buildname($uo['object'], 'object', false);
                    }

					if($contract_id > 0){
						$invoice_lib->addHolidaysOutsidePattern();
						$invoice_lib->addContractDiscounts([
							'for_contract' => $contract_id,
							'objects' => [[
								'obj_id' => $uo['object'],
								'o_address' => $object_address,
								'o_number' => '',
								'build' => ''
							]]
						]);
					}

					$address_specification = '';
					if(count($uo_rows) > 1){
						$address_specification = ' - ' . $object_address;
						$values = [$uo['object'] => $invoice_lib->getValues($uo['object'])];
					} else {
						$values = $invoice_lib->getValues();
					}


					if($invoice_lib->period['days'] > 0)
						foreach($values as $object_id => $object)
							foreach($object as $value){
								$period_value = $invoicePeriodType == 'monthly' ? $last_normal_run['periodvalue'] : floor((date("W", $periodFrom) + 3) / 4);
 								$period_translate = $invoice_lib->translatePeroid($invoicePeriodType, $period_value) . ' ' . date('Y', $periodFrom);
 								$rows[] = array(
									'description' => $value['label'] . $address_specification . ' - ' . $period_translate,
									'price' => 0 - $value['value_excl'],
									'taxprice' => 0 - $value['value_incl'],
									'component' => $value['component'],
									'taxrate' => tax()->setLevel($value['tax_rate'])->get()->getRate(),
									'ledger' => $value['ledger'],
									'provision' => $value['provision'],
									'investor_payout' => $value['investor_payout']
								);
							}

					$periodFrom = $period_increment($periodFrom);
					$period_till = $period_increment($period_till);
				}

				$deposit_repayed = false;

				if(Settings::get('general_company_shortname') == 'debazaar'){
					if(
						(($uo['deposit_amount'] - $uo['deposit_repayed']) > 0) &&
						(!in_array($contract_row['type'], ['periodical', 'transient']))
					){
                        $deposit_uos[] = $uo;
                        $deposit_repayed = false;
                    }
				} else {
                    if (($uo['deposit_payed'] - $uo['deposit_repayed']) > 0 && $uo['deposit_payed_to_broker'] != '1' && ($uo['deposit_type'] == 'cash' || !$uo['deposit_type'])) {
                        $deposit_repayed = $uo['deposit_payed'] - $uo['deposit_repayed'];
                        $deposit_array = array(
                            'description' => 'Borg' . $address_specification,
                            'price' => 0 - $deposit_repayed / 100,
                            'taxprice' => 0 - $deposit_repayed / 100,
                            'component' => $deposit_component_row['id'],
                            'taxrate' => 0.0,
                            'ledger' => Settings::get('financial_export_depositcode'),
                        );

                        if ($deposit_component_row) {
                            $deposit_array['ledger'] = $deposit_component_row['ledger'];
                            $deposit_array['taxrate'] = $deposit_component_row['tax_rate'];
                            $deposit_array['component'] = $deposit_component_row['id'];
                        }

                        $rows[] = $deposit_array;
                    }
                }
			}

			if(count($deposit_uos) > 0){
				$deposit_uos_total = array_sum(array_column($deposit_uos, 'deposit_amount')) - array_sum(array_column($deposit_uos, 'deposit_repayed'));

                if(abs($deposit_uos_total) > 0) {
                    $deposit_array = array(
                        'description' => 'Waarborg',
                        'price' => 0 - $deposit_uos_total / 100,
                        'taxprice' => 0 - $deposit_uos_total / 100,
                        'component' => $deposit_component_row['id'],
                        'taxrate' => 0.0,
                        'ledger' => Settings::get('financial_export_depositcode'),
                    );

                    if ($deposit_component_row) {
                        $deposit_array['ledger'] = $deposit_component_row['ledger'];
                        $deposit_array['taxrate'] = $deposit_component_row['tax_rate'];
                        $deposit_array['component'] = $deposit_component_row['id'];
                    }

                    $rows[] = $deposit_array;
                }
			}

			$period_value = $invoicePeriodType == 'monthly' ? date("n", $periodFrom) : floor((date("W", $periodFrom) + 3) / 4);

            $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

            $language = $user->getLanguage($userId, false, false, true);

            $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

            if ($language !== 'en') {
                $description =  Settings::get('invoice_description_last_invoice') ? : 'Hierbij ' . $pronoun . ' de laatste factuur voor verrekening van de huur en (eventuele) borg.' ;
            } else {
                $description = Settings::get('invoice_description_last_invoice_en') ? :  'This document contains your last invoice for rent and deposit.';
            }

			$data = array(
				'select' => array(
					'type' => 'user',
					'project' => $project['id'],
					'attach' => 'user_' . $userId,
					'object' => $currentObjectId,
					'uoid' => $uoID,
					'for_contract' => $contract_id
				),

				'total' => array(
					'collection' => false
				),

				'general' => array(
					'title' => Settings::get('general_company_shortname') != 'debazaar' ? 'Laatste nota' : $period_translate . ' ' . $object_row['build'],
					'is_last' => true,
					'description' => $description,
					'advance' => false,
					'credit' => false,
					'tax_rate' => '0', // globale taxrate voor factuur, ignore!
					
				),

				'corporation' => array(
					'corporation' => $project['corporation']
				),

				'frequence' => array(
					'repeat' => 0,
					'period' => 0,
					'number' => 1,
					'endpreset' => 7
				),

				'fiscal' => array(
					'period' => $invoicePeriodType == 'monthly' ? 'm_'. $period_value : 'f_'.$period_value,
					'year' => date("Y", $periodFrom)
				),

				'rows' => $rows
			);

			$ic = new InvoiceCustom();

			if(count($data['rows']) > 0){
				$data['rows'] = json_encode($data['rows']);
				$invoice_custom_id = $ic->add(false, $data);
			}

            $uo_lib = new Objectusers();
			$cr_uo_model = new ContractRelationsUo();

			if($deposit_repayed !== false){
				foreach($uo_rows as $uo_row){
					$uo = $uo_lib->getById($uo_row['id']);
					$uo->deposit_repayed = new StringFormat($deposit_repayed/100, 'money_db');
					$uo->save();
				}
			}

			foreach($deposit_uos as $deposit_uo){
                $uo = $uo_lib->getById($deposit_uo['id']);
                $uo->deposit_repayed = $uo->deposit_amount;
                $uo->save();

                $cruo_select = db()->select()
					->from(['cr_uo' => 'contract_relations_uo'], ['id'])
					->joinLeft(['cr' => 'contract_relations'], 'cr.fields = cr_uo.id', false)
					->where('cr.type = "users_objects" AND cr.map_to = ?', $uo->id)
					;

                if($cruo_id = db()->fetchOne($cruo_select)){
                    $cr_uo_model
						->getById($cruo_id)
						->setFromArray([
                            'deposit_amount' => $uo->deposit_amount,
							'deposit_repayed' => $uo->deposit_repayed
						])
						->save();
				}
			}

			$this->_redirect('invoice/custom-list');
		}

    	public function makeLastInvoiceNewAction() {
        $this->disableView();
        $invoice_lib = new Invoice();
        $u_lib = new User();
        $uo_model = new Objectusers();


        $crModel = new ContractRelationsUo();


        $userId = $this->_getParam('id') ;
        $uoID = $this->_getParam('uoid') ;
        $contract_id = $this->_getParam('contract') ;
        $invoicePeriodType = Settings::get('general_company_shortname') != 'debazaar' ? 'monthly' : 'fourweekly';
        $uo_rows = array();
        $uip_values = [];

        if($contract_id > 0){

            if($crList = $crModel->getList(['contract' => $contract_id]))
                foreach($crList as $crRow)
                    if($crRow['map_to'] > 0)
                        if($uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $crRow['map_to']))){

                            if(!$invoice_lib->needsLastInvoice(false, false, $uo_row['id']))
                                continue;

                            $userId = $uo_row['customer'];

                            if(!($uoID > 0))
                                $uoID = $uo_row['id'];

                            $uip_values = UserInvoicePreferences::getForUsersObjects($uoID) ?: [];

                            $uo_rows[] = $uo_row;
                        }
        }

        if ($userId > 0 && count($uip_values) === 0) {
            $uip_values = UserInvoicePreferences::getForUser($userId) ?: [];
        }

        if ($invoicePeriodType === 'fourweekly' && $uip_values['rate'] === '12') {
            $invoicePeriodType = 'monthly';
        }

        if($userId > 0 && !($uoID > 0)){
            $uo = $uo_model->getCurrentForUser($userId);
            $uoID = $uo['id'];
        } elseif($uoID > 0){
            $uo = $uo_model->getById($uoID);
            $userId = $uo->customer;
        }

        $uo_row = db()->fetchRow(db()->select()->from('users_objects')->where('id = ?', $uoID));
        $depositComponentId = (new \Components\GetOrCreateDepositComponentService(new Components()))->execute();
        $depositComponentRowSelect = db()->select()->from('components')->where('id = ?', $depositComponentId);
        $deposit_component_row = db()->fetchRow($depositComponentRowSelect);
            $deposit_uos = [];

        if(count($uo_rows) == 0)
            $uo_rows = array($uo);

        $rows = array();

        foreach($uo_rows as $uo_row){
            $currentObjectId = $uo_row ? $uo_row['object']  :$this->_getParam('object');

            $schedule_id = false;

            if($currentObjectId > 0){
                $og_schedule_select = db()->select()
                    ->from(['o' => 'objects'], false)
                    ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['schedule'])
                    ->where('o.id = ?', $currentObjectId);

                $schedule_id = db()->fetchOne($og_schedule_select);
            }

            $contract_row = $contract_id > 0 ? db()->fetchRow(db()->select()->from('contracts')->where('id = ?', $contract_id)) : false;
            $schedule_id = $contract_row['schedule'] > 0 ? $contract_row['schedule'] : $schedule_id;

            $invoice_lib = new Invoice();

            if($schedule_id > 0)
                $invoice_lib->period_days_using_schedule = $schedule_id;

            $project = (new Projects())->getProjectDataForUserObjectId($uo_row['id']);
            $object_row = Objects::get($currentObjectId);


            $uo = db()->fetchRow(db()->select()->from('users_objects')->where('customer = ?', $userId)->where('object = ?', $currentObjectId)->where('role = ?', 'normal')->order('from DESC'));

            if(!$uo) continue;

            if(!$uo['till']) continue;

            if(Settings::get('general_company_shortname') == 'debazaar'){
                $last_normal_run_select = db()->select()
                    ->from(array('ir' => 'invoices_run'), ['*','end_date_last_run_invoice' => 'end'])
                    ->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
                    ->joinLeft(array('io' => 'invoice_objects'), 'io.invoice = i.id', false)
                    ->where('io.object = ?', $uo['object'])
                    ->where('i.for_contract = ?', $contract_id)
                    ->where('ir.type = ?', 'advance')
                    ->where('ir.status = ?', 2)
                    ->order('ir.start DESC');
            } else {
                $last_normal_run_select = db()->select()
                    ->from(array('ir' => 'invoices_run'))
                    ->joinLeft(array('i' => 'invoices'), 'i.run = ir.id', false)
                    ->joinLeft(array('irw' => 'invoice_rows'), 'i.id = irw.invoice', ['end_date_last_run_invoice'=> 'MAX(applied_end_day)'])
                    ->where('i.users_objects = ?', $uo['id'])
                    ->where('ir.type = ?', 'advance')
                    ->where('ir.status = ?', 2)
                    ->order('ir.start DESC');
            }

            $last_normal_run = db()->fetchRow($last_normal_run_select);
            
            if(!$last_normal_run) continue;

            $costCenter = (new \Object\GetCostCenterForObjectService())->execute($uo['object']);
            $costCarrier = (new Objects())->getCostCarrierForObjectId($uo['object']);

            $endDateLastInvoiceRun = strtotime($last_normal_run['end_date_last_run_invoice']);
            $tenantLastTillDate = strtotime($uo['till']);
            $tenantEndDate = strtotime('+1 days', strtotime($uo['till']));

            if($invoicePeriodType == 'fourweekly'){
                $periodFrom = $invoice_lib->findFourweeklyPeriodByDate($tenantEndDate);
            } else {
                $periodFrom = $invoice_lib->findMonthlyPeriodByDate($tenantEndDate);
            }

            $period_increment = function($periodFrom) use ($invoicePeriodType){
                if($invoicePeriodType == 'monthly')
                    return strtotime('+1 month', $periodFrom);
                else
                    return strtotime('+4 weeks', strtotime(date('Y', $periodFrom) . 'W' . date("W", $periodFrom)));
            };

            $period_till = $period_increment($periodFrom);


                        //p($endDateLastInvoiceRun);
                       // p($tenantEndDate);
                        //p($period_till);

                        //p($periodFrom);

                      //  die('whatisthis');


            //p(date('l dS \o\f F Y h:i:s A', $period_till),'die');
            // only calculate the amount to return based on invoiced amount // redirect to user if nothing is to invoice last
            if ($endDateLastInvoiceRun > $tenantLastTillDate) {
                while ($periodFrom <= $endDateLastInvoiceRun) {

                    $period_till = strtotime('-1 days', $period_till);
                    $periodFromTimestamp = mktime(0, 0, 0, date('m', $periodFrom), date('d', $periodFrom), date('Y', $periodFrom));
                    $usageStartDate = max($tenantEndDate, $periodFromTimestamp);
                    $invoice_lib->calcPeriod($periodFromTimestamp, $period_till, $usageStartDate, $period_till);
                    //p($invoice_lib);
                    $period_from_calc = $invoice_lib->period;
                    /*
                    p('---------');
                    p(date('l dS \o\f F Y h:i:s A', $periodFromTimestamp));
                    p(date('l dS \o\f F Y h:i:s A', $period_till));
                    p(date('l dS \o\f F Y h:i:s A', $usageStartDate));
                    p(date('l dS \o\f F Y h:i:s A', $period_till));
                    */

                    $invoice_lib->override_days = $invoice_lib->period['days'];
                    $invoice_lib->override_days = $invoice_lib->period['days'];
                    $invoice_lib->makeDummy($userId, $currentObjectId, $tenantEndDate, $period_till, false);

                    $invoice_lib->period['usage_days'] = $period_from_calc['usage_days'];
                    $invoice_lib->period['total_days'] = $period_from_calc['total_days'];


                    if(Settings::get('general_company_shortname') === 'debazaar'){
                        $object_address = 'UNIT ' .  db()->fetchOne(db()->select()->from('objects', ['build'])->where('id = ?', $uo['object']));
                    } else {
                        $object_address = Address::buildname($uo['object'], 'object', false);
                    }

                    if($contract_id > 0){
                        $invoice_lib->addHolidaysOutsidePattern();
                        $invoice_lib->addContractDiscounts([
                            'for_contract' => $contract_id,
                            'objects' => [[
                                'obj_id' => $uo['object'],
                                'o_address' => $object_address,
                                'o_number' => '',
                                'build' => ''
                            ]]
                        ]);
                    }

                    $address_specification = '';
                    if(count($uo_rows) > 1){
                        $address_specification = ' - ' . $object_address;
                        $values = [$uo['object'] => $invoice_lib->getValues($uo['object'])];
                    } else {
                        $values = $invoice_lib->getValues();
                    }

                    //p($invoice_lib->period['days'],'die');
                    if($invoice_lib->period['days'] > 0)
                        foreach($values as $object_id => $object)
                            foreach($object as $value){
                                $period_value = $invoicePeriodType == 'monthly' ? date('m', $periodFromTimestamp) : floor((date("W", $periodFrom) + 3) / 4);
                                $period_translate = $invoice_lib->translatePeroid($invoicePeriodType, $period_value) . ' ' . date('Y', $periodFrom);
                                $newRow = [
                                    'description' => $value['label'] . $address_specification . ' - ' . $period_translate,
                                    'price' => 0 - $value['value_excl']   ,
                                    'taxprice' => 0 - $value['value_incl'] ,
                                    'component' => $value['component'],
                                    'taxrate' => tax()->setLevel($value['tax_rate'])->get()->getRate(),
                                    'ledger' => $value['ledger'],
                                    'provision' => $value['provision'],
                                    'investor_payout' => $value['investor_payout']
                                ];

                                if ($costCenter) {
                                    $newRow['cost_center'] = $costCenter;
                                }

                                if ($costCarrier) {
                                    $newRow['cost_carrier'] = $costCarrier;
                                }

                                $rows[] = $newRow;
                            }

                    $periodFrom = $period_increment($periodFrom);
                    $period_till = $period_increment($period_till);
                }
            }

            $deposit_repayed = false;

            if(Settings::get('general_company_shortname') == 'debazaar'){
                if(
                    (($uo['deposit_amount'] - $uo['deposit_repayed']) > 0) &&
                    (!in_array($contract_row['type'], ['periodical', 'transient']))
                ){
                    $deposit_uos[] = $uo;
                    $deposit_repayed = false;
                }
            } else {

                $depositIsNotRepayed = ($uo['deposit_payed'] - $uo['deposit_repayed']) > 0;
                $depositIsNotPayedToBroker = $uo['deposit_payed_to_broker'] != '1';
                $depositTypeShouldBeRepayed = $uo['deposit_type'] == 'cash_deposit' || !$uo['deposit_type'];

                if ($depositIsNotRepayed && $depositIsNotPayedToBroker && $depositTypeShouldBeRepayed) {
                    $deposit_repayed = $uo['deposit_payed'] - $uo['deposit_repayed'];
                    $deposit_array = array(
                        'description' => 'Borg' . $address_specification,
                        'price' => 0 - $deposit_repayed / 100,
                        'taxprice' => 0 - $deposit_repayed / 100,
                        'component' => $deposit_component_row['id'],
                        'taxrate' => 0.0,
                        'ledger' => Settings::get('financial_export_depositcode'),
                    );

                    if ($deposit_component_row) {
                        $deposit_array['ledger'] = $deposit_component_row['ledger'];
                        $deposit_array['taxrate'] = $deposit_component_row['tax_rate'];
                        $deposit_array['component'] = $deposit_component_row['id'];
                    }

                    if ($costCenter) {
                        $deposit_array['cost_center'] = $costCenter;
                    }

                    if ($costCarrier) {
                        $deposit_array['cost_carrier'] = $costCarrier;
                    }

                    $rows[] = $deposit_array;
                }
            }
        }

        if(count($deposit_uos) > 0){
            $deposit_uos_total = array_sum(array_column($deposit_uos, 'deposit_amount')) - array_sum(array_column($deposit_uos, 'deposit_repayed'));

            if(abs($deposit_uos_total) > 0) {
                $deposit_array = array(
                    'description' => 'Waarborg',
                    'price' => 0 - $deposit_uos_total / 100,
                    'taxprice' => 0 - $deposit_uos_total / 100,
                    'component' => $deposit_component_row['id'],
                    'taxrate' => 0.0,
                    'ledger' => Settings::get('financial_export_depositcode'),
                );

                if ($deposit_component_row) {
                    $deposit_array['ledger'] = $deposit_component_row['ledger'];
                    $deposit_array['taxrate'] = $deposit_component_row['tax_rate'];
                    $deposit_array['component'] = $deposit_component_row['id'];
                }

                if (isset($costCenter)) {
                    $deposit_array['cost_center'] = $costCenter;
                }

                if (isset($costCarrier)) {
                    $deposit_array['cost_carrier'] = $costCarrier;
                }

                $rows[] = $deposit_array;
            }
        }

        $period_value = $invoicePeriodType == 'monthly' ? date("n", $periodFrom) : floor((date("W", $periodFrom) + 3) / 4);

        $pronoun = Settings::get('modules_tenantLogin_firstnamebasis') ? 'ontvang je' : 'ontvangt u';

        $data = array(
            'select' => array(
                'type' => 'user',
                'project' => $project['id'],
                'attach' => 'user_' . $userId,
                'object' => $currentObjectId,
                'uoid' => $uoID,
                'for_contract' => $contract_id
            ),

            'total' => array(
                'collection' => false
            ),

            'general' => array(
                'title' => Settings::get('general_company_shortname') != 'debazaar' ? 'Laatste nota' : $period_translate . ' ' . $object_row['build'],
                'is_last' => true,
                'description' => Settings::get('general_company_shortname') != 'debazaar' ? 'Hierbij ' . $pronoun . ' de laatste factuur voor verrekening van de huur en (eventuele) borg' : '',
                'advance' => false,
                'credit' => false,
                'tax_rate' => '0', // globale taxrate voor factuur, ignore!,
                'cost_center' => $costCenter

            ),

            'corporation' => array(
                'corporation' => $project['corporation']
            ),

            'frequence' => array(
                'repeat' => 0,
                'period' => 0,
                'number' => 1,
                'endpreset' => 7
            ),

            'fiscal' => array(
                'period' => $invoicePeriodType == 'monthly' ? 'm_'. $period_value : 'f_'.$period_value,
                'year' => date("Y", $periodFrom)
            ),

            'rows' => $rows
        );



        $ic = new InvoiceCustom();

        if(count($data['rows']) > 0){
            $data['rows'] = json_encode($data['rows']);
            $invoice_custom_id = $ic->add(false, $data);
        } else {
            $this->view->referrer = $_SERVER['HTTP_REFERER'];
            $this->render('error-final-nota-page');
            return;

        }
              $uo_lib = new Objectusers();
            $cr_uo_model = new ContractRelationsUo();

            if($deposit_repayed !== false){
                foreach($uo_rows as $uo_row){
                    $uo = $uo_lib->getById($uo_row['id']);
                    $uo->deposit_repayed = new StringFormat($deposit_repayed/100, 'money_db');
                    $uo->save();
                }
            }

            foreach($deposit_uos as $deposit_uo){
                $uo = $uo_lib->getById($deposit_uo['id']);
                $uo->deposit_repayed = $uo->deposit_amount;
                $uo->save();

                $cruo_select = db()->select()
                    ->from(['cr_uo' => 'contract_relations_uo'], ['id'])
                    ->joinLeft(['cr' => 'contract_relations'], 'cr.fields = cr_uo.id', false)
                    ->where('cr.type = "users_objects" AND cr.map_to = ?', $uo->id)
                ;

                if($cruo_id = db()->fetchOne($cruo_select)){
                    $cr_uo_model
                        ->getById($cruo_id)
                        ->setFromArray([
                            'deposit_amount' => $uo->deposit_amount,
                            'deposit_repayed' => $uo->deposit_repayed
                        ])
                        ->save();
                }
            }


        $this->_redirect('invoice/custom-list');
    }

		public function updateBicCodesAction(){
			$this->view->Breadcrumbs()
				->addCrumb('BIC codes automatisch bijwerken');

			$this->view->execute = $execute = $this->_getParam('execute') ? true : false;

			$u_model = new Users();
			$tcp_model = new TransactionsCollectionPayments();

			$results = array();

			$add_to_results = function($type, $result_code, $user_name, $user_iban, $user_bic, $new_bic = '') use (&$results){
				$results[] = array('type' => $type, 'code' => $result_code, 'user' => $user_name, 'iban' => $user_iban, 'old_bic' => $user_bic, 'new_bic' => $new_bic);
			};

			$process_result = function($type, $user_name, $user_iban, $user_bic) use ($add_to_results){

				$iban = new Iban_Parser($user_iban);
				$bank_from_iban = $iban->Bank();

				$banks = json_decode(file_get_contents('library/Iban/bic_codes.json'), true);

				if(!isset($banks[$bank_from_iban])){
					$add_to_results($type, 'not_found', $user_name, $user_iban, $user_bic);
					return false;
				}

				$bic = $banks[$bank_from_iban]['BIC'];

				if($user_bic == $bic){
					$add_to_results($type, 'not_modified', $user_name, $user_iban, $user_bic, $bic);
					return false;
				};

				$add_to_results($type, $user_bic == '' || is_null($user_bic) ? 'new' : 'update', $user_name, $user_iban, $user_bic, $bic);

				return $bic;
			};

			foreach($u_model->fetchAll($u_model->select()->where('iban IS NOT NULL')->where('iban != ?', '')) as $user){

				$bic = $process_result('user', User::buildname(false, $user->id), $user->iban, $user->bic);

				if($bic === false) continue;

				$user->bic = $bic;

				if($execute === true)
					$user->save();
			}

			foreach($tcp_model->fetchAll($tcp_model->select()->where('iban IS NOT NULL')->where('iban != ?', '')) as $tpc_row){

				$bic = $process_result('collection', $tpc_row->name, $tpc_row->iban, $tpc_row->bic);

				if($bic === false) continue;

				$tpc_row->bic = $bic;

				if($execute === true)
					$tpc_row->save();
			}

			$this->view->list = $this->view->ListView($results, array())
				->setTypes(array(
					'type' => array('title' => 'Opslag', 'width' => 'medium'),
					'user' => array('title' => 'Gebruiker', 'width' => 'medium truncate'),
					'iban' => array('title' => 'IBAN', 'width' => 'large'),
					'old_bic' => array('title' => 'Huidige bic', 'width' => 'medium'),
					'new_bic' => array('title' => 'Nieuwe bic', 'width' => 'medium'),
					'code' => array('title' => 'Actie', 'width' => 'medium')
				))

				->addFormat('type', function($value){
					if($value == 'user') return 'Gebruiker instelling';
					if($value == 'collection') return 'Incasso opdracht';
				})

				->addFormat(array('new_bic', 'old_bic'), function($value){ return $value == '' ? '-' : $value; })

				->addFormat('iban', 'iban')

				->addFormat('code', function($result){
					$codes = array(
						'not_found' => 'Niet gevonden',
						'not_modified' => 'Niet aangepassen',
						'new' => 'Nieuw',
						'update' => 'Bijwerken',
					);
					return $codes[$result];
				})

				->setOptions(array(
					'paginator' => false,
					'show_title' => false,
					'render_to_controller' => false
				))

				->render($this);


		}

		public function updateNumberAction(){
			$this->disableView();

			if($user = $this->_getParam('id')){
				$u = new User();
				$u->getNumber($user, true);
			}

			header('Location: ' . $_SERVER['HTTP_REFERER']);
		}

		public function checkDoubleNumbersAction(){
			$this->_helper->viewRenderer->setNoRender(true);
			$this->view->extraWidth = true;

			$u = new User();
			$t = new Transactions();
			
			$select = db()->select()

				->from(array('u' => 'users'), array('count' => 'COUNT(u.`number`)', 'number'))
				->joinLeft(array('uo' => 'users_objects'), 'uo.customer = u.id AND uo.role = "normal"', false)
				->joinLeft(array('o' => 'objects'), 'o.id = uo.object', false)
				->joinLeft(array('og' => 'objectgroup'), 'og.id = o.objectgroup', false)
				->joinLeft(array('p' => 'projects'), 'p.id = og.project', false)
				->group('u.number')
				->where('u.number != ?', '')
				->where('p.exploitation = ? || p.id IS NULL', true)
				->where('p.test_project = ? || p.test_project IS NULL', false)
				->where('u.type = ?', 'tenant')
				->having('count > 1')
			;


			echo '
			<style type="text/css">
			.content ul, .content li {
				list-style: none;
			}

			.content h2 {
				margin-top: 100px;
			}

			.content .gray {
				color: lightgray;
			}

			.content ul li span, .content ul li a {
				display: inline-block;
				width: 240px;
			}

			.content ul li .address {
				width: 400px;
			}

			.content ul li a {
				width: 150px;
			}

			.content ul li a[href]:hover {
				text-decoration: underline;
			}
			</style>
			';

			foreach(db()->fetchAll($select) as $row){
				$select = db()->select()
					->from(array('u' => 'users'), array('id', 'name', 'number'))
					->joinLeft(array('uo' => 'users_objects'), 'uo.customer = u.id', array('from', 'till', 'object'))
					->joinLeft(array('o' => 'objects'), 'o.id = uo.object', array('build'))
					->where('number = ?', $row['number'])
					->group('u.id');

				if(count(db()->fetchAll($select)) <= 1)
					continue;

				echo '<h2>Klantnummer: ' . $row['number'] . '</h2>';
				echo '<ul>';

				echo '<li><i><b><a>Naam</a><span class="address">Object koppeling</span><span>Facturen ontvangen</span><a>Verwijderen</a><a>Klantnummer bijwerken</a></b></i></li>';

				foreach(db()->fetchAll($select) as $user){

					$address = $user['object'] ? Address::get($user['object'], 'object') : false;
					$transaction = $t->fetchRow($t->select()->where('user = ?', $user['id']));

					echo '<li>';
					echo '<a href="support/show/id/' . $user['id'] . '/type/user" target="_blank">' . User::buildname(false, $user['id']) . '</a>';

					if($address){
 						echo '<a href="support/show/id/' . $user['object'] . '/type/object" target="_blank" class="address">' . $address->address . ' ' . $address->number . ' ' . $user['build'] . ' - <small>' . $user['from'] . ($user['till'] ? ' t/m ' . $user['till'] : ' (geen einddatum)') . '</small></a>';
					} else {
 						echo '<span class="address gray">Nee</span>';
					}

 					echo '<span' . (!$transaction ? ' class="gray"' : '') . '>' . ($transaction ? 'Ja' : 'Nee') . '</span>';
 					echo !$transaction ? '<a href="user/admin-delete/id/' . $user['id'] . '">Verwijderen</a>' : '<a>-</a>';
 					echo $user['object'] ? '<a href="user/update-number/id/' . $user['id'] . '">Bijwerken</a>' : '-';
					echo '</li>';

				}
				echo '</ul>';
			}
		}

		public function checkDigitalSignedAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$uo = new Objectusers();

			$db = db();
			$select = $db->select()
				->from(array('uo' => 'users_objects'), array('uoid' => 'id'))
				->joinLeft(array('sd' => 'support_documents'), 'sd.id = uo.contract_id', array('id', 'user', 'map_to'));

			foreach ($db->fetchAll($select) as $contract) {
				$contract['digital'] = !$contract['id'] || ($contract['user'] != $contract['map_to']) ? false : true;
				if ($contract['digital']) {
					$row = $uo->fetchRow($uo->select()->where('id = ?', $contract['uoid']));
					$row->digital = true;
					//$row->save();
				}

			}

		}


		public function getObjectsAction(){
			$this->disableView();

			$user = $this->_getParam('user');
			$investor = $this->_getParam('investor');

			$object_is_inactive = function($object_id){
				return db()->fetchOne(db()->select()->from('objects', ['inactive'])->where('id = ?', $object_id)) == '1';
			};

            if ($user > 0) {
                $uo = new Objectusers();

                $objects = [];
                foreach ($uo->getForUser($this->_getParam('user')) as $object) {

                    $isHistoric = false;

                    if ($object['till']) {
                        if (strtotime($object['till']) < mktime(0, 0, 0)) {
                            $isHistoric = true;
                        }
                    }

                    $sortingKey = (0 - strtotime($object['from'])) . '.' . uniqid();

                    $objects[$sortingKey] = [
                        'objectId' => $object['object'],
                        'address' => Address::buildname($object['object'], 'object'),
                        'inactive' => $object_is_inactive($object['object']),
                        'historic' => $isHistoric,
                    ];
                }
            } elseif ($investor > 0){

				$i_model = new Investors();

                foreach ($i_model->getObjectsForInvestor($investor) as $object) {
                    $objects[$object] = [
                        'objectId' => $object,
                        'address' => Address::buildname($object, 'object'),
                        'inactive' => $object_is_inactive($object)
                    ];
                }

			}

            krsort($objects);

			echo json_encode(array_values($objects));
		}

		public function ibanToBicAction(){
			$this->disableView();

			$iban = $this->_getParam('iban') ? urldecode($this->_getParam('iban')) : false;

			$iban = strtoupper(str_replace(' ', '', $iban));

			$parser = new Iban_Parser($iban);
			if(!$parser->Verify($iban))
				die(json_encode(false));

			$bank_id = $parser->Bank();

			$bic = new BIC();
			$bic_code = $bic
				->findByBankid($bank_id)
				->getBIC();

			echo json_encode($bic_code !== false ? $bic_code : 'NO_BIC');
		}

		/**
		 * Shows a list of E-mails for the current user
		 * @return null
		 */
		public function emailAction(){

		}


		public function documentsOverviewAction()
		{
			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Documenten');

			$u_model = new Users();
			$data = $u_model->getObligateddocumentsList($this->getAllUrlParams(), false);
			
 			$this->view->ListView($data)

				->setTypes(array(
					'project_name' => array('title' => 'Project', 'width' => 'large truncate'),
					'og_type' => array('title' => 'Type', 'width' => 'xsmall'),
					'firstname' => array('title' => 'Voornaam', 'width' => 'medium truncate', 'order_using_column' => 'firstname'),				
					'name' => array('title' => 'Achternaam', 'width' => 'medium truncate', 'order_using_column' => 'name'),
					'address' => array('title' => 'Adres', 'width' => 'large truncate'),
					'number' => array('title' => 'Nr', 'width' => 'xxxsmall'),
					'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') : (Settings::get('software_type') == 'energy' ? "Bouwnr" : '#spcf.'), 'width' => 'large'),
 					'bewijs' => array('title' => 'BW', 'width' => 'xxxsmall'),
 					'pasfoto' => array('title' => 'PH', 'width' => 'xxxsmall'),
 					'oplever' => array('title' => 'OD', 'width' => 'xxxsmall'),
 					'warmte' => array('title' => 'WM', 'width' => 'xxxsmall'),
 					'contract' => array('title' => 'Contract getekend', 'width' => 'medium'),


				))

				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat('og_type', function($value){ return ucfirst($value); })		 
				->addFormat('address', 'object_support_link')
				->addFormat('name', function($value, $item){
					return $item['userid'] > 0 ? '<a href="support/show/id/' . $item['userid'] . '/type/user" target="_blank">' . ($value ? $value : '-') . '</a>' : '-';
				})
				->addFormat('bewijs', function($value, $item){
					if ($value != '') {
						return '<a class="button  pdf" href="'.$item['bewijs'].'" target="_blank"> </a>';
					} else {
						return 'x' ;
					}
				})
				->addFormat('pasfoto', function($value, $item){
					if ($value != '') {
						return '<a  class="button page" href="'.$item['pasfoto'].'" target="_blank"> </a>';
					} else {
						return 'x' ;
					}				
				})
				->addFormat('oplever', function($value, $item){
					if ($value != '') {
						return '<a  class="button page" href="'.$item['oplever'].'" target="_blank"> </a>';
					} else {
						return 'x' ;
					}				
				})

				->addFormat('warmte', function($value, $item){
					if ($value != '') {
						return '<a  class="button page" href="'.$item['warmte'].'" target="_blank"> </a>';
					} else {
						return 'x' ;
					}				
				})

				->addFormat('contract', 'bool')

 
				->setFilters(array(
					'project_name' => array(),
					'og_type' => array(),
  					'address' => array('type' => 'input'),
					'name' => array('type' => 'input'),
					'firstname' => array('type' => 'input'),
					'number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
   					'tenant' => array(),
   					'contract' => array('type' => 'select'),

 				))

				->setOptions(array())

				->render($this);
		}

		
		public function exactTenantListAction(){
			
			 

			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Huurderslijst Exact');

			$this->_setParam('add_additional_user_data', true);

			$u_model = new Users();

			$data = $u_model->getTenantList($this->getAllUrlParams(), false);

			$this->view->ListView($data)

				->setTypes(array(
					'project' => array('title' => 'Project', 'width' => 'small truncate'),
					'og_description' => array('title' => 'Objectgroep', 'width' => 'xsmall truncate'),
					'build' => array('title' => 'Boxnummer', 'width' => 'small right'),
					'user_identifier' => array('title' => 'Klantnummer', 'width' => 'large'),
					'from' => array('title' => 'Begindatum', 'width' => 'small truncate'),
					'price_excl' => array('title' => 'Huurbedrag ex btw', 'width' => 'medium right'),
					'price_tax' => array('title' => 'BTW', 'width' => 'medium right'),
					'price_incl' => array('title' => 'Huurbedrag inc btw', 'width' => 'medium right'),
					'spacer2' => array('title' => '', 'width' => 'xxxxsmall'),
					'has_tax' => array('title' => 'Klant BTW', 'width' => 'xsmall truncate'),
					'deposit' => array('title' => 'Borg', 'width' => 'medium right'),
					'spacer' => array('title' => '', 'width' => 'xxxxsmall'),
					'collection' => array('title' => 'Automatische incasso', 'width' => 'xsmall truncate'),
					'og_type' => array('title' => 'Objectgroeptype', 'width' => 'small truncate'),
					'company' => array('title' => 'Bedrijfsnaam', 'width' => 'small truncate'),
					'initials' => array('title' => 'Voorletters', 'width' => 'small truncate'),
					'middlename' => array('title' => 'Tussenvoegsels', 'width' => 'small truncate'),
					'firstname' => array('title' => 'Voornaam', 'width' => 'small truncate'),
					'name' => array('title' => 'Achternaam', 'width' => 'small truncate'),
					'bdate' => array('title' => 'Geboortedatum', 'width' => 'small truncate'),
					'gender' => array('title' => 'Geslacht', 'width' => 'small truncate'),
					'phone' => array('title' => 'Telefoon', 'width' => 'small truncate'),
					'phone2' => array('title' => 'Mobiel', 'width' => 'small truncate'),
					'email' => array('title' => 'E-mail', 'width' => 'xlarge truncate'),
					'iban' => array('title' => 'IBAN', 'width' => 'large truncate'),
					'bsn' => array('title' => 'BSN', 'width' => 'small truncate'),
					'o_address' => array('title' => 'Contractadres', 'width' => 'small truncate'),
					'o_zipcode' => array('title' => 'Contractadres postcode', 'width' => 'small truncate'),
					'o_city' => array('title' => 'Contractplaats', 'width' => 'small truncate'),
					'u_address' => array('title' => 'Factuuradres', 'width' => 'xlarge truncate'),
					'u_zipcode' => array('title' => 'Postcode factuuradres', 'width' => 'small truncate'),
					'u_city' => array('title' => 'Plaats factuuradres', 'width' => 'small truncate'),
					'u_country' => array('title' => 'Land factuuradres', 'width' => 'small truncate'),
				))

				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat(['collection'], 'bool')
				->addFormat(['o_zipcode', 'u_zipcode'], 'zipcode')
				->addFormat('og_type', function($value){ return ucfirst($value); })

				->addFormat('o_address', function($value, $item){
					return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('user_identifier', function($value, $item){
					$value = str_replace('.', '', $value);  
					$value = str_replace('D', '', $value);  
					$value = str_replace('R', '', $value);

					if ($item['debtorcode'] != '')
						$value = $item['debtorcode'];
					
					return $value;				
				})

				->addFormat('price_tax', function($value){
					return $value > 0 ? '&euro; ' .  new StringFormat($value, 'money') : '-';
				})

				->addFormat('bdate', function($value){
					$date = strtotime($value);
					return date('Y', $date) > 1800 ? date('d-m-Y', $date) : '-';
 				})

 				->addFormat(['phone', 'phone2'], 'phone')

 				->addFormat('iban', 'iban')

 				->addFormat('gender', function($value){
 					$genders = ['male' => 'Man', 'female' => 'Vrouw'];
					return isset($genders[$value]) ? $genders[$value] : 'Onbekend';
 				})

				->addFormat('has_tax', 'bool')

				->addFormat(['from'], 'date')

				->addFormat(['price_excl', 'price_incl', 'deposit'], 'money')

				->addFormat('name', function($value, $item){
					return $item['user_id'] > 0 ? '<a href="support/show/id/' . $item['user_id'] . '/type/user" target="_blank">' . $value . '</a>' : '-';
				})
				->addLegend('tenant-list-legend')

				->addTotals(array('address','size', 'price_excl', 'price_tax', 'price_incl', 'deposit'))

				->setFilters(array(
					'project' => [],
					'og_description' => ['type' => 'input'],
					'build' => ['type' => 'input'],
					'user_identifier' => ['type' => 'input'],
					'from' => ['type' => 'date_range'],
					'has_tax' => [],
					'collection' => [],
					'og_type' => [],
					'company' => ['type' => 'input'],
					'initials' => ['type' => 'input'],
					'middlename' => ['type' => 'input'],
					'firstname' => ['type' => 'input'],
					'name' => ['type' => 'input'],
					'bdate' => ['type' => 'date_range'],
					'gender' => [],
					'phone' => ['type' => 'input'],
					'phone2' => ['type' => 'input'],
					'email' => ['type' => 'input'],
					'iban' => ['type' => 'input'],
					'bsn' => ['type' => 'input'],
					'o_address' => ['type' => 'input'],
					'o_zipcode' => ['type' => 'input'],
					'o_city' => ['type' => 'input'],
					'u_address' => ['type' => 'input'],
					'u_zipcode' => ['type' => 'input'],
					'u_city' => ['type' => 'input'],
					'u_country' => []
				))

				->setOptions(array())

				->render($this);
		}

		public function tenantListAction(){
			
			 

			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Huurderslijst');

            if (is_null($this->getParam('showEmpty')) && !$this->isAjaxRequest) {
                $this->setParam('showEmpty', '1');
            }

			$u_model = new Users();
			$data = $u_model->getTenantList($this->getAllUrlParams(), false);

			$this->view->ListView($data)

                ->setFormattedExcelOptions([
                    'groupValueKey' => 'project',
                    'groupTitleKey' => 'project',
                ])

				->setTypes(array(
					'project' => array('title' => 'Project', 'width' => 'small truncate'),
					'og_type' => array('title' => 'Objectgroeptype', 'width' => 'xsmall'),
					'tenant' => array('title' => 'Huurder', 'width' => 'medium truncate', 'order_using_column' => 'name'),
                    'code' => array('title' => 'Code', 'width' => 'xsmall '),
                    'from' => ['title' => 'Ingang', 'width' => 'medium', 'type' => 'date'],
					'address' => array('title' => 'Adres', 'width' => 'xsmall truncate'),
					'number' => array('title' => 'Nr', 'width' => 'xxxsmall left'),
					'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') : (Settings::get('software_type') == 'energy' ? "Bouwnr" : '#spcf.'), 'width' => 'xxsmall left'),
 					'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
					'city' => array('title' => 'Plaats', 'width' => 'xsmall'),
					'price_excl' => array('title' => 'Excl.', 'width' => 'xsmall right'),
					'price_incl' => array('title' => 'Incl.', 'width' => 'xsmall right'),
                    'spacer2' => array('title' => '', 'width' => 'xxxxsmall'),
                    'rate' => ['title' => 'Periode', 'width' => 'xxxsmall'],
					'deposit' => array('title' => 'Waarborg', 'width' => 'xsmall right'),
					'spacer3' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
					'state' => array('title' => 'Beschikbaar', 'width' => 'small'),

					'indexation' => array('title' => 'Indexatie', 'width' => 'xsmall', 'type' => 'date'),
					'inactive' => array('title' => 'Inactief', 'width' => 'xxxsmall')
				))

				->addFormat('address', function($value){ return $value ? $value : '-'; })
				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat('inactive', 'bool')
                ->addFormat( 'from', 'date')
                ->addFormat( 'rate', 'ucfirst')
                ->addFormat( 'code', function($value){ return $value ?: '-'; })
				->addFormat('zipcode', 'zipcode')
				->addFormat('size', function($value){ return $value > 0 ? number_format($value, 0, ',', '.')  : '-'; })
				->addFormat('state', function($value, $item){
					if($value == 'tenant'){
						if($item['till']){
							return 'Per ' . date('d-m-Y', strtotime('+1 days', strtotime($item['till'])));
						} else {
							return 'Datum onbekend';
						}
					} else{
						return 'Direct';
					}
				})

				->addFormat('og_type', function($value){ return ucfirst($value); })
				
				->addFormat('investor', function($value){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . Investors::getName($value) . '</a>' : '-';
				})

				->addFormat('address', function($value, $item){
					return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('indexation', 'date')

				->addFormat(array('price_excl', 'price_incl', 'deposit'), 'money')

				->addFormat('tenant', function($value, $item){
					$returntenant =  ($item['finaldate'] ? '' : '<img src="media/images/icons/error.png" title="Datum niet definitief">') ;
					$returntenant = $returntenant .( $value > 0 ? ' <a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . '</a>' : '-' );
					return $returntenant;
				})
				->addLegend('tenant-list-legend')

				->addTotals(array('address','size', 'price_excl', 'price_incl', 'deposit'))

				->setFilters(array(
					'project' => array(),
					'og_type' => array(),
					'state' => array(),
                    'from' => array(),
					'investor' => array(),
					'address' => array('type' => 'input'),
					'number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'size' => array('type' => 'number'),
					'zipcode' => array('type' => 'input'),
					'city' => array('type' => 'input'),
                    'rate' => ['type' => 'select'],
					'tenant' => array(),
					'inactive' => array('title' => 'Inactief', 'default' => '0'),
                    'showEmpty' => [
                        'type' => 'select',
                        'renderSeparately' => true,
                        'comparison_operator' => 'ignore',
                        'options' => [
                            0 => ['title' => 'Nee'],
                            1 => ['title' => 'Ja'],
                        ],
                        'title' => 'Leegstand weergeven',
                        'show_all_disabled' => true,
                        'preApplied' => true
                    ],
				))

				->setOptions(array())

				->render($this);
		}

		public function mailCodeAction()
		{
			$this->disableView();

			$uo_id = $this->getParam('uo_id');
			$project_id = $this->getParam('project_id');

			$p = new Project();

			if($uo_id && $project_id)
				$p->mailInvitations($project_id, false, $uo_id);

			if($referer = $_SERVER['HTTP_REFERER'])
				header('Location: '. $referer);
		}


        public function inviteLeadProposalAction () {

            $this->disableView();

            $objectid = $this->getParam('object_id');
            $leadid = $this->getParam('lead_id');

            $uo = Objectusers::getCurrentForUser($leadid);
            $project = Objects::getProject($objectid);

            $p = new Project();

            if($uo['id'] && $project['id']){
                $p->mailInvitations($project['id'], false, $uo['id']);
                $uoModel = new \Objectusers();
                $currentUoRow = $uoModel->getCurrentForUser($uo['customer']);
                \SubscriptionStatusLog::append($currentUoRow, \SubscriptionStatusLog::STATUS_DOCUMENTS_REQUESTED); (edited);
            }


        }



		public function tenantListUsageAction(){
			
			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Bezettingslijst');

			$u_model = new Users();
			$data = $u_model->getTenantList($this->getAllUrlParams(), false , true ,"usage");
			
			$this->view->ListView($data)

				->setTypes(array(
 					'og_description' => array('title' => 'Objectgroep', 'width' => 'large'),
					'number' => array('title' => 'Nr', 'width' => 'small left'),
					'ot_description' => array('title' => 'Object type', 'width' => 'xxlarge'),
					'op_capacity' => array('title' => 'Capaciteit (A)', 'width' => 'small'),
					'tenant' => array('title' => 'Huurder', 'width' => 'xxlarge truncate', 'order_using_column' => 'name'),
  					'size' => array('title' => 'Opp m<sup>2</sup>', 'width' => 'xsmall right'),
					'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
  					'spacer2' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
  					
				))

 				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat('inactive', 'bool')
 				->addFormat('size', function($value){ return $value > 0 ? number_format($value, 0, ',', '.')  : '-'; })
 				->addFormat('op_capacity', function($value){ return $value > 0 ? number_format($value, 0, ',', '.')  : '-'; })
				 

				->addFormat('og_description', function($value){ return ucfirst($value); })

				
				->addFormat('investor', function($value){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . Investors::getName($value) . '</a>' : '-';
				})

				->addFormat('address', function($value, $item){
					return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('indexation', 'date')

 
				->addFormat('tenant', function($value, $item){
					$returntenant =  ($item['finaldate'] ? '' : '') ;
					$returntenant = $returntenant .( $item['customer'] > 0 ? ' <a href="support/show/id/' . $item['customer'] . '/type/user" target="_blank">' . $value . '</a>' : '-' );
					return $returntenant;
				})
 
 
				->setFilters(array(
 					'og_description' => array(),
 					'address' => array('type' => 'input'),
					'number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'size' => array('type' => 'number'),
					'zipcode' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'tenant' => array(),
					'inactive' => array('title' => 'Inactief', 'default' => '0'),
				))

				->setOptions(array())

				->render($this);
		}



        public function tenantListExportAction(){
            $this->view->extraWidth = true;
            $this->view->Breadcrumbs()
                ->addCrumb('Overzichten')
                ->addCrumb('Huurderslijst componenten');

            $u_model = new Users();
            $oLib = new Object();

            $data = $u_model->getExportTenantList($this->getAllUrlParams(), true);

            $types = array(
                'project' => array('title' => 'Projectid', 'width' => 'medium truncate'),
                'project_name' => array('title' => 'Project', 'width' => 'medium truncate'),
                'og_description' => array('title' => 'Objectgroep', 'width' => 'medium truncate'),
                'investor_id' => array('title' => 'Belegger id', 'width' => 'medium truncate'),
                'investor_description' => array('title' => 'Belegger', 'width' => 'medium truncate'),
                'investor_firstname' => array('title' => 'Belegger voornaam', 'width' => 'medium truncate'),
                'investor_lastname' => array('title' => 'Belegger achternaam', 'width' => 'medium truncate'),
                'investor_middlename' => array('title' => 'Belegger tussenvoegsel', 'width' => 'medium truncate'),
                'investor_initals' => array('title' => 'Belegger initialen', 'width' => 'medium truncate'),
                'investor_gender' => array('title' => 'Belegger geslacht', 'width' => 'medium truncate'),
                'investor_email' => array('title' => 'Belegger email', 'width' => 'medium truncate'),
                'investor_phone' => array('title' => 'Belegger telefoon', 'width' => 'medium truncate'),
                'provision' => array('title' => 'provision', 'width' => 'medium truncate'),
                'provisionamount' => array('title' => 'provisionamount', 'width' => 'medium truncate'),
                'provisiondetails_vat' => array('title' => 'provisiondetails_vat', 'width' => 'medium truncate'),
                'provisiondetails_period_type' => array('title' => 'provisiondetails_period_type', 'width' => 'medium truncate'),
                'provisionamount_type' => array('title' => 'provisionamount_type', 'width' => 'medium truncate'),
                'provisiondetails_payment_day' => array('title' => 'provisiondetails_payment_day', 'width' => 'medium truncate'),
                'provisiondetails_interval' => array('title' => 'provisiondetails_interval', 'width' => 'medium truncate'),
				'investor_iban' => ['title' => 'Iban belegger', 'width' => 'medium truncate'],
				'investor_bic' => ['title' => 'bic belegger', 'width' => 'medium truncate'],
				'og_type' => ['title' => 'Objectgroeptype', 'width' => 'small'],
                'tenant' => array('title' => 'Huurder', 'width' => 'large truncate'),
                'id' => array('title' => 'Objectid', 'width' => 'xsmall truncate'),
                'address' => array('title' => 'Adres', 'width' => 'xsmall truncate'),
                'number' => array('title' => 'Nr', 'width' => 'xxxsmall left'),
                'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') : (Settings::get('software_type') == 'energy' ? "Bouwnr" : '#spcf.'), 'width' => 'xxsmall left'),
                'zipcode' => array('title' => 'Postcode', 'width' => 'xxsmall'),
                'city' => array('title' => 'Plaats', 'width' => 'xsmall'),
                'from' => array('title' => 'Ingangsdatum', 'width' => 'xsmall', 'type' => 'date'),
                'till' => array('title' => 'Einddatum', 'width' => 'xsmall' ,'type' => 'date'  ),
                'period_formula' => array('title' => 'Verlengformule', 'width' => 'xsmall'),
				'expire' => array('title' => 'Expiratiedatum', 'width' => 'xsmall', 'type' => 'date'),
                'bill' => array('title' => 'factuurvoorkeur', 'width' => 'xsmall'),
                'collection' => array('title' => 'incasso', 'width' => 'xsmall'),
                'iban' => array('title' => 'iban', 'width' => 'xsmall'),
                'email' => array('title' => 'Email adres', 'width' => 'xsmall'),
                'email_company' => array('title' => 'Email adres', 'width' => 'xsmall'),
                'phone' => array('title' => 'Telefoon', 'width' => 'xsmall'),
                'phone2' => array('title' => 'Telefoon 2', 'width' => 'xsmall'),
                'minimum_indexation' => array('title' => 'Min. indexatie', 'width' => 'xsmall'   ),
                'maximum_indexation' => array('title' => 'Max. indexatie', 'width' => 'xsmall' ),
                'indexation_addition' => array('title' => 'Indexatie toevoeg.', 'width' => 'xsmall' ),
                'email_address_copy' => array('title' => 'Copy mail adres', 'width' => 'xsmall'   ),
                'user_id' => array('title' => 'userid', 'width' => 'xsmall'   ),
                'name' => array('title' => 'achternaam', 'width' => 'xsmall'   ),
                'initials' => array('title' => 'initialen', 'width' => 'xsmall'   ),
                'firstname' => array('title' => 'voornaam', 'width' => 'xsmall'   ),
                'user_gender' => array('title' => 'geslacht', 'width' => 'xsmall'   ),
                'user_initials' => array('title' => 'initialen', 'width' => 'xsmall'   ),
                'user_firstname' => array('title' => 'voornaam', 'width' => 'xsmall'   ),
                'user_middlename' => array('title' => 'tussenvoegsel', 'width' => 'xsmall'   ),
                'user_name' => array('title' => 'achternaam', 'width' => 'xsmall'   ),
                'user_rendered_name' => array('title' => 'huurder', 'width' => 'xsmall'   ),
                'user_phone_home' => array('title' => 'telefoon', 'width' => 'xsmall'   ),
                'user_phone_secondary' => array('title' => 'telefoon alternatief', 'width' => 'xsmall'   ),
                'user_phone_emergency' => array('title' => 'noodnummer', 'width' => 'xsmall'   ),
                'user_email' => array('title' => 'email', 'width' => 'xsmall'   ),
                'user_BSN' => array('title' => 'identificatie', 'width' => 'xsmall'   ),
                'user_identication_type' => array('title' => 'indentificatie type', 'width' => 'xsmall'   ),
                'user_identication_valid_till' => array('title' => 'identificatie geldig tot', 'width' => 'xsmall'   ),
                'user_bdate' => array('title' => 'geboortedatum', 'width' => 'xsmall'   ),
                'partner_gender' => array('title' => 'partner geslacht', 'width' => 'xsmall'   ),
                'partner_initials' => array('title' => ' partner voornaam', 'width' => 'xsmall'   ),
                'partner_firstname' => array('title' => 'partner initialen', 'width' => 'xsmall'   ),
                'partner_middlename' => array('title' => 'partner tussenvoegsel', 'width' => 'xsmall'   ),
                'partner_name' => array('title' => 'partner achternaam', 'width' => 'xsmall'   ),
                'partner_rendered_name' => array('title' => 'partnernaam', 'width' => 'xsmall'   ),
                'partner_phone_home' => array('title' => 'partner telefoon', 'width' => 'xsmall'   ),
                'partner_phone_secondary' => array('title' => 'partner alternatief telefoon', 'width' => 'xsmall'   ),
                'partner_phone_emergency' => array('title' => 'partner noodnummer', 'width' => 'xsmall'   ),
                'partner_email' => array('title' => 'partner email', 'width' => 'xsmall'   ),
                'partner_BSN' => array('title' => 'partner identificatie', 'width' => 'xsmall'   ),
                'partner_identication_type' => array('title' => 'partner indentificatie type', 'width' => 'xsmall'   ),
                'partner_identication_valid_till' => array('title' => 'partner identificatie geldig tot', 'width' => 'xsmall'   ),
                'partner_bdate' => array('title' => 'partner geboortedatum', 'width' => 'xsmall'   ),
                'contractor_gender' => array('title' => 'contractor geslacht', 'width' => 'xsmall'   ),
                'contractor_initials' => array('title' => ' contractor voornaam', 'width' => 'xsmall'   ),
                'contractor_firstname' => array('title' => 'contractor initialen', 'width' => 'xsmall'   ),
                'contractor_middlename' => array('title' => 'contractor tussenvoegsel', 'width' => 'xsmall'   ),
                'contractor_name' => array('title' => 'contractor achternaam', 'width' => 'xsmall'   ),
                'contractor_rendered_name' => array('title' => 'contractornaam', 'width' => 'xsmall'   ),
                'contractor_phone_home' => array('title' => 'contractor telefoon', 'width' => 'xsmall'   ),
                'contractor_phone_secondary' => array('title' => 'contractor alternatief telefoon', 'width' => 'xsmall'   ),
                'contractor_phone_emergency' => array('title' => 'contractor noodnummer', 'width' => 'xsmall'   ),
                'contractor_email' => array('title' => 'contractor email', 'width' => 'xsmall'   ),
                'pcontractor_BSN' => array('title' => 'contractor identificatie', 'width' => 'xsmall'   ),
                'contractor_identication_type' => array('title' => 'contractor indentificatie type', 'width' => 'xsmall'   ),
                'contractor_identication_valid_till' => array('title' => 'contractor identificatie geldig tot', 'width' => 'xsmall'   ),
                'contractor_bdate' => array('title' => 'contractor geboortedatum', 'width' => 'xsmall'   ),
				'deposit' => array('title' => 'deposit', 'width' => 'xsmall'   ),
                'deposit_type' => array('title' => 'deposit_type', 'width' => 'xsmall'   ),
                'deposit_payed_to_broker' => array('title' => 'deposit_payed_to_broker', 'width' => 'xsmall'   ),
                'notice_period' => array('title' => 'Opzegtermijn', 'width' => 'xsmall'),
                'notice' => array('title' => 'Opzegdatum', 'width' => 'xsmall', 'type' => 'date'),
                'useridentifier' => array('title' => 'Klantnummer', 'width' => 'xsmall'),
                'size' => array('title' => 'Opp m2', 'width' => 'xxsmall right'),
                'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
                'parking_spots' => array('title' => 'Park.', 'width' => 'xxsmall'),
            );

            if(Settings::get('general_company_shortname') == 'ravel')
                $types['contract'] = array('title' => 'Contract getekend', 'width' => 'medium');


            $all_components = array();
            foreach($data as $item)
                if(is_array(($item['components'])))
                    foreach($item['components'] as $component) {
                        $all_components[$component] = $component;

                    }

            $totalcomponents = [];
            foreach($all_components as $component => $value)
                $totalcomponents[] .= $value ;

            $othertotals = array( 'size', 'price_excl', 'price_incl', 'deposit');

            $totals = array_merge((array) $totalcomponents, (array) $othertotals);

            $view = $this->view;

            foreach($all_components as $component)
                $types[$component] = array('title' => $component, 'width' => 'medium');

            $types += array(
                'spacer2' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
                'state' => array('title' => 'Expiratie', 'width' => 'xsmall'),
                'indexation' => array('title' => 'Indexatie', 'width' => 'xsmall', 'type' => 'date'),
                'deposit' => array('title' => 'Waarborg', 'width' => 'xsmall right'),
                'spacer3' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
                'rate' => array('title' => 'Factuur', 'width' => 'xsmall'),
                'ledger' => array('title' => 'Grootboek', 'width' => 'hidden'),
            );

            $this->view->ListView($data)

                ->setTypes($types)

                ->addFormat('address', function($value){ return $value ? $value : '-'; })
                 ->addFormat('inactive', 'bool')
                ->addFormat('zipcode', 'zipcode')
                ->addFormat('contract', 'bool')
                ->addFormat( [ 'from' ], 'date')
                ->addFormat('notice_period', function($value){ return $value ? $value . ' maanden' : '-'; })
                ->addFormat('period_formula', function($value, $item) use ($view) {
                    return $view->periodFormula($value, $item['from'], $view, array('compact' => true), $item['notice_period']);
                })
                ->addFormat('expire', function($value, $item) use ($view, $oLib) {
                    $nextTillDate = $oLib->getNextTillDate($item['period_formula'], $item['from'], $item['notice_period']);
                    return intval($nextTillDate) > time()? date('d-m-Y', $nextTillDate): '-';
                })
                ->addFormat('notice', function($value, $item) use ($view, $oLib) {
                    $nextTillDate = $oLib->getNextTillDate($item['period_formula'], $item['from'], $item['notice_period']);

                    if(intval($nextTillDate) > time()) {
                        $notice_period = intval($item['notice_period']) > 0? $item['notice_period']: 1;
                        return date('d-m-Y', strtotime( '-' . $notice_period . ' months', $nextTillDate));
                    } else {
                        return '-';
                    }
                })
                ->addFormat('size', function($value){ return $value > 0 ? number_format($value, 0, ',', '.') : '-'; })
                ->addFormat('state', function($value, $item){
                    if($value == 'tenant'){
                        if($item['till']){
                            return  date('d-m-Y', $item['till']);
                        } else {
                            return 'Onbekend';
                        }
                    } else{
                        return 'Direct';
                    }
                })

                ->addFormat('indexation', 'date')

                ->addFormat('og_type', function($value){ return ucfirst($value); })


                ->addFormat('investor', function($value){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . Investors::getName($value) . '</a>' : '-';
                })

                ->addFormat('address', function($value, $item){
                    return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
                })

                ->addFormat(array('price_excl', 'price_incl', 'deposit' ), 'money')

                ->addFormat('tenant', function($value,$item){
                    return ($value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . '</a>' : ($item['future']['from']? 'Vacancy till '. date('d-m-Y',strtotime($item['future']['from'])) : 'Vacancy'));
                })


                ->addFormat($all_components, 'money')

                ->addLegend('tenant-list-legend')

                ->addTotals($totals)

                ->setFilters(array(
                    'project' => array(),
                    'og_description' => array(),
                    'og_type' => array(),
                    'state' => array(),
                    'investor' => array(),
                    'contract' => array(),
                    'address' => array('type' => 'input'),
                    'number' => array('type' => 'number'),
                    'build' => array('type' => 'input'),
                    'size' => array('type' => 'number'),
                    'zipcode' => array('type' => 'input'),
                    'city' => array('type' => 'input'),
                    'from' => array('type' => 'input'),
                    'expire' => array('type' => 'input'),
                    'notice' => array('type' => 'input'),
                    'useridentifier' => array('type' => 'input'),
                    'tenant' => array('type' => 'input'),
                    'inactive' => array('title' => 'Inactief', 'default' => '0'),
                ))

                ->setOptions(array('custom_class'=> 'commercialtenantlist'))
                ->render($this);
        }

        private function handleSessionSavedFilterSettings(){
			$filters = [
				'prices_multiplier' => 'yearly',
				'future_uo' => 'show',
				'ocv_version' => 'invoiced'
			];

			foreach($filters as $filter_name => $filter_default_value) {
                if (!is_null($this->getParam($filter_name))) {
                    $_SESSION[$filter_name . '_value'] = $this->getParam($filter_name);
                } elseif (!$this->isAjaxRequest) {
                    if ($_SESSION[$filter_name . '_value']) {
                        $this->setParam($filter_name, $_SESSION[$filter_name . '_value']);
                    } else {
                        $this->setParam($filter_name, $filter_default_value);
                    }
                }
            }
		}

		public function tenantListAdvancedAction(){

            ini_set('max_input_time', 10000);
            ini_set('memory_limit', '8000M');
            ini_set('max_execution_time', 0);
		 

			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Huurderslijst componenten');

			$oLib = new Object();

			$this->handleSessionSavedFilterSettings();

			$tenant_list_model = new \Controllers\User\TenantListAdvanced();
			$data = $tenant_list_model->getList($this->getAllUrlParams());

            $investorNames = (new \Investors())->getInvestorNames(array_column($data, 'investor'));
			
			$types = [
                'corporation_name' => ['title' => 'Entiteit', 'width' => 'medium truncate'],
                'administration_code' => ['title' => 'Administratie', 'width' => 'hidden'],
                'project_code_financial' => ['title' => 'Projectcode', 'width' => 'hidden'],
                'investor' => ['title' => 'Belegger', 'width' => 'medium truncate'],
				'project' => ['title' => 'Project', 'width' => 'medium truncate'],
				'og_description' => ['title' => 'Objectgroep', 'width' => 'medium truncate'],
				'og_type' => ['title' => 'Objectgroeptype', 'width' => 'small'],
				'tenant' => ['title' => 'Huurder', 'width' => 'large truncate'],
				'address' => ['title' => 'Adres', 'width' => 'xsmall truncate'],
				'number' => ['title' => 'Nr', 'width' => 'xxxsmall left'],
                'build' => [
                    'title' => Settings::get('build_label') ?: '#spcf.',
                    'width' => 'xxsmall left',
                    'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT
                ],
				'zipcode' => ['title' => 'Postcode', 'width' => 'xxsmall'],
				'city' => ['title' => 'Plaats', 'width' => 'xsmall'],
				'from' => ['title' => 'Ingangsdatum', 'width' => 'xsmall', 'type' => 'date'],
				'period_formula' => ['title' => 'Verlengformule', 'width' => 'xsmall', 'format_current_page_items_only' => true],
				'expire' => ['title' => 'Expiratiedatum', 'width' => 'xsmall', 'type' => 'date'],
				'notice_period' => ['title' => 'Opzegtermijn', 'width' => 'xsmall'],
                'investor_notice_date' => ['title' => 'Opzegdatum belegger', 'width' => 'hidden'],
				'notice' => ['title' => 'Opzegdatum', 'width' => 'xsmall', 'type' => 'date'],
                'woz_previous_year' => ['title' => 'WOZ ' . (date('Y') - 1), 'width' => 'xsmall'],
                'woz' => ['title' => 'WOZ ' . date('Y'), 'width' => 'xsmall'],
                'energy_label' => ['title' => 'ELabel', 'width' => 'xsmall'],
				'useridentifier' => ['title' => 'Klantnummer', 'width' => 'xsmall'],
                'relation_id_financial_software' => ['title' => 'Klantnummer fin. pakket', 'width' => 'hidden'],
                'tenant_id' => ['title' => 'tenant id#', 'width' => 'hidden'],
                'id' => ['title' => 'object id#', 'width' => 'hidden'],
				'size' => array('title' => 'VVO', 'width' => 'xxsmall right'),
                'bvo' => array('title' => 'BVO', 'width' => 'xxsmall right'),
				'spacer' => ['title' => '&nbsp;', 'width' => 'xxxxsmall'],
				'parking_spots' => ['title' => 'Park.', 'width' => 'xxsmall'],
            ];

			if (!Settings::get('for_third_party')) {
				unset($types['investor']);
			}

			if(Settings::get('general_company_shortname') == 'ravel')
				$types['contract'] = ['title' => 'Contract getekend', 'width' => 'medium'];

			$breakOptionColumns = [
				'bo_notice_period' => [ 'title' => "Break-optie<br>opzegtermijn", 'width' => 'xsmall', 'type' => 'date' ],
				'bo_date_0' => [ 'title' => "Eerste<br>break-optie", 'width' => 'xsmall', 'type' => 'date' ],
				'bo_date_1' => [ 'title' => "Tweede<br>break-optie", 'width' => 'xsmall', 'type' => 'date' ],
			];
			array_add_after($types, 'from', $breakOptionColumns);

            $objectIds = array_column($data, 'id');

			$all_components = $tenant_list_model->getAllComponents($objectIds);

            foreach ($all_components as $componentId => $componentName) {
                $componentColumnKey = 'component_' . $componentId;
                $componentColumns[] = $componentColumnKey;
                $types[$componentColumnKey] = ['title' => $componentName, 'width' => 'medium'];
            }

				$othertotals = array( 'size', 'price_excl', 'price_incl', 'deposit');
		
			$totals = array_merge((array) $componentColumns, (array) $othertotals);

			$view = $this->view;

            $types += array(
				'spacer2' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
				'state' => array('title' => 'Expiratie', 'width' => 'xsmall'),
				'indexation_date' => ['title' => 'Indexatie', 'width' => 'xsmall', 'type' => 'date'],
				'deposit' => array('title' => 'Waarborg', 'width' => 'xsmall right'),
				'spacer3' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
				'invoice_rate' => ['title' => 'Factuur', 'width' => 'xsmall'],
				'ledger' => array('title' => 'Grootboek', 'width' => 'hidden'),
			);

			$this->view->ListView($data)

				->setTypes($types)

                ->setFormattedExcelOptions([
                    'groupValueKey' => 'project',
                    'groupTitleKey' => 'project',
                ])

				->addFormat('address', function($value){ return $value ? $value : '-'; })
				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat('inactive', 'bool')
				->addFormat('zipcode', 'zipcode')
				->addFormat('contract', 'bool')
				->addFormat( [ 'from', 'bo_date_0', 'bo_date_1', 'investor_notice_date' ], 'date')
				->addFormat(['woz_previous_year', 'woz'], 'money')
				->addFormat(['notice_period', 'bo_notice_period'], function($value){ return $value ? "$value maanden" : '-'; })
				->addFormat('period_formula', function($value, $item) use ($view) {
					return $view->periodFormula($value, $item['from'], $view, array('compact' => true), $item['notice_period']);
				})
				->addFormat('expire', function($value, $item) use ($view, $oLib) {
					$nextTillDate = $item['next_till_date'];
					return intval($nextTillDate) > time()? date('d-m-Y', $nextTillDate): '-';
				})
                ->addFormat('notice', function($value, $item) use ($view, $oLib) {
                    $nextTillDate = $item['next_till_date'];

                    if(intval($nextTillDate) > time()) {
                        $notice_period = intval($item['notice_period']) > 0? $item['notice_period']: 1;
                        return(Date::calculateExpireDate($nextTillDate,$notice_period));
                    } else {
                        return '-';
                    }

                })

				->addFormat('size', function($value){ return $value > 0 ? number_format($value, 0, ',', '.') : '-'; })
                ->addFormat('bvo', function($value){ return $value > 0 ? number_format($value, 0, ',', '.') : '-'; })
				->addFormat('state', function($value, $item){
					if($value == 'tenant'){
						if($item['till']){
							return  date('d-m-Y', strtotime($item['till']));
						} else {
							return 'Onbekend';
						}
					} else{
						return 'Direct';
					}
				})

				->addFormat('indexation_date', 'date')

				->addFormat('build', function($value){ return $value ?: '-'; })
				
				->addFormat('og_type', function($value){ return ucfirst($value); })

                ->addFormat('investor', function ($value) use ($investorNames) {
                    if(!$value){
                        return '-';
                    }

                    $investorName = isset($investorNames[$value]) ? $investorNames[$value] : '-';

                    return '<a href="/support/show/id/' . $value . '/type/investor" target="_blank">' . $investorName . '</a>';
                })

				->addFormat('address', function($value, $item){
					return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat(array('price_excl', 'price_incl', 'deposit' ), 'money')

				->addFormat('tenant', function($value,$item){
					return ($value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $item['tenant_name'] . '</a>' : ($item['future']['from']? 'Vacancy till '. date('d-m-Y',strtotime($item['future']['from'])) : 'Vacancy'));
				})

				->addFormat('invoice_rate', function($value){
                    $rates = [
                        12 => 'maand',
                        4 => 'kwartaal',
                        52 => 'week',
                        2 => 'halfjaar',
                        1 => 'jaar'
                    ];

                    return isset($rates[$value]) ? ucfirst($rates[$value]) : '-';
				})

				
				->addFormat($componentColumns, 'money')

				->addLegend('tenant-list-legend')
				
				->addTotals($totals)
				
				->setFilters(array(
                    'corporation_name' => array(),
					'project' => array(),
					'og_description' => array(),
					'og_type' => array(),
					'state' => array(),
					'investor' => ['type' => 'input'],
					'contract' => array(),
					'address' => array('type' => 'input'),
					'number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'size' => array('type' => 'number'),
					'zipcode' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'from' => array('type' => 'input'),
					'expire' => array('type' => 'input'),
					'notice' => array('type' => 'input'),
					'useridentifier' => array('type' => 'input'),
					'tenant' => array('type' => 'input'),
					'inactive' => array('title' => 'Inactief', 'default' => '0'),
                    'invoice_rate' => [],
                    'prices_multiplier' => [
                    	'renderSeparately' => true,
						'title' => 'Bedragen periode',
						'custom_options' => ['yearly' => ['title' => 'Jaarbasis'], 'periodical' => ['title' => 'Periodiek']],
						'hideCount' => true,
						'preApplied' => true,
						'custom_options_only' => true,
						'show_all_disabled' => true
					],
                    'future_uo' => [
                        'renderSeparately' => true,
                        'title' => 'Toekomstige huurders',
                        'custom_options' => ['show' => ['title' => 'Weergeven'], 'hide' => ['title' => 'Verbergen']],
                        'hideCount' => true,
                        'preApplied' => true,
                        'custom_options_only' => true,
                        'show_all_disabled' => true
                    ],
                    'ocv_version' => [
                        'renderSeparately' => true,
                        'title' => 'Versie van bedragen',
                        'custom_options' => [
                        	'invoiced' => ['title' => 'Meest recent gefactureerde versie'],
							'most_recent' => ['title' => 'Meest actuele versie']
						],
                        'hideCount' => true,
                        'preApplied' => true,
                        'custom_options_only' => true,
                        'show_all_disabled' => true
                    ]
				))

				->setOptions(array('custom_class'=> 'commercialtenantlist'))
				->render($this);
		}


		/**
		 * Shows a list of E-mails for the current user
		 * @return null
		 */
		public function emailListAction(){
			 
			$this->_helper->viewRenderer->setNoRender(true);

			$allowed_params = array('ajax', 'page', 'query','matchedignore', 'thisUserOnly' , 'filterComplaint');
			
			foreach($this->_getAllParams() as $param_name => $param_value)
				if(!in_array($param_name, $allowed_params)){
					$this->_setParam($param_name, NULL);
					unset($_POST[$param_name]);
				}

			echo $this->view->action('list', 'email', null, array(
				'title' => translate()->_('email_messages'), 
				'filterUser' => loginManager::data()->id,
				'object' => true,
				'folder' => 'all',
				'ajax' => (bool) $this->_getParam('ajax'),
				'matchedignore' => true,
				'thisUserOnly' => true,
				'showFolderlist' => false,
				'filterComplaint' => $this->_getParam('filterComplaint'),
				'concept' => false,
				'limit' => 10,
				'showSearch' => true,
				'unsend' => false,
				'showMemo' => false,
				'url' => 'user/email-list/',
				'detailsUrl' => 'user/email-details/',
                'addClass' => Settings::get('modules_tenantLogin_new_styling') ? 'infobox' : '',
                'hideUserAssignedEmails' => Settings::get('email_hide_matched_on_portal'),
			));
		}

		/**
		 * Shows a details view of an Email for the current user
		 * @return null
		 */
		public function emailDetailsAction(){
			$this->_helper->viewRenderer->setNoRender(true);

			$allowed_params = array('id');

			foreach($this->_getAllParams() as $param_name => $param_value)
				if(!in_array($param_name, $allowed_params)){
					$this->_setParam($param_name, NULL);
					unset($_POST[$param_name]);
				}

			if(!$this->_getParam('id'))
				return;

			echo $this->view->action('details', 'email', null, array(
				'filterUser' => loginManager::data()->id,
				'ajax' => true,
				'id' => $this->_getParam('id'),
			));
		}

		public function nawExportAction(){
			
			//die();
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$type = $this->_getParam('type') ?: 'debtor';
			
			$u = new User();

			if(Settings::get('financial_export_system') == 'exactonline')
				$u->exactNawExact($this->_getParam('project'), $this->_getParam('corporation'));
			elseif(Settings::get('financial_export_system') == 'accountview')
				$u->accountviewNaw($this->_getParam('project'), $type == 'debtor', $type == 'technical');
		}

		public function investorExportAction(){
			
			//die();
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			
			$u = new User();
			
			$u->investorNawExact($this->_getParam('project')); 
		}
	



		public function updatebankaccountToIbanAction(){
			
			
			$this->disableView();
			$u = new Users();
			 

			
			foreach(explode("\r", file_get_contents('updateiban.csv')) as $i => $line){
				

				$item = explode(";", $line);
				
				$csv_columns = $csv_columns ? $csv_columns : $item;
	
				if($i == 0) 
					continue;
				
				$item = array_combine($csv_columns, $item);

				$u_row = $u->fetchRow($u->select()->where('id = ?', $item['ID']));
			

				if(!$u_row) 
					continue;

					
					if ($item['IBAN'] != '') {
						$u_row->iban = $item['IBAN'];
						$u_row->bic = $item['BIC'];
						$u_row->save();
					} else {
						new EmailOutgoing(array(
							'to' => 			array('email' => Settings::get('general_email'), 'name' => 'Klantenservice'),
							'template' => 		'noibanconvertpossible.phtml',
							'subject' =>		'Rekeningnummer kan niet automatisch geconverteerd worden naar IBAN en BIC.',
							'userid' =>			$item['ID'],
						));
					}
						


			}

		}

		public function investorCsvImportAction(){
			
			$this->disableView();
			
			$iv = new Investors();
			$u = new Users();
			$a = new Address();
			$p = new UserInvoicePreferences();
			
			foreach(explode("\r", file_get_contents('investors.csv')) as $i => $line){
				
				$item = explode(";", $line);
				$csv_columns = $csv_columns ? $csv_columns : $item;
	
				if($i == 0) continue;
				
				$item = array_combine($csv_columns, $item);
				
				// add to user table
				$user = $u->createRow();
				$user->type = 'investor';	
				$user->gender = $item['gender'];
				$user->initials = $item['initials'];
				$user->firstname = $item['firstname'];
				$user->middlename = $item['middlename'];
				$user->name = $item['name'];
				$user->bankaccount =  $item['bankaccount'];
				$user->iban =  $item['iban'];
				$user->bankaccountname =  $item['bankaccountname'];
				
				$user->save();

				// add to investor table
				$investor = $iv->createRow();
				$investor->name = $item['name'] . ', ' . $item['initials'];
				$investor->description = ($item['gender'] == 'male' ? 'Dhr.' : 'Mevr.') . " " .$item['initials'] . " " . $item['firstname']." " .$item['name'];
				$investor->user = $user->id;				
				$investor->save();
				
				// add address  to table for contact info (type = user)
				$address = $a->createRow();
				$address->type = 'user';
				$address->type_id = $user->id;
				$address->phone1 = $item['phone1'];
				$address->phone2 = $item['phone2'];
				$address->email = $item['email'];
				$address->save();
				
				
				// add address info to table for address info (type = investor)
				$address = $a->createRow();
				$address->type = 'investor';
				$address->type_id = $investor->id;
				$address->address = $item['Adres'];
				$address->number = $item['Nummer'];
				$address->zipcode = str_replace(' ', '', $item['Postcode']);
				$address->city = $item['Stad'];
 				$address->save();

			}
		}




		public function tenantCsvImportAction(){
			
			$this->disableView();
			
			$u = new Users();
			$a = new Address();
			$p = new UserInvoicePreferences();
			
			foreach(explode("\r", file_get_contents('tenants.csv')) as $i => $line){
					
				$line = trim($line);

				$item = explode(";", $line);

				foreach ($item as &$column) $column = trim($column);

				$csv_columns = $csv_columns ? $csv_columns : $item;
	
				
				if($i == 0 || $i == 1) continue;
				
			
				$item = array_combine($csv_columns, $item);

				
				$user = $u->createRow();

				$user->type = 'tenant';

				if($item['relatievorm'] == 'bedrijf' || $item['corporation'] != ''){
					$user->corporation = $item['corporation'];
				} else {
					
					$date = explode("/", $item['bdate']);

					$dateday= $date[1];
					$datemonth = $date[0];
					$dateyear = '19'.$date[2];

					if(in_array($item['gender'], array('Man', 'Vrouw')))
						$user->gender = $item['gender'] == 'Vrouw' ? 'female' : 'male';
					elseif(in_array($item['gender'], array('Mevrouw', 'De heer')))
						$user->gender = $item['gender'] == 'Mevrouw' ? 'female' : 'male';
					elseif(in_array($item['gender'], array('dhr.', 'mevr.')))
						$user->gender = $item['gender'] == 'mevr.' ? 'female' : 'male';
					elseif(in_array($item['gender'], array('dhr', 'mevr')))
						$user->gender = $item['gender'] == 'mevr' ? 'female' : 'male';
					else
						$user->gender = 'unknown';//$item['gender'];

					$user->initials = $item['initials'];
					$user->firstname = $item['firstname'];
					$user->middlename = $item['middlename'];
					$user->name = $item['name'];
					$user->bankaccount =  $item['bankaccount'];
					$user->bic =  $item['bic'];
					$user->iban =  $item['iban'];
					$user->marital =  $item['marital'];
					$user->adults =  $item['adults'];
					$user->language =  ($item['country'] == 'Nederland' || $item['country'] == '' ? 'nl' : 'en');
					$user->bdate =  $dateyear."-".$datemonth."-".$dateday;
					$user->kids =  $item['kids'];
				//	$user->project =  '282';
					$user->comment =  $item['comment'] ."\n" .$item['study'] ;
				}

				$user->chamberofcommerce = $item['NawNaam'];

				$user->save();

				if ( preg_match('/([^\d]+)\s?(.+)/i', $item['NawPostAdres'], $result) ){
					$item['address'] = $result[1];
					$item['number'] = $result[2];
 				}

				$email_model = new CRMEmailAddress();

				if($item['email'] && trim($item['email']) != '') {
					$email_row = $email_model->createRow();

					$email_row->address = $item['email'];
					$email_row->address_type = 'private';
					$email_row->type = 'user';
					$email_row->map_to = $user->id;

					$email_row->save();
				}

				$phone_model = new PhoneNumber();

				foreach(array('phone1' => 'primary', 'phone2' => 'secondary') as $phone_field => $phone_priority) {

					if(!$item[$phone_field] || trim($item[$phone_field]) == '') continue;

					$phone_row = $phone_model->createRow();

					$phone_row->number = $item[$phone_field];
					$phone_row->number_type = 'private';
					$phone_row->priority = $phone_priority;
					$phone_row->type = 'user';
					$phone_row->map_to = $user->id;

					$phone_row->save();
				}

				if($item['address']) {
					$address = $a->createRow();

					$address->type = 'user';
					$address->type_id = $user->id;

					$address->address = $item['address'];
					$address->number = $item['number'];
					$address->zipcode = str_replace(' ', '', $item['zipcode']);
					$address->city = $item['city'];

					$address->save();
				}

				if ( preg_match('/([^\d]+)\s?(.+)/i', $item['FactuurAdres'], $result) ){
					$item['invoice_address'] = $result[1];
					$item['invoice_number'] = $result[2];
 				}

 				$has_invoice_address = trim($item['invoice_address']) != '';

 				if($has_invoice_address){
					$invoice_address = $a->createRow();

					$invoice_address->type = 'invoice';
					$invoice_address->type_id = $user->id;

					$invoice_address->address = $item['invoice_address'];
					$invoice_address->number = $item['invoice_number'];
					$invoice_address->zipcode = str_replace(' ', '', $item['FactuurPostcode']);
					$invoice_address->city = $item['FactuurPlaats'];

					$invoice_address->save();

					$invoice_user = $u->createRow();

					$invoice_user->type = 'tenant';

					if($item['relatievorm'] == 'bedrijf'){
						$invoice_user->corporation = $item['corporation'];
					} else {
						$invoice_user->gender = $item['FactuurAanhef'];
						$invoice_user->initials = $item['FactuurVoorletter'];
						$invoice_user->middlename = $item['FactuurTussenvoegsel'];
						$invoice_user->name = $item['FactuurNaam'];
					}

					$invoice_user->save();
				}

				$prefs = $p->createRow();

				$prefs->user = $user->id;
				$prefs->address = $invoice_address->id > 0 ? $invoice_address->id : $address->id;
				$prefs->rate = $item['relatievorm'] == 'bedrijf' ? 3 : 1;
				$prefs->bill = $address->email ? 'email' : 'mail';

				if($invoice_user > 0)
					$prefs->invoice_user =  $invoice_user->id;

				$prefs->save();
			}
		}
		
		public function dummyLoginAction(){
			
		}
		public function dummyLoginInvestorAction(){

		}
		
		public function depositOverrideAction(){
			$this->view->Breadcrumbs()
                ->addCrumb('Huurder pagina', 'support/show/type/user/id/' . $this->getParam('customer') . '/')
                ->addCrumb('Waarborg bedrag bewerken');

			$this->view->EditView($this, 'deposit', array('modeloverride' => 'objectusers', 'id' => $this->_getParam('uoid'), 'customer' => $this->_getParam('customer')))
							
				->setOptions([
					'redirect_to' => 'breadcrumbs'
                ])
				
				->render($this);
		}

		public function rentalSavedOverrideAction(){
			$this->view->Breadcrumbs()->addCrumb('Waarborg bedrag bewerken');

			$this->view->EditView($this, 'rental-saved', array('modeloverride' => 'objectusers', 'id' => $this->_getParam('uoid')))
							
				->setOptions(array(
					'redirect_to' => 'referer'
				))
				
				->render($this);
		}
		
		public function payDepositAction(){
            $this->view->headScriptHashed()->appendFile('media/javascript/user/deposit-override.js');

			$trans = new Transaction();
			$from = strtotime('-6 months');
			$this->payProbs = $trans->getPaymentProblems($this->_getParam('customer'), $from);

			$editViewParams = [
				'modeloverride' => 'objectusers',
				'customer' => $this->_getParam('customer'),
			];

			if(intval($this->getParam('uoid', false)) > 0) {
				$editViewParams['id'] = $this->getParam('uoid');
			}
			 
			$this->view->Breadcrumbs()
                ->addCrumb('Huurder pagina', 'support/show/type/user/id/' . $this->getParam('customer') . '/')
                ->addCrumb('Waarborg bedrag uitbetalen');

			$this->view->EditView($this, 'pay-deposit', $editViewParams)
							
				->setOptions(array(
					'redirect_to' => 'breadcrumbs',
					'postHandler' => function($post, $params){
						if(intval($params['uoid']) > 0) {
							$uoModel = new Objectusers();
							$uo = $uoModel->getById($params['uoid']);

						} else {
							$uo = Objectusers::getCurrentForUser($params['customer']);
						}

						$object = $uo['object'];
						$corporation = Object::getCorporation($object);
						$project = Objects::getProject($object);

                        if ($post['deposit']['pay_back'] > 0) {
                            $clieop = TransactionsCollection::create(array(
                                'type' => 'debet',
                                'process_date' => date('d-m-Y', strtotime('+3 days')),
                                'corporation' => $corporation['id'],
                                'project' => $project ? $project->id : false
                            ));

                            $clieop->createPayment(array(
                                'user' => $params['customer'],
                                'amount' => abs((float)((string)new StringFormat($post['deposit']['pay_back'], 'money_db') / 100)),
                                'description' => $post['deposit']['description'] ? $post['deposit']['description'] : 'Uitbetaling borgbedrag'
                            ));
                        }

						$uo->deposit_repayed = new StringFormat($post['deposit']['pay_back'], 'money_db');
						$uo->save();

						return true;
					}
				))
				
				->render($this);
		}		
		
		/**
		 * Dummy login using hash
		 * Hash generated by xmlrpc
		 * 
		 * @return null Returns null value on fail
		 */
		public function loginUsingHashAction(){
			$u = new Users();
			
			$this->disableView();

			if(!$this->_getParam('hash') || !$this->_getParam('user'))
				return;

			$udl = new UserDummyLogin();
			
			$udl_row = $udl->fetchRow($udl->select()->where('user = ?', $this->_getParam('user')));
			
			if(!$udl_row)
				return;

			if($this->dummyLoginSecretHash($udl_row->user, $udl_row->secret, $udl_row->tenant) !== $this->_getParam('hash'))
				return;



			$row = $u->fetchRow($u->select()
				->where('id = ?', $udl_row->tenant)
				->where('type IN (?)', [
					'tenant',
					'company',
                    'investor',
					'registrant',
				])
			);
			
			if(!$row)
				return;
			
			$backup = $row->toArray();
			
			$row->username = uniqid();
			$row->password = sha1('temp');
			
			$row->save();

			loginManager::login($row->username, 'temp');
			
			$row->username = $backup['username'];
			$row->password = $backup['password'];
			
			$row->save();
			
			$udl_row->delete();
			
			$this->tenantLoginRedirect($row['type']);
		}
		
		/**
		 * Get dummy login hash
		 * @return null Returns null value on fail
		 */
		public function getDummyLoginHashAction(){
			$this->disableView();


			
			if(!$this->_getParam('id'))
				return;

			$u = new Users();
			
			// check if selected user exists
			if(!$u->fetchRow($u->select()->where('id = ?', $this->_getParam('id'))))
				return;


			global $config;

			$secret = uniqid();

			// The next few lines (including the Zend_XmlRpc_Client part) are used to inform the test environment of the
			// incoming user/login-using-hash call. Otherwise the test env. has no way of knowing wheter the call is valid.
			$live = $this->_getParam('live');
			if ($live == 'true') {
				$url =  ($config->app->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl;
			} else {
				$url = Settings::get('modules_tenantLogin_custom_dummy_login_url') ? Settings::get('modules_tenantLogin_custom_dummy_login_url') : ($config->app->secure ? 'https://' : 'http://') . $config->app->server . $config->app->baseurl;
			}

			$client = new Zend_XmlRpc_Client($url . '/xmlrpc/');
 			$result = $client->call('user.setSecret', array(loginManager::data()->id, $this->_getParam('id'), $secret));
			
			if(!$result)
				return;
			
			// return hash
			echo json_encode($this->dummyLoginSecretHash(loginManager::data()->id, $secret, $this->_getParam('id')));
			
		}
		
		/**
		 * Build login hash using secret
		 * @param  integer $user Admin user id
		 * @param  string $secret Pre-determined secret component
		 * @param  integer $user2 Tenant user id
		 * @return string Hash for dummy login, md5 encrypted
		 */
		public function dummyLoginSecretHash($user, $secret, $user2){
			return md5($user . $secret . 'mijnvaanster' . $user2);
		}
		
		public function checkUserPaymentAction(){
			$this->disableView();
			
			$db = db();
			$uip = new UserInvoicePreferences();
			$uo = new ObjectUsers();
			
			$select = $db->select()
			
				->from(array('o' => 'objects'),					array('id'))
				
				->joinLeft(array('og' => 'objectgroup'),
					'og.id = o.objectgroup',					array('type'))
				
				->joinLeft(array('uo' => 'users_objects'),
					'uo.object = o.id',							array('uoid' => 'id','customer', 'invoiceable', 'order'))
					
				->joinLeft(array('uo2' => 'users_objects'),
					'uo2.object = o.id AND
					uo.id != uo2.id AND
					uo2.role = "normal"',						array('uo2id' => 'id', 'customer2' => 'customer', 'invoiceable2' => 'invoiceable'))
				
				->joinLeft(array('u' => 'users'),
					'u.id = uo.customer',						array('bankaccount'))
					
				->joinLeft(array('u2' => 'users'),
					'u2.id = uo2.customer',						array('bankaccount2' => 'bankaccount'))
					
				->joinLeft(array('a' => 'address'),
					'a.type_id = o.id AND a.type = "object"',	array('address' => 'id'))
					
				->joinLeft(array('uip' => 'users_invoice_preferences'),
					'uip.user = uo.customer',					array('pref' => 'id', 'payment' => 'type'))
					
				->joinLeft(array('uip2' => 'users_invoice_preferences'),
					'uip2.user = uo2.customer',					array('pref2' => 'id', 'payment2' => 'type'))
					
				->where('uo.invoiceable = ?', 0)
				->where('uo.role = ?', 'normal')
				->where('uo2.invoiceable = ? OR uo2.id IS NULL', 0)
				->order('uo.order ASC');
					
			foreach($db->fetchAll($select) as $item){
				if ($item['bankaccount'] && !$item['pref'] && $item['pref2']){
					
					$row = $uip->fetchRow($uip->select()->where('id = ?', $item['pref2']));
					$row->user = $item['customer'];
					// $row->save();
					
					$uoid = $item['uoid'];
					
					p('voorkeuren van tweede naar eerste gebruiker');
				} else if ($item['bankaccount2'] && !$item['pref2'] && $item['pref']){
					
					$row = $uip->fetchRow($uip->select()->where('id = ?', $item['pref']));
					$row->user = $item['customer2'];
					// $row->save();
					
					$uoid = $item['uo2id'];
					p('voorkeuren van eerste naar tweede gebruiker');
				} else if ($item['bankaccount'] && !$item['pref'] && $item['pref2']){
					
					$row = $uip->fetchRow($uip->select()->where('id = ?', $item['pref2']));
					$row->user = $item['customer'];
					// $row->save();
					
					$uoid = $item['uoid'];
					p('test voorkeuren van eerste naar eerste gebruiker');
				} else if ($item['bankaccount'] && $item['payment'] == 'ideal' && $item['payment2'] == 'collection'){
					$row = $uip->fetchRow($uip->select()->where('id = ?', $item['pref2']));
					$row->user = $item['customer'];
					// $row->save();
					
					$uoid = $item['uoid'];
					
					p('voorkeuren van tweede naar eerste gebruiker');
				} else if (!$item['bankaccount'] && !$item['bankaccount2']){
					
					$row = $uip->createRow();
					$row->user = $item['customer'];
					$row->address = $item['address'];
					$row->rate = $item['type'] == 'particulier' ? 1 : 3;
					$row->type = 'ideal';
					$row->bill = 'email';
					$row->finalbill =  0;
					// $row->save();	
					
					p('nieuwe voorkeuren gebruiker');
					$uoid = $item['uoid'];		
				} else if ($item['bankaccount'] && $item['pref']){
					$uoid = $item['uoid'];		
					p('voorkeuren van eerste gebruiker behouden');			
				} else if ($item['bankaccount2'] && $item['pref2']){
					$uoid = $item['uo2id'];					
					p('voorkeuren van tweede gebruiker behouden');
				}

				$row = $uo->fetchRow($uo->select()->where('id = ?', $uoid));
				$row->invoiceable = true;
				
				$gebruiker = $uoid == $item['uoid'] ? 'Eerste' : 'Tweede';
				p('Facturen naar ' . $gebruiker . '.');
				//$row->save();	
			}
		}
		
		
		/*
		 * @ACL
		 */
		public function invalidIpAction() {

            $this->addMinify('css', 'css_new');
            $this->addMinify('js', 'js_new');

		    if (!$this->_getParam('user'))
				die();
			
			$form = new Form($this, 'user/invalidip');

			if($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
				$data = $form->getValues();
	
				$ips = new Ips();
				$row = $ips->createRow();
				$row->ip = $_SERVER['REMOTE_ADDR'];
				$row->request_user = $this->_getParam('user');
				$row->description = $data['description'];
				$row->save();

				$u = new User();
				$user = $u->getUsers($this->_getParam('user'));

				new EmailOutgoing([
					'to' => 			$user[0]['id'],
					'template' => 		'invalid-ip.phtml',
					'subject' =>		'Admin login IP toegang',
					'user' =>			$user[0],
					'description' =>	$data['description'],
					'data' => $user[0]
				]);
				$this->view->posted = true;
			}

			$this->view->form = $form;

		}

		public function toggleErrorNotifyAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (!$this->login->id || !$this->_getParam('id') || !$this->_getParam('set'))
				die();

			$ec = new ErrorsOccurences();

			$row_user = $ec->fetchRow($ec->select()->where('id = ?', $this->_getParam('id')));

			if ($row_user) {
				$row_user->notify = $this->_getParam('set') == 'true' ? 1 : 0;
			}
			$row_user->save();
		}

		public function errorNotifyAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (!$this->login->id || !$this->_getParam('id') || !$this->_getParam('set'))
				die();

			$e = new Errors();
			$eo = new ErrorsOccurences();
			$u = new User();

			$error = $e->fetchRow($e->select()->where('id = ?', $this->_getParam('id')));
			$error->fixed = 1;
			$error->save();

			$users = $eo->fetchAll($eo->select()->where('error = ?', $this->_getParam('id'))->where('notify = 1'));

			foreach ($users as $occurence) {
				$u->user = $occurence['user'];
				$user = $u->getProfile();

				new EmailOutgoing(array(
					'to' => 			$u->user,
					'template' => 		'errorNotify.phtml',
					'subject' =>		'Storing opgelost',
					'user' =>			$user,
					'occ' =>			$occurence,
					'error' =>			$error
				));
			}
		}

		public function checkUsernameAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (!$this->_getParam('username'))
				die();

			$user = new User();
			ob_clean();
			echo json_encode($user->checkUsername($this->_getParam('username')));
		}

		/**
		 * Profile
		 */
		public function profileAction() {

			if (loginManager::data()->info['profile_completion'] == '0' && loginManager::data()->info['first'] == '1' && Settings::get('modules_tenantLogin_contract_validating')) {
            	$this->_helper->redirector('contract', 'user');
        	}

			$this->view->headScriptHashed()->appendFile('media/javascript/iban_to_bic.js');

			$rights = loginManager::data()->rights;
			$rights = $rights == 'company'? 'tenant' : $rights;

			if ($rights == 'admin') {
				$this->view->Breadcrumbs()->addCrumb('Profiel aanpassen');
			}

            if($rights === 'registrant'){
                $this->view->headScriptHashed()->appendFile('media/javascript/user/profile/second_registrant.js');
            }

			// user data
			$profile = new User();
			$o = new Object();
			
			$profile->user = loginManager::data()->id;

			if ($rights == 'registrant') {
                $this->userprofile = $userprofile = loginManager::data()->info['commercial'] === false ? $profile->getProfile(false, loginManager::data()->info['object'] ) : $profile->getCommercialProfile();
            } else {
                $this->userprofile = $userprofile = loginManager::data()->info['commercial'] === false ? $profile->getProfile() : $profile->getCommercialProfile();
            }

			if (acl()->hasRole('admin') || acl()->hasRole('internal')){
				$type = 'admin';
			}
			else {
				$type = $rights == 'tenant' ? $userprofile['other']['type'] : $rights;
			}
			
			$type = $type ? $type : 'particulier';
			
			#TODO hier betere manier voor bedenken
			if($this->_getParam('type') && (acl()->isAllowed($role, 'rights:view', null, $caller) || DEBUGGING))
				$type = $this->_getParam('type');

			//create form
			$profileform = new Form($this, 'user/profile/' . $type);
			

			try {
				if(loginManager::data()->info['uo'] > 0){
					$uo = new Objectusers();
					$uo = $uo->fetchRow($uo->select()->where('id = ?', loginManager::data()->info['uo']));
				} else {
					$uor = new UsersObjectsRegister();
					$row = $uor->fetchRow($uor->select()->where('user = ?', loginManager::data()->info['id']));
				}
			}
			catch (Exception $e){
				$uo = 0;
			}


			if (loginManager::data()->rights == 'company') {

				$disabledFieldsTranslate = [
					'legal_company' => 'user',
					'legal_contact' => 'general',
					'contract_date' => 'start',
					'address_invoice' => 'invoiceing',
					'contact_complaint' => 'emergency',
					'payment_info' => 'invoice'
				];

				$disabledFields = []; 
				$white_list = [];
 				
 				$options = json_decode(Project::getPPChangeOptions(loginManager::data()->project['id']), true);

 				foreach($disabledFieldsTranslate as $option_name => $field_name)
	  				if(in_array($option_name, $options))
	  					$disabledFields[] = $field_name;

				foreach($disabledFields as $subForm) {
					$profileform->$subForm->addAttribs(['class' => 'hidden']);
					foreach ($profileform->$subForm as $fieldName => $item) {
						$item->setIgnore(true);
						$item->setRequired(false);
						$item->setAttrib('disabled', true);
					}
				}
			}


            $u_model = new Users();

			if ($rights == 'tenant'){ #TODO dit moet omgezet worden naar ACL?
				if (loginManager::data()->info['first'] == 0){

					// dont allow contracting data to be changed
					$disabledFields = [
						'general' => [
							'gender',
							'initials',
							'firstname',
							'middlename',
							'name',
							'bdate',
							'birthplace'
                        ],
						'start' => [
							'date'
                        ]
                    ];


					foreach($u_model->getPartnerUserFields() as $partnerUserField){
						$partner_subform_name = $u_model->getPartnerSubformName($partnerUserField, 'tenant');
                        $disabledFields[$partner_subform_name] = $disabledFields['general'];
					}
					
					
				} elseif (loginManager::data()->info['first'] == 1 && $userprofile['start']['date']){
					$disabledFields = array(
						'start' => array(
							'date',
							'enddate',
						),
						/*array('start', 	'date'),
						array('start', 	'enddate'),*/
					);	
					
					if($uo->object)
						if($period = Object::getPeriod($uo->object) && !$userprofile['start']['enddate'])
							$userprofile['start']['enddate'] = date('d-m-Y', strtotime('+' . $period . ' months', strtotime($uo->from)));
				}

				if(loginManager::data()->info['first'] == 0 || (!Settings::get('modules_tenantLogin_nameplate_email'))){
                    $disabledFields['nameplate'] = ['nameplate'];
				}

                $hiddenFields = [];


                $hiddenFieldsTranslate = [
                     'contract_date' => 'start',
                     'payment_info' => 'invoice'
                ];




				$options = json_decode(Project::getPPChangeOptions(loginManager::data()->project['id']), true);

                foreach($hiddenFieldsTranslate as $option_name => $field_name)
                    if(in_array($option_name, $options))
                        $hiddenFields[] = $field_name;

                foreach($hiddenFields as $subForm) {
                    $profileform->$subForm->addAttribs(['class' => 'hidden']);
                    foreach ($profileform->$subForm as $fieldName => $item) {
                    	//p($item);
                        $item->setIgnore(true);
                        $item->setRequired(false);
                        $item->setAttrib('disabled', true);
                    }
                }


				if($disabledFields) {
					foreach($disabledFields as $subForm => $fields){
						foreach ($fields as $fieldName) {
							if (isset($profileform->$subForm->$fieldName)) {
								$field =& $profileform->$subForm->$fieldName;
								$field->setIgnore(true);
								$field->setRequired(false);
								$field->setAttrib('disabled', true);
							}
							/*if($profileform->$field[0]->$field[1]){
								$profileform->$field[0]->$field[1]->setIgnore(true);
								$profileform->$field[0]->$field[1]->setRequired(false);
								$profileform->$field[0]->$field[1]->setAttrib('disabled', true);
							}*/
						}
					}
				}
			}

			$should_be_saved_values = [];

			//Check form
			if($post = $this->getRequest()->getPost()) {

                if (is_numeric($this->cleanCell($post['general']['phone_home'])))
                    $profileform->general->phone_home->setRequired(false);

                $extra_tenant_subforms = [];
                $u_model = new Users();
                foreach($u_model->getPartnerUserFields() as $partnerUserField){
                    foreach($u_model->getPartnerSubforms($partnerUserField) as $partner_subform){
                        $extra_tenant_subforms[] = $partner_subform;
					}
				}

                foreach($extra_tenant_subforms as $subform_name) {
					if(null !== $profileform->getSubForm($subform_name)) {
						$required = $profileform->makeDependableSubformRequiredFields(
							$profileform->$subform_name,
							is_array($post[$subform_name]) ? $post[$subform_name] : []
						);
						if(isset($profileform->$subform_name->should_be_saved)) {
							$should_be_saved_values[$subform_name] = $required;
						}
					}
				}

            }

			if ($this->getRequest()->isPost() && $profileform->isValid($this->getRequest()->getPost())){


				$data = $profileform->getValues();

                // quick sanitation
				foreach ($data as &$formPart) {
				    foreach ($formPart as $itemName => &$itemValue) {
				        if ($itemName === 'password') {
				            continue;
                        }

                        $itemValue =  htmlspecialchars($itemValue, ENT_QUOTES);
                    }
                }



				foreach($data as $subform_name => $subform)
					if(isset($should_be_saved_values[$subform_name]))
						$data[$subform_name]['should_be_saved'] = $should_be_saved_values[$subform_name];

 				if ($type == 'commercieel' && $rights == 'tenant') {
					if($uo && !empty($uo->id)) {
						$data['uoid'] = $uo->id;
					}
					$profile->editCommercial( $data, $uo->object );
				} else {
					$profile->edit($data, $rights);
				}

				if ($rights == 'tenant') {

					if ($this->firstLogin && ($uo->contract == 0 || $uo->digital == 1)) {
						if($this->hasCell($data['general']['phone_home']))
							$this->_redirect('user/contract');
						else 
							$this->_redirect('user/cellular', array('redirect' => 'contract'));
					}
					else {
						$profile->tenantChanged($data, $userprofile, $type , $user = $profile->getProfile());
						$user = $profile->getProfile();
						$profile->endFirst();
                        $this->testContracting(false);
						if($this->hasCell($data['general']['phone_home']))
							$this->_redirect('user/contract');
						else 
							$this->_redirect('user/cellular', array('redirect' => 'dashboard'));
					}
				}
				elseif ($rights == 'admin') {
					$this->_redirect('index/index');
				}

				if ($rights == 'registrant') {
                    $this->_redirect('user/dashboard');
				}
			}


			foreach($userprofile as $subform => $fields){
				if(isset($userprofile[$subform]['email']))
					$userprofile[$subform]['emailcheck'] = $userprofile[$subform]['email'];
			}

			$userprofile['tenantinfo']['adults'] = 1;
 			$profileform->setDefaults($userprofile);
			
			if ($this->getRequest()->isPost())
 				$profileform->populate($this->getRequest()->getPost());



			//view
			$max_occupants = $userprofile['other']['max_occupants'] === null ? (isset($userprofile['second_tenant']) ? 2 : 1) : $userprofile['other']['max_occupants'];

			$this->view->meters = $uo->object ? $o->getMeters($uo->object, false, 0, $userprofile['start']['date']) : array();

			$this->view->meterdoc = $profile->getMeterDocs($userprofile['other']['project']);
            if ($userprofile['other']['project']) {
                $this->view->project = Project::getDetails($userprofile['other']['project']);
            }

 			$this->view->max_occupants = $max_occupants;
 			$this->view->userprofile = $userprofile;
            $this->view->hasSecondTenant = $userprofile['second_tenant']['id'] > 0;

			$this->view->profile = $profileform;
			$pModel = new Projects();
			if(intval($userprofile['other']['project']) > 0 && $project = $pModel->getById($userprofile['other']['project']))
				$this->view->tenant_page_collection_payment = $project->tenant_page_collection_payment;
			
			//$this->setTabsInView();
			//$this->view->activeTab = "login";
			
			$this->render('profile/' . $type);
		}


        public function confirmCellPhoneAction()
        {
			$model = new \Controllers\User\ConfirmCellPhone();

			if($this->getParam('confirm') === '1') {
                $this->view->status = 'sms_send';

				$model->sendCode(
					loginManager::data()->id
				);
			} else {
                if($_POST){
                    $result = $model->confirmCode(
                        loginManager::data()->id,
                        $_POST['code']
                    );

                    if($result === false) {
                        $this->view->status = 'failed';
                    }
                }

                if($model->isConfirmed(loginManager::data()->id)){
                    $this->view->status = 'success';
                }
			}

		}

		public function testCodeAction(){
			$this->disableView();

			$select = db()->select()
				
				->from(array('u' => 'users'), array('id'))

				->joinLeft(array('uo' => 'users_objects'), 'uo.customer = u.id', false)
				
				->where('uo.id IS NOT NULL')
				->order('uo.from');

			foreach(db()->fetchAll($select) as $user)
				User::getNumber($user['id'], true);
		}


		public function updateCommentAction(){
			$this->disableView();

			$select = db()->select()
				->from(array('u' => 'users'), array('id','comment'))
				->where('u.comment != ?', '');

			foreach(db()->fetchAll($select) as $user)
				User::insertSupportNote($user['id'], $user['comment']);
		}
		
		public function cellularAction(){
			$form = new Form($this, 'user/cellular');

			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
				$data = $form->getValues();

				$a = new Address();

				$row = $a->fetchRow($a->select()->where('type_id = ?', loginManager::data()->id)->where('type = ?', 'user'));

				if(!$data['skip'] && $this->hasCell($data['phone'])){
					$row = $row ? $row : $a->createRow();

					$row->phone2 = $this->cleanCell($data['phone']);

					$row->save();
					$this->_redirect('user/' . $this->_getParam('redirect'));
				} elseif($data['skip']){
					if($row){
						$row->phone2 = '';
						$row->save();
					}

					$this->_redirect('user/' . $this->_getParam('redirect'));
				} else {
					$this->view->showError = true;
				}

			}

			$this->view->form = $form;
		}
		
		public function hasCell($value){
			$value = $this->cleanCell($value);

			if($this->view->language == "nl"){
                return strlen($value) > 9;
				//return (strpos($value, '06') === 0 && strlen($value) == 10);
			} else {
				return strlen($value) > 6;
			} 
		}

		public function cleanCell($value){
			return $this->view->language == "nl" ? preg_replace('/\D/', '', $value) : trim($value);
		}
		
		
		public function resetTestUserAction(){
			$this->disableView();
			
			$user = new User();
			if (!$this->_getParam('id') || $this->view->pageInfo['host'] != 'develop.wennet.nl')
				die();
				
			$user->resetTestUser($this->_getParam('id'));
		}

		public function invertIncorrectContractAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$db = db();
			$d = new SupportDocuments();
			$u = new User();

			$select = $db->select()
				->from(array('d' => 'support_documents'), array('id'))
				->joinLeft(array('uo' => 'users_objects'),
				'uo.contract_id = d.id', array('uoid' => 'id', 'contract'))
				->joinLeft(array('u' => 'users'),
				'u.id = uo.customer', array('gender', 'initials', 'firstname', 'middlename', 'name'))
				->joinLeft(array('a' => 'address'),
				'a.type_id = uo.customer AND a.type = "user"', array('email', 'phone1', 'phone2'))
				->joinLeft(array('d2' => 'support_documents'),
				'd2.type = \'user\' AND
					d2.map_to = uo.customer AND
					d2.id != d.id', array('d2id' => 'id'))
				->where('d.date > ?', '2010-09-02 11:00:00')
				->where('d.type = ?', 'user');

			foreach ($db->fetchAll($select) as $item) {
				echo '<pre>';
				print_r(array(
					'fullname' => $u->buildname($item),
					'email' => $item['email'],
					'phone' => $item['phone1'],
					'phone2' => $item['phone2'],
				));

				//$d->delete('id = "' . $item['id'] . '"');
				}
		}

		public function invoiceAction() {
			$user = new User();
			$transaction = new Transaction();
			$invoicerun = new InvoiceRun();
			$invoice = new Invoice();
			$profile = new Usage();

			$this->view->Breadcrumbs()->addCrumb('Facturatie voorkeuren');

			$overrides = $profile->getOverrides($this->_getParam('id'));

			$this->view->user = $this->_getParam('id');
			$this->view->overrides = $overrides['items'];
			$this->view->override = $overrides['current'];
			$this->view->profile = /*!$this->view->override ?*/ new StringFormat(tax()->get()->setAmount($invoicerun->defaultTotal($this->_getParam('id')))->add(), 'money') /*: false*/;
			$this->view->remaining = new StringFormat($transaction->fetchRemaining($this->_getParam('id')) / 100, 'money');
		}

		public function arrangementAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Facturatie voorkeuren', 'user/invoice/id/' . $this->_getParam('id') . '/')
				->addCrumb('Betalingsregeling toevoegen of bewerken');

			//create form
			$form = new Form($this, 'user/arrangement');
			$user = new User();
			$transaction = new Transaction();
			$remaining = $transaction->fetchRemaining($this->_getParam('id'));

			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
				$this->disableView();
				$data = $form->getValues();
				$data['remaining'] = $remaining;
				
				$user->addArrangement($this->_getParam('id'), $data);
				$this->_redirect('user/invoice', array('id' => $this->_getParam('id')));
			}

			$this->view->user = $this->_getParam('id');
			$this->view->remaining = $remaining;
			$form->populate($user->getArrangement($this->_getParam('id')));
			$this->view->form = $form;
		}

		public function overrideAction() {
			$invoicerun = new InvoiceRun();
			$profile = new Usage();

			$this->view->Breadcrumbs()
				->addCrumb('Facturatie voorkeuren', 'user/invoice/id/' . $this->_getParam('id') . '/')
				->addCrumb('Voorschot bedrag aanpassen');

			$form = new Form($this, 'user/override');
			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
				$this->disableView();

				$data = $form->getValues();
				$profile->addOverride($this->_getParam('id'), $data);
				$this->_redirect('user/invoice', array('id' => $this->_getParam('id')));
			}

			$this->view->override = new StringFormat($profile->getOverride($this->_getParam('id')) / 100, 'money');

			$form->populate(array('override' => $this->view->override));
			
			$this->view->defaultTotal = new StringFormat($invoicerun->defaultTotal($this->_getParam('id')), 'money');
			$this->view->user = $this->_getParam('id');
			$this->view->form = $form;
		}

		public function migrateAction() {

			$u = new User();
			$o = new Object();
			$c = new Consumption();
			$u->user = loginManager::data()->id;
			$us = new Usage();
			$address = $u->getCurrentAddress();

			if ($address['till'] ) {
				$this->render('migrate/done');
				return;
			} else if (!$address['uoid']) {
				$this->render('migrate/no');
				return;
			}
/*
			 $meters = $o->getMeters($address['object']);
			 		
			 foreach((array) $meters as $i => $meter){
				 if(!$meter)
				 	continue;
	
				 $meters[$i]['consumption'] = $c->getLatest($meter['meterid']);
				 	
				 $usage = $us->getUsage($meter['usageprofile'], $address['object']);
				 	
				 $meters[$i]['usage'] = $usage[$meter['meterproduct']][$meter['meterunit']] ? $usage[$meter['meterproduct']][$meter['meterunit']]['value'] : false;
				 $meters[$i]['estimate'] = $c->estimate($meters[$i]['consumption']['value'], $meters[$i]['consumption']['date'], $meters[$i]['usage']);
			 }
			 $this->view->meters = $meters;
			 */

			if ($address['uoid']) {
				if ($this->_getParam('step') == 'form') {
					$form = new Form($this, 'user/migrate');

					if ($address['ownership'] != 'buy')
						foreach ($form->nextTenant as $i => $item)
							unset($form->nextTenant->$i);

					$form->nextTenant->name->setRequired = false;

					if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
						$data = $form->getValues();

						$u->migrate($address['uoid'], $data);
						$this->render('migrate/done');
					}

					$this->view->data = $address;
					$this->view->form = $form;
					$this->render('migrate/form');
				}
				else {
					$this->render('migrate/' . $address['ownership']);
				}
			}
			else {
				$this->_redirect('user/dashboard');
			}
		}

		public function adminListAction() {
            $this->view->Breadcrumbs()
                ->addCrumb('Gebruikers');

            $data = Users::getInternalUsers();

            $types = [
                'name' => ['title' => translate()->_('surname'), 'width' => 'medium truncate'],
                'firstname' => ['title' => translate()->_('firstname'), 'width' => 'medium truncate'],
				'rendered_name' => ['title' => ucfirst(translate()->_('employee')), 'width' => 'xxlarge truncate'],
                'shortname' => ['title' => ucfirst(translate()->_('shortname')), 'width' => 'xxsmall truncate'],
                'dept_name' => ['title' => ucfirst(translate()->_('afdeling')), 'width' => 'small truncate'],
				'only_inspections_user' => ['title' => 'Insp', 'width' => 'xxsmall'],
            ];

            if (in_array(\loginManager::data()->rights, ['admininternal', 'admin'], true)) {
                $types['GA_timeout'] = ['title' => 'Tweefactorauthenticatie', 'width' => 'large'];
            }

			$this->view->extraWidth = true;

			$this->view->listView($data)
                ->setTypes($types)
                ->addFormat('only_inspections_user', 'nullable_bool')
				->setFilters(array(
                    'name' => ['type' => 'input'],
                    'firstname' => ['type' => 'input'],
                    'shortname' => ['type' => 'input'],
                	'dept_name' => ['renderSeparately' => true, 'title' => 'Afdeling', 'order_by_title' => 'ASC'],
					'only_inspections_user' => ['renderSeparately' => true, 'title' => 'InspectieApp gebruiker', 'order_by_title' => 'ASC']
                ))
				->addFormat('GA_timeout', function ($value, $item) {
					return "<a class='button GA_timeout' href='#' target='_blank'><i class='fa fa-user-shield' style='padding-right: 5px;'></i></a>
					<span hintOffset='{x:-25, y: 0}' title='Zet een timeout van 15 minuten op de tweefactorauthenticatie van deze gebruiker.'>Tijdelijk uitzetten</span>";
				})
                ->addButtons([
					'edit' => 'Bewerken',
                	'delete' => 'Inactief',

                ])
                ->render($this);

		}

		public function adminTimeoutGoogleAction()
        {
			$this->disableView();

            if (!in_array(\loginManager::data()->rights, ['admininternal', 'admin'], true)) {
                throw new RuntimeException('Time out 2FA is not allowed by user');
            }


			$userId = $this->_getParam('id');
			if (!$userId) { echo "Geen gebruiker ID opgegeven."; return; }

			$EnableTimeOutService = new EnableTimeOutService();
			$time = $EnableTimeOutService->execute($userId);

			if ($time) {
				echo 'De Google Authenticator voor deze gebruiker is nu gedeactiveerd tot ' . $time . '.';
			} else {
				echo 'Gebruiker niet gevonden.';
			}
		}

		public function adminEditAction() {
            $this->renderWithNewLayout();

			$userModel = new User();
			$form = new Form($this, 'user/adminEdit');
			$this->view->Breadcrumbs()
				->addCrumb('Gebruikers', 'user/admin-list/')
				->addCrumb('Gebruiker bewerken/toevoegen');
			
			$this->view->editPassword = false;
			if (loginManager::data()->rights !== 'admin') {
				$form->user->type->removeMultiOption('admin');
			}

			if ($userId = $this->_getParam('id')) {
				$user = array_shift($userModel->getUsers($userId));
				unset($user['user']);

				$form->user->username->setAttrib('disabled', true);
                $form->user->username->setValidators([]);

				$form->populate($user);
				
				$this->view->userId = $userId;
				
				if (loginManager::data()->id == $userId)
					$this->view->editPassword = true;
			} else {
				$user = array();
			}

			$twoFactorAuthenticationService = new TwoFactorAuthenticationService();

			if ($this->getRequest()->isPost()) {
				$data = $this->getRequest()->getPost();

				if($userId && isset($data['user']['ga_code'])) {
                    try {
                        $twoFactorAuthenticationService->enableTwoFactorAuth($userId, $data['user']['ga_code']);
                    } catch (AuthenticationFailedException $e) {
                        $this->view->is_authentication_error = true;
                    }
				}

				if ($user)
					$data['user']['username'] = $user['username'];
				if ($form->isValid($data)) {
					
					$userData = $data['user'];
					if (
						!isset($userData['password'])
						||
						!$userData['password']
						||
						$userData['password'] == $userData['confirm_password']
					) {
						if ($this->_getParam('id')) {
							$user->user = $this->_getParam('id');
						}

						$formValues = $form->getValues();
						if ($formValues['user']['type'] === 'admin' && loginManager::data()->rights !== 'admin') {
							$this->_redirect('user/admin-list');
						}

						$userId = $userModel->edit($formValues, 'administrator');

						if (!$this->_getParam('id')) {
							$userModel->welcomeMail($userId);
						}
						$this->_redirect('user/admin-list');
					} else {
						$form->user->confirm_password->addError("De wachtwoorden die u heeft ingevuld komen niet overeen");
					}
				}
			}

            $twoFactorViewSetupService = new ViewSetupService();

            // get view variables for setting up a new Google Authenticator
            $this->view->newGAData = $twoFactorViewSetupService->execute($userId);
			$this->view->form = $form;
		}

		public function findAdminUsersAction(){
			$this->disableView();
			$user = new User();

			$admin_users = $user->getUsers(false, acl()->getInternalRights());
			$matches = [];
			foreach($admin_users as $admin_user){
				if(stripos($admin_user['fullname'], $this->_getParam('value')) !== false)
					$matches[$admin_user['id']] = ['id' => $admin_user['id'], 'name' => $admin_user['fullname']];
			}

			echo json_encode($matches);

		}

		public function adminDeleteAction() {
			$this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');
			$this->userIdForForm = $this->_getParam('id');

			$userModel = new User();
			$form = new Form($this, 'user/adminDelete');

			$this->view->Breadcrumbs()
				->addCrumb('Gebruikers', 'user/admin-list/')
				->addCrumb('Gebruiker inactief maken');

			if ($userId = $this->_getParam('id')) {
				$user = Users::get($userId);
				$user = !empty($user) && is_object($user)? $user->toArray(): [];

				$form->populate($user);

				$userOptions = [];
				foreach ($userModel->getUsers(false, acl()->getInternalRights()) as $user)
					if($user['id'] !== $userId && strtotime($user['enddate']) === false)
						$userOptions[$user['id']] = User::buildname(false, $user['id']);

				$form->admin_delete_form->inheritor->setMultiOptions($userOptions);

				$this->view->userId = $userId;

			} else {
				$user = array();
			}
			
			if ($this->getRequest()->isPost()) {
				$data = $this->getRequest()->getPost();

				if(intval($data['admin_delete_form']['id']) > 0 && $userId = intval($data['admin_delete_form']['id']))
					$user = Users::get($userId);

				if ($form->isValid($data) && !empty($user) && is_object($user)) {
					$enddate = $data['admin_delete_form']['enddate'];
					$enddate = strtotime($enddate);

					if($enddate === false)
						$this->_redirect('user/admin-delete', ['id' => $userId]);

					$inheritor = $data['admin_delete_form']['inheritor'];
					$inheritor =  Users::get($inheritor);

					if(empty($inheritor) || !is_object($inheritor) || $inheritor->id == $user->id)
						$this->_redirect('user/admin-delete', ['id' => $userId]);

					$user->enddate = date('Y-m-d', $enddate);
					$user->save();

					$tModel = new TasksTable();
					$eModel = new Emails();
					$complModel = new SupportComplaints();
					$saModel = new SupportAssign();

					try { // this is to maintain functionality for installations without the proper database tables 
						$cRentalModel = new ContractsRental();

						$cRentals = db()->fetchAll( db()->select()
							->from(['cr' => 'contracts_rental'], ['id'])
							->where('account_manager = ?', $user->id)
						);

						foreach ($cRentals as $cRental) {
							$cRen = $cRentalModel->getById($cRental['id']);
							$cRen->account_manager = $inheritor->id;
							$cRen->save();
						}
					} catch (Exception $e) {}


					try { // this is to maintain functionality for installations without the proper database tables
						$cServiceModel = new ContractsService();

						// replace responsible_commercial
						$cServices = db()->fetchAll( db()->select()
							->from(['cr' => 'contracts_service'], ['id'])
							->where('responsible_commercial = ?', $user->id)
						);

						foreach ($cServices as $cService) {
							$cServ = $cServiceModel->getById($cService['id']);
							$cServ->responsible_commercial = $inheritor->id;
							$cServ->save();
						}

						// replace responsible_financial
						$cServices = db()->fetchAll( db()->select()
							->from(['cr' => 'contracts_service'], ['id'])
							->where('responsible_financial = ?', $user->id)
						);

						foreach ($cServices as $cService) {
							$cServ = $cServiceModel->getById($cService['id']);
							$cServ->responsible_financial = $inheritor->id;
							$cServ->save();
						}

						// replace responsible_technical
						$cServices = db()->fetchAll( db()->select()
							->from(['cr' => 'contracts_service'], ['id'])
							->where('responsible_technical = ?', $user->id)
						);

						foreach ($cServices as $cService) {
							$cServ = $cServiceModel->getById($cService['id']);
							$cServ->responsible_technical = $inheritor->id;
							$cServ->save();
						}
					} catch (Exception $e) {}

					try { // this is to maintain functionality for installations without the proper database tables
						$cTransientModel = new ContractsTransient();

						$cTransients = db()->fetchAll( db()->select()
							->from(['ct' => 'contracts_transient'], ['id'])
							->where('account_manager = ?', $user->id)
						);

						foreach ($cTransients as $cTransient) {
							$cTran = $cTransientModel->getById($cTransient['id']);
							$cTran->account_manager = $inheritor->id;
							$cTran->save();
						}
					} catch (Exception $e) {}

					$settingsNames = ['task_mail_weekly_management_overview','invoice_xml_export_users',
						'bankimport_report_users','voyanta_report_recipients'];

					foreach ($settingsNames as $settingName) {
						$addInheritor = false;
						$settingUsers = (array) Settings::get($settingName);

						foreach ($settingUsers as $key => $settingUser){
							if($settingUser === $user->id){
								$settingUsers[$key] = $inheritor->id;
							}
						}

						Settings::set($settingName, $settingUsers);
					}

					$ca_responsible_employee = Settings::get('ca_responsible_employee');
					if($ca_responsible_employee == $user->id)
						Settings::set('ca_responsible_employee', $inheritor->id);

					$tasks = $tModel->getList([
						'user' 			=> $user->id,
						'completed'		=> '0',
					]);

					foreach ($tasks as $task) {
						$t = $tModel->getById($task['id']);
						$t->user = $inheritor->id;
						$t->save();
					}

					$emails = $eModel->getList([
						'user' 			=> $user->id,
						'archived'		=> '0',
					]);

					foreach ($emails as $email) {
						$e = $eModel->getById($email['id']);
						$e->user = $inheritor->id;
						$e->save();
					}

					$complaints = $complModel->getList([
						'last_status' 	=> [0,1,2,3],
					]);

					foreach ($complaints as $complaint) {
						$assigns = $saModel->getList([
							'type' => 'complaint',
							'map_to' => $complaint['id'],
							'user' => $user->id,
						]);

						foreach ($assigns as $ass) {
							$assign = $saModel->getById($ass['id']);
							$assign->user = $inheritor->id;
							$assign->save();
						}
					}

					// complaint categories default assignee
					$ccModel = new ComplaintCategories();
					$ccRows = $ccModel->matchAll(['default_assignee' => $user->id]);
					foreach ( $ccRows as $ccRow ) {
						$ccRow->default_assignee = $inheritor->id;
						$ccRow->save();
					}

					$this->_redirect('user/admin-list');
				}
			}

			$this->view->form = $form;
		}

		public function deleteOverrideAction() {
			$profile = new Usage();

			$profile->deleteOverride($this->_getParam('id'));

			$this->disableView();
			$this->_redirect('user/invoice', array('id' => $this->_getParam('id')));

		}

		public function contactDateAction() {
			$this->disableView();
			
			$post = $this->getRequest()->getPost();
		
			if(!is_numeric($post['user']) || strtotime($post['value']) === false)
				die(json_encode(false));
				
			$u = new Users();
			
			if($row = $u->fetchRow($u->select()->where('id = ?', $post['user']))){
				
				$row->last_report_contact = date('Y-m-d', strtotime($post['value']));
				$row->save();

				echo json_encode($this->view->RelativeDate(strtotime($row->last_report_contact)));
				
			} else {
				die(json_encode(false));
			}
			
						
		}
		
		public function noContactAction() {
			$this->disableView();
			$post = $this->getRequest()->getPost();
			
			//$u = new Users();
			$note = new SupportNotes();
			$unc = new UsersNoContact();
			
			$userId    = $post['user'];
			$type      = $post['type'];
			$noContact = $post['no_contact'];
			
			$query = $unc->select()->where('userid = ?', $post['user'])->where('type = ?', $post['type']);
			if ($row = $unc->fetchRow($query)) {
				$row->no_contact = $noContact;
				$res = $row->save();
			} else {
				$unc->insert(array(
					'userid'     => $userId,
					'type'       => $type,
					'no_contact' => $noContact,
				));
			}
			
			/*
			switch($type) {
				case 'phone':
					$typeLabel = "telefoonnummer";
					break;
				case 'email':
					$typeLabel = "e-mail adres";
					break;
				case 'bankaccount':
					$typeLabel = "rekeningnummer";
					break;
				case 'collection':
					$typeLabel = "automatische incasso";
					break;
			}
			*/
			
			$typeLabel = $unc->typeLabels[$type];
			
			if ($noContact) {
				$title = "Wenst geen contact ivm {$typeLabel}";
				$message = "Voor deze gebruiker staat geen {$typeLabel} in het systeem. Hij/zij wenst hier geen correspondentie over te ontvangen.";
			} else {
				$title = "Mag weer gecontacteerd worden over {$typeLabel}";
				$message = "Voor deze gebruiker staat geen {$typeLabel} in het systeem. Hij/zij is weer bereid om hier correspondentie over te ontvangen.";
			}
			
			$note->insert(array(
				'user'     => loginManager::data()->id,
				'map_to'   => $userId,
				'title'    => $title,
				'message'  => $message,
				'category' => 2, // category 2: Administratie
				'warning'  => true,
				'type'     => 'user',
			));
			
			echo json_encode(array('ok' => true));
		}

		public function exportAction() {
			$this->view->extraWidth = true;
			
			$this->view->Breadcrumbs()->addCrumb('Gebruikers zoeken', 'user/export/');
			$user = new User();
			$params = array();

			if ($this->isAjaxRequest) {
				echo Zend_Json::encode($user->searchUsers($this->getRequest()->getPost()));

				$this->disableView();
			}
			elseif ($this->_getParam('download')) {
				$this->disableView();

				$data = $user->searchUsers($this->getRequest()->getQuery());

				if ($this->_getParam('download') == 'csv') {
					header('Content-type: application/vnd.ms-excel');
					header('Content-disposition: attachment; filename=user_export.csv');
					
					$this->view->users = $data;
					
					$this->render('export_csv');
				}
				elseif ($this->_getParam('download') == 'email') {
					header('Content-type: text/plain');
					header('Content-disposition: attachment; filename=user_export.txt');

					foreach ($data as $user) {
						if ($user['email'] == '')
							continue;

						echo $user['email'] . (end($data) != $user ? ';' : '');
					}
				}
				elseif ($this->_getParam('download') == 'pdf') {
					$this->disableView();

					$pdf = new Pdf();

					include('application/views/scripts/partials/pdftemplates/styles.phtml');
					include('application/views/scripts/partials/pdftemplates/export.phtml');

					$pdf->addStationery($invoice, 'headerOnly');

					$pdf->browseroutput('user_export.pdf');
				}
				elseif ($this->_getParam('download') == 'print') {
					$this->view->data = $data;
					$this->render('exportprint');
				}

			}
			else {
				$this->view->data = array();
			}
		}

		public function welcomeAction() {

			$user = new User();
			$user->user = loginManager::data()->id;
            $this->view->actionHelper = new Controllers\User\Contract();
			$this->view->profile = $user->getProfile();

			if(loginManager::data()->rights == 'company' && loginManager::data()->info['company_type']['technical_contact'] === '1') {
				$this->_helper->redirector('dashboard', 'company');
			} elseif(loginManager::data()->rights == 'call_center'  && loginManager::data()->info['company_type']['call_center'] === '1') {
                $this->_helper->redirector('dashboard', 'company');
            } elseif (loginManager::data()->info['profile_completion'] == '0' && Settings::get('modules_tenantLogin_contract_validating')) {
                $this->_helper->redirector('contract', 'user');
            } elseif (loginManager::data()->info['profile_completion'] == '9' && !Settings::get('modules_rental_complete_dossier_for_assign')) {
                $this->_helper->redirector('dashboard', 'user');
            } elseif (
                loginManager::data()->info['profile_completion'] == '1' &&
                loginManager::data()->rights == 'registrant'
            ) {
                $this->_helper->redirector('dashboard', 'user');
            } elseif (loginManager::data()->info['profile_completion'] != '9' && loginManager::data()->rights == 'registrant') {
                $this->_helper->redirector('contract', 'user');
            }
            elseif (loginManager::data()->info['profile_completion'] == '2' ) {
                $this->_helper->redirector('contract', 'user');
            } elseif (loginManager::data()->rights !== 'registrant') {

				$this->view->project = Projects::get($this->view->profile['other']['project']);
				$this->view->contracts = $user->getContract();
				$this->testContracting(false);

			}
		}

		public function nocontractingAction () {


		}


		public function anyMissingContractsOnUser($no_actions, $user_id) {

            // only do this function if the login manager has the object id, otherwise function always returns false


                $d = new Document;
                $uo = new Objectusers();
                $uor = new UsersObjectsRegister();

                // user has a users_object_register row, so can't be done contracting
                if(count($uor->matchAll(array('user' => $user_id))) > 0){
                    $has_missing_contract = true;
                } else {

                    // asume the user has no missing contracts
                    $has_missing_contract = false;

                    // loop through the uo rows for this user to find all objects
                    foreach($uo->fetchAll($uo->select()->where('customer = ?', $user_id)->group('object')->group('from')) as $uo_row)
                        $objects[] = $uo_row->object;

                    // if the count of the contracts is smaller then the count of objects
                    if($d->getContract($user_id) < count($objects))
                        $has_missing_contract = true;
                }

                // if the user doesn't have any missing contracts, redirect to dashboard
                if ($has_missing_contract === false)
                    if($no_actions){
                        return false;
                    } else {

                        $u = new User();
                        $u->user = $user_id;
                        $u->endFirst();
                        $this->_helper->redirector('dashboard', 'user');
                    }

		}

		public function testContracting($no_actions = false, $object_id = false, $user_id = false){

            $user_id = $user_id ? $user_id : loginManager::data()->id;
            $object_id = $object_id ? $object_id : loginManager::data()->info['object'];

 			$contracting_neccesary = Project::ContractingAllowed($object_id);
  			if(!$contracting_neccesary){
  				if($no_actions)
					return false;

 				$u = new User();
				$u->user = $user_id;				
				$u->endFirst();
                $this->_helper->redirector('dashboard', 'user');
			}

			//TO DO checken of er voor die taal een contract is ingesteld
			$template = ContractTemplate::getMostRelevant($object_id );


 			$docxOK = !(!isset($template) || !isset($template['document_fullpath']) || strlen($template['document_fullpath']) < 1);


 			//TODO -> TO veranderen naar ingestelde mail account op project ipv algemene mail? sowieso op account baseren.
 			if(!$docxOK) {
				new EmailOutgoing(array(
					'to' => 			array('email' => Settings::get('general_email'), 'name' => 'Klantenservice'),
					'template' => 		'nocontractingpossible.phtml',
					'subject' =>		'Klant kan niet digitaal contracteren omdat geen geldig contract aanwezig is.',
					'object' =>			($object_id ? $object_id : loginManager::data()->info['object']),
				));

				$this->_helper->redirector('nocontracting', 'user');
			}

            if ($user_id > 0 && (loginManager::data()->info['object'] > 0 || is_numeric($object_id) )) {
                return $this->anyMissingContractsOnUser($no_actions, $user_id);
            }

			return true;

		}

		public function showInviteAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$p = new Project();
			$p->showInvitation(loginManager::data()->id);
		}

		public function resignedAction() {
			if (!$this->_getParam('set'))
				die();

			$u = new User();
			$u->user = loginManager::data()->id;

			$u->setResigned($this->_getParam('set') == 'resigned' ? true : false);

			if ($this->_getParam('set') == 'resigned') {
				$u->endFirst();
				$this->_helper->redirector('dashboard', 'user');
			}
			else {
				new EmailOutgoing(array(
					'to' => 			array('email' => Settings::get('general_email'), 'name' => 'Klantenservice'),
					'template' => 		'resigned.phtml',
					'subject' =>		'Klant is niet akkoord met digitaal contracteren',
					'user' =>			$u->getProfile(),
				));
			}
		}

		public function advanceAction() {
			$ratesheetClass = new Ratesheet();
			$usageClass = new Usage();
			$usageProfileClass = new UsageProfile();
			$invoiceClass = new Invoice();

			$ratesheet = $ratesheetClass->getByUser(loginManager::data()->id);

			if ($ratesheet !== false) {
				$usageprofile = $usageProfileClass->getByUser(loginManager::data()->id);

				$ratesheetDetails = $ratesheetClass->getDetails($ratesheet['id'], false, false, true);

				$usage = $usageClass->getUsage($usageprofile);
				$invoiceClass->period['days'] = 1;
				$invoiceClass->usage($ratesheetDetails, $usage, $size = 1);

				$this->view->total = new StringFormat($invoiceClass->calcTax('add', $invoiceClass->total), 'money');
			}
			else {
				$this->view->total = false;
			}
		}
/*
		public function emaillistAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$user = new User();
			$user->emaillist();
		}
*/
		public function remoteloginAction() {
			$this->view->form = new Form($this, 'user/login');
			
			$this->view->testing = $this->_getParam('testing');
			
			$this->_helper->layout->disableLayout();
		}

		/**
		 * Login form
		 */
		public function loginAction() {

			$this->addMinify('css', 'css_new');
			$this->addMinify('js', 'js_new');
		
			if(loginManager::data()->rights != 'guest' && !Settings::get('modules_rental_website_enabled')){
				$this->render('noRights');
			}
			else {
				//create form
				$login = new Form($this, 'user/login');
	
				//Check form
				if ($this->getRequest()->isPost() && $login->isValid($this->getRequest()->getPost())) {
					//post data
					$data = $login->getValues();

					$bruteForceProtection = new \Login\BruteForceProtectionService();
					$bruteForceProtection->execute(new \Login\BruteForceProtectionRequest(
						$data['user']['username']
					));

					$user = loginManager::login(
						$data['user']['username'],
						$data['user']['password'],
						false,
						$data['user']['GA_code'],
						$data['user']['remember_device_and_location']
					);

                    if ($user['error'] == 'invalid-ip') {
                        $this->_helper->redirector('invalid-ip', 'user', false, array('user' => $user['userid']));
                    } elseif ($user['error'] === 'tenant_without_object') {
                        $this->view->tenant_without_object = true;
                    } elseif ($user) {
							if($this->isAjaxRequest) {
							    $startSessionAt = (new \DateTime());
                                $expirationSessionAt = (new \DateTime())->modify('+10 mins');

							    $respons = [
							        'username' => loginManager::data()->name,
							        'is_payed_account' => loginManager::data()->info['payed_account'] == 1,
                                    'start_session_at'=> $startSessionAt->format('Y-m-d H:i:s'),
                                    'expiration_session_at' => $expirationSessionAt->format('Y-m-d H:i:s')
                                ];
								CORSHelper::outputJSONResponse($respons);

							} elseif(loginManager::data()->info['department'] && !loginManager::data()->debug){
								$this->_redirect('dashboard/index');
							} elseif (in_array(loginManager::data()->rights, ['tenant', 'registrant'], true)) {
                                $this->tenantLoginRedirect();
							} else {
								if(Settings::get('modules_tenantLogin_maintainance') && loginManager::data()->rights == 'investor') {
									$this->_helper->redirector('maintainance', 'user');
								}

								if (strpos($_SERVER['REDIRECT_SCRIPT_URI'], '/user/login/') === false && $_SERVER['REDIRECT_SCRIPT_URI'] != '') {
									header('Location: ' . $_SERVER['REDIRECT_SCRIPT_URI']);
								} else {
									// Google Authenticator required, show code field
									if ($user['error'] === "GA_required") {
										$this->view->show_GA = true;
									}
									// Google Authenticator required and incorrect code entered, show field and error
									elseif($user['error'] === "GA_incorrect") {
										$this->view->show_GA = true;
										$this->view->show_GA_error = true;
									}
									// Default flow
									else {
										$this->_helper->redirector('index', 'index');
									}
								}
							}
					} else {
						if ($this->isAjaxRequest) {

                            RentalWebsiteLogger::instance()->log(
                                'user/login',
                                'Error: user login failed'
                            );

							CORSHelper::outputJSONResponse(
								[
									'username' => [
										'loginFailed',
									],
									'password' => [
										'loginFailed',
									],
								],
								403
							);
						}

						$this->view->loginerror = true;
					}
				} elseif ($this->isAjaxRequest) {
					CORSHelper::outputJSONResponse(
						(array) $login->getErrors('user'),
						403
					);
				}
	
				//view
				$this->view->login = $login;
			}

		}

		public function maintainanceAction(){
			$this->disableView();
			loginManager::logout();

			global $omniboxx_base_dir;

			include($omniboxx_base_dir . '/maintainance.php');

		}

		public function tenantLoginRedirect($type = false){
			// bespreken daarom comment hier
			if(Settings::get('modules_tenantLogin_maintainance') && $_SERVER['REMOTE_ADDR'] != '83.82.213.148')
				$this->_helper->redirector('maintainance', 'user');


			$browserName = (new \Sinergi\BrowserDetector\Browser())->getName();
			if ($browserName === 'Internet Explorer') {
				$this->_helper->redirector('not-supported', 'user');
			}

            if (loginManager::data()->rights === 'registrant') {
                $this->registrantLoginRedirect();
                return;
            }

            if ($type == 'investor') {
                $this->_helper->redirector('welcome', 'broker');
			}

			$r = new Resigned();

			$resigned = $r->fetchRow($r->select()->where('userobject = ?', loginManager::data()->info['uo'])->where('resigned = ?', 1));
			$uo = loginManager::data()->info['uo'];	
								
			if (loginManager::data()->info['first'] == 0) {
								
				if ($uo->digital && !$resigned && loginManager::data()->info['commercial'] === false) {
					loginManager::data()->info['first'] = 1;
					$this->_helper->redirector('welcome', 'user');
				} else {
					$this->_helper->redirector('dashboard', 'user');
				}
			}
			else {
				$this->_helper->redirector('welcome', 'user');
			}
		}

		private function registrantLoginRedirect() {
                $this->_helper->redirector('welcome', 'user');
		}

		public function resetAction() {

		}

		public function recoverAction() {
 			// recover action requested from backoffice?
			if (is_numeric($this->_getParam('id'))) {
				$user = new User();
				$language = $user->getLanguage($this->_getParam('id'), false, false, true);

                if ('tenant' === $this->_getParam('type') || 'registrant' === $this->_getParam('type') ) {
                    $select = db()->select()
                        ->from(['u' => 'users'], ['username', 'countusers' => 'COUNT(u.id)', 'userId' => 'id'])
                        ->joinLeft(['ea' => 'crm_email_address'],
                            'ea.map_to = u.id AND ea.type = "user"',
                            ['email' => 'address'])
                        ->where('u.id = ?', $this->_getParam('id'));

                    $row = db()->fetchRow($select);
                } elseif ('company' === $this->_getParam('type')) {
                    $select = db()->select()
                        ->from(['u' => 'users'], ['username', 'countusers' => 'COUNT(u.id)'])
                        ->joinLeft(['c' => 'company'], 'c.dummy_user = u.id', ['userId' => 'dummy_user'])
                        ->joinLeft(['ea' => 'crm_email_address'],
                            'ea.map_to = c.id AND ea.type = "company"',
                            ['email' => 'address'])
                        ->where('u.id = ?', $this->_getParam('id'));

                    $row = db()->fetchRow($select);
                }

                if ($row) {
                    $user->checkEmail($row['email'], $language, $row['countusers'], $row['userId']);
                }
				$this->disableView();
				return;
			}


			$this->addMinify('css', 'css_new');
			$this->addMinify('js', 'js_new');
			
			$this->view->fase = 'recover';

			//create form
			$recover = new Form($this, 'user/register/recover');

			//Check form
			if ($this->getRequest()->isPost() && $recover->isValid($this->getRequest()->getPost())) {
				
				$data = $recover->getValues();
				
				$user = new User();
				if ($data['code']) {
					/* check if the code is genuine */
					$user = $user->checkCode($data['code']);

					/* go to the next step */
					if (is_numeric($user['objectid']))
						$this->_helper->redirector('register', 'user', false, array('fase' => 'register', 'code' => $data['code']));
					else
						$this->view->errorcode = $user;
				}
				elseif ($data['email'] ) {
					$select = db()->select()
						->from(array('cea'=>'crm_email_address'), array('type','map_to'))
						->joinLeft(array('c' => 'company'), 'cea.map_to = c.id AND cea.type = "company"', array('dummy_user'))
						->joinLeft(array('u' => 'users'), 'cea.map_to = u.id  OR c.dummy_user = u.id', array('username','id', 'countusers' => 'COUNT(DISTINCT u.id)'))
						->where('cea.address = ?', $data['email'])
                        ->group('cea.address');
					$row = db()->fetchRow($select);
					
					if (!is_array($row) ) {
						$this->view->unknownEmail = true;
					}
					else if ($row['username'] == null && $row['countusers'] == 1) {
						$this->view->unknownUsername = true;
					} else {
						$language = $user->getLanguage($row['id'], false, false, true);
						$user->checkEmail($data['email'],$language, $row['countusers']);
						$this->render('register/recovered_email');

						return;
					}
				}
			}
			//view
			$this->view->recover = $recover;
			$this->render('register/recover');
		}



    public function recoveryAction() {

        $this->addMinify('css', 'css_new');
        $this->addMinify('js', 'js_new');

        $this->view->fase = 'recover';

        //create form
        $recover = new Form($this, 'user/register/recoverusername');

        //Check form
        if ($this->getRequest()->isPost()) {

            //$data = $recover->getValues();
            $data = $this->getRequest()->getPost();

            if ($data['username'] ) {

                $select = db()->select()
                    ->from(array('u'=>'users'), array('username','id', 'countusers' => 'COUNT(u.id)'))
                    ->joinLeft(array('cea'=>'crm_email_address'), 'cea.map_to = u.id', array('type','map_to', 'address'))
                    ->where('u.username = ?', $data['username']);
                $row = db()->fetchRow($select);
          
                if (!is_array($row) ) {
                    $this->render('register/recovered_username');
                    return;
                }
                else if ($row['address'] == null) {
                    $this->render('register/recovered_username');
                    return;
                } else {
                    $user = new User();
                    $language = $user->getLanguage($row['id'], false, false, true);
                    $user->createAndSendNewPasswordOnUsername($row['id'],$language);
                    $this->render('register/recovered_username');
                    return;
                }
            }
        }
        //view
        $this->view->recover = $recover;
        $this->render('register/recoverusername');



    }


		/**
		 * Register form
		 */
		public function registerAction() {

			$this->addMinify('css', 'css_new');
			$this->addMinify('js', 'js_new');

			/* First ask for the unique code */
			if ($this->_getParam('fase') == 'code' || !$this->_getParam('fase')) {
				$this->view->fase = 'code';

				//create form
				$register = new Form($this, 'user/register/code');
				if ($this->getRequest()->isPost() && $register->isValid($this->getRequest()->getPost())) {

					//post data
					$data = $register->getValues();

					/* check if the code is genuine */
					$user = new User();
					$check = $user->checkCode($data['code']);

					/* go to the next step */
					if ($check !== false) {
						$this->_helper->redirector('register', 'user', false, array('fase' => 'register', 'code' => $data['code']));
					} else {
						$this->view->errorcode = ['objectid' => false];
					}
				}

				if($this->_getParam('errorcode') == 'objectid')
					$this->view->errorcode = array('objectid' => false);

				//view
				$this->view->register = $register;
				$this->render('register/code');

				/* Next step, ask for a username and password */
			}
			elseif ($this->_getParam('fase') == 'register' || is_numeric($this->_getParam('code'))) {
				
				$user = new User();
				$check = $user->checkCode($this->_getParam('code'));

				//create form
				$register = new Form($this, 'user/register/register');

				//Check form
				if ($this->getRequest()->isPost() && $register->isValid($this->getRequest()->getPost())) {
					
					$data = $register->getValues();
					
					

					

					if ($check === false)
						$this->_helper->redirector('register', 'user', false, array('fase' => 'code'));

					

					if ($data['user']['password'] != $data['user']['password_repeat'] ) {
						$this->view->notmatchingpass = true;
					} else {
						/* register a new user only if passwords are in sync */
						$login = $user->register($data, $this->_getParam('code'), $check['user']);
					}

					if ($login === false ) {

						// login is invalid, already taken?
						$this->view->errorcode = ['username_double' => false];

					} 
					if ($this->view->errorcode == false && $this->view->notmatchingpass == false) {
						

							// use an user_objects row
							if($check['uo']){
								$uo = new Objectusers();
								$uo_row = $uo->getById($check['uo']);
							
								$uo_row->customer = $login;

								if(!Settings::get('modules_tenantLogin_roomselector')) {
									$uo_row->finaldate = true;
								}
								
								// if the uo_row doesn't have a till date and the object has a defined period, add a enddate
								if(!$uo_row->till && $uo_row->from && Object::getPeriod($uo_row->object))
									$uo_row->till = date('Y-m-d', strtotime('+' . Object::getPeriod($uo_row->object) . ' months', strtotime($uo_row->from)));
								
								// erase the unique code
								$uo_row->code = nullValue();

								$uo_row->save();

							// make or use a user object register connection
							} else {
								// make a temp connection between the user and object
								$uor = new UsersObjectsRegister();

								// if code is attached to user_object_register, select it by id
								if($check['uor']){
									$row = $uor->getById($check['uor']);

								// code is attached to object, see if user_object_register row exists
								} else {
									$row = $uor->fetchRow($uor->select()
										->where('object = ?', $check['object'])
										->where('user = ?', $login));
								}
								
								// get or create row
								$row = $row ? $row : $uor->createRow();
								
								// set the parameters
								$row->user = $login;
								$row->object = $check['object'];
								$row->code = $this->_getParam('code');
								
								// save the user_object_register row
								$row->save();

								// update the code for the object
								$o = new Object();
								$o->newCode($row->object);
							}
							
						// all done, login and redirect the user
						$login = loginManager::login($data['user']['username'], $data['user']['password']);	

						// ensure the session language is always set to the one dependend on the currently
						// registering user instead of the session (for Ravel at least)
						if(Settings::get('general_company_shortname') === 'ravel')
							$_SESSION['language'] = loginManager::data()->info['language']?: $_SESSION['language'];
									
						if (loginManager::data()->info['first'] == 0)
							$this->_helper->redirector('dashboard', 'user');
						
						$this->testContracting();
						
						$this->_helper->redirector('welcome', 'user');
					}
				}

				$this->view->code = $this->_getParam('code');
				$this->view->register = $register;
				$this->render('register/register');
			}
		}

		public function convertObjectCodeToUserCodeAction(){
			$this->disableView();
			
			$u = new Users();
			$o = new Object();
			
			$select = db()->select()
				->from(array('u' => 'users'),		array('id'))
				
				->joinLeft(array('uo' => 'users_objects'),
					'uo.customer = u.id',			array('object'))
				
				->joinLeft(array('o' => 'objects'),
					'o.id = uo.object',				array('code'))
				
				->joinLeft(array('og' => 'objectgroup'),
					'og.id = o.objectgroup',		false)
					
				->where('o.code IS NOT NULL')
				->where('u.code IS NULL')
				->where('u.username IS NULL');
					
			if($this->_getParam('project'))		
				$select->where('og.project = ?', $this->_getParam('project'));
			
			foreach(db()->fetchAll($select) as $user){
				
				$row = $u->fetchRow($u->select()->where('id = ?', $user['id']));

				$row->code = $user['code'];
				continue;
				$row->save();
				
				$o->newCode($user['object']);
			}			
			
		}
  
		public function setLoudAction(){
			
			$u = new Users();
			
			if($row = $u->fetchRow($u->select()->where('id = ?', $this->_getParam('id')))){
				$row->loud_customer = 1;
				$row->save();
			}
		 	$this->disableView();
			$this->_redirect('support/show', array('id' => $this->_getParam('id'), 'type' => 'user'));
		  
	      
	    }
		
		public function unsetLoudAction(){
			
			$u = new Users();
			
			if($row = $u->fetchRow($u->select()->where('id = ?', $this->_getParam('id')))){
				$row->loud_customer = 0;
				$row->save();
			}
		 	$this->disableView();
			$this->_redirect('support/show', array('id' => $this->_getParam('id'), 'type' => 'user'));
		  
	      
	    }
		
	    public function reMailContractAction(){
	      $this->view->Breadcrumbs()->addCrumb('Contract opnieuw versturen');
	      
	      if (!$this->_getParam('id'))
	         die();
	         
	      $u = new User();
	      $this->view->result = $u->reMailContract($this->_getParam('id'));
	    }

	    public function saveSignatureAction(){
	    	$this->disableView();

	    	$user_id = $this->getParam('user');

	    	if(is_null($user_id))
				dieWithStatuscode(404);

			if(!is_numeric($user_id))
				dieWithStatuscode(404);

	    	$signature_model = new \Controllers\User\Signature($user_id);

			$image_data = $signature_model->getImageDataFromUpload();
			$document_id = $signature_model->saveImageDataAsDocument($image_data);

			$signature_model->addDocumentToSession($document_id);
	    }

        public function deleteSignatureAction(){
            $this->disableView();

            $user_id = $this->getParam('user');
            $type = $this->getParam('type');

            if(is_null($user_id))
                dieWithStatuscode(404);

            if(!is_numeric($user_id))
                dieWithStatuscode(404);

            $signature_model = new \Controllers\User\Signature($user_id);
            $signature_model->deleteSignature($user_id, $type);

            if($referer = $_SERVER['HTTP_REFERER']) {
                header('Location: ' . $referer);
            }
        }
        public function remainingDepositDocumentsAction()
        {
        		$this->view->extraWidth = true;

            $this->view->Breadcrumbs()
                ->addCrumb('Waarborg uitwisseling overzicht');

            $select = db()->select()
                ->from(['uo' => 'users_objects'], ['id', 'user' => 'customer', 'object'])
                ->joinLeft(['dk' => 'deposit_korfine'], 'dk.user_object_id = uo.id', ['dk_id' => 'id', 'dk_document' => 'support_document_id'])
                ->joinLeft(['o' => 'objects'], 'o.id = uo.object', ['rendered_address'])
                ->joinLeft(['i' => 'investor'], 'i.id = o.investoroverride', ['investor' => 'id', 'investor_name' => 'name'])
                ->joinLeft(['iu' => 'users'], 'iu.id = i.user', false)
                ->joinLeft(['iup' => 'users'], 'iup.id = iu.partner', false)
                ->joinLeft(['i2' => 'investor'], 'i2.user = iup.id', ['investor_2' => 'id', 'investor_2_name' => 'name'])
                ->joinLeft(['u' => 'users'], 'u.id = uo.customer', ['rendered_name'])
                ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
				->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project' => 'id', 'project_name' => 'name'])
                ->where('uo.from >= CURDATE()')
				->where('o.deposit > ?', 0)
                ->where('uo.contract_id > 0');

			$data = db()->fetchAll($select);

            $data = array_map(function($item){
				$item['status'] = 'todo';

				if($item['dk_id'] > 0)
                    $item['status'] = 'concept_done';

                if($item['dk_document'] > 0)
                    $item['status'] = 'done';

				return $item;
			}, $data);

            if(is_null($this->getParam('status'))){
            		$this->setParam('status', 'todo');
			}

            $this->view->ListView($data)
                ->setTypes([
                    'project' => ['title' => '', 'width' => 'hidden'],
                	'user' => ['title' => 'Huurder', 'width' => 'xxxlarge truncate'],
                    'investor' => ['title' => 'Belegger', 'width' => 'xxxlarge truncate'],
                    'investor_2' => ['title' => 'Belegger partner', 'width' => 'xxxlarge truncate'],
                    'object' => ['title' => 'Object', 'width' => 'xxxxlarge truncate'],
                    'status' => ['title' => 'Status', 'width' => 'hidden'],
				])

				->addFormat('user', function($value, $row){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
				})

                ->addFormat('investor', function($value, $row){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . $row['investor_name'] . '</a>' : '-';
                })

                ->addFormat('investor_2', function($value, $row){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . $row['investor_2_name'] . '</a>' : '-';
                })

                ->addFormat('object', function($value, $row){
                    return $value > 0 ? $row['rendered_address'] : '-';
                })

                ->addFormat('project', function($value, $row){
                    return $value > 0 ? $row['project_name'] : '-';
                })

				->addFormat('status', function($value){
					$status_names = [
						'todo' => 'Niet verstuurd',
                        	'concept_done' => 'Dossier aangemaakt, nog niet ondertekend',
                        	'done' => 'Afgerond',
					];
					return $status_names[$value];
				})

                ->setOptions([
                    'paginator' => true,
                ])

				->setFilters([
					'user' => ['type' => 'input'],
                    'object' => ['type' => 'input'],
                    'status' => ['renderSeparately' => true, 'title' => 'Status', 'show_all_disabled' => true],
                    'project' => ['renderSeparately' => true, 'title' => 'Project'],
				])

				->addActions([
					'buttons' => [
						[
							'title' => 'Dossier versturen',
							'icon' => 'page_go',
							'params' => [],
							'action' => 'user/send-deposit-document/',
							'confirm' => true
						],
						[
							'title' => 'Ondertekening versturen',
							'icon' => 'document-pencil',
							'params' => [],
							'action' => 'user/sign-deposit-document/',
							'confirm' => true
						]
					]
				])

				->addDetails('user/deposit-document-details', ['style' => 'popup'])

                ->render($this);
	    }

		public function depositDocumentDetailsAction(){
            $this->view->Breadcrumbs()
                ->addCrumb('Waarborg uitwisseling overzicht')
                ->addCrumb('Waarborg uitwisseling details');

	    	$this->disableView(true);

            $u_lib = new User();
            $this->view->deposit = $u_lib->outputDepositDocumentData($this->getParam('id'));
		}

	    public function sendDepositDocumentAction(){
	    	$this->disableView();
            $uo_ids = false;

	    	if(is_null($this->getParam('uo_id')) && !is_null($this->getParam('ids'))){
                $uo_ids = $this->getParam('ids');
			} elseif (!is_null($this->getParam('uo_id'))) {
	    		$uo_ids = [$this->getParam('uo_id')];
			}

			if($uo_ids) {
                $u_lib = new User();

                foreach($uo_ids as $uo_id) {
                    $u_lib->sendDepositDocument($uo_id);
                }
            }

            if($referer = $_SERVER['HTTP_REFERER'])
                header('Location: '. $referer);
		}

        public function signDepositDocumentAction(){
            $this->disableView();
            $uo_ids = false;

            if(is_null($this->getParam('uo_id')) && !is_null($this->getParam('ids'))){
                $uo_ids = $this->getParam('ids');
            } elseif (!is_null($this->getParam('uo_id'))) {
                $uo_ids = [$this->getParam('uo_id')];
            }

            if($uo_ids) {
                foreach($uo_ids as $uo_id) {
                    $repository = new \Deposit\Infrastructure\Domain\Model\DepositRepository(
                        $uo_id
                    );
                    $sign_service = new \Deposit\Infrastructure\Domain\Service\Korfine\KorfineSignDepositService(
                        $repository
                    );

                    $sign_service->sign();
                }
            }

            if($referer = $_SERVER['HTTP_REFERER'])
                header('Location: '. $referer);
        }

        public function depositPayoutListAction()
        {
            $this->view->extraWidth = true;

            $this->view->Breadcrumbs()
                ->addCrumb('Waarborg uitkering overzicht');

            $model = new \Controllers\User\DepositPayout();
            $data = $model->getList();

            foreach($data as $data_key => $data_item){
            	if((!$data_item['user_agree']) || (!$data_item['user_iban'])) {
                    $data[$data_key]['listview_row_classes'] = ['status', 'red'];
                }
			}

            $user = new User();

            $this->view->ListView($data)
                ->setTypes([
                    'project' => ['title' => '', 'width' => 'hidden'],
                    'user' => ['title' => 'Huurder', 'width' => 'large truncate'],
                    'object' => ['title' => 'Object', 'width' => 'large truncate'],
                    'payment_reference' => ['title' => 'Korfine ref.', 'width' => 'small truncate'],
                    'inspection_date' => ['title' => 'Inspectie datum', 'width' => 'medium right'],
                    'deposit_payed' => ['title' => 'Oorspronkelijk bedrag', 'width' => 'medium right'],
                    'spacer1' => ['title' => ' ', 'width' => 'xxxsmall'],
                    'expense_trustee' => ['title' => 'Beheerder', 'width' => 'small right'],
                    'expense_owner' => ['title' => 'Verhuurder', 'width' => 'small right'],
                    'expense_return' => ['title' => 'Huurder', 'width' => 'small right'],
                    'user_agree' => ['title' => 'Overeenstemming', 'width' => 'xxsmall'],
                    'user_iban' => ['title' => 'IBAN', 'width' => 'small'],
					'identification' => ['title' => 'ID', 'width' => 'xxxsmall'],
					'preview' => ['title' => 'Preview', 'width' => 'xxsmall'],
					'edit' => ['title' => 'Bewerken', 'width' => 'xxsmall']
                ])

				->addFormat(['deposit_payed', 'expense_return', 'expense_trustee', 'expense_owner'], 'money_from_int_db')

				->addFormat('user_agree', 'bool')
				->addFormat('user_iban', 'iban')

                ->addFormat('user', function($value, $row){
                    return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $row['rendered_name'] . '</a>' : '-';
                })

                ->addFormat('object', function($value, $row){
                    return $value > 0 ? $row['rendered_address'] : '-';
                })

                ->addFormat('project', function($value, $row){
                    return $value > 0 ? $row['project_name'] : '-';
                })

				->addFormat('inspection_date', 'date')


				->addFormat('identification', function ($value, $row) use ($user) {

					if ($row['identification_support_version_document_id']) {
                        $version = (new SupportDocumentsVersions())->fetchRowById($row['identification_support_version_document_id']);

                        $documentId = $version['support_documents'];

					} else {
                        $documentId = $user->getIdentificationDocumentId($row['user']);
					}

                    return $documentId ? "<a href='document/download/id/{$documentId}' target='_blank'>ja <i class='fa fa-file-pdf-o' aria-hidden='true'></i></a>" : 'nee';
                })
                
				->addFormat('edit', function ($value, $row) {
					return "<a href='user/deposit-payout-edit/id/{$row['inspection_id']}'><i class='fa fa-edit'></i></a>";
				})

				->addFormat('preview', function ($item, $row) {
                    return "<a href='user/cancel-deposit-pdf-preview/user_id/{$row['user']}/user_object_id/{$row['user_object_id']}' target='_blank'><i class='fa fa-file-pdf' aria-hidden='true'></i></a>";
				})

				->addFormat('payment_reference', function ($value, $item) {
					return $value ?: $item['old_korfine_deposit_reference'];
				})

                ->setOptions([
                    'paginator' => true,
                ])

                ->setFilters([
                    'user' => ['type' => 'input'],
                    'object' => ['type' => 'input'],
                    'payment_reference' => ['type' => 'input'],
                    'deposit_payed' => ['type' => 'range'],
                    'expense_return' => ['type' => 'range'],
                    'inspection_date' => ['type' => 'date_range'],
                    'user_iban' => ['type' => 'input'],
                    'user_agree' => [],
                    'identification' => [],
                    'project' => ['renderSeparately' => true, 'title' => 'Project'],
                ])

				->addTotals(['deposit_payed', 'expense_return'])

                ->addActions([
                    'buttons' => [
                        [
                            'title' => 'Waarborg uitkeren',
                            'icon' => 'page_go',
                            'params' => [],
                            'action' => 'user/do-deposit-payout/',
                            'confirm' => true
                        ],
                        [
                            'title' => 'Verwijderen',
                            'icon' => 'delete',
                            'params' => [],
                            'action' => 'user/cancel-deposit-payout/',
                            'confirm' => true
                        ]
                    ]
                ])

				->render($this);
        }

        public function depositPayoutEditAction()
        {
        	$inspectionId = $this->getParam('id');

        	if (!is_numeric($inspectionId)) {
        		$this->redirect('user/deposit-payout-list');
			}

			$inspection = (new Inspection())->fetchRowById($inspectionId);

        	if (!$inspection) {
                $this->redirect('user/deposit-payout-list');
			}

			$data = $inspection->toArray();
        	$data['expense_return'] /= 100;
        	$data['expense_trustee'] /= 100;
        	$data['expense_owner'] /= 100;
        	
        	$form = new Form($this, 'deposit/deposit-payout-edit');
			$form->populate($data);

			$uoRow = db()->select()
				->from('users_objects', 'deposit_payed')
				->where('id = ?', $data['users_objects_id'])
				->query()
				->fetch();

        	$this->view->assign('form', $form);
        	$this->view->assign('depositPayed', '&euro; ' . ($uoRow['deposit_payed'] / 100));

            if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
                $formData = $form->getValues();

                $uoRow = (new UsersObjects())->fetchRowById($data['users_objects_id']);

                $formData['expense_trustee'] = (new StringFormat($formData['expense_trustee'], 'money_db'))->userstring;
                $formData['expense_owner'] = (new StringFormat($formData['expense_owner'], 'money_db'))->userstring;
                $formData['expense_return'] = $uoRow['deposit_payed'] - ($formData['expense_trustee'] + $formData['expense_owner']);

				$i = new Inspection();
				$row = $i->fetchRowById($formData['id']);

				if ($row) {
					$row->setFromArray($formData)->save();
				}

                $this->redirect('user/deposit-payout-list');
            }
        }

        public function cancelDepositPdfPreviewAction()
        {
			$this->disableView();

			$userId = $this->getParam('user_id');
			$userObjectId = $this->getParam('user_object_id');

			$user = (new Users())->fetchRowById($userId);

			if ($user['language'] === 'nl') {
                $createCancelDepositFormPdf = new \Deposit\Domain\Service\CreateCancelDepositFormPdf();
			} else {
                $createCancelDepositFormPdf = new \Deposit\Domain\Service\FRCreateCancelDepositFormPdf();
			}

            $pdf = (new \Deposit\Application\Service\CreateCancelDepositFormPDFService(
                new \Deposit\Domain\Service\RetrieveCancelDepositData(),
                new \Deposit\Domain\Factory\CancelDepositFromFactory(),
                $createCancelDepositFormPdf
            ))->execute($userObjectId);

            ob_clean();
            ob_end_clean();
            header('Content-type: application/pdf');
            header('Cache-Control: private');
            header('Pragma: private');
            header('Content-disposition: inline; filename="preview.pdf"');

            echo $pdf;
        }

        public function cancelDepositPayoutAction()
        {
			$this->disableView();

			$ids = $this->getParam('ids');

            $model = new \Controllers\User\DepositPayout();

			foreach($ids as $id) {
                $model->markDepositRepayed($id, 0);
			}
        }

        public function doDepositPayoutAction()
        {
            $this->disableView();

            $userObjectIds = $this->getParam('ids');

            $model = new \Controllers\User\DepositPayout();
            $model->filterUserObject($userObjectIds);

            foreach($model->getList() as $deposit_item) {
                $model->markDepositRepayed($deposit_item['id'], $deposit_item['expense_return']);
                $this->createCancelDepositFormSupportDoc($deposit_item['user'], $deposit_item['user_object_id']);
                $model->requestKorfinePayout($deposit_item);
            }
        }

        private function createCancelDepositFormSupportDoc($user_id, $userObjectId)
        {
            $user = (new \Users())->fetchRowById($user_id);

            if ($user['language'] === 'nl') {
                $createCancelDepositFormPdf = new \Deposit\Domain\Service\CreateCancelDepositFormPdf();
            } else {
                $createCancelDepositFormPdf = new \Deposit\Domain\Service\FRCreateCancelDepositFormPdf();
            }

            $pdf = (new \Deposit\Application\Service\CreateCancelDepositFormPDFService(
                new \Deposit\Domain\Service\RetrieveCancelDepositData(),
                new \Deposit\Domain\Factory\CancelDepositFromFactory(),
                $createCancelDepositFormPdf
            ))->execute($userObjectId);

            $docTable = new \SupportDocuments();
            $verTable = new \SupportDocumentsVersions();
            $titleModel = new \SupportDocumentsCategoriesTitles();

            $user_id = db()->fetchOne(db()->select()->from('users_objects', ['customer'])->where('id = ?', $userObjectId));

            $filename = \Main::app()->getDir('document') . 'user/' . $user_id . '/' . $userObjectId . ' - ' . date('d-m-Y H:i:s') . ' cancel-deposit.pdf';

            createFolder($filename);
            file_put_contents($filename, $pdf);

            $title_row = $titleModel->getFirstWithSettings(['deposit_payout_document' => true]);

            if(!$title_row)
                $title_row = $titleModel->create([
                    'deposit_payout_document' => true,
                    'title' => 'Waarborg documenten',
                    'inspection' => false
                ]);

            $attributes = [
                'category' => $title_row['document_category'],
                'titleid' => $title_row['id'],
                'title' => 'Waarborg uitbetalingsdocumenten',
                'type' => 'user',
                'map_to' => $user_id,
                'user' => loginManager::data()->id
            ];

            if(!($document = $docTable->matchRow($attributes)))
                $document = $docTable->createRow($attributes);

            $document->description = 'Waarborg uitbetalingsdocumenten Korfine';
            $document->document = $filename;
            $document->visibleForUser = 1;

            $documentId = $document->save();

            $version_attributes = array(
                'support_documents' => $documentId
            );

            if(!($version = $verTable->matchRow($version_attributes))){
                $version = $verTable->createRow($version_attributes);
            }

            $version->title = $document->title;
            $version->description = $document->description;
            $version->document = $filename;
            $version->originalDate = new \Zend_Db_Expr("NOW()");
            $version->save();

        }

		public function contractAction() {
            $d = new Document();
			$user = new User();
			$user->user = loginManager::data()->id;
			$is_registrant = loginManager::data()->rights === 'registrant';

			$this->testContracting(Settings::get('modules_tenantLogin_checkbeforesign') || $is_registrant);


			$rd = new RentalDocumentcheckSets();
			// get or generate contract

            if(is_numeric(loginManager::data()->project['id'])) {
                $documentsetId = $rd->getSetForProject(loginManager::data()->project['id']);
                $documentsettext = $rd->getSetText($documentsetId);
            }
             if($is_registrant || loginManager::data()->info['profile_completion'] == '2' ) {
                $document = new Document();
                $this->view->actionHelper = new Controllers\User\Contract();
                $this->view->first = true;
                $this->view->documentset = $documentsettext;
                $this->view->contract = ['u_id' => loginManager::data()->id];
                $this->view->login->project['vidii_enabled'] = 'yes';
                $u_lib = new User();
                $u_lib->user = loginManager::data()->id;
                $this->view->profile = $u_lib->getProfile();
                if (loginManager::data()->info['profile_completion'] == '2') {
                    $this->view->enableDocumentUpload = true;
            	} else {
                    $this->view->enableDocumentUpload = false;
				}
                $this->view->documents = $document->getList(
                    $user->user,
                    'user',
                    [],
                    true,
                    loginManager::data()->info['commercial']
                );
                if (loginManager::data()->info['profile_completion'] == '0'){
                    $this->testContracting(false);
				} else {
                    $this->render('sign');
				}

            } elseif (!is_numeric($this->_getParam('id'))) {
                $this->testContracting(false);
				$this->view->contracts = $user->getContract();
				$allsigned = true;
				$notsigned = false;
				
				$typefilename = translate()->_('contract');
				
				if (is_array($this->view->contracts))
					foreach ($this->view->contracts as $i => $contract) {
						$this->view->contracts[$i]['filename'] = $contract['company'] ? $typefilename . $contract['company'] : $typefilename . $contract['address'] . ' ' . $contract['number'];
						$allsigned = $contract['signed'] == 0 ? false : $allsigned;
						$notsigned = $contract['signed'] == 0 ? $contract['id'] : $notsigned;
					}

				if ($allsigned && $this->firstLogin) {
					$user->endFirst(); // end first session
					$this->_helper->redirector('documents', 'user');
				} elseif ($notsigned){
					$this->_helper->redirector('contract', 'user', false, array('id' => $notsigned));					
				} elseif($allsigned) {
                    $this->_helper->redirector('dashboard', 'user');
				}

				// sign or export contract
			} else {
                 if(false) {
                     // if we ever encounter any technical problems with the phpdocx (word merge) server, this clause can be enabled to close contracting and prevent empty contract files
                     $this->render('contracting-disabled');
                     return;
                 }


				$contract = $user->getContract($this->_getParam('id'));


                if($this->login->info['partner'] > 0)
                    $this->view->partner_row = db()->fetchRow(db()->select()->from('users')->where('id = ?', $this->login->info['partner']));

				
				$typefilename = translate()->_('contract') ;
				
				$filename = $this->view->filename = $contract['company'] ? $typefilename . $contract['company'] : $typefilename . $contract['address'] . ' ' . $contract['number'];

				if ($this->_getParam('signed')) {
                    if(Settings::get('modules_tenantLogin_confirm_cell_phone')) {
                        $model = new \Controllers\User\ConfirmCellPhone();
                        if (!$model->isConfirmed(loginManager::data()->id)) {
                            die('Bevestiging per SMS niet ontvangen, ga terug naar de vorige pagina en probeer het nogmaals');
                        }
                    }

					if(!isset($_SESSION['contract_signed']) && (is_null($contract['contract_id']) || $contract['contract_id'] === '0')){
						$result = $user->addContract(
						    $this->_getParam('id'),
                            $filename,
                            true,
                            $contract['objectgroup_type'],
                            $contract['docs']
                        );

						if($result !== true){
                            $this->render('sign');
                            return;
                        } else {

                            $this->appendSubscriptionStatusLog($this->_getParam('id'));

                            $this->callUpdateLofts($this->_getParam('id'));

                            try {
                                $nameplate = new \Controllers\User\Nameplate();
                                $nameplate->request(
                                    loginManager::data()->info['uo']
                                );
                            } catch (Exception $e) {
                                error_log($e->getMessage());
                            }

                            $_SESSION['contract_signed'] = true;
                        }
					}

					$user->endFirst();
					unset($_SESSION['contract_signed']);
					$this->_helper->redirector('signed', 'user');
				}

				
				if ($contract['signed']) {
					$this->_helper->layout->disableLayout();
					$this->_helper->viewRenderer->setNoRender(true);
					
					
					$this->view->contract = $user->exportContract($contract['object'], $filename);
				} elseif ($this->_getParam('preview')){
					$this->_helper->layout->disableLayout();
					$this->_helper->viewRenderer->setNoRender(true);
 					$this->view->contract = $user->generateContract($this->_getParam('id'), $filename, true);

				} else {

                    unset($_SESSION['contract_signature_document_id']);

					$document = new Document();
					$docsParams = [];
					if(Settings::get('general_company_shortname') == 'ravel')
						$docsParams['language'] = User::getLanguage($user->user);
					$this->view->documents = $list =$document->getList($user->user, 'user', $docsParams, true, loginManager::data()->info['commercial']);
                    $this->view->project = Project::getDetails(loginManager::data()->project['id']);
					$this->view->contractingpossible = $d->getObligatedDocuments($user->user);
					$this->view->contract = $contract;
					$this->view->userObjectId = $contract['id'];

					$this->view->actionHelper = new Controllers\User\Contract();
					$this->render('sign');
				}

			}

		}

		public function contractMergeDocumentPreviewAction(){
			$this->disableView();

			$model = new \Controllers\User\ContractMergeDocument(
				$this->getParam('id'),
				loginManager::data()->info['uo']
			);

			$model->setPreview(true);

			$pdf = $model->getPdfClass();
			$pdf->browseroutput('preview.pdf');
		}

		public function signatureAction()
		{

			$this->view->headLink()->appendStylesheet('media/style/user/signature.css');
			$this->view->headScriptHashed()
				->appendFile('media/javascript/signature_pad/signature_pad.js')
				->appendFile('media/javascript/user/signature.js');

			$this->view->id = $user_id = $this->getParam('id');
			$this->view->type = $type = $this->getParam('type');

            $signature_model = new \Controllers\User\Signature($user_id);
            $signature_model->loadSignatureFromDocumentsForUser($user_id, $type);

			$this->render('partssign/signature');
		}

		private function appendSubscriptionStatusLog($usersObjectId) {
			$uoModel = new Objectusers();

			if($usersObject = $uoModel->get($usersObjectId)) {
				SubscriptionStatusLog::append($usersObject, SubscriptionStatusLog::STATUS_DONE);
			}
		}

		private function callUpdateLofts($usersObjectId) {

			$usersObject = (new Objectusers())->get($usersObjectId);
			if ($usersObject && intval($usersObject['object']) > 0) {
				$hostname = ObjectsAlert::getUpdateLoftsHostnameForObject($usersObject['object']);

				if (trim($hostname)) startWorker("update-lofts-worker/hostname/$hostname");
			}
		}

		public function indicateDossierIsFinalizableAction() {
			$params = $this->getAllParams();

			try {
				$contract = new Controllers\User\IndicateDossierIsFinalizableService();
				$contract->execute( $params['user_id'] );

			} catch ( Exception $e ) {
			}

			$this->_redirect('user/contract', [
				'id' => $params['contract_id'],
			]);
		}

		public function testIpAction(){
			$this->disableView();
		}

		public function exportDummyContractAction() {
			$this->disableView();
			
			$user = new User();
			
			if (is_numeric($this->_getParam('project')))
			
				if(!$user->getDummyContract($this->_getParam('project'),(bool) $this->_getParam('empty'),$this->_getParam('contract') ))
					die('Geen gebruiker gevonden in dit project');
		}

		public function testAction() {
			$user = new User();
			$user->user = 400;
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$user->testContract(898, 237, 32, 131);
		}

		public function signedAction() {
			$this->view->commercial = loginManager::data()->info['commercial'];
			$this->view->to_be_validated = Settings::get('modules_tenantLogin_contract_validating');
		}

		/**
		 * Get User
		 */
		public function getuserAction() {
			$user = new User();

			if ($this->_getParam('name')) {
				echo Zend_Json::encode($user->getusers(false, 'tenant', $this->_getParam('name')));
			}
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

		}

		/**
		 * Logout
		 */
		public function logoutAction() {
			$this->disableView();

			loginManager::logout();
			unset($_SESSION['testrights']);

			if ($this->isAjaxRequest) {
				CORSHelper::outputJSONResponse();
			}

			//forward to main page
			$this->_redirect('index/index');
		}

		/**
		 * getAddressBook
		 */
		public function getaddressbookAction() {

			$this->view->headLink()->appendStylesheet('media/style/useredit.css');
			$this->view->headLink()->appendStylesheet('media/style/combobox.css');

			$user = new User();
			$commercial = $this->_getParam('commercial') ? $this->_getParam('commercial') : false;
			$type = $this->_getParam('type');
			$type = ($commercial && $type == 'tenant')? 'company' : $type;
			$details = (is_numeric($this->_getParam('details'))) ? $this->_getParam('details') : false;

			$value_name = $this->_getParam('valuename') ? str_replace(' ', '_', $this->_getParam('valuename')) :false;
			$investor = (is_numeric($this->_getParam($value_name ? $value_name : 'Belegger'))) ? $this->_getParam($value_name ? $value_name : 'Belegger') : false;
			$this->view->editOnly = $editOnly = $this->_getParam('edit-only') == '1';

			if($type == 'investor')
				if($investor)
					$details = $investor;

			if($details) {
				$this->view->users = $users = $user->getUsers($details, $type, false, '8, 0');

				foreach ($users as $user) {
					if ($user['id'] == $details && intval($user['companyid']) > 0)
						$details = intval($user['companyid']);
				}
			}

			$is_investor_combination = false;
			if(strpos($this->_getParam('details'), 'combination_') !== false){
                $details = str_replace('combination_', '', $this->_getParam('details'));
                $is_investor_combination = true;
                $commercial = false;
            } elseif(strpos($this->_getParam('Belegger'), 'combination_') !== false){
                $details = str_replace('combination_', '', $this->_getParam('Belegger'));
                $is_investor_combination = true;
                $commercial = false;
            }

			$this->view->details = ($details !== false) ? $details : false;
			$this->view->type = $this->_getParam('type');
			$this->view->commercial = $commercial;
			$this->view->is_combination = $is_investor_combination;
			$this->view->objectid = $this->_getParam('objectid') ? $this->_getParam('objectid') : false;
			$this->view->uoid = $this->_getParam('uoid') ? $this->_getParam('uoid') : false;
			$this->_helper->layout->disableLayout();
		}

		/**
		 * edit
		 */
		public function editAction() {
			$this->disableView(true);
			$this->view->headLink()->appendStylesheet('media/style/useredit.css');
			
			$commercial = $this->_getParam('commercial') == 'true' ? true : false;
			
			$this->view->id = is_numeric($this->_getParam('id')) ? $this->_getParam('id') : false;
			
			
			$user = new User();
			if ($commercial){
				$form = new Form($this, 'user/addCommercial');
			} elseif (!$commercial){
				if($this->_getParam('type') == 'tenant'){
					$form = new Form($this,'user/add');
				} elseif ($this->_getParam('type') == 'investor'){
					$form = new Form($this, 'user/addInvestor');
				}	
			}

			if ($this->view->id !== false) {
				$type = ($_POST['type'] != '') ? $_POST['type'] : false;
				if ($commercial){
					$user->user = $this->view->id;

					if($uo_id = $this->_getParam('uoid'))
						$user->uo_id = $uo_id;

					$data = $user->getCommercialProfile(false, true);
				} else {
					$data = array_shift($user->getUsers($this->view->id, $type));
					$this->view->partnerid = $data['partner']['id'];
				}

				$form->populate($data);
				$this->view->newUser = false;
			}
			else {
				$this->view->newUser = true;
			}

			$this->view->type = $this->_getParam('type');
			$this->view->initial = $this->_getParam('uoid') ? false : true;
			$this->view->useradd = $form;
			
			if ($commercial){
				$this->render('editCommercial');
			}
		}

		public function partnerUnlinkAction(){
			$this->disableView();

			$user_id = $this->_getParam('user');
			$partner_nr = $this->_getParam('partner_number');
			$u_model = new Users();
			$uo_model = new Objectusers();

			$u_row = $u_model->getById($user_id);

			if ($partner_nr == 2) {
                $partner_id = $u_row->partner;
                $u_row->partner = nullValue();
                $u_row->save();

                $uo_rows = $uo_model->matchAll([
                    'customer' => $partner_id,
                    'role' => 'partner'
                ]);
            } else if  ($partner_nr == 3) {
                $partner_id = $u_row->partner_2;
				$u_row->partner_2 = nullValue();
                $u_row->save();
			} else if  ($partner_nr == 4) {
                $partner_id = $u_row->partner_3;
                $u_row->partner_3 = nullValue();
                $u_row->save();
			}

			foreach($uo_rows as $uo_row)
				$uo_row->delete();

		}

        public function partnerUnlinkForRegistrantAction(){
            $this->disableView();

            $user_id = loginManager::data()->id;
            $u_model = new Users();
            $uo_model = new Objectusers();
            $u_row = $u_model->getById($user_id);

            $partner_id = $u_row->partner;


            if(is_numeric($partner_id)) {

                $u_row->partner = nullValue();
                $u_row->save();

                $u_row_partner = $u_model->getById($partner_id);
                $u_model->delete($u_row_partner['id']);

                $uo_rows = $uo_model->matchAll([
                    'customer' => $partner_id,
                    'role' => 'partner'
                ]);
            }

            if($uo_rows) {
                foreach($uo_rows as $uo_row)
                    $uo_row->delete();
            }

            $this->redirect('user/profile');

        }


		/**
		 * save
		 */
		public function saveAction() {
			$this->disableView();
			
			$commercial = $this->_getParam('commercial') != 'false' && $this->_getParam('commercial') != NULL ? true : false;
			if ($commercial){
				$form = new Form($this, 'user/addCommercial');
			} elseif (!$commercial){
				if($this->_getParam('type') == 'tenant'){
					$form = new Form($this,'user/add');
				} elseif ($this->_getParam('type') == 'investor'){
					$form = new Form($this, 'user/addInvestor');
				}	
			}

			$user = new User();
			$userid = is_numeric($this->_getParam('id')) ? $this->_getParam('id') : 'new';
			
			if($_POST['invoiceing']['overrule'])
				$_POST['invoiceing']['overrule'] = $_POST['invoiceing']['overrule'] == 'true';
				
			$post = $this->getRequest()->getPost();
			unset($post['type']);

			if($userid != 'new')
				if($post_check_id = $this->_getParam('type') == 'investor' ? Investors::getUserId($userid) : $userid);
					$post['id'] = $post_check_id;
	
			if($this->getRequest()->isPost() && $form->isValid($post)){
				$data = $form->getValues();
				
				$data['type'] = $this->_getParam('type'); #TODO
				
				if ($commercial && $this->_getParam('objectid')){
					$user->user = is_numeric($userid) ? $userid : false;

					if($uoid = $this->_getParam('uoid'))
						$data['uoid'] = $uoid;

					$userid = $user->editCommercial($data, $this->_getParam('objectid'));
				} elseif (!$commercial) {
					if ($this->_getParam('type') == 'tenant'){
						$userid = $user->editUser($data, $userid);
					} elseif ($this->_getParam('type') == 'investor'){
						$userid = $user->editInvestor($data, $userid);
					}
				}

				if(is_numeric($userid))
					echo json_encode($userid);

			} else {

				if($this->getRequest()->isPost())
					if($_POST['id'] != $post['id'])
						$form->populate(array('id' => $_POST['id']));

				$this->view->type = $this->_getParam('type');
				$this->view->useradd = $form;
				$this->render('edit');
			}
		}

		/**
		 * search
		 */
		public function searchAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$user = new User();

			$type = $_POST['type'];

			$users = $user->getUsers(false, $type, $_POST['term'], $_POST['limit']);

			if($type == 'company') {
				$more = $user->getUsers(false, 'tenant', $_POST['term'], $_POST['limit']);

			} elseif($type == 'tenant') {
				$more = $user->getUsers(false, 'company', $_POST['term'], $_POST['limit']);

			}

			if(!empty($more) && is_array($more))
				$users = array_merge($users, $more);

            $users = utf8_encode_recursive($users);

			echo json_encode($users);
		}

		/**
		 * details
		 */
		public function detailsAction() {
			$this->_helper->layout->disableLayout();
			///$this->_helper->viewRenderer->setNoRender(true);

			if($this->_getParam('commercial') == 'true') {
				$c = Company::getByDummyUserId($this->_getParam('id'));

				$output = array();

				if (!empty($c)) {
					$output['company'] = $c->name;

					if ($c->has('email_address'))
						$output['email'] = $c->email_address->address;

					if ($c->has('phone_primary'))
						$output['phone1'] = $c->phone_primary->number;

					if ($c->has('phone_secondary'))
						$output['phone2'] = $c->phone_secondary->number;

					if ($c->has('bank_account'))
						$output['bankaccount'] = $c->bank_account->iban ?: $c->bank_account->number;

					$users = array($output);
					$_POST['id'] = $c->dummy_user;
				}
			} elseif($this->getParam('is_combination') == 'true'){
				$id = $this->view->id = $this->getParam('id');
				$this->view->investor_combination = db()->fetchRow(db()->select()->from('investor_combination')->where('id = ?', $id));
				$this->render('details/investor_combination_link');
				return;
			} else {

				$user = new User();
				$users = $user->getUsers($_POST['id'], $_POST['type']);
			}

			$this->view->type = $_POST['type'];
			$this->view->users = $users[0];
		}

		/**
		 * Invoices
		 */

		public function invoicesAction() {

			if ($this->_getParam('download')) {
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);

				$select = db()->select()->from(array('t' => 'transactions'), array('*'))
                    ->joinLeft(array('ir' => 'invoices_run'),
                        'ir.id = t.invoicerun',			array('start'))
					->where("t.user = ?", loginManager::data()->id)
					->where("t.invoice = ?", $this->_getParam('download'));

                if (Settings::get('tenantportal_date_show_invoices') != '') {
                    $select->where("ir.start > ?", date('Y-m-d', strtotime(Settings::get('tenantportal_date_show_invoices'))));
                }

				$download = db()->fetchRow($select);

				if (empty($download))
					die('bestand bestaat niet');
				else {
					$invoice = new Invoice();
					$invoice->export($this->_getParam('download'));
				}

			}
			else {
				$invoices = new Invoice();
				$invoices->unpayed = $this->_getParam('onlyUnpayed') ? true : false;

				foreach(array('first' => 'ASC', 'last' => 'DESC') as $order_name => $order_type){
					$select = db()->select()
						->from(array('t' => 'transactions'), false)
						->joinLeft(array('ir' => 'invoices_run'),
							'ir.id = t.invoicerun',			array('start'))
						->joinLeft(array('p' => 'projects'),
							'p.id = ir.project',			false)
						->joinLeft(array('c' => 'corporations'),
							'c.id = p.corporation',			array('ideal_enabled'))
						->where("t.user = ?", loginManager::data()->id);

                    if (Settings::get('tenantportal_date_show_invoices') != '') {
                        $select->where("ir.start > ?", date('Y-m-d', strtotime(Settings::get('tenantportal_date_show_invoices'))));
                    }

                    $select->order('ir.start ' . $order_type);

					$result = db()->fetchRow($select);


					
					$this->view->$order_name = $result ? date('Y', strtotime($result['start'])) : date('Y');
				}				

				$year = $this->_getParam('year') ? $this->_getParam('year') : $this->view->latest;
				$i = $invoices->forUser(loginManager::data()->id, false, !$this->_getParam('widget') ? $year : false, $this->_getParam('widget') ? 5 : 0, true);



				// indicate if an invoice has been send to the collections office
				$irModel = new InvoicesReminds();
				foreach ($i as &$invoice) {
					if(intval($invoice['max_inr_id']) > 0) {
						$ir = $irModel->getById($invoice['max_inr_id']);
						$invoice['remind_to_collections_office'] = ($ir['penalty'] == '99' && $ir['send'] == '1');
					}
				}
				unset($invoice);

				$this->view->corp_ideal_enabled = $result['ideal_enabled'] === '1';
				$this->view->invoices = $i;
				$this->view->year = $year;

				if ($this->_getParam('widget')) {
					$this->render('invoices-dashboard');
				}
			}

		}

		public function documentsAction() {
			$userId = loginManager::data()->id;
			$document = new Document();
			$user = new User();

			if($_FILES['document'] && $_POST['upload-category'])
				if($this->documentUpload($_FILES['document'], $_POST['upload-category'])) {
                    $this->view->documentUploadSuccess = true;

                    if (loginManager::data()->info['profile_completion'] == '2' || Settings::get('modules_rental_complete_dossier_for_assign')) {
                        $this->redirect('user/contract');
					}

                    if(!Settings::get('modules_vidii')) {

                     	$namedocument = SupportDocumentsCategoriesTitles::getTitleById($_POST['upload-category']);
                        try {
                            new EmailOutgoing(array(
                                'to'		=> [
                                    'name' => 'Klantenservice',
                                    'email' => Settings::get('general_email'),
                                ],
                                'from'		=> [
                                    'name' => 'Klantenservice',
                                    'email' => Settings::get('general_email'),
                                ],
                                'subject'	=> 'Een gebruiker heeft een document geupload.',
                                'text'		=> 'De gebruiker heeft het volgende document '.$_FILES['document']['name'].' geupload onder documenttype '.$namedocument['title'].' Ga naar het <a href="/support/show/id/'.$userId.'/type/user/">Huurdossier</a>  om het document te controleren.'
                            ));

                        } catch (Exception $e) {
                            error_log('[error] Got exception when trying to inform customer about uploading a document on personal page, exception message: '.$e->getMessage());
                        }

                    }

                    
                    if (loginManager::data()->info['profile_completion'] == '2') {
                        $this->redirect('user/contract');
                    }
				}


			$docsParams = array();
			$docsParams['language'] = User::getLanguage($userId);
            $userlanguage = loginManager::data()->language;


			$this->view->hasContract = $user->hasContract($userId);
			$this->view->hasIdentification = $user->hasIdentification($userId);
			$this->view->documents = $document->getList($userId, 'user', $docsParams, true, loginManager::data()->info['commercial'], $userlanguage);
            $is_registrant = loginManager::data()->rights === 'registrant';

            $projectDocumentOverrideEnabled = in_array(
                loginManager::data()->project['tenant_page_document_upload_override'],
                ['yes']
            );

            if ($is_registrant) {
                $this->view->enableDocumentUpload =
                    $projectDocumentOverrideEnabled ||
                    loginManager::data()->info['profile_completion'] == '2';
            } else {
                $this->view->enableDocumentUpload =
                    $projectDocumentOverrideEnabled ||
                    Settings::get('modules_tenantLogin_document_upload');
            }

			$extform = new Extform();
			$this->view->extforms = $extform->getList();


            if (Settings::get('modules_tenantLogin_document_upload_pre_contracting') &&  $is_registrant) {
                if (loginManager::data()->info['profile_completion'] == '2' || (loginManager::data()->info['profile_completion'] == '9' && Settings::get('modules_rental_complete_dossier_for_assign') ) ) {
                    $this->_helper->redirector('contract', 'user');
                }
            }
		}

		public function dealsAction() {
			$userId = loginManager::data()->id;

			//get rental deals
			$rd = new RentalDeals();

			$this->view->userDeals = $rd->getByUserId($userId);

		}

        public function selectionAction() {
            $userId = loginManager::data()->id;

            //get rental deals
            $rd = new RentalDeals();

            $this->view->userDeals = $rd->getByUserId($userId);

        }

        public function viewingsAction() {
		    if (!loginManager::check()) {
                $this->getResponse()->setHttpResponseCode(401);
                return;
            }

            $userId = loginManager::data()->id;

            if (!$userId) {
                error_log('user controller viewings() userId is not set');
            }

            //get website deals
            $rd = new RentalDeals();

            $this->view->websiteDeals = $rd->getWebsiteDealsByUserId($userId);

        }

        public function reactionsAction() {
            $userId = loginManager::data()->id;

            //get website deals
            $rd = new RentalDeals();

            $this->view->websiteDeals = $rd->getWebsiteDealsByUserId($userId);

        }

		public function accountAction() {
            $userId = loginManager::data()->id;

            $userHasActiveDealsService = new \Controllers\User\UserHasActiveDealsService();
            $this->view->userHasActiveDeals = $userHasActiveDealsService->execute($userId);
            $userModel = new Users();
            $user = $userModel->getById(loginManager::data()->id);
			$this->view->payed_account_state = $this->getPayedAccountState($user);
            $this->view->payed_account_start_date = $user['payed_account_start_date'];
            if(in_array($this->view->language, ['en'])){
                $this->render('account_' . $this->view->language);
            }
		}

		public function searchProfileAction() {
            $this->view->headScriptHashed()
                ->appendFile('media/javascript/multiSelectFilter.js')
                ->appendFile('media/javascript/formHelpers/FormChangesWatcher.js')
                ->appendFile('media/javascript/formHelpers/PreventLeavingChangedForm.js');

			$profile = new User();
			$profile->user = loginManager::data()->id;
			$searchProfileForm = new Form($this, 'user/profile/searchProfile');

			if ($this->getRequest()->isPost() && $searchProfileForm->isValid($this->getRequest()->getPost())) {

			    $_POST['searchregion']['allowed_cities'] = getUnfilteredPost()['searchregion[allowed_cities][]'];

                if (!is_array($_POST['searchregion']['allowed_cities'])) {
                    $_POST['searchregion']['allowed_cities'] = [$_POST['searchregion']['allowed_cities']];
                }

                // quick sanitation
                foreach ($_POST as &$formPart) {
                    foreach ($formPart as $itemName => &$itemValue) {
                        if($itemName === 'allowed_cities'){
                            if(!(new ProjectsCities())->isValidValue($itemValue)){
                                $itemValue = null;
                            }
                        } else {
                            $itemValue = htmlspecialchars($itemValue, ENT_QUOTES);
                        }
                    }
                }

				new SearchProfile($_POST);

				$this->redirect('user/dashboard');
			}

			$spec_model = new ObjectsSpecificationsValues();
			$specifications = $spec_model->getSearchSpecificationsForUser(loginManager::data()->id);

			//get search profile
			$this->specifications = $specifications;
			$this->userprofile = loginmanager::data()->info['commercial'] === false ? $profile->getProfile() : $profile->getCommercialProfile();
            $this->userprofile['general']['rent_price'] = $this->userprofile['searchprofile']['rent_price'];
            
            foreach ($this->specifications as $specification_group) {
                foreach ($specification_group['types'] as $specification) {
                    if ($specification['filter_type'] === 'range' && $specification['value']) {
                        $specification['value'] = $specification['value_min'] . ',' . $specification['value_max'];
                    }
                    $this->userprofile['searchprofile'][$specification['type_name']] = $specification['value'];
                }
            }

            $userModelRow = (new Users())->getById(loginManager::data()->id);

            if ($userModelRow->allowed_cities) {
                $this->userprofile['searchregion'] = [
                    'allowed_cities' => json_decode($userModelRow->allowed_cities, true)
                ];
            }
            $searchProfileForm->populate($this->userprofile);

			$this->view->profile = $searchProfileForm;

		}

		public function extformDoneAction(){
			$this->view->instance_id = $instance_id = $this->getParam('instance');
			
			if(!is_numeric($instance_id)) die();

			$model = new ExtformInstance();
			$form = $model->getFormForInstance($instance_id);

			if(!$form) die();

			$this->render('extform/' . $form['name']);
		}
		
		public function uploadIdentificationAction() {
			$userId = loginManager::data()->id;
			
			$fileInfo = isset($_FILES['identification']) ? $_FILES['identification'] : false;

			$sdct = new SupportDocumentsCategoriesTitles();

			$identification_doc_title = $sdct->matchRow(array('identification' => true));

			if (!$fileInfo['tmp_name']) {
				$this->view->message = "Kies AUB een bestand alvorens op 'Legitimatie versturen' te klikken";
				
			} else {
				$extension = pathinfo($fileInfo['name'], PATHINFO_EXTENSION);
				switch(strtolower($extension)) {
					case 'pdf':
					case 'png':
					case 'jpg':
					case 'jpeg':
					case 'doc':
					case 'docx':
					case 'gif':
					case 'bmp':
						$validExt = true;
						break;
					default:
						$validExt = false;
						break;
				}
			
				if (!$validExt) {
					$this->view->message =
						"U kunt geen bestand uploaden dat eindig op '.{$extension}'.<br />"
						."Bestand moet zijn van het type PDF, PNG, JPEG, DOC, GIF of BMP."
					;
				} else {
					$destDir = Main::app()->getDir('document') . "user/{$userId}";

					$date = date('y-m-d');
					$destFile = "{$destDir}/{$date} - {$fileInfo['name']}";

					if (!is_readable($destDir))
						createFolder($destFile);
					
					$moved = move_uploaded_file($fileInfo['tmp_name'], $destFile);
					if ($moved) {
						$docTable = new SupportDocuments();
						$verTable = new SupportDocumentsVersions();
					
						$document = $docTable->createRow();
						$version  = $verTable->createRow();
						
						$document->type   = 'user';
						$document->map_to = $userId;
						$document->user   = $userId;
						
						$document->category = $identification_doc_title['document_category'];
						$document->titleid  = $identification_doc_title['id'];
						
						$document->title = $version->title = "Legitimatie";
						$document->description = $version->description =
							"Legitimatie door de gebruiker geupload"
						;
						
						$version->document = $destFile;
						$version->originalDate = new Zend_Db_Expr("NOW()");
						
						$documentId = $document->save();
						$version->support_documents = $documentId;
						$version->save();
						
						User::updateIdentification($userId, 1);

						$this->view->identificationUploadSuccess = true;
					}
				}
			}

			$this->documentsAction();
			$this->render('documents');
		}

		public function documentUpload($fileInfo, $title_id) {
			$userId = loginManager::data()->id;

			$title = DocumentCategory::getFile($title_id);
	
			$extension = pathinfo($fileInfo['name'], PATHINFO_EXTENSION);

			switch(strtolower($extension)) {
				case 'pdf':
				case 'png':
				case 'jpg':
				case 'jpeg':
				case 'doc':
                case 'docx':
				case 'gif':
				case 'bmp':
					$validExt = true;
					break;
				default:
					$validExt = false;
					break;
			}

			if (!$validExt) {
				return false;
			} else {
				$destDir = Main::app()->getDir('document') . "user/{$userId}";

				$date = date('y-m-d');
				$destFile = "{$destDir}/{$date} - {$fileInfo['name']}";
                $destFile = checkFilenameAvailability($destFile);

				if (!is_readable($destDir))
					createFolder($destFile);
				
				$moved = move_uploaded_file($fileInfo['tmp_name'], $destFile);
				if ($moved) {
					$docTable = new SupportDocuments();
					$verTable = new SupportDocumentsVersions();
				
					$document = $docTable->createRow();
					$version  = $verTable->createRow();
					
					$document->type   = 'user';
					$document->map_to = $userId;
					$document->user   = $userId;
					
					$document->category = $title['document_category'];
					$document->titleid  = $title_id;
					
					$document->title = $version->title = pathinfo($fileInfo['name'], PATHINFO_FILENAME);
					$document->description = $version->description = 'Document door de gebruiker geupload';
					
					$version->document = $destFile;
					$version->originalDate = new Zend_Db_Expr("NOW()");
					
					$documentId = $document->save();
					$version->support_documents = $documentId;
					$version->save();

					return true;
				}
			}

			return false;
		}	

		public function documentsdashboardAction() {
			$document = new Document();
            $docsParams = array();
            $docsParams['language'] = User::getLanguage(loginManager::data()->id);
            $userlanguage = loginManager::data()->language;
            $this->view->documents = $document->getList(loginManager::data()->id, 'user', $docsParams, true, loginManager::data()->info['commercial']);
		}

		/**
		 * dashboard
		 */
		public function dashboardAction() {
			$docsParams = array(
				'limit' => 3
			);

			$docsParams['language'] = User::getLanguage(loginManager::data()->id);
            $userlanguage = loginManager::data()->language;
			//document
			$document = new Document();
 			$d = $document->getList(loginManager::data()->id, 'user', $docsParams, true, loginManager::data()->info['commercial'], $userlanguage);


 			//user detail
			$user = new User();
			$i = loginmanager::data()->rights;
			$user = $user->getUsers(
			    loginManager::data()->id,
                $i
            );

			if ($i === 'registrant') {
				$user[0]['address']['address'] = $user[0]['street'];
				$user[0]['address']['address_number'] = $user[0]['addressnr'];
				$user[0]['address']['zipcode'] = $user[0]['zip']->userstring;
				$user[0]['address']['city'] = $user[0]['city'];
			} else if (!$user[0]['address']['uoid'] && !$user[0]['future_address']['uoid']) {
				$user[0]['address'] = $user[0]['future_address'];
			}

			//view
			$this->view->highlightComplaint = $this->_getParam('highlight_complaint');
			$this->view->user = $user[0];
			$this->view->userid = loginManager::data()->id;
			$this->view->documents = $d;

		}

		/**
		 * @throws Exception
		 */
		public function activateAccountAction() {
			$this->_helper->viewRenderer->setNoRender(true);
			$userModel = new Users();
			$currentUser = $userModel->getById(loginManager::data()->id);
			if ($currentUser === null) {
				throw new \Exception('Login data error');
			}
			$accountStatus = $this->getPayedAccountState($currentUser);
			switch ($accountStatus) {
				case ('never_active'):
					$paymentTotal = Settings::get('payed_account_initial_price');
					$mode = 'activeren';
					break;
				case ('expired'):
					$paymentTotal = Settings::get('payed_account_extension_price');
					$mode = 'verlengen';
					break;
                case ('active'):
                    $paymentTotal = Settings::get('payed_account_extension_price');
                    $mode = 'verlengen';
                    break;
				default:
                    error_log('MolliePayment No payment for User : ' . $currentUser . ' accountStatus :' . $accountStatus);
					return;
			}
			$paymentData = [
				'amount' => [
				    'value' => str_replace(',', '.', $paymentTotal),
                    'currency' => 'EUR'
                ],
				'description' => 'Betaald account ' . $mode . ' voor gebruiker ' . $currentUser->username . ', (' . $currentUser->id . ')',
				'metadata' => json_encode([
					'userId' => $currentUser->id,
				]),
				'activate_account_payment' => 1,
			];
			$service = new \Ideal\Mollie\Application\CreateMolliePaymentService();
			try {
				$service->create($paymentData);
			} catch (Exception $e) {
				error_log($e->getMessage());
				$this->render('transaction/ideal/error', null, true);
				return;
			}
		}

		private function getPayedAccountState($userRow) {
			if ($userRow->payed_account == 1) {
				return strtotime('-1 year') > strtotime($userRow->payed_account_start_date) ? 'expired' : 'active';
 			}
			return 'never_active';
		}

		public function activateAccountCallbackAction() {
			$postParams = $this->getRequest()->getParams();
			$paymentId = $postParams['id'];

            if (!$paymentId) {
                \Logger::add(['mollie-payments', 'paid-accounts', 'payment-status-webhook'], 'PaymentId not set');
                dieWithStatuscode(500);
            }

            try {
                $service = new \Ideal\Mollie\Application\ProcessActivateAccountPaymentService(new Users());
                $service->process($paymentId);
            } catch (Exception $e) {
                \Logger::add(['mollie-payments', 'paid-accounts', 'payment-status-webhook'], 'Problem with the processing part. PaymentId: ' . $paymentId . ' - Error: ' . $e->getMessage(), $e->getTraceAsString());
                dieWithStatuscode(500);
            }
		}

        public function easyLivingServicesAction()
        {
            $projectId = loginManager::data()->project['id'];

            $select = db()->select()
                ->from(['hp' => 'hospitality_projects'], false)
				->join(['hc' => 'hospitality_categories'], 'hc.id = hp.category_id', '*')
                ->where('hp.project_id = ?', $projectId);

            $categories = db()->fetchAll($select);

			$this->view->categories = $categories;
		}

        public function easyLivingServicesFaqAction()
        {
        	$url = "https://www.mvgm.nl/wp-json/ivaldi/v1/faqs/mijn-huurwoning";
            $data = file_get_contents($url);
            
            if ($data === false) {
            	$this->view->error = 'Er is iets mis gegaan tijdens het laden van de pagina. Probeert u het later nog eens';
            	$content = [];
			} else {
                $content = json_decode($data, true);
			}

			$this->view->content = $content;
		}

        public function elsServicesAction()
        {
			$categoryId = $this->getParam('category');
			$projectId = loginManager::data()->project['id'];

			$select = db()->select()
				->from(['hp' => 'hospitality_projects'], '*')
				->joinLeft(['hc' => 'hospitality_categories'], 'hc.id = hp.category_id', ['category_name' => 'name'])
				->where('hp.project_id = ?', $projectId)
				->where('hp.category_id = ?', $categoryId);

			$projects = db()->fetchRow($select);

			$servicesIds = explode(',', $projects['products']);

			$select = db()->select()
				->from(['hp' => 'hospitality_products'], '*')
				->joinLeft(['c' => 'company'], 'c.id = hp.company_id', [
					'company_name' => 'name',
					'company_description' => 'description',
					'hospitality_logo',
					'hospitality_url'
				])
				->joinLeft(['u' => 'users'], 'u.id = c.dummy_user', ['company_description' => 'u.comment'])
				->where('hp.id IN (' . implode_for_where_in($servicesIds) .')')
				->where('hp.active = ?', 1);

            $services = db()->fetchAll($select);

            // extract the company level
			$newData = [];

			foreach ($services as $service) {

				if (array_key_exists($service['company_id'], $newData))
					continue;

				$newData[$service['company_id']] = [
					'category_name' => $projects['category_name'],
					'company_logo' => $destination = Main::app()->getDir('hospitality') . $service['company_id'] . '/logo/' . $service['hospitality_logo'],
					'company_name' => $service['company_name'],
					'company_description' => $service['company_description'],
					'company_order_page_url' => $service['hospitality_url'],
					'products' => []
				];
			}

			// than on company level add the corresponding products to the company
			foreach ($services as $service) {
                $newData[$service['company_id']]['products'][] = [
                    'title' => $service['title'],
                    'short_description' => $service['short_description'],
                    'full_description' => $service['full_description'],
                    'rate' => new StringFormat($service['rate'], 'money')
                ];
			}

			$naw = db()->select()
				->from(['u' => 'users'], ['name' => 'u.rendered_name'])
				->where('u.id = ?', loginManager::data()->id)
				->joinLeft(['a' => 'address'], "a.type_id = u.id AND a.type = 'user'", ['street' => 'a.address', 'number', 'zipcode', 'city'])
				->joinLeft(['ea' =>'crm_email_address'], "ea.map_to = u.id AND ea.type = 'user'", ['email' => 'address'])
				->query()
				->fetch();

			// Everything in this array must be considered privacy sensitive!
			$exposedToOutsideWorld = [
				'name' => $naw['name'],
				'street' => $naw['street'],
				'number' => $naw['number'],
				'zipcode' => $naw['zipcode'],
				'city' => $naw['city'],
				'email' => $naw['email']
			];

			$httpQuery = http_build_query($exposedToOutsideWorld);

			$this->view->assign('naw', $httpQuery);
			$this->view->assign('services', $newData);
		}
		
		public function consumptionAction() {
			$this->view->headScriptHashed()->appendFile('media/javascript/tabs.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/chart/chart.js');

			//$userModel = new User();
			$objModel  = new Object();
			$consModel = new ObjectsConsumptions();
			$uoModel = new Objectusers();
		
			$userInfo = Zend_Auth::getInstance()->getIdentity();
			$userId = $userInfo->id;
		
			$uo = $uoModel->getByUserId($userId);
			$objectId = $uo['object'];
			$where = array('object' => $objectId);
			$consumption = $consModel->searchConsumption($where, $uo['from'], $uo['till']);
			
			$meterId = $this->_getParam('meter');
			$meters = $objModel->getMeters($objectId);
			if (!$meterId) {
				$firstMeter = reset($meters);
				$meterId = $firstMeter['meterid'];
			}

			if(!empty($meterId) && intval($meterId) > 0) {
				try {
					$userunit = db()->fetchRow(
						db()->select()->from(['m' => 'meters'], false)
						->joinLeft(['u' => 'units'], 'u.id = m.userunit', ['label'])
						->where('m.id = ?', $meterId)
					);
				} catch (Exception $e) {
					$userunit = ['label' => 'Eenheid'];
				}
			}

			$this->view->userunit = (!empty($userunit) && !empty($userunit['label']) && trim($userunit['label']) != false)? $userunit['label']: 'Eenheid';

			$ie_model = new InvoiceEstimate();
			$iew_model = new InvoiceEstimateWarning();
			if(($ie_model->hasEstimate($uo['id']) === false || !$ie_model->canBeShownToTenant($uo['id'])))
				$this->view->disableGraph = true;

			// disabled, requested by ab
			$this->view->disableGraph = !in_array($_SERVER['REMOTE_ADDR'], ['85.150.139.112', '83.162.254.233']);
			$this->view->consumption = $consModel->groupByMeter($consumption);
			$this->view->meters      = $meters;
			$this->view->meterId     = $meterId;
			$this->view->year 		 = $this->_getParam('year') ? $this->_getParam('year') : date('Y');
		}

		public function consumptionGraphAction(){
			$user_object_id = $this->_getParam('userObjectId');
			$year = $this->_getParam('year');

			$invoices_model = new Invoices();
			$user_object_model = new Objectusers();
			$ied_model = new InvoiceEstimateDatum();
			$ie_model = new InvoiceEstimate();

			$this->view->user_object_row = $user_object_model->getById($user_object_id);

			if(!$ie_model->canBeShownToTenant($user_object_id))
				return $this->render('consumption-graph-no-data');

			$start_year = $ied_model->getStartYear($user_object_id);

			if(!$start_year)
				return $this->render('consumption-graph-no-data');

			$this->view->yearRange = range($start_year, date('Y'));

			if(!in_array($year, $this->view->yearRange)) return;

			$amounts = array(
				'advance_current_year' => $ied_model->getForYear($user_object_id, $year, true),
				'consumption_current_year' => $ied_model->getForYear($user_object_id, $year, false),
				'consumption_previous_year' => $ied_model->getForYear($user_object_id, $year-1, false),
			);

			foreach($amounts as $amount_title => $amount)
				$this->view->$amount_title = $amount;

			$this->setGraphMinMax($amounts);
			$this->setGraphTotals($amounts);
		}

		public function consumptionReviewGraphAction() {
			$user_object_id = $this->_getParam('userObjectId');
			$year = $this->_getParam('year');
			$meterId = $this->_getParam('meter');
			$this->view->userunit = $userUnit = $this->_getParam('userunit');

			if(empty($user_object_id) || intval($user_object_id) <= 0)
				return [];

			if(empty($year) || intval($year) <= 2000 || intval($year) > (intval(date('Y')) + 1))
				return [];

			$cLib = new Consumption();
			$user_object_model = new Objectusers();
			$ied_model = new InvoiceEstimateDatum();
			$mModel = new Meters();
			$ocvModel = new ObjectsComponentsVersions();
			$ocvaModel = new ObjectsComponentsValues();
			$cModel = new Components();

			$this->view->user_object_row = $usersObjects = $user_object_model->getById($user_object_id);

			$knownConsumptionPeriod = $cLib->getStoredDateRange($user_object_id, $meterId);
			$fromStamp = (strtotime($usersObjects->from) != false && strtotime($usersObjects->from) > $knownConsumptionPeriod['from'])?
					strtotime($usersObjects->from): $knownConsumptionPeriod['from'];

			$start_year = date('Y', $fromStamp);

			if(!$start_year)
				return $this->render('consumption-graph-no-data');

			$this->view->yearRange = range($start_year, date('Y'));

			if(!in_array($year, $this->view->yearRange)) return;
			$this->view->year = $year;

			$from = strtotime("01-01-$year");
			$till = strtotime("31-12-$year");

			// euro waarden ophalen
			$r2Model = new Ratesheets2Table();
			$ratesheet = $r2Model->forObject($usersObjects->object);
			if(empty($ratesheet))
				return $this->render('consumption-graph-no-data');

			$iLib = new Indexes();
			$meter = $mModel->getById($meterId);

			$getProducts = function($datestamp = false) use($iLib, $meter, $ratesheet) {
				$datestamp = !empty($datestamp)? $datestamp: time();
				$ratesheetProducts = [];
				foreach($ratesheet->categories as $category) {
					if($category->has('values')) {
						foreach($category->values->toArray() as $value){
							if($category->component->id !== $meter['component'])
								continue;

							$quarter = dateToQuarter($datestamp);
							$indexed = $iLib->calculateSimple($value['id'], $quarter['year'], $quarter['quarter'], $category->component->category);

							if(!isset($indexed['amount']))
								$indexed = $iLib->calculateSimple($value['id'], $quarter['year'], $quarter['quarter']-1, $category->component->category);

							$value['stairType'] = $category['stairType'];
							$value['value'] = round($indexed['amount'], 5);

							$ratesheetProducts[] = array_merge(array('component' => $category->component->id), $value);
						}
					}
				}

				$mostRecentProduct = [];
				foreach ($ratesheetProducts as $i => $rsProduct) {
					if(empty($mostRecentProduct) || strtotime($mostRecentProduct['date']) < strtotime($rsProduct['date']))
						$mostRecentProduct = $rsProduct;
				}
				return $mostRecentProduct;
			};

			$mostRecentProduct = $getProducts();

			if(empty($mostRecentProduct))
				return $this->render('consumption-graph-no-data');

			$oldestMoneyDate = strtotime($mostRecentProduct['date']);
			$valueMultiplier = floatval($mostRecentProduct['value']);
			$unitMultiplier = ($userUnit == 'kWh')? 0.0036: 1;

			$currentValues = $cLib->getCustomerGraphData($user_object_id, $from, $till, $meterId);

			// for "warmth" meters, missing meter values should be spread differently than for others
			$component = $cModel->getById($meter->component);
			$productName = $component->product->name;

			$DDAverages = [];
			if($productName == 'warm') {
				$ddModel = new DegreeDays();
				$DDAverages = $ddModel->getAveragesForAllMonths();
				$DDAverages = array_values($DDAverages);

				// prepend one empty value because the $currentValues array is 1-based
				array_unshift($DDAverages, 0);
			}

			// check if we have any gaps in our meter values, and interpolate them
			for ($month=1; $month < 13; $month++) {
				$value = $currentValues[$month];

				// if a value is missing, seek until you find the next available one and measure the difference
				// between the last known value and the next one, so it can be spread over the "empty" months
				if(is_null($value) && intval($emptyCount) <= 0) {
					$lastVal = max($currentValues[$month-1], 0);

					$noNextValFound = false;
					$nextVal = null;
					$emptyCount = 1;
					$DDTotal = $DDAverages[$month];
					for ($i=($month+1); $i < 13; $i++) {
						if(!is_null($currentValues[$i])) {
							$nextVal = $currentValues[$i];
							break;
						} else {
							$DDTotal += $DDAverages[$i];
							$emptyCount++;
						}
					}

					if(is_null($nextVal))
						$noNextValFound = true;

					$totalDiff = $nextVal - $lastVal;
					$monthDiff = $totalDiff / $emptyCount;
				}

				if(is_null($value) && !$noNextValFound) {
					// for warmth meters, use degree days to spread the total difference
					// proportionally. Otherwise, spread it equally.
					if($productName == 'warm') {
						$weight = $DDAverages[$month] / $DDTotal;
						$currentValues[$month] = $currentValues[$month-1] + ($totalDiff * $weight);
					} else {
						$currentValues[$month] = $currentValues[$month-1] + $monthDiff;
					}
					$emptyCount--;
				}
			}

			$uoStartStamp = strtotime($usersObjects['from']);
			$uoEndStamp = strtotime($usersObjects['till']);
			$nowEndStamp = strtotime('today');
			for ($month=1; $month < 13; $month++) {
				$currentStamp = strtotime("01-$month-$year");

				if($currentStamp < $uoStartStamp || ($uoEndStamp != false && $currentStamp > $uoEndStamp) || $currentStamp > $nowEndStamp)
					$currentValues[$month] = null;
			}


			$currentMoneyValues = [];
			foreach ($currentValues as $key => $curValue) {
				$onDate = strtotime('+'.($key-1).' months', $from);

				if($onDate < $oldestMoneyDate || is_null($curValue)) {
					$currentMoneyValues[$key] = null;
					continue;
				}

				// calculate the monetary value of the given consumption, converting van kWh to GJ (if needed)
				$currentMoneyValues[$key] = round(($curValue * $valueMultiplier * $unitMultiplier), 2);
			}

			$amounts = [
				'consumption_current_year' => ['actual' => $currentValues],
				'consumption_previous_year' => false,
				'advance_current_year'		=> ['advance_actual' => $currentMoneyValues],
				'advance_previous_year'		=> false,
			];

			$lastYear = $year - 1;
			if(in_array($lastYear, $this->view->yearRange)) {

				$lastFrom = strtotime("01-01-$lastYear");
				$lastTill = strtotime("31-12-$lastYear");

				$amounts['consumption_previous_year']['actual'] 
					= $lastYearsValues = $cLib->getCustomerGraphData($user_object_id, $lastFrom, $lastTill, $meterId);

				$lastYearsMoneyValues = [];
				foreach ($lastYearsValues as $key => $lastYValue) {
					$onDate = strtotime('+'.($key-1).' months', $lastFrom);

					if($onDate < $oldestMoneyDate || is_null($lastYValue)) {
						$lastYearsMoneyValues[$key] = null;
						continue;
					}

					// calculate the monetary value of the given consumption, converting van kWh to GJ (if needed)
					$lastYearsMoneyValues[$key] = round(($lastYValue * $valueMultiplier * $unitMultiplier), 2);
				}

				$amounts['advance_previous_year']['advance_actual'] = $lastYearsMoneyValues;
			}

			foreach($amounts as $amount_title => $amount)
				$this->view->$amount_title = $amount;

			$this->setGraphMinMax($amounts);
			$this->setGraphTotals($amounts);
		}



		protected function setGraphTotals($amounts){
			$graph_min = false;
			$graph_totals = array(
				'advance' => 0,
				'consumption' => 0
			);

			foreach($amounts as $amount_title => $amount){

				if(strpos($amount_title, 'current_year') === false) continue;

				$type = strpos($amount_title, 'advance') === false ? 'advance' : 'consumption';

				foreach($amount as $amount_type)
					if(isset($amount_type[12]))
						$graph_totals[$type] = $amount_type[12];

			}

			$this->view->graph_totals = $graph_totals;
		}

		protected function setGraphMinMax($amounts){
			$graph_min = false;
			$graph_max = false;

			foreach($amounts as $amount_title => $amount){
				foreach((array) $amount as $amount_type)
					foreach((array) $amount_type as $amount_type_value)
						if(!is_null($amount_type_value) && $amount_type_value !== false){
							if($amount_type_value > $graph_max || $graph_max === false)
								$graph_max = $amount_type_value;

							if($amount_type_value < $graph_min || $graph_min === false)
								$graph_min = $amount_type_value;
						}
			}

			$this->view->graph_min = floor($graph_min/100) * 100;
			$this->view->graph_max = ceil($graph_max);
		}

		public function setConsumptionNotificationAction() {
			$this->disableView();
			$userId = $this->_getParam('user');
			$notify = $this->_getParam('notify');

			if(empty($userId) || intval($userId) <= 0)
				dieWithStatuscode(404);

			$uModel = new Users();

			$user = $uModel->getById($userId);

			if(empty($user) || !is_object($user))
				dieWithStatuscode(404);

			$user->consumption_notification = ($notify === 'true');
			$user->save();
		}
		
		public function searchRecipientsAction() {
			$this->disableView();
			$uModel = new User();
			
			$recipients = array();
			$q = isset($_POST['value']) ? $_POST['value'] : (isset($_POST['search']) ? $_POST['search'] : false);
			if ($q) {
                $args = [
                    'name' => $q,
                    'withMobilePhone' => true,
                    'verbose' => true,
                ];

                $recipients = $uModel->find($args);

                $recipients = orderArrayByLevenstein($recipients, $q);

                $formattedRecipients = [];
                foreach ($recipients as $recipientKey => $recipient) {
                    $formattedRecipients[] = [
                        $recipient['id'],
                        $recipient['buildname'],
                    ];

                    if ($recipientKey === 10) {
                        break;
                    }
                }
			}
			
			echo json_encode($formattedRecipients);
		}

		/**
		 * Redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function indexAction() {
			$this->_helper->redirector('list', 'user');
		}

		/**
		 * Lists all Users
		 * 
		 * @return null
		 */
		public function listAction() {
            ini_set('memory_limit', '800M');

			$tenantLabel = Settings::get('resident_label') ?: 'Huurder';
			$tenantLabel = strlen($tenantLabel) < 1? 'Huurder': $tenantLabel;

			$this->view->Breadcrumbs()
				->addCrumb('Personen overzicht', '');

            $params = [];

            if (!is_null($this->getParam('buildName'))) {
                $params['buildName'] = $this->getParam('buildName');
            }


            $this->view->modelListView('Users', $params)

				->setTypes(array(
                        'id',
                        'usertype',
						'buildName',
						'company_name',
						'email',
						'address',
						'a_number',
                        'build',
						'zipcode',
						'city',
						'p_name' => 'hidden',
					)
				)

                ->addFormat('buildName', function($value, $item){
                    return $item['id'] > 0 ? '<a href="support/show/id/' . $item['id'] . '/type/user">' . $value . '</a>' : '-';
                })

				->addFormat('usertype', static function($value) use ($tenantLabel) {
					switch ($value) {
						case 'tenant':
							return  $tenantLabel;
							break;

						case 'registrant':
							return 'Lead';
							break;

                        case 'employee':
                            return 'Medewerker';
                            break;

						default:
							return 'Overig';
							break;
					}
				})

				->setOptions(array(
					'item_title' => 'Gebruiker',
				))

				->addButtons(array(
					'edit-crm' => '',
				),

				array(
					'id' => 'id',
					'redirect' => 'true'
				))

				->setFilters(array(
					'usertype' => [
						'title' => 'Type',
						'renderSeparately' => true,
					],
					'buildName' => ['type' => 'input', 'preApplied' => true],
					'company_name' => array('type' => 'input'),
					'number' => array('type' => 'input'),
					'p_name' => array('title' => 'Project','renderSeparately' => true),
				))

				->render($this);
		}


		/**
		 * @Inject
		 * @var \ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\UpdateEmployeeInstructionsByUserService
		 */
		private $updateEmployeeInstructionsByUserService;
		/**
		 * @Inject
		 * @var \ReceptionTelephoneSupport\Application\Service\EmployeeInstruction\GetEmployeeInstructionsByUserService
		 */
		private $getEmployeeInstructionsByUserService;

		/**
		 * Edit the User indicated by the id parameter
		 * using an EditView
		 * 
		 * @return null
		 */
		public function editCrmAction() {
			$this->view->headScriptHashed()
				->appendFile('media/javascript/multiSelectFilter.js')
				->appendFile('media/javascript/multirange.js');

			$this->view->headLink()->appendStylesheet('media/style/multirange.css');

			if($_POST && is_null($this->_getParam('type')))
				$this->setParam('type', $_POST['usertype_form']['usertype'] == 'user' ? 'tenant' : $_POST['usertype_form']['usertype']);

			$breadcrumb_type = !is_null($this->_getParam('type')) ? 'type/' . $this->getParam('type') . '/' : '';

			if(!$this->isAjaxRequest)
				$this->view->Breadcrumbs()
					->addCrumb('Personen overzicht', 'user/list/' . $breadcrumb_type)
					->addCrumb('Persoon aanpassen/toevoegen', '');
			$users = new Users();
			$ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) ? $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest' : false;

			// this is to have the userId and whether deleting this user
			// is allowed available in the form through
			// $this->getOwner()->id / deletionAllowed
			$this->view->userid = $this->userid = $this->_getParam('id');
			$this->view->type = $this->type = $this->_getParam('type');
			$this->view->uoid = $this->uoid = $this->_getParam('uoid');
			$this->view->deletionAllowed = $this->deletionAllowed = $users->deleteAllowed($this->_getParam('id'));

			$controller = $this;

			$this->view->form = $this->view->EditView($this, 'Users', array('id' => $this->_getParam('id'), 'uoid' => $this->_getParam('uoid')))
				// set options
				->setOptions(array(
					'render_to_controller' => false,
					'redirect_to' => $ajax ? false : 'breadcrumbs',
					'loadHandler' => function($editView, $params)  {

                        if (Settings::get('module_reception_telephone_support_enabled')) {
                            if (isset($params['id'])) {
                                $userId = $params['id'];
                                $employeeInstructionsModel = $this->getEmployeeInstructionsByUserService->execute($userId);

                                if ($employeeInstructionsModel) {
                                    $editView->data['reception_telephone_support_form'] = [];
                                    $editView->data['reception_telephone_support_form']['representation_employee_instructions'] = $employeeInstructionsModel->getRepresentationInstructions();
                                }
                            }
                        }
					},
					'postHandler' => function($post, $params){
						$u_model = new Users();
						$u_model->save_return_type = 'inserted_id';

						if(isset($params['delete']) && isset($params['general']['id'])) {
							if( $u_model->delete($params['general']['id']) !== false ) {
								$redirector = new Zend_Controller_Action_Helper_Redirector();
								$redirector->direct('list', 'user', null, ['type' => $post['usertype_form']['usertype']]);
							} else {
								return false;
							}

						} else {

							if (Settings::get('module_reception_telephone_support_enabled')) {
							    if (isset($params['id'])) {
                                    $userId = $params['id'];

                                    $value = $post['reception_telephone_support_form']['representation_employee_instructions'];

                                    try {
                                        $this->updateEmployeeInstructionsByUserService->execute([
                                            'representation_instructions' => $value
                                        ], $userId);
                                    } catch (\Exception $exception) {
                                        error_log('module_reception_telephone_support_enabled by id '
                                            . $userId
                                            . ' :'
                                            . $exception->getMessage());
                                    }
                                }

							}

							if($userId = $u_model->save($post, $params)){
								$this->view->userid = $userId;

								if(isset($params['employee_form']) && isset($params['employee_form']['company']) && intval($params['employee_form']['company']) > 0
								&& isset($params['usertype_form']['usertype']) && $params['usertype_form']['usertype'] === 'employee' )
									$this->_helper->redirector('edit', 'company', null, ['id' => $params['employee_form']['company'], ]);


								$spec_model = new ObjectsSpecificationsValues();
								$spec_model->setFilterOgType('private');

								$specification_post_values = [];
								foreach( $spec_model->getSearchSpecificationsForUser($userId) as $specification_group_id => $specification_group) {
									foreach ($specification_group['types'] as $specification_type_id => $specification_type)
										foreach(['', '_low', '_high'] as $spec_type)
											if(isset($params['object_specifications_' . $specification_group_id][$specification_type['type_name'] . $spec_type]))
												$specification_post_values[$specification_type['type_name'] . $spec_type] = $params['object_specifications_' . $specification_group_id][$specification_type['type_name'] . $spec_type];
								}

								$spec_model = new ObjectsSpecificationsSearchValues();
								$spec_model->saveSpecificationsForUser($userId, $specification_post_values);
								/*
								$loyalty4G = new Loyalty4G($userId);
								$loyalty4G->syncUser();
								*/

								return true;
							} else {
								return false;
							}
						}
					}
				))

				->render($this);
		}

		/**
		 * Deletes the User indicated by the id parameter
		 * and redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function deleteAction() {
			$this->disableView();
 
			$u = new Users();
 
			if($u->delete($this->_getParam('id')) !== false)
				$this->_helper->redirector('list', 'user');
			else
				$this->_helper->redirector('edit', 'user', null, ['id' => $this->_getParam('id'), ]);
		}

		/**
		 * If requested with a userId as an 'id' parameter and a file in
		 * the $_FILE['templateFile'] array, this method tries to use the given
		 * file as a template and fills it with data relevant to the user with
		 * the given userId.
		 * To further specify which information should be used, it also accepts 
		 * an object id (as param 'object') or a users_objects id (as param 'uoId').
		 * 
		 * @return echo's a pdf document to the output
		 */
		public function renderTemplateToPdfAction() {
			$this->disableView();
			$params = $this->getRequest()->getParams();

			// input safety checks
			if((!is_numeric($params['id']) && !is_numeric($params['uoId'])) || !isset($_FILES)
				|| !is_array($_FILES) || !isset($_FILES['templateFile']['tmp_name']) ) {
				die();
			}

			// prepare/rename/check some vars
			$noPdf = $params['noPdf'] == '1';
			$userId = $params['id'];
			$templateFilename = realpath($_FILES['templateFile']['tmp_name']);
			$filename = pathinfo($_FILES['templateFile']['name'], PATHINFO_FILENAME). ($noPdf ? '.doc' : '.pdf');

			$object = $params['object'];
			$usersObjectsModel = new Objectusers();

			// get the correct user-object to fill the template with
			if(is_numeric($params['uoId'])) {
				$userObject = $usersObjectsModel->get($params['uoId']);

			} elseif (is_numeric($params['object'])) {
 				$userObject = $usersObjectsModel->getByUserId($userId,$object);

 			} else {
 				$userObject = $usersObjectsModel->getByUserId($userId);
			}
			$uoId = $userObject['id'];

			// get the data required by the template and render it
			$conTemplate = new ContractTemplate($uoId, $templateFilename);
			$conTemplate->noPdf = $noPdf;
			$conTemplate->render();

			// return the file to the user
			$conTemplate->browserOutput($filename);
		}

		/**
		 * Allows public access to a docx-template based contract for a limited time.
		 * The hash parameter ensures only the contract for the given users_objects id
		 * can be accessed until the given time.
		 * 
		 */
		public function publicContractAction() {
			$this->disableView();
			$uo_id = $this->_getParam('uoid');
			$time = $this->_getParam('time');
			$inputHash = $this->_getParam('hash');

			if(intval($uo_id) <= 0 || intval($time) <= 0 || empty($inputHash)) {
				ob_clean();
				header($_SERVER["SERVER_PROTOCOL"].' 404', true, 404);
				die();
			}

			$privateHash = sha1($uo_id.'_'.$time.'_'.Settings::get('general_company_shortname').'_omniboxx');

			$uo = Objectusers::get($uo_id);

			if($inputHash !== $privateHash || intval($time) < time() || empty($uo) || intval($uo['object']) <= 0) {
				ob_clean();
				header($_SERVER["SERVER_PROTOCOL"].' 404', true, 404);
				die();
			}

			$user_lib = new User();
			$aModel = new Address();

			$address = $aModel->get($uo['object'], 'object');
			$language = User::getLanguage($uo['customer']);
			$prefix = $language === 'en'? 'Rental contract': 'Huurovereenkomst';

			$filename = $prefix.' '.$address['address'].' '.$address['number'];
			$user_lib->generateContract($uo_id, $filename);
		}

		public function registrationRequestAdditionalDocumentsAction() {
			$this->disableView();
			$userId = $this->getParam( 'id' );
            $objectId = $this->getParam('object_id');
			$service = new Controllers\User\RegistrationRequestAdditionalDocumentsService();

			try {
				$service->execute($userId, $objectId);
			} catch ( InvalidArgumentException $e ) {
			}

			$this->_redirect( 'support/show', [ 'id' => $userId, 'type' => 'user' ] );
		}

		public function registrationCancelAction() {
			$this->disableView();
			$userId = $this->getParam( 'id' );
			$objectId = $this->getParam('object_id');
			$service = new Controllers\User\RegistrationCancelService();

			try {
				$service->execute($userId, $objectId);

			} catch ( InvalidArgumentException $e ) {
			}

			$this->_redirect( 'support/show', [ 'id' => $userId, 'type' => 'user' ] );
		}

		public function registrationFinalizeAction() {
		    // called by vidii files approved button -> other action then normal workflow files approved
			$this->disableView();
			$userId = $this->getParam('id', 0);

			$service = new Controllers\User\RegistrationFinalizeService();

			try {
				$service->execute( $userId );
			} catch ( Exception $e ) {
				$this->_redirect('user/list');
			}

			$this->_redirect('support/show', ['id' => $userId, 'type' => 'user']);
		}


        public function registrationFinalizeNoContractingAction() {
            $this->disableView();
            $userId = $this->getParam('id', 0);

            $service = new Controllers\User\RegistrationFinalizeService();

            try {
                $service->execute($userId, false);
            } catch (Exception $e) {
                $this->_redirect('user/list');
            }

            $this->_redirect('support/show', ['id' => $userId, 'type' => 'user']);
        }

		public function registrationFilesApprovedAction() {
			$this->disableView();
			$userId = $this->getParam('id', 0);

			$service = new Controllers\User\RegistrationFilesApprovedService();

			try {
				$service->execute( $userId );
			} catch ( Exception $e ) {
				$this->_redirect('user/list');
			}

			$this->_redirect('support/show', ['id' => $userId, 'type' => 'user']);
		}



		public function notSupportedAction() {
			$this->_helper->layout->disableLayout();

			loginManager::logout();
		}

    public function leadWelcomeEmailAction()
    {
        $this->_helper->viewRenderer->setNoRender(true);

        $branchLocationId = $this->getParam('branch_location_id');
        $numberOfEmails = $this->getParam('number_of_emails', 100);
        $payedAccountOnly = $this->getParam('payed_account_only', true);

        if (!$branchLocationId) {
            die('Missing argument: branch_location_id');
        }

        $service = new \User\LeadWelcomeEmailService(new Users());
        $service->execute($branchLocationId, $payedAccountOnly, $numberOfEmails);
    }


    public function deleteMyAccountAction()
    {
        $userId = loginManager::data()->id;

        $userHasActiveDealsService = new \Controllers\User\UserHasActiveDealsService();
        $userHasActiveDeals = $userHasActiveDealsService->execute($userId);

        //check and prevent delete if someone enters this url by accident and has active deals
        if ($userHasActiveDeals || !Settings::get('modules_rental') || loginManager::data()->rights != 'registrant') {
            $this->_redirect('user/account');
        }
        else
        {
            $rental_deal_model = new \RentalDeals();
            $website_deal_models_select = $rental_deal_model
                ->select()
                ->where('(user_id = "' . $userId . '")')
                ->where('website_deal = ?', 'yes')
                ->where('cancelled = ?', 0)
                ->where('archived = ?', 0);

            $website_deals = $rental_deal_model->fetchAll($website_deal_models_select);

            foreach ($website_deals as $website_deal) {
                $website_deal
                    ->setFromArray([
                        'cancelled' => true,
                        'cancel_date' => date('Y-m-d H:i:s'),
                        'cancel_by_user' => \loginManager::data()->id,
                    ])
                    ->save();
            }

            $service = new \User\DisableInactiveLeadService(
                new Users(),
                new CRMEmailAddress(),
                new PhoneNumber(),
                new SupportDocuments(),
                new SupportDocumentsVersions(),
                new RentalDeals()
            );

            try {
                $service->execute([$userId], false);
            } catch (Exception $exception) {
                $this->_redirect('user/account');
            }

            $this->_redirect('user/logout');


        }


    }


    public function disableInactiveLeadsAction()
    {
        $this->_helper->viewRenderer->setNoRender(true);

        $findInactiveLeadsService = new \User\FindInactiveLeadsService();

        $disableInactiveLeadService = new \User\DisableInactiveLeadService(
            new Users(),
            new CRMEmailAddress(),
            new PhoneNumber(),
            new SupportDocuments(),
            new SupportDocumentsVersions(),
            new RentalDeals()
        );

        $numberOfLeads = $this->getParam('number') ?: 2000;

        $inactiveLeadUserIds = $findInactiveLeadsService->execute(
            $numberOfLeads,
            $this->getParam('branch_location_id')
        );

        foreach ($inactiveLeadUserIds as $inactiveLeadUserId) {
            try {
                $disableInactiveLeadService->execute($inactiveLeadUserId, true);
            } catch (Exception $exception) {
                p($exception->getMessage());
            }
        }
    }

	}

?>
