<?php

use WatsonHolmes\Application\Service\OneDayCacheDecorator;
use WatsonHolmes\Application\Service\ViewLeadsService;
use WatsonHolmes\Application\Service\ViewObjectsService;
use WatsonHolmes\Application\Service\ViewProjectsService;
use WatsonHolmes\Application\Service\ViewRentalDealsService;
use WatsonHolmes\Application\Service\ViewRentalDealsStatusService;
use WatsonHolmes\Application\Service\ViewRentalDealStatusTypesService;
use WatsonHolmes\Infrastructure\Domain\Model\Lead\SQLDataLeadFactory;
use WatsonHolmes\Infrastructure\Domain\Model\Lead\SqlLeadRepository;
use WatsonHolmes\Infrastructure\Domain\Model\Object\SqlObjectFactory;
use WatsonHolmes\Infrastructure\Domain\Model\Object\SqlObjectRepository;
use WatsonHolmes\Infrastructure\Domain\Model\Project\SqlProjectFactory;
use WatsonHolmes\Infrastructure\Domain\Model\Project\SqlProjectRepository;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDeal\SQLDataRentalDealFactory;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDeal\SqlRentalDealRepository;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDealStatus\SqlRentalDealStatusFactory;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDealStatus\SqlRentalDealStatusRepository;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDealStatusType\SQLRentalDealStatusTypeFactory;
use WatsonHolmes\Infrastructure\Domain\Model\RentalDealStatusType\SQLRentalDealStatusTypeRepository;
use WatsonHolmes\Infrastructure\Domain\Model\SimpleObjectToXmlTransformer;

class WatsonHolmesController extends GlobalController
{
    public function preDispatch()
    {
        $this->disableView();

        if (!$this->isCallerAuthorized()) {
            die('Caller is not authorized');
        }
    }

    private function isCallerAuthorized()
    {
        $authorizedIpAddresses = [
            '127.0.0.1',
            '*************', // Omniboxx web server
            '*************', // Menno Utrecht - Terwijde
            '**************', // MVGM
            '*************', // MVGM
            '*************', // MVGM
            '**************', // MVGM
            '************', // MVGM
            '*************', // Omniboxx - Vianen
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '141.105.123.37', // Watson and Holmes
            '141.105.123.36', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
            '**************', // Watson and Holmes
        ];

        return in_array($_SERVER['REMOTE_ADDR'], $authorizedIpAddresses, true);
    }

    public function objectsAction()
    {
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '2000M');
        ini_set('max_execution_time', 0);

        $objectRepository = new SqlObjectRepository(
            new SqlObjectFactory()
        );

        $viewObjectsService = new ViewObjectsService(
            $objectRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewObjectsService = new OneDayCacheDecorator($viewObjectsService);
        $viewObjectsService->setCacheName('objects');

        echo $viewObjectsService->execute();
    }

    public function projectsAction()
    {
        $projectRepository = new SqlProjectRepository(
            new SqlProjectFactory()
        );

        $viewProjectsService = new ViewProjectsService(
            $projectRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewProjectsService = new OneDayCacheDecorator($viewProjectsService);
        $viewProjectsService->setCacheName('projects');

        echo $viewProjectsService->execute();
    }

    public function leadsAction()
    {
        $pageNumber = $this->getParam('page');

        $leadRepository = new SqlLeadRepository(
            new SQLDataLeadFactory()
        );

        $viewLeadsService = new ViewLeadsService(
            $leadRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewLeadsService = new OneDayCacheDecorator($viewLeadsService);
        $viewLeadsService->setCacheName('leads');

        echo $viewLeadsService->execute($pageNumber);
    }

    public function rentalDealsAction()
    {
        $pageNumber = $this->getParam('page');

        $rentalDealRepository = new SqlRentalDealRepository(
            new SQLDataRentalDealFactory()
        );

        $viewRentalDealService = new ViewRentalDealsService(
            $rentalDealRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewRentalDealService = new OneDayCacheDecorator($viewRentalDealService);
        $viewRentalDealService->setCacheName('rental_deals');

        echo $viewRentalDealService->execute($pageNumber);
    }

    public function rentalDealStatusTypesAction()
    {
        $rentalDealStatusTypeRepository = new SQLRentalDealStatusTypeRepository(
            new SQLRentalDealStatusTypeFactory()
        );

        $viewRentalDealStatusTypesServices = new ViewRentalDealStatusTypesService(
            $rentalDealStatusTypeRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewRentalDealStatusTypesServices = new OneDayCacheDecorator($viewRentalDealStatusTypesServices);
        $viewRentalDealStatusTypesServices->setCacheName('rental_deal_status_types');

        echo $viewRentalDealStatusTypesServices->execute();
    }

    public function rentalDealsStatusAction()
    {
        $pageNumber = $this->getParam('page');

        $rentalDealStatusRepository = new SqlRentalDealStatusRepository(
            new SqlRentalDealStatusFactory()
        );

        $viewRentalDealsStatusService = new ViewRentalDealsStatusService(
            $rentalDealStatusRepository,
            new SimpleObjectToXmlTransformer()
        );

        $viewRentalDealsStatusService = new OneDayCacheDecorator($viewRentalDealsStatusService);
        $viewRentalDealsStatusService->setCacheName('rental_deals_status');

        echo $viewRentalDealsStatusService->execute($pageNumber);
    }
}
