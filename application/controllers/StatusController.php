<?

	class StatusController extends GlobalController {

		public function changeAction() {
			
			//status
			$ss = new SupportStatus();
			$s = $ss->fetchRow("type = 'complaint' AND map_to = " . $this->_getParam('mapTo') . "");
			if ($s->status != $this->_getParam('status')) {
				//status update
				$s = $ss->createRow();
				$s->user = loginManager::data()->id;
				$s->type = $this->_getParam('type');
				$s->map_to = $this->_getParam('mapTo');
				$s->status = $this->_getParam('status');
				$s->save();
			}

			//update last_status on parent
			if ($this->_getParam('type') == 'complaint') {
				$table = new SupportComplaints();
				$c = $table->fetchRow('id = \'' . $this->_getParam('mapTo') . '\'');
				$c->last_status = $this->_getParam('status');
				$c->closed_description = $this->_getParam('description');
				$c->save();
				
				// Mailtje sturen naar de gebruiker(s)
				$complaintType = $this->_getParam('complaint_type');
				if ( in_array($complaintType, ['malfunction', 'request']) ) {
					$cc = new Complaint();
					$cc->informUser($c->map_to, $c->type, $c, $s);
				}

			}

			//return status list
			$this->_forward('list', null, null, array(
				'mark' => array(
					'id' => $d->id,
					'type' => $d->type
				),
				'mapTo' => $this->_getParam('mapTo'),
				'type' => $this->_getParam('type'),
				'ajax' => true
			));

		}

		/**
		 * List of status
		 */
		public function listAction() {

			//dropdown form
			$dropdown = new Form($this, 'support/status');

			//list of all status
			$db = Zend_Db_Table::getDefaultAdapter();
			$select = $db->select()
				->from(array('ss' => 'support_status'), array('*'))
				->joinLeft(array('u' => 'users'),
				'u.id = ss.user', array('name' => 'CONCAT(u.firstname, \' \', u.name)'))
				->joinLeft(array('c' => 'company'),
				'c.id = ss.contact', array('contact_name' => 'name'))
				->joinLeft(array('sst' => 'support_status_types'),
				'sst.id = ss.status', array('status'))
				->where('ss.map_to = ?', $this->_getParam('mapTo'))
				->where('ss.type = ?', $this->_getParam('type'))
				->order('ss.date DESC');
			$list = $db->fetchAll($select);

			$this->view->list = $list;
			$this->view->descriptionOnClose = $this->_getParam('descriptionOnClose') == 'true';
			$this->view->mark = $this->_getParam('mark');
			$this->view->ajax = $this->_getParam('ajax');
			$this->view->mapTo = $this->_getParam('mapTo');
			$this->view->type = $this->_getParam('type');
			$this->view->dropdown = $dropdown;

		}

	}
