<?php

namespace Indexation;

class BaseIndexation extends \GlobalController
{
    public function preDispatch()
    {
        $this->view->extraWidth = true;
    }

    protected function getTypes($additionTypes = [])
    {
        $types = [
            'user_id' => ['title' => 'Huurder', 'width' => 'xlarge truncate'],
            'object_id' => ['title' => 'Object', 'width' => 'xxxlarge truncate'],
            'indexation_date' => ['title' => 'Datum', 'width' => 'xsmall'],
            'project_name' => ['title' => 'Project', 'width' => 'small hidden'],
            'objectgroup_name' => ['title' => 'Objectgroep', 'width' => 'small hidden'],
            'investor_name' => ['title' => 'Belegger', 'width' => 'small hidden'],
            'previous_amount' => ['title' => 'Oud ', 'width' => 'small'],
            'amount' => ['title' => 'Nieuw ', 'width' => 'small'],
            'index_type' => ['title' => 'Indexatie type', 'width' => 'large truncate'],
            'percentage_calculated' => ['title' => 'perc (%)', 'width' => 'xxxsmall truncate'],
            'minimum_indexation' => ['title' => 'Min', 'width' => 'xxxsmall truncate'],
            'maximum_indexation' => ['title' => 'Max', 'width' => 'xxxsmall truncate'],
            'indexation_status' => ['title' => 'Status', 'width' => 'xlarge truncate'],
            'preview' => ['title' => 'Preview', 'width' => 'xxxsmall'],
        ];

        return array_merge($types, $additionTypes);
    }

    protected function getFilters($additionFilters = [])
    {
        $filters = [
            'user_id' => ['type' => 'input'],
            'address' => ['type' => 'input'],
            'indexation_date' => [],
            'object_id' => ['type' => 'input'],
            'project_name' => [
                'renderSeparately' => true,
                'title' => 'Project',
                'type' => 'select',
                'order_by_title' => 'ASC',
            ],
            'objectgroup_name' => ['renderSeparately' => true, 'title' => 'Objectgroep', 'type' => 'select'],
            'investor_name' => ['renderSeparately' => true, 'title' => 'Belegger', 'type' => 'select'],
            'indexation_status' => [],
            'status_calculate' => []
        ];

        return array_merge($filters, $additionFilters);
    }

    protected function getListView($data, $types, $filters, $actions, $previewLocation, $legend)
    {
        return $this->view->ListView($data)
            ->addLegend($legend)
            ->setTypes($types)
            ->addFormat('user_id', 'user_support_link')
            ->addFormat('object_id', function ($value, $item) {
                return "<a href='object/edit/id/$value'>{$item['object_address']}</a>";
            })
            ->addFormat('indexation_date', 'date')
            ->addFormat('previous_amount', 'money')
            ->addFormat('amount', 'money')
            ->addFormat('indexation_status', function ($value, $item) {
                $status = [
                    'done' => 'Staat klaar',
                    'previous' => 'Staat klaar',
                    'missing_index_input' => 'Geen index beschikbaar',
                    'indexation_block' => 'Indexatie voor dit object is geblokeerd',
                    'sent_using_email' => 'Brief verstuurd per E-mail',
                    'sent_using_mail' => 'Brief verstuurd per post',
                ];

                return $status[$value];
            })
            ->addFormat('index_percentage', 'percentage')
            ->addFormat('preview', function ($value, $item) use ($previewLocation) {
                $isDoneIndexation = in_array($item['indexation_status'], ['done', 'previous']);

                if ($isDoneIndexation) {
                    return "<a href='$previewLocation/preview/invoice_id/{$item['id']}' class='xxxxsmall button pdf' target='_blank'></a>";
                }

                return '';
            })
            ->setFilters($filters)
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->addActions($actions)
            ->render($this);
    }
}
