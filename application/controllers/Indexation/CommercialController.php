<?php


use Indexation\Application\Service\ViewCommercialObjectListService;
use Indexation\Application\Service\CreateCommercialIndexationLetterRequest;
use Indexation\Application\Service\CreateCommercialIndexationLetterService;
use Indexation\Application\Service\PreviewIndexationLetterDecorator;
use Indexation\Application\Service\SendCommercialLetterService;
use Indexation\Domain\Service\IndexationLetterService;
use Indexation\Infrastructure\Domain\Model\IndexationLetter\LegacyRecipientRepository;
use Indexation\Infrastructure\Domain\Model\Variables\LegacyVariablesRepository;
use Indexation\Infrastructure\Domain\Service\SendIndexationLetterByPostService;
use Indexation\Infrastructure\Domain\Service\ZendSendIndexationLettersByEmailService;
use SharedKernel\Infrastructure\Domain\Service\LegacyStationaryPdfService;

class Indexation_CommercialController extends \Indexation\BaseIndexation
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Commerciële indexatiebrieven versturen');

        $data = (new ViewCommercialObjectListService())
            ->execute();

        $types = $this->getTypes();
        $filters = $this->getFilters();

        $actions = [
            'disabled_check' => function ($row) {
                return $row['indexation_status'] === 'Staat klaar';
            },
            'buttons' => [
                [
                    'title' => 'Brief versturen per E-mail',
                    'icon' => 'mail',
                    'action' => 'indexation_commercial/send-letters/send_method/email',
                    'confirm' => true,
                    'noAjax' => true
                ],
                [
                    'title' => 'Brief versturen per Post',
                    'icon' => 'mail',
                    'action' => 'indexation_commercial/send-letters/send_method/post',
                    'confirm' => true,
                    'noAjax' => true
                ]
            ]
        ];

        $previewLocation = 'indexation_commercial';
        $legend = "list-legend";

        $this->getListView($data, $types, $filters, $actions, $previewLocation, $legend);
    }

    public function previewAction()
    {
        $this->disableView();

        $invoiceId = $this->getParam('invoice_id');

        $service = new CreateCommercialIndexationLetterService(
            new LegacyRecipientRepository(),
            new LegacyVariablesRepository(),
            new IndexationLetterService(),
            new LegacyStationaryPdfService()
        );

        $service = new PreviewIndexationLetterDecorator($service);

        $service->execute(new CreateCommercialIndexationLetterRequest(
            $invoiceId
        ));
    }

    public function sendLettersAction()
    {
        $this->disableView();

        $invoiceIds = $this->getParam('ids');
        $sendMethod = $this->getParam('send_method');

        $createCommercialIndexationLetterService = new CreateCommercialIndexationLetterService(
            new LegacyRecipientRepository(),
            new LegacyVariablesRepository(),
            new IndexationLetterService(),
            new LegacyStationaryPdfService()
        );

        $service = new SendCommercialLetterService(
            $createCommercialIndexationLetterService,
            new ZendSendIndexationLettersByEmailService(),
            new SendIndexationLetterByPostService()
        );

        $service->execute($invoiceIds, $sendMethod);

        $this->_redirect('indexation_commercial/list');
    }
}
