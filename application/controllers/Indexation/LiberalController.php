<?php

use Indexation\Application\Service\CreateLiberalIndexationLetterRequest;
use Indexation\Application\Service\CreateLiberalIndexationLetterService;
use Indexation\Application\Service\PreviewIndexationLetterDecorator;
use Indexation\Application\Service\ViewLiberalObjectListService;
use Indexation\Application\Service\SendLiberalLetterService;
use Indexation\Domain\Service\IndexationLetterService;
use Indexation\Infrastructure\Domain\Model\IndexationLetter\LegacyRecipientRepository;
use Indexation\Infrastructure\Domain\Model\Variables\LegacyVariablesRepository;
use Indexation\Infrastructure\Domain\Service\SendIndexationLetterByPostService;
use Indexation\Infrastructure\Domain\Service\ZendSendIndexationLettersByEmailService;
use SharedKernel\Infrastructure\Domain\Service\LegacyStationaryPdfService;

class Indexation_LiberalController extends \Indexation\BaseIndexation
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Liberale indexatiesbrieven versturen');

        $data = (new ViewLiberalObjectListService())
            ->execute();

        $types = $this->getTypes();
        $filters = $this->getFilters();

        $actions = [
            'disabled_check' => function ($row) {
                return $row['indexation_status'] === 'Staat klaar';
            },
            'buttons' => [
                [
                    'title' => 'Brief versturen per E-mail/Post',
                    'icon' => 'mail',
                    'action' => 'indexation_liberal/send-letters/send_method/email',
                    'confirm' => true,
                    'noAjax' => true
                ],
                [
                    'title' => 'Brief versturen per Post',
                    'icon' => 'mail',
                    'action' => 'indexation_liberal/send-letters/send_method/post',
                    'confirm' => true,
                    'noAjax' => true
                ]
            ]
        ];

        $previewLocation = 'indexation_liberal';
        $legend = "list-legend";

        $this->getListView($data, $types, $filters, $actions, $previewLocation, $legend);
    }

    public function previewAction()
    {
        $this->disableView();

        $invoiceId = $this->getParam('invoice_id');

        $service = new CreateLiberalIndexationLetterService(
            new LegacyRecipientRepository(),
            new LegacyVariablesRepository(),
            new IndexationLetterService(),
            new LegacyStationaryPdfService()
        );

        $service = new PreviewIndexationLetterDecorator($service);

        $service->execute(new CreateLiberalIndexationLetterRequest(
            $invoiceId
        ));
    }

    public function sendLettersAction()
    {
        $this->disableView();

        $invoiceIds = $this->getParam('ids');
        $sendMethod = $this->getParam('send_method');

        $createLiberalIndexationLetterService = new CreateLiberalIndexationLetterService(
            new LegacyRecipientRepository(),
            new LegacyVariablesRepository(),
            new IndexationLetterService(),
            new LegacyStationaryPdfService()
        );

        $service = new SendLiberalLetterService(
            $createLiberalIndexationLetterService,
            new ZendSendIndexationLettersByEmailService(),
            new SendIndexationLetterByPostService()
        );

        $service->execute($invoiceIds, $sendMethod);

        $this->_redirect('indexation_liberal/list');
    }
}
