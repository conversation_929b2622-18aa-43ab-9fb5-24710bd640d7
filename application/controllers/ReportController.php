<?
	ini_set('memory_limit', '1024M');

	class ReportController extends GlobalController {

		public $transaction;

		public function preDispatch() {
			$this->transaction = new Transaction();

			$this->view->Breadcrumbs()->addCrumb('Rapporten', 'report/');

			$this->view->types = Project::getTypes();

			$no_extraWithActions = ['customer-support', 'customer-email'];
			$this->view->extraWidth = !in_array($this->view->pageInfo['action'], $no_extraWithActions);
		}

		public function indexAction() {

		}

		
		public function customerEmailAction() {

		}
		
		public function projectListAction() {
			
			$ir = new ReportTransaction();
			$p = new Project();
			$t = new Transaction();
			
			$this->view->lastUpdates = 	$t->lastUpdate();
			$this->view->filters =		$ir->filters;
			$this->view->reminds = 		$ir->getProjectList();
			//$this->view->fine = 		$ir->fines[$next];
			$this->view->next = 		$next;
			
			$this->render('project-list');
		}

		public function inputAction() {
			
			
			$typereport = $this->_getParam('type');
			
			$this->view->descriptions = ReportInput::getDescriptions(false, false);
			
			if ($typereport == 'tenants') {
				$this->view->tenantsort = $tenantsort = $this->setFilter('tenantsort');
				$tenantdirection = $this->setFilter('tenantdirection');
			} elseif ($typereport == 'objects') {
				$this->view->objectsort = $objectsort = $this->setFilter('objectsort');
				$objectdirection = $this->setFilter('objectdirection');
			}
			//redirect naar normale URL
			if ($this->getRequest()->isPost()) {
				$params = array(
					'type' => $this->getRequest()->getPost('type'),
					'invoiceableOnly' => $this->getRequest()->getPost('invoiceableOnly') == 0 ? false : true,
					'tenantsort' => $tenantsort,
					'objectsort' => $objectsort,
					'tenantdirection' => $tenantdirection,
					'objectdirection' => $objectdirection,
				);
				$this->_redirect('report/input', $params);
			}

			$this->view->Breadcrumbs()->addCrumb('Invoer', 'input/');

			$type = $this->setFilter('type');
			$invoiceableOnly = $this->setFilter('invoiceableOnly');

			$reports = array();

			if ($typereport == 'tenants') {
				$tempReport = ReportInput::model()
				->with('tenant', 'contactInfo', 'invoicepreferences', 'invoices')
				//->invoicableOnly($invoiceableOnly)
				->inExploitationOnly()
				->type($type)
				->get();

			} else {
				$tempReport = ReportInput::model()
				->with( 'delivery', 'meters', 'invoices')
				//->invoicableOnly($invoiceableOnly)
				->inExploitationOnly()
				->type($type)
				->get();

			}

			//groeperen en berekenen
			

			//p($tempReport ,'die');	
			
			if ($typereport == 'tenants') {
				$inputErrorsTenant = ReportInput::groupByProject($tempReport, 'tenant');
				$this->sortBy($tenantsort, $inputErrorsTenant, $tenantdirection);
			} elseif ($typereport == 'objects') {
				$inputErrorsObject = ReportInput::groupByProject($tempReport, 'object');
				$this->sortBy($objectsort, $inputErrorsObject, $objectdirection);
			}
      		
      		if ($typereport == 'tenants') {
				$reports['inputErrorsTenant'] = array(
					'reports' => array_slice($inputErrorsTenant, 0, 99),
					'paginated' => Zend_Paginator::factory($inputErrorsTenant),
					'sort' => 'calculated',
					'total' => count($inputErrorsTenant)
				);
			} elseif ($typereport == 'objects') {
				$reports['inputErrorsObject'] = array(
					'reports' => array_slice($inputErrorsObject, 0, 99),
					'paginated' => Zend_Paginator::factory($inputErrorsObject),
					'sort' => 'calculated',
					'total' => count($inputErrorsObject)
				);
			}
			
			$this->view->reports = $reports;
			$this->view->type = $type;
			$this->view->tenantdirection = $tenantdirection;
			$this->view->objectdirection = $objectdirection;
			$this->view->invoiceableOnly = $invoiceableOnly;
		}
		
		public function setFilter($name){
			$filters = array(	'type' =>				array(	'default' =>	1	),
								'invoiceableOnly' =>	array(	'default' =>	true ),
								'tenantsort' =>			array(	'default' =>	'name' ),
								'objectsort' =>			array(	'default' =>	'name' ),
								'tenantdirection' =>	array(	'default' =>	SORT_DESC ),
								'objectdirection' =>	array(	'default' =>	SORT_DESC )	);
								
			if (!$filters[$name])
				return;
									
			if ($this->_getParam($name)){
				$return = $this->_getParam($name);
			} elseif($_SESSION['report'][$name]){
				$return = $_SESSION['report'][$name];
			} else {
				$return = $filters[$name]['default'];
			}
			
			$_SESSION['report'][$name] = $return;
			
			return $return;
		}
		
		protected function getReport($invoicableOnly, $type, $tenantType, $project, $list, $error) {
			$rawReport = ReportInput::model()
				->with('tenant', 'contactInfo', 'address', 'invoicepreferences', 'delivery', 'meters', 'invoices')
				->invoicableOnly($invoiceableOnly)
				->type($type)
				->tenantType($tenantType)
				->project($project)
				->get();
				
			return ReportInput::groupByTenant($rawReport, $list, $error, true);
		}
		
		public function inputDetailsAction() {
			$unc = new UsersNoContact();
			$transactionModel = new Transaction();
			$notesModel = new SupportNotes();
			$this->view->Breadcrumbs()->addCrumb('Invoer', 'report/input/');

			$page = $this->_getParam('page') ? $this->_getParam('page') : 1;
			$limit = $this->_getParam('limit') ? $this->_getParam('limit') : 250;
			$sort = $this->_getParam('sort') ? $this->_getParam('sort') : 'name';
			$error = $this->_getParam('error') ? $this->_getParam('error') : 'tenant';
			$direction = $this->_getParam('direction') ? $this->_getParam('direction') : SORT_ASC;
			$type = $this->_getParam('type');
			$tenanttype = $this->_getParam('tenanttype');
			$project = $this->_getParam('project');
			$list = $this->_getParam('list');
			$invoiceableOnly = $this->_getParam('invoiceableOnly') == 0 ? false : true;
			$reports = array();

			//groeperen en berekenen
			$inputErrors = $this->getReport($invoicableOnly, $type, $tenanttype, $project, $list, $error);
			 
			$counters = array();
			$counters[$tenanttype] = count($inputErrors);
			$othertype = $tenanttype == 'commercieel' ? 'particulier' : 'commercieel';
			$counters[$othertype] = count($this->getReport($invoicableOnly, $type, $othertype, $project, $list, $error));

			//$inputErrors = ReportInput::groupByTenant($tempReport, $list, $error, true);
			$this->sortBy($sort, $inputErrors, $direction);
			//p($inputErrors,'die');
			foreach ($inputErrors as &$row) {
				if (isset($row['userid'])) {
					$userId = $row['userid'];
					$row['payment_problems'] = $transactionModel->getPaymentProblems($userId, strtotime('-6 months'));
					$row['urgent_notes'] = $notesModel->getWarnings($userId);
				}
			}
			
			//pagination
			$report['paginated'] = Zend_Paginator::factory($inputErrors)
				->setCurrentPageNumber($page)
				->setItemCountPerPage($limit);
			
			$this->view->title = ReportInput::getDescriptions($error, $list);
			$this->view->Breadcrumbs()->addCrumb($this->view->title);
			
			$this->view->report = $report;
			$this->view->counters = $counters;
			$this->view->type = $type;
			$this->view->tenanttype = $tenanttype;
			$this->view->list = $list;
			$this->view->error = $error;
			$this->view->invoiceableOnly = $invoiceableOnly;
			$this->view->params = $this->_getAllParams();
			$this->view->noContactInfo = $unc->getNoContactInfo();
			$this->view->ncTypeLabels = $unc->typeLabels;

			$this->render('/input/details');
		}

		public function transactionAction() {

			$this->view->Breadcrumbs()->addCrumb('Debiteuren', 'transaction/');

			$type = $this->_getParam('type');

			$reports = array(
				'total' => array(
					'reports' => array(
						array(
							'description' => 'Facturen zonder herinnering',
							'data' => $openNonReminded = ReportTransaction::model()
								->open()
								->reminded(false)
								->type($type)
								->get(),
								'value' => '&euro;' . new StringFormat($openNonReminded['calculated']['total'] / 100, 'money')
						),
						array(
							'description' => 'Facturen die herinnerd zijn',
							'data' => $reminded = ReportTransaction::model()
								->open()
								->reminded()
								->type($type)
								->get(),
								'value' => '&euro;' . new StringFormat($reminded['calculated']['total'] / 100, 'money')
						)
					),
					'total' => $openNonReminded['calculated']['total'] + $reminded['calculated']['total']
				),
				'amountPerTenant' => array(
					'reports' => array(),
					'total' => 0
				),
				'postsPerTanant' => array(

				)
			);

			//groeperen en berekenen
			$tempReport = ReportTransaction::model()
				->open()
				->with('tenant', 'project', 'oldestPost')
				->type($type)
				->get();
			$openTransactions = ReportTransaction::groupByTenant($tempReport);

			//sorteren op total openstaand bedrag
			$amountPerTenant = $openTransactions;
			$this->sortBy('calculated', $amountPerTenant, SORT_DESC);
			$reports['amountPerTenant'] = array(
				'reports' => array_slice($amountPerTenant, 0, 10),
				'paginated' => Zend_Paginator::factory($amountPerTenant),
				'sort' => 'calculated',
				'total' => count($amountPerTenant)
			);

			//sorteren op aantal openstaande posten
			$postsPerTenant = $openTransactions;
			$this->sortBy('transactionsTotal', $postsPerTenant, SORT_DESC);
			$reports['postsPerTenant'] = array(
				'reports' => array_slice($postsPerTenant, 0, 10),
				'paginated' => Zend_Paginator::factory($postsPerTenant),
				'sort' => 'transactionsTotal',
				'total' => count($postsPerTenant)
			);

			//sorteren oudste nota's
			$oldestPerTenant = $openTransactions;
			$this->sortBy('dateInDays', $oldestPerTenant, SORT_DESC);
			$reports['oldestPerTenant'] = array(
				'reports' => array_slice($oldestPerTenant, 0, 10),
				'paginated' => Zend_Paginator::factory($oldestPerTenant),
				'sort' => 'dateInDays',
				'total' => count($oldestPerTenant)
			);

			$this->view->reports = $reports;
			$this->view->type = $type;

		}

		public function transactionDetailsAction() {

			$this->view->Breadcrumbs()->addCrumb('Debiteuren', 'report/transaction');
			$this->view->Breadcrumbs()->addCrumb('Details', 'report/transactionDetails/');


			//groeperen en berekenen
			$tempReport = ReportTransaction::model()
				->open()
				->with('tenant', 'project', 'invoicePreferences')
				->filter('u.name', $filter)
				->type($type);
			$tempReport = $tempReport->get();

            //p($tempReport,'die');

			$openTransactions = ReportTransaction::groupByTenant($tempReport);
            //p($openTransactions,'die');
            $this->view->listView($openTransactions)

                ->setTypes(array(
                    'projectname' => array(
                        'title' => 'Project',
                        'width' => 'xxxlarge truncate',
                    ),
                    'rendered_address' => array(
                        'title' => 'Adres',
                        'width' => 'xxxlarge truncate',
                    ),
                    'ca_name' => array(
                        'title' => 'Incassobureau',
                        'width' => 'xxxlarge truncate',
                    ),
                    'fullname' => array(
                        'title' => 'Naam debiteur',
                        'width' => 'xxxlarge truncate',
                    ),
                    'transactionsTotal' => array(
                        'title' => 'Aantal posten',
                        'width' => 'xsmall truncate',
                    ),
                    'calculated' => array(
                        'title' => 'Openstaand bedrag',
                        'width' => 'small truncate',
                        'order_using_raw' => true,
                    ),
                ))



                ->addFormat('fullname', function($value, $item){
                    return '<a href="support/show/id/' . $item['id'] . '/type/user" target="_blank">' . $value . '</a>';
                })

                ->addFormat('calculated', 'money')


                ->setFilters([
                    'projectname' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Project', 'order_by_value' == 'ASC'],
                    'rendered_address' => ['type' => 'input', 'renderSeparately' => true, 'title' => 'Adres', 'order_by_value' == 'ASC'],
                    'ca_name' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Incassobureau', 'order_by_value' == 'ASC'],
                ])

                ->addTotals(array('calculated'))


                ->render($this);


            /*
            //p($openTransactions,'die');
			$this->sortBy($sort, $openTransactions, $direction);

			$report = array(
				'total' => count($openTransactions)
			);

			//pagination
			$report['paginated'] = Zend_Paginator::factory($openTransactions)
				->setCurrentPageNumber($page)
				->setItemCountPerPage($limit);

			$this->view->report = $report;
			$this->view->filter = $filter;
			$this->view->type = $type;
			$this->view->direction = $direction == SORT_ASC ? SORT_DESC : SORT_ASC;

			if ($this->_getParam('direction')) {
				$this->_helper->layout->disableLayout();
				$this->render('transaction/detailsList');
			}
			else
				$this->render('transaction/details');
            */

		}

		public function tenantAction() {

			$this->view->Breadcrumbs()->addCrumb('Debiteuren', 'report/transaction');
			$this->view->Breadcrumbs()->addCrumb('Debiteur', 'report/tenant/');

			$page = $this->_getParam('page') ? $this->_getParam('page') : 1;
			$limit = $this->_getParam('limit') ? $this->_getParam('limit') : 20;
			$type = $this->_getParam('type');

			$tempReport = ReportTransaction::model()
				->open()
				->with('tenant', 'project', 'oldestPost')
				->type($type)
				->get();
			$openTransactions = ReportTransaction::groupByTenant($tempReport);

			$sortBy = $this->_getParam('sort') ? $this->_getParam('sort') : 'calculated';

			$this->sortBy($sortBy, $openTransactions, $direction);
			$report = array(
				//'reports' => array_slice($openTransactions, ($page*$limit)-$limit, $limit),
				'total' => count($openTransactions)
			);

			//pagination
			$report['paginated'] = Zend_Paginator::factory($openTransactions)
				->setCurrentPageNumber($page)
				->setItemCountPerPage($limit);

			$this->view->title = $this->_getParam('title');
			$this->view->short = $this->_getParam('short');
			//$this->view->page = $page;
			//$this->view->sort = $this->_getParam('sort');
			//$this->view->limit = $limit;

			$this->view->report = $report;

			$this->render('/transaction/tenant');

		}

		public function customerSupportAction(){
			$this->view->Breadcrumbs()->addCrumb('Klantenservice overzicht');

			$email_model = new Emails();
			$complaints_model = new SupportComplaints();
			$tasks_model = new TasksTable();

			$users_select = db()->select()
				->from(array('u' => 'users'), array('user' => 'id'))
				->joinLeft(array('d' => 'departments'), 'd.id = u.internal', array('department_name' => 'name', 'department' => 'id'))
				->where('u.type IN (' . implode_for_where_in(acl()->getInternalRights()) . ')')
				->where('u.internal > 0 AND u.internal != 5')
				->order('u.internal DESC');

			$data = db()->fetchAll($users_select);
			$users = array_column($data, 'user');

			$function_add_counts_to_data_array = function($key, $count_data) use (&$data){
				foreach($count_data as $item)
					foreach($data as $data_row_key => $data_row)
						if($data_row['user'] == $item['user'])
							$data[$data_row_key][$key] = $item['count'];
			};

			$function_add_counts_to_data_array('emails', $email_model->getNumberOfAssignedPerUser($users));
			$function_add_counts_to_data_array('emails_overdue', $email_model->getNumberOfAssignedPerUser($users, true));

			$function_add_counts_to_data_array('complaints', $complaints_model->getNumberOfAssignedPerUser($users));
			$function_add_counts_to_data_array('complaints_overdue', $complaints_model->getNumberOfAssignedPerUser($users, true));

			$function_add_counts_to_data_array('defects', $complaints_model->getNumberOfAssignedPerUser($users, false, false));
			$function_add_counts_to_data_array('defects_overdue', $complaints_model->getNumberOfAssignedPerUser($users, true, false));

			$function_add_counts_to_data_array('tasks', $tasks_model->getNumberOfAssignedPerUser($users));
			$function_add_counts_to_data_array('tasks_overdue', $tasks_model->getNumberOfAssignedPerUser($users, true));

			$this->view->ListView($data, array())
				->setTypes(array(
					'department' => array('title' => 'Afdeling', 'width' => 'small', 'group_equal_rows' => true),
					'user' => array('title' => 'Gebruiker', 'width' => 'medium truncate'),

					'emails' => array('title' => 'Email', 'width' => 'xxsmall right'),
					'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxxsmall'),
					'emails_overdue' => array('title' => '!', 'width' => 'xxxxsmall right'),

					'complaints' => array('title' => 'Klachten', 'width' => 'medium right'),
					'spacer2' => array('title' => '&nbsp;', 'width' => 'xxxxxsmall'),
					'complaints_overdue' => array('title' => '!', 'width' => 'xxxxsmall right'),

					'defects' => array('title' => 'Storingen', 'width' => 'medium right'),
					'spacer3' => array('title' => '&nbsp;', 'width' => 'xxxxxsmall'),
					'defects_overdue' => array('title' => '!', 'width' => 'xxxxsmall right'),

					'tasks' => array('title' => 'Taken', 'width' => 'medium right'),
					'spacer4' => array('title' => '&nbsp;', 'width' => 'xxxxxsmall'),
					'tasks_overdue' => array('title' => '!', 'width' => 'xxxxsmall right'),
				))

				->addFormat('user', function($value, $item){
					return User::buildname(false, $value);
				})

				->addFormat('department', function($value, $item){
					return $item['department_name'];
				})

				->addFormat(array('mailed', 'archived'), function($value){
					if($value > 0) return $value;
					return '-';
				})

				->setFilters(array(
					'department' => array(),
					'user' => array(),
				))

				->addTotals(array('emails', 'emails_overdue', 'complaints', 'complaints_overdue', 'defects', 'defects_overdue', 'tasks', 'tasks_overdue'))


				->setOptions(array(
				))

				->render($this);
		}
		
		
		
		public function projectGraphsAction() {
		}
		
		public function paymentGraphsAction() {
		}

		public function budgetGraphsAction() {
		}

		public function rentalGraphsAction() {
		}
		
		public function closedAndExploitGraphDataAction () {
	 		$this->projectsGraphDataAction(false); 
	    }
	    
	    public function projectsGraphDataAction ($showProspects = true) {
	    	$this->disableView();
	    	$f = new Finance();
	    	
	    	$thisYear = date('Y');
	    	
	    	$labels = array();
	    	
	    	$prospect     = 0;
	    	$closed       = 0;
	    	$exploitation = 0;
	    	
	    	$prospectData = array();
	    	$closedData   = array();
	    	$exploitData  = array();
	    	
	    	$prospectProjCount = 0;
	    	$closedProjCount   = 0;
	    	$exploitProjCount  = 0;
	    	
	    	$prospectProjCounts = array();
	    	$closedProjCounts   = array();
	    	$exploitProjCounts  = array();
	    	
	    	foreach($f->getClosed($thisYear - 5, $thisYear + 5) as $year => $proj) {
	    		$labels[] = $year;
	    		
	    		$prospect     += isset($proj['prospect'])   ? $proj['prospect']   / 100 : 0;
	    		$closed       += isset($proj['inprogress']) ? $proj['inprogress'] / 100 : 0;
	    		$exploitation += isset($proj['closed'])     ? $proj['closed']     / 100 : 0;
	    		
	    		$prospectData[] = $prospect;
	    		$closedData[]   = $closed;
	    		$exploitData[]  = $exploitation;
	    		
	    		$prospectProjCount += $proj['prospect_projects'];
	    		$closedProjCount   += $proj['inprogress_projects'];
	    		$exploitProjCount  += $proj['closed_projects'];
	    		
	    		$prospectProjCounts[] = $prospectProjCount;
	    		$closedProjCounts[]   = $closedProjCount;
	    		$exploitProjCounts[]  = $exploitProjCount;
	    	}
	    	
	    	if ($showProspects) {
	    		echo json_encode(array(
					'legend' => array("Exploitatie", "Closed", "Prospect"),
					'data'   => array($exploitData, $closedData, $prospectData),
					'labels' => $labels,
					'internal_labels' => array($exploitProjCounts, $closedProjCounts, $prospectProjCounts),
					'colors' => array("#8aa717", "#1751a7", "#a74217"),
				));
	    	} else {
				echo json_encode(array(
					'legend' => array("Exploitatie", "Closed"),
					'data'   => array($exploitData, $closedData),
					'labels' => $labels,
					'internal_labels' => array($exploitProjCounts, $closedProjCounts),
					'colors' => array("#8aa717", "#1751a7"),
				));
			}
	    }
	    
	    public function prospectsGraphDataAction () {
	    	$this->disableView();
	    	$f = new Finance();
	    	
	    	$labels = array();
	    	$prospectData = array();
	    	$cumulativeData = array();
	    	$thisYear = date('Y');
	    	foreach ($f->getProspects($thisYear - 5, $thisYear + 5) as $year => $prospects) {
	    		$labels[] = $year;
	    		$total = $prospects['total'] / 100;
	    		$projCount = $prospects['total_projects'];
	    		
	    		$prospectData[] = $total;
	    		$projCounts[] = $projCount;
	    		$cumulativeData[] = array_sum($prospectData);
	    		$cumProjCounts[] = array_sum($projCounts);
	    	}
	    	
	    	echo json_encode(array(
	    		'legend' => array("Prospects (jaar)", "Prospects (cumulatief)"),
	    		'data'   => array($prospectData, $cumulativeData),
	    		'labels' => $labels,
	    		'internal_labels' => array($projCounts, $cumProjCounts),
	    		'colors' => array("#a74217", "#800"),
	    	));
	    }
	    
	    public function closedGraphDataAction () {
	    	$this->disableView();
	    	$f = new Finance();
	    	
	    	$labels = array();
	    	$closedData = array();
	    	$cumulativeData = array();
	    	$thisYear = date('Y');
	    	//var_dump($f->getClosed($thisYear - 5, $thisYear + 5));
	    	foreach ($f->getClosed($thisYear - 5, $thisYear + 5) as $year => $closed) {
	    		if ($closed['inprogress']) {
					$labels[] = $year;
					$total = $closed['inprogress'] / 100;
					$projCount = $closed['inprogress_projects'];
					
					$closedData[] = $total;
					$projCounts[] = $projCount;
					$cumulativeData[] = array_sum($closedData);
					$cumProjCounts[] = array_sum($projCounts);
				}
	    	}
	    	
	    	echo json_encode(array(
	    		'legend' => array("Closed (jaar)", "Closed (cumulatief)"),
	    		'data'   => array($closedData, $cumulativeData),
	    		'labels' => $labels,
	    		'internal_labels' => array($projCounts, $cumProjCounts),
	    		'colors' => array("#a74217", "#800"),
	    	));
	    }
		
		public function unpayedGraphDataAction () {
			$this->disableView();

			$transMod = new Transaction();
			$rawStats = $transMod->fetchReportData(['showClosed' => false]);


			foreach($rawStats as $rawStat_key => $row){
				if($row['type'] == 'd'){
					$row['amount'] = $row['amount'] > 0 ? 0 - $row['amount'] : 0;
					$row['payed'] = $row['payed'] > 0 ? 0 - $row['payed'] : 0;
				}

				$rawStats[$rawStat_key]['unpayed'] = ($row['amount'] + $row['penalty']) - ($row['payed'] + $row['penalty_payed']);

			}

			$unpayedStats = array(
				'within_30d' => 0,
				'within_60d' => 0,
				'beyond_60d' => 0,
				
			);

			$amountlabels = array(
				0 => 0,
				1 => 0,
				2 => 0,
			);

			foreach ($rawStats as $row) {
				if ($row['days'] >=1 ) {			
					if ($row['days'] >=  1 and $row['days'] <= 30) {
						$category = 'within_30d';
						$amountlabels[0] += $row['unpayed'];
						$unpayedStats[$category]+= $row['unpayed'];
						
					} else if ($row['days']  >=  31 and $row['days'] <= 60) {
						$category = 'within_60d';
						$amountlabels[1] += $row['unpayed'];
						$unpayedStats[$category] += $row['unpayed'];
						
					} else if  ($row['days']  >=  61)  {
						$category = 'beyond_60d';
						$amountlabels[2] += $row['unpayed'];
						$unpayedStats[$category] += $row['unpayed'];
					}
					
				}
			}

			foreach ($unpayedStats as $key => $value) {
				if ($value==0)
					unset($unpayedStats[$key]);
			}

			// Because rapheal sorts pie chart from big to low value also sort data accordingly
			arsort($amountlabels);

			foreach ($amountlabels as $key => $value) {
				$amountlabels[$key] = '€ '. new StringFormat($value/100, 'money');
				if ($value==0)
					unset($amountlabels[$key]);
			}

			echo json_encode(array(
				'legend' => array(
					"Openstand  0 - 30 dagen : %% ",
					"Openstand 31 - 60 dagen : %%",
					"Openstand > 60 dagen : %%",
				),

				'internal_labels' => array_values($amountlabels),


				'data' => array_values($unpayedStats),
			));
		}


		public function paymentBehaviorGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);

			//p($rawStats,'die');
			
			$unpayedStats = array(
				'before_due' => 0,
				'within_30d' => 0,
				'within_60d' => 0,
				'beyond_60d' => 0,
			);
			
			foreach ($rawStats as $row) {
				if ($row['remaining_days'] >= 0) {
					$category = 'before_due';
					
				} else if ($row['remaining_days'] >= -30) {
					$category = 'within_30d';
					
				} else if ($row['remaining_days'] >= -60) {
					$category = 'within_60d';
					
				} else {
					$category = 'beyond_60d';
				}
				$unpayedStats[$category] += $row['count'];
			}

			p($unpayedStats,'die');
			
			echo json_encode(array(
				'legend' => array(
					"%% - Voor vervaldatum",
					"%% - Binnen 30 dagen na vervaldatum",
					"%% - Binnen 60 dagen na vervaldatum",
					"%% - Meer dan 60 dagen na vervaldatum",
				),

				'data' => array_values($unpayedStats),
			));
		}


		public function rentalGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'Bemiddeling' => 63,
				'Overige' => 8,
				'Contractkosten' => 29,

			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - Bemiddeling ",
					"%% - Contractkosten",
					"%% - Overige",
				),
 	    		'internal_labels' => array( 0 => 18.675,
   											 1 => 8.596,
   											 2 => 2.371),
				'data' => array_values($unpayedStats),
			));
		}

		public function budgetGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'Huur' => 63,
				'Servicekosten' => 8,
				'Voorschot' => 29,

			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - Huur ",
					"%% - Servicekosten",
					"%% - Voorschot Gas Water Electra",
				),
 	    		'internal_labels' => array( 0 => 18.675,
   											 1 => 8.596,
   											 2 => 2.371),
				'data' => array_values($unpayedStats),
			));
		}

		public function visitsGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'0-15 dagen' => 55,
				'16-30 dagen' => 15,
				'31-60 dagen' => 15,
				'> 61 dagen' => 15,


			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - 0 tot 16 dagen ",
					"%% - 16 tot 30 dagen",
					"%% - 31 tot 60 dagen ",
					"%% - langer dan 61 dagen"
				),
 	 			'colors' => array("brown", "yellow", "purple","grey"),
 	 			'internal_labels' => array( 0 => 55,
   											 1 => 15,
   											 2 => 15,
   											 3 => 15

   											 ),
				'data' => array_values($unpayedStats),
			));
		}


		public function voorschotGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'Opbrengsten' => 55,
				'Kosten' => 45

			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - Opbrengsten ",
					"%% - Kosten"
				),
 	 			'colors' => array("green", "red"),
 	 			'internal_labels' => array( 0 => 2.371,
   											 1 => 1.939),
				'data' => array_values($unpayedStats),
			));
		}

		public function servicekostenGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'Opbrengsten' => 42,
				'Kosten' => 58

			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - Opbrengsten ",
					"%% - Kosten"
				),
 	 			'internal_labels' => array( 0 => 11.871,
   											 1 => 8.596),
				'data' => array_values($unpayedStats),
				'colors' => array("red", "green"),
			));
		}


		public function conversionGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
				'Verhuurd' => 78,
				'Niet verhuurd' => 42
				

			);
			
			echo json_encode(array(
				'legend' => array(
					"%% - Verhuurd ",
					"%% - Niet Verhuurd"
				),
 	 			'internal_labels' => array( 0 => 75,
   											 1 => 13),
				'data' => array_values($unpayedStats),
				'colors' => array("green", "red"),
			));
		}


		public function costGraphDataAction () {
			$this->disableView();
			
			$transMod = new Transaction();
			$rawStats = $transMod->outstandingStats(false);
			
			$unpayedStats = array(
 				'Servicekosten' => 26,
				'Voorschot' => 74,
			);
			
			echo json_encode(array(
				'legend' => array(
 					"%% - Servicekosten",
					"%% - Voorschot Gas Water Electra"
				),
				'data' => array_values($unpayedStats),
			));
		}

		
		public function paymentPreferencesGraphDataAction() {
			$this->disableView();
			
			$stats['ideal'] = 0;
			$stats['collection'] = 0;
			

			$transMod = new Transaction();
			$stats = $transMod->paymentPreferencesStats();
			
			echo json_encode(array(
				'legend' => array(
					"%% - Automatische incasso",
					"%% - Handmatige betaling",
				),
				'data' => array_values($stats),
			));
		}
		

		public function departingAction() {
			$this->view->headLink()->appendStylesheet('media/style/report/departing.css');


            if(is_null($this->getParam('year')) && $this->getParam('view_template') != 'excel' && !$this->isAjaxRequest){
                $this->setParam('year', date('Y'));
            }

			$departed  = $this->_getParam('departed');
			$this->view->Breadcrumbs()->addCrumb(($departed ? 'Vertrokken' :  'Vertrekkende') .' huurders');

			$projParam  = $this->_getParam('project');

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('till' => 'ASC')));

			$projectId = $projParam  ? $projParam  : false;

			$this->view->extraWidth = true;
			$objModel  = new Object();

			$data = $objModel->getDeparting($projectId,false,$departed);
			$build_label =  (Settings::get('build_label')  ? Settings::get('build_label') :  '#spec.');

			$this->view->ListView($data)

				->setTypes(array(
					'project' => array('title' => 'Project', 'width' => 'hidden'),
					'street' => array('title' => 'Adres', 'width' => 'medium truncate'),
					'house_number' => array('title' => '#', 'width' => 'xxxsmall'),
					'build' => array('title' =>  $build_label, 'width' => 'xxxsmall'),
					'city' => array('title' =>  'Stad', 'width' => 'xsmall'),
					'user' => array('title' => 'Huurder', 'width' => 'xlarge'),
                    'initials' => array('title' => 'initialen', 'width' => 'hidden'),
                    'firstname' => array('title' => 'voornaam', 'width' => 'hidden'),
                    'middlename' => array('title' => 'Tussenvoegsel', 'width' => 'hidden'),
                    'name' => array('title' => 'Achternaam', 'width' => 'hidden'),

                    'user_id' => array('title' => 'tenant id#', 'width' => 'hidden'),
                    'object' => array('title' => 'object id#', 'width' => 'hidden'),
                    'bdate' => array('title' => 'Geboortedatum', 'width' => 'hidden'),
                    'gender' => array('title' => 'Geslacht', 'width' => 'hidden'),
                    'iban' => array('title' => 'IBAN', 'width' => 'hidden'),
                    'payment_method' => array('title' => 'Betaalmethode', 'width' => 'hidden'),
                    'phone_number' => array('title' => 'Telefoon', 'width' => 'medium   '),
					'from' => array('title' => 'Ingang datum', 'width' => 'small', 'type' => 'date'),
					'till' => array('title' => 'Vertrek datum', 'width' => 'small', 'type' => 'date'),
					'nextcustomerid' => array('width' => 'xxsmall', 'title' => "Dir. opvolger"),
					'email' => array('title' => 'E-mail', 'width' => 'large truncate'),
					'notes' => array('title' => 'Opmerkingen', 'width' => 'xsmall'),
					'notes_csv' => array('column' => 'notes', 'title' => 'Opmerkingen', 'width' => 'hidden'),
					'unpayed_amount' => array('title' => 'Achterstand', 'width' => 'small right'),
					'deposit_payed' => array('title' => 'Borgsom betaald', 'width' => 'medium right')
				))

				->addFormat('street', function($value, $item){
					return '<a href="support/show/id/' . $item['object'] . '/type/object" target="_blank">' . $value . '</a>';
				})
				->addFormat('from', 'date')
				->addFormat('till', 'date')
				->addFormat(array('unpayed_amount', 'deposit_payed'), 'money')

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $item['rendered_name'] . '</a>' : '-';
				})

				->addFormat('nextcustomerid', function($value, $item){
                        return $value > 0 && ($item['till'] == $item['nextfrom'] || date('Y-m-d', strtotime('+1 days', strtotime($item['till']))) == $item['nextfrom'])  ? 'Ja' : 'Nee';
				})

				->addFormat('notes', function($value){
					return is_string($value) && $value !== '' ? '<span noHintButton="1" class="hasNotes" title="' . htmlentities($value) . '">&nbsp;</span>' : '-';
				})

				->addFormat('notes_csv', function($value){
					if(!is_string($value) || $value === '') return '';

					foreach(array('"', ',') as $delete)
						$value = str_replace($delete, '', $value);

					foreach(array('<br />', "\n") as $space)
						$value = str_replace($space, ' - ', $value);

					$value = str_replace('<small>', '<small> || ', $value);
					$value = substr($value, 11);

					$value = str_replace('</small>', ' - </small>', $value);

					return htmlentities($value);
				})

				->addFormat('email', function($value){ return $value ? $value : '-'; })

				->setFilters(array(
					'project' => array('type' => 'select', 'renderSeparately' => true, 'order_by_value' => 'ASC' , 'title' => 'Project'),
                    'year' => array('type' => 'select', 'renderSeparately' => true, 'order_by_value' => 'DESC' ,'title' => 'Jaar'),
                    'investorname' => array('type' => 'select', 'renderSeparately' => true, 'order_by_value' => 'ASC' ,'title' => 'Belegger'),
					'street' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'user' => array('type' => 'input'),
				))

				->addTotals(array('unpayed_amount', 'deposit_payed'))

				->setOptions(array())

				->render($this);
		}

		public function depositAction(){

			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Waarborgsom overzicht');

			$this->_setParam('no_amounts', true);

			$u_model = new Users();

            if (is_null($this->getParam('inactive')) && !$this->isAjaxRequest) {
                $this->setParam('inactive', false);
            }

			$data = $u_model->getDepositList($this->getAllUrlParams(), false);

			$this->view->ListView($data)

				->setTypes(array(
					'project' => array('title' => 'Project', 'width' => 'hidden'),
					'og_type' => array('title' => 'Type', 'width' => 'hidden'),
                    'state' => array('title' => 'status', 'width' => 'hidden'),
					'rendered_name' => array('title' => 'Huurder', 'width' => 'medium truncate', 'order_using_column' => 'name'),
					'address' => array('title' => 'Adres', 'width' => 'medium truncate'),
					'number' => array('title' => 'Nr', 'width' => 'xxsmall left'),
					'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') : (Settings::get('software_type') == 'energy' ? "Bouwnr" : '#spcf.'), 'width' => 'xsmall left'),
					'zipcode' => array('title' => 'Postcode', 'width' => 'xxsmall'),
					'city' => array('title' => 'Plaats', 'width' => 'xsmall'),
					'deposit_amount' => array('title' => 'Waarborg bedrag', 'width' => 'medium right'),
					'deposit_payed' => array('title' => 'Waarborg betaald', 'width' => 'medium right'),
					'deposit_repayed' => array('title' => 'Waarborg uitgekeerd', 'width' => 'medium right'),
					'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
					'deposit_payed_to_broker' => array('title' => 'Aan belegger betaald', 'width' => 'medium'),
					'deposit_type' => array('title' => 'Type waarborg', 'width' => 'small'),
                    'inactive' => ['title' => 'Inactief', 'width' => 'hidden'],
                    'investor' => ['title' => 'Belegger', 'width' => 'hidden'],
				))

				->addFormat('address', function($value){ return $value ? $value : '-'; })
				->addFormat('project', function($value, $item){ return $item['project_name']; })
				->addFormat('zipcode', 'zipcode')
                ->addFormat('investor', function ($value, $item) {
                    return $item['investorName'];
                })

				->addFormat('deposit_payed_to_broker', 'bool')

				->addFormat('og_type', function($value){ return ucfirst($value); })
				->addFormat('deposit_type', function($value){
					$types = ['cash_deposit' => 'Gestort', 'bank_guarantee' => 'Bank Garantie', 'concern_guarantee' => 'Concern Garantie'];

					return isset($types[$value]) ? $types[$value] : '-';
				})

				->addFormat(array('deposit_amount', 'deposit_payed', 'deposit_repayed'), 'money')

				->addFormat('address', function($value, $item){
					return '<a href="support/show/id/' . $item['id'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('rendered_name', function($value, $item){
                    if ($value) {
                        return '<a href="support/show/id/' . $item['user_id'] . '/type/user" target="_blank">' . $value . '</a>';
                    } else {
                        return '-';
                    }


				})
                ->addFormat('inactive', 'bool')

				->addLegend('deposit-legend')

				->addTotals(array('deposit_amount', 'deposit_payed', 'deposit_repayed'))

				->setFilters(array(
					'project' => array('title' => 'Project', 'renderSeparately' => true, 'order_by_name' => 'ASC'),
					'og_type' => array('title' => 'Type', 'renderSeparately' => true, 'order_by_name' => 'ASC'),
                    'corporation_name' => array('title' => 'Juridische entiteit', 'renderSeparately' => true, 'order_by_name' => 'ASC'),
                    'investor' => array('title' => 'Belegger', 'renderSeparately' => true, 'order_by_name' => 'ASC'),
                    'inactive' => ['renderSeparately' => true, 'title' => 'Inactieve objecten'],
					'address' => array('type' => 'input'),
					'number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'zipcode' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'tenant' => array('type' => 'input'),
					'deposit_amount' => array('type' => 'range'),
					'deposit_payed' => array('type' => 'range'),
					'deposit_repayed' => array('type' => 'range'),
					'deposit_payed_to_broker' => array(),
					'deposit_type' => array()
				))

				->setOptions(array())

				->render($this);
		}		

		public function depositPayoutAction() {
			$this->view->headLink()->appendStylesheet('media/style/report/departing.css');

			$this->view->Breadcrumbs()->addCrumb('Waarborg uitkering overzicht');

			$projParam  = $this->_getParam('project');

			if(is_null($this->getParam('year')) && !$this->isAjaxRequest){
			    $this->setParam('year', date('Y'));
            }

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('till' => 'ASC')));

			$projectId = $projParam  ? $projParam  : false;

			$this->view->extraWidth = true;
			$objModel  = new Object();

			$data = $objModel->getDeparting($projectId, array(
				'future_till_date' => false,
				'deposit_only' => true
			));

			$build_label = Settings::get('software_type') == 'energy' ? 'Bouwnummer' : (Settings::get('build_label')  ? Settings::get('build_label') :  'Kamernummer');

			$this->view->ListView($data)

				->setTypes(array(
					'till' => array('title' => 'Vertrek datum', 'width' => 'small', 'type' => 'date'),
					'year' => array('title' => 'Jaar', 'width' => 'hidden'),
					'month' => array('title' => 'Maand', 'width' => 'hidden'),
					'project' => array('title' => 'Project', 'width' => 'hidden'),
					'project' => array('title' => 'Project', 'width' => 'hidden'),
					'street' => array('title' => 'Adres', 'width' => 'xlarge truncate'),
					'house_number' => array('title' => 'Nummer', 'width' => 'xsmall'),
					'build' => array('title' =>  $build_label, 'width' => 'small'),
					'city' => array('title' =>  'Stad', 'width' => 'small'),
					'user' => array('title' => 'Huurder', 'width' => 'xlarge'),
					'deposit_payed_to_broker' => array('title' => 'Betaald aan belegger', 'width' => 'large'),
					'deposit_payed' => array('title' => 'Borgsom betaald', 'width' => 'large right'),
					'deposit_repayed' => array('title' => 'Borgsom uitbetaald', 'width' => 'large right')
				))

				->addFormat('street', function($value, $item){
					return '<a href="support/show/id/' . $item['object'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('till', 'date')
				->addFormat(array('deposit_payed', 'deposit_repayed'), 'money')

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . '</a>' : '-';
				})

				->addFormat('next', function($value, $item){
					return $value['customer'] > 0 && ($item['till'] == $value['from'] || date('Y-m-d', strtotime('+1 days', strtotime($item['till']))) == $value['from'])  ? 'Ja' : 'Nee';
				})

				->addFormat('deposit_payed_to_broker', 'bool')

				->addFormat('year', function($value, $item){ return date('Y', strtotime($item['till'])); })
				->addFormat('month', function($value, $item){ return ucFirst(strftime('%B', strtotime($item['till']))); })

				->addFormat('notes', function($value){
					return is_string($value) && $value !== '' ? '<span noHintButton="1" class="hasNotes" title="' . $value . '">&nbsp;</span>' : '-';
				})

				->addFormat('notes_csv', function($value){
					if(!is_string($value) || $value === '') return '';

					foreach(array('"', ',') as $delete)
						$value = str_replace($delete, '', $value);

					foreach(array('<br />', "\n") as $space)
						$value = str_replace($space, ' - ', $value);

					$value = str_replace('<small>', '<small> || ', $value);
					$value = substr($value, 11);

					$value = str_replace('</small>', ' - </small>', $value);

					return $value;
				})

				->addLegend('deposit-payed-legend')

				->addFormat('email', function($value){ return $value ? $value : '-'; })

				->setFilters(array(
					'project' => array('type' => 'select', 'renderSeparately' => true, 'title' => 'Project'),
					'street' => array('type' => 'input'),
					'house_number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'user' => array('type' => 'input'),
					'year' => array('title' => 'Jaar', 'renderSeparately' => true, 'order_by_value' => 'ASC'),
					'month' => array('title' => 'Maand', 'renderSeparately' => true, 'order_by_value' => 'ASC'),
					'deposit_payed_to_broker' => array('value' => 0)

				))

				->addTotals(array('deposit_repayed', 'deposit_payed'))

				->setOptions(array())

				->render($this);
		}

		public function arrivingAction($income_overview = false) {
			$this->view->headLink()->appendStylesheet('media/style/report/departing.css');

			$this->view->Breadcrumbs()->addCrumb($income_overview === false ? 'Aankomende huurders' : 'Aankomende huurders inkomsten');

			$projParam  = $this->_getParam('project');

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('from' => 'ASC')));

			$this->view->extraWidth = true;

			$projectId = $projParam  ? $projParam  : false;

			$objModel  = new Object();

			$data = $objModel->getArriving($projectId, $income_overview);

			$build_label = Settings::get('software_type') == 'energy' ? 'Bouwnummer' : (Settings::get('build_label')  ? Settings::get('build_label') :  'Kamernummer');

			$types = array(
				'project' => array('title' => 'Project', 'width' => 'hidden'),
				'street' => array('title' => 'Adres', 'width' => ($income_overview ? 'medium' : 'xxlarge') . ' truncate'),
				'house_number' => array('title' => 'Nummer', 'width' => 'xsmall'),
				'build' => array('title' =>  $build_label, 'width' => 'xsmall'),
				'city' => array('title' =>  'Stad', 'width' => ($income_overview ? 'medium' : 'large')),
				'user' => array('title' => 'Huurder', 'width' => ($income_overview ? 'medium' : 'xxlarge')),
				'from' => array('title' => 'Aankomst datum', 'width' => 'small', 'type' => 'date'),
				'email' => array('title' => 'E-mail', 'width' => 'xlarge truncate'),
				'contract' => array('title' => 'Contract getekend', 'width' => 'medium'),
				'notes' => array('title' => 'Opmerkingen', 'width' => 'xsmall'),
				'notes_csv' => array('column' => 'notes', 'title' => 'Opmerkingen', 'width' => 'hidden'),
			);


			if($income_overview){
				array_add_after($types, 'from', array(
					'furniture' => array('title' =>  'Gemeubileerd', 'width' => 'xsmall'),
					'rent' => array('title' =>  'Kale huur', 'width' => 'small right'),
					'service' => array('title' =>  'Servicekosten', 'width' => 'small right'),
					'spacer' => array('title' =>  '', 'width' => 'xxxxsmall')
				));
			}

			if(Settings::get('modules_app_inspection'))
				$types['inspection'] = array('column' => 'inspection', 'title' => 'Inspectie', 'width' => 'xxsmall');

			$this->view->ListView($data)

				->setTypes($types)

				->addFormat('street', function($value, $item){
					return '<a href="support/show/id/' . $item['object'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('from', 'date')

				->addFormat(array('rent', 'service'), 'money')

				->addFormat(array('furniture', 'contract'), 'bool')

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . '</a>' : '-';
				})

				->addFormat('notes', function($value){
					return is_string($value) && $value !== '' ? '<span noHintButton="1" class="hasNotes" title="' . $value . '">&nbsp;</span>' : '-';
				})

				->addFormat('notes_csv', function($value){
					if(!is_string($value) || $value === '') return '';

					foreach(array('"', ',') as $delete)
						$value = str_replace($delete, '', $value);

					foreach(array('<br />', "\n") as $space)
						$value = str_replace($space, ' - ', $value);

					$value = str_replace('<small>', '<small> || ', $value);
					$value = substr($value, 11);

					$value = str_replace('</small>', ' - </small>', $value);

					return $value;
				})

				->addFormat('email', function($value){ return $value ? $value : '-'; })

				->setFilters(array(
					'project' => array('type' => 'select', 'renderSeparately' => true, 'title' => 'Project'),
					'street' => array('type' => 'input'),
                    'from' => array(),
					'house_number' => array('type' => 'number'),
					'build' => array('type' => 'input'),
					'city' => array('type' => 'input'),
					'user' => array('type' => 'input'),
					'contract' => array('type' => 'select'),
					'furniture' => array()

				))

				->setOptions(array())

				->render($this);
		}

		public function arrivingIncomeAction(){
			$this->arrivingAction(true);
		}

		public function vacanciesAction() {
			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Leegstand');
            $this->view->headLink()->appendStylesheet('media/style/report/departing.css');
			$projectId  = $this->_getParam('project', false);

			$build_label = Settings::get('build_label')?: '# Specificatie';

            if (is_null($this->getParam('inactive')) && !$this->isAjaxRequest) {
                $this->setParam('inactive', false);
            }

			$data = (new Object())->getVacancies($projectId);

			$this->view->ListView($data)

				->setTypes(array(
					'project' => array('title' => 'Project', 'width' => 'small truncate', 'group_equal_rows' => true),
					'og_descr' => array('title' => 'Objectgroep', 'width' => 'small truncate', 'group_equal_rows' => true),
					'street' => array('title' => 'Adres', 'width' => 'medium truncate'),
					'house_number' => array('title' => 'Nummer', 'width' => 'small'),
					'build' => array('title' =>  $build_label, 'width' => 'xsmall'),
					'city' => array('title' => 'Stad', 'width' => 'xsmall truncate'),
					'inactive' => array('title' =>  'Inactief', 'width' => 'xsmall'),
 					'from' => array('title' => 'Beschikbaar per', 'width' => 'small', 'type' => 'date'),
					'price' => array('title' => 'Laatste huurbedrag', 'width' => 'xsmall right'),
					'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
					'user' => array('title' => 'Huidige huurder', 'width' => 'medium'),
					'phone' => array('title' => 'Telefoonnummer', 'width' => 'medium'),
                    'notes' => array('title' => 'Opmerkingen', 'width' => 'xsmall'),
                    'finalenddate' => ['title' => 'Definitieve einddatum', 'width' => 'xsmall'],
				))

				->addFormat('street', function($value, $item){
					return '<a href="support/show/id/' . $item['object'] . '/type/object" target="_blank">' . $value . '</a>';
				})

				->addFormat('from', function($value){
					return $value  ? date('d-m-Y', strtotime($value)) : 'Direct ';
				})

                ->addFormat('finalenddate', 'bool')

                ->addFormat('notes', function($value){
                    if($this->getParam('view_template') == 'excel'){
                        return strip_tags(str_replace('<br>', "\n", $value));
                    } else {
                        return is_string($value) && $value !== '' ? '<span noHintButton="1" class="hasNotes" title="' . $value . '">&nbsp;</span>' : '-';
                    }
                })

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . ($item['till'] ? ' <small>tot: ' . date('d-m-Y', strtotime($item['till'])) . '</small>' : '') . '</a>' : '-';
				})

				->addFormat('price', 'money')
				->addFormat('inactive', 'bool')
				->addFormat('phone', 'phone')

				->setFilters([
					'project' => [],
                    'branch_name' => ['renderSeparately' => true, 'title' => 'Vestiging'],
					'og_descr' => [],
                    'from' => [],
					'year' => ['renderSeparately' => true, 'title' => 'Jaar'],
                    'type' => ['renderSeparately' => true, 'title' => 'Type'],
                    'finalenddate' => ['renderSeparately' => true, 'title' => 'Definitieve einddatum'],
					'inactive' => ['renderSeparately' => true, 'title' => 'Inactieve objecten'],
					'street' => array('type' => 'input'),
					'house_number' => array('type' => 'input'),
					'build' => array('type' => 'input'),
					'city' => array(),
				])

				->render($this);
		}


		public function vacancyReportAction() {
			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Leegstand');

            $this->view->headLink()->appendStylesheet('media/style/report/departing.css');
            
			$broker = $this->_getParam('broker');

			$build_label = Settings::get('build_label')?: 'Kamernummer';

            $params = [];

            if ($broker && is_null($this->getParam('year'))) {
                if (isset($_SESSION['kpi_year'])) {
                    $params['year'] = $year = $_SESSION['kpi_year'];
                } else {
                    $params['year'] = $year = date('Y');
                }
                $this->setParam('year', $year);
            }


            if(!is_null($this->getParam('combination_id'))) {
                $params['filter_investor_combination_id'] = $this->getParam('combination_id');
            }

			$data = (new Object())->getVacancy(
				false,
                $broker && isset($year) ? $year . '-01-01' : false,
				$broker? loginManager::data()->info['investor_company_id'] : false,
                $params
			);


            if (is_null($this->getParam('inactive')) && !$this->isAjaxRequest) {
                $this->setParam('inactive', false);
            }

            $types = array(
                'project' => array('title' => 'Project', 'width' => 'large truncate', 'group_equal_rows' => true),
                'investor' => array('title' => 'Belegger', 'width' => 'hidden'),
                'og_descr' => array('title' => 'Objectgroep', 'width' => 'xxlarge truncate', 'group_equal_rows' => true),
                'street' => array('title' => 'Adres', 'width' => 'medium truncate'),
                'house_number' => array('title' => 'Nummer', 'width' => 'xsmall'),
                'build' => ['title' =>  $build_label ?:'#spec', 'width' => 'xxsmall'],
                'city' => array('title' => 'Stad', 'width' => 'medium truncate'),
                'inactive' => array('title' =>  'Inactief', 'width' => 'xsmall'),
                'from' => array('title' => 'Leeg vanaf', 'width' => 'small'),
                'acceptance_date' => array('title' => 'Aanvaarding', 'width' => 'small'),
                'till' => array('title' => 'Leeg tot', 'width' => 'small'),
                'days' => array('title' => 'Dagen leegstand ', 'width' => 'small'),
                'year_rent_for_empty_days' => array('title' => 'Kosten leegstand ', 'width' => 'small'),
                'm2' => ['title' => 'Oppervlakte', 'width' => 'xsmall right'],
                'spacer1' => ['title' => '', 'width' => 'xxxxsmall'],
                'notes' => array('title' => 'Opmerkingen', 'width' => 'xsmall'),
                'spacer' => array('title' => '&nbsp;', 'width' => 'xxxxsmall'),
            );

            $years = [date('Y') => ['title' => date('Y')]];

            if (isset($year)) {
                $years[$year] = ['title' => $year];
            }

            $filters =  array(
                'project' => array('renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'),
                'branch_name' => ['renderSeparately' => true, 'title' => 'Vestiging'],
                'investor_manager' => ['renderSeparately' => true, 'title' => 'Project accountmanager' , 'order_by_title' => 'ASC'],
                'investor' => ['renderSeparately' => true, 'title' => 'Belegger', 'order_by_title' => 'ASC'],
                'account_manager' => ['renderSeparately' => true, 'title' => 'Belegger accountmanager' , 'order_by_title' => 'ASC'],
                'og_descr' => array('renderSeparately' => true, 'title' => 'Objectgroep'),
                'year' => ['renderSeparately' => true, 'title' => 'Jaar', 'custom_options' => $years, 'hideCount' => true, 'show_all_disabled' => false],
                'inactive' => ['renderSeparately' => true, 'title' => 'Inactieve objecten'],
                'till_state' => [ 'title' => 'Periode', 'renderSeparately' => true, 'value' => 'current'],
                'from' => array('type' => 'date_range'),
                'street' => array('type' => 'input'),
                'house_number' => array('type' => 'input'),
                'build' => array('type' => 'input'),
                'm2' => ['type' => 'range'],
                'city' => array(),
            );

            if ($broker) {
				unset(
					$types['project'],
					$types['og_descr'],
					$types['inactive'],
					$filters['street'],
					$filters['house_number'],
					$filters['build'],
                    $filters['city'],
                    $filters['till_state'],
                    $filters['inactive']
				);
			}

            if (Settings::get('software_XXL')) {
                $types['project']['width'] = 'medium truncate';
                $types['og_descr']['width'] = 'medium truncate';
                array_add_after($types, 'project', [
                    'account_manager' => ['title' => 'Project accountmanager', 'width' => 'hidden'],
                    'investor_manager' => ['title' => 'Belegger accountmanager', 'width' => 'hidden'],
                ]);
            }

            $this->view->ListView($data)

				->setTypes($types)

				->addFormat('street', function($value, $item, $broker){
					return  $broker ? $value : '<a href="support/show/id/' . $item['object'] . '/type/object" target="_blank">' . $value . '</a>' ;
				})

				->addFormat('from', function($value){
					return $value  ? date('d-m-Y', strtotime($value)) : 'Onbekend ';
				})
                ->addFormat('acceptance_date', function($value){
                    return $value  ? date('d-m-Y', strtotime($value)) : 'Onbekend ';
                })

				->addFormat('till', function($value){
					return $value  ? date('d-m-Y', strtotime($value)) : 'Onbekend ';
				})

                ->addFormat('m2', function ($value) {
                    return $value > 0 ? new StringFormat($value, 'moneyRoundedDecimals') : '-';
                })

                ->addFormat('till_state', function($value){
                    $state_labels = [
                        'past' => 'Verleden',
                        'current' => 'Actueel',
                        'future' => 'Toekomstig'
                    ];

                    return $state_labels[$value];
                })

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . User::buildname(false, $value) . ($item['till'] ? ' <small>tot: ' . date('d-m-Y', strtotime($item['till'])) . '</small>' : '') . '</a>' : '-';
				})

                ->addFormat('investor', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    }

                    return $item['investor_name'];
                })
                
                ->addFormat('account_manager', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    } else {
                        $link_html = '<a href="support/show/id/' . $value . '/type/user" target="_blank">';
                        $link_html .= $item['account_manager_name'];
                        $link_html .= '</a>';

                        return $link_html;
                    }
                })
                ->addFormat('investor_manager', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    } else {
                        $link_html = '<a href="support/show/id/' . $value . '/type/user" target="_blank">';
                        $link_html .= $item['investor_manager_name'];
                        $link_html .= '</a>';

                        return $link_html;
                    }
                })

                ->addFormat('year_rent_for_empty_days', function($value){
                    return $value > 0 ? '€ '. new StringFormat($value,'money') : 'jaarhuur ontbreekt';
                })

                ->addFormat('price', 'money')
				->addFormat('inactive', 'bool')
				->addFormat('phone', 'phone')

                ->addFormat('notes', function($value){
                    if($this->getParam('view_template') == 'excel'){
                        return strip_tags(str_replace('<br>', "\n", $value));
                    } else {
                        return is_string($value) && $value !== '' ? '<span noHintButton="1" class="hasNotes" title="' . $value . '">&nbsp;</span>' : '-';
                    }
                })

				->setFilters($filters)

				->setOptions(array())

				->addTotals(['days','year_rent_for_empty_days'])

				->render($this);
		}


		public function vacancyAction() {
			$this->view->Breadcrumbs()->addCrumb("Leegstand");
			
			$minYear = 2000;
			$maxYear  = (int) date('Y') + 3;
			
			$projParam  = $this->_getParam('project');
			$fromParam  = $this->_getParam('from');
			$untilParam = $this->_getParam('until');
			
			$projectId = $projParam  ? $projParam  : false;
			$fromYear  = $fromParam  ? $fromParam  : $minYear;
			$untilYear = $untilParam ? $untilParam : $maxYear;
			
			$objModel  = new Object();
			$projModel = new Project();
			$invModel  = new Investors();
			
			$fromDate  = "{$fromYear}-01-01";
			$vacancy = Model::multiGroupBy($objModel->getVacancy($projectId, $fromDate), ['project_id', 'investor_id', 'year']);
			
			$projects = array();
			foreach ($projModel->getNamesByid() as $projId => $row) {
				$projects[$projId] = $row['long'];
			}
			
			$yearOpts = array();
			foreach (range($minYear, $maxYear) as $year) {
				$yearOpts[$year] = $year;
			}
			
			$this->view->vacancy      = $vacancy;
			$this->view->yearOpts     = $yearOpts;
			$this->view->projectOpts  = $projects;
			$this->view->projectId    = $projectId;
			$this->view->investorOpts = $invModel->getNamesById();
			$this->view->fromYear     = $fromYear;
			$this->view->untilYear    = $untilYear;
		}
		
		private function sortBy($field, &$arr, $sorting = SORT_ASC, $case_insensitive = true) {
			if (is_array($arr) && (count($arr) > 0)) {
				if ($case_insensitive == true)
					$strcmp_fn = "strnatcasecmp";
				else
					$strcmp_fn = "strnatcmp";

				if ($sorting == SORT_ASC) {
					$fn = create_function('$a,$b', '
		                if(is_array($a) && is_array($b)){
		                    return '
						. $strcmp_fn . '($a["' . $field . '"], $b["' . $field . '"]);
		                }else return 0;
		            ');
				}
				else {
					$fn = create_function('$a,$b', '
		                if(is_array($a) && is_array($b)){
		                    return '
						. $strcmp_fn . '($b["' . $field . '"], $a["' . $field . '"]);
		                }else return 0;
		            ');
				}
				usort($arr, $fn);
				return true;
			}
			else {
				return false;
			}
		}

        public function customObjectOverviewAction()
        {
            ini_set('memory_limit', '8000M');
            $this->view->extraWidth = true;
            $this->view->Breadcrumbs()
                ->addCrumb('Overzichten')
                ->addCrumb('Objecten overzicht');

            $data = Objects::getExtendedRealEstateReportData();
            $object_ids = array_column($data, 'id');
            $investorData = (new Investors())->getInvestorsForObjectIds($object_ids);
            $allInvestorIds = array_unique(array_values(array_flatten($investorData)));
            $investorNames = (new Investors())->getInvestorNames($allInvestorIds);
            $investorAbbreviations = (new Investors())->getInvestorAbbreviations($allInvestorIds);
            $sharedOwnershipPercentages = (new Investors())->getSharedOwnershipPercentages($object_ids);

            foreach ($data as $data_key => $data_item) {
                if (isset($investorData[$data_item['id']])) {
                    $data[$data_key]['investors'] = $investorData[$data_item['id']];
                }
            }


            $types = [
                'projectId' => ['title' => 'Project id', 'width' => 'medium truncate'],
                'projectName' => ['title' => 'Project', 'width' => 'medium truncate'],
                'objectgroupName' => ['title' => 'Objectgroep', 'width' => 'medium truncate'],
                'address' => ['title' => 'Adres', 'width' => 'medium truncate'],
                'number' => ['title' => 'Nummer', 'width' => 'xsmall left'],
                'build' => ['title' => Settings::get('build_label')  ? Settings::get('build_label') :  '#spcf.', 'width' => 'small'],
                'zipcode' => ['title' => 'Postcode', 'width' => 'xsmall'],
                'city' => ['title' => 'Plaats', 'width' => 'medium'],
                'tenant_name' => ['title' => 'Huurder', 'width' => 'medium truncate'],
                'from' => ['title' => 'Ingangsdatum', 'width' => 'xsmall', 'type' => 'date'],
                'period_formula' => ['title' => 'Verlengformule', 'width' => 'xsmall', 'format_current_page_items_only' => true],
                'expire' => ['title' => 'Expiratiedatum', 'width' => 'xsmall', 'type' => 'date'],
                'next_indexation' => ['title' => 'Indexatie', 'width' => 'xsmall', 'type' => 'date'],
                'notice_period' => ['title' => 'Opzegtermijn', 'width' => 'xsmall'],
                'investors' => ['title' => 'Belegger', 'width' => 'medium', 'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT],
                'woz_values_pr' => ['title' => 'WOZ waarde pr', 'width' => 'xxlarge', 'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT],
                'woz_values_ob' => ['title' => 'WOZ waarde ob', 'width' => 'xxlarge', 'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT],
                'energy_label' => ['title' => 'Energielabel', 'width' => 'xsmall  '],
                'purchase_price' => ['title' => 'Aanschafwaarde pr.', 'width' => 'large'],
                'appraisal_values' => ['title' => 'Taxatiewaarde pr.', 'width' => 'xxlarge', 'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT],
                'internal_appraisal_value' => ['title' => 'Geschatte waarde.', 'width' => 'medium right'],
                'taxation_value_object' => ['title' => 'Taxatiewaarde ob.', 'width' => 'medium right'],
                'spacer' => ['title' => '', 'width' => 'xxsmall'],
                'finances' => ['title' => 'Finances', 'width' => 'xxxxlarge', 'excel_export_formatting' => PHPExcel_Style_NumberFormat::FORMAT_TEXT],
                'total_amount_excl' => ['title' => 'Jaar huursom excl', 'width' => 'xsmall right'],
                'total_amount' => ['title' => 'Jaar huursom', 'width' => 'xsmall right'],
                'objectType' => ['title' => 'Objecttype', 'width' => 'xsmall right'],
                'vvo' => ['title' => 'VVO', 'width' => 'xsmall right'],
                'bvo' => ['title' => 'BVO', 'width' => 'xsmall right'],
                'rent_price_per_vvo' => ['title' => 'Prijs per m2(vvo)', 'width' => 'xsmall right'],

            ];

            $this->view->ListView($data)
                ->setTypes($types)
                ->addFormat('projectName', function($value, $row){
                    if(!$value){
                        return '-';
                    }
                    return '<a href="support/show/type/project/id/' . $row['projectId'] . '/">' . $row['projectName'] . '</a>';
                })
                ->addFormat('objectgroupName', function($value, $row){
                    if(!$value){
                        return '-';
                    }
                    return '<a href="support/show/type/objectgroup/id/' . $row['objectgroup'] . '/">' . $value . '</a>';
                })
                ->addFormat(['address', 'number', 'build'], function($value, $row){
                    if(!$value){
                        return '-';
                    }
                    return '<a href="support/show/type/object/id/' . $row['id'] . '/">' . $value . '</a>';
                })

                ->addFormat('zipcode', 'zipcode')

                ->addFormat('expire', function ($value, $row) {
                    $next_till_date = (new Object())->getNextTillDate(
                        $row['period_formula'],
                        $row['from'],
                        $row['notice_period']
                    );
                    return $next_till_date ? date("d-m-Y", $next_till_date) : '-';
                })
                ->addFormat('from', 'date')
                ->addFormat(['total_amount', 'total_amount_excl','rent_price_per_vvo', 'woz_values_pr','woz_values_ob','appraisal_values'], 'money')
                ->addFormat('taxation_value', 'money')
                ->addFormat('purchase_price', 'money')
                ->addFormat('internal_appraisal_value', 'money')
                ->addFormat('taxation_value_object', 'money')
                ->addFormat('woz_value', 'money')
                ->addFormat('investors', function ($value, $item) use ($investorNames, $investorAbbreviations, $sharedOwnershipPercentages) {
                    $objectId = $item['object'];
                    $investorName = isset($investorNames[$value]) ? $investorNames[$value] : '-';
                    $investorAbbreviation = isset($investorAbbreviations[$value]) ? $investorAbbreviations[$value] : '-';

                    $ownershipPercentage = 100;
                    if(isset($sharedOwnershipPercentages[$objectId])){
                        if(isset($sharedOwnershipPercentages[$objectId][$value])) {
                            $ownershipPercentage = $sharedOwnershipPercentages[$objectId][$value];
                        }
                    }

                    return
                        '<a href="support/show/id/' . $value . '/type/investor">' .
                        $investorAbbreviation . ' - ' .
                        $ownershipPercentage . '% - ' .
                        $investorName .
                        '</a><br />';
                })
                ->addFormat(['finances'], function ($value) {
                    if ($this->getParam('view_template') === 'excel') {
                        return str_replace('€', 'E', $value);
                    }
                    return $value;
                })

                ->addFormat('city', function($value){
                    return $value && $value != '' ? $value : 'Onbekend';
                })




                ->setFilters([
                    'projectName' => ['type' => 'select', 'order_by_value' => 'ASC'],
                    'objectgroupName' => ['order_by_value' => 'ASC'],
                    'address' => ['order_by_value' => 'ASC'],
                    'number' => ['type' => 'range'],
                    'build' => [],
                    'zipcode' => [],
                    'city' => [],
                    'tenant' => ['type' => 'input'],
                    'from' => ['type' => 'date_range']

                ])
                ->render($this);

        }
	}
