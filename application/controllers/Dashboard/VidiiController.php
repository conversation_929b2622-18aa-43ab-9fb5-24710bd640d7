<?php

use Controllers\Dashboard\Vidii\ListStageAvailableService;
use Controllers\Dashboard\Vidii\ListStageClearedForContractingDeadlineExceededService;
use Controllers\Dashboard\Vidii\ListStageClearedForContractingService;
use Controllers\Dashboard\Vidii\ListStageDocumentsRequestedDeadlineExceededService;
use Controllers\Dashboard\Vidii\ListStageDocumentsRequestedService;
use Controllers\Dashboard\Vidii\ListStageDocumentsSubmittedService;
use Controllers\Dashboard\Vidii\ListStageOccupiedService;
use Controllers\Dashboard\Vidii\ListStageRequest;
use Controllers\Dashboard\Vidii\ListStageSubscribedService;
use Controllers\Dashboard\Vidii\StageAvailableService;
use Controllers\Dashboard\Vidii\StageClearedForContractingDeadlineExceededService;
use Controllers\Dashboard\Vidii\StageClearedForContractingService;
use Controllers\Dashboard\Vidii\StageDocumentsRequestedDeadlineExceededService;
use Controllers\Dashboard\Vidii\StageDocumentsRequestedService;
use Controllers\Dashboard\Vidii\StageDocumentsSubmittedService;
use Controllers\Dashboard\Vidii\StageOccupiedService;
use Controllers\Dashboard\Vidii\StageRequest;
use Controllers\Dashboard\Vidii\StageSubscribedService;

class Dashboard_VidiiController extends GlobalController
{
    public function workflowAction()
    {
        $this->appendJqueryFiles($this->view);
    }

    public function countStageAction()
    {
        $this->disableView();
        $stage = $this->getParam('use_stage', false);
        $projectIds = $this->getParam('project', []);

        if (is_array($projectIds)) {
            $projectIds = array_filter($projectIds);
        }

        if (!$stage) {
            dieWithStatuscode(400);
        }

        $stageRequest = new StageRequest($projectIds);

        $service = '';
        switch ($stage) {
            case 'available':
                $service = new StageAvailableService($stageRequest);
                break;

            case 'subscribed':
                $service = new StageSubscribedService($stageRequest);
                break;

            case 'documents_requested':
                $service = new StageDocumentsRequestedService($stageRequest);
                break;

            case 'documents_requested_deadline_exceeded':
                $service = new StageDocumentsRequestedDeadlineExceededService($stageRequest);
                break;

            case 'documents_submitted':
                $service = new StageDocumentsSubmittedService($stageRequest);
                break;

            case 'cleared_for_contracting':
                $service = new StageClearedForContractingService($stageRequest);
                break;

            case 'cleared_for_contracting_deadline_exceeded':
                $service = new StageClearedForContractingDeadlineExceededService($stageRequest);
                break;

            case 'occupied':
                $service = new StageOccupiedService($stageRequest);
                break;

            default:
                dieWithStatuscode(400);
        }

        echo $service->getCount();
    }

    public function listStageAction()
    {
        $stage = $this->getParam('use_stage', false);
        $projectIds = $this->getParam('project', false);

        if (strpos($projectIds, ',') !== false) {
            $projectIds = explode(',', $projectIds);
        } elseif (is_numeric($projectIds)) {
			$projectIds = [$projectIds];
		}

        if (!$stage) {
            $this->_helper->redirector('vidii', 'dashboard');
        }

        if (!$this->getParam('sorting')) {
            $this->setParam('sorting', json_encode([
                'rendered_address' => 'ASC',
                'last_change_days_diff' => 'DESC',
            ]));
        }

        $stageRequest = new StageRequest($projectIds);

        switch ($stage) {
            case 'available':
                $request = new ListStageRequest(new StageAvailableService($stageRequest), $this->view);
                $service = new ListStageAvailableService();
                break;

            case 'subscribed':
                $request = new ListStageRequest(new StageSubscribedService($stageRequest), $this->view);
                $service = new ListStageSubscribedService();
                break;

            case 'documents_requested':
                $request = new ListStageRequest(new StageDocumentsRequestedService($stageRequest), $this->view);
                $service = new ListStageDocumentsRequestedService();
                break;

            case 'documents_requested_deadline_exceeded':
                $request = new ListStageRequest(
                    new StageDocumentsRequestedDeadlineExceededService($stageRequest),
                    $this->view
                );
                $service = new ListStageDocumentsRequestedDeadlineExceededService();
                break;

            case 'documents_submitted':
                $request = new ListStageRequest(new StageDocumentsSubmittedService($stageRequest), $this->view);
                $service = new ListStageDocumentsSubmittedService();
                break;

            case 'cleared_for_contracting':
                $request = new ListStageRequest(new StageClearedForContractingService($stageRequest), $this->view);
                $service = new ListStageClearedForContractingService();
                break;

            case 'cleared_for_contracting_deadline_exceeded':
                $request = new ListStageRequest(
                    new StageClearedForContractingDeadlineExceededService($stageRequest),
                    $this->view
                );
                $service = new ListStageClearedForContractingDeadlineExceededService();
                break;

            case 'occupied':
                $request = new ListStageRequest(new StageOccupiedService($stageRequest), $this->view);
                $service = new ListStageOccupiedService();
                break;

            default:
                dieWithStatuscode(400);
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $listview = $service->execute($request);

        $projectName = (count($projectIds) == 1 && $project = Projects::get($projectIds[0])) ? $project['name'] : false;
        $stageLabel = $service->formatStage($stage);

        if ($this->isAjaxRequest) {
			echo $listview->render($this);
			die();
		}

        $this->view->Breadcrumbs()
            ->addCrumb('Vidii dashboard', 'dashboard/vidii')
            ->addCrumb(
                'Inschrijvingen status' . ($projectName ? " in $projectName" : ''),
                $projectName ? "dashboard_vidii/list-stage/use_stage/$stage/project/$projectIds[0]" : ''
            );

        $this->view->stageLabel = $stageLabel;
        $this->view->listView = $listview->render($this);
    }
}
