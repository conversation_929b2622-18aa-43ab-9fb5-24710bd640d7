<?
	class RelationTypeController extends GlobalController {


		/**
		 * Redirects to the 'list' action
		 * 
		 */
		public function indexAction() {
			$this->_helper->redirector('list', 'relation-type');
		}

		/**
		 * Lists all Object types using a Listview
		 * 
		 */
		public function listAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Relatietype overzicht', '');

			$data = RelationType::getList();

			$this->view->listView($data)

				->setTypes(array(
					'name' => array(
						'title' => 'Naam',
						'width' => 'xxxxxlarge truncate',
					),
				))

				->setOptions(array(
					'item_title' => 'Relatietype',
					'disabled_buttons_test' => function($row, $button){
						if($button !== 'delete')
							return false;

						if(intval($row['id']) < 1)
							return true;

						$cModel = new Company();
						$companies = $cModel->fetchAll($cModel->select()->where('relation_type = ?', $row['id']));
						return (count($companies) > 0);
					}
				))

				->addButtons(array(
					'add' => 'Toevoegen',
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen',
				),
				array(
					'id' => 'id',
				))

				->render($this);
		}

		/**
		 * Deletes the RelationType indicated by the id parameter
		 * and redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function deleteAction() {

			$this->disableView();
			$id = $this->_getParam('id');
			$cModel = new Company();
			$ctModel = new RelationType();

			if(intval($id) < 1)
				$this->_helper->redirector('list', 'relation-type');

			$companies = $cModel->fetchAll($cModel->select()->where('relation_type = ?', $id));

			if(isset($companies) && count($companies) > 0)
				$this->_helper->redirector('list', 'relation-type');

			$ct = $ctModel->getById($id);

			if(isset($ct))
				$ct->delete();

			$this->_helper->redirector('list', 'relation-type');
		}


		public function editAction() {

			$this->view->Breadcrumbs()
				->addCrumb('Relatietypes overzicht', 'relation-type/list/')
				->addCrumb('Relatietype bewerken', '');

			// this is to have the objecttypeId available in the form
			// through $this->getOwner()->id
			$this->objectTypeid = $this->_getParam('id');

			$this->view->form = $this->view->EditView($this, 'RelationType',
				array('id' => $this->_getParam('id')))
				->render($this);

		}
	}
?>
