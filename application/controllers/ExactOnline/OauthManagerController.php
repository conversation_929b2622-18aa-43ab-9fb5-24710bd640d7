<?php


use OAuth\Common\Storage\OmniboxxCacheDir;

class ExactOnline_OauthManagerController extends \GlobalController
{

    const TOKEN_KEY_NAME = 'ExactOnline';
    public function preDispatch()
    {
        $this->renderWithNewLayout();
        $this->view->assign('extraWidth', true);

        $this->view->Breadcrumbs()
            ->addCrumb('Instellingen')
            ->addCrumb('Koppelingen')
            ->addCrumb('Exact online oauth manager');
    }

    public static function getTokensByType($tokenKeyName)
    {
        $storage = new OmniboxxCacheDir();
        $tokenList = $storage->retrieveAllAccessToken();

        array_filter($tokenList, function($value) use ($tokenKeyName) {
            if (!strpos($value, $tokenKeyName)) {
                return false;
            }
            return false;
        },ARRAY_FILTER_USE_KEY);

        $data = [];
        foreach ($tokenList as $name => $item) {
            $nameArr = explode('_', $name);
            if (count($nameArr) == 2) {
                $data[] = [
                    'key' => $name,
                    'name' => $nameArr[1],
                ];
            } else {
                $data[] = [
                    'key' => $name,
                    'name' => $nameArr[0],
                ];
            }
        }

        return $data;
    }
    public function listAction()
    {

        $data = self::getTokensByType(self::TOKEN_KEY_NAME);

        $this->view->ListView($data)
            ->setTypes([
                'name' => ['title' => 'Token name', 'width' => 'large'],
                'actions' => ['title' => '', 'width' => 'large'],
            ])
            ->addFormat('actions', function($value, $row) {
                $htmlStr = '';

                if (self::TOKEN_KEY_NAME == $row['key']) {
                    return $htmlStr;
                }

                $htmlStr = "<a href='exact-online_oauth-manager/remove/token_key/{$row['key']}'>
                            Verwijderen <i class='fa fa-remove'></i>
                        </a>";

                return $htmlStr;
            })
            ->addFormat('object_rendered_address', function ($value, $row) {
                return "<a href='object/edit/id/{$row['object_id']}'>$value</a>";
            })
            ->addFormat('error', function ($value) {
                return wordwrap($value, 150, "<br />\n");
            })
            ->setOptions([
                'paginator_items_per_page' => 100,
            ])
            ->addLegend('add-form')
            ->render($this);
    }



    public function removeAction()
    {
        $this->disableView();
        $token_key = $this->getParam('token_key');

        if (!$token_key) {
            $this->_redirect('exact-online_oauth-manager/list');
        }

        $storage = new OmniboxxCacheDir();
        $storage->clearToken($token_key);

        $this->_redirect('exact-online_oauth-manager/list');
    }

    public function addAction()
    {
        $this->disableView();
        $oauthStorageKey = $this->getParam('oauthStorageKey');
        $oauthStorageKey = trim($oauthStorageKey);
        $oauthStorageKey = str_replace(' ', '_', $oauthStorageKey);


        $this->_redirect('exact-online_oauth/start', ['oauthStorageKey' => $oauthStorageKey]);
    }





}
