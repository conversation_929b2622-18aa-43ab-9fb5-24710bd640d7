<?php


class ExactOnline_OauthController extends \GlobalController
{

    public function startAction()
    {
        $this->disableView();

        $oauthStorageKey = $this->getParam('oauthStorageKey');

        if (!$oauthStorageKey) {
            $oauthStorageKey = 'oauthStorageKey';
        }

        $exactOnlineOAuth2 = $this->setExactOnlineOauth($oauthStorageKey);

        header('Location: '. $exactOnlineOAuth2->getAuthorizationUri());
    }

    public function saveTokenAction()
    {
        $this->disableView();

        $oauthStorageKey = $this->getParam('oauthStorageKey');
        $code = $this->getParam('code');

        if (!$code) {
            return;
        }
        $exactOnlineOAuth2 = $this->setExactOnlineOauth($oauthStorageKey);

        $exactOnlineOAuth2->requestAccessToken($code);


//        $this->_redirect('exact-online_oauth-manager/list');
    }


    private function setExactOnlineOauth($oauthStorageKey)
    {
        $callbackUrl = $this->getCallbackUrl($oauthStorageKey);
        $httpClient = new \OAuth\Common\Http\Client\CurlClient();

        $redirect_uri = \Accounting\Infrastructure\ExactOnline\Model\Credentials::$oAuthRedirectUri;
        $redirect_to = http_build_query(['redirect_to' => $this->getCallbackUrl($oauthStorageKey)]);


        $credentials = new \OAuth\Common\Consumer\Credentials(
            \Accounting\Infrastructure\ExactOnline\Model\Credentials::$oAuthClientId,
            \Accounting\Infrastructure\ExactOnline\Model\Credentials::$oAuthClientSecret,
            $redirect_uri .'?'. $redirect_to
        );

        $storage = new \OAuth\Common\Storage\SetTokenKey(
            new \OAuth\Common\Storage\OmniboxxCacheDir(),
            $oauthStorageKey
        );

        $exactOnlineOAuth2 = new \OAuth\OAuth2\Service\ExactOnline(
            $credentials,
            $httpClient,
            $storage
        );

        return $exactOnlineOAuth2;
    }

    private function getCallbackUrl($oauthStorageKey)
    {
        $redirectUri = 'https://'. $_SERVER['SERVER_NAME'] ."/exact-online_oauth/save-token/oauthStorageKey/". $oauthStorageKey;
        return $redirectUri;
    }




}
