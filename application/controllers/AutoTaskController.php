<? 
include_once('Task.php');

class AutoTaskController extends GlobalController {

	public function preDispatch(){
		$this->view->Breadcrumbs()->addCrumb('Geautomatiseerde taken', 'auto-task');

		$this->events = new AutotaskEvents();
		$this->tasks = new AutotaskTasks();
		$this->applied = new AutotaskApplied();
	}

	public function indexAction() {

		foreach($this->events->fetchAll()->toArray() as $event){
			$event['applied'] = $this->appliedCount($event['id']);
			$events[] = $event;
		}

		$this->view->events = (array) $events;
	}

	public function tasksAction(){
		$p = new Projects();

		if(!$this->_getParam('event'))
			return;

		foreach($this->applied->fetchAll($this->applied->select()->where('event = ?',  $this->_getParam('event')))->toArray() as $apply){
			if($apply['type'] == 'project'){
				$project = $p->fetchRow($p->select()->where('id = ?', $apply['map_to']));
				$apply['name'] = $project->name;
			}
			$applied[] = $apply;
		}
		
		$this->view->applied = $applied;
		$this->view->event = $this->events->fetchRow($this->events->select()->where('id = ?', $this->_getParam('event')));
		$this->view->tasks = $this->tasks->fetchAll($this->tasks->select()->where('event = ?', $this->_getParam('event')));

		$this->view->Breadcrumbs()->addCrumb('Gebeurtenis \'' . $this->view->event->name . '\'');
	}

	public function editTaskAction(){

		if(!$this->_getParam('event') && !$this->_getParam('id'))
			return;

		$form = new Form($this, 'auto-task/task');

		$task = $this->_getParam('id') ? $this->tasks->fetchRow($this->tasks->select()->where('id = ?', $this->_getParam('id'))) : false;
		$event = $this->events->fetchRow($this->events->select()->where('id = ?', $this->_getParam('event') ? $this->_getParam('event') : $task->event));

		$this->view->Breadcrumbs()
			->addCrumb('Taken bij \'' . $event->name . '\'', 'auto-task/tasks/event/' . $event->id . '/')
			->addCrumb('Taak bewerken');

		foreach(TaskList::model()->allUsers() as $user){		
			$options = $form->task->user->getAttrib('options');
			$options[$user['id']] = $user['full_name'];
			$form->task->user->setAttrib('options', $options);
		}


		$form->task->user->setRegisterInArrayValidator(false);

		$form->populate($this->taskFormArray($task));	

		if ($this->getRequest()->isPost())
			if($form->isValid($this->getRequest()->getPost())){
				$post = $this->getRequest()->getPost();

				$task = $task ? $task : $this->tasks->createRow();

				if($event->id)
					$task->event = $event->id;

				$task->title = $post['task']['title'];
				$task->description = $post['task']['description'];
				$task->user = $post['task']['user'];
				$task->offset = $post['time']['offset'];
				$task->repeat = $post['time']['repeat'];
				
				$task->save();

				$this->_redirect('auto-task/tasks', array('event' => $event->id));
			}

		$this->view->form = $form;
		$this->view->event = $event;			
	}

	public function editEventAction(){
		$this->view->Breadcrumbs()->addCrumb('Gebeurtenis bewerken');

		$form = new Form($this, 'auto-task/event');

		$event = $this->_getParam('id') ? $this->events->fetchRow($this->events->select()->where('id = ?', $this->_getParam('id'))) : false;

		if($event)
			$form->populate($event->toArray());	

		if ($this->getRequest()->isPost())
			if($form->isValid($this->getRequest()->getPost())){
				$post = $this->getRequest()->getPost();

				$event = $event ? $event : $this->events->createRow();
				
				$event->name = $post['name'];
				
				$event->save();

				$this->_redirect('auto-task/index');
			}

		$this->view->form = $form;
		$this->view->event = $event;			
	}

	public function appliedTasksAction(){
		$p = new Projects();
		$t = new TasksTable();

		if(!$this->applied->fetchRow($this->applied->select()->where('map_to = ?', $this->_getParam('map_to'))->where('type = ?', $this->_getParam('type'))->where('event = ?', $this->_getParam('event'))))
			die();

		$event = $this->events->fetchRow($this->events->select()->where('id = ?', $this->_getParam('event')));

		if($this->_getParam('type') == 'project')
			$name = $p->fetchRow($p->select()->where('id = ?', $this->_getParam('map_to')));

		foreach($this->tasks->fetchAll($this->tasks->select()->where('event = ?', $this->_getParam('event')))->toArray() as $task){
			$instances = array();
			
			foreach($t->fetchAll($t->select()->where('auto = ?', $task['id'])->where('project = ?', $this->_getParam('map_to'))) as $instance)
				$instances[] = $instance;

			$task['instances'] = count($instances) > 0 ? $instances : array('user' => 0);
			$task['date'] = $applied ? strtotime($applied->dueDate) : ($name->startdate ? strtotime($task['offset'] . ' days', strtotime($name->startdate)) : false);
			$task['user'] = $applied ? $applied->user : $task['user'];

			$tasks[] = $task;
		}

		$this->view->users = TaskList::model()->allUsers();
		$this->view->tasks = $tasks;
		$this->view->map_to = $this->_getParam('map_to');
		$this->view->type = $this->_getParam('type');

		$this->view->Breadcrumbs()
			->addCrumb('Taken bij \'' . $event->name . '\'', 'auto-task/tasks/event/' . $event->id . '/')
			->addCrumb('Taken toegepast in \'' . $name->name . '\'');
	}

	public function applyTaskAction(){
		$this->disableView();

		if(!$this->_getParam('id') || !$this->_getParam('map_to') || !$this->_getParam('user'))
			die();
		
		$atask = $this->tasks->fetchRow($this->tasks->select()->where('id = ?', $this->_getParam('id')));

		AutotaskTasks::apply($atask, array(
			'map_to' => $this->_getParam('map_to'),
			'type' => 'project',
			'user' => $this->_getParam('user')
		));
	}

	public function setUserAction(){
		$this->disableView();

		$t = new TasksTable();

		if(!$this->_getParam('id') || !$this->_getParam('user'))
			die();
		
		$task = $t->fetchRow($t->select()->where('id = ?', $this->_getParam('id'))->where('auto IS NOT NULL'));

		if(!$task)
			die();

		$task->user = $this->_getParam('user');
		$task->save();
	}

	public function applyEventAction(){
		$this->disableView();

		if(!$this->_getParam('type') || !$this->_getParam('map_to') || !$this->_getParam('event')  || !$this->_getParam('date'))
			die();

		$ata = new AutotaskApplied();

		$row = $ata->fetchRow(
			$ata->select()
				->where('event = ?', 	$this->_getParam('event'))
				->where('type = ?', 	$this->_getParam('type'))
				->where('map_to = ?', 	$this->_getParam('map_to')));

		$row = $row ? $row : $ata->createRow();
		
		$row->event = 	$this->_getParam('event');
		$row->date = 	$this->_getParam('date');
		$row->type = 	$this->_getParam('type');
		$row->map_to = 	$this->_getParam('map_to');
		
		$row->save();
	}

	public function taskFormArray($data){
		$array = array();

		$array['task']['title'] = $data->title;
		$array['task']['description'] = $data->description;
		$array['task']['user'] = $data->user;
		$array['time']['offset'] = $data->offset;
		$array['time']['repeat'] = $data->repeat;

		return $array;
	}

	public function appliedCount($event){
		$row = $this->applied->fetchRow($this->applied->select()->from('autotask_applied', array('count' => 'COUNT(`id`)'))->where('event = ?', $event));

		return $row->count;

	}
}