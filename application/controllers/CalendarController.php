<?php

use Controllers\Calendar\EventSaveService;

class CalendarController extends GlobalController
{

    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Agenda');
    }

    public function dashboardAction()
    {
        if (Settings::get('outlook_calendar_enable') && Settings::get('modules_user_project')) {
            $this->synchroniseLinkedCalendarsBranchelocation();
        } else if (Settings::get('outlook_calendar_enable')) {
            $this->synchroniseLinkedCalendars();
        }
        $this->view->extraWidth = true;

        $this->appendJqueryFiles($this->view);
        if (Settings::get('modules_user_project')) {
            $this->view->users = (new Calendars())->getAdminUsersBranchelocation();
        } else {
            $this->view->users = (new Calendars())->getAdminUsers();
        }
        $this->view->user = $this->_getParam('user') ?: false;
        $this->view->object = $this->_getParam('object') ?: false;
        $this->view->event_type_id = $this->_getParam('event_type_id') ?: false;
        $this->view->summary = $this->_getParam('summary') ? urldecode($this->_getParam('summary')) : false;
        $this->view->map_to_type = $this->_getParam('map_to_type') ?: false;
        $this->view->map_to_id = $this->_getParam('map_to_id') ?: false;
        $this->view->userSelection = (array) $_SESSION['calendarUserSelection'];
    }

    private function synchroniseLinkedCalendars()
    {
        $syncService = new \Calendar\Sync\Application\SynchroniseCalendarService();
        $tokenClient = new \Calendar\Sync\Infrastructure\CalendarTokenRestClient();
        $selectedBranch = \loginManager::data()->selected_branch_location;
        $select = db()->select()
            ->from(['u' => 'users'], ['calendar_id', 'calendar_token', 'id'])
            ->where('u.calendar_token IS NOT NULL');
        $usersToSync = db()->fetchAll($select);
        foreach ($usersToSync as $userRow) {
            try {
                $accessTokenData = json_decode(
                    $tokenClient->getAccessTokenByRefreshToken($userRow['calendar_token']),
                    true
                );
                $syncService->execute($accessTokenData['access_token'], $userRow['id'], $userRow['calendar_id']);
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                $response = $e->getResponse();
                if (null !== $response && $response->getStatusCode() === 400) {
                    $userModel = new Users();
                    $userDbRow = $userModel->getById($userRow['id']);
                    if (null === $userDbRow) {
                        error_log($e->getMessage());
                        continue;
                    }
                    $userDbRow->calendar_token = nullValue();
                    $userDbRow->save();
                    if ($userRow['id'] === \loginManager::data()->id) {
                        $this->_redirect('synchronise');
                        exit;
                    }
                } else {
                    error_log($e->getMessage());
                }
            }
        }
    }
    private function synchroniseLinkedCalendarsBranchelocation()
    {
        $syncService = new \Calendar\Sync\Application\SynchroniseCalendarService();
        $tokenClient = new \Calendar\Sync\Infrastructure\CalendarTokenRestClient();
        $selectedBranch = \loginManager::data()->selected_branch_location;
        $select = db()->select()
            ->from(['u' => 'users'], ['calendar_id', 'calendar_token', 'id'])
            ->where('u.calendar_token IS NOT NULL')
            ->where('u.branch_location = ?', $selectedBranch);
        $usersToSync = db()->fetchAll($select);
        foreach ($usersToSync as $userRow) {
            try {
                $accessTokenData = json_decode(
                    $tokenClient->getAccessTokenByRefreshToken($userRow['calendar_token']),
                    true
                );
                $syncService->execute($accessTokenData['access_token'], $userRow['id'], $userRow['calendar_id']);
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                $response = $e->getResponse();
                if (null !== $response && $response->getStatusCode() === 400) {
                    $userModel = new Users();
                    $userDbRow = $userModel->getById($userRow['id']);
                    if (null === $userDbRow) {
                        error_log($e->getMessage());
                        continue;
                    }
                    $userDbRow->calendar_token = nullValue();
                    $userDbRow->save();
                    if ($userRow['id'] === \loginManager::data()->id) {
                        $this->_redirect('synchronise');
                        exit;
                    }
                } else {
                    error_log($e->getMessage());
                }
            }
        }
    }


    public static function appendJqueryFiles($view)
    {
        $view->headScriptHashed()->appendFile('media/javascript/jquery.js');

        $view->headScriptHashed()->appendFile('media/javascript/jquery-ui.js');
        $view->headLink()->appendStylesheet('media/style/jquery-ui/jquery-ui.css');
        $view->headLink()->appendStylesheet('media/style/jquery-ui/jquery-ui.structure.css');
        $view->headLink()->appendStylesheet('media/style/jquery-ui/jquery-ui.theme.css');


        $view->headScriptHashed()->appendFile('media/javascript/moment-with-locales.js');
        $view->headScriptHashed()->appendFile('media/javascript/fullcalendar.js');
        $view->headLink()->appendStylesheet('media/style/fullcalendar/fullcalendar.css');

        $view->headScriptHashed()->appendFile('media/javascript/jquery.timepicker.js');
        $view->headLink()->appendStylesheet('media/style/jquery.timepicker.css');


        $view->headScriptHashed()->appendFile('media/javascript/tagify.js');
        $view->headLink()->appendStylesheet('media/style/tagify.css');
    }

    public function saveUserSelectionAction()
    {
        $this->disableView();
        $selection = $this->getParam('value');
        $selection = explode(',', $selection);

        if(!is_array($selection)){
            return;
        }

        $_SESSION['calendarUserSelection'] = $selection;
    }

    public function getEventsAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $type = $this->_getParam('type');
        $link = $this->_getParam('link');

        $start = strtotime($this->_getParam('start')) ?: strtotime('first day of');
        $end = strtotime($this->_getParam('end')) ?: strtotime('last day of');

        $color = '#' . substr(sha1($link), 0, 6); // alle kleuren

        // grijs
        // $nr = substr(sha1($link), 0, 2);
        // $nr = dechex( 48 + round(hexdec($nr)*0.8) );
        // $color = '#'.str_repeat($nr, 3);

        $events = Calendars::getEventsForStartBetween($type, $link, $start, $end);

        $colorSelect = db()->select()
            ->from('users', ['id', 'calendar_color'])
            ->where('calendar_color IS NOT NULL');

        $calendarColors = db()->fetchPairs($colorSelect);

        $items = [];
        foreach ($events as $event) {
            $newItem = [];

            $newItem['id'] = $event->id;
            $newItem['title'] = $event->summary;
            $newItem['start'] = date(DateTime::ISO8601, strtotime($event->start));
            $newItem['end'] = date(DateTime::ISO8601, strtotime($event->end));
            $newItem['selectable'] = true;
            $newItem['color'] = '#4e4e4e';

            if ($event['calendar_link'] && isset($calendarColors[$event['calendar_link']])) {
                $newItem['color'] = $calendarColors[$event['calendar_link']];
            }

            $items[] = $newItem;
        }

        echo json_encode($items);
    }

    public function eventDetailsAction()
    {
        $eventId = $this->_getParam('eventId');

        $this->appendJqueryFiles($this->view);

        $users = [];

        if (Settings::get('modules_user_project')) {
            $calendarDepartments = (new Calendars())->getAdminUsersBranchelocation();
        } else {
            $calendarDepartments = (new Calendars())->getAdminUsers();
        }

        foreach ($calendarDepartments as $calendarDepartment) {
            $calendarDepartment['id'] = $calendarDepartment['id'] ?: '0';

            if (!isset($users['calendar_' . $calendarDepartment['id']])) {
                $users['calendar_' . $calendarDepartment['id']] = [
                    'name' => $calendarDepartment['name'],
                    'id' => $calendarDepartment['id'],
                    'users' => [],
                ];
            }

            foreach ($calendarDepartment['users'] as $calendarUserId => $calendarUserName) {
                $users['calendar_' . $calendarDepartment['id']]['users'][$calendarUserId] = $calendarUserName;
            }
        }

        $this->view->users = $users;


        $autoCompleterDataFormatterService =
            new \Calendar\Events\Contacts\AutoCompleterDataFormatterService(
                new CalendarEventsContacts(),
                new Users()
            );

        if (intval($eventId) > 0 && $event = CalendarEvents::get($eventId)) {
            $calenderEventContactsRows = (new CalendarEventsContacts())
                ->matchAll(['calendar_event_id' => $eventId])
                ->toArray();
        } else {
            $params = $this->getAllParams();

            $ceModel = new CalendarEvents();
            $event = $ceModel->createRow();

            $event->object_id = $this->_getParam('object') ?: false;
            $event->event_type_id = $this->_getParam('event_type_id') ?: false;
            $event->summary = $this->_getParam('summary') ?: false;
            $event->map_to_type = $this->_getParam('map_to_type') ?: false;
            $event->map_to_id = $this->_getParam('map_to_id') ?: false;

            $calenderEventContactsRows = (new \Calendar\Events\Contacts\CreateRowsFromUrlParameterService())
                ->execute($this->getParam('user'));

            if (!empty($params['presets']) && is_array($params['presets'])) {
                $start = strtotime($params['presets']['start']);
                $start = $start ?: time();
                $event->start = date('Y-m-d H:i', $start);

                $end = strtotime($params['presets']['end']);
                $end = $end ?: time();
                $event->end = date('Y-m-d H:i', $end);
            }
        }
        $this->view->event = $event;
        $this->view->event_types = CalendarEventsTypes::getArray();


        $this->view->eventsContacts = $autoCompleterDataFormatterService->execute($calenderEventContactsRows);
    }

    public function eventSaveAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $service = new EventSaveService(
            new CalendarEvents(),
            new CalendarEventsContacts(),
            new Corporation()
        );

        $params = $this->getAllParams();

        unset($params['controller'], $params['action'], $params['module']);

        $eventRow = $service->execute($params);
        if (Settings::get('outlook_calendar_enable')) {
            if ($eventRow !== null) {
                $this->createEventInLinkedOutlookCalendar($eventRow->toArray());
            }
        }

        $params['link'] = explode('_', $params['link']);

        if (count($params['link']) === 2 && is_numeric($params['link'][1])) {
            $params = [
                'type' => $params['link'][0],
                'link' => $params['link'][1],
            ];

            startWorker(
                'build-ical-cache/type/' . $params['type'] . '/link/' . $params['link'] . '/',
                'cronjob_calendar',
                'direct',
                true
            );
        }
    }

    public function eventDeleteAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $eventId = $this->_getParam('id');
        $event = CalendarEvents::get($eventId);
        if (null === $event) {
            dieWithStatuscode();
        } else {
            try {
                if (!empty($event['external_id'])) {
                    $this->deleteEventFromLinkedOutlookCalendar($event);
                }
            } catch (Exception $e) {
                error_log($e->getMessage());
                dieWithStatuscode();
            } finally {
                $event->delete('id = ?', $eventId);
            }
        }
        $this->_redirect('dashboard');
    }

    private function deleteEventFromLinkedOutlookCalendar($calendarEventRowData)
    {
        $userModel = new Users();
        $userRow = $userModel->getById($calendarEventRowData['calendar_link']);
        if (null === $userRow) {
            dieWithStatusCode();
        }
        $refreshToken = $userRow->calendar_token;
        $calendarId = $userRow->calendar_id;
        $tokenClient = new \Calendar\Sync\Infrastructure\CalendarTokenRestClient();
        $accessTokenData = json_decode($tokenClient->getAccessTokenByRefreshToken($refreshToken), true);
        $eventDeleteService = new \Calendar\Sync\Application\DeleteCalendarEventByGraphIdService(
            new \Calendar\Sync\Infrastructure\GraphRestClient()
        );
        try {
            $eventDeleteService->delete(
                $calendarEventRowData['external_id'],
                $accessTokenData['access_token'],
                $calendarId
            );
        } catch (Exception $e) {
            error_log($e->getMessage());
        }
    }

    private function createEventInLinkedOutlookCalendar($calendarEventRowData) {
        if ('user' != $calendarEventRowData['calendar_type']) {
            return;
        }

        $userModel = new Users();
        $userRow = $userModel->getById($calendarEventRowData['calendar_link']);
        if (null === $userRow) {
            dieWithStatusCode();
        }
        $refreshToken = $userRow->calendar_token;
        $calendarId = $userRow->calendar_id;
        $tokenClient = new \Calendar\Sync\Infrastructure\CalendarTokenRestClient();
        $accessTokenData = json_decode($tokenClient->getAccessTokenByRefreshToken($refreshToken), true);
        $postCalendarEventService = new \Calendar\Sync\Application\PostCalendarEventByGraphIdService(
            new \Calendar\Sync\Domain\GraphCalendarEvent\GraphCalendarEventFactory(),
            new \Calendar\Sync\Infrastructure\GraphRestClient(),
            new \CalendarEvents()
        );
        $postCalendarEventService->post($calendarEventRowData, $accessTokenData['access_token'], $calendarId);
    }

    public function getIcalAction()
    {
        $this->disableView();

        $filename = $this->getParam('filename', 'calendar.ics');
        $addTimezone = (boolean)$this->getParam('add-timezone', false);

        $params = [
            'type' => $this->getParam('type'),
            'link' => $this->getParam('link'),
            'addTimezone' => $addTimezone ? 1 : 0
        ];

        $calenderCache = new Cache_Calendar();
        $calenderCache->setIdParams($params);

        $iCalCacheData = $calenderCache->load();

        if (!$iCalCacheData) {
            startWorker(
                'build-ical-cache/type/' . $params['type'] . '/link/' . $params['link'] . '/',
                'cronjob_calendar',
                'direct',
                true
            );
            http_response_code(503);
            return;
        }

        header('Content-Type: text/calendar; charset=utf-8');
        header("Content-Disposition: attachment; filename=\"{$filename}\"");

        echo $iCalCacheData;
        die();
    }

    public function objectSearchAction()
    {
        $this->disableView();

        $term = $this->_getParam('term') ?: '';

        $select = db()->select()
            ->from(array('o' => 'objects'), array('id', 'build'))
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
            ->joinLeft(array('a' => 'address'), 'a.type_id = o.id AND a.type = "object"', array('address', 'number', 'city', 'zipcode'))
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', false)
            ->where('a.id IS NOT NULL')
            ->where('CONCAT(a.address, " ", a.number, "(", o.build, "), ", a.zipcode, " te ", a.city) LIKE "%' . $term . '%"')
            ->where('p.deleted = ?', false)
            ->where('p.sales_status = ?', 'Closed')
            ->where('p.exploitation = ?', 1);

        UsersProjects::filterSelect($select, ['p.id']);

        $objects = array();
        $a_lib = new Address();

        foreach (db()->fetchAll($select) as $object) {
            $address = $a_lib->buildname(false, 'object', $object);

            $objects[] = array(
                'id' => $object['id'],
                'value' => $address,
                'label' => $address
            );
        }

        $objects = orderArrayByLevenstein($objects, $term, 'label');
        $objects = array_slice($objects, 0, 20);

        ob_clean();
        echo json_encode($objects);
    }

    public function registrantSearchAction()
    {
        $this->disableView();

        $term = $this->_getParam('value') ?: '';

        $select = db()->select()
            ->from(
                ['u' => 'users'],
                [
                    'id',
                    'user_id' => 'id',
                    'value' => 'CONCAT(u.rendered_name, " (", COALESCE(cea.address, "-"), ")")'
                ]
            )
            ->joinLeft(['cea' => 'crm_email_address'], 'u.id = cea.map_to AND cea.type = "user"', false)
            ->where('u.rendered_name LIKE "%' . $term . '%"');


        if (loginManager::isProjectLimited()) {
            $selectedBranchLocation = loginManager::data()->selected_branch_location;
            $select
                ->joinLeft(['brp' => 'branch_locations_projects'], 'brp.project_id = u.project', false)
                ->where('u.branch_location = ? OR brp.branch_location_id = ?', $selectedBranchLocation);
        }
        $registrants = db()->fetchAll($select);
        $registrants = orderArrayByLevenstein($registrants, $term, 'value');
        $registrants = array_slice($registrants, 0, 20);

        ob_clean();
        echo json_encode($registrants);
    }

    public function updateColorAction()
    {
        $this->disableView();
        $userId = $this->getParam('userId');
        $value = $this->getParam('value');


        if (!$userId || !$value) {
            return;
        }

        $userModel = new Users();

        $userModel
            ->getById($userId)
            ->setFromArray(['calendar_color' => '#' . $value])
            ->save();
    }

    public function synchroniseAction()
    {
        if (!Settings::get('outlook_calendar_enable')) {
            $this->_redirect('dashboard');
        }
        $params = $this->getAllUrlParams();
        $service = new \Calendar\Sync\Application\AuthenticateCalendarApiService(
            new loginManager(),
            new Users(),
            new \Calendar\Sync\Infrastructure\CalendarTokenRestClient(),
            new \Calendar\Sync\Infrastructure\GraphRestClient()
        );
        $service->authenticate(!empty($params) ? $params : null);
        $this->synchroniseLinkedCalendars();
        $this->_redirect('dashboard');
    }
}
