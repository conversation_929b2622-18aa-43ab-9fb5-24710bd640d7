<?php

class AuthJwtController extends GlobalController
{

    private $jwtSecret;
    private $hostname;


    public function preDispatch()
    {

        $this->jwtSecret = "<dbzgl&|fLG-xVXj.%by9n40''B)#@r~T(^RP-FjeKp'|Ug*g1";
        $this->hostname = gethostname();

    }
    public function loginAction()
    {

        if (!$this->getRequest()->isPost()) {
            CORSHelper::outputJSONResponse(
               'is not a post',
                403
            );
            return;
        }

        $loginForm = new Form($this, 'user/login');

        if (!$loginForm->isValid($this->getRequest()->getPost())) {
            CORSHelper::outputJSONResponse(
                (array) $loginForm->getErrors('user'),
                403
            );
            return;
        }

        $loginFormValues = $loginForm->getValues();

        $username = $loginFormValues['user']['username'];
        $password = $loginFormValues['user']['password'];

        try {
            $loginIsSuccess = loginManager::login($username, $password);

            if (is_array($loginIsSuccess)) {
                error_log('JWT LOGIN INFO loginIsSuccess is array: '. json_encode($loginIsSuccess));
            }

        } catch (\Exception $exception) {
            error_log($exception->getMessage());
            CORSHelper::outputJSONResponse(
                'data incomplete',
                403
            );
            return;
        }

        if (!$loginIsSuccess) {
            CORSHelper::outputJSONResponse(
                'NOT A USER',
                403
            );
            return;
        }

        $userId = loginManager::data()->id;
        $jwtToken = $this->buildJwtToken($userId);

        CORSHelper::outputJSONResponse(
            ['token' => $jwtToken],
            200
        );
        return;
    }


    public function renewAction()
    {
        if (!loginManager::check()) {
            CORSHelper::outputJSONResponse(
                'user not logged in',
                401
            );
            return;
        }
        $userId = loginManager::data()->id;
        $jwtToken = $this->buildJwtToken($userId);

        CORSHelper::outputJSONResponse(
            ['token' => $jwtToken],
            200
        );
        return;
    }

    public function logoutAction()
    {
        if (!loginManager::check()) {
            CORSHelper::outputJSONResponse(
                'user not logged in',
                401
            );
            return;
        }

        loginManager::logout();
        CORSHelper::outputJSONResponse(
            'bye',
            200
        );
    }


    private function buildJwtToken($userId)
    {
        $payload = [
            'iss' => $this->hostname, // Issuer
            'aud' => $this->hostname, // Audience
            'iat' => time(),            // Issued at
            'exp' => time() + 3600,     // Verloopt na 1 uur
            'user_id' => $userId
        ];


        $jwt = \Firebase\JWT\JWT::encode($payload, $this->jwtSecret, 'HS256');
        return $jwt;
    }
}
