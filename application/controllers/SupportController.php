<?

	class SupportController extends GlobalController {

		public function searchAction() {

			if ($this->getRequest()->isPost() && $this->_getParam('q') != '') {
				$support = new Support();
				$searchFor = 'get' . ucfirst($this->_getParam('type')) . 's';

				$this->view->type = $this->_getParam('type');
 				$this->view->results = $support->$searchFor($this->_getParam('q'), $this->_getParam('page'), $this->_getParam('phonetic'), $this->_getParam('archived'), $this->_getParam('invoiceIdent'), $this->_getParam('build'), $this->_getParam('employees'),$this->_getParam('onlyLastname'),$this->_getParam('onlyEmail'),$this->_getParam('onlyAddress'));
			}
		}

		public function searchAllAction() {
			ini_set('memory_limit', '8000M');

			$this->view->phonetic = $this->_getParam('phonetic');
			$this->view->build = $this->_getParam('build');
			$this->view->employees = $this->_getParam('employees');
			$this->view->invoice = $this->_getParam('invoiceIdent');
            $this->view->onlyLastname = $this->_getParam('onlyLastname');
            $this->view->onlyAddress = $this->_getParam('onlyAddress');
			$this->view->q = trim($this->_getParam('q'));
		}

		public function appendAdressbookIncludes(){
			$this->addMinify('js', 'js_addressbook');
		}

		public function appendSupportIncludes(){
			$this->addMinify('js', 'js_support');
			$this->addMinify('css', 'css_support');
		}

		/**
		 */
		public function showAction() {
            ini_set('memory_limit', '8000M');

			$this->appendSupportIncludes();
			$this->appendAdressbookIncludes();
			
			$type = $this->_getParam('type');
			$id   = $this->_getParam('id');

			// handle the support page of complaints in a different function
			if (in_array($type, ['complaint', 'objectgroup']))
				$this->showAdvancedAction();

			// if the showComplaint param is set, the page directly shows a complaint
			$this->view->showComplaint = $this->_getParam('showComplaint');
			$this->_setParam('showComplaint', NULL);

			$this->view->emailToComplaint = $this->_getParam('emailToComplaint');
			$this->_setParam('emailToComplaint', NULL);

			
			$object   = $this->_getParam('object');
			if (is_numeric($object)) {
                $this->view->complex = Project::isComplex($object);
            }


			if ($type == 'investor') {
				$invModel = new Investors();
				$this->view->investorId = $id;
				$this->view->userId = $invModel->getUserId($id);
				$this->view->investorName = $invModel->getName($id);



				$select = db()->select()
					->from(array('inv' => 'investor'), false)
					->joinLeft(array('o' => 'objects'), 'o.investoroverride = inv.id', false)
					->joinLeft(array('og' => 'objectgroup'), 'og.investor = inv.id OR og.investor2 = inv.id OR og.id = o.objectgroup', array('project'))
					->where('inv.id = ?', $id);

				$this->view->project_id = db()->fetchOne($select);

			}

			if($type == 'user' && $id) {
				$h = new Hierarchy($id, $type);

				$user = new User();
				$u_model = new Users();
				$uo = new Objectusers();
				$invoice_lib = new Invoice();
				$this->checkUserRole($id);
				$this->view->loud_customer      = $user->getLoud($id);

				$userModelRow = $u_model->getById($id);
				$this->view->userdata      = $userModelRow;
				$this->view->usertype      = $userModelRow['type'];

				if($h->_data['object'] > 0) {
                    $key_select = db()->select()
                        ->from('km_key_rings', ['id', 'location_id'])
                        ->where('object_id = ?', $h->_data['object'])
                        ;

                    if($this->view->keyring = db()->fetchRow($key_select)) {

                        $key_status_select = db()->select()
                            ->from('km_keyring_log', ['event_type'])
                            ->where('keyring_id = ?', $this->view->keyring['id'])
                            ->order('event_time DESC');

                        $this->view->keyring_status = db()->fetchOne($key_status_select);
                    }

				}


				if ($userModelRow['type'] == 'registrant'){
 					$spec_model = new ObjectsSpecificationsValues();
					$this->view->searchSpecifications = $spec_model->getSpecificationsForUserFlat($id);
				}


  				$db = db();
				$row = $db->fetchRow( $db->select()
					->from(array('c' => 'company'), array('*'))
					->joinLeft(
						array('ct' => 'company_type'),
						'c.type = ct.id',
						array('ct.technical_contact', 'ct.commercial_user')
					)
					->where('dummy_user = ?', $id)
				);

				if ($userModelRow->type == 'company') {
                    $select = db()->select()
                        ->from(['c' => 'company'], false)
                        ->joinLeft(['ct' => 'company_type'], 'ct.id = c.type', 'hospitality')
                        ->where('c.dummy_user = ?', $id);

                    $isHospitality = db()->fetchOne($select);
                    $this->view->isHospitality = $isHospitality;
                }

				$this->view->company = $row? $row['id'] : false;

				if(isset($row['id']) && intval($row['id']) > 0)
					$this->view->user = array('company' => $row['name']);

				$this->view->is_support_contact = $row? ($row['technical_contact'] == 1) : false;
				$this->view->is_general_company = $a = $row? ($row['technical_contact'] === '0' && $row['commercial_user'] === '0') : false;

				$this->view->is_dummy_user = $is_dummy_user = $userModelRow->has('company');
				if($is_dummy_user)
					$this->view->employees = $userModelRow->company->has('employees') ? $userModelRow->company->employees : array();

				$this->view->buildname = User::buildname(false, $id);
				$this->view->username = $userModelRow['username'];

				if($h->_data['project'] > 0 && is_null($this->view->project_id))
					$this->view->project_id = $h->_data['project'];
				elseif($userModelRow['project'] > 0 && is_null($this->view->project_id))
					$this->view->project_id = $userModelRow['project'];

				$object = $object ? $object : $h->_data['object'];
				$this->view->services = $h->_data['services'];


				if (Settings::get('modules_vidii') && Settings::get('modules_vidii_access_code_enabled')) {
					$this->view->vidii_access_code_enabled = true;

					$this->view->vidiiAccessCode =
						(new \Vidii\Infrastructure\Domain\Repository\VidiiAccessCodesZendDbRepository())
						->getMostRelevantForUser($id);
				}

                if (Settings::get('modules_vidii')) {
                    $this->view->number_vidii_deals = RentalDeals::getVidiiDealsByUserId($id);
                }

			} elseif ($type == 'object' || $type == 'objectgroup'){
				$available = Settings::get('modules_rental');
				$objectsModel = new Object();

				$uo_select = db()->select()
					->from(['users_objects'], ['object', 'from' => 'MAX(`from`)', 'customer', 'till' => 'case when MAX(`till` IS NULL) = 0 THEN max(`till`) END', 'services'])
					->where('object = ?', $id)
					->group('object');

				if($uo_item = db()->fetchRow($uo_select))
					if(((!$uo_item['till']) && $uo_item['customer'] > 0) || $uo_item['services'] == 'manage')
						$available = false;

				$h = new Hierarchy($id, $type);
				$this->view->available = $available;
				$this->view->project_id = $h->_data['project'];
				$this->view->objectdetails = $objectsModel->getDetails($id);

				if($h->_data['objectgroup'] > 0)
					$this->view->ratesheet_id = db()->fetchOne(db()->select()->from(['objectgroup'], ['rate'])->where('id = ?', $h->_data['objectgroup']));

				if($type == 'object') $object = $id;				
			}

			if(!$this->validateProjectAllowed($this->view->project_id)) return;

			$c = new Complaint();
			$c->id = $id;
			$c->type = $type;
			$this->view->complaint = array(
				'templates' => $c->getTemplates()
			);

			if ($type == 'project')
                $this->view->project = Projects::get($id);
			
			$this->view->id = $id;
			$this->view->type = $type;
			$this->view->object = $object;
			$this->view->open = $this->_getParam('open');
			$this->view->openid = $this->_getParam('openid');

			// general score for a tenant
			$showModel = new Controllers_Support_Show();

			if (Settings::get('modules_surveys_enabled') && Settings::get('modules_surveys_show_scores')) {

				if ($showModel->isTenant($id) || $showModel->isCommercialTenant($this->view->company)) {

					$data = $showModel->getGeneralScoreForTenant($id);

					$service = new \application\models\Surveys\Application\Service\GeneralScoreForTechnicalContactsByTenantService();
					$dataTechnicalContactsScores = $service->execute($id);

					$this->view->survey = [
						'title' => $this->view->buildname . " geeft onze service gemiddeld een: ",
						'score' => $data['general_score'],
						'invite_count' => $data['invite_count'],
						'technical_contacts' => $dataTechnicalContactsScores
					];

				} elseif ($showModel->isTechnicalContact($this->view->company)) {

					$data = $showModel->getGeneralScoreForTechnicalContact($this->view->company);

					$this->view->survey = [
						'title' => "Klanten geven deze service partij gemiddeld een: ",
						'score' => $data['general_score'],
						'invite_count' => $data['invite_count'],
						'technical_contacts' => []
					];
				}
			}
		}

		function showAdvancedAction() {
			$this->view->headLink()->appendStylesheet( 'media/style/support/show-advanced.css' );

			$params            = $this->getAllParams();
			$supportBoxFactory = new Controllers_Support_SupportBox_SupportBoxFactory();
			$supportBox_Config = new Controllers_Support_SupportBox_Config( $params );

			$this->view->context = Controllers_Support_SupportBox_Context_Context::getContext( $params, $this->view );
			$this->boxes         = $supportBoxFactory->getSupportBoxes( $supportBox_Config );


			if($params['type'] === 'objectgroup') {
				$objectgroup = (new Objectgroups())->fetchRowById($params['id']);
				$this->view->objectgroup = $objectgroup;
				$ps = new TechnicalSpecs('objectgroup', $objectgroup->id);
				$this->view->specs = $ps->getArray();
			}

			$this->render( 'show-advanced' );
		}

		function infoAdvancedAction() {
			$params                     = $this->getAllParams();
			$this->view->context        = Controllers_Support_SupportBox_Context_Context::getContext( $params, $this->view );

			switch($params['type']) {
				case('complaint'):
					$this->view->infoSupportBox = new Controllers_Support_SupportBox_ComplaintInfoSupportBox( [ ] );
					break;

				case('user'):
				default:
					$this->view->infoSupportBox = new Controllers_Support_SupportBox_InfoSupportBox( [ ] );
					break;
			}
		}

		public function detailsAction(){

			foreach(array('mapTo', 'type', 'object', 'users_objects', 'rights', 'is_support_contact', 'is_general_company') as $param)
				$params[$param] = $this->view->$param = $this->_getParam($param);

			$this->view->id = $params['mapTo'];
            if ($params['type'] == 'investor') {
                $nawTitle = 'Beleggerinfo';
            } else {
                $nawTitle = ucfirst(translate()->_('tenant_info'));
            }

			$tabs = array(
				'naw' => $nawTitle,
			);

			if($params['is_support_contact'] === true || $params['is_general_company'] === true)
				$tabs = [
					'simple-naw' => $nawTitle,
				];


			if (in_array($params['type'], array('user', 'object', 'investor'))
				&& loginManager::data()->hidefinancial != 1
				&& !(in_array(Settings::get('general_company_shortname'), ['tweel',]) && $this->view->rights == 'investor')
			) {
				$tabs += array(
					'invoices' => ucfirst(translate()->_('invoices')),
				);
			}

 			if ($this->view->rights != 'investor')  {

			if ($params['type'] == 'investor') {
				$tabs += array(
					'objects'  => 'Objecten',
					'projects' => 'Projecten',
				);
			}

			if (($params['type'] == 'user' && !$params['is_support_contact'] && !$params['is_general_company']) || $params['type'] == 'object') {
				$tabs += array(
					'project' => 'Project',
				);

			}
			}

			
			$this->view->tabs = $tabs;
		}

		public function leadsAction() {
			$this->view->Breadcrumbs()->addCrumb('Matched Leads');

			$model = new Controllers_RentalDashboard_Leads();
			$model->setMatchAgainstObjectId($this->getParam('object'));

			$params = array('url-override' => 'support/leads');

			foreach(array('view_template', 'user') as $param)
				if(!is_null($this->_getParam($param)))
					$params[$param] = $this->_getParam($param);

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('date' => 'DESC')));

			$this->view->listView = $this->view->ListView($model->getList(), $params)
				->setTypes([
					'name' => ['title' => 'Naam', 'width' => 'xxlarge truncate'],
					'status' => ['title' => 'Status', 'width' => 'xxsmall truncate'],
					'date' => ['title' => 'Toegevoegd', 'width' => 'small']
				])

				->addFormat('name', function($value, $item){
					return '<a href="support/show/type/user/id/' . $item['id'] . '/">' . $value . '</a>';
				})

				->addFormat('status', function($value){
					return 'Nieuw';
				})

				->addFormat('date', 'relativeDate')

				->setFilters([
					'name' => ['type' => 'input'],
					'status' => []
				])

				->setOptions(array(
					'paginator_items_per_page' => 5
				))

				->render($this);
		}

		public function leadsObjectsAction() {
			$this->view->Breadcrumbs()->addCrumb('Matched Aanbod');

			$model = new Controllers_RentalDashboard_Objects();
			$model->setMatchAgainstUserId($this->getParam('user'));

			$params = array('url-override' => 'support/leads-objects');

			foreach(array('view_template', 'user') as $param)
				if(!is_null($this->_getParam($param)))
					$params[$param] = $this->_getParam($param);

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('date' => 'DESC')));

			$this->view->listView = $this->view->ListView($model->getList(), $params)
				->setTypes([
					'address' => ['title' => 'Adres', 'width' => 'large truncate'],
					'number' => ['title' => 'Nummer', 'width' => 'xsmall right'],
					'spacer' => ['title' => '', 'width' => 'xxsmall'],
					'city' => ['title' => 'Stad', 'width' => 'xsmall truncate'],
				])

				->addFormat('address', function($value, $item){
					return '<a href="support/show/type/object/id/' . $item['id'] . '/">' . $value . '</a>';
				})

				->setFilters([
					'address' => ['type' => 'input'],
					'number' => ['type' => 'input'],
					'city' => ['type' => 'input'],
					'status' => []
				])

				->setOptions(array(
					'paginator_items_per_page' => 5
				))

				->render($this);
		}

		public function postAction() {
			$this->view->Breadcrumbs()->addCrumb('Poststukken');

			$postModel = new Post();

			$params = array('url-override' => 'support/post');

			foreach(array('view_template', 'user') as $param)
				if(!is_null($this->_getParam($param)))
					$params[$param] = $this->_getParam($param);

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('date' => 'DESC')));

			$this->view->listView = $this->view->ListView($postModel->getListForRecipient($params['user']), $params)
				->setTypes(array(
					'typename'		=> array('title' => 'Soort', 'width' => 'small truncate', 'group_equal_rows' => true),
					'date'			=> array('title' => 'Datum', 'width' => 'xsmall', 'group_equal_rows' => true, 'type' => 'date'),
					'user_filename'	=> array('title' => 'Bestand', 'width' => 'medium truncate'),
					'sent'			=> array('title' => 'Verzonden', 'width' => 'xsmall', 'type' => 'date'),
				))

				->addFormat(array('date', 'sent'), 'date')

				->addFormat('user_filename', function($value, $item){

					if($item['typename'] == 'Mailing per post'){
						$mailing_number = strpos($item['filename'], ' - mailing nr');
						$value = substr($item['filename'], 0, $mailing_number !== false ? $mailing_number : strlen($item['filename']));
					}

					$filename = pathinfo($value, PATHINFO_FILENAME);

					$url = 'support/download-post/id/' . $item['post_recipient_id'] . '/filename/' . urlencode($filename);

					$logo = '<a href="' . $url . '/stationery/add/"><img src="media/images/icons/pdf.png" /></a> ';

					return $logo . '<a target="_blank" href="' . $url . '">' . $filename . '</a>';
				})

				->setFilters(array(
					'typename' => array(),
					'date' => array(),
					'user_filename' => array('type' => 'input'),
					'sent' => array(),
				))

				->setOptions(array(
					'paginator_items_per_page' => 8
				))

				->render($this);
		}
		
		public function downloadPostAction() {
			$this->disableView();
			$postRecipientId = $this->_getParam('id');
			$zip = new ZipArchive();
			
			$postModel = new Post();
			
			$post = $postModel->getByPostRecipientId($postRecipientId);
			$corporation = $postModel->getCorporation($post['id']);

			$path = "_cache/post/{$post['type']}/{$post['filename']}";
			
			$opened = $zip->open($path);
			if (!$opened)
				throw new Exception("Could not open zip archive at: {$path}");

			$pdf_file = $zip->getFromName($post['user_filename']);

			$pdf = new Pdf();
			$pdf->addPageTemplateToCurrentPdfFromString($pdf_file);

			if($this->_getParam('stationery') && $corporation){
				$select = db()->select()
					->from(array('c' => 'corporations'), array('name', 'logo','number', 'kvk', 'tax'))

					->joinLeft(array('ba' => 'corporations_bank_accounts'), 'ba.corporation_id = c.id AND ba.is_current = 1', array('bank' => 'number', 'iban', 'bic'))

					->where('c.id = ?', $corporation);

				include('application/views/scripts/partials/pdftemplates/styles.phtml');
				$pdf->addStationery(db()->fetchRow($select) , 'headeronly');
			}

			$pdf->browseroutput($this->_getParam('filename') ? urldecode($this->_getParam('filename')) . '.pdf': $post['user_filename']);
		}
		
		public function checkUserRole($user){
			$uo = new Objectusers();

			$row = $uo->fetchRow($uo->select()->where('customer = ?', $user)->where('role = ?', 'normal'));

			if($row)
				return;

			$row = $uo->fetchRow($uo->select()->where('customer = ?', $user)->order('from DESC'));

			if(!$row || empty($row->object) || empty($row->from))
				return;

			$select = $uo->select()
				->where('object = ?', $row->object)
				->where('role = ?', 'normal')
				->where('customer > ?', 0)
				->where('`from` = ?', $row->from);

			if($new = $uo->fetchRow($select))
				$this->_redirect('support/show', array('type' => user, 'id' => $new->customer));

		}

        public function firstAndLastInvoiceButtonsAction()
        {
            $userObjectId = $this->getParam('userObjectId');

            if (!$userObjectId) {
                return;
            }

            $invoiceLibrary = new Invoice();
            $userObjectModel = new Objectusers();
            $userObjectModelRow = $userObjectModel->getById($userObjectId);

            $this->view->userObjectId = $userObjectId;
            $this->view->hasFirstInvoice = $invoiceLibrary->hasFirstInvoice(
                $userObjectModelRow->customer,
                $userObjectModelRow->object
            );
            $this->view->needsLastInvoice = $invoiceLibrary->needsLastInvoice(
                $userObjectModelRow->customer,
                $userObjectModelRow->object
            );
        }

		/**
		 * Al read actions
		 */
		public function nawAction() {
		 	
			$mapTo = $this->_getParam('mapTo');
			$type  = $this->_getParam('type');
		 	
		 
			$h = new Hierarchy($mapTo, $type);
			$c = new Complaint();
			$object = new Object();
			$u_model = new Users();
			$boModel = new BreakOptions();
			
			$consumptionModel = new ObjectsConsumptions();
			$usersObjectsModel = new Objectusers();
			
			$invoice_lib = new Invoice();
			$profile = new Usage();
			
			if ($type == 'object') {
				


				$objectId = $h->_data['object'];
				
				$meters = $object->getMeters($objectId);
				
				$consumptionByMeter = array();
				foreach((array) $meters as $meter) {
					$meterId = $meter['meterid'];
					$consumptionByMeter[$meterId] = $consumptionModel->searchConsumption(array('object' => $objectId, 'meter' => $meterId));
				}
				
				$this->view->object = $object->getDetails($this->_getParam('mapTo'));
				$this->view->objectgroups = $object->getObjectGroups($h->_data['project']);
				$this->view->meters = $meters;
				$this->view->consumption = $consumptionByMeter;

				$usersObjects = Objectusers::getClosestForObject($objectId);

				$this->view->objectFromDate =  (!empty($usersObjects) && !empty($usersObjects->from))?
					date('d-m-Y', strtotime($usersObjects->from) ): '-';
				$this->view->objectTillDate =  (!empty($usersObjects) && !empty($usersObjects->till))?
					date('d-m-Y', strtotime($usersObjects->till) ): '-';

				 

			}
			elseif ($type == 'project') {
				$project = new Project();

				$this->view->project = $project->getDetails($this->_getParam('mapTo'));
				$this->view->objectgroups = $object->getObjectGroups($this->_getParam('mapTo'));
			}
			elseif ($type == 'user') {
				
				 
				$userId = $this->_getParam('mapTo');
				$usersObjectsId = $this->getParam('users_objects');
				$user = new User();
				$t = new Transaction();
				$user->user = $userId;
			
				$objectId = $this->_getParam('object');
				$currentObjectId = $this->_getParam('object');
				
				// keep existing working if from url or link without object parameter.
				if (!is_numeric($currentObjectId)) {
						$currentObjectId = $user->getLastObject();
				}

				$user->object = $currentObjectId;
				$meters = $object->getMeters($objectId);
				$userObject = $usersObjectsModel->getNewestByUserId($userId,$objectId);

                if (!$userObject && $usersObjectsId) {
                    $userObject = $usersObjectsModel->get($usersObjectsId)->toArray();
                }

				$from = $userObject['from'];
				$until = $userObject['until'];

				if($userObject && $boRow = $boModel->matchRow(['users_object' => $userObject['id']]) ) {
					$this->view->breakOption = [
						'notice_period' => $boRow->notice_period,
						'date_0' => ($boRow->date_0 == null? null: new DateTimeImmutable($boRow->date_0)),
						'date_1' => ($boRow->date_1 == null? null: new DateTimeImmutable($boRow->date_1)),
					];
				}
				

				$userModelRow = $u_model->getById($userId);

				$db = db();
				$row = $db->fetchRow( $db->select()
						->from(array('c' => 'company'), array('*'))
						->joinLeft(
							array('ct' => 'company_type'),
							'c.type = ct.id',
							array('ct.technical_contact')
						)
						->where('dummy_user = ?', $userId)
				);

				$this->view->company = $row? $row['id'] : false;
				$this->view->is_support_contact = $row? ($row['technical_contact'] == 1) : false;
				$this->view->is_dummy_user = $is_dummy_user = $userModelRow->has('company');
				if($is_dummy_user)
					$this->view->employees = $userModelRow->company->has('employees') ? $userModelRow->company->employees : array();
				$this->view->usertype = in_array($userModelRow['type'], ['tenant', 'company',]) ? $userModelRow['type'] : null;

				$this->view->buildname = User::buildname(false, $userId);

				$this->view->project_id = $h->_data['project'];

				$default_total_stamp = strtotime($from) > strtotime('today') ? strtotime($from) : 0;
				$total = $invoice_lib->defaultTotal($userId, $currentObjectId, $default_total_stamp, $default_total_stamp);


                $this->view->meters = $meters;
 				$this->view->payment = $t->getPaymentProblems($this->_getParam('mapTo'), strtotime('-6 months'));
				$this->view->user = $user->getCurrentAddress(true, true);
				$this->view->userModelRow = $userModelRow = $u_model->getById($this->_getParam('mapTo'));
				if( $this->view->is_dummy_user )
					$this->view->company = Company::getByDummyUserId($userId);
				else
					$this->view->company = $userModelRow->has('employee') ? $userModelRow->employee->company : false;

				$this->view->preferences = $user->getInvoicePreferences($userId);

				if (isset($userObject['id'])) {
                    $preferencesUsersObject = UserInvoicePreferences::getForUsersObjects($userObject['id']);
                    $total['yearlyDivider'] = $preferencesUsersObject !== null && isset($preferencesUsersObject['rate'])?
						$preferencesUsersObject['rate']: $total['yearlyDivider'];
                }

				$this->view->hasContract = $user->hasContract($userId);
				$this->view->hasIdentification = $user->hasIdentification($userId);
				$this->view->advance = new StringFormat($total['total'], 'money');


				$this->view->userObject = $userObject;
				
				$this->view->invoiceRate = $invoiceRate = $total['yearlyDivider'];

				$this->view->yearadvance = new StringFormat($total['total'] * $invoiceRate, 'money');

			}
			elseif ($type == 'investor') {
				$user = new User();
				$t = new Transaction();
				$invModel = new Investors();
				
				$this->view->id      = $invModel->getUserId($mapTo);
				$this->view->investor = $mapTo;
				$this->view->user    = $user->getInvestor($mapTo);
				$this->view->payment = $t->getInvestorPaymentProblems($mapTo, strtotime('-6 months'));
			}
			
			$this->view->incidents = (array) $c->getIncidents($h->_data);
			
			
			$this->view->tabcount = $this->tabCount();
			$this->view->tabUrgency = $this->tabUrgency();
			$this->view->currentObjectId = $currentObjectId;
			$this->view->objectid = $h->_data['object'];
			$this->view->objectgroupid = $h->_data['objectgroup'];
			$this->view->type = $this->_getParam('type');
			
		
			$this->render('read-naw');
		}

		/**
		 * A simplified version of the normal NAW action,
		 * (initially) used for support_contact companies.
		 * 
		 */
		public function simpleNawAction() {
			$this->appendAdressbookIncludes();

			$type  = $this->_getParam('type');
			$mapTo = $this->_getParam('mapTo');
			$all = $this->getAllParams();

			$c = new Company();
			$company = $c->fetchRow($c->select()->where('dummy_user = ?', $mapTo));

			$this->view->company = $company;
			$this->render('read-simple-naw');
		}

		public function warningsAction($params = array()) {
			foreach($params as $param => $value)
				$this->_setParam($param, $value);

			$type = $this->_getParam('type');
			$rights =  $this->_getParam('rights');

			if ($type == 'user') {
				$customerId = $this->_getParam('id');

				$Warning = new Controllers_Support_Warning($customerId);

				$recentCreditCheckDone = $Warning->recentCreditCheckDone();

				$this->view->recentCreditCheckDone = $recentCreditCheckDone;
				$this->view->riskcode = $recentCreditCheckDone[0]['riskcode'];
			}


			$from = strtotime('-6 months');
			$hier = new Hierarchy($this->_getParam('id'), $type);
			$ids = $hier->_data;

			if($type == 'user' && !$ids['object'])
				return;
			
			$p = new Project();
            $this->view->project_id = $hier->_data['project'];
            if ($type == 'user' or $type == 'object') {
                $this->view->accountmanager = Projects::getCommercialAssigneeName($this->view->project_id);
                $this->view->technicalmanager = Projects::getTechnicalAssigneeName($this->view->project_id);
                $this->view->financialmanager = Projects::getFinancialAssigneeName($this->view->project_id);
            } else {
                $this->view->accountmanager = Investors::getInvestorManagerName($this->_getParam('id'));
                $this->view->investormanager = Investors::getAccountManagerName($this->_getParam('id'));

            }
			$user  = new User();
			$user->user = $ids['user'];
			//$uci   = new UserCommercialInfo();
			$notes = new SupportNotes();
			$compl = new SupportComplaints();
			$trans = new Transaction();
			
			if ($rights != 'investor') {
				$this->view->loud_customer      = $user->getLoud($user->user);
			}
			
			
			$missing = array();

			if($type == 'user')
				$notesQuery = $notes->select()
					->where(
						 "`map_to` = '{$ids['user']}' AND `type` = 'user'"
						." OR "
						."`map_to` = '{$ids['project']}' AND `type` = 'project'"
					) 
					->where("`warning` = 1")
					->order("date DESC");

					
			if($type == 'object')
				$notesQuery = $notes->select()
					->where(
						 "`map_to` = '{$this->_getParam('id')}' AND `type` = 'object'"
					) 
					->where("`warning` = 1")
					->order("date DESC");		

			if ($type == 'user') {
				$complQuery = $compl->select()
					->where("`date` >= ?", gmdate("Y-m-d\TH:i:s\Z", $from))
					->where("`complaint_type` = ?", 'malfunction')
					->where(
						 "`map_to` = '{$ids['user']}' AND `type` = 'user'"
						." OR "
						."`map_to` = '{$ids['object']}' AND `type` = 'object'"
						." OR "
						."`map_to` = '{$ids['objectgroup']}' AND `type` = 'objectgroup'"
						." OR "
						."`map_to` = '{$ids['project']}' AND `type` = 'project'"
					) 
				;
				 
			
				$complStats = array(
					'open'  => 0,
					'total' => 0,
					'open_urgent' => 0,
					'total_urgent' => 0,
				);

				foreach ($compl->fetchAll($complQuery) as $c) {
					if ($c['last_status'] != 4) {
						$complStats['open']++;
						if ($c['urgency'])
							$complStats['open_urgent']++;
						
					}
					$complStats['total']++;
					if ($c['urgency'])
						$complStats['total_urgent']++;

				}
			
				$payProbs = $trans->getPaymentProblems($ids['user'], $from);
				
				$addr = $user->getCurrentAddress(true);



				$contract = $user->hasContract($ids['user']);
				$chamber = $user->hasChamberofCommerce($ids['user']);
 				if(!$contract)
					$missing[] =  'rental_agreement' ;

				if ($addr['ogtype'] == 'commercieel') {
					if (!$addr['chamberofcommerce'] && !$chamber)
						$missing[] = 'chamberofcommerce';
				}

				// indicate what kind of management we do for this user's investor, if any
				if ( Settings::get( 'for_third_party' ) ) {
					$objectId = $this->_getParam( 'object' );
					$objectId = intval( $objectId ) > 0 ? $objectId : $ids['object'];
					if ( intval( $objectId ) > 0 ) {
						$investorId = Object::getInvestorId( $objectId );

						if ( intval( $investorId ) > 0 ) {
							$iModel = new Investors();

							$iRow = $iModel->getById( $investorId );

							if ( ! empty( $iRow ) && $hier->_data['services'] != 'rent_deal' ) {
								$investorManagements = [ ];
								if ( $iRow['manage_technical'] == 1 ) {
									$investorManagements['technical'] = true;
								}
								if ( $iRow['manage_financial'] == 1 ) {
									$investorManagements['financial'] = true;
								}

								if ( count( $investorManagements ) > 0 ) {
									$this->view->investorManagements = $investorManagements;
								}
							}
						}
					}
				}


			} else if ($type == 'investor') {
				$invModel = new Investors();
				$payProbs = $trans->getInvestorPaymentProblems($ids['user'], $from);
				
				$addr = $user->getCurrentAddress(true, true);
				
				$notesQuery = $notes->select()
					->where("`map_to` = ?", $invModel->getUserId($this->_getParam('id')))
					->where("`type` = ?", 'user') 
					->where("`warning` = 1")
					->order("date DESC")
				;
			}
			
			if ($type == 'user' || $type == 'investor') {
				$complQuery = $compl->select()
					->where("`date` >= ?", $from)
					->where("`complaint_type` = ?", 'malfunction')
					->where($hier->genQueryByIds($ids)) 
				;

				if (!$addr['phone1'] && !$addr['phone2'])
					$missing[] = 'phone';
				if (!$addr['email'])
					$missing[] = 'email';
				if (!$addr['iban'])
					$missing[] = 'bankaccount';
				//if ($payProbs['method'] != 'Automatische incasso')
				//	$missing[] = 'collection';
			
				if ( // Laat automatische incasso ook als ontbrekend zien als er betalingsproblemen zijn:
					($missing || $payProbs['retransfer'] || $payProbs['reminds'] || $payProbs['unpayed'])
					&&
					$payProbs['method'] != 'Automatische incasso'
					&&
					Settings::get('software_type') !== 'real estate'
				) {
					$missing[] = 'collection';
				}
			}	
			
			$highUrgencies = array(
				'notes'   => true,
				'compl'   => $complStats['open'] ? true : false,
			);
			if ($type == 'user') {
				$highUrgencies['payment'] = $payProbs['unpayedAmount'] ? true : false;
				$highUrgencies['user']    = !$addr['contract'] || !$addr['identification'] ? true : false;
			}
			
			//p($notesQuery);
			$this->view->notes           = $notes->fetchAll($notesQuery);
			if ($type != 'investor') {
				$this->view->complaints      = $complStats;
			}
			if ($type == 'user') {
				$this->view->paymentProblems = $payProbs;
				if ($rights != 'investor') {
					$this->view->missing         = $missing;
					$this->view->thirdparty       = $addr['object'] ? $p->isForthirdProject($addr['object']) : false ;
				}
			}
			$this->view->highUrgencies   = $highUrgencies;
			$this->view->paymentOnly = $this->_getParam('paymentOnly') == 'true';

			return $this->view->highUrgencies;
		}
		
		public function tabcount(){
			if ($this->view->user['ogtype'] != 'commercieel') {
				$required = array(
					'identification',
					'contract',
				);
			} else {
				$required = array(
					'identification',
					'contract',
					'chamberofcommerce',
					'ratesheet',
					'rental_agreement',
				);
			}
			foreach ($required as $key) {
				$user += !$this->view->user[$key] ? 1 : 0;
			}
			
			$payment = $this->view->payment;
			
			return array(
				'payment' => 	$payment['reminds'] + $payment['urgent_reminds'] + $payment['retransfer'],
				'incidents' => 	count($this->view->incidents),
				'user' => 		$user				
			);
		}
		
		public function tabUrgency() {
			$t = new Transaction();
			
			$urgency = array();
			$incidents = $this->view->tabcount['incidents'];
			if ($incidents > 2) {
				$urgency['incidents'] = 'high';
			} else if ($incidents > 1) {
				$urgency['incidents'] = 'medium';
			} else {
				$urgency['incidents'] = 'low';
			}
			
			$mapToParam = $this->_getParam('mapTo');
			$recent = $t->getPaymentProblems($mapToParam, strtotime('-6 months'));
			$older  = $t->getPaymentProblems($mapToParam, strtotime('-12 months'));
			
			//echo "<pre>"; var_dump($recent); echo "</pre>";
			//echo "<pre>"; var_dump($older); echo "</pre>";
			
			$older['reminds'] -= $recent['reminds'];
			$older['urgent_reminds'] -= $recent['urgent_reminds'];
			
			if (
				$recent['reminds'] >= 3 || $recent['urgent_reminds'] >= 2 || $recent['collection_agency']
				||
				$older['reminds']  >= 4 || $older['urgent_reminds']  >= 3
			) {
				$urgency['payment'] = 'high';
				
			} else if (
				$recent['reminds'] >= 2 || $recent['urgent_reminds'] >= 1
				||
				$older['reminds']  >= 3 || $older['urgent_reminds']  >= 2
			) {
				$urgency['payment'] = 'medium';
				
			} else {
				$urgency['payment'] = 'low';
			}
			
			return $urgency;
		}

		public function invoicesAction() {
			//get all relevant ids
			//$h = new Hierarchy($this->_getParam('mapTo'), $this->_getParam('type'));

			$this->view->is_all_invoices = $all_invoices = $this->_getParam('all-invoices') == '1';

			global $multiple_objects;

			if($this->_getParam('multiple-objects') == '1')
				$multiple_objects = true;

			if($all_invoices)
				$multiple_objects = true;

			$mapToParam = $this->_getParam('mapTo');
			$object = $this->_getParam('object');
			$list_content_only = $this->getParam('listContentOnly') == '1';


			$id = $mapToParam ? $mapToParam : $this->_getParam('id');;
			if (!$page = $this->_getParam('page'))
				$page = 1;

			if($all_invoices){
				$all_invoices_page = $page;			
				$page = 1;
			} else {
				$all_invoices_page = 1;
			}
				
			$type = $this->_getParam('type');
			
			$invoiceModel = new Invoice();

			if($this->view->rights == 'investor') {
                $invoiceModel->filter_project_corporation_id = Settings::get('broker_page_filter_project_corporation_id');
                if (isset(loginManager::data()->info['investor_manage_start_date'])) {
                    if (isValidTimeStamp(strtotime(loginManager::data()->info['investor_manage_start_date']))) {
                        $invoiceModel->filter_invoice_list_start_date = strtotime(loginManager::data()->info['investor_manage_start_date']);
                    }
                }
            }

			switch ($type) {
				case 'user':
					$invoiceModel->object = $object;
					$invoices = $invoiceModel->forUser($id, $page);
					break;
				case 'object':
					$invoices = $invoiceModel->forObject($id, $page);
					break;
				case 'investor':
					$invoices = $invoiceModel->forInvestor($id, $page);
					break;
				default:
					throw new Exception("Unknown type for invoices: '{$type}'");
					break;
			}

			$invoice_tabs = array('invoices');

			$objects = Objectusers::getObjectsForUser($id);
			if(is_array($objects) && count($objects) > 1)
				$multiple_objects = true;

			if(($multiple_objects === true && $type == 'user') || $all_invoices){
				$invoiceModel->object = false;
				$this->view->all_invoices = $invoiceModel->forUser($id, $all_invoices_page);

				$invoice_tabs[] = 'all_invoices';
			}
			
			$this->view->invoice_tabs = $invoice_tabs;
			$this->view->multiple_objects = $multiple_objects;
			$this->view->invoices = $invoices;
			$this->view->mapTo = $id;
			$this->view->type = $type;
			$this->view->currentTab = $all_invoices && $list_content_only ? 'all_invoices' : 'invoices';
			$this->view->object = $object;

			$this->render($list_content_only ? 'read-invoices-list' : 'read-invoices');
		}

		public function invoicesPrintAction() {
			/*
				The next two lines are very ugly, but due to combination of time pressure and 
				having to circumvent the normal ListView functionality the normal/better ways
				are not viable.
			*/
			echo '<link href="/media/style/transaction/get-payments-list-print.css" media="screen, print" rel="stylesheet" type="text/css" />';
			echo '<link href="/OmniBoxx/media/style/transaction/get-payments-list-print.css" media="screen, print" rel="stylesheet" type="text/css" />';

			$params = $this->getAllParams();
			$params['view_template'] = 'print';

			$monthsAgo = is_numeric($params['months_ago']) && intval($params['months_ago']) > 0? intval($params['months_ago']): 0;

			$tp_model = new TransactionsPayments();
			$icModel = new InvoicesCustoms();
			$iLib = new Invoice();

			$this->view->is_all_invoices = $all_invoices = $this->_getParam('all-invoices') == '1';

			global $multiple_objects;

			if($this->_getParam('multiple-objects') == '1')
				$multiple_objects = true;

			if($all_invoices)
				$multiple_objects = true;

			$mapToParam = $this->_getParam('mapTo');
			$object = $this->_getParam('object');

			$id = $mapToParam ? $mapToParam : $this->_getParam('id');;
				
			$type = $this->_getParam('type');
			
			$invoiceModel = new Invoice();
			switch ($type) {
				case 'user':
					if($multiple_objects !== true)
						$invoiceModel->object = $object;
					$invoices = $invoiceModel->forUser($id, 1, false, 1000);
					break;
				case 'object':
					$invoices = $invoiceModel->forObject($id, 1, false, 1000);
					break;
				case 'investor':
					$invoices = $invoiceModel->forInvestor($id, 1, false, 1000);
					break;
				default:
					throw new Exception("Unknown type for invoices: '{$type}'");
					break;
			}

			$output = [];

			$output[] = [
				'date'			=> 'Facturen overzicht',
				'amount'		=> '',
				'identifier'	=> '',
				'title'         => '',
				'open'			=> '',
			];

			$output[] = [
				'date'			=> 'Huidige datum:',
				'amount'		=> date('d-m-Y'),
				'identifier'	=> '',
				'title'         => '',
				'open'			=> '',
			];

			if($type == 'user') {
				$output[] = [
					'date'			=> 'Klantnaam:',
					'amount'		=> User::buildname(false, $id),
					'identifier'	=> '',
					'title'         => '',
					'open'			=> '',
				];

				$output[] = [
					'date'			=> 'Klantnummer:',
					'amount'		=> User::getNumber($id),
					'identifier'	=> '',
					'title'         => '',
					'open'			=> '',
				];
			}


			$output[] = [ 'date' => '', 'amount' => '', 'identifier' => '', 'title' => '', 'open' => '', ];
			$output[] = [ 'date' => '', 'amount' => '', 'identifier' => '', 'title' => '', 'open' => '', ];

			foreach ($invoices as $i => $invoice) {
				if($monthsAgo > 0 && strtotime("$monthsAgo months ago") > strtotime($invoice['date']))
					continue;

				$output[] = [
					'date'			=> 'Factuur datum',
					'amount'		=> 'Factuur bedrag',
					'identifier'	=> 'Factuurnr.',
					'title'         => 'Factuurtitel',
					'open'			=> 'Openstaand',
				];

				$open = ($invoice['type'] == 'd')?
					(0 - ($invoice['amount'] - $invoice['payed'])):
					($invoice['amount'] - $invoice['payed']);

				$amount = $invoice['amount'];

				if(!is_numeric($amount)) {
					$amount = '';

				} else {
					$amount = $invoice['type'] == 'c'? $amount: $amount * -1;
					$amount = '€ '.(new StringFormat($amount, 'money_from_int_db'));
				}

				if(is_numeric($invoice['customid'])) {
					$icRow = $icModel->getById($invoice['customid']);
					$title = is_object($icRow)? $icRow['title']: '';

				} else {
					$title = ucfirst($iLib->translatePeroid($invoice['period'], $invoice['periodvalue']));
					$title .= ' '.date('Y', strtotime($invoice['invoice_start']));
				}


				$output[] = [
					'date'			=> (strtotime($invoice['date']) != false? date('d-m-Y', strtotime($invoice['date'])): ''),
					'amount'		=> $amount,
					'identifier'	=> $invoice['identifier'],
					'title'         => $title,
					'open'			=> '€ '.(new StringFormat($open/100, 'money')),
				];

				if(intval($invoice['transaction_id']) <= 0)
					continue;

				$select = $tp_model->select()
					->from(array('transactions_payments'))
					->where('transaction = ?', $invoice['transaction_id'])
					->order('date_created ASC')
				;

				$payments = $tp_model->fetchAll($select);

				if(count($payments) > 0) {
					$output[] = [
						'date'			=> '',
						'amount'		=> 'Betaald per',
						'identifier'	=> 'Bedrag',
						'title'         => '',
						'open'			=> 'Omschrijving',
					];

					foreach ($payments as $payment) {
						$amount = $payment['amount'];

						if(!is_numeric($amount)) {
							$amount = '';

						} else {
							$amount = $payment['direction'] == 'incoming'? $amount: $amount * -1;
							$amount = '€ '.(new StringFormat($amount, 'money_from_int_db'));
						}

						$output[] = [
							'date'			=> '',
							'amount'		=> (strtotime($payment['date']) != false? date('d-m-Y', strtotime($payment['date'])): ''),
							'identifier'	=> $amount,
							'title'         => '',
							'open'			=> TransactionsPayments::getStatusLabel($payment->status, [
												'user_buildname' 	=> User::buildname(false, $payment->user),
												'date_created'		=> $payment->date_created,
											]),
						];
					}
				}

				if($i >= $invoices->getTotalItemCount() - 1)
					break;

				$output[] = [ 'date' => '', 'amount' => '', 'identifier' => '', 'title' => '', 'open' => '', ];
				$output[] = [ 'date' => '', 'amount' => '', 'identifier' => '', 'title' => '', 'open' => '', ];
			}
 
			$this->view->listView($output, $params)

				->setTypes([
					'date' => [
						'title' => 'Factuur datum',
						'width' => 'xxxxxlarge truncate',
					],
					'amount' => [
						'title' => 'Factuur bedrag',
						'width' => 'xxxxxlarge truncate',
					],
					'identifier' => [
						'title' => 'Factuurnr.',
						'width' => 'xxxxxlarge truncate',
					],
					'title' => [
						'title' => 'Factuurtitel',
						'width' => 'medium truncate',
					],
					'open' => [
						'title' => 'Openstaand',
						'width' => 'xxxxxlarge truncate',
					],
				])

				->setOptions([
					'item_title' 					=> "Factuur",
					'show_title' 					=> false,
				])

				->render($this);
		}

		public function residentsAction() {

			$object = new Object();

			$this->view->residents = $object->getResidents($this->_getParam('mapTo'));

			$this->render('read-residents');
		}
		
		public function financesAction() {
			$type  = $this->_getParam('type');
			$map_to = $this->_getParam('mapTo');

			$object = new Object();
			$finances = $object->getFinances($type, $map_to);

			if($type == 'project') {
				$objectgroups = Objectgroups::getAllForProject($map_to);
				foreach ($objectgroups as $objectgroup) {
					$finances = array_merge(
						$finances,
						$object->getFinances('objectgroup', $objectgroup['id'])
					);
				}
			}

			$this->view->finances = $finances;
			$this->render('read-finances');
		}

		public function spacesAction() {

			$this->view->spaces = db()->fetchAll(db()->select()->from('objects_spaces')->where('`object` =  ?', $this->_getParam('object')));
			$this->render('read-spaces');
		}

		public function conditionsAction() {
			$type = $this->_getParam('type');
			$map_to = $this->_getParam('map_to');

			$conditions = [];
			if(intval($map_to) > 0) {
				$conditions = db()->fetchAll(db()
					->select()
					->from('contract_conditions_links')
					->where('`type` =  ?', $type)
					->where('`map_to` =  ?', $map_to)
					->joinLeft(array('cc' => 'contract_conditions'), 'cc.id = contract_conditions_links.condition_id', array('title'))
				);


				if ($type == "object") {

					$h = new Hierarchy($map_to, $type);

					$objectgroupConditions = db()->fetchAll(db()
						->select()
						->from('contract_conditions_links')
						->where('`type` =  ?', 'objectgroup')
						->where('`map_to` =  ?', $h->_data['objectgroup'])
						->joinLeft(['cc' => 'contract_conditions'], 'cc.id = contract_conditions_links.condition_id', ['title'])
					);

					$conditions = array_merge($conditions, $objectgroupConditions);
				}
			}

			$this->view->conditions = $conditions;
			$this->render('read-conditions');
		}		

		public function projectAction() {

		    if ($object_id = $this->getParam('object')) {
                $select = db()->select()
                    ->from(['o' => 'objects'], false)
                    ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['objectgroup_id' => 'id'])
                    ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project_id' => 'id'])
                    ->where('o.id = ?', $object_id);

                $data = db()->fetchRow($select);

                $project_id = $data['project_id'];
                $objectgroup_id =$data['objectgroup_id'];
            } else {
                //get all relevant ids
                $h = new Hierarchy($this->_getParam('mapTo'), $this->_getParam('type'));
                $project_id = $h->_data['project'];
                $objectgroup_id = $h->_data['objectgroup'];
                $object_id = $h->_data['object'];
            }

			$project = new Project();
			$object = new Object();

			$this->view->type = 'project';
			$this->view->id = $project_id;

			if ($project_id) {
				$this->view->project = $project->getDetails($project_id);
				$this->view->objectgroups = $object->getObjectGroups($project_id);
			}
			$this->view->objectgroupid = $objectgroup_id;
			$this->view->objectid = $object_id;
			$this->view->projectid = $project_id;

			$this->render('read-project');
		}

		public function objectListAction(){

			$this->view->headScriptHashed()->appendFile('media/javascript/support/object-list.js');

			$this->view->Breadcrumbs()->addCrumb('Objecten');

			$params = array('url-override' => 'support/object-list');

			foreach(array('view_template', 'id', 'user') as $param)
				if(!is_null($this->_getParam($param)))
					$params[$param] = $this->_getParam($param);

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('city' => 'ASC', 'address' => 'ASC', 'number' => 'ASC')));

			$uo = new Objectusers();

			$objects = array();
			foreach($uo->fetchAll($uo->select()->where('customer = ?', $params['user'])) as $uo_row)
				if($address = Address::get($uo_row->object, 'object')){
					$objects[$uo_row->object] = $address->toArray();
					$objects[$uo_row->object]['id'] = $address['type_id'];
					$objects[$uo_row->object]['uoId'] = $uo_row->id;

					$object = Objects::get($uo_row->object);

					if(Settings::get('general_company_shortname') == 'debazaar'){
						$select = db()->select()
							->from(['cr' => 'contract_relations'], false)
							->joinLeft(['c' => 'contracts'], 'c.id = cr.contract', [(Settings::get('modules_contracts_use_custom_identifier')?
									'identifier': 'system_identifier')])
							->where('cr.map_to = ?', $uo_row->id)
							->where('cr.type = ?', 'users_objects');

						$contract_id = db()->fetchOne($select);
						$objects[$uo_row->object]['contract_id'] = $contract_id ?: '-';
					}

					if($object != false){
						$objects[$uo_row->object]['build'] = $object->build;
						$objects[$uo_row->object]['object_type'] = $object->type;
					}

					if($params['id'] == $address['type_id'])
						$objects[$uo_row->object]['listview_row_classes'] = array('current');
				}

			if(count($objects) <= 1){
				$this->_helper->viewRenderer->setNoRender(true);
				return;
			}

			global $multiple_objects;
			$multiple_objects = true;

			$buildLabel = Settings::get('build_label');
			$buildLabel = !empty($buildLabel) && trim($buildLabel) !== false? $buildLabel: 'Bouwnr';

			$types = [
				'uoId' => ['title' => '', 'width' => 'hidden uoId'],
				'city' => ['title' => 'Stad', 'width' => 'small truncate', 'group_equal_rows' => true],
				'address' => ['title' => 'Adres', 'width' => 'small truncate'],
				'number' => ['title' => 'Nummer', 'width' => 'xxsmall right'],
				'build' => ['title' => $buildLabel, 'width' => 'xxsmall right truncate'],
				'spacer' => ['title' => '&nbsp;', 'width' => 'xxxxsmall'],
				'details' => ['title' => 'Details', 'width' => 'xxxsmall'],
			];

			if(Settings::get('general_company_shortname') == 'debazaar'){
				unset($types['city'], $types['address'], $types['number']);

				$types['build']['width'] = 'xsmall right truncate';
				array_add_after($types, 'spacer', ['object_type' => ['title' => 'Type', 'width' => 'medium truncate']]);
				array_add_after($types, 'spacer', ['contract_id' => ['title' => 'Contract', 'width' => 'small truncate']]);
			}


			$this->view->ListView($objects, $params)
				->setTypes($types)

				->addFormat('user', function($value, $item){
					return User::buildname(false, $value);
				})

				->addFormat('object_type', function($value){
					if($value > 0)
						$object_type = db()->fetchOne(db()->select()->from('object_type', ['name'])->where('id = ?', $value));
					
					return $object_type ?: '-';
				})

				->addFormat('zipcode', function($value){
					return "&nbsp;$value";
				})

				->addFormat('details', function($value, $row){
					return '<a class="button details" href="object/edit/id/' . $row['id'] . '" target="_blank">&nbsp;</a>';
				})

				->setOptions(array(
					'paginator_items_per_page' => 5
				))

				->setFilters([
					'city' => [],
					'address' => ['type' => 'input'],
					'number' => ['type' => 'input'],
					'build' => ['type' => 'input'],
					'contract_id' => ['type' => 'input'],
					'object_type' => []
				])

				->render($this);
		}

		public function objectAction() {

			//get all relevant ids
			$h = new Hierarchy($this->_getParam('object'), 'object');
			$consumptionModel = new ObjectsConsumptions();

			// get the first 'from' date that the indicated user has with
			// the indicated object and the last 'till' date, unless one of
			// the 'till' dates is NULL (then set $tillDate date to '-')
			$objUser = new Objectusers();
			$query = $objUser->select()
						->where('object = ?', $h->_data['object'])
						->where('customer = ?', $h->_data['user'])
					;
			$objects = $objUser->fetchAll($query);

			$fromDate = NULL;
			$tillDate = NULL;
			foreach ($objects as $obj) {
				if( isset($obj->from) && ( !isset($fromDate) || strtotime($obj->from) < $fromDate)) {
					$fromDate = strtotime($obj->from);
				}

				if (isset($obj->till) && (!isset($tillDate) || strtotime($obj->till) > $tillDate) && $tillDate !== 0) {
					$tillDate = strtotime($obj->till);
				}
				if (!isset($obj->till)) {
					$tillDate = 0;
				}
			}

			$fromDate = isset($fromDate)? date('d-m-Y', $fromDate) : '-';
			$tillDate = isset($tillDate) && $tillDate > 0 ? date('d-m-Y', $tillDate) : '-';
			
			$object = new Object();
			$this->view->type = 'object';
			if ($h->_data['object']) {
				$objectId = $h->_data['object'];
				$meters = $object->getMeters($objectId);
				$consumptionByMeter = array();
				foreach((array) $meters as $meter) {
					$meterId = $meter['meterid'];
					$consumptionByMeter[$meterId] = $consumptionModel->searchConsumption(array('object' => $objectId, 'meter' => $meterId));
				}
				$this->view->object = $object->getDetails($objectId);
				$this->view->objectFromDate = $fromDate;
				$this->view->objectTillDate = $tillDate;
				$this->view->consumption = $consumptionByMeter;
				$this->view->mapTo = $this->_getParam('mapTo');
				$this->view->meters = $meters;
			}
			if ($h->_data['project'])
				$this->view->objectgroups = $object->getObjectGroups($h->_data['project']);

			$this->render('read-object');
		}
		
		public function objectsAction() {
			$invModel = new Investors();
		
			$type = $this->_getParam('type');
			$id   = $this->_getParam('mapTo');
			
			if ($type == 'investor')
				$this->view->objects = $invModel->getObjects($id);
		}
		
		public function projectsAction() {
			$invModel = new Investors();
		
			$type = $this->_getParam('type');
			$id   = $this->_getParam('mapTo');
			
			if ($type == 'investor')
				$this->view->projects = $invModel->getProjects($id);
		}

		public function uploadAction(){
			
		
			
			$this->view->Breadcrumbs()->addCrumb('Support', 'support/');
			$this->view->Breadcrumbs()->addCrumb('Documenten uploaden', 'support/upload');

			$document = new Form($this, 'support/upload');

			//on post
			if ($this->getRequest()->isPost() && $document->isValid($this->getRequest()->getPost())) {
				$sd = new SupportDocuments();
				$uploaded = false;
				
				$data = $document->getValues();
				//print_r($data);

				$select = db()->select()
					->from(array('p'=>'projects'), array('id'))
					->where('p.type = ?', $data['type']);
				$projects = db()->fetchAll($select);

				foreach($projects as $project){

					$d = $sd->createRow();
					$sd->movefile = $uploaded ? false : true;
					$data = $document->getValues();
					
					$d->user = loginManager::data()->id;
					$d->title = $data['title'];
					$d->category = $data['category'];
					$d->titleid = $data['categoryTitle'];

					@mkdir(Main::app()->getDir('document').'project/'.$project['id'], 0777);
					$d->document = $uploaded ? end(explode('/',$uploaded)) : $data['document'];
					
					$d->description = $data['description'];
					$d->visibleForUser = $data['visibleForUser'];
					if ($data['originalDate'])
						$d->originalDate = new Zend_Db_Expr('FROM_UNIXTIME(' . strtotime($data['originalDate']) . ')');

					//bind $document to objectgroup
					$d->type = 'project';
					$d->map_to = $project['id'];
					
					$id = $d->save();
					copy($uploaded, $d->document);
					$uploaded = $d->document;
	
					//tags
					if ($data['tags'])
						new Tags($id, 'document', $data['tags']);
					
				}
						
				$this->_redirect('support/upload', array('success' => true));
				

			}

			$this->view->success = $this->_getParam('success');
			$this->view->document = $document;

		}

		public function techniqueAction() {

			$this->render('read-technique');
		}

		/**
		 * This function deduces what the type param should actually be based on the user-row's type, fixes the 'type'
		 * and 'id' parameters and redirects to support/show.
		 *
		 * At this moment this only fixes type 'user' to 'investor' and ignores all other cases.
		 */
		public function deduceTypeAndRedirectAction() {
			$this->disableView();
			$params = $this->getAllParams();

			if ( $params['type'] == 'user' && intval( $params['id'] ) > 0 ) {

				$user = Users::get( $params['id'] );
				if($user && $user->type == 'investor') {
					$iModel   = new Investors();
					$investor = $iModel->matchRow( [ 'user' => $params['id'] ] );

					if ( ! empty( $investor ) ) {
						$params['type'] = 'investor';
						$params['id']   = $investor->id;
					}
				}
			}

			$this->_redirect( 'support/show', $params );
		}

		public function actionbuttonVidiiDossierValidationAction() {
			$params = $this->getAllParams();

			$service = new Controllers\Support\ActionbuttonVidiiDossierValidation\Service();

			$service->execute(new Controllers\Support\ActionbuttonVidiiDossierValidation\Request(
				$params['type'], $params['map_to'], $this->view
			));
		}
	}
