<?

	class RentalDocumentsSetsController extends GlobalController {



        public function indexAction() {

            $this->_helper->redirector('list', 'rental-documents-sets');
        }


        public function listAction(){

            $rds = new RentalDocumentcheckSets();
            $data  =  $rds->getList();


            /**
             * @var $listView Zend_View_Helper_ListView
             */
            $this->view->ListView($data)
                ->setTypes([
                    'star' => ['title' => '', 'width' => 'xxxxsmall'],
                    'name' => ['title' => 'Naam', 'width' => 'xxxlarge'],
                    'status' =>  ['title' => 'Status', 'width' => 'small'],
                    'finalized' =>  ['width' => 'hidden'],
                ])

                ->addFormat('star', function($value, $item){
                    if($item['source'] == 'system'){
                        return '<i style="color: #84B83D;" class="fa fa-star" title="Systeem default"></i>';
                    }
                })

                ->addFormat(['finalized', 'archived'], 'bool')

                ->addFormat('status', function($value, $item){
                    if($item['archived']){
                        return 'Gearchiveerd';
                    }
                })

                ->setFilters([
                    'name' => ['type' => 'input', 'renderSeparately' => true, 'title' => 'Omschrijving'],
                    'archived' => ['renderSeparately' => true, 'title' => 'Gearchiveerd'],


                ])
                ->setOptions([
                    'item_title' => 'Document Set',
                    'disabled_buttons_test' => function($row, $button){
                        if($button == 'copy'){
                            return false;
                        }

                        if($button == 'edit'){
                            if (loginManager::data()->rights != 'admin' && $row['source'] == 'system') {
                                return true;
                            } else {
                                return false;
                            }
                        }
                        if($button == 'archive'){
                            if ($row['source'] == 'system' || $row['archived']) {
                                return true;
                            } else {
                                $rds = new RentalDocumentcheckSets();
                                return $rds->usedInProjects($row['id']);
                            }
                        }

                        return $row['finalized'] == 'Ja';
                    }
                ])

                ->addButtons([
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'archive' => 'Archiveren'
                ])

                ->render($this);

        }

        public function editAction()
        {
            $this->view->Breadcrumbs()
                ->addCrumb('Document Sets', 'rental-documents-sets/list/')
                ->addCrumb('Set '.($this->_getParam('id') ? 'bewerken' : 'toevoegen') ,'');


            $this->id = $this->getParam('id');

            $this->view->form = $this->view->EditView($this, 'RentalDocumentcheckSets',
                array('id' => $this->_getParam('id')))
                ->render($this);

        }

        public function archiveAction()
        {
            $this->disableView();

            $model = new RentalDocumentcheckSets();

            $model
                ->getById($this->getParam('id'))
                ->setFromArray(['archived' => true])
                ->save();


            if($referer = $_SERVER['HTTP_REFERER'])
                header('Location: '. $referer);
        }



	}
