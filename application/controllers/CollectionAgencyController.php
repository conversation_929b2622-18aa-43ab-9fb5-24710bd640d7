<?

	class CollectionAgencyController extends GlobalController {

		public function preDispatch(){
			$this->view->Breadcrumbs()
				->addCrumb('Financieel')
				->addCrumb('Incassobureau\'s', 'collection-agency/');
		}
		
		public function indexAction() {
			$ca = new CollectionAgency();
			$this->view->agencies = $ca->getList();
		}
		
		public function editAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Bewerken');
				
			$f = new Form($this, 'collectionAgency');
			$ca = new CollectionAgency();

			if ($this->getRequest()->isPost()){
				if($f->isValid($this->getRequest()->getPost())) {
					$ca->save($this->_getParam('id'), $this->getRequest()->getPost());
					
					$this->_helper->redirector('index', 'collection-agency');
				}
			} elseif ($this->_getParam('id')) {
				$data = $ca->get($this->_getParam('id'));
				
				$f->populate($data);
			}

			$this->view->form = $f;
		}
		
		public function deleteAction() {
			if (!is_numeric($this->_getParam('id')))
				die();	
				
			$ca = new CollectionAgencies();
			$ca->delete('id = ' . $this->_getParam('id'));
			
			$this->_helper->redirector('index', 'collection-agency');
		}
	}
