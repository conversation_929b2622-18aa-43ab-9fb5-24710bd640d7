<?

	include_once('Quarterly.php');

	class QuarterlyController extends GlobalController {

		public function indexAction() {
			$this->_forward('users');
		}

		public function manageAction() {

			$q = new Quarterly();
			$this->view->users = $q->getUsers();
			$this->view->departments = $q->departments();

		}

		/**
		 * @ACL Maps overzicht voor manage pagina
		 */
		public function quarterAction() {
			$this->disableView();

			$q = new QuarterlyQuarter($this->_getParam('quarter'), $this->_getParam('year'));
			$quarter = $q->get();

			echo json_encode($quarter);

		}

		public function userAction() {

			$id = $this->_getParam('id') ? $this->_getParam('id') : loginManager::data()->id;
			$quarter = $this->_getParam('quarter') ? $this->_getParam('quarter') : $this->currentQuarter();
			$year = $this->_getParam('year') ? $this->_getParam('year') : date('Y');

			$qq = new QuarterlyQuarter($quarter, $year);

			$this->view->user = $id;
			$this->view->tasks = $qq->forUser($id);
			$this->view->quarter = $qq->getquarter();
			$this->view->year = $qq->getYear();

		}

		public function usersAction() {

			$year = $this->_getParam('year') ? $this->_getParam('year') : date('Y');
			$department = (int) $this->_getParam('department') ? $this->_getParam('department') : loginManager::data()->info['department'];
			$department = $department == - 1 ? null : $department;

			$q = new Quarterly();

			$this->view->quarters = array();
			for ($i = 1; $i <= 4; $i++) {
				$qq = new QuarterlyQuarter($i, $year);
				$this->view->quarters[] = array(
					'info' => $qq->info(),
					'tasks' => $qq->tasks()
				);
			}

			$this->view->year = $year;
			$this->view->departments = $q->departments();
			$this->view->department = $department;
			$this->view->lastYear = quarterToDate($year - 1, 1);
			$this->view->nextYear = quarterToDate($year + 1, 1);

		}

		/**
		 * @ACL Maps opslaan
		 */
		public function saveTaskAction() {
			$this->disableView();

			$data = $this->_getParam('task');
			$task = new QuarterlyTask();
			$task->edit($data);
			$task->save();

			echo json_encode($task->toArray());
		}
		
		/**
		 * @ACL Maps verwijderen
		 */
		public function deleteTaskAction() {
			$this->disableView();

			$data = $this->_getParam('task');

			$task = new QuarterlyTask($data);
			$task->delete();
		}

		public function overviewAction() {

			$department = (int) $this->_getParam('department') > 0 ? $this->_getParam('department') : null;

			$q = new Quarterly();

			$history = $q->history($userId = null, $this->currentQuarter(), date('Y'), $limit = 45, $department);

			$this->view->history = $history;
			$this->view->departments = $q->departments();
			$this->view->department = $department;

		}

		private function currentQuarter() {
			return ceil(date('n') / 3);
		}

	}
