<?php

use Email\Dashboard\Service\GetEmailAccountListService as GetEmailAccountListService;
use Email\Dashboard\Service\GetEmailLabelListService as GetEmailLabelListService;
use Email\Dashboard\Service\GetEmailFolderListService as GetEmailFolderListService;
use Email\Dashboard\Service\GetStarredCountService as GetStarredCountService;

class Email_DashboardController extends GlobalController
{
    /**
     * @var $emailController EmailController
     */
    private $emailController;
    private $account_id;

    public function preDispatch()
    {
        $this->useParamCache('email_dashboard', ['folder', 'showUnassigned', 'account']);
        $this->account_id = $this->view->account_id = $this->getParam('account_id');

        $this->emailController = new EmailController(
            $this->getRequest(),
            $this->getResponse(),
            $this->getInvokeArgs()
        );

        $this->emailController->preDispatch();
    }

    private function loadDeprecatedListData(Zend_Paginator $list){
        $current_page_items = utf8_encode_recursive(
            $list->getCurrentItems()->getArrayCopy()
        );

        $current_page_items = array_combine(
            array_column($current_page_items, 'id'),
            $current_page_items
        );

        $array_list = [];

        foreach($list->getAdapter()->getItems(0, null) as $item){

            if(isset($current_page_items[$item['id']])){
                $item = $current_page_items[$item['id']];
            }

            if(count($item) > 1) {
                $item['from_id'] = $item['from']['id'];
                $item['to_id'] = $item['to']['id'];

                unset($item['from'], $item['to']);
            }

            $array_list[] = $item;
        }

        return $array_list;
    }

    private function setNumberOfMailsPerFolder()
    {
        $this->emailController->setUserList(false);


        $this->view->numberOfMails = [
            'inbox' => $this->view->users['inbox'],
            'unassigned' => $this->view->users['unassigned'],
            'archived' => $this->view->users['archived'],
            'system' => $this->view->users['system'],
            'sent' => $this->view->users['sent'],
            'error' => $this->view->users['error'],
            'concept' => $this->view->users['concept'],
            'me' => 0,
            'otherUsers' => [],
        ];

        foreach (Departments::getDepartments() as $department) {
            if (!isset($this->view->users[$department])) {
                continue;
            }

            foreach ($this->view->users[$department] as $departmentUser) {
                if ($departmentUser['id'] == loginManager::data()->id) {
                    $this->view->numberOfMails['me'] = $departmentUser['assigned'];
                } else {
                    $this->view->numberOfMails['otherUsers'][$departmentUser['id']] = $departmentUser['assigned'];
                }
            }
        }

        $this->view->users = null;
        $this->view->userids = null;
    }

    public function sidebarAction()
    {
        $emailAccountList = (new GetEmailAccountListService(new loginManager()))->execute();

        if (count($emailAccountList) === 0) {
            die('Geen E-mail accounts gevonden');
        }

        $selectedBranchLocation = loginManager::data()->selected_branch_location ?: null;

        $emailLabelList = (new GetEmailLabelListService())->execute($this->account_id, $selectedBranchLocation);
        $emailFolderList = (new GetEmailFolderListService())->execute($this->account_id, $selectedBranchLocation);
        $starredCount = (new GetStarredCountService())->execute($this->account_id);

        $this->setNewListDefaultValues($emailAccountList);

        if ($this->isAjaxRequest) {
            $this->disableView(true);
            $this->view->internalUsers = (new Users())->getInternalUsersOptionsGroupedByDepartment();
        }

        $this->view->accountList = $emailAccountList;
        $this->view->labelList = $emailLabelList;
        $this->view->folderList = $emailFolderList;


        $this->view->starredCount = $starredCount;


        $this->setNumberOfMailsPerFolder();

        $this->render('parts/sideBar');

    }

    public function indexAction()
    {
        $this->view->headScriptHashed()
            ->appendFile('media/javascript/email_dashboard/loader.js', 'module');

        $this->view->Breadcrumbs()
            ->addCrumb('Email')
            ->addCrumb('Email dashboard');

        $this->renderWithNewLayout();

        $selectedBranchLocation = loginManager::data()->selected_branch_location ?: null;
        $this->view->internalUsers = (new Users())->getInternalUsersOptionsGroupedByDepartment();
        $this->view->labelList = (new GetEmailLabelListService())->execute($this->account_id, $selectedBranchLocation);
        $this->view->folderList = (new GetEmailFolderListService())->execute($this->account_id, $selectedBranchLocation);
    }

    private function getTempAccountFilter($emailAccountList, $value = false)
    {
        $filterValues = [];
        foreach ($emailAccountList as $accountId => $accountName) {
            $filterValues[$accountId] = ['title' => $accountName];
        }

        $filterArray = [
            'title' => 'Account',
            'custom_options' => $filterValues,
            'hideCount' => true,
            'preApplied' => true,
            'custom_options_only' => true,
            'show_all_disabled' => true
        ];

        if ($value !== false) {
            $filterArray['value'] = $value;
        }

        return $filterArray;
    }

    private function getTempLabelFilter($emailLabelList, $value = false)
    {
        $filterValues = [];
        foreach ($emailLabelList as $labelId => $labelValues) {
            $filterValues[$labelId] = ['title' => $labelValues['label']];
        }

        $filterArray = [
            'title' => 'Label',
            'custom_options' => $filterValues,
            'hideCount' => true,
            'preApplied' => true,
            'custom_options_only' => true,
            'show_all_disabled' => false
        ];

        if ($value !== false) {
            $filterArray['value'] = $value;
        }

        return $filterArray;
    }

    private function getTempFolderFilter($emailFolderList, $value = false)
    {
        $filterValues = [];
        foreach ($emailFolderList as $folderId => $folderValues) {
            $filterValues[$folderId] = ['title' => $folderValues['label']];
        }

        $filterArray = [
            'title' => 'Map',
            'custom_options' => $filterValues,
            'hideCount' => true,
            'preApplied' => true,
            'custom_options_only' => true,
            'show_all_disabled' => false
        ];

        if ($value !== false) {
            $filterArray['value'] = $value;
        }

        return $filterArray;
    }

    private function getTempStarredFilter($value = false)
    {
        $filterArray = [
            'title' => 'Ster',
            'custom_options' => [
                0 => ['title' => 'Nee'],
                1 => ['title' => 'Ja'],
            ],
            'hideCount' => true,
            'preApplied' => true,
            'custom_options_only' => true,
            'show_all_disabled' => false
        ];

        if ($value !== false) {
            $filterArray['value'] = $value;
        }

        return $filterArray;
    }

    private function getTempAssignedFilter($value = false)
    {
        $assignedValues = [
            'unassigned' => 'Inbox (Niet toegewezen)',
            'inbox' => 'Alle (Niet afgehandeld)',
            'archived' => 'Afgehandeld',
            'sent' => 'Verzonden',
            'error' => 'Niet afgeleverd',
            'concept' => 'Concepten',
            'system' => 'Systeemberichten',
        ];

        $internalUsers = (new Users())->getInternalUsersOptionsGroupedByDepartment();

        foreach ($internalUsers as $departmentUsers) {
            foreach ($departmentUsers as $userId => $userName) {
                $assignedValues[$userId] = $userName;
            }
        }

        $assignedValues = array_map(function($filterValues){
            return ['title' => $filterValues];
        }, $assignedValues);

        $assignedArray = [
            'title' => 'Map',
            'custom_options' => $assignedValues,
            'hideCount' => true,
            'preApplied' => true,
            'custom_options_only' => true,
            'show_all_disabled' => true,
        ];

        if ($value !== false) {
            $assignedArray['value'] = $value;
        }

        return $assignedArray;
    }

    private function setNewListDefaultValues($emailAccountList)
    {
        if (!$this->getParam('account_id')) {
            if (count($emailAccountList) > 0) {
                $firstAccountId = array_keys($emailAccountList)[0];
                $this->setParam('account_id', $firstAccountId);
            }
        }

        if (is_null($this->getParam('assigned'))) {
            $this->setParam('assigned', loginManager::data()->id);
        }

        if (is_null($this->getParam('showUnassigned'))) {
            $isOtherUserThenMe = is_numeric($this->getParam('assigned')) && $this->getParam('assigned') != loginManager::data()->id;
            $this->setParam(
                'showUnassigned',
                !$isOtherUserThenMe
            );
        }
    }

    private function setMagicParams()
    {
        if ($this->getParam('assigned') === 'concept') {
            $this->setParam('concept', 'true');
        }

        if (
            !is_null($this->getParam('folder_id')) ||
            !is_null($this->getParam('label')) ||
            $this->getParam('starred') === '1'
        ) {
            $this->setParam('assigned', false);
            $this->setParam('folder', false);
        }

        if ($this->getParam('assigned') === 'error') {
            $this->setParam('errorList', true);
        }
    }

    private function isIncomingMailbox()
    {
        if (is_null($this->getParam('assigned'))) {
            return true;
        }

        if (!in_array($this->getParam('assigned'), ['sent', 'error', 'concept'])) {
            return true;
        }

        return false;
    }

    public function newListAction(){
        $selectedBranchLocation = loginManager::data()->selected_branch_location ?: null;
        $emailAccountList = (new GetEmailAccountListService(new loginManager()))->execute();
        $emailLabelList = (new GetEmailLabelListService())->execute($this->account_id, $selectedBranchLocation);
        $emailFolderList = (new GetEmailFolderListService())->execute($this->account_id, $selectedBranchLocation);

        $this->setNewListDefaultValues($emailAccountList);

        $this->setMagicParams();

        $this->renderWithNewLayout();


        $this->setParam('limit', 25);


        $internalUsers = (new Users())->getInternalUsers();
        $isIncomingMailbox = $this->isIncomingMailbox();

        $this->emailController->listAction();
        /** @var Zend_Paginator $list */
        $list = $this->view->list;
        $listData = $this->loadDeprecatedListData($list);

        foreach ($listData as $listDataKey => $listDataItem) {
            $listData[$listDataKey]['listview_row_classes'] = [];

            if (!$listDataItem['read']) {
                $listData[$listDataKey]['listview_row_classes'][] = 'table-warning';
            }
        }


        $this->view->listDataHash = md5(json_encode($listData));

        if ($this->isAjaxRequest) {
            $previousListDataHash = $this->getParam('listDataHash');
            if ($previousListDataHash === $this->view->listDataHash) {
                $this->disableView();

                $this
                    ->getResponse()
                    ->setHttpResponseCode(304);

                return;
            }
        }

        $emailViewPaginatorAdapter = new EditableCountPaginatorAdapter($listData);
        $emailViewPaginatorAdapter->setCount($list->getTotalItemCount());

        $paginatorInstance = new Zend_Paginator($emailViewPaginatorAdapter);

        $this->view->ListView($paginatorInstance, ['url-override' => 'email_dashboard/new-list/', 'paginator_instance' => $paginatorInstance])
            ->setTypes([
                'extraActions' => ['width' => 'p-r-0 extraActions', 'format_current_page_items_only' => true],
                'query' => ['width' => 'p-0 widthZero searchQuery'],
                'assigned' => ['width' => 'js-assigned-filter assigned', 'format_current_page_items_only' => true],
                'sender_recipient' => ['title' => $isIncomingMailbox ? 'Afzender' : 'Ontvanger', 'width' => 'truncate senderRecipient', 'format_current_page_items_only' => true],
                'subject' => ['title' => 'Onderwerp', 'width' => 'js-subject truncate subject', 'format_current_page_items_only' => true],
                'attachment' => ['title' => '', 'format_current_page_items_only' => true],
                'date' => ['title' => 'Datum', 'width' => 'date', 'format_current_page_items_only' => true],
                'folder_id' => ['width' => ' js-folder-filter folder-filter hide'],
                'account_id' => ['width' => ' js-account-filter account-filter'],
                'label' => ['width' => 'hidden js-label-filter label-filter'],
                'starred' => ['width' => 'hidden js-starred-filter starred-filter'],
            ])
            ->addActions([
                'buttons' => [
                    ['title' => 'Etalage sheet', 'icon' => 'report', 'action' => 'rental/get-window-sheets', 'confirm' => false, 'noAjax' => true],
                    ['title' => 'Brochure downloaden', 'icon' => 'report', 'action' => 'rental/get-brochure', 'noAjax' => true],
                    ['title' => 'Brochure mailen', 'icon' => 'report', 'action' => 'rental/brochure-email', 'javascript_function' => 'email_compose_function', 'confirm' => false],
                    ['title' => 'Route maken', 'icon' => 'house_go', 'action' => 'rental/make-route', 'confirm' => false, 'noAjax' => true],
                ]
            ])

            ->addFormat('extraActions', function ($item, $row) {

                $extraActionsHtml = '';

                if ($row['starred']) {
                    $extraActionsHtml .= '<i class="fas fa-star m-r-5 text-warning"></i>';
                } else {
                    $extraActionsHtml .= '<i class="fa fa-star m-r-5 text-muted"></i>';
                }

                if ($row['label_id']) {
                    $extraActionsHtml .=
                        '<i
                            class="fa fa-circle m-l-5"
                            style="color:' . $row['labelColor'] . '"
                            title="' . $row['label'] . '"
                         ></i>';
                }

                return $extraActionsHtml;
            })

            ->addFormat('assigned', function ($item, $row) use ($internalUsers) {

                if ($row['user'] > 0 && isset($internalUsers[$row['user']])) {
                    $userData = $internalUsers[$row['user']];
                    $initials = $userData['shortname'] ?: str_replace([' ', '.'], '', $userData['initials']);
                    $title = htmlentities('Toegewezen aan: ' . $userData['rendered_name']);
                    $isMeClass = $row['user'] == loginManager::data()->id ? ' isMe' : '';

                    return '<div
                            class="js-assigned-to-user assignedToUser' . $isMeClass . '"
                            title="' . $title . '"
                            noHint="1">' .
                        $initials .
                        '</div>';
                }

                return '';
            })

            ->addFormat('date', function($value){
                if(!strtotime($value)){
                    return '-';
                }

                $fullDateString = strftime('%a %d-%m-%y - %H:%M', strtotime($value));
                $relativeDateString = relativeDate(strtotime($value));

                return '<span title="' . $relativeDateString . '" noHint="1">' . $fullDateString . '</span>';
            })

            ->addFormat('subject', function($value, $item){
                return '<a href="email/preview/id/' . $item['id']. '">' . $value . '</a>';
            })

            ->addFormat('attachment', function ($value) {
                if (!$value) {
                    return '';
                }

                return '<i class="fas fa-paperclip" title="E-mail bericht heeft een bijlage"></i>';
            })

            ->addFormat('sender_recipient', function($value, $item) use ($isIncomingMailbox){
                return $isIncomingMailbox ? $item['from_name'] : $item['to_name'];
            })

            ->setFilters([
                'date' => ['type' => 'date_range'],
                'recipient' => ['type' => 'input'],
                'query' => [
                    'title' => 'Zoeken',
                    'type' => 'input',
                    'placeholder' => 'Doorzoek E-mailbox',
                    'preApplied' => true
                ],
                'assigned' => $this->getTempAssignedFilter($this->getParam('assigned')),
                'account_id' => $this->getTempAccountFilter($emailAccountList, $this->getParam('account_id')),
                'label' => $this->getTempLabelFilter($emailLabelList, $this->getParam('label')),
                'folder_id' => $this->getTempFolderFilter($emailFolderList, $this->getParam('folder_id')),
                'starred' => $this->getTempStarredFilter($this->getParam('starred'))
            ])

            ->addLegend('list-legend')

            ->setOptions([
                'enable_param_cache' => true
            ])

            ->render($this);
    }

    public function newDetailsAction(){
        if($this->isAjaxRequest){
            $this->disableView(true);
        }

        $this->emailController->detailsAction();

        $this->setEmailAttachmentUserId();
    }

    public function markAsReadAction()
    {
        $this->disableView();

        $markAsReadService = new \Controllers\Email\Dashboard\Services\MarkAsRead(
            new Emails()
        );

        $ids = $this->getParam('ids');

        if ($this->getParam('undo')) {
            $markAsReadService->markAsUnread($ids);
        } else {
            $markAsReadService->execute($ids);
        }
    }

    private function setEmailAttachmentUserId()
    {
        $email = $this->view->email;

        if($email['direction'] !== 'incoming'){
            return;
        }

        $userId = null;

        if($email['from']) {
            $userId = $email['from']['id'];
        }

        if (isset($email['matched_once'])) {
            if (isset($email['matched_once']['user'])) {
                $userId = $email['matched_once']['user'];
            }
        }

        $this->view->emailAttachmentUserId = $userId ?: null;
    }
}
