<?php

class Email_EmailAddressOverviewController extends GlobalController
{
    public function indexAction()
    {
        $this->view->Breadcrumbs()->addCrumb('E-mailadres systeem weergave');
        $this->view->extraWidth = true;

        $searchParam = $this->getParam('email_address');

        $data = (new \Email\Service\ViewEmailAdressOverviewService())->execute($searchParam);

        $this->view->list = $this->view->listView($data)
            ->setTypes([
                'email_address' => ['title' => 'E-mail adres', 'width' => 'xxxlarge'],
                'connected_to' => ['title' => 'Gekoppeld aan.', 'width' => 'xxxxlarge'],
                'type' => ['title' => 'Typen', 'width' => 'xlarge'],
                'actions' => ['title' => 'Acties', 'width' => 'small'],
            ])
            ->addFormat('connected_to', function ($value, $item) {
                switch ($item['source']) {
                    case 'users':
                        switch ($item['type']) {
                            case 'tenant':
                            case 'registrant':
                            case 'investor':
                            case 'landlord':
                            case 'broker':
                            case 'internal':
                            case 'employee':
                            case 'manager':
                            case 'cfo':
                            case 'company':
                                return "<a href='support/show/id/{$item['map_to']}/type/user#naw' target='_blank'>$value</a>";
                            default:
                                return '-';
                        }
                    case 'email_users':
                        return "-";
                    case 'email_accounts':
                        return "<a href='/email/account-edit/id/{$item['map_to']}' target='_blank'>$value</a>";
                    default:
                        return '-';
                }
            })
            ->addFormat('type', function ($value, $item) {
                $userTypeMap = [
                    'tenant' => 'Huurder',
                    'registrant' => 'Registrant',
                    'investor' => 'Registrant',
                    'landlord' => 'Belegger',
                    'broker' => 'Makelaar',
                    'internal' => 'Interne gebruiker',
                    'employee' => 'Medewerker',
                    'manager' => 'Manager',
                    'cfo' => 'CFO',
                    'company' => 'company',
                ];

                switch ($item['source']) {
                    case 'users':
                        return $userTypeMap[$item['type']];
                    case 'email_users':
                        return "E-mail communicatie";
                    case 'email_accounts':
                        return 'Interne gebruiker';
                    case 'email_address_exclude':
                        return 'Verborgen e-mailadressenlijst';
                    default:
                        return '-';
                }
            })
            ->addFormat('actions', function ($value, $item) use ($searchParam) {
                if ($item['is_excluded'] === true) {
                    return "<a class='button add' href='/email_email-address-overview/remove-email-address-exclude/email_address/{$item['email_address']}/search_param/$searchParam'>Zichtbaar maken</a>";
                }

                return "<a class='button delete' href='/email_email-address-overview/add-email-address-exclude/email_address/{$item['email_address']}/search_param/$searchParam'>Verbergen</a>";
            })
            ->setFilters([
                'email_address' => ['renderSeparately' => true, 'title' => 'E-mail adres', 'type' => 'input'],
                'type' => ['type' => 'select'],
                'connected_to' => ['type' => 'input'],
                'status' => ['type' => 'select'],
                'actions' => ['type' => 'select'],
            ])
            ->addLegend('list-legend')
            ->render($this);
    }

    public function addEmailAddressExcludeAction()
    {
        $this->disableView();

        $emailAddress = $this->getParam('email_address');
        $searchParam = $this->getParam('search_param');

        $emailAddressExcludeRow = (new \DbTable\EmailAddressExclude())->fetchRow(['email_address = ?' => $emailAddress]);

        if (null === $emailAddressExcludeRow) {
            $emailAddressExcludeRow = (new \DbTable\EmailAddressExclude())->createRow();
        }

        $emailAddressExcludeRow->email_address = strtolower($emailAddress);
        $emailAddressExcludeRow->save();

        $this->_redirect('index', ['email_address' => $searchParam]);
    }

    public function removeEmailAddressExcludeAction()
    {
        $this->disableView();

        $emailAddress = $this->getParam('email_address');
        $searchParam = $this->getParam('search_param');

        $emailAddressExcludeRow = (new \DbTable\EmailAddressExclude())->fetchRow(['email_address = ?' => $emailAddress]);

        if (null === $emailAddressExcludeRow) {
            throw new RuntimeException('Could not find email address by provided: ' . $emailAddress);
        }

        $emailAddressExcludeRow->delete();

        $this->_redirect('index', ['email_address' => $searchParam]);
    }
}
