<?php


class ComplaintCategory_BudgetAuthorizationController extends \GlobalController
{
    private $categoryId;

    public function preDispatch()
    {
        parent::preDispatch();

        if (!Settings::get('support_complaint_autorisation')) {
            throw new \Exception('support_complaint_autorisation is not active');
        }

        $this->categoryId = $this->_getParam('categoryId');

        if (!$this->categoryId) {
            throw new \Exception('No categoryId is set! missing param: categoryId ');
        }

        $this->setCategoryBreadcrumb($this->categoryId);

    }

    private function setCategoryBreadcrumb($categoryId)
    {
        $complaintCategoryModel = new ComplaintCategories();
        $complaintCategoryRow = $complaintCategoryModel->getById($categoryId);

        if (!$complaintCategoryRow) {
            throw new Exception('Complaint Categories doesn\'t exist');
        }
        $this->view->Breadcrumbs()
            ->addCrumb('Melding categorieën instellen', 'complaint-categories/list')
            ->addCrumb($complaintCategoryRow['name'],
                'complaint-category_budget-authorization/list/categoryId/' .$complaintCategoryRow['id']. '/'
            )
            ->addCrumb('Budget Authorization',
                'complaint-category_budget-authorization/list/categoryId/' .$complaintCategoryRow['id']. '/'
            );
    }

    public function indexAction()
    {
        $this->_helper->redirector(
            'list',
            'complaint-category_budget-authorization',
            null,
            ['categoryId' => $this->categoryId]
        );
        return;
    }

    public function listAction()
    {
        $select = db()->select()
            ->from(['sccba' => 'support_complaints_categories_budget_authorization_rules'])
            ->joinLeft(['dp' => 'departments'],
                'sccba.department_id = dp.id',
                ['department_name' => 'name']
            )
            ->joinLeft(['ar' => 'acl_roles'],
                'sccba.acl_role_id = ar.id',
                ['acl_role_name' => 'name']
            )

            ->where('support_complaints_category_id = ?' , $this->categoryId)
            ->order('sccba.from_budget')
            ->order('sccba.department_id')
            ->order('sccba.acl_role_id')
        ;

        $budgetAuthorizationListViewResponseRows = db()->fetchAll($select);

        $this->view->ListView($budgetAuthorizationListViewResponseRows)
            ->setTypes([
                'from_budget' => ['title' => 'Vanaf bedrag', 'width' => 'large'],
                'department_name' => ['title' => 'Afdeling', 'width' => 'large'],
                'acl_role_name' => ['title' => 'Rechten', 'width' => 'large'],
            ])
            ->setFilters([])
            ->addFormat('from_budget', 'money')
            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
                'delete' => 'Verwijderen'
            ], [
                'id' => 'id',
                'categoryId' => $this->categoryId
            ])
            ->render($this);
    }


    public function editAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Bewerken / toevoegen');


        $this->view->EditView($this, 'ComplaintCategoryBudgetAuthorizationRules')
            ->setOptions([])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();
        $complaintCategoryBudgetAuthorizationRulesId = $this->getParam('id');

        if (!$complaintCategoryBudgetAuthorizationRulesId) {
            $this->indexAction();
        }

        $complaintCategoryBudgetAuthorizationRulesModel = new ComplaintCategoryBudgetAuthorizationRules();
        $complaintCategoryBudgetAuthorizationRuleRow = $complaintCategoryBudgetAuthorizationRulesModel
            ->getById($complaintCategoryBudgetAuthorizationRulesId);

        if (!$complaintCategoryBudgetAuthorizationRuleRow) {
            $this->indexAction();
        }

        $complaintCategoryBudgetAuthorizationRuleRow->delete();

        $this->indexAction();
    }


    public function getFormSelectDepartments()
    {
        $select = db()->select()
            ->from(['d' => 'departments'],  ['id', 'name']);
        $departmentsRows = db()->fetchAssoc($select);

        if (!$departmentsRows) {
            return [];
        }

        return array_column($departmentsRows, 'name', 'id');
    }

    public function getFormSelectAclRoles()
    {
        $select = db()->select()
            ->from(['ar' => 'acl_roles'], ['id','name']);
        $aclRolesRows = db()->fetchAssoc($select);



        if (!$aclRolesRows) {
            return [];
        }

        return array_column($aclRolesRows, 'name', 'id');
    }

    public function getCategoryId()
    {
        return $this->categoryId;
    }

}
