<?

use Controllers\Vidii\Api\GetPublishedObjectsFileService;
use Controllers\Vidii\Api\GetProjectDataAction;

class Vidii_ApiController extends GlobalController
{
    /**
     * http://dev.omniboxx.johan.loc/vidii_api/get-published-objects/strategy/all-published-in-rental-light/
     *
     * ** strategy **
     * - all-in-project
     * - all-published-in-rental-light
     * - all-published-in-rental-mutations
     * - single-object
     * - quick-publish-object
     *
     * @return void
     */
	public function getPublishedObjectsAction()
    {
	    try {
			$fileContents = (new GetPublishedObjectsFileService())->execute(
				$this->getAllParams()
			);

		    ob_clean();
		    header('Content-Type: text/xml');
		    echo $fileContents;
		    die();
		} catch (\Exception $e) {
		    dieWithStatuscode(304);
	    }
    }

    public function getProjectDataAction(){
	    $this->disableView();

        $file_contents = (new GetProjectDataAction())->execute();

        ob_clean();
        header('Content-Type: text/xml');
        echo $file_contents;
    }
}
