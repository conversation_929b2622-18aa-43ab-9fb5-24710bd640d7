<?php

use Vidii\Application\Service\ViewAgeReportRequest;
use Vidii\Application\Service\ViewAgeReportService;
use Vidii\Application\Service\ViewGenderReportRequest;
use Vidii\Application\Service\ViewGenderReportService;
use Vidii\Application\Service\ViewGeoReportRequest;
use Vidii\Application\Service\ViewGeoReportService;
use Vidii\Application\Service\ViewLanguageReportRequest;
use Vidii\Application\Service\ViewLanguageReportService;
use Vidii\Application\Service\ViewSourceReportRequest;
use Vidii\Application\Service\ViewSourceReportService;
use Vidii\Application\Transformer\DashboardAgeReportTransformer;
use Vidii\Application\Transformer\DashboardGenderReportTransformer;
use Vidii\Application\Transformer\DashboardGeoReportTransformer;
use Vidii\Application\Transformer\DashboardLanguageReportTransformer;
use Vidii\Application\Transformer\DashboardSourceReportTransformer;
use Vidii\Application\Transformer\GoogleMap;
use Vidii\Domain\Exception\AgeReportNotFoundException;
use Vidii\Domain\Exception\GenderReportNotFoundException;
use Vidii\Domain\Exception\GeoReportNotFoundException;
use Vidii\Domain\Exception\LanguageReportNotFoundException;
use Vidii\Domain\Exception\SourceReportNotFoundException;
use Vidii\Infrastructure\Domain\Factory\Report\AgeReportFactory;
use Vidii\Infrastructure\Domain\Factory\Report\GenderReportFactory;
use Vidii\Infrastructure\Domain\Factory\Report\GeoReportFactory;
use Vidii\Infrastructure\Domain\Factory\Report\LanguageReportFactory;
use Vidii\Infrastructure\Domain\Factory\Report\SourceReportFactory;
use Vidii\Infrastructure\Domain\Repository\PDOAgeReportRepository;
use Vidii\Infrastructure\Domain\Repository\PDOGenderReportRepository;
use Vidii\Infrastructure\Domain\Repository\PDOGeoReportRepository;
use Vidii\Infrastructure\Domain\Repository\PDOLanguageReportRepository;
use Vidii\Infrastructure\Domain\Repository\PDOSourceReportRepository;
use Vidii\Infrastructure\Persist\Gateway\AgeReportGateway;
use Vidii\Infrastructure\Persist\Gateway\GenderReportGateway;
use Vidii\Infrastructure\Persist\Gateway\GeoReportGateway;
use Vidii\Infrastructure\Persist\Gateway\LanguageReportGateway;
use Vidii\Infrastructure\Persist\Gateway\SourceReportGateway;

class Vidii_ChartController extends GlobalController
{
    private $from;
    private $till;
    private $projectIds = [];

    public function preDispatch()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/chart/chart.bundle.2.6.0.js');
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/vidii-chart.js');
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/main.js');
        $this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');

        $this->from = new DateTime($this->getParam('from'));
        $this->till = new DateTime($this->getParam('till'));
        $this->setProjectIds($this->getParam('project_ids'));

        $this->view->defaultFrom = (new DateTime('-10 months'))->format('d-m-Y');
        $this->view->defaultTill = (new DateTime())->format('d-m-Y');
    }

    private function setProjectIds($projectIds)
    {
        if (is_array($projectIds)) {
            $projectIds = array_filter($projectIds);
        }

        if (!is_array($projectIds) && strpos($projectIds, ',') !== false) {
            $projectIds = explode(',', $projectIds);
        }

        if ($projectIds == "undefined" || $projectIds == "") {
            $projectIds = [];
        }

        $this->projectIds = (array)$projectIds;
    }

    public function ageChartAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/age-chart.js');
    }

    public function jsonAgeChartAction()
    {
        $this->disableView();

        $ageReportGateway = new AgeReportGateway();
        $ageReportFactory = new AgeReportFactory();
        $ageReportRepository = new PDOAgeReportRepository($ageReportGateway, $ageReportFactory);
        $ageReportTransformer = new DashboardAgeReportTransformer();
        $service = new ViewAgeReportService($ageReportRepository, $ageReportTransformer);

        try {
            $service->execute(new ViewAgeReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            echo json_encode($service->getAgeReportTransformer()->read());
        } catch (AgeReportNotFoundException $e) {
            http_response_code(404);
        }
    }

    public function genderChartAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/gender-chart.js');
    }

    public function jsonGenderChartAction()
    {
        $this->disableView();

        $genderReportGateway = new GenderReportGateway();
        $genderReportFactory = new GenderReportFactory();
        $genderReportRepository = new PDOGenderReportRepository($genderReportGateway, $genderReportFactory);
        $genderReportTransformer = new DashboardGenderReportTransformer();
        $service = new ViewGenderReportService($genderReportRepository, $genderReportTransformer);

        try {
            $service->execute(new ViewGenderReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            echo json_encode($service->getGenderReportTransformer()->read());
        } catch (GenderReportNotFoundException $e) {
            http_response_code(404);
        }
    }

    public function languageChartAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/language-chart.js');
    }

    public function jsonLanguageChartAction()
    {
        $this->disableView();

        $languageReportGateway = new LanguageReportGateway();
        $languageReportFactory = new LanguageReportFactory();
        $languageReportRepository = new PDOLanguageReportRepository($languageReportGateway, $languageReportFactory);
        $languageReportTransformer = new DashboardLanguageReportTransformer();
        $service = new ViewLanguageReportService($languageReportRepository, $languageReportTransformer);

        try {
            $service->execute(new ViewLanguageReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            echo json_encode($service->getLanguageReportTransformer()->read());
        } catch (LanguageReportNotFoundException $e) {
            http_response_code(404);
        }
    }

    public function sourceChartAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/source-chart.js');
    }

    public function jsonSourceChartAction()
    {
        $this->disableView();

        $sourceReportGateway = new SourceReportGateway();
        $sourceReportFactory = new SourceReportFactory();
        $sourceReportRepository = new PDOSourceReportRepository($sourceReportGateway, $sourceReportFactory);
        $sourceReportTransformer = new DashboardSourceReportTransformer();
        $service = new ViewSourceReportService($sourceReportRepository, $sourceReportTransformer);

        try {
            $service->execute(new ViewSourceReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            echo json_encode($service->getSourceReportTransformer()->read());
        } catch (SourceReportNotFoundException $e) {
            http_response_code(404);
        }
    }

    public function geoGoogleMapsAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/geo-google-maps.js');
    }

    public function jsonGeoGoogleMapsAction()
    {
        $this->disableView();

        $geoReportGateway = new GeoReportGateway();
        $geoReportFactory = new GeoReportFactory();
        $geoReportRepository = new PDOGeoReportRepository($geoReportGateway, $geoReportFactory);
        $geoReportTransformer = new \Vidii\Application\Transformer\GoogleMapsGeoReportTransformer();
        $service = new ViewGeoReportService($geoReportRepository, $geoReportTransformer);

        try {
            $service->execute(new ViewGeoReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            $data = $service->getGeoReportTransformer()->read();

            echo (new GoogleMap())->render($data);
        } catch (GeoReportNotFoundException $e) {
            echo '<p style="text-align: center; margin-top: 30%;">Geen data beschikbaar.</p>';
        }
    }

    public function geoChartAction()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/vidii_chart/geo-chart.js');
    }

    public function jsonGeoChartAction()
    {
        $this->disableView();

        $geoReportGateway = new GeoReportGateway();
        $geoReportFactory = new GeoReportFactory();
        $geoReportRepository = new PDOGeoReportRepository($geoReportGateway, $geoReportFactory);
        $geoReportTransformer = new DashboardGeoReportTransformer();
        $service = new ViewGeoReportService($geoReportRepository, $geoReportTransformer);

        try {
            $service->execute(new ViewGeoReportRequest(
                $this->from,
                $this->till,
                $this->projectIds
            ));

            echo json_encode($service->getGeoReportTransformer()->read());
        } catch (GeoReportNotFoundException $e) {
            http_response_code(404);
        }
    }
}
