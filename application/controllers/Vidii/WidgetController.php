<?php

use Vidii\Application\Service\ViewRentedTargetAudiencesService;
use Vidii\Application\Service\ViewRentedThisWeekService;
use Vidii\Application\Service\ViewSubscriptionThisWeekService;
use Vidii\Application\Service\ViewTotalRentedService;

class Vidii_WidgetController extends GlobalController
{
    private $projectIds = [];

    public function preDispatch()
    {
        $this->view->headScriptHashed()->appendFile('media/javascript/chart/chart.bundle.2.6.0.js');
        $this->setProjectIds($this->getParam('project_ids'));
    }

    private function setProjectIds($projectIds)
    {
        if (is_array($projectIds)) {
            $projectIds = array_filter($projectIds);
        }

        if (!is_array($projectIds) && strpos($projectIds, ',') !== false) {
            $projectIds = explode(',', $projectIds);
        }

        if ($projectIds == "undefined" || $projectIds == "") {
            $projectIds = [];
        }

        $this->projectIds = (array)$projectIds;
    }

    public function rentedThisWeekAction()
    {
        $result = (new ViewRentedThisWeekService())->execute($this->projectIds);

        $this->view->subscriptionCount = $result['subscription_count'];
        $this->view->availableObjectCount = $result['available_object_count'];
    }

    public function totalRentedAction()
    {
        $result = (new ViewTotalRentedService())->execute($this->projectIds);

        $this->view->assign('totalAvailable', $result['total_available']);
        $this->view->assign('totalSubscription', $result['total_subscription']);
        $this->view->assign('totalDocumentsRequested', $result['total_documents_requested']);
        $this->view->assign('totalDocumentsRequestedDeadlineExceeded', $result['total_documents_requested_deadline_exceeded']);
        $this->view->assign('totalDocumentsSubmitted', $result['total_documents_submitted']);
        $this->view->assign('totalClearedForContract', $result['total_cleared_for_contract']);
        $this->view->assign('totalClearedForContractExceeded', $result['total_cleared_for_contract_exceeded']);
        $this->view->assign('totalRented', $result['total_rented']);
    }

    public function subscriptionsThisWeekAction()
    {
        $result = (new ViewSubscriptionThisWeekService())->execute($this->projectIds);

        $this->view->assign('subscriptionCount', $result['rented_this_week']);
        $this->view->assign('totalObjects', $result['total_objects']);
        $this->view->assign('totalAvailable', $result['total_available']);
        $this->view->assign('totalInOption', $result['total_in_option']);
        $this->view->assign('totalRented', $result['total_rented']);
    }

    public function targetAudiencesAction()
    {
        $result = (new ViewRentedTargetAudiencesService())->execute($this->projectIds);

        $this->view->data = $result['data'];
        $this->view->labels = $result['labels'];
    }
}
