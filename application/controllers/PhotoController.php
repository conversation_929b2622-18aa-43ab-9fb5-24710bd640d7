<?php

use Photo\Application\Service\ChangeOrderRequest;
use Photo\Application\Service\DeletePhotoRequest;
use Photo\Application\Service\UploadPhotoRequest;
use Photo\Domain\Model\PhotoOrderChanged;
use Photo\Domain\Model\PhotoUploaded;
use Photo\Infrastructure\Domain\Model\PDOPhotoRepository;
use SharedKernel\Domain\DomainEventPublisher;

class PhotoController extends GlobalController
{
		public function preDispatch() {
			// I've made a small attempt to make sure user writeable parameters
			// are not used for filenames/sql commands unless they are part of 
			// an approved range of values.
			if(	$this->_getParam('type') == 'project'
				|| $this->_getParam('type') == 'object'
				|| $this->_getParam('type') == 'user'
				|| $this->_getParam('type') == 'objectgroup') {
				
				$this->view->type = $this->_getParam('type');
			}
			
			if (is_numeric($this->_getParam('map_to')))
				$this->view->map_to = $this->_getParam('map_to');
		}

		/**
		 * List
		 * @ACL
		 */
		public function listAction() {
			$this->view->headScriptHashed()->appendFile('media/javascript/slideshow.js');

			$p = new Photos();
			
			$select = $p->select()
							->where('map_to = ?', $this->view->map_to)
							->where('type = ?', $this->view->type)
							->order('order ASC');
			
			$this->view->rights = $this->_getParam('rights') ;
			$this->view->photos = $p->fetchAll($select);
		}

        public function fileListAction()
        {
            $this->disableView();

            $select = db()->select()
                ->from('photos')
                ->where('map_to = ?', $this->getParam('map_to'))
                ->where('type = ?', $this->getParam('type'))
                ->order('order ASC');

            echo json_encode(db()->fetchAll($select));
		}

		/**
		 * Add
		 * @ACL
		 */
		public function addAction() {
			$addPhotoForm = new Form($this, 'addPhoto');
            $categoryChoices = $this->makeCategoryChoices();
            $addPhotoForm->category->setMultiOptions($categoryChoices);
			$this->view->categoryTitles = $this->getCategoryTitles();

			if ($this->getRequest()->isPost() && $addPhotoForm->isValid($this->getRequest()->getPost())) {
				$formData = $addPhotoForm->getValues();
				$photosData = json_decode($formData['photo'], true);
				$photosModel = new Photos();

				foreach ($photosData as $photoData) {
					$photoRow = $photosModel->createRow();
					$photoRow->type		= $this->view->type;
					$photoRow->map_to		= $this->view->map_to;
					$photoRow->filename 	= $photoData['filename'];
					$photoRow->title 		= $photoData['title'];
					$photoRow->category 	= $formData['category'];
					$photoRow->titleid 	= $formData['categoryTitle'];

					$nextOrderNumber = (new PDOPhotoRepository())->getNextOrderNumber(
                        $this->view->type,
                        $this->view->map_to,
                        $formData['category']
                    );

                    $photoRow->order = $nextOrderNumber;
					$photoRow->save();

                    if ($this->view->type == 'project' || $this->view->type == 'objectgroup') {
                        DomainEventPublisher::instance()->publish(new PhotoUploaded($photoRow->toArray()));
                    }
				}
				
				if ($this->view->type == 'project') {
                    $projectModel = new Projects();
                    $projectRow = $projectModel->getById($this->view->map_to);
                    $projectRow->update_at = (new \DateTime())->format('Y-m-d H:i:s');
                    $projectRow->save();

					$this->_helper->redirector('details', 'project', false, ['id' => $this->view->map_to]);

				} else if ($this->view->type == 'object') {
					$this->_helper->redirector('show', 'support', false, ['type' => $this->view->type, 'id' => $this->view->map_to]);
				} else if ($this->view->type == 'objectgroup') {
					$this->_helper->redirector('show', 'support', false, ['type' => $this->view->type, 'id' => $this->view->map_to]);
				} else if ($this->view->type == 'user') {
					$this->_helper->redirector('show', 'support', false, ['type' => $this->view->type, 'id' => $this->view->map_to]);
				} else {
					p("when trying to add a new photo \'type\' was not set, quitting...");
					die;
				}
			}
			
			$this->view->photo = $addPhotoForm;
		}

    /**
     * Delete
     * @ACL
     */
    public function deleteAction()
    {
        $this->disableView();
        $photoId = $this->_getParam('id');

        $photosModel = new Photos();
        $photosRow = $photosModel->getById($photoId);

        switch ($photosRow->type) {
            case 'project':
                $projectModel = new Projects();
                $projectRow = $projectModel->getById($photosRow->map_to);
                $projectRow->update_at = (new \DateTime())->format('Y-m-d H:i:s');
                $projectRow->save();
                break;
        }

        $this->commandBus()->handle(new DeletePhotoRequest($photoId));
    }

		/**
		 * Upload iframe
		 * @ACL
		 */
		public function uploadAction() {

			if ($this->getRequest()->isPost()) {
				if ($this->_getParam('delete')) {
					unset($_SESSION['photos']);
					@unlink($file['temp']);
				}

				if($_FILES['photo']){
					$files = array();
					foreach($_FILES['photo'] as $file_var_name => $file_var_value)
						foreach($file_var_value as $file_id => $file_value)
							$files[$file_id][$file_var_name] = $file_value;

					if (count($files) > 0) {

						$files_data = array();

						foreach($files as $file){
							$file['random'] = mt_rand();
							$file['temp'] = '_cache/photos/' . $file['random'];

							createFolder($file['temp'], 0777, true);
							chmod('_cache/photos/', 0777);

							move_uploaded_file($file['tmp_name'], $file['temp']);

							$files_data[] = $file;

							$_SESSION['photos'][$file['random']] = $file;
						}

						$this->view->file = $files_data;
					}
				}
				
			} elseif (is_numeric($this->_getParam('photo'))) {
				$this->view->file = $_SESSION['photos'][$this->_getParam('photo')];
				
			} elseif ($this->_getParam('photo')) {
				$this->view->file = array(
					'name' => $this->_getParam('photo')
				);
			}
			
			$this->_helper->layout->disableLayout();
		}

    public function directUploadAction()
    {
        $this->disableView();

        ini_set('post_max_size', '10M');
        ini_set('upload_max_filesize', '10M');

        $type = $this->getParam('type');
        $map_to = $this->getParam('map_to');
        $category = $this->getParam('category');

        $photoRow = $this->commandBus()->handle(new UploadPhotoRequest($type, $map_to, $category));

        echo json_encode($photoRow->toArray());
    }
		
		/**
		 * Called when the user reorders photo's in the overview.
		 */
		public function orderAction() {
			$this->disableView();

			if (!is_numeric($this->_getParam('id'))) {
                die();
            }

			$post = $this->getRequest()->getPost();

            $photoIdsInOrderSequence = explode(',', $post['order']);
			$photoModel = new Photos();

			foreach($photoIdsInOrderSequence as $orderNumber => $photoId){
                $photoRow = $photoModel->fetchRowById($photoId);

                if (null === $photoRow) {
                    continue;
                }

                $oldPhotoOrderPosition = $photoRow->order;

				$photoRow->order = $orderNumber;
				$photoRow->save();

                DomainEventPublisher::instance()->publish(new PhotoOrderChanged(
                    $oldPhotoOrderPosition,
                    $photoRow->toArray()
                ));
			}
		}
	
    public function saveOrderAction()
    {
        $this->disableView();

        $data = $this->_getParam('data', []);
        $data = json_decode($data, true);

        $this->commandBus()->handle(new ChangeOrderRequest($data));
    }
	
		/**
		 * Get all the sub-categories that photos can be placed in and that belong in the current pages type.
		 */
		public function getCategoryTitles(){
			$categories = array();
			$select = db()->select()
				->from(array('ppct'=>'photo_categories_titles'), array('*'))
				->joinLeft(array('ppc'=>'photo_categories'), 'ppct.photo_category = ppc.id', array('type' => 'ppc.type'))
				->order('ppct.title');
			
			
			foreach(db()->fetchAll($select) as $item) {
				if($item['type'] == $this->view->type) {
					$categories[$item['photo_category']][] = $item;
				}
			}
			
			return $categories;
		}

    private function makeCategoryChoices()
    {
        $db = Zend_Db_Table::getDefaultAdapter();

        $select = $db->select()
            ->from(array('ppc'=>'photo_categories'), array('id' => 'id', 'category' => 'category'))
            ->where('ppc.type = \''.$this->view->type.'\'')
            ->order('ppc.category');

        $categoriesData =  $db->fetchPairs($select);

        $choices['items'][0] = "Maak je keuze";

        foreach ($categoriesData as $id => $category) {
            $choices['items'][$id] = $category;
        }
        return $choices;
    }
}
