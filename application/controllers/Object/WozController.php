<?php


class Object_WozController extends GlobalController
{
    public function listAction(){
        $this->view->Breadcrumbs()
            ->addCrumb('WOZ waarde overzicht');

        $isExportListview =
            !is_null($this->getParam('view_template')) &&
            $this->getParam('view_template') !== 'list';

        $listDataService = new \Controllers\Object\Woz\ListDataService();

        if ($isExportListview) {
            $data = $listDataService->getExportListviewData();
            $yearColumns = $listDataService->getYearColumnsFromData($data);
            $this->createExportListView($data, $yearColumns);
        } else {
            $data = $listDataService->getListData();
            $this->createListView($data);
        }
    }

    private function createListView($data){

        $this->view->listView($data)

            ->setTypes([
                'project' => [
                    'title' => 'Project',
                    'width' => 'large truncate',
                ],
                'objectgroup' => [
                    'title' => 'Objectgroep',
                    'width' => 'hidden',
                ],
                'object' => [
                    'title' => 'Object',
                    'width' => 'xxxlarge truncate',
                ],
                'object_woz_previous_year' => [
                    'title' => 'WOZ waarde ' . (date('Y') - 1),
                    'width' => 'medium right object_woz_previous_year',
                ],
                'object_woz' => [
                    'title' => 'WOZ waarde ' . date('Y'),
                    'width' => 'medium right object_woz',
                ],
                'object_woz_next_year' => [
                    'title' => 'WOZ waarde ' . (date('Y') + 1),
                    'width' => 'medium right object_woz_next_year',
                ],
            ])

            ->addFormat('project', function($value, $item){
                return $value > 0 ? '<a href="project/details/id/' . $value . '/">' . $item['project_name'] . '</a>' : '-';
            })

            ->addFormat('objectgroup', function($value, $item){
                return $value > 0 ? $item['objectgroup_name']: '-';
            })

            ->addFormat('object', function($value, $item){
                return $value > 0 ? '<a href="object/edit/id/' . $value . '/">' . $item['object_address'] . '</a>' : '-';
            })

            ->addFormat(['object_woz', 'object_woz_previous_year',  'object_woz_next_year'], 'money_input_rounded')

            ->setOptions([
                'item_title' => 'WOZ waarde',
                'paginator' => false,
            ])

            ->setFilters([
                'project' => ['title' => 'Project', 'renderSeparately' => true],
                'objectgroup' => ['title' => 'Objectgroep', 'renderSeparately' => true],
                'object' => ['title' => 'Type', 'renderSeparately' => true],
            ])

            ->render($this);
    }

    private function createExportListView($data, $yearColumns){
        $types = [
            'project' => [
                'title' => 'Project',
                'width' => 'large truncate',
            ],
            'objectgroup' => [
                'title' => 'Objectgroep',
                'width' => 'hidden',
            ],
            'object' => [
                'title' => 'Object',
                'width' => 'xxxlarge truncate',
            ],
        ];

        foreach ($yearColumns as $year => $yearColumn) {
            $types[$yearColumn] = [
                'title' => 'WOZ waarde ' . $year,
                'width' => 'medium right',
            ];
        }

        $this->view->listView($data)

            ->setTypes($types)

            ->addFormat('project', function($value, $item){
                return $value > 0 ? '<a href="project/details/id/' . $value . '/">' . $item['project_name'] . '</a>' : '-';
            })

            ->addFormat('objectgroup', function($value, $item){
                return $value > 0 ? $item['objectgroup_name']: '-';
            })

            ->addFormat('object', function($value, $item){
                return $value > 0 ? '<a href="object/edit/id/' . $value . '/">' . $item['object_address'] . '</a>' : '-';
            })

            ->addFormat(array_values($yearColumns), 'moneyRounded')

            ->setOptions([
                'item_title' => 'WOZ waarde',
                'paginator' => false,
            ])

            ->setFilters([
                'project' => ['title' => 'Project', 'renderSeparately' => true],
                'objectgroup' => ['title' => 'Objectgroep', 'renderSeparately' => true],
                'object' => ['title' => 'Type', 'renderSeparately' => true],
            ])

            ->render($this);
    }

    public function saveAction()
    {
        $this->disableView();

        $obj_woz_model = new ObjectsWoz();

        if (!is_null($this->getParam('object_woz_previous_year'))) {
            $year = date('Y') - 1;
        } elseif (!is_null($this->getParam('object_woz_next_year'))) {
            $year = date('Y') + 1;
        } else {
            $year = date('Y');
        }

        $woz_attributes = [
            'type' => $this->getParam('type'),
            'map_to' => $this->getParam('map_to'),
            'year' => $year
        ];

        $woz_row = $obj_woz_model->matchRow($woz_attributes);

        $woz_row = $woz_row ?: $obj_woz_model->createRow($woz_attributes);


        if($object_woz_previous_year = $this->getParam('object_woz_previous_year')){
            $woz_row->value = str_replace('.', '', str_replace(',', '.',
                ((string) new StringFormat($object_woz_previous_year, 'money_db'))/100
            ));
            $woz_row->save();
        }

        if($object_woz_next_year = $this->getParam('object_woz_next_year')){
            $woz_row->value = str_replace('.', '', str_replace(',', '.',
                ((string) new StringFormat($object_woz_next_year, 'money_db'))/100
            ));
            $woz_row->save();
        }

        if($object_woz = $this->getParam('object_woz')){
            $woz_row->value = str_replace('.', '', str_replace(',', '.',
                ((string) new StringFormat($object_woz, 'money_db'))/100
            ));
            $woz_row->save();
        }

    }
}
