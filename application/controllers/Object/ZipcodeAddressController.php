<?php

use Object\ZipcodeAddress\ZipcodeAddressService;

class Object_ZipcodeAddressController extends GlobalController
{

    public function indexAction()
    {
        $this->disableView();
        $zipcodeValue = $this->getParam('zipcode');
        $houseNumberValue = $this->getParam('houseNumber');
        $houseNumberAdditionValue = $this->getParam('houseNumberAddition');

        $bagApiClient = new \Object\ZipcodeAddress\Bag(null);

        $zipcodeAddress = new \Controllers\Object\ZipcodeAddress(
            new Cache_ZipcodeAddress(),
            new ZipcodeAddressService($bagApiClient),
            new \Object\ZipcodeAddress\FilterResultsByHouseNumberService()

        );

        $results = $zipcodeAddress->search($zipcodeValue, $houseNumberValue, $houseNumberAdditionValue);
        echo json_encode($results);
    }

}
