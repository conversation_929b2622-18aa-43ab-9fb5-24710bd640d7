<?
	class PortalController extends GlobalController {



        public function settingsAction(){

            $this->view->headScriptHashed()->appendFile('media/javascript/image-upload.js');

            $this->view->Breadcrumbs()
                ->addCrumb('Instellingen', 'settings')
                ->addCrumb('Portal instellingen bewerken');

            $this->view->EditView(
                $this,
                'portal/settings',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }


        public function settingsTenantAction(){

            $this->view->Breadcrumbs()
                ->addCrumb('Instellingen', 'settings')
                ->addCrumb('Huurder portal instellingen bewerken');

            $this->view->EditView(
                $this,
                'portal/settings-tenant',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }


        public function settingsInvestorAction(){

            $this->view->Breadcrumbs()
                ->addCrumb('Instellingen', 'settings')
                ->addCrumb('Belegger portal instellingen bewerken');

            $this->view->EditView(
                $this,
                'portal/settings-investor',
                [
                    'modeloverride' => 'Settings',
                ]
            )
                ->render($this);
        }


        public function loginBackgroundAction(){

            $this->view->Breadcrumbs()
                ->addCrumb('Instellingen', 'settings')
                ->addCrumb('Portal achtergronden instelling');

            $this->view->EditView(
                $this,
                'portal/loginbackgrounds',
                [
                    'modeloverride' => 'Settings',
                    'fullwidth' => true
                ]
            )
                ->render($this);
        }

	}
