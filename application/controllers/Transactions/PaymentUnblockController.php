<?php

use Transactions\Payment\UnblockPaymentRequest;
use Transactions\Payment\UnblockPaymentService;
use Transactions\Payment\ViewPaymentUnblockService;

class Transactions_PaymentUnblockController extends GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Betalingen deblokkeren');

        $this->view->extraWidth = true;

        $types = [
            'iban' => ['title' => 'Tegenrekening', 'width' => 'xxxlarge'],
            'type' => ['title' => 'Type', 'width' => 'medium'],
            'date' => ['title' => 'Datum', 'width' => 'medium'],
            'matched' => ['title' => 'Ongematched', 'width' => 'medium'],
            'amount' => ['title' => 'Totaalbedrag', 'width' => 'medium'],
            'description' => ['title' => 'Omschrijving', 'width' => 'xxxxlarge multiline']
        ];

        $filters = [
            'iban' => ['type' => 'input'],
            'type' => [],
            'date' => ['type' => 'input'],
        ];

        $service = new ViewPaymentUnblockService();
        $data = $service->execute();

        /**
         * @var $listView Zend_View_Helper_ListView
         */
        $listView = $this->view->ListView($data);

        $listView->addLegend('list-legend');
        $listView->setTypes($types);

        $listView->addFormat('description', function ($value) {
            $value_parts = [];
            foreach (explode(' ', $value) as $value_part) {
                if (strlen($value_part) > 5) {
                    $value_split_parts = str_split($value_part, 5);

                    $value_part = '';
                    foreach ($value_split_parts as $value_split_part) {
                        $value_part .= $value_split_part . "&#8203;";
                    }
                }

                $value_parts[] = $value_part;
            }

            $value = implode(' ', $value_parts);

            return '<span noHintButton="true" class="forceAutoHint description" style="font-size: 10px; display: inline-block; width:100%;" title="' . nl2br($value) . '">' . $value . '</span>';
        });

        $listView->addFormat('iban', 'iban');
        $listView->addFormat('matched', 'money');
        $listView->addFormat('amount', 'money');

        $actions = [
            'buttons' => [
                ['title' => 'Betaling deblokkeren', 'icon' => 'simular_cross', 'params' => ['month_offset', 'project'], 'action' => 'transactions_payment-unblock/unblock', 'confirm' => true, 'confirm_message' => 'Het deblokkeren van deze betaling kan achterliggende consequenties hebben, gaat u hier mee akkoord?']
            ]
        ];

        $listView->addActions($actions);
        $listView->setFilters($filters);
        $listView->render($this);
    }

    public function unblockAction()
    {
        $this->disableView();

        $transactionImportPaymentIds = $this->getParam('ids');

        if ($transactionImportPaymentIds) {
            $service = new UnblockPaymentService();

            foreach ($transactionImportPaymentIds as $transactionImportPaymentId) {
                $service->execute(new UnblockPaymentRequest($transactionImportPaymentId));
            }
        }
    }
}
