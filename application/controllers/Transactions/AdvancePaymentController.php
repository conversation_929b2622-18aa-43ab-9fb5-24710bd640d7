<?php

class Transactions_AdvancePaymentController extends GlobalController
{
    public function addAction()
    {
        $this->disableView();

        $transactionsImportPaymentId = $this->getParam('tip_id');
        $userId = $this->getParam('user_id');
        $viewHash = $this->getParam('hash');

        $tip_model = new TransactionsImportPayments();
        $currentTIPRow = $tip_model->getUnmatched($transactionsImportPaymentId);

        $service = new \Transactions\Match\HashService();
        $currentHash = $service->execute(
            new \Transactions\Match\HashRequest(
                $currentTIPRow['id'],
                $currentTIPRow['unmatched'],
                $currentTIPRow['deleted'],
                $currentTIPRow['type'],
                $currentTIPRow['date'],
                $currentTIPRow['amount'],
                $currentTIPRow['description'],
                $currentTIPRow['possible_duplicate']
            )
        );

        if ($currentTIPRow == false) {
            $response = [
                'status' => 'failure',
                'type' => 'unmatched_not_found',
                'data' => []
            ];
        } else if ($viewHash !== $currentHash) {
            $response = [
                'status' => 'failure',
                'type' => 'hash_conflict',
                'data' => [
                    'new_hash' => $currentHash,
                    'unmatched_amount' => [
                        'remains' => (string)new StringFormat($currentTIPRow['unmatched'], 'money'),
                        'id' => 0
                    ]
                ]
            ];
        } else {
            $service = new \Transactions\Payment\AdvancePaymentService();
            $service->execute(new \Transactions\Payment\AdvancePaymentRequest(
                $transactionsImportPaymentId,
                $userId
            ));

            $response = [
                'status' => 'success',
            ];
        }

        echo Zend_Json::encode($response);
    }
}