<?php

class Transactions_ImportFilesController extends GlobalController
{

    public function listAction()
    {
        $this->view->extraWidth = true;

        $this->view->Breadcrumbs()->addCrumb('Ingelezen bankbestanden');

        if (is_null($this->_getParam('sorting'))) {
            $this->setParam('sorting', json_encode(['created_at' => 'DESC']));
        }

        $listActionService = new \Transactions\Import\Files\ListActionService();
        $listData = $listActionService->execute();

        /**
         * @var $listView Zend_View_Helper_ListView
         */
        $listView = $this->view->ListView($listData);

        $listView->setTypes([
            'corporations' => ['title' => '', 'width' => 'hidden'],
            'bankaccounts' => ['title' => '', 'width' => 'hidden'],
            'created_at' => ['title' => 'Toegevoegd op', 'width' => 'xlarge', 'order_using_raw' => true],
            'user_id' => ['title' => 'Door', 'width' => 'large truncate'],
            'source_filename' => ['title' => 'Bestandsnaam', 'width' => 'xxxlarge truncate'],
            'parser' => ['title' => 'Type', 'width' => 'xxsmall'],
            'status' => ['title' => 'Status', 'width' => 'small'],
            'statements' => ['title' => 'Bankafschrift(en) binnen bestand', 'width' => 'xxxxlarge statements'],
        ]);

        $listView->addFormat('statements', function($value){
            if(!is_array($value)){
                return '-';
            }

            $previousLastEntryDateValue = $value['previous_last_entry_date'] ?: $value['transactions_first_entry_date'];

            if(!$value['corporation_name']){
                $value['corporation_name'] = 'Bankrekening of entiteit onbekend';
            }

            return implode('<span class="statementSeperator">|</span>', [
                $value['bankaccount'],
                $value['corporation_name'],
                $value['number_of_transactions'] .
                ' regels van ' .
                date('d-m-Y', strtotime($value['transactions_first_entry_date'])) .
                ' t/m ' .
                date('d-m-Y', strtotime($value['transactions_last_entry_date'])),
                'Ingelezen: ' . $value['number_of_new_transactions'] .
                ' vanaf ' .
                date('d-m-Y', strtotime($previousLastEntryDateValue))
            ]);
        });

        $listView->addFormat('status', function ($value) {
            $statusDescriptions = [
                'file_submitted' => 'Bestand toegevoegd',
                'invalid_file_extension' => 'Ongeldige bestandsextentie',
                'uploaded' => 'Bestand geupload',
                'file_not_found' => 'Fout bij upload',
                'worker_started' => 'Verwerking gestart',
                'completed' => 'Inlezen afgerond'
            ];

            return isset($statusDescriptions[$value]) ? $statusDescriptions[$value] : '-';
        });

        $listView->addFormat('parser', function($value){
            return $value ? strtoupper($value) : 'Onbekend';
        });

        $listView->addFormat('created_at', function ($value) {
            return date('d-m-Y  H:i:s', strtotime($value));
        });

        $listView->addFormat('user_id', function ($value, $item) {
            if (!$value) {
                return '-';
            }

            return '<a href="/user/admin-edit/id/' . $value . '/">' . $item['user_name'] . '</a>';
        });

        $listView->addFormat('corporation_id', function ($value, $item) {
            if (!$value) {
                return '-';
            }

            return '<a href="/corporation/edit/id/' . $value . '/">' . $item['corporation_name'] . '</a>';
        });

        $listView->addFormat('source_filename', function ($value, $item) {

            return '<a href="/transactions_import-files/download/source_filename_id/' . $item['id'] . '">' . $item['source_filename'] . '</a>';
        });


        $listView->addFormat('bankaccount', 'iban');

        $listView->addFormat(['transactions_first_entry_date', 'previous_last_entry_date'], 'date');

        $listView->setFilters([
            'corporations' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Juridische entiteit'],
            'bankaccounts' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Bankrekening'],
            'source_filename' => ['type' => 'input'],
            'parser' => [],
            'user_id' => [],
            'created_at' => ['type' => 'date_range'],
            'status' => [],
        ]);

        $listView->addLegend('list-legend');

        $listView->render($this);
    }


    public function downloadAction()
    {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $sourceFilenameId = $this->getParam('source_filename_id');
        $transactionsImportFilesRow = (new TransactionsImportFiles())->matchRow(['id' => $sourceFilenameId]);

        header('Content-type: application/mt940');
        header('Content-Disposition: inline; filename="'. $transactionsImportFilesRow['source_filename'] .'"');
        echo file_get_contents($transactionsImportFilesRow['uploaded_filename']);
    }

}
