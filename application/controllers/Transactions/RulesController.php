<?php

class Transactions_RulesController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Betalingen match regels', 'transactions_rules/');
    }

    public function indexAction()
    {
        $select = db()->select()
            ->from('transactions_rules', '*')
            ->joinLeft('transactions_rules_conditions', 'transactions_rules_conditions.transactions_rules_id = transactions_rules.id AND type = "description"', ['condition'] )
            ->where('deleted = ?', false)
            ->group('transactions_rules.id')
            ;

        $data = db()->fetchAll($select);
        
        /**
         * @var $listView Zend_View_Helper_ListView
         */
        $this->view->ListView($data)
            ->setTypes([
                'name' => ['title' => 'Naam', 'width' => 'xxxxlarge'],
                'condition' => ['title' => 'Referentie', 'width' => 'xxlarge'],
            ])
            ->setFilters([
                'name' => ['type' => 'input','title' => 'Naam', 'order_by_value' == 'ASC'],
                'condition' => ['type' => 'input',  'title' => 'Referentie', 'order_by_value' == 'ASC'],
            ])
            ->setOptions([
                'item_title' => 'Match regel'
            ])

            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
                'delete' => 'Verwijderen'
            ])

            ->render($this);
    }

    public function editAction()
    {
        $this->view->headScriptHashed()
            ->appendFile('media/javascript/emailGroup.js')
            ->appendFile('media/javascript/multiSelectFilter.js');

        $this->view->headLink()->appendStylesheet('media/style/emailgroup.css');
        $this->view->headLink()->appendStylesheet('media/style/userlist.css');

        $this->view->Breadcrumbs()->addCrumb('Bewerken / toevoegen');


        $this->view->EditView($this, 'TransactionsRules')
            ->setOptions([])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $model = new TransactionsRules();

        $model
            ->getById($this->getParam('id'))
            ->setFromArray(['deleted' => true])
            ->save();

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }
}
