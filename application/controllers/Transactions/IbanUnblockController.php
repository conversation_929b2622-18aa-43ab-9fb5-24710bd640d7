<?php

use Transactions\Iban\UnblockIbanRequest;
use Transactions\Iban\UnblockIbanService;
use Transactions\Iban\ViewIbanUnblockService;

class Transactions_IbanUnblockController extends GlobalController
{
    public function listAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Iban deblokkeren');

        $types = [
            'iban' => ['title' => 'Iban', 'width' => 'xxxlarge'],
            'bankaccount' => ['title' => 'Bankrekening', 'width' => 'xxxlarge']
        ];

        $filters = [
            'bankaccount' => ['type' => 'input'],
            'iban' => ['type' => 'input']
        ];

        $service = new ViewIbanUnblockService();
        $data = $service->execute();

        /**
         * @var $listView Zend_View_Helper_ListView
         */
        $listView = $this->view->ListView($data);

        $listView->addLegend('list-legend');
        $listView->setTypes($types);

        $listView->addFormat('iban', 'iban');
        $listView->addFormat('bankaccount', 'bank');

        $actions = [
            'buttons' => [
                ['title' => 'Iban deblokkeren', 'icon' => 'simular_cross', 'params' => ['month_offset', 'project'], 'action' => 'transactions_iban-unblock/unblock', 'confirm' => true]
            ]
        ];

        $listView->addActions($actions);
        $listView->setFilters($filters);
        $listView->render($this);
    }

    public function unblockAction()
    {
        $this->disableView();

        $transactionsImportPaymentsIds = $this->getParam('ids');

        if ($transactionsImportPaymentsIds) {

            $service = new UnblockIbanService();

            foreach ($transactionsImportPaymentsIds as $transactionsImportPaymentsId) {
                $service->execute(new UnblockIbanRequest($transactionsImportPaymentsId));
            }
        }
    }
}