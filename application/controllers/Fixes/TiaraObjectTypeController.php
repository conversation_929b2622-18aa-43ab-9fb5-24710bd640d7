<?php

use DbTable\TiaraEntity;
use DbTable\TiaraFormState;
use DbTable\TiaraQueue;

class Fixes_TiaraObjectTypeController extends GlobalController
{

//    public function createAction()
//    {
//        $projectId = $this->getParam('project_id');
//
//        try {
//            $this->executeFixTiaraProject($projectId);
//        } catch (\Exception $exception) {
//            print $exception->getMessage();
//        } finally {
//            print 'done';
//        }
//    }


    private function executeFixTiaraProject($projectId)
    {
        $projectRow = $this->findProjectById($projectId);
        $objectGroupsRows = $this->findObjectGroupByProjectId($projectId);
        $tiaraEntityProject = $this->findTiaraEntityByProjectId($projectId);

        if (!$tiaraEntityProject) {
            return;
        }

        foreach ($objectGroupsRows as $objectGroupsRow) {
            if (!$objectGroupsRow['tiara_id']) {
                continue;
            }

            $tiaraEntityId = $this->createTiaraEntity($tiaraEntityProject['project_id'], $objectGroupsRow['tiara_id'], $objectGroupsRow['id']);

            $tiaraQueueId = $this->createTiaraQueueByTiaraEntity($tiaraEntityId, 'AanmeldenMedia', $panentTiaraQueueId = null);
            $this->createTiaraQueueByTiaraEntity($tiaraEntityId, 'OpvragenResultaatBerichten', $tiaraQueueId);
        }


    }

    private function findProjectById($objectId)
    {
        $select = db()->select()
            ->from(['p' => 'projects'])
            ->where('p.id = ?', $objectId);

        return db()->fetchRow($select);
    }

    private function createTiaraEntity($tiaraProjectId, $tiaraObjectTypeId, $objectGroupRowId)
    {
        $tiaraEntityNewRow = (new TiaraEntity())->createRow();
        $tiaraEntityNewRow->setFromArray([
            'omniboxx_entity_id' => $objectGroupRowId,
            'object_id' => null,
            'project_id' => $tiaraProjectId,
            'object_type_id' => $tiaraObjectTypeId,
            'object_guid' => NULL,
            'project_guid' => NULL,
            'object_type_guid' => NULL,
            'media_guid' => NULL,
            'type' => 'object_type',
            'post_data' => NULL,
            'xml' => '<?xml version = "1.0" encoding = "UTF-8"?><result></result>',
            'state' => 'added',
            'medium_order_number' => NULL,
            're_sync' => '0',
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return $tiaraEntityNewRow->save();
    }

    private function findTiaraEntityByExternalTiaraId($externalTiaraId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.object_type_id = ?', $externalTiaraId);

        return db()->fetchRow($select);
    }

    private function findTiaraEntityProjectByGuid($guid)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.project_guid = ?', $guid);

        return db()->fetchRow($select);
    }

    private function findTiaraEntityByObjectGroupId($objectGroupId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.`type` = ?', 'object_type')
            ->where('te.omniboxx_entity_id = ?', $objectGroupId);

        $dataRow = db()->fetchRow($select);
        return (new TiaraEntity())->getById($dataRow['id']);
    }

    private function findObjectGroupByProjectId($projectId)
    {
        $select = db()->select()
            ->from(['og' => 'objectgroup'])
            ->where('og.project = ?', $projectId);

        return db()->fetchAll($select);
    }


    private function findTiaraEntityByExternalTiaraIdNotRemoved($externalTiaraId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'], ['maxId' => 'MAX(id)'])
            ->where('te.`type` = ?', 'object_type')
            ->where('te.object_type_id = ?', $externalTiaraId);

        $data = db()->fetchRow($select);
        if (!$data['maxId']) {
            return;
        }

        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.id = ?', $data['maxId']);

        return db()->fetchRow($select);
    }

    private function findTiaraEntityByProjectId($projectId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.`type` = ?', 'object_type')
            ->where('te.omniboxx_entity_id = ?', $projectId);

        $dataRow = db()->fetchRow($select);
        return (new TiaraEntity())->getById($dataRow['id']);
    }

    private function findTiaraEntityById($projectId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.`type` = ?', 'object_type')
            ->where('te.id = ?', $projectId);

        $dataRow = db()->fetchRow($select);
        return (new TiaraEntity())->getById($dataRow['id']);
    }

    private function createTiaraQueueByTiaraEntity($tiaraEntityId, $callType, $panentTiaraQueueId = null)
    {
        $TiaraQueueNewRow = (new TiaraQueue())->createRow();
        $TiaraQueueNewRow->setFromArray([
            'tiara_entity_id' => $tiaraEntityId,
            'linked_tiara_queue_id' => $panentTiaraQueueId,
            'mutation_id' => '',
            'request_xml' => '',
            'response_xml' => '',
            'call_type' => $callType,
            'state' => 'processed',
            'error_message' => null,
            'error_trace' => null,
            'attempt_count' => 0,
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return $TiaraQueueNewRow->save();
    }


    /**
     * http://dev.omniboxx.johan.loc/fixes_tiara-object-type/update-from-csv
     */
    public function updateFromCsvAction()
    {

        $vestegingNrList = [
            70004,
            70008,
            70010,
            70011,
            70041,
            70043,
            70044,
            70088,
            70097,
            70122,
            70123,
            70124,
            70172,
            70177,
            70208,
            70232,
            71023
        ];


        foreach ($vestegingNrList as $vestegingNr) {
            error_log('Start import object type for vestegingNr: ' . $vestegingNr);
            try {

                $this->importByCsv(sprintf('%s/_cache/tiara/%s/objectypes_%s.csv',
                    $_SERVER['CONTEXT_DOCUMENT_ROOT'],
                    $vestegingNr,
                    $vestegingNr
                ));
            } catch (\Exception $exception) {
                error_log($exception->getMessage());
                error_log($exception->getTraceAsString());
            }
        }

        die('end of live');
    }

    private function importByCsv($filePath)
    {
        $CsvReader = new CsvReader($filePath, ',');

        while ($csvRow = $CsvReader->getRow()) {
            $externalTiaraId = $csvRow['tiara id'];
            if (!trim($externalTiaraId) || '' == $externalTiaraId) {
                continue;
            }

            $tiaraEntitRow = $this->findTiaraEntityByExternalTiaraIdNotRemoved($externalTiaraId);
            if (!$tiaraEntitRow) {
                error_log('Tiara item is not found: ' . $externalTiaraId);
                continue;
            }

            if ('removed' == $tiaraEntitRow['state']) {
                error_log('Tiara item is removed: ' . $externalTiaraId);
                continue;
            }

            $this->updateTiaraEntity($tiaraEntitRow['id'], $csvRow);

            $this->updateTiaraPostData($tiaraEntitRow['id'], $csvRow);

        }
    }

    private function updateTiaraPostData($tiaraEntitId, $data)
    {
        $tiaraEntitRow = $this->findTiaraEntityById($tiaraEntitId, 'object_type');
        $tiaraFormStateRow = $this->findTiaraFormStateByOmniboxxIdAndType($tiaraEntitRow->omniboxx_entity_id, 'object_type');
        if (!$tiaraFormStateRow) {
            $this->createTiaraFormState($tiaraEntitRow->omniboxx_entity_id, 'object_type');
            $tiaraFormStateRow = $this->findTiaraFormStateByOmniboxxIdAndType($tiaraEntitRow->omniboxx_entity_id, 'object_type');
        }

        $projectTiaraEntity = $this->findTiaraEntityProjectByGuid($data['project guid']);

        if (!$projectTiaraEntity) {
            error_log('PojectTiaraEntity not found for objectTypeTiaraEntity: '. $tiaraEntitId);
        }

        $postData = (array)json_decode($tiaraFormStateRow->json_post_data, true);

        if (!$postData['ObjectType']) {
            $postData['ObjectType'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetails']) {
            $postData['ObjectTypeObjectTypeDetails'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsBestemmingRecreatie']) {
            $postData['ObjectTypeWonenWonenDetailsBestemmingRecreatie'] = [];
        }
        if (!$postData['ObjectTypeWonenVerdiepingenBeganeGrondOfFlat']) {
            $postData['ObjectTypeWonenVerdiepingenBeganeGrondOfFlat'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsFinancieleGegevensHuurprijs']) {
            $postData['ObjectTypeObjectTypeDetailsFinancieleGegevensHuurprijs'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsMatenBrutoInhoud']) {
            $postData['ObjectTypeObjectTypeDetailsMatenBrutoInhoud'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsMatenWoonoppervlakte']) {
            $postData['ObjectTypeObjectTypeDetailsMatenWoonoppervlakte'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsMatenPerceeloppervlakte']) {
            $postData['ObjectTypeObjectTypeDetailsMatenPerceeloppervlakte'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsPresentatie']) {
            $postData['ObjectTypeObjectTypeDetailsPresentatie'] = [];
        }
        if (!$postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing']) {
            $postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing'] = [];
        }
//        if (!$postData['ObjectTypeWonenSwitch']) {
//            $postData['ObjectTypeWonenSwitch'] = [];
//        }
//        if (!$postData['ObjectTypeObjectTypeDetailsFinancieleGegevensSwitch']) {
//            $postData['ObjectTypeObjectTypeDetailsFinancieleGegevensSwitch'] = [];
//        }
        if (!$postData['ObjectTypeWonenWonenDetails']) {
            $postData['ObjectTypeWonenWonenDetails'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsBestemming']) {
            $postData['ObjectTypeWonenWonenDetailsBestemming'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsSchuurBerging']) {
            $postData['ObjectTypeWonenWonenDetailsSchuurBerging'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsTuin']) {
            $postData['ObjectTypeWonenWonenDetailsTuin'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsDiversenDak']) {
            $postData['ObjectTypeWonenWonenDetailsDiversenDak'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsInstallatie']) {
            $postData['ObjectTypeWonenWonenDetailsInstallatie'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsInstallatieCVKetel']) {
            $postData['ObjectTypeWonenWonenDetailsInstallatieCVKetel'] = [];
        }
        if (!$postData['ObjectTypeWonenWonenDetailsGarage']) {
            $postData['ObjectTypeWonenWonenDetailsGarage'] = [];
        }
        if (!$postData['ObjectTypeWonenVerdiepingenZolder']) {
            $postData['ObjectTypeWonenVerdiepingenZolder'] = [];
        }
        if (!$postData['ObjectTypeWonenVerdiepingenVliering']) {
            $postData['ObjectTypeWonenVerdiepingenVliering'] = [];
        }
        if (!$postData['ObjectTypeWonenAppartement']) {
            $postData['ObjectTypeWonenAppartement'] = [];
        }
        if (!$postData['ObjectTypeWonenAppartementVVEKenmerken']) {
            $postData['ObjectTypeWonenAppartementVVEKenmerken'] = [];
        }
        if (!$postData['ObjectTypeWonenAppartementVVEKenmerken']) {
            $postData['ObjectTypeWonenAppartementVVEKenmerken'] = [];
        }


        $postData['ObjectTypeWonenSwitch'] = 'ObjectTypeWonen' . ucfirst($data['object soort']);
        $postData['ObjectTypeObjectTypeDetailsFinancieleGegevensSwitch'] = '';

        $postData['ObjectType']['NVMVestigingNR'] = $data['vestigingsnummer'];
        $postData['ObjectType']['ProjectID'] = $projectTiaraEntity['project_id'];
        $postData['ObjectType']['ObjectTypeID'] = $data['tiara id'];

        $postData['ObjectTypeObjectTypeDetails']['Valuta'] = 'EUR';
        $postData['ObjectTypeObjectTypeDetails']['Naam'] = $data['naam'];
        $postData['ObjectTypeObjectTypeDetails']['DatumInvoer'] = date('d-m-Y', strtotime($data['aanmelddatum']));
        $postData['ObjectTypeObjectTypeDetails']['KoopHuur'] = 'huur';
        $postData['ObjectTypeObjectTypeDetails']['AantalEenheden'] = $data['aantal eenheden'];
        $postData['ObjectTypeObjectTypeDetails']['AantalVrijeEenheden'] = $data['aantal vrije eenheden'];
        $postData['ObjectTypeObjectTypeDetails']['DatumStartBouw'] = '' != $data['start bouw'] ? date('d-m-Y', strtotime($data['start bouw'])) : '';
        $postData['ObjectTypeObjectTypeDetails']['DatumOpleveringVanaf'] = '' != $data['oplevering vanaf'] ? date('d-m-Y', strtotime($data['oplevering vanaf'])) : '';
//        $postData['ObjectTypeObjectTypeDetails']['Inschrijfvoorwaarden'] = $data[''];
//        $postData['ObjectTypeObjectTypeDetails']['Wachttijd'] = $data[''];


//        $postData['ObjectTypeWonenWonenDetailsBestemmingRecreatie']['Recreatiewoning'] = 'nee';
//        $postData['ObjectTypeWonenWonenDetailsBestemmingRecreatie'][''] = '';

//        $postData['ObjectTypeWonenVerdiepingenBeganeGrondOfFlat']['VerdiepingNr'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenBeganeGrondOfFlat']['Naam'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenBeganeGrondOfFlat']['AantalKamers'] = $data[''];

        $postData['ObjectTypeObjectTypeDetailsFinancieleGegevensHuurprijs']['Van'] = $data['huurprijs van'];
        $postData['ObjectTypeObjectTypeDetailsFinancieleGegevensHuurprijs']['TotEnMet'] = $data['huurprijs tot'];

        $postData['ObjectTypeObjectTypeDetailsMatenBrutoInhoud']['Van'] = $data['bruto inhoud van'];
        $postData['ObjectTypeObjectTypeDetailsMatenBrutoInhoud']['TotEnMet'] = $data['bruto inhoud tot'];

        $postData['ObjectTypeObjectTypeDetailsMatenWoonoppervlakte']['Van'] = $data['bruto inhoud van'];
        $postData['ObjectTypeObjectTypeDetailsMatenWoonoppervlakte']['TotEnMet'] = $data['bruto inhoud tot'];

//        $postData['ObjectTypeObjectTypeDetailsMatenPerceeloppervlakte']['Van'] = $data[''];
//        $postData['ObjectTypeObjectTypeDetailsMatenPerceeloppervlakte']['TotEnMet'] = $data[''];

        $postData['ObjectTypeObjectTypeDetailsPresentatie']['Aanbiedingstekst'] = $data['naam'];

//        $postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing'][0]['Plaatsing'] = $data[''];
//        $postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing'][0]['Prijsvermelding'] = $data[''];
//        $postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing'][0]['DatumVrijgave'] = $data[''];
//        $postData['ObjectTypeObjectTypeDetailsPresentatieInternetplaatsing'][0]['DatumEind'] = $data[''];

//        $postData['ObjectTypeWonenWonenDetails']['Praktijkruimte'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsBestemming']['PermanenteBewoning'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsSchuurBerging']['Soort'] = $data[''];
//        $postData['ObjectTypeWonenWonenDetailsSchuurBerging']['TotaalAantal'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsTuin']['Positie'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsDiversenDak']['Type'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsInstallatie']['SoortenVerwarming'] = $data[''];
//        $postData['ObjectTypeWonenWonenDetailsInstallatie']['SoortenWarmWater'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsInstallatieCVKetel']['Brandstof'] = $data[''];
//        $postData['ObjectTypeWonenWonenDetailsInstallatieCVKetel']['Eigendom'] = $data[''];
//        $postData['ObjectTypeWonenWonenDetailsInstallatieCVKetel']['Combiketel'] = $data[''];
//
//        $postData['ObjectTypeWonenWonenDetailsGarage']['Capaciteit'] = $data[''];
//        $postData['ObjectTypeWonenWonenDetailsGarage']['TotaalAantalGarages'] = $data[''];
//
//        $postData['ObjectTypeWonenVerdiepingenZolder']['VerdiepingNr'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenZolder']['Naam'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenZolder']['Omschrijving'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenZolder']['AantalKamers'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenZolder']['AantalSlaapkamers'] = $data[''];
//
//        $postData['ObjectTypeWonenVerdiepingenVliering']['VerdiepingNr'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenVliering']['Naam'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenVliering']['Omschrijving'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenVliering']['AantalKamers'] = $data[''];
//        $postData['ObjectTypeWonenVerdiepingenVliering']['AantalSlaapkamers'] = $data[''];
//
//        $postData['ObjectTypeWonenAppartement']['SoortAppartement'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['KenmerkAppartement'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['KwaliteitAppartement'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['OpenPortiek'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['Woonlaag'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['AantalWoonlagen'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['VVEChecklistAanwezig'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['Woningtypecode'] = $data[''];
//        $postData['ObjectTypeWonenAppartement']['BijdrageVVE'] = $data[''];
//
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['InschrijvingKvK'] = $data[''];
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['VergaderingVVE'] = $data[''];
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['PeriodiekeBijdrage'] = $data[''];
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['Reservefonds'] = $data[''];
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['Onderhoudsverwachting'] = $data[''];
//        $postData['ObjectTypeWonenAppartementVVEKenmerken']['Opstalverzekering'] = $data[''];












//        foreach ($postData as $formName => $formValues) {
//            foreach ($formValues as $inputName => $inputValue) {
//                if (strpos($formName, 'Wij')) {
//                    $postData[$formName][$inputName] = $inputValue;
//                } else {
//                    $postData['Wij' . $formName][$inputName] = $inputValue;
//                }
//
//            }
//        }


        $tiaraFormStateRow->json_post_data = json_encode($postData);

        $tiaraFormStateRow->save();
    }


    private function updateTiaraEntity($tiaraEntitId, $data)
    {
        $tiaraEntitRow = $this->findTiaraEntityById($tiaraEntitId);

        $tiaraEntitRow->project_guid = $data['project guid'];
        $tiaraEntitRow->object_type_guid = $data['object type guid'];

        $tiaraEntitRow->save();
    }


    private function findTiaraFormStateByOmniboxxIdAndType($omniboxxEntityId, $omniboxxEntityType)
    {
        $select = db()->select()
            ->from(['tfs' => 'tiara_form_state'])
            ->where('tfs.omniboxx_entity_id = ?', $omniboxxEntityId)
            ->where('tfs.omniboxx_entity_type = ?', $omniboxxEntityType);

        $dataRow = db()->fetchRow($select);
        if (!$dataRow) {
            return;
        }

        return (new TiaraFormState())->getById($dataRow['id']);
    }

    private function createTiaraFormState($id, $type)
    {
        $TiaraFormState = (new TiaraFormState())->createRow();
        $TiaraFormState->setFromArray([
            'omniboxx_entity_id' => $id,
            'omniboxx_entity_type' => $type,
            'json_post_data' => json_encode([])
        ]);

        return $TiaraFormState->save();
    }
}
