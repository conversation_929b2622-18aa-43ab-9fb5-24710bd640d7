<?php


use DbTable\TiaraEntity;

class Fixes_TiaraController extends \GlobalController
{

    public function preDispatch()
    {
        $this->disableView();
    }

    /**
     * http://dev2.omniboxx.johan.loc/fixes_tiara/unsubscribe-objects-by-project/project_id/1537
     * @return void
     */
    public function unsubscribeObjectsByProjectAction()
    {
        $projectId = $this->getParam('project_id');




        $tiaraEntityProjectRows = db()->select()
            ->from(['te' => 'tiara_entity'], '*')
            ->where('te.`type` = ?', TiaraEntity::TYPE_PROJECT)
            ->where('te.omniboxx_entity_id = ?', $projectId)
            ->query()
            ->fetchAll();

        if (!isset($tiaraEntityProjectRows[0])) {
            die('no data');
        }

        $tiaraEntityObjectTypeRows = db()->select()
            ->from(['te' => 'tiara_entity'], '*')
            ->where('te.`type` = ?', TiaraEntity::TYPE_OBJECT_TYPE)
            ->where('te.project_id = ?', $tiaraEntityProjectRows[0]['project_id'])
            ->query()
            ->fetchAll();

        foreach ($tiaraEntityObjectTypeRows as $tiaraEntityObjectTypeRow) {
            $tiaraEntityObjectRows = db()->select()
                ->from(['te' => 'tiara_entity'], '*')
                ->where('te.`type` = ?', TiaraEntity::TYPE_OBJECT)
                ->where('te.object_type_id = ?', $tiaraEntityObjectTypeRow['object_type_id'])
                ->query()
                ->fetchAll();

            foreach($tiaraEntityObjectRows as $tiaraEntityObjectRow) {
                try {
                    (new \TiaraWonen\Application\RemoveTiaraByRevokeObjectService())->execute($tiaraEntityObjectRow['omniboxx_entity_id']);
                } catch (\Exception $exception) {
                    error_log($exception->getMessage());
                }
            }
        }

    }

    /**
     * http://dev2.omniboxx.johan.loc/fixes_tiara/unsubscribe-object-types-by-project/project_id/1537
     * @return void
     * @throws Exception
     */
    public function unsubscribeObjectTypesByProjectAction()
    {
//        $projectId = $this->getParam('project_id');

        $projectIds = [369,871,752,156,1149,2409,1185,1528,1535,1522,1512,1533,1513,584,136,1565,2399,2456,966,52,353,1530,75,57,1442,349,1113,12,9,65,15,62,13,28,11,967,1400,1277,54,1521,877,1524,1511,1539,1538,935,1645,1245,347,183,360,344,373,355,375,357,352,378,146,385,1372,1456,1640,1428,1435,1637,1322,1476,1318,1390,1632,1398,345,343,354,1609,1464,1477,1426,1441,1112,1614,1612,1618,1459,1385,1147,1150,1474,1375,1430,1191,376,342,381,705,1242,1494,1334,1365,1144,346,1111,1117,946,948,947,955,1268,60,942,931,949,957,1625,1437,1125,1434,1427,1636,1635,1423,1345,1389,1438,10,71,1439,1399,160,1361,940,380,1061,384,1611,1319,387,1630,383,1273,1324,1613,1621,1455,356,1542,1514,1475,73,1624,1605,575,14,838,1604,340,1311,1603,149,1396,918,926,1037,1374,1393,924,1507,1508,923,1119,921,1386,1601,852,644,597,592,832,819,587,586,661,657,849,656,817,813,842,841,834,811,846,833,850,848,851,847,831,336,1421,920,815,814,818,828,827,843,829,822,826,807,1419,331,1597,1596,1563,1106,50,804,1155,1294,1502,914,912,1038,1039,317,319,1415,1582,323,1590,1580,1588,1589,1570,1591,1585,324,1156,326,327,325,1050,1296,1044,1593,41,1247,911,799,1504,913,1417,1416,658,790,1258,44,42,1204,1414,906,1574,1264,771,202,321,789,201,786,316,784,1036,907,302,287,1370,1094,1369,315,1098,782,1368,239,40,1367,314,1495,1493,1575,1576,904,313,1571,1057,1230,39,776,765,898,1363,702,774,33,1028,1030,306,773,1032,896,309,895,178,310,304,1413,1031,1298,762,1232,756,755,758,725,759,1360,1409,757,754,753,1473,1412,890,1411,1350,1352,880,881,872,883,886,879,281,285,1551,288,745,1224,1027,730,729,1307,728,726,1026,1025,1024,1022,1021,1020,1019,296,294,1016,295,1013,1012,291,290,293,292,289,1009,1008,1007,981,982,1003,724,721,720,719,716,1557,1559,1558,712,711,1216,1554,284,985,1303,1553,649,279,265,588,651,867,264,581,263,260,259,262,261,692,691,1210,659,606,660,591,173,1099,1071,179,1073,1081,1078,1074,1075,228,1072,237,1215,1069,1128,240,1093,1548,1547,229,223,275,138,190,979,225,187,272,269,233,200,1546,188,1545,242,647,1085,1541,244,212,167,164,182,213,168,181,227,217,208,196,195,192,143,191,1293,650,1067,1536,1291,978,961,1529,234,1288,977,1519,863,1384,1523,219,218,177,134,576,209,1381,1188,582,1520,140,148,185,176,175,1084,165,161,158,150,861,144,2470,2398,686,666,2492,2428,2413,1167,1180,1178,968,1262,1362,2505,2359,1198,1107,1162,1199,1172,1357,1261,1348,1269,1260,1248,1217,1355,1240,1286,1287,2553,2552,2556,2563,2387,2557,2562,2554,2551,2565,2564,962];

        foreach ($projectIds as $projectId) {
            $rows = db()->select()
                ->from(['p' => 'projects'], false)
                ->joinLeft(['og' => 'objectgroup'], 'og.project = p.id', ['id'])
                ->joinLeft(['te' => 'tiara_entity'], 'te.omniboxx_entity_id = og.id', false)
                ->where('te.`type` = ?', TiaraEntity::TYPE_OBJECT_TYPE)
                ->where('te.`state` != ?', TiaraEntity::STATE_REMOVED)
                ->where('p.id = ?', $projectId)
                ->group('og.id')
                ->query()
                ->fetchAll();

            foreach ($rows as $row) {
                (new \TiaraWonen\Application\RemoveTiaraByRevokeObjectTyprService())->execute($row['id']);
            }
        }
    }

    /**
     * http://dev2.omniboxx.johan.loc/fixes_tiara/unsubscribe-project/project_id/1537
     * @return void
     * @throws Exception
     */
    public function unsubscribeProjectAction()
    {

        $projectId = $this->getParam('project_id');

        $rows = db()->select()
            ->from(['p' => 'projects'], ['id'])
            ->joinLeft(['te' => 'tiara_entity'], 'te.omniboxx_entity_id = p.id', false)
            ->where('te.`type` = ?', TiaraEntity::TYPE_PROJECT)
            ->where('te.`state` != ?', TiaraEntity::STATE_REMOVED)
            ->where('p.id = ?', $projectId)
            ->group('p.id')
            ->query()
            ->fetchAll();

        foreach($rows as $row) {
            (new \TiaraWonen\Application\RemoveTiaraByRevokeProjectService())->execute($row['id']);
        }
    }


    /**
     * http://dev2.omniboxx.johan.loc/fixes_tiara/subscribe-objects-by-project/project_id/1537
     * @return void
     */
    public function subscribeObjectsByProjectAction()
    {
        $projectId = $this->getParam('project_id');


        $rows = db()->select()
            ->from(['p' => 'projects'], false)
            ->joinLeft(['og' => 'objectgroup'], 'og.project = p.id', false)
            ->joinLeft(['o' => 'objects'], 'o.objectgroup  = og.id', ['id'])
            ->joinLeft(['op' => 'object_publish'], 'op.object_id  = o.id', false)
            ->where('p.id = ?', $projectId)
            ->where('op.enabled = ?', 1)
            ->where('op.publish_funda = ?', 1)
            ->group('o.id')
            ->query()
            ->fetchAll();

        foreach($rows as $row) {
            (new \TiaraWonen\Application\RegisterObjectByTiaraService())->execute($row['id']);
        }
    }

    /**
     *
     * http://dev2.omniboxx.johan.loc/fixes_tiara/test_connection
     *
     * @return void
     */
    public function testConnectionAction()
    {
        $connectionInfo = new \TiaraWonen\V2\Service\TiaraApiClient();

        var_dump($connectionInfo);

    }

}
