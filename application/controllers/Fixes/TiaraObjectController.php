<?php


use DbTable\TiaraEntity;
use Tiara<PERSON>onen\Application\RemoveTiaraByRevokeObjectService;
use TiaraWonen\Application\Service\Request\ZipCodeCheckService;

class Fixes_TiaraObjectController extends \GlobalController
{

    public function preDispatch()
    {
        $this->disableView();
    }

    /**
     * @Inject
     * @var \TiaraWonen\Application\RemoveTiaraByRevokeObjectService
     */
    private $removeTiaraByRevokeObjectService;

    /**
     * http://dev.omniboxx.johan.loc/fixes_tiara-object/unsubscribe-objects-from-archive-projects
     *
     */
    public function unsubscribeObjectsFromArchiveProjectsAction()
    {
        $archiveObjectsToUnsubscribe = db()->select()
            ->from(['o' => 'objects'], [
                    'object_id' => 'id',
                    'object_address' => 'rendered_address',
                ]
            )
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
            ->joinLeft(['p' => 'projects'], 'p.id = og.project',
                [
                    'project_id' => 'id',
                    'project_name' => 'name',
                ]
            )
            ->joinLeft(['te' => 'tiara_entity'], 'te.omniboxx_entity_id = o.id AND te.`type` = "object"', [
                'tiara_object_id' => 'object_id',
                'tiara_state' => 'state',
                'tiara_created_at' => 'created_at',
            ])
            ->where('p.sales_status = ?', 'Exploitation_ended')
            ->where('te.state != ?', 'removed')
            ->query()
            ->fetchAll();

        echo "<pre>";

        foreach ($archiveObjectsToUnsubscribe as $item) {
            try {
                $this->removeTiaraByRevokeObjectService->execute($item['object_id']);
            } catch (\Exception $exception) {
                var_dump($item);
                var_dump($exception);
            }
        }
    }


    /**
     * http://dev.omniboxx.johan.loc/fixes_tiara-object/update-nvm-vestiging-number-by-tiara-form-state
     *
     * @return void
     */
    public function updateNvmVestigingNumberByTiaraFormStateAction()
    {
        $tiaraFormStateRows = db()->select()
            ->from(['tfs' => 'tiara_form_state'], '*')
            ->query()
            ->fetchAll();

        foreach ( $tiaraFormStateRows as  $tiaraFormStateRow) {
            $jsonPostData = json_decode($tiaraFormStateRow['json_post_data']);
            $NVMVestigingNR = $this->findNvmVestigingNumberInJson($jsonPostData);

            if (!$NVMVestigingNR) {
                continue;
            }

            $tiaraEntityModel = new \DbTable\TiaraEntity();
            $states = [TiaraEntity::STATE_NEW, TiaraEntity::STATE_REMOVED];

            $tiaraEntityRows = $tiaraEntityModel->fetchAll([
                'type = ?' => TiaraEntity::TYPE_OBJECT,
                'state NOT IN(' . implode_for_where_in($states) . ')',
                'omniboxx_entity_id = ?' =>   $tiaraFormStateRow['omniboxx_entity_id']
            ]);

            foreach ($tiaraEntityRows as $tiaraEntityRow) {
                var_dump([$tiaraEntityRow->id, $tiaraEntityRow->vestiging_nr, $NVMVestigingNR]);
                $tiaraEntityRowArr = $tiaraEntityRow->toArray();
                $tiaraEntityRowArr['vestiging_nr'] = $NVMVestigingNR;
                $tiaraEntityModel->save([$tiaraEntityRowArr], ['id' =>  $tiaraEntityRow->id]);
            }
        }
    }

    private function findNvmVestigingNumberInJson($jsonData)
    {
        foreach ($jsonData as $key => $jsonItem) {
            if ('NVMVestigingNR' == $key) {
                return $jsonItem;
            }
            return $this->findNvmVestigingNumberInJson($jsonItem);
        }
    }


    /**
     * http://dev4.omniboxx.johan.loc/fixes_tiara-object/zipcode
     * @return void
     */
    public function zipcodeAction()
    {

        $response = (new ZipCodeCheckService())->execute(
            '3641EB',
            '95',
            ''
        );

        var_dump($response);
    }
}
