<?php

class Fixes_InvoiceController extends GlobalController
{
    public function markInvoicesAsPayedAction()
    {
        $this->disableView();

        $filterFiscalDateEnd = $this->getParam('filterFiscalDateEnd');
        $filterFiscalDateEnd = $filterFiscalDateEnd ? new DateTime($filterFiscalDateEnd) : null;

        $setFinancialNoSync = $this->getParam('setFinancialNoSync') === '1';
        $payedDate = $this->getParam('payedDate');
        $payedDate = new DateTime($payedDate);

        $findFinalizedInvoicesService = new \Invoice\FindFinalizedInvoicesWithInvoiceDateRangeService();

        $markAsPayedService = new \Invoice\MarkAsPayedService(
            new Transactions(),
            new TransactionsPayments()
        );

        $setFinancialNoSyncService = new \Invoice\SetFinancialNoSyncService(new Invoices());

        $invoiceIds = $findFinalizedInvoicesService->execute(null, $filterFiscalDateEnd);

        foreach ($invoiceIds as $invoiceId) {
            $markAsPayedService->execute($invoiceId, $payedDate);

            if ($setFinancialNoSync) {
                $setFinancialNoSyncService->execute($invoiceId);
            }
        }

    }

    /**
     * @throws Exception
     */
    public function resumeEmailSendingAction()
    {
        $this->disableView();

        $runId = $this->getParam('runId');

        if(!$runId){
            throw new InvalidArgumentException('Missing argument: runId');
        }

        $invoiceRunLibrary = new InvoiceRun();

        $select = db()->select()
            ->from(['i' => 'invoices'], ['id'])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run')
            ->where('i.process_status = ?', 'finalizable')
            ->where('ir.status != ?', 0)
            ->where('i.run = ?', $runId);

        $invoices = db()->fetchCol($select);

        /* the first one is probably the invoice that got E-mailed but where the process died, dont double send this one */
        array_shift($invoices);

        foreach ($invoices as $invoiceId) {
            try {
                $invoiceRunLibrary->getEmailList($runId, $invoiceId);
            } catch (Exception $exception){
                error_log($exception->getMessage());
            }
        }
    }

    public function fixBrokenInvoiceIdentifiersAction()
    {
        $this->disableView();

        $select = db()->select()
            ->distinct()
            ->from(['ir' => 'invoices_run'], ['id'])
            ->joinLeft(['i' => 'invoices'], 'i.run = ir.id', false)
            ->where('ir.type = ?', 'advance')
            ->where('ir.corporation_id IS NULL')
            ->where('ir.status = ?', '2')
            ->where('ir.date >= ?', '2022-09-01')
            ->where('i.financial_invoice_id IS NULL')
            ->where('i.financial_no_sync = ?', false);

        $invoicesModel = new Invoices();

        foreach (db()->fetchCol($select) as $runId) {

            $invoiceLibrary = new Invoice();

            $select = db()->select()
                ->from(['ir' => 'invoices_run'], [
                    'start',
                    'ir_identifier' => 'identifier',
                    'corporation' => 'COALESCE(ir.corporation_id, p.corporation)',
                ])
                ->joinLeft(['p' => 'projects'], 'p.id = ir.project', false)
                ->where('ir.id = ?', $runId);

            $run_info = db()->fetchRow($select);

            $invoiceLibrary->corporation = $run_info['corporation'];
            p($invoiceLibrary->corporation);

            $invoicesModelRowsSelect = $invoicesModel->select()
                ->from('invoices')
                ->where('run = ?', $runId)
                ->order('id ASC');

            $invoicesModelRows = $invoicesModel->fetchAll($invoicesModelRowsSelect);
            foreach ($invoicesModelRows as $invoicesModelRow) {
                $invoicesModelRow->identifier = $invoiceLibrary->addIdentifier(date('Y',
                    strtotime($run_info['start'])));
                $invoicesModelRow->save();
                p($run_info['ir_identifier'] . '.' . $invoicesModelRow->identifier);
            }
        }
    }

    public function removeInvalidThirteenthMonthRunsAction()
    {
        $this->disableView();

        $select = db()->select()
            ->from(['ir' => 'invoices_run'], ['id'])
            ->where('ir.status = ?', 0)
            ->where('ir.type = ?', 'advance')
            ->where('ir.periodvalue = ?', 13)
            ->where('ir.period = ?', 'monthly');

        $invalidRuns = db()->fetchCol($select);

        if (count($invalidRuns) === 0) {
            return;
        }

        (new InvoiceRun())->remove($invalidRuns);
    }

    public function createCollectionForPurchaseInvoiceAction()
    {
        $this->disableView();
        $invoiceCustomId = $this->getParam('id');

        if (!$invoiceCustomId) {
            throw new InvalidArgumentException('Missing argument "id"');
        }

        $invoiceCustomLib = new InvoiceCustom();
        $invoiceCustomLib->is_purchase = true;

        $customInvoiceData = $invoiceCustomLib->get($invoiceCustomId);

        if (!$customInvoiceData) {
            throw new InvalidArgumentException('No purchase invoice found for id ' . $invoiceCustomId);
        }

        $invoiceCustomLib->customClieopPurchase($customInvoiceData, $customInvoiceData['invoice_id']);
    }
}
