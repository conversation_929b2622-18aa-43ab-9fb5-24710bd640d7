<?php


use EmailImport\Domain\Model\NewEmail\NewEmail;
use EmailImport\Domain\Model\NewEmail\NewEmailFactory;
use EmailImport\Domain\Service\EmailMessageFinderService;
use EmailImport\Domain\Service\ImportNewEmailService;
use EmailImport\Infrastructure\Domain\Model\EmailAccount\ZendEmailAccountRepository;

class Fixes_EmailController extends GlobalController
{

    public function __construct(Zend_Controller_Request_Abstract $request, Zend_Controller_Response_Abstract $response, array $invokeArgs = array())
    {
        parent::__construct($request, $response, $invokeArgs);
        $this->disableView();
    }

    /**
     *
     * http://support.omniboxx.johan.loc/fixes_email/fix
     *
     * @return void
     */
    public function startSendEmailWorkerAction()
    {
        $this->disableView();

        startWorker('process-queue-worker', 'cronjob', 'direct');
        echo 'Process is add to te queue';
    }

    public function fixAction()
    {

        /**
         * This is a temp function to fix email te a importen wrong
         */

//        $select = (new Emails())->select()
//            ->where('direction = ?', 'incoming')
////            ->where('archived_date IS NULL', 'incoming')
//            ->where('date_created > ?', '2025-08-13 12:00:00')
//            ->where('date_created < ?', '2025-08-13 14:00:00')
//        ;
        $select = (new Emails())->select()
            ->where('direction = ?', 'incoming')
//            ->where('archived_date IS NULL', 'incoming')
//            ->where('date_created > ?', '2025-08-13 12:00:00')
//            ->where('date_created < ?', '2025-08-13 14:00:00')
//        ->limit(10)
        ->limit( 50, 400)
            ->order('id DESC')
        ;

        $emailModelRows = (new Emails())->fetchAll($select);

        echo 'emails to update'. $emailModelRows->count(). '</br>';
        foreach ($emailModelRows as $emailModelRow) {
            echo '<a href="/email/show-email-body/email-id/'.$emailModelRow->id.'">'. $emailModelRow->subject  .' </a><br/>';
            try {
                $this->reSyncMessageFromImap($emailModelRow->id);
            } catch (\Exception $exception) {
                error_log($exception->getMessage() .' email_id: '. $emailModelRow->id .' :: '. $exception->getTraceAsString());

            }

        }

    }


    /**
     *
     * http://support.omniboxx.johan.loc/fixes_email/re-sync-message-from_imap/email_id/75280
     *
     * @return void
     */
    public function reSyncMessageFromImapAction()
    {

        $emailId = $this->getParam('email_id');
        if (!$emailId || !is_numeric($emailId)) {
            echo "Neet email id";
        }

        try {
            $this->reSyncMessageFromImap($emailId);
        } catch (\Exception $exception) {
            p($exception->getMessage() . " emmailId: ". $emailId);
        }

    }

    /**
     * @Inject
     * @var \EmailImport\Application\Service\FindEmailMessageService
     */
    private $findEmailMessageService;

    public function reSyncMessageFromImap($emailId)
    {
        if (!$emailId || !is_numeric($emailId)) {
            echo "Neet email id";
        }

        $emailModelRow = (new Emails())->getById($emailId);

        if (!$emailModelRow->id) {
            throw new Exception('No email fount by id:'. $emailId);
        }

        $emailAccountId = $emailModelRow->account;
        $messageId = $emailModelRow->message_id;
        $messageId = trim($messageId, '<>');

        /**
         * @var NewEmail
         */
        $newEmail = $this->findEmailMessageService->execute($emailAccountId, $messageId);

        if (!$newEmail) {
            throw new Exception('no email');
        }

//        $html = $newEmail->getContent();
//        libxml_use_internal_errors(true);
//        $doc = new DOMDocument();
//        $result = $doc->loadHTML($html);
//        $errors = libxml_get_errors();
//        libxml_clear_errors();
//
//        if ($result) {
//            echo "HTML is parseable.\n";
//        } else {
//            echo "HTML failed to parse.\n";
//        }
//
//        if ($errors) {
//            file_put_contents('email_id.log', $emailModelRow->id. "\n", FILE_APPEND);
//            file_put_contents('email_id.log', 'http://support.omniboxx.johan.loc/email/show-email-body/email-id/'.$emailModelRow->id. "\n", FILE_APPEND);
//            echo "Warnings/Errors:\n";
//            foreach ($errors as $error) {
//
//                switch (true) {
//                    case strpos($error->message, 'Comment not terminated') !== false:
//                    case strpos($error->message, 'Unexpected end of file') !== false:
////                    case strpos($error->message, 'Entity') !== false:
////                    case strpos($error->message, 'AttValue:') !== false:
////                    case strpos($error->message, 'Opening and ending tag mismatch') !== false:
//                        file_put_contents('email_id.log', '###############################################'. "\n", FILE_APPEND);
//                        break;
//                }
//
//
//                echo trim($error->message) . "\n";
//                file_put_contents('email_id.log', trim($error->message) . "\n" , FILE_APPEND);
//            }
//        }


        $emailModelRow->message = $newEmail->getContent();
        $emailModelRow->save();
    }

}
