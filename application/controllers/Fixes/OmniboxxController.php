<?php

/**
 * This fixes controller contains scripts used by- and for our own purposes
 */

class Fixes_OmniboxxController extends GlobalController
{
    public function sendActiveUsersEmailAddressesToAdminsAction(){
        $this->disableView();

        $adminEmailAddresses = [
            '<EMAIL>',
        ];

        $service = new \Fixes\Omniboxx\SendActiveUsersEmailAddressesToAdminsService(
            new Acl()
        );

        $service->execute($adminEmailAddresses);
    }

    public function databaseCharsetMigrationAction()
    {
        $this->disableView();

        $mysqlServer = [
            'host' => 'omniboxx_mysql',
            'username' => 'root',
            'password' => 'omniboxx'
        ];

        $rootPdo = new PDO(
            'mysql:host=' . $mysqlServer['host'],
            $mysqlServer['username'],
            $mysqlServer['password']
        );

        $queryString = db()->select()
            ->from(
                'TABLES',
                [
                    'database' => 'TABLE_SCHEMA',
                    'table' => 'TABLE_NAME',
                ],
                'information_schema'
            )
            ->where('TABLE_SCHEMA = ?', db()->getConfig()['dbname'])
            ->where('`TABLE_COLLATION` = ?', 'latin1_swedish_ci');

        $pdoStatement = $rootPdo
            ->prepare($queryString);

        $pdoStatement->execute();

        $tables = $pdoStatement->fetchAll(PDO::FETCH_ASSOC);

        foreach ($tables as $table) {
            $rootPdo->query('USE ' . $table['database']);
            $rootPdo->query('ALTER TABLE ' . $table['table'] . ' CHARACTER SET utf8');
        }

        $queryString = db()->select()
            ->from(
                'COLUMNS',
                [
                    'database' => 'TABLE_SCHEMA',
                    'table' => 'TABLE_NAME',
                    'column' => 'COLUMN_NAME',
                    'type' => 'COLUMN_TYPE',
                    'charset' => 'CHARACTER_SET_NAME',
                ],
                'information_schema'
            )
            ->where('TABLE_SCHEMA = ?', db()->getConfig()['dbname'])
            ->where('!(TABLE_NAME = "email" AND COLUMN_NAME = "message")')
            ->where('(COLUMN_TYPE LIKE "VARCHAR%") OR (COLUMN_TYPE LIKE "%CHAR%")') // OR (COLUMN_TYPE LIKE "%TEXT%")
        ;

        $pdoStatement = $rootPdo
            ->prepare($queryString);

        $pdoStatement->execute();

        $columns = $pdoStatement->fetchAll(PDO::FETCH_ASSOC);

        foreach ($columns as $column) {
            $rootPdo->query('USE ' . $column['database']);

            $select = db()->select()
                ->from($column['table'], ['id'])
                ->where('(LENGTH(`' . $column['column'] . '`) - CHAR_LENGTH(`' . $column['column'] . '`)) > 1');

            // TO DO -> NOGMAALS DE LENGTE VAN LENGTH() EN CHAR_LENGTH() CONTROLEREN MET DE CONVERT -> OM TE ZIEN OF DIT HET GEWENSTE EFFECT GAAT HEBBEN
            
            $pdoStatement = $rootPdo->prepare($select);
            $pdoStatement->execute();

            $convertCandidates = $pdoStatement->fetchAll(PDO::FETCH_COLUMN);

            if (count($convertCandidates) === 0) {
                continue;
            }

            $updateQuery =
                'UPDATE `' . $column['table'] . '` ' .
                'SET `' . $column['column'] . '` = convert(cast(convert(`' . $column['column'] . '` using  latin1) as binary) using utf8) ' .
                'WHERE `id` IN (' . implode(",", $convertCandidates) . ')';

            $rootPdo->query($updateQuery);

            p([
                $column['table'],
                $column['column'],
                count($convertCandidates)
            ]);
        }
    }
}
