<?php

class Fixes_IndexationController extends \GlobalController
{
    public function testCommercialIndexationAction()
    {
        $this->disableView();

        $userObjectId = $this->getParam('userObjectId');

        $indexationDataSelect = db()->select()
            ->from(['uo' => 'users_objects'], ['id', 'indexation_date', 'object'])
            ->joinLeft(['o' => 'objects'], 'o.id = uo.object', false)
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['projectId' => 'id'])
            ->where('uo.id = ?', $userObjectId);

        $indexationData = db()->fetchRow($indexationDataSelect);

        if (!$indexationData) {
            throw new InvalidArgumentException();
        }

        $runDataSelect = db()->select()
            ->from(['ir' => 'invoices_run'], ['id', 'period'])
            ->joinLeft(['i' => 'invoices'], 'i.run = ir.id', false)
            ->where('i.users_objects = ?', $userObjectId)
            ->where('ir.type = ?', 'advance')
            ->order('ir.start DESC');

        $runData = db()->fetchRow($runDataSelect);

        if (!$runData) {
            throw new InvalidArgumentException();
        }

        $invoice = new StdClass();
        $invoice->usersobjects = $userObjectId;
        $invoice->invoicerun = $runData['id'];
        $invoice->period = $runData['period'];
        $invoice->project = $indexationData['projectId'];


        (new Indexes())->indexCommercialObject(
            date('m', strtotime($indexationData['indexation_date'])),
            $indexationData['object'],
            $indexationData['indexation_date'],
            false,
            $invoice
        );

    }

    public function bazaarIndexationAction()
    {
        $this->disableView();

        $indexationDate = '2023-07-19';

        $indexationRates = [
            1 => 5,
            2 => 8.5,
            3 => 6
        ];

        $objectIdSelect = db()->select()
            ->from(['o' => 'objects'], ['id'])
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
            ->where('og.project = ?', 1);

        $objectIds = db()->fetchCol($objectIdSelect);

        $version_select = db()->select()
            ->from('objects_components_versions', ['object', 'versionId' => 'id'])
            ->where('object IN (' . implode_for_where_in($objectIds) . ')')
            ->where('concept = ?', false)
            ->where('source != "indexation" OR YEAR(`date`) < ?', date('Y'))
            ->order('date ASC')
            ->order('number ASC');

        $versionIds = db()->fetchPairs($version_select);

        $valuesSelect = db()->select()
            ->from(['ocval' => 'objects_components_values'])
            ->joinLeft(['c' => 'components'], 'c.id = ocval.component', null)
            ->where('ABS(ocval.value_excl) > 0')
            ->where('ocval.version IN (?)', $versionIds)
            ->where('c.id IS NOT NULL')
            ->order('ocval.order ASC');

        $values = db()->fetchAll($valuesSelect);

        foreach ($values as $value) {
            $ocVersionModel = new ObjectsComponentsVersions();
            $ocValuesModel = new ObjectsComponentsValues();

            $ocVersionModelRowData = [
                'object' => $value['object'],
                'date' => $indexationDate,
            ];

            $ocVersionModelRow = $ocVersionModel->matchRow($ocVersionModelRowData + ['source' => 'indexation']);

            if (!$ocVersionModelRow) {
                $ocVersionModelRow = $ocVersionModel->add($ocVersionModelRowData, 'indexation');
            }

            $ocValuesModelRowData = [
                'object' => $value['object'],
                'version' => $ocVersionModelRow->id,
                'component' => $value['component'],
            ];

            $ocValuesModelRow = $ocValuesModel->matchRow($ocValuesModelRowData);

            if (!$ocValuesModelRow) {
                $ocValuesModelRow = $ocValuesModel->createRow($ocValuesModelRowData);
            }

            $ocValuesModelRow->setFromArray([
                'tax_rate' => $value['tax_rate'],
                'vat_percentage' => $value['vat_percentage'],
                'provision' => $value['provision'],
                'investor_payout' => $value['investor_payout'],
                'index' => $value['index'],
                'order' => $value['order'],
            ]);

            if ($value['index'] && isset($indexationRates[$value['index']])) {
                $newValue = $value['value_excl'] * (1 + ($indexationRates[$value['index']] / 100));
                $newValueInCents = \SharedKernel\Domain\Model\Money::makeFromEuroInput($newValue)
                    ->getAmountInCents();

                $newValueIncludingVatInCents = $newValueInCents * (1 + ($ocValuesModelRow->vat_percentage) / 100);
            } else {
                $newValueInCents = \SharedKernel\Domain\Model\Money::makeFromEuroInput($value['value_excl'])->getAmountInCents();
                $newValueIncludingVatInCents = \SharedKernel\Domain\Model\Money::makeFromEuroInput($value['value_incl'])->getAmountInCents();
            }

            $ocValuesModelRow
                ->setFromArray([
                    'value_excl' => $newValueInCents / 100,
                    'value_incl' => round($newValueIncludingVatInCents / 100, 2),
                    'pre_indexation_value_excl' => $value['value_excl'],
                ])
                ->save();;
        }
    }
}
