<?php


use Accounting\Infrastructure\Domain\Model\Corporation\CorporationRepository;
use Accounting\Infrastructure\ExactOnline\Factory\OutstandingInvoice\ExactOnlineConnectionFactory;
use Accounting\Infrastructure\ExactOnline\Gateway\ExactOnlineConnection;
use Accounting\Infrastructure\ExactOnline\Service\RetrieveTransactionByAdministrationIdAndInvoiceNumberService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveThirdPartyOutstandingItemsService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactSyncInvoicePaymentsWithThirdPartyInvoicePaymentsService;

class Fixes_ExactOnlineController extends \GlobalController
{

    /**
     * @Inject
     * @var ExactOnlineConnectionFactory
     */
    private $ExactOnlineConnectionFactory;
    /**
     * @Inject
     * @var ExactRetrieveThirdPartyOutstandingItemsService
     */
    private $ExactRetrieveThirdPartyOutstandingItemsService;/**
     * @Inject
     * @var RetrieveTransactionByAdministrationIdAndInvoiceNumberService
     */
    private $RetrieveTransactionByAdministrationIdAndInvoiceNumberService;

    /**
     * http://support.omniboxx.johan.loc/cronjob/transactions-import-from-financial-export
     *
     * http://support.omniboxx.johan.loc/fixes_exact-online/test-connection
     * @return void
     */
    public function testConnectionAction()
    {
        $this->disableView();
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $corporationRows = (new CorporationRepository(new Corporations()))->findAllFinancialImportTransactionsEnabled();
        foreach ($corporationRows as $corporationRow) {
            p('--------');
            p($corporationRow['id']);
            p($corporationRow['administration']);
            p($corporationRow['name']);

            $tokenKey = 'Fortivest';

            try {
                $exactOnlineConnection = $this->ExactOnlineConnectionFactory->build($corporationRow['administration'], $corporationRow['financial_link_map_to']);
                p($exactOnlineConnection->getDailyLimitRemaining());
                p($exactOnlineConnection->getDailyLimitReset());
                p($exactOnlineConnection->getTokenExpires());

                $data = $this->ExactRetrieveThirdPartyOutstandingItemsService->retrieve($corporationRow['administration']);

                p(count($data));
//                sleep(60);
            } catch (\Exception $exception) {
                p($exception->getMessage());
            }
        }
    }

    /**
     * http://support.omniboxx.johan.loc/fixes_exact-online/outstanding-items
     * @return void
     */
    public function outstandingItemsAction()
    {
        $this->disableView();
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $corporationRows = (new CorporationRepository(new Corporations()))->findAllFinancialImportTransactionsEnabled();

        $tokenKey = 'Fortivest';


        foreach ($corporationRows as $corporationRow) {
            $tokenKey = $corporationRow['financial_link_map_to'];

            $thirdPartyOutstandingItems = [];
            try {
                $exactOnlineConnection = $this->ExactOnlineConnectionFactory->build($corporationRow['administration'], $tokenKey);

                $thirdPartyOutstandingItems = $this->ExactRetrieveThirdPartyOutstandingItemsService->retrieve($corporationRow['administration'], $tokenKey);

//                sleep(60);
            } catch (\Exception $exception) {
                p($exception->getMessage());
            }

            echo '<hr/>';
            echo $corporationRow['administration'];
            echo '<br/>';
            echo $corporationRow['name'];
            echo '<br/>';

            echo '<br/>';

            echo '<table>';

            echo '<tr>';
            echo '<th>AccountCode</th>';
            echo '<th>AccountId</th>';
            echo '<th>AccountName</th>';
            echo '<th>Amount</th>';
            echo '<th>AmountInTransit</th>';
            echo '<th>CurrencyCode</th>';
            echo '<th>Description</th>';
            echo '<th>DueDate</th>';
            echo '<th>EntryNumber</th>';
            echo '<th>HID</th>';
            echo '<th>Id</th>';
            echo '<th>InvoiceDate</th>';
            echo '<th>InvoiceNumber</th>';
            echo '<th>JournalCode</th>';
            echo '<th>JournalDescription</th>';
            echo '<th>YourRef</th>';
            echo '</tr>';

            foreach ($thirdPartyOutstandingItems as $thirdPartyOutstandingItem) {
                echo '<tr>';

                echo '<td>' .$thirdPartyOutstandingItem->getAccountCode(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getAccountId(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getAccountName(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getAmount()->getAmountInCents(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getAmountInTransit()->getAmountInCents(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getCurrencyCode(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getDescription(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getDueDate()->format('Y-m-d'). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getEntryNumber(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getHID(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getId(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getInvoiceDate()->format('Y-m-d'). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getInvoiceNumber(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getJournalCode(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getJournalDescription(). '</td>';
                echo '<td>' .$thirdPartyOutstandingItem->getYourRef(). '</td>';

                echo '</tr>';
            }

            echo '</table>';

            echo '<br/><br/><br/>';
        }

    }

    /**
     *
     * http://dev.omniboxx.johan.loc/fixes_exact-online/check-system-division-by-corporation/corporation_id/67
     *
     * @return void
     */
    public function checkSystemDivisionByCorporationAction()
    {
        $this->disableView();
//        ini_set('max_input_time', 10000);
//        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $corporationId = $this->getParam('corporation_id');

        $corporationRow = (new Corporation())->get($corporationId);

//        $exactAdministrationId = $corporationRow['administration'];
        $tokenKey = $corporationRow['financial_link_map_to'];
//        $tokenKey = 'NIET_LIVE';

        $retrieveSystemDivisionService = new \Accounting\Infrastructure\ExactOnline\Service\RetrieveSystemDivisionService();
        $systemDivisions = $retrieveSystemDivisionService->retrieve($tokenKey);

        /**
         * @var $systemDivision \Accounting\Infrastructure\ExactOnline\Domain\SystemDivision
         */
        foreach ($systemDivisions as $systemDivision) {
            var_dump([
                'HID' => $systemDivision->getHid(),
                'Description' => $systemDivision->getDescription(),
                'Code' => $systemDivision->getCode(),
                'status' => $systemDivision->getStatus(),
                'IsMainDivision' => $systemDivision->getIsMainDivision(),
            ]);
            error_log( $systemDivision->getDescription());
        }
    }
}
