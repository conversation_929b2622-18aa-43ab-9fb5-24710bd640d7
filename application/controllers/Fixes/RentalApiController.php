<?php

class Fixes_RentalApiController extends GlobalController
{
    public function testRegisterLeadAction()
    {
        $this->disableView();

        $apiController = new ApiController(
            $this->getRequest(),
            $this->getResponse(),
            $this->getInvokeArgs()
        );

        $apiController->preDispatch();

        $data = json_decode('{
    "_wpcf7": "485521",
    "_wpcf7_container_post": "46279",
    "_wpcf7_locale": "nl_NL",
    "_wpcf7_posted_data_hash": "",
    "_wpcf7_recaptcha_response": "",
    "_wpcf7_unit_tag": "wpcf7-f485521-p46279-o1",
    "_wpcf7_version": "5.6.2",
    
    
    
    "_wpcf7cf_repeaters": "[]",
    "_wpcf7cf_steps": "{}",
    
    "action": "to_obx",
    "add_partner": "Array",
    "addressnr": "38",
    "addressnradd": "",
    "agreeTerms": "1",
    "bdate": "09-10-1974",
    "branch_location_id": "16",
    "city": "Nijmegen",
    "email": "hansjansen38_' . uniqid() . '@gmail.com",
    "firstname": "Hans",
    "gender": "male",
    "income_amount": "67000",
    "initials": "Cmj",
    "living_situation": "koopwoning (niet verkocht)",
    "middlename": "",
    "min_bedrooms": "2",
    "name": "Kansen",
    "new_construction": "",
    "partner_income_amount": "13000",
    "partner_source_of_income": "pensioen",
    "password": "Groenkazerne.6",
    "payed_account": "1",
    "phone2": "**********",
    "profession": "Overig",
    "profession_specification": "",
    "property_type": "Array",
    "range_price": "",
    "rent_price_include_service_costs": "-1",
    "rent_price_max": "1600",
    "rent_price_min": "800",
    "source_of_income": "pensioen",
    "space_from": "70",
    "street": "Pater Leijdekkersstraat",
    "username": "Hansjansen6_' . uniqid() . '",
    "zip": "6522 MK"
}', true);


        $_POST = $data;

        $apiController->registerLeadAction();
    }

}
