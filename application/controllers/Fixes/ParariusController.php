<?php
//
//namespace Fixes;

//use Pararius\ParariusXmlFactory;

use Pararius\ParariusXmlFactory;

class Fixes_ParariusController extends GlobalController
{

    public function testAction()
    {
        $this->disableView();

        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 60 * 60 * 2); // 2 hours


        $corporationNumber = $this->_getParam('number');

        try {
            $xmlStr = (new ParariusXmlFactory($corporationNumber))->execute();
        } catch (\Exception $exception) {
            var_dump($exception);
//            error_log($exception->getMessage());
            var_dump(libxml_get_errors());

            return;
        }

        //        $export_filename = 'Pararius' . ($this->_getParam('number') ? '_' . $this->_getParam('number') : '') . '.xml';
        $export_filename = 'Pararius.xml';

        ob_clean();
        header('Content-type: text/xml');
        // dit moet weer aan voor download !!!
//        header('Content-disposition: attachment; filename="' . $export_filename . '"');

        echo $xmlStr;
    }
}
