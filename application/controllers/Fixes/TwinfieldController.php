<?php


use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\DetailLineFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\HeaderFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\PurchaseInvoiceFactory as TwinfieldPurchaseInvoiceFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\TotalLineFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\VatLineFactory;

class Fixes_TwinfieldController extends GlobalController
{

    /**
     * http://dev.omniboxx.johan.loc/fixes_twinfield/sync-invoice-transactions/invoiceId/{invoiceId}
     *
     * @return string
     */
    public function syncInvoiceTransactionsAction()
    {
        $this->disableView();

        //********
        $invoiceId = $this->getParam('invoiceId');

        if (!$invoiceId) {
            return 'not invoiceId';
        };

        $twinfield = new \Twinfield();
        try {
            $twinfield->updateInvoicePaymentStatus($invoiceId);
        } catch (\Exception $exception) {
            p($exception->getMessage());
        }
    }


    /**
     * fixes_twinfield/fix-twinfield-imported-purchase-invoices-free-text3
     *
     * @return void
     */
    public function fixTwinfieldImportedPurchaseInvoicesFreeText3Action()
    {
        $administrations = (new \Accounting\Application\Service\RetrieveAdministrationsService())->execute();
//        $fromDate = new DateTimeImmutable('-1 month');
        $fromDate = new DateTimeImmutable('-1 year');

        foreach ($administrations as $financialSystem => $administrationCodes) {
            foreach ($administrationCodes as $administrationCode => $corporationIds) {

                $twinfieldPurchaseInvoices = (new \Accounting\Infrastructure\Twinfield\Service\RetrievePurchaseInvoiceService(
                    new \Accounting\Infrastructure\Twinfield\Gateway\PurchaseInvoiceGateway(),
                    new TwinfieldPurchaseInvoiceFactory(
                        new HeaderFactory(),
                        new TotalLineFactory(),
                        new DetailLineFactory(),
                        new VatLineFactory()
                    )
                ))->retrieve($administrationCode, $fromDate);

                foreach ($twinfieldPurchaseInvoices as $twinfieldPurchaseInvoice) {

                    $importedPurchaseInvoiceRow = (new \DbTable\ImportedPurchaseInvoices())->matchRow([
                        'financial_invoice_id' => $twinfieldPurchaseInvoice->getNumber(),
                        'administration' => $twinfieldPurchaseInvoice->getOffice(),
                    ]);

                    if (!$importedPurchaseInvoiceRow) {
                        continue;
                    }

                    if ($importedPurchaseInvoiceRow->external_reference) {
                        continue;
                    }

                    $importedPurchaseInvoiceRow->external_reference = $twinfieldPurchaseInvoice->getFreeText3();
                    $importedPurchaseInvoiceRow->retry_document_download = true;

                    $importedPurchaseInvoiceRow->save();
                }
            }
        }
    }
}
