<?php

class Fixes_PhotosController extends GlobalController
{
    /**
     * http://support.omniboxx.johan.loc/fixes_photos/reset-photos-hash
     */
    public function resetPhotosHashAction()
    {
        $this->disableView();

        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 0);

        $photoModel = new \Photos();

        $photorows = $photoModel->fetchAll();
        foreach ($photorows as $photorow) {
            $photoModel->createAndSaveHash($photorow['id']);
        }

        die('done');
    }


}
