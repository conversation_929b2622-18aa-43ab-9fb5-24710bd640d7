<?php


use Zenvoices\Infrastructure\RetrieveAdministrationListService;

class Fixes_ZenvoicesController extends GlobalController
{
    public function __construct(Zend_Controller_Request_Abstract $request, Zend_Controller_Response_Abstract $response, array $invokeArgs = array())
    {
        parent::__construct($request, $response, $invokeArgs);

        $this->disableView();
    }

    /**
     * /fixes_zenvoices/administration-list
     *
     * @return void
     */
    public function administrationListAction()
    {
        $administrationList = (new RetrieveAdministrationListService())->retrieve();
        var_dump($administrationList);
    }


    /**
     * /fixes_zenvoices/update-purchase-invoices
     *
     * @return void
     */
    public function updatePurchaseInvoicesAction()
    {
        $select = db()->select()
            ->from(['ipi' => 'imported_purchase_invoices'], [
                'external_reference',
                'custom_invoice_id'
            ])
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = ipi.custom_invoice_id', false)

//            ->where('ipi.custom_invoice_id = ?', 46152)
            ->where('ipi.external_reference IS NOT NULL', false)
            ->where('ic.original IS NULL', false)
        ;

        $checkList = db()->fetchAll($select);

        foreach ($checkList as $todo) {
            $externalReference = $todo['external_reference'];
            $customInvoiceId = $todo['custom_invoice_id'];

            try {
                $attachmentsBinary = (new \Zenvoices\Application\DownloadAttachmentService())->download($externalReference);
            } catch (\Exception $exception) {
                $attachmentsBinary = [];
            }

            foreach ($attachmentsBinary as $filename => $attachmentBinary) {
                $invoicesCustomRow = (new InvoicesCustoms())->getById($customInvoiceId);

                if (!$invoicesCustomRow) {
                    continue;
                }

                (new InvoiceCustom())->saveOriginalToDisk($filename, $attachmentBinary, $customInvoiceId);

                $invoicesCustomRow->original = $filename;
                $invoicesCustomRow->save();

                var_dump([
                    'customInvoiceId' => $customInvoiceId,
                    'externalReference' => $externalReference,
                    'filename' => $filename,
                ]);

                break;
            }

        }
    }


}
