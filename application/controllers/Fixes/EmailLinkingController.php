<?php


class Fixes_EmailLinkingController extends GlobalController
{

    /**
     * http://support.omniboxx.johan.loc/fixes_email-linking/fix-missing-links
     *
     * @return void
     */
    public function fixMissingLinksAction()
    {
        $this->disableView();
        
        ini_set('max_input_time', 20000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $emailUsersRows = $this->getEmailUsersForCompaniesWithBrokenLink();

        $updateEmailUsers = [];
        foreach ($emailUsersRows as $emailUsersRow) {
            $emailUser = new EmailUser($emailUsersRow['type'], ['id' => $emailUsersRow['id'], 'email' => $emailUsersRow['address']]);
            $emailUser->findId();
            if (!$emailUser->params['name']) {
                continue;
            }

            $emailUser->save($emailUsersRow['email']);
            $updateEmailUsers[] = $emailUsersRow['id'];
        }

        error_log('Fix missing link email -> users');
        error_log(json_encode($updateEmailUsers));
    }




    private function getEmailUsersWhiteMissingLink()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['id', 'email', 'type', 'user_id', 'address'])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', false)
            ->where('e.`date` > ?', '2023-09-01 00:00:00.000')
            ->where('eu.user_id IS NULL')
        ;

        $data = db()->fetchAll($select);
        return $data;
    }

    private function getEmailUsersForCompaniesWithBrokenLink()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['id', 'email', 'type', 'user_id', 'address'])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', false)
            ->joinLeft(['c' => 'company'], 'c.id = eu.user_id', false)
            ->joinLeft(['cea' => 'crm_email_address'], 'cea.address = eu.address AND cea.address_type = "business"', false)
            ->where('e.`date` > ?', '2023-09-01 00:00:00.000')
            ->where('eu.user_id IS NOT NULL')
            ->where('c.id IS NOT NULL')
            ->where('cea.id IS NOT NULL')
        ;

        $data = db()->fetchAll($select);
        return $data;
    }
}
