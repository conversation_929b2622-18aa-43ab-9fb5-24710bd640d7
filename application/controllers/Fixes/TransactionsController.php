<?php


class Fixes_TransactionsController extends \GlobalController
{
    public function preDispatch()
    {
        $this->disableView();
    }

    /**
     * @Inject
     * @var \Transactions\RecalculateTransactionByTransactionPaymentService
     */
    private $recalculateTransactionByTransactionPaymentService;

    /**
     * http://support.omniboxx.johan.loc/fixes_transactions/recalculate-transaction-all
     * @return void
     * @throws Zend_Db_Adapter_Exception
     */
    public function recalculateTransactionAllAction()
    {
        ini_set('max_input_time', 20000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $transactionsRows = $this->getTransactions();

        foreach ($transactionsRows as $transactionsRow) {
            $this->recalculateTransactionByTransactionPaymentService->execute($transactionsRow['id']);

            $recalculateTransactionRow = (new Transactions())->getById($transactionsRow['id']);
            if ($transactionsRow['payed'] !== $recalculateTransactionRow['payed']) {
                echo sprintf('transaction %s : %s -> %s <br>',
                    $transactionsRow['id'],
                    $transactionsRow['payed'],
                    $recalculateTransactionRow['payed']
                );
            }
        }
    }


    private function getTransactions()
    {
        $select = db()->select()
            ->from(['t' => 'transactions'], ['id'])
//            ->where('t.id = ?', 43018)
        ;

        $data = db()->fetchAll($select);
        return $data;
    }

    /**
     * http://support.omniboxx.johan.loc/fixes_transactions/recalculate-transaction-by-invoice-id/invoice_id/785
     * @return void
     * @throws Zend_Db_Adapter_Exception
     */
    public function recalculateTransactionByInvoiceIdAction()
    {
        $invoiceId = $this->getParam('invoice_id');

        $transactionsRow = (new Transactions())->fetchRow(
            (new Transactions())->select()->where('invoice = ?', $invoiceId)
        )->toArray();

        $this->recalculateTransactionByTransactionPaymentService->execute($transactionsRow['id']);

        $recalculateTransactionRow = (new Transactions())->getById($transactionsRow['id']);
        if ($transactionsRow['payed'] !== $recalculateTransactionRow['payed']) {
            echo sprintf('transaction %s : %s -> %s <br>',
                $transactionsRow['id'],
                $transactionsRow['payed'],
                $recalculateTransactionRow['payed']
            );
        }

    }

    public function doubleCronjobCorrectionAction()
    {
        $this->disableView();
        
        // this service is made to correct the resulst of support issue #244158, to be executed on a test db on later notice
        $service = new \Fixes\Transactions\DoubleCronjobCorrectionService();
        $service->execute();

    }
}
