<?php


class Fixes_InvestorProvisionController extends FixesController
{
    public function correctInvoicePdfAction()
    {
        $this->disableView();

        $invoice_id = $this->getParam('invoice_id');

        $select = db()->select()
            ->from(['i' => 'invoices'], ['customid'])
            ->joinLeft(['ips' => 'investor_provision_send'], 'ips.custom_id = i.customid', ['data'])
            ->where('i.id = ?', $invoice_id);

        $invoiceData = db()->fetchRow($select);
        $data = json_decode($invoiceData['data'], true);

        $total = 0;

        foreach ($data['payments'] as $uoId => $uoData) {

            if ($uoData['object'] == 205) {

                foreach ($uoData['payments'] as $invoiceId => $invoice) {
                    if ($invoice['invoice'] != '5185493') {
                        continue;
                    }

                    foreach ($invoice['payout_rows'] as $payoutRowId => $payoutRow) {
                        $data['payments'][$uoId]['payments'][$invoiceId]['payout_rows'][$payoutRowId]['ex_vat'] = $payoutRow['total'];
                    }

                    $data['payments'][$uoId]['payments'][$invoiceId]['amount_ex_vat'] = -125362;
                    $data['payments'][$uoId]['payments'][$invoiceId]['amount_for_payout_ex'] = -125362;
                    $data['payments'][$uoId]['payments'][$invoiceId]['amount_for_provision_ex'] = -125362;
                    $data['payments'][$uoId]['payments'][$invoiceId]['shared_amount_ex'] = -125362;
                }

                $data['payments'][$uoId]['calculated']['amount_for_provision'] = 36003;
                $data['payments'][$uoId]['calculated']['amount_for_payout_ex'] = 36003;
                $data['payments'][$uoId]['calculated']['payments_ex'] = 36003;
                $data['payments'][$uoId]['calculated']['not_shared_amount_for_payout_ex'] = 36003;
                $data['payments'][$uoId]['calculated']['shared_amount_ex'] = 36003;
            }


            $total += $data['payments'][$uoId]['calculated']['shared_amount_ex'];
        }
        $data['totals']['not_shared_amount_for_payout_ex'] = 3306942;
        $data['totals']['amount_for_payout_ex'] = 3306942;

        $investor_lib = new Investors();
        $specification_pdf = $investor_lib->getProvisionSpecification($data);

        $invoiceCustomModel = new InvoicesCustoms();
        $invoiceCustomModel
            ->getById($invoiceData['customid'])
            ->setFromArray([
                'attachments' => serialize([$specification_pdf->zendpdf->render()]),
            ])
            ->save();


        $pdf = (new InvoiceCustom())->buildpdf(
            $invoiceData['customid'],
            $invoice_id
        );

        $invoiceCustomModel
            ->getById($invoiceData['customid'])
            ->setFromArray([
                'attachments' => nullValue()
            ])
            ->save();

//        $specification_pdf->browseroutput('new specification sheet.pdf');
        $pdf->browseroutput('new invoice.pdf');
    }


    public function investorProvisionRebuildInvoiceRowsAndPdfAction()
    {
        $investorProvisionId = $this->getParam('id');

        $service = new \Fixes\InvestorProvision\RebuildInvoiceRowsAndPdfService(
            new Investors(),
            new Pdf(),
            new InvoicesCustoms(),
            new InvoiceCustom(),
            new Invoice(),
            new InvoicesCustomsRows(),
            new InvoiceRows()
        );
        $service->execute($investorProvisionId);
    }

    public function investorProvisionSendCostsRemarkAsDoneAction()
    {
        $investorProvisionSendId = $this->getParam('id');

        $data_select = db()->select()
            ->from('investor_provision_send', ['data'])
            ->where('id = ?', $investorProvisionSendId);

        $dataJsonEncoded = db()->fetchOne($data_select);
        $data = json_decode($dataJsonEncoded, true);

        $investorModel = new Investors();
        $investorModel->saveInvestorProvisionSendCosts($investorProvisionSendId, $data);
    }

    public function updateSendPaymentsAmountsAction()
    {
        $updateSendPaymentsAmountsService = new \Fixes\InvestorProvision\UpdateSendPaymentsAmountsService(
            new InvestorProvisionsSendPayments()
        );
        $updateSendPaymentsAmountsService->execute();

        $updateSendCostAmountsService = new \Fixes\InvestorProvision\UpdateSendCostAmountsService(
            new InvestorProvisionsSendCosts()
        );
        $updateSendCostAmountsService->execute();

    }
}
