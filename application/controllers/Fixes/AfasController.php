<?php

use Accounting\Domain\Model\Customer\NoCustomersFoundException;
use Accounting\Domain\Model\Invoice\NoInvoicesFoundException;
use Accounting\Infrastructure\Domain\Model\Transport\Afas_ApiTransport as Afas_ApiTransport;
use Accounting\Infrastructure\Domain\Service\Afas_CreateOrUpdatePersonDebtorsService as Afas_CreateOrUpdateDebtorsService;
use Object\SyncAfasCostCenterService as SyncAfasCostCenterService;
use Accounting\Domain\Service\CreateExportFileService as CreateExportFileDomainService;

class Fixes_AfasController extends GlobalController
{

    private $createExportFileService;
    public function syncAllCostCentersAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => \Settings::get('financial_export_afas_customer_id'),
            'appToken' => \Settings::get('financial_export_afas_app_token'),
        ];

        if ($environment = \Settings::get('financial_export_afas_environment')) {
            $restClientOptions['environment'] = $environment;
        }

        $afasClient = new PracticalAfas\Client\RestCurlClient(
            $restClientOptions,
            [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]
        );

        $service = new SyncAfasCostCenterService(
            $afasClient,
            new Objects()
        );

        $select = db()->select()->from('objects', ['id']);
        $objectIds = db()->fetchCol($select);

        foreach ($objectIds as $objectId) {
            try {
                $service->execute($objectId);
            } catch (Exception $e) {
                p($e);
                error_log($e->getMessage());
            }
        }
    }

    public function syncAllTenantsAction()
    {
        $this->disableView();

        $userData = db()->select()
            ->from(['u' => 'users'], ['id'])
            ->joinLeft(['uo' => 'users_objects'], 'uo.customer = u.id', false)
            ->joinLeft(['o' => 'objects'], 'o.id = uo.object', false)
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', false)
            ->joinLeft(
                ['c' => 'corporations'],
                'c.id = p.corporation',
                [
                    'corporationId' => 'id',
                    'financial_export_sales_debtcode',
                ]
            )
            ->where('u.type = ?', 'tenant')
            ->where('c.id IS NOT NULL')
            ->query()
            ->fetchAll();

        $usersPerCorporations = [];

        foreach ($userData as $userItem) {
            if (!isset($usersPerCorporations[$userItem['corporationId']])) {
                $usersPerCorporations[$userItem['corporationId']] = [
                    'financial_export_sales_debtcode' => $userItem['financial_export_sales_debtcode'],
                    'userIds' => [],
                ];
            }

            $usersPerCorporations[$userItem['corporationId']]['userIds'][] = $userItem['id'];
        }

        foreach ($usersPerCorporations as $usersPerCorporation) {
            $service = new Afas_CreateOrUpdatePersonDebtorsService(
                new Afas_ApiTransport(),
                $usersPerCorporation['financial_export_sales_debtcode']
            );

            try {
                $service->execute($usersPerCorporation['userIds']);
            } catch (Exception $e) {
                p($e);
                error_log($e->getMessage());
            }
        }

    }

    /**
     * @Inject
     * @var Afas\Infrastructure\Afas\GetThirdPartyOutstandingInvoicesService
     */
    private $getThirdPartyOutstandingInvoicesService;

    public function paymentsTestAction()
    {
        $this->disableView();

//        $restClientOptions = [
//            'customerId' => Settings::get('financial_export_afas_customer_id'),
//            'appToken' => Settings::get('financial_export_afas_app_token'),
//        ];
//
//        if($environment = Settings::get('financial_export_afas_environment')){
//            $restClientOptions['environment'] = $environment;
//        }
//
//        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);
//
//        try {
//            $response = $client->callAfas(
//                'GET',
//                'connectors/OMNIBOXX_Openstaande_posten',
//                []
////                ['skip' => 100]
//            );
//        } catch (Exception $e){
//            p($e);
//        }
//
//        $response = json_decode($response, true);
//
//        p($response);

        $data = $this->getThirdPartyOutstandingInvoicesService->retrieve();
        p($data);

    }


    public function getDebtorTestAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => Settings::get('financial_export_afas_customer_id'),
            'appToken' => Settings::get('financial_export_afas_app_token'),
        ];

        if($environment = Settings::get('financial_export_afas_environment')){
            $restClientOptions['environment'] = $environment;
        }

        // p($restClientOptions,'die');
        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);

        try {
            $response = $client->callAfas(
                'GET',
                'connectors/Omniboxx_Debiteuren',
                ['take' => 10000]
            // ['skip' => 100]
            );
        } catch (Exception $e){
            p($e);
        }

        $response = json_decode($response, true);
        p($response);
        $item = (max($response['rows']));

        $newAfasNumber = $item['Nummer_debiteur']+1;
        p($newAfasNumber,'die');



    }


    public function purchaseInvoicesHeaderTestAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => Settings::get('financial_export_afas_customer_id'),
            'appToken' => Settings::get('financial_export_afas_app_token'),
        ];


        if($environment = Settings::get('financial_export_afas_environment')){
            $restClientOptions['environment'] = $environment;
        }

        // p($restClientOptions,'die');
        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);

        try {
            $response = $client->callAfas(
                'GET',
                'connectors/Omniboxx_inkoopfacturen_header',
                ['take' => 1000 ,'filterfieldids' => 'administration', 'filtervalues' => 25, 'operatortypes' =>  1]
            // ['skip' => 100]
            );
        } catch (Exception $e){
            p($e);
        }

        $response = json_decode($response, true);

        p($response,'die');

        //$data = $this->getThirdPartyOutstandingInvoicesService->retrieve();
        //p($data);

    }


    public function getInvoicePurchaseDocumentTestAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => Settings::get('financial_export_afas_customer_id'),
            'appToken' => Settings::get('financial_export_afas_app_token'),
        ];


        if($environment = Settings::get('financial_export_afas_environment')){
            $restClientOptions['environment'] = $environment;
        }

        // p($restClientOptions,'die');
        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);

        try {
            $response = $client->callAfas(
                'GET',
                'connectors/Profit_Subject_Attachments_Omniboxx/',
                ['take' => 1000 ,'filterfieldids' => 'attachment_id', 'filtervalues' => 262 , 'operatortypes' =>  1]
            // ['skip' => 100]
            );
        } catch (Exception $e){
            p($e);
        }

        $response = json_decode($response, true);

        p($response,'die');

        //$data = $this->getThirdPartyOutstandingInvoicesService->retrieve();
        //p($data);

    }

    public function getInvoicePurchaseDocumentSelfTestAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => Settings::get('financial_export_afas_customer_id'),
            'appToken' => Settings::get('financial_export_afas_app_token'),
        ];


        if($environment = Settings::get('financial_export_afas_environment')){
            $restClientOptions['environment'] = $environment;
        }

        // p($restClientOptions,'die');
        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);

        try {
            $response = $client->callAfas(
                'GET',
                'SubjectConnector/1280/ED6405D8025646569FDD2CA7BCFFEF2C',
                ['take' => 1000]

            // ['skip' => 100]
            );
        } catch (Exception $e){
            p($e);
        }

        var_dump($response);
        $response = json_decode($response, true);

        p($response,'die');

        //$data = $this->getThirdPartyOutstandingInvoicesService->retrieve();
        //p($data);

    }

    public function previewRunIdJsonAction() {

        $this->disableView();

        $runId = $this->getParam('run-id');

        if (empty($runId)) {
            die('no run-id param provided');
        }


        $invoiceRepository = new \Accounting\Infrastructure\Domain\Model\Invoice\PdoInvoiceRepository();

        if ($areCustomInvoices) {
            $customerRepository = new \Accounting\Infrastructure\Domain\Model\Customer\PdoCustomerCustomInvoiceRepository();

        } else {
            $customerRepository = new \Accounting\Infrastructure\Domain\Model\Customer\PdoCustomerRepository();
        }

        $invoiceIds = $invoiceRepository ->findAllDebugByRunId($runId);

        if (empty($invoiceIds)) {
            throw new NoInvoicesFoundException();
        }

        $customers = $customerRepository->findByInvoices($invoiceIds);

        if (empty($customers)) {
            throw new NoCustomersFoundException();
        }

        $transport = new \Accounting\Infrastructure\Domain\Model\Transport\Afas_DebugTransport();
        $createExportFileService = new \Accounting\Infrastructure\Domain\Service\Afas_CreateExportJSONService($transport);

        $service = new \Accounting\Application\Service\CreateExportFileService(
            $invoiceRepository, $customerRepository, $createExportFileService
        );

        $createExportFileService->debug($runId, $invoiceIds, $customers);


        p($transport->getData());
        p($transport->getMessage());



    }


    public function purchaseInvoicesLinesTestAction()
    {
        $this->disableView();

        $restClientOptions = [
            'customerId' => Settings::get('financial_export_afas_customer_id'),
            'appToken' => Settings::get('financial_export_afas_app_token'),
        ];


        if($environment = Settings::get('financial_export_afas_environment')){
            $restClientOptions['environment'] = $environment;
        }

        // p($restClientOptions,'die');
        $client = new \PracticalAfas\Client\RestCurlClient($restClientOptions, [CURLOPT_SSLVERSION => CURL_SSLVERSION_DEFAULT]);

        try {
            $response = $client->callAfas(
                'GET',
                'connectors/Omniboxx_inkoopfacturen_lines',
                ['take' => 1000 ,'filterfieldids' => 'administration', 'filtervalues' => 15, 'operatortypes' =>  1]

            // ['skip' => 100]
            );
        } catch (Exception $e){
            p($e);
        }

        $response = json_decode($response, true);

        p($response,'die');

        //$data = $this->getThirdPartyOutstandingInvoicesService->retrieve();
        //p($data);

    }





}
