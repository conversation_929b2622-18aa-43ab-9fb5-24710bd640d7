<?php

use Financial\Infrastructure\Models\FinancialSystems;


class Fixes_FinancialController extends \GlobalController
{

    /**
     * @Inject
     * @var Financial\Application\GetFinancialSystemByReferenceService
     */
    private $getFinancialSystemByReferenceService;

    /**
     * @Inject
     * @var Financial\Application\Contact\AddFinancialContactIdentifierService
     */
    private $addFinancialContactIdentifierService;

    /**
     * @Inject
     * @var Financial\Application\Contact\AddFinancialContactErrorService
     */
    private $addFinancialContactErrorService;

    /**
     * @Inject
     * @var Financial\Application\Contact\GetAndOrCreateFinancialContactService
     */
    private $getAndOrCreateFinancialContactService;

    /**
     * @Inject
     * @var Financial\Application\Contact\UpdateSyncDateByUserIdService
     */
    private $updateSyncDateByUserIdService;


    /**
     * http://dev.omniboxx.johan.loc/fixes_financial/test-contact-error-loging
     */
    public function testContactErrorLogingAction()
    {
        $this->disableView();

        try {
            $financialSystem = $this->getFinancialSystemByReferenceService->execute(FinancialSystems::REF_AFAS);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }

        try {
            $userId = 568; //test user
            $administration = 'test_123'; //test data

            $financialContact = $this->getAndOrCreateFinancialContactService->execute(
                $financialSystem,
                $userId,
                $administration
            );
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }

        try {
            $financialContactIdentifier = '123'; //test data

            $this->addFinancialContactIdentifierService->execute($financialContact, $financialContactIdentifier);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }

        try {
            $financialContactIdentifier = '123'; //test data

            $this->addFinancialContactIdentifierService->execute($financialContact, $financialContactIdentifier);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }

        try {
            $this->updateSyncDateByUserIdService->execute($financialContact);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }


        try {
            $message = 'Oeps een error'; //test data

            $this->addFinancialContactErrorService->execute($financialContact, $message);
        } catch (\Exception $exception) {
            echo $exception->getMessage();
        }




    }


}
