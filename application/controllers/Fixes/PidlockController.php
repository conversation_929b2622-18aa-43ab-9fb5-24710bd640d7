<?php


class Fixes_PidlockController extends GlobalController
{
    /**
     * sudo -u www-data php /var/www/omniboxx_dev2/scripts/zf-cli.php -s omniboxx_dev2 -a fixes_pidlock/test -e local
     *
     * http://dev2.omniboxx.johan.loc/fixes_pidlock/test
     *
     * @return void
     */
    public function testAction()
    {

        $pid_lock = new PIDLock('test', ['test-id' => 1], $lifeTimestamp = 20);
        sleep(5);

        $pid_lock = new PIDLock('test', ['test-id' => 1], $lifeTimestamp = 20);
        sleep(5);


        $pid_lock->remove();

    }


    /**
     * http://dev2.omniboxx.johan.loc/fixes_pidlock/remove
     *
     * @return void
     */
    public function removeAction()
    {

    }
}





/**
 *
 *
 *
 * https://www.php.net/manual/en/function.register-tick-function.php
 * https://www.php.net/manual/en/function.unregister-tick-function.php
 *
 * sudo -u www-data php /var/www/omniboxx_dev2/scripts/zf-cli.php -s omniboxx_dev2 -a cronjob_test/pid-lock -e local
 *
 * http://dev2.omniboxx.johan.loc/cronjob_test/pid-lock
 * @return void
 */
//    public function pidLockAction()
//    {
//        $pid_lock = new PIDLock('test_lock', [], 100, 'pietje');
//
//        $microtime = microtime();
//        $result = $pid_lock->lock(function () {
//
//        //    throw new \Exception('test');
//            for ($i=0; $i < 60; $i++) {
//                sleep(1);
//            }
//
//            return 'end of live';
//        });
//
//
//        $a = $microtime - microtime();
//        var_dump($microtime - microtime());
//        var_dump($result);
//    }
//}
//
//declare(ticks=1);
//class PIDALiveService {
//
//    private $pidFilePath;
//
//    public function execute($pidFilePath)
//    {
//        $this->pidFilePath = $pidFilePath;
//
//        $PIDfile = $this->readLock();
//        $this->setNewProcessTime($PIDfile);
//    }
//
//
//
//    protected function readLock()
//    {
//        if (!file_exists($this->pidFilePath)) {
//            throw new NoLockFile($this->pidFilePath);
//        }
//
//        $fileContentString = file_get_contents($this->pidFilePath);
//        if (!$fileContentString) {
//            throw new NoLockFileContent('file is empty');
//        }
//
//        $contentObject = json_decode($fileContentString);
//        if (json_last_error()) {
//            throw new NoLockFileContent('file content is corrupt json error: '. json_last_error_msg());
//        }
//
//        $PIDfile = new PIDfile(
//            $contentObject->pid,
//            $contentObject->psString,
//            $contentObject->name,
//            $contentObject->params,
//            $contentObject->createTimestamp,
//            $contentObject->lifeTimestamp,
//            $contentObject->checkList,
//            $contentObject->mailSend,
//            $contentObject->processTime
//        );
//
//        return $PIDfile;
//    }
//
//    public function setNewProcessTime(PIDfile $PIDfile)
//    {
//        createFolder($this->pidFilePath);
//
//        file_put_contents($this->pidFilePath, json_encode([
//            'pid' => $PIDfile->getPid(),
//            'psString' => $PIDfile->getpsString(),
//            'name' => $PIDfile->getName(),
//            'params' => $PIDfile->getParams(),
//            'createTimestamp' => $PIDfile->getCreateTimestamp(),
//            'lifeTimestamp' => $PIDfile->getLifeTimestamp(),
//            'checkList' => $PIDfile->getCheckList(),
//            'mailSend' => $PIDfile->getMailSend(),
//            'processTime' => time(),
//        ]));
//    }
//}
//
//class PIDLock
//{
//    private $pidFilePath;
//    private $name;
//    /**
//     * @var array
//     */
//    private $params;
//    /**
//     * @var false
//     */
//    private $lifeTimestamp;
//
//    public function __construct($name, $params = [], $lifeTimestamp = false)
//    {
//        $this->name = $name;
//        $this->params = $params;
//        $this->lifeTimestamp = $lifeTimestamp;
//
//        $this->pidFilePath = $this->makeFilePath($this->name, $this->params);
//    }
//
//    public function lock($callback)
//    {
//
//
//
//        try {
//            if ($this->checkOrFileLock()) {
//                die('is lockt');
//            }
//        } catch (\ProcessTimeExpires $doesNotMatch) {
//            $this->remove();
//        }  catch (\PsStringDoesNotMatch $doesNotMatch) {
//            $this->remove();
//        } catch (\Exception $exception) {
//            $PIDfile = $this->readLock();
//            $this->sendMail($PIDfile);
//        }
//
//
//        $PIDfile = new PIDfile(
//            getmypid(),
//            $this->getCurrentPsString(),
//            $this->name,
//            $this->params,
//            time(),
//            $this->lifeTimestamp
//        );
//
//        $this->setLock($PIDfile);
//
//        $PIDALiveService = new PIDALiveService();
//        register_tick_function([$PIDALiveService, 'execute'], $this->pidFilePath);
//
//        try {
//            $result = $callback();
//        } catch (\Exception $exception) {
//            $e = $exception;
//        } finally {
//            $this->remove();
//        }
//
//        return $result;
//    }
//
//
//
//    private function checkOrFileLock()
//    {
//        try {
//            $PIDfile = $this->readLock();
//
//        } catch (NoLockFile $noLockFile) {
//            // PiD is no lockt
//            return false;
//        } catch (\Exception $exception) {
//            // no file
//            return false;
//        }
//
//        if (!$PIDfile->getProcessTime() < strtotime('- 10 minutes')) {
//            throw new ProcessTimeExpires();
//        }
//
////        if (!$PIDfile->getPsString() != $this->getCurrentPsString()) {
////            throw new PsStringDoesNotMatch('Current: '. $this->getCurrentPsString() . ' Old: '.$PIDfile->getPsString());
////        }
//
//
//        $currentTimeStamp = time();
//        if (($PIDfile->getCreateTimestamp() + $PIDfile->getLifeTimestamp()) < $currentTimeStamp) {
//            throw new LockException('Lock is expired');
//        }
//
//
//        return true;
//    }
//
//    private function getCurrentPsString()
//    {
//        $pid = getmypid();
//        exec('ps -f -p ' . $pid . '| grep ' . $pid, $output);
//
//        return $output;
//    }
//
//
//    public function setLock(PIDfile $PIDfile)
//    {
//        createFolder($this->pidFilePath);
//
//        file_put_contents($this->pidFilePath, json_encode([
//            'pid' => $PIDfile->getPid(),
//            'psString' => $PIDfile->getpsString(),
//            'name' => $PIDfile->getName(),
//            'params' => $PIDfile->getParams(),
//            'createTimestamp' => $PIDfile->getCreateTimestamp(),
//            'lifeTimestamp' => $PIDfile->getLifeTimestamp(),
//            'checkList' => $PIDfile->getCheckList(),
//            'mailSend' => $PIDfile->getMailSend(),
//            'processTime' => $PIDfile->getProcessTime(),
//        ]));
//    }
//
//
//    private function makeFilePath($name, $params)
//    {
//        $lockBashFolder = Main::app()->getDir('pid_locks');
//        $pidFilePath = $lockBashFolder . $name . '/';
//
//        foreach ($params as $param) {
//            $pidFilePath .= $param . '/';
//        }
//
//        $pidFilePath .= 'pid.lock';
//        return $pidFilePath;
//    }
//
//    protected function readLock()
//    {
//        if (!file_exists($this->pidFilePath)) {
//            throw new NoLockFile($this->pidFilePath);
//        }
//
//        $fileContentString = file_get_contents($this->pidFilePath);
//        if (!$fileContentString) {
//            throw new NoLockFileContent('file is empty');
//        }
//
//        $contentObject = json_decode($fileContentString);
//        if (json_last_error()) {
//            throw new NoLockFileContent('file content is corrupt json error: '. json_last_error_msg());
//        }
//
//        $PIDfile = new PIDfile(
//            $contentObject->pid,
//            $contentObject->psString,
//            $contentObject->name,
//            $contentObject->params,
//            $contentObject->createTimestamp,
//            $contentObject->lifeTimestamp,
//            $contentObject->checkList,
//            $contentObject->mailSend,
//            $contentObject->processTime
//        );
//
//        return $PIDfile;
//    }
//
//    protected function getFilename()
//    {
//        return $this->pidFilePath;
//    }
//
//    protected function remove()
//    {
//        if (!file_exists($this->pidFilePath)) {
//            throw new \Exception('File do not exists: '. $this->pidFilePath);
//        }
//
//        if (!is_readable($this->pidFilePath)) {
//            throw new \Exception('File is not readable: '. $this->pidFilePath);
//        }
//
//        unlink($this->pidFilePath);
//    }
//
//
//    private function sendMail(PIDfile $PIDfile)
//    {
//        $subject = sprintf('PIDlock is stak: %s (%s)',
//            $PIDfile->getName(),
//            json_encode($PIDfile->getParams())
//        );
//
//        $emailOutgoing = new \EmailOutgoing(array(
//            'to' =>				'<EMAIL>',
//            'subject' =>		$subject,
//            'text' => implode(' ', [
//                $subject
//            ])
//        ));
//
//        $this->mailIsSend($PIDfile);
//
//        $preview = new \EmailPreview($emailOutgoing->id);
//        $preview->display();
//        die();
//    }
//
//    private function mailIsSend(PIDfile $PIDfile)
//    {
//        $newPIDfile = new PIDfile(
//            $PIDfile->getPid(),
//            $PIDfile->getPsString(),
//            $PIDfile->getName(),
//            $PIDfile->getParams(),
//            $PIDfile->getCreateTimestamp(),
//            $PIDfile->getLifeTimestamp(),
//            $PIDfile->getCheckList(),
//            true,
//            $PIDfile->getProcessTime()
//        );
//
//        $this->setLock($newPIDfile);
//    }
//}
//
//class PIDfile {
//
//    private $pid;
//    private $psString;
//    private $name;
//    private $params;
//    private $createTimestamp;
//    private $lifeTimestamp;
//    private $checkList;
//    /**
//     * @var null
//     */
//    private $mailSend;
//    private $processTime;
//
//    public function __construct(
//        $pid,
//        $psString,
//        $name,
//        $params,
//        $createTimestamp,
//        $lifeTimestamp,
//        $checkList = [],
//        $mailSend = null,
//        $processTime = null
//    ) {
//        $this->pid = $pid;
//        $this->psString = $psString;
//        $this->name = $name;
//        $this->params = $params;
//        $this->createTimestamp = $createTimestamp;
//        $this->lifeTimestamp = $lifeTimestamp;
//        $this->checkList = $checkList;
//        $this->mailSend = $mailSend;
//        $this->processTime = $processTime;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getPid()
//    {
//        return $this->pid;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getName()
//    {
//        return $this->name;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getParams()
//    {
//        return $this->params;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getLifeTimestamp()
//    {
//        return $this->lifeTimestamp;
//    }
//
//    /**
//     * @return array|mixed
//     */
//    public function getCheckList()
//    {
//        return $this->checkList;
//    }
//
//    /**
//     * @return mixed|null
//     */
//    public function getMailSend()
//    {
//        return $this->mailSend;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getCreateTimestamp()
//    {
//        return $this->createTimestamp;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getPsString()
//    {
//        return $this->psString;
//    }
//
//    /**
//     * @return mixed
//     */
//    public function getProcessTime()
//    {
//        return $this->processTime;
//    }
//}
//
//
//class LockException extends \Exception {
//
//}
//class NoLockFile extends \Exception {
//
//}
//class NoLockFileContent extends \Exception {
//
//}
//
//class PsStringDoesNotMatch extends \Exception {
//
//}
//class ProcessTimeExpires extends \Exception {
//
//}
