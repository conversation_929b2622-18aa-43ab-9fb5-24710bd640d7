<?php


use TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Onderhoud\Binnen;
use TiaraWonen2\Model\ObjectData\Structure\Wonen\Woonlagen\Verdieping\BeganeGrondOfFlat;

class Fixes_FundaController extends \GlobalController
{


    public function testAction()
    {
        $Huisnummer = new \TiaraWonen2\Model\ObjectData\Structure\Adres\Nederlands\Huisnummer\Huisnummer('279');
        $Huisnummer->setHuisnummerToevoeg('A');
        $Nederlands = new TiaraWonen2\Model\ObjectData\Structure\Adres\Nederlands\Nederlands('3543 EN', $Huisnummer);
        $Adres = new TiaraWonen2\Model\ObjectData\Structure\Adres\Adres();
        $Adres->setNederlands($Nederlands);

        $StatusBeschikbaarheid = new TiaraWonen2\Model\ObjectData\Structure\StatusBeschikbaarheid\StatusBeschikbaarheid(\TiaraWonen2\Model\ObjectData\Structure\StatusBeschikbaarheid\Status::BESCHIKBAAR);
        $Vertrouwelijk = 'nee';
        $EigenBelang = 'nee';

        $Aanbiedingstekst = ' Ja een heel leuk verhaal';


        $ObjectAanmelding = \TiaraWonen2\Model\ObjectData\Structure\ObjectAanmelding::IN_VERHUUR_GENOMEN;

        $DatumInvoer =  (new DateTime('2024-05-27'));

        $ObjectData = (new \TiaraWonen2\Model\ObjectData\Post\ObjectData(
            $Adres,
            \TiaraWonen2\Model\ObjectData\Structure\Aanvaarding::DIRECT,
            $StatusBeschikbaarheid,
            $Vertrouwelijk,
            $EigenBelang,
            $Aanbiedingstekst,
            $ObjectAanmelding,
            $DatumInvoer
        ));


        $Huur = new TiaraWonen2\Model\ObjectData\Structure\Huur\Huur(
            '666',
            'PerMaand',
            '32',
            '1332',
            'Permanent'
        );
        $ObjectData->setHuur($Huur);


        $Recreatie = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Bestemming\Recreatie\Recreatie('nee');
        $Bestemming = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Bestemming\Bestemming(true, $Recreatie);
        $MatenEnLigging = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\MatenEnLigging\MatenEnLigging('100', '40');
        $Bouwjaar = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Bouwjaar\Bouwjaar('nee');
        $Bouwjaar->setPeriode('From1906to1930');


        $Binnen = new Binnen('Slecht');
        $Buiten = new \TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Onderhoud\Buiten('Slecht');
        $Onderhoud = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\Onderhoud\Onderhoud($Binnen, $Buiten);

        $WonenDetails = new TiaraWonen2\Model\ObjectData\Structure\Wonen\WonenDetails\WonenDetails(
            $Bestemming,
            $MatenEnLigging,
            $Bouwjaar,
            $Onderhoud
        );

        $BeganeGrondOfFlat = new BeganeGrondOfFlat(2);
        $Woonlagen = new TiaraWonen2\Model\ObjectData\Structure\Wonen\Woonlagen\Woonlagen($BeganeGrondOfFlat);

        $Wonen = new TiaraWonen2\Model\ObjectData\Structure\Wonen\Wonen($WonenDetails, $Woonlagen);

        $ObjectData->setWonen($Wonen);

        $Appartement = new TiaraWonen2\Model\ObjectData\Structure\Wonen\Appartement\Appartement(
            'Bovenwoning',
            true,
            '1'
        );

        $Wonen->setAppartement($Appartement);

        $ObjectData->setBouwvorm('BestaandeBouw');


        $ObjectData->setServicekostenPerMaand('80');


//        var_dump($objectData);
//        die();




        $ObjectData->postValidate();

        if (\TiaraWonen2\Model\Wonen\Validatie\Validator::instance()->hasErrors()) {
            $errorMessages = \TiaraWonen2\Model\Wonen\Validatie\Validator::instance()->getErrorMessages();
            throw new \TiaraWonen2\Model\Wonen\Validatie\ValidationException($errorMessages);
        }

        $tiaraObjectSerializer = (new \TiaraWonen2\Serializer\TiaraObjectSerializerFactory())->build();

        $json = $tiaraObjectSerializer->serialize($ObjectData, 'json');
        echo '<pre>';
        p($json);
        die();


    }

}
