<?php


use DbTable\TiaraEntity;
use DbTable\TiaraFormState;
use DbTable\TiaraQueue;

class Fixes_TiaraProjectController extends GlobalController
{
    public function createAction()
    {
        $projectId = $this->getParam('project_id');

        try {
            $this->executeFixTiaraProject($projectId);
        } catch (\Exception $exception) {
            print $exception->getMessage();
        } finally {
            print 'done';
        }
    }

    private function executeFixTiaraProject($projectId)
    {
        $projectRow = $this->findProjectById($projectId);
        $tiaraEntityRow = $this->findTiaraEntityByProjectId($projectRow['id']);
        if (!$tiaraEntityRow) {
            return;
        }

        $tiaraEntityId = $this->updateTiaraEntityByProject($projectRow);

        $tiaraQueueId = $this->createTiaraQueueByTiaraEntity($tiaraEntityId, 'AanmeldenMedia', $panentTiaraQueueId = null);
        $this->createTiaraQueueByTiaraEntity($tiaraEntityId, 'OpvragenResultaatBerichten', $tiaraQueueId);

    }

    private function findProjectById($objectId)
    {
        $select = db()->select()
            ->from(['p' => 'projects'])
            ->where('p.id = ?', $objectId);

        return db()->fetchRow($select);
    }

    private function updateTiaraEntityByProject($projectRow)
    {
        $tiaraEntityNewRow = $this->findTiaraEntityByProjectId($projectRow['id']);
        $tiaraEntityNewRow->xml = '<?xml version = "1.0" encoding = "UTF-8"?><result></result>';
//        $tiaraEntityNewRow->state = 'added';
//        $tiaraEntityNewRow->re_sync = '0';
        $tiaraEntityNewRow->setFromArray([
//            'omniboxx_entity_id' => $objectRow['id'],
//            'object_id' => $objectRow['externalTiaraId'],
//            'project_id' => NULL,
            'object_type_id' => NULL,
            'object_guid' => NULL,
            'project_guid' => NULL,
            'object_type_guid' => NULL,
            'media_guid' => NULL,
//            'type' => 'object',
            'post_data' => NULL,
            'xml' => '<?xml version = "1.0" encoding = "UTF-8"?><result></result>',
            'state' => 'added',
            'medium_order_number' => NULL,
            're_sync' => '0',
//            'created_at' => new DateTime(),
//            'updated_at' => new DateTime(),
        ]);

        return $tiaraEntityNewRow->save();
    }

    private function findTiaraEntityByExternalTiaraIdNotRemoved($externalTiaraId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'], ['maxId' => 'MAX(id)'])
            ->where('te.`type` = ?', 'project')
            ->where('te.project_id = ?', $externalTiaraId);

        $data = db()->fetchRow($select);
        if (!$data['maxId']) {
            return;
        }

        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.id = ?', $data['maxId']);

        return db()->fetchRow($select);
    }

    private function findTiaraEntityByProjectId($projectId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.`type` = ?', 'project')
            ->where('te.omniboxx_entity_id = ?', $projectId);

        $dataRow = db()->fetchRow($select);
        return (new TiaraEntity())->getById($dataRow['id']);
    }

    private function findTiaraEntityById($projectId)
    {
        $select = db()->select()
            ->from(['te' => 'tiara_entity'])
            ->where('te.`type` = ?', 'project')
            ->where('te.id = ?', $projectId);

        $dataRow = db()->fetchRow($select);
        return (new TiaraEntity())->getById($dataRow['id']);
    }

    private function createTiaraQueueByTiaraEntity($tiaraEntityId, $callType, $panentTiaraQueueId = null)
    {
        $TiaraQueueNewRow = (new TiaraQueue())->createRow();
        $TiaraQueueNewRow->setFromArray([
            'tiara_entity_id' => $tiaraEntityId,
            'linked_tiara_queue_id' => $panentTiaraQueueId,
            'mutation_id' => '',
            'request_xml' => '',
            'response_xml' => '',
            'call_type' => $callType,
            'state' => 'processed',
            'error_message' => null,
            'error_trace' => null,
            'attempt_count' => 0,
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return $TiaraQueueNewRow->save();
    }


    /**
     * http://dev.omniboxx.johan.loc/fixes_tiara-project/update-from-csv
     */
    public function updateFromCsvAction()
    {

        $vestegingNrList = [
            70004,
            70008,
            70010,
            70011,
            70041,
            70043,
            70044,
            70088,
            70097,
            70122,
            70123,
            70124,
            70172,
            70177,
            70208,
            70232,
            71023
        ];



        foreach ($vestegingNrList as $vestegingNr) {
            error_log('Start import project for vestegingNr: ' . $vestegingNr);
            try {
                $this->importByCsv(sprintf('%s/_cache/tiara/%s/projecten_%s.csv',
                    $_SERVER['CONTEXT_DOCUMENT_ROOT'],
                    $vestegingNr,
                    $vestegingNr
                ));
            } catch (\Exception $exception) {
                error_log($exception->getMessage());
                error_log($exception->getTraceAsString());
            }
        }

        die('end of live');
    }

    private function importByCsv($filePath)
    {
        $CsvReader = new CsvReader($filePath, ',');

        while ($csvRow = $CsvReader->getRow()) {
            $externalTiaraId = $csvRow['tiara id'];
            if (!trim($externalTiaraId) || '' == $externalTiaraId) {
                continue;
            }

            $tiaraEntitRow = $this->findTiaraEntityByExternalTiaraIdNotRemoved($externalTiaraId);
            if (!$tiaraEntitRow) {
                error_log('Tiara item is not found: ' . $externalTiaraId);
                continue;
            }

            if ('removed' == $tiaraEntitRow['state']) {
                error_log('Tiara item is removed: ' . $externalTiaraId);
                continue;
            }

            $this->updateTiaraEntity($tiaraEntitRow['id'], $csvRow);

            $this->updateTiaraPostData($tiaraEntitRow['id'], $csvRow);

        }
    }

    private function updateTiaraPostData($tiaraEntitId, $data)
    {
        $tiaraEntitRow = $this->findTiaraEntityById($tiaraEntitId, 'project');
        $tiaraFormStateRow = $this->findTiaraFormStateByOmniboxxIdAndType($tiaraEntitRow->omniboxx_entity_id, 'project');
        if (!$tiaraFormStateRow) {
            $this->createTiaraFormState($tiaraEntitRow->omniboxx_entity_id, 'project');
            $tiaraFormStateRow = $this->findTiaraFormStateByOmniboxxIdAndType($tiaraEntitRow->omniboxx_entity_id, 'project');
        }

        $postData = (array)json_decode($tiaraFormStateRow->json_post_data, true);

        if (!$postData['Project']) {
            $postData['Project'] = [];
        }

        if (!$postData['ProjectProjectDetailsAdres']) {
            $postData['ProjectProjectDetailsAdres'] = [];
        }
        if (!$postData['ProjectProjectDetailsFinancieleGegevensHuurprijs']) {
            $postData['ProjectProjectDetailsFinancieleGegevensHuurprijs'] = [];
        }
        if (!$postData['ProjectProjectDetails']) {
            $postData['ProjectProjectDetails'] = [];
        }
        if (!$postData['ProjectProjectDetailsPresentatie']) {
            $postData['ProjectProjectDetailsPresentatie'] = [];
        }
        if (!$postData['ProjectProjectDetailsMatenBrutoInhoud']) {
            $postData['ProjectProjectDetailsMatenBrutoInhoud'] = [];
        }
        if (!$postData['ProjectProjectDetailsMatenWoonoppervlakte']) {
            $postData['ProjectProjectDetailsMatenWoonoppervlakte'] = [];
        }

        if (!$postData['ProjectProjectDetailsPresentatieInternetPlaatsing']) {
            $postData['ProjectProjectDetailsPresentatieInternetPlaatsing'] = [];
        }
        $postData['Project']['NVMVestigingNR'] = $data['vestigingsnummer'];
        $postData['Project']['ProjectID'] = $data['tiara id'];

        $postData['ProjectProjectDetailsAdres']['Postcode'] = $data['postcode'];
        $postData['ProjectProjectDetailsAdres']['Woonplaats'] = $data['woonplaats'];
        $postData['ProjectProjectDetailsAdres']['Land'] = $data['land'];

        $postData['ProjectProjectDetailsFinancieleGegevensHuurprijs']['Van'] = $data['huurprijs van'];
        $postData['ProjectProjectDetailsFinancieleGegevensHuurprijs']['TotEnMet'] = $data['huurprijs tot'];

        $postData['ProjectProjectDetails']['Valuta'] = 'EUR';
//        $postData['ProjectProjectDetails']['Opdrachtgever'] = $data[''];
        $postData['ProjectProjectDetails']['Projectnaam'] = $data['naam'];
        $postData['ProjectProjectDetails']['DatumInvoer'] = date('d-m-Y', strtotime($data['aanmelddatum']));
        $postData['ProjectProjectDetails']['KoopHuur'] = 'huur';
        $postData['ProjectProjectDetails']['Bouwvorm'] = $data['bouwvorm'];
        $postData['ProjectProjectDetails']['AantalEenheden'] = $data['aantal eenheden'];
//        $postData['ProjectProjectDetails']['DatumStartBouw'] = $data[''];
//        $postData['ProjectProjectDetails']['DatumOpleveringVanaf'] = $data[''];

//        $postData['ProjectProjectDetailsPresentatie']['Aanbiedingstekst'] = $data[''];
//        $postData['ProjectProjectDetailsPresentatie']['Website'] = $data[''];
//        $postData['ProjectProjectDetailsPresentatie']['Omgeving'] = $data[''];

        $postData['ProjectProjectDetailsMatenBrutoInhoud']['Van'] = $data['bruto inhoud van'];
        $postData['ProjectProjectDetailsMatenBrutoInhoud']['TotEnMet'] = $data['bruto inhoud tot'];

        $postData['ProjectProjectDetailsMatenWoonoppervlakte']['Van'] = $data['woonoppervlakte van'];
        $postData['ProjectProjectDetailsMatenWoonoppervlakte']['TotEnMet'] = $data['woonoppervlakte tot'];

//        $postData['ProjectProjectDetailsPresentatieInternetPlaatsing'][0]['Plaatsing'] = $data[''];
//        $postData['ProjectProjectDetailsPresentatieInternetPlaatsing'][0]['DatumVrijgave'] = $data[''];
//        $postData['ProjectProjectDetailsPresentatieInternetPlaatsing'][0]['DatumEind'] = $data[''];

//        $postData['ProjectProjectDetails']['DatumInvoer'] = $data['datuminvoer'];


        foreach ($postData as $formName => $formValues) {
            foreach ($formValues as $inputName => $inputValue) {
                if (strpos($formName, 'Wij')) {
                    $postData[$formName][$inputName] = $inputValue;
                } else {
                    $postData['Wij' . $formName][$inputName] = $inputValue;
                }

            }
        }


        $tiaraFormStateRow->json_post_data = json_encode($postData);

        $tiaraFormStateRow->save();
    }


    private function updateTiaraEntity($tiaraEntitId, $data)
    {
        $tiaraEntitRow = $this->findTiaraEntityById($tiaraEntitId);

        $tiaraEntitRow->project_guid = $data['project guid'];

        $tiaraEntitRow->save();
    }



    private function findTiaraFormStateByOmniboxxIdAndType($omniboxxEntityId, $omniboxxEntityType)
    {
        $select = db()->select()
            ->from(['tfs' => 'tiara_form_state'])
            ->where('tfs.omniboxx_entity_id = ?', $omniboxxEntityId)
            ->where('tfs.omniboxx_entity_type = ?', $omniboxxEntityType);

        $dataRow = db()->fetchRow($select);
        if (!$dataRow) {
            return;
        }

        return (new TiaraFormState())->getById($dataRow['id']);
    }

    private function createTiaraFormState($id, $type)
    {
        $TiaraFormState = (new TiaraFormState())->createRow();
        $TiaraFormState->setFromArray([
            'omniboxx_entity_id' => $id,
            'omniboxx_entity_type' => $type,
            'json_post_data' => json_encode([])
        ]);

        return $TiaraFormState->save();
    }


}
