<?php

use Accounting\Infrastructure\Domain\Model\Transport\Afas_ApiTransport as Afas_ApiTransport;
use Accounting\Infrastructure\Domain\Service\Afas_CreateOrUpdatePersonDebtorsService as Afas_CreateOrUpdateDebtorsService;
use Object\SyncAfasCostCenterService as SyncAfasCostCenterService;

class Fixes_InspectionController extends FixesController
{
    /**
     * http://support.omniboxx.johan.loc/fixes_inspection/resync-inspection-zipfiles/startDate/2021-01-01/endDate/2021-01-30
     *
     */
    public function resyncInspectionZipfilesAction()
    {
        $zipfilesPath = '_cache/inspections';
        $inspectionModel = new Inspection();

        $startDateParam = $this->getParam('startDate');
        $endDateParam = $this->getParam('endDate');

        if (!$startDateParam || !$endDateParam) {
            die('Please specify startDate, endDate and limit param');
        }

        $startDateTimestamp = strtotime($startDateParam);
        $endDateTimestamp = strtotime($endDateParam);

        foreach (scandir($zipfilesPath) as $inspectionFolderName) {
            if (str_replace('.', '', $inspectionFolderName) === '') {
                continue;
            }

            $inspectionFilename = implode([
                $zipfilesPath,
                $inspectionFolderName,
                'inspection.zip'
            ], '/');

            if (!is_readable($inspectionFilename)) {
                continue;
            }

            $inspectionSyncTimestamp = filectime($inspectionFilename);

            if ($inspectionSyncTimestamp < $startDateTimestamp) {
                continue;
            }

            if ($inspectionSyncTimestamp > $endDateTimestamp) {
                continue;
            }

            $inspectionModel->saveZip($inspectionFilename);
        }

        echo 're-synced inspections';
    }

    public function recreateAndMailInspectionDocumentsAction()
    {
        $idsParamValue = $this->getParam('ids');

        if (!$idsParamValue) {
            return;
        }

        $ids = explode(',', $idsParamValue);

        if (!is_array($ids) || count($ids) === 0) {
            return;
        }

        foreach ($ids as $id) {
            startWorker('inspection-document/id/' . $id . '/');
        }

        echo 'Inspection document workers are started for inspection document ids: ' . $idsParamValue;
    }

    /**
     * http://support.omniboxx.johan.loc/fixes_inspection/find-zip/inspectionId/3262
     */
    public function findZipAction()
    {
        $inspectionId = $this->getParam('inspectionId');
        $zipfilesPath = '_cache/inspections';

        foreach (scandir($zipfilesPath) as $inspectionFolderName) {
            if (str_replace('.', '', $inspectionFolderName) === '') {
                continue;
            }

            $inspectionFilename = implode([
                $zipfilesPath,
                $inspectionFolderName,
                'inspection.zip'
            ], '/');

            if (!is_readable($inspectionFilename)) {
                continue;
            }

            $zipArchive = new ZipArchive();
            if ($zipArchive->open($inspectionFilename) === true) {
                $inspectionData = json_decode($zipArchive->getFromName('inspections.json'), true);
                foreach ($inspectionData as $inspection_id => $inspection) {
                    if ($inspectionId == $inspection_id) {
                        var_dump($inspectionFilename);
                    }
                }
            }

        }
    }
}
