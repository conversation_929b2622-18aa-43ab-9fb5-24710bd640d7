<?php



class Fixes_WorkerTestController extends GlobalController
{

    /**
     * http://dev.omniboxx.johan.loc/fixes_worker-test/test
     * @return void
     */
    public function testAction()
    {

//        startWorker('test/id/'.uniqid().'/priority/direct/', 'cronjob_worker-test', 'direct');



        Logger::add(['startWorker', 'testrun'], 'start test run');

        // ["direct", "high", "low"]
        for ($i =0; $i < 500; $i++) {
            startWorker('test/id/'.uniqid().'/priority/direct/', 'cronjob_worker-test', 'direct');
        }

//        for ($i =0; $i < 500; $i++) {
//            startWorker('test/id/'.uniqid().'/priority/high/', 'cronjob_worker-test', 'high');
//        }
//
//        for ($i =0; $i < 500; $i++) {
//            startWorker('test/id/'.uniqid().'/priority/low/', 'cronjob_worker-test', 'low');
//        }

        Logger::add(['startWorker', 'testrun'], 'start test run done');
    }

}
