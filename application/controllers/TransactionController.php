<?
use TransactionCollection\BundleCollectionsRequest;
use TransactionCollection\BundleCollectionsService;
use TransactionCollection\UnBundleCollectionRequest;
use TransactionCollection\UnBundleCollectionService;

ini_set('memory_limit', '8000M');

	if(ini_get('max_execution_time') < 300)
		ini_set('max_execution_time', 300);

	if(ini_get('max_execution_time') < 300)
		ini_set('max_execution_time', 300);

	class TransactionController extends GlobalController {

		/**
		 * @var Transaction
		 */
		public $transaction;

		public function preDispatch() {
			$this->transaction = new Transaction();

			if($this->view->Breadcrumbs()->count() == 1)
				$this->view->Breadcrumbs()->addCrumb('Financieel');
		}
		
		public function creditNoteAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Authorisatie credit nota\'s');
			
		}

		public function echoTransactionImportReportsAction(){
			$this->disableView();

			ini_set('display_errors', false);

			include('library/EmailQueue.php');

			$e = new Emails();

			foreach($e->fetchAll($e->select()->where('subject = ?', 'Ingelezen betalingen')) as $email)
				new EmailDisplay(false, $email);

		}


		public function transactionImportStornoFixAction(){

			$select = db()->select()

				->from(array('t' => 'transactions'), array('id', 'invoice'))
				->joinLeft(array('i' => 'invoices'), 'i.id = t.invoice', false)
				->joinLeft(array('irun' => 'invoices_run'), 'irun.id = t.invoicerun', array('type'))
				->joinLeft(array('it' => 'invoice_totals'), 'it.invoice = t.invoice', array('amount'))
				->joinLeft(array('ic' => 'invoice_custom'), 'ic.id = i.customid', array('sum' => 'SUM(icr.`price`)'))
				->joinLeft(array('icr' => 'invoice_custom_rows'), 'icr.custom = i.customid', false)
				->joinLeft(array('uo' => 'users_objects'), 'uo.id = i.users_objects', array('customer'))
				->where('t.user = 0 AND t.investor = 0 AND t.relation IS NULL')
				//->where('it.amount > 0 OR ic.credit = 0')
				//->having('sum > 0 OR sum IS NULL')
				->where('t.`type` = ?', 'd')
				->where('uo.customer IS NOT NULL')
				->where('t.invoice IS NOT NULL')
				->group('t.id');

			$t_model = new Transactions();
			$tp_model = new TransactionsPayments();
			foreach(db()->fetchAll($select) as $transaction){

				$t_row = $t_model->getById($transaction['id']);

				$type = 'd';

				if(!is_null($transaction['amount']))
					$type = $transaction['amount'] > 0 ? 'c' : 'd';

				if(!is_null($transaction['sum']))
					$type = $transaction['sum'] > 0 ? 'c' : 'd';

				if($transaction['type'] != 'final')
					$t_row->type = $type;

				$t_row->user = $transaction['customer'];

				if($t_row->type == 'c'){

					$transactions_payments = $tp_model->matchAll(array('transaction' => $t_row->id));

					if(count($transactions_payments) == 1){
						$t_row_new = $t_model->createRow();

						$t_row_new->amount = $t_row_new->payed = $t_row->payed;
						$t_row_new->description = $t_row->description;
						$t_row_new->type = 'd';
						$t_row_new->date = $t_row->date;

						//$id = $t_row_new->save();

						$transactions_payments[0]->transaction = $id;
						$transactions_payments[0]->save();

						$t_row->description = '';

					}
					//$t_row->payed = 0;
				}

				//$t_row->save();
			}
		}
		protected function debtorOverviewGetData($project_id){
			$select = db()->select()
				->from(['dcov' => 'debtor_control_overview_values'], ['value', 'type'])
				->joinLeft(['dcop' => 'debtor_control_overview_periods'], 'dcop.id = dcov.period_id', ['date'])
				->order('dcop.date')
				->where('dcov.rent_only = ?', false)
				->where('dcov.project_id = ?', $project_id)
				->where('dcop.date > ?', date('Y-m-d', strtotime('-9 months')))
				->where('dcop.date < ?', date('Y-m-d'))
			;

			$data = [];

			foreach(db()->fetchAll($select) as $period){
				$period_name = strftime('%b %Y', strtotime($period['date']));
				if(!isset($data[$period_name]))
					$data[$period_name] = [];

				$data[$period_name][$period['type']] = $period['value'];
			}

			return $data;
		}

		protected function debtorOverviewTypes($data){
			$types = ['title' => ['title' => '', 'width' => 'xlarge']];
			foreach($data as $period_name => $period_data)
				$types[$period_name] = ['title' => $period_name, 'width' => 'medium right'];

			return $types;
		}

		protected function debtorOverviewFormatData($source_data){
			if(Settings::get('general_company_shortname') == 'ravel')
				$data = [
					['title' => 'Omzet', 'type' => 'invoiced_between_dates'],
					['title' => 'Ontvangen', 'type' => 'payed_between_dates'],
					['title' => 'Voorgaande openstand', 'type' => 'unpayed_on_date_start'],
					['title' => 'Afgeschreven', 'type' => 'special_status_in_period'],
					['title' => 'Huidige maand', 'type' => 'unpayed_on_between_dates'],
					['title' => 'Openstand einde periode', 'type' => 'unpayed_on_date_end'],
					['title' => '', 'type' => 'spacer1'],
					['title' => 'Potentiele omzet', 'type' => 'potential_rent'],
					['title' => 'Daadwerkelijke omzet', 'type' => 'invoiced_between_dates'],
					['title' => 'Leegstand verlies', 'type' => 'vacancy_loss'],
					['title' => '', 'type' => 'spacer2'],
					['title' => '3% marge regel', 'type' => 'margin_rule'],
					['title' => 'Leegstand verlies', 'type' => 'vacancy_loss'],
					['title' => 'Netto marge overschot/tekort', 'type' => 'net_margin_deficit']
				];
			else
				$data = [
					['title' => 'Omzet', 'type' => 'invoiced_between_dates'],
					['title' => 'Ontvangen', 'type' => 'payed_between_dates'],
					['title' => 'Voorgaande openstand', 'type' => 'unpayed_on_date_start'],
					['title' => 'Afgeschreven', 'type' => 'special_status_in_period'],
					['title' => 'Huidige maand', 'type' => 'unpayed_on_between_dates'],
					['title' => 'Openstand einde periode', 'type' => 'unpayed_on_date_end'],
					['title' => '', 'type' => 'spacer1'],
					['title' => 'Potentiele omzet', 'type' => 'potential_rent'],
					['title' => 'Daadwerkelijke omzet', 'type' => 'invoiced_between_dates'],
					['title' => 'Leegstand verlies', 'type' => 'vacancy_loss']
				];

			$periods = [];
			foreach($source_data as $period_name => $period_data)
				$periods[$period_name] = false;

			foreach($data as $data_key => $type) {
				$data[$data_key] = array_merge(['title' => $type['title']], $periods);

				foreach($source_data as $period_name => $period_data) {
					foreach($period_data as $period_data_type => $period_data_value)
						if($period_data_type == $type['type'])
							$data[$data_key][$period_name] = $period_data_value;
				}
			}

			return $data;
		}

		protected function controleAccountTypes($params){
			$t_lib = new Transaction();

			$rent_only = Settings::get('debtor_overviews_rental_component_only');

			$unpayed_on_date_start = $t_lib->unpayedTotalOnDate([
				'periods' => [['from' => false, 'till' => 0]],
				'period-id' => 0,
				'closed_date' => $params['date_start'],
				'project' => $params['project'],
				'sub_unpayed' => true,
				'rent_only' => $rent_only
			]);

			$unpayed_on_date_end = $t_lib->unpayedTotalOnDate([
				'periods' => [['from' => false, 'till' => 0]],
				'period-id' => 0,
				'closed_date' => $params['date_end'],
				'project' => $params['project'],
				'sub_unpayed' => true,
				'rent_only' => $rent_only
			]);

			$invoiced_between_dates = $t_lib->invoicedBetweenDates($params['date_start'], $params['date_end'], ['project' => $params['project'], 'rent_only' => $rent_only]);

			$payed_between_dates = $t_lib->payedBetweenDates($params['date_start'], $params['date_end'], ['project' => $params['project'], 'rent_only' => $rent_only]);

			$corporation_ids = [];
			foreach(Components_Report::getCorporationLabels() as $corporation_id => $corporation_name)
				$corporation_ids[] = $corporation_id;

			if(date('m', $params['date_start']) == date('m', $params['date_end'])){
				$invoice_period_textual = strftime('%B', $params['date_start']);
			} else {
				$invoice_period_textual = strftime('%B', $params['date_start']) . ' t/m ' . strftime('%B', $params['date_end']);
			}


			$payed_period_textual = date('d-m', $params['date_start']) . ' t/m ' . date('d-m', $params['date_end']);

			$types = [
				[
					'title' => 'Openstand op: ' . date('d-m-Y', $params['date_start']),
					'amount' => $unpayed_on_date_start['unpayed'],
					'link' => 'transaction/unpayed-on-date/date/' . date('d-m-Y', $params['date_start']) . '/'
				],
				[
					'title' => 'Gefactureerd in boekperiode: ' . $invoice_period_textual,
					'amount' => $invoiced_between_dates,
					'link' => 'components/report-object/year/' . date('Y', $params['date_start']) . '/start_month/' . date('n', $params['date_start']) . '/end_month/' . date('n', $params['date_end']) . '/date_source_objectcomponents/false/'
				],
				[
					'title' => 'Ontvangsten in boekperiode: ' . $invoice_period_textual,
					'amount' => $payed_between_dates,
					'link' => 'transaction/matched-list/date_range_start/' . date('d-m-Y', $params['date_start']) . '/date_range_end/' . date('d-m-Y', $params['date_end'])
				],
				[	
					'title' => 'Openstand op: ' . date('d-m-Y', $params['date_end']),
					'amount' => $unpayed_on_date_end['unpayed'],
					'link' => 'transaction/unpayed-on-date/date/' . date('d-m-Y', $params['date_end']) . '/'
				],
				[	
					'title' => '[TIJDELIJKE TEST REGEL] Verschil',
					'amount' => ($unpayed_on_date_start['unpayed'] + ($invoiced_between_dates - $payed_between_dates)) - $unpayed_on_date_end['unpayed'],
					'link' => false
				]
			];	

			foreach($types as &$type){
				$type['date_start'] = $params['date_start'];
				$type['date_end'] = $params['date_end'];
			}

			return $types;
		}

		protected function controleAccountParams(){

			$params = [];

			$params['date_start'] = strtotime($this->_getParam('date_start') ?: date('d-m-Y', mktime(0, 0, 0, date('m') - 1, 1)));

			$params['date_end'] =	strtotime($this->_getParam('date_end') ?: date('d-m-Y', mktime(0, 0, 0, date('m'), 0)));


			if(!is_null($this->_getParam('project')))
				$params['project'] = $this->_getParam('project');

			return $params;
		}

		public function debtorOverviewAction(){
			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Debiteuren')
				->addCrumb('Export overzicht');

			$projects_select = db()->select()->from('projects', ['id', 'name']);

			foreach(db()->fetchAll($projects_select) as $project)
				$projects[$project['id']] = ['title' => $project['name']];

			if(is_null($this->getParam('project_id')))
				$this->setParam('project_id', array_keys($projects)[0]);

			$source_data = $this->debtorOverviewGetData($this->getParam('project_id'));
			$types = $this->debtorOverviewTypes($source_data);
			$data = $this->debtorOverviewFormatData($source_data);

			$money_types = [];
			foreach($types as $type_name => $type)
				if(strpos($type['width'], 'right') !== false)
					$money_types[] = $type_name;


			$this->view->listView = $this->view->ListView($data)
				->setTypes($types)

				->addLegend('overview-legend')

				->addFormat($money_types, function($value){
					if(abs($value) > 0)
						return '&euro; ' . number_format($value, 0, '', '.');
					else
						return '-';
				})

				->setFilters(array(
					'project_id' => array('renderSeparately' => true, 'title' => 'Project', 'custom_options' => $projects, 'hideCount' => true, 'preApplied' => true, 'custom_options_only' => true)
				))

				->render($this);
		}

		public function controlAccountAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Debiteuren')
				->addCrumb('Controle overzicht');

			$params = $this->controleAccountParams();

			if($params['date_start'])
				$this->_setParam('date_start', date('d-m-Y', $params['date_start']));
			
			if($params['date_end'])
				$this->_setParam('date_end', date('d-m-Y', $params['date_end']));
			
			$types = $this->controleAccountTypes($params);

			$t_lib = new Transaction();
			$data = $types;

			$projects_select = db()->select()->from('projects', ['id', 'name']);

			foreach(db()->fetchAll($projects_select) as $project)
				$projects[$project['id']] = ['title' => $project['name']];

			$this->view->listView = $this->view->ListView($data)
				->setTypes([
					'date_start' => ['title' => 'Begindatum', 'width' => 'small', 'group_equal_rows' => true],
					'date_end' => ['title' => 'Einddatum', 'width' => 'small', 'group_equal_rows' => true],
					'title' => ['title' => 'Aantal dagen', 'width' => 'xxxlarge'],
					'amount' => ['title' => 'Bedrag', 'width' => 'small'],
					'details' => ['title' => 'Details', 'width' => 'button details large'],
				])

				->addFormat(['date_start', 'date_end'], function($value){
					return date('d-m-Y', $value);
				})

				->addFormat('amount', 'money')

				->addLegend('control-account-legend')

				->addFormat('details', function($value, $item){
					return '<a href="' . $item['link'] . '" target="_blank">Details</a>';
				})

				->setFilters(array(
					'date_start' => array('renderSeparately' => true, 'title' => 'Begindatum', 'type' => 'date', 'preApplied' => true),
					'date_end' => array('renderSeparately' => true, 'title' => 'Einddatum', 'type' => 'date', 'preApplied' => true),
					'project' => array('renderSeparately' => true, 'title' => 'Project', 'custom_options' => $projects, 'hideCount' => true, 'preApplied' => true, 'custom_options_only' => true)
				))				

				->render($this);			
		}

		protected function unpayedOnDatePeriods($date = false){

			$date = $date ?: mktime(0, 0, 0);


			$periods = [
				['id' => 0, 'title' => 'Huidig (0-30 dagen)', 'from' => 0, 'till' => 1],
				['id' => 1, 'title' => '31-60 dagen', 'from' => 1, 'till' => 2],
				['id' => 2, 'title' => '61-90 dagen', 'from' => 2, 'till' => 3],
				['id' => 3, 'title' => '91+ dagen', 'from' => 3, 'till' => 0],
				['id' => 4, 'title' => 'Totaal', 'from' => 0, 'till' => 0]
			];	

			foreach($periods as &$period){
				$period['date'] = $date;
				$period['items'] = [];
				$period['period_description'] = strftime('%B', strtotime('-' . $period['from'] . ' months', $date));

				if($period['till'] == 0 && $period['from'] > 0)
					$period['period_description'] = strftime('%B', strtotime('-' . $period['from'] . ' months', $date)) . ' en ouder';
				elseif($period['till'] == 0 && $period['from'] == 0)
					$period['period_description'] = 'alle perioden';
			}

			return $periods;
		}

		protected function unpayedOnDateParams(){
			$date = $this->_getParam('date') ? strtotime($this->_getParam('date')) : false;
			$project = !is_null($this->_getParam('project')) ? $this->_getParam('project') : false;

			return ['date' => $date, 'project' => $project, 'rent_only' => Settings::get('debtor_overviews_rental_component_only')];
		}		

		public function unpayedOnDateAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Debiteuren')
				->addCrumb('Oudendomsbalans');

			$params = $this->unpayedOnDateParams();

			$this->_setParam('date', date('d-m-Y', $params['date'] ?: mktime(0, 0, 0)));
			$periods = $this->unpayedOnDatePeriods($params['date']);

			$t_lib = new Transaction();
			$data = $t_lib->unpayedTotalOnDate([
				'periods' => $periods,
				'closed_date' => $params['date'],
				'project' => $params['project'],
				'rent_only' => $params['rent_only'],
				'sub_unpayed' => true
			]);

			$projects_select = db()->select()->from('projects', ['id', 'name']);

			foreach(db()->fetchAll($projects_select) as $project)
				$projects[$project['id']] = ['title' => $project['name']];

			$this->view->listView = $this->view->ListView($data)
				->setTypes([
					'date' => ['title' => 'Datum', 'width' => 'small', 'group_equal_rows' => true],
					'title' => ['title' => 'Aantal dagen', 'width' => 'large'],
					'period_description' => ['title' => 'Boekperiode', 'width' => 'large'],
					'unpayed' => ['title' => 'Openstaand', 'width' => 'large'],
					'details' => ['title' => 'Details', 'width' => 'button details large'],
				])

				->addFormat('date', function($value){
					return date('d-m-Y', $value);
				})

				->addFormat('period_description', function($value){
					return ucfirst($value);
				})

				->addFormat('unpayed', 'money')

				->addFormat('details', function($value, $item) use ($params){
					$date = date('d-m-Y', $params['date'] ?: mktime(0, 0, 0));
					return '<a href="transaction/unpayed-on-date-details/id/' . $item['id'] . '/date/' . $date . ($params['project'] > 0 ? '/project/' . $params['project'] : '') . '/">Details</a>';
				})

				->setFilters(array(
					'date' => array('renderSeparately' => true, 'title' => 'Datum', 'type' => 'date', 'preApplied' => true),
					'project' => array('renderSeparately' => true, 'title' => 'Project', 'custom_options' => $projects, 'hideCount' => true, 'preApplied' => true, 'custom_options_only' => true)
				))				

				->render($this);			
		}

		public function unpayedOnDateDetailsAction(){
			$id = $this->_getParam('id');

			$params = $this->unpayedOnDateParams();

			$this->_setParam('date', date('d-m-Y', $params['date'] ?: mktime(0, 0, 0)));
			$periods = $this->unpayedOnDatePeriods($params['date']);

			if($params['project'] > 0){
				$projects_select = db()->select()->from('projects', ['id', 'name'])->where('id = ?', $params['project']);
				$project_row = db()->fetchRow($projects_select);	
			}

			$this->view->Breadcrumbs()
				->addCrumb('Debiteuren')
				->addCrumb('Oudendomsbalans', 'unpayed-on-date')
				->addCrumb('Oudendomsbalans details: ' . $periods[$id]['title'] . ' per ' . date('d-m-Y', $params['date']) . ($params['project'] > 0 ? ' voor project ' . $project_row['name'] : ''));

			$t_lib = new Transaction();
			$data = $t_lib->unpayedTotalOnDate([
				'periods' => $periods,
				'closed_date' => $params['date'],
				'period-id' => $id,
				'rent_only' => $params['rent_only'],
				'project' => $params['project'],
			]);

			$this->view->url_params = $params;

			$this->view->listView = $this->view->ListView($data['items'], [])
				->setTypes([
					'object' => ['title' => 'Object', 'width' => 'xlarge'],
					'user' => ['title' => 'Huurder', 'width' => 'large'],
					'invoice' => ['title' => 'Factuur', 'width' => 'large button pdf'],
					'days' => ['title' => 'Dagen vervallen', 'width' => 'small'],
					'amount' => ['title' => 'Factuur bedrag', 'width' => 'medium right'],
					'unpayed' => ['title' => 'Openstaand', 'width' => 'medium right']
				])

				->addFormat(['amount', 'unpayed'], 'money')

				->addFormat('user', function($value, $item){
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $item['buildname'] . '</a>' : '-';
				})

				->addFormat('object', function($value, $item){
					return '<a href="object/edit/id/' . $value.'" target="_blank">' . $item['address'] . '</a>';
				})

				->addFormat('invoice', function($value, $item){
					return '<a href="invoice/export/id/' . $value.'" target="_blank">' . $item['identifier'] . '</a>';
				})

				->addLegend('unpayed-on-date-details-legend')

				->setFilters(array(
					'object' => array('type' => 'input'),
					'user' => array('type' => 'input'),
					'invoice' => array('type' => 'input')
				))	

				->addTotals(['amount', 'unpayed'])



				->render($this);			
		}		

		public function addBankGroupedCollectionAction(){
			$this->disableView();

			$t_lib = new Transaction();
			$t_lib->addBankGroupedCollection($this->_getParam('id'));
		}

		public function markRunAsPayedAction(){


			$id = $this->view->id = $this->_getParam('id');
			$execute = $this->_getParam('execute') && $this->_getParam('id') ? true : false;

			if(!$execute) return;

			$grouped_collection_only = $this->_getParam('grouped-collection-only') ? true : false;

			$t_model = new Transactions();

			$select = db()->select()
				->from(array('t' => 'transactions'))
				->joinLeft(array('tcp' => 'transactions_collection_payments'), 'tcp.invoice_id = t.invoice', false)
				->where('t.payed < t.amount')
				->where('t.invoicerun = ?', $id);

			if($grouped_collection_only)
				$select->where('tcp.id IS NOT NULL');

			foreach(db()->fetchAll($select) as $transaction_row){

				$t_model
					->getById($transaction_row['id'])
					->setFromArray(array('payed' => $transaction_row['amount'], 'closed' => true))
					->save();

				TransactionsPayments::add(array(
					'transaction' => $transaction_row['id'],
					'user' => nullValue(),
					'date' => $transaction_row['date'],
					'status' => $grouped_collection_only ? 'grouped_collection' : 'run_set_as_payed',
					'direction' => $transaction_row['type'] == 'c' ? 'incoming' : 'outgoing',
					'amount' => $transaction_row['amount'] - $transaction_row['payed'],
					'bankaccount_customer' => false,
					'bankaccount' => false
				));
			}

			$this->_redirect('invoice/index', array('status' => 2));
		}

		public function rematchUnidentifiedAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Niet gematchte betalingen', 'transaction/unidentified/')
				->addCrumb('Automatisch matchen opnieuw uitvoeren');

			$t_lib = new Transaction();

			$this->view->retrying_matching = true;
			$this->view->transactions = $t_lib->retryMatching();

			$this->render('unidentified/rematch');
		}
		
		public function uploadAction(){

			$file_formats = array(
				// mt940 file format
				'mt940' => array(
					// should use the 'uploaded' parser
					'uploaded' => array('swi', 'swift', 'mt940', '940', 'inc', 'sta', 'sta.txt')
				),
				// coda format
				'coda' => [
					// should use the 'uploaded' parser
					'uploaded' => [ 'cod', 'bc2', 'coda' ],
				],
				// csv format
				'csv' => array(
					// should use the 'uploaded' parser
					'uploaded' => array('csv')
				),
			);

			if(Settings::get('bankimport_accountview_xml_upload'))
				$file_formats['xml'] = array(
					// should use the 'detect' parser
					'detect' => array('xml')
				);

			$this->view->file_formats = $file_formats;

            if ($this->getRequest()->isPost()) {
                $transactionsImportFilesModel = new TransactionsImportFiles();
                try {
                    $handleUploadedFileService = new \Transactions\Import\HandleUploadedFileService(
                        $transactionsImportFilesModel
                    );

                    $handleUploadedFileService
                        ->execute(
                            $_FILES['mt940']['tmp_name'],
                            $_FILES['mt940']['name'],
                            $_POST['corporation'],
                            $file_formats
                        );
                } catch (Exception $e) {
                    $this->view->valid = false;
                }

                $this->view->valid = true;
                $this->view->imported = true;
            }

			$select = db()->select()
				->from(array('cba' => 'corporations_bank_accounts'), 		array('*'))
				->join(array('c' => 'corporations'),
				'c.id = cba.corporation_id',					array('corp_name' => 'name'))
				->where('(cba.is_current = 1) OR (cba.is_active = 1)')
				->where('`type` IN (' . implode_for_where_in(array(	'abn', 'ing',  'insingergilissen' ,'rabobank', 'sns', 'lanschot', 'triodos', 'deutsche', 'kbc', 'bnp', 'handel', 'VOWA')) . ')');

			$banks =  db()->fetchAll($select);
 			foreach($banks as $bank_id => $bank){
				$select = db()->select()
					->from(['ti' => 'transactions_import'], ['date'])
					->where('ti.account = ?', $bank['id'])
					->order('ti.date DESC');

                if ($bank['number']) {
                    $ibanParser = new Iban_Parser($bank['iban']);
                    if ($ibanParser->Verify()) {
                        $bank['number'] = (int) $ibanParser->Account();
                    }

                    $select->orWhere('ti.account_number = ?', $bank['number']);
                }

				$banks[$bank_id]['date'] = db()->fetchOne($select);
			}

 			
			$this->view->banks = $banks;

			

			$this->view->Breadcrumbs()
				->addCrumb('Bank transacties importeren');
			
		}

		public function importWorkerAction(){
			$this->disableView();
			startWorker('transaction-import');
		}

		public function clearInvestorProvisionCacheAction(){
			$this->disableView();

			$i = new Investors();
			$period_offset = $this->_getParam('period_offset') ? $this->_getParam('period_offset') : 0;
			$i->paymentDay = $paymentDay = $this->_getParam('payment_day');
			$i->filterProject = $project_id = $this->_getParam('project_id') ? $this->_getParam('project_id') : false;
			$i->clearProvisionCache($period_offset);

			$url_params = $this->getAllUrlParams();

			$this->_redirect('transaction/investor-provision', $url_params);
		}

		public function investorProvisionRegeneratingStatusAction(){
			$this->disableView();

			$period_offset = $this->_getParam('period_offset') ? $this->_getParam('period_offset') : 0;
			$project_id = $this->_getParam('project-id') ? $this->_getParam('project-id') : false;
			include 'library/PIDLock.php';
			$pid_lock = new PIDLockStatus('investor-provision-cache', ['period_offset' => $period_offset, 'project_id' => $project_id]);

			if(!$pid_lock->isLocked())
				echo json_encode('1');
		}

		public function investorProvisionAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Belegger-beheerprovisieoverzicht');

			$i = new Investors();

			$this->view->period_offset = $period_offset = $this->_getParam('period_offset') ?: 0;
			$this->view->payment_day = $i->paymentDay = $payment_day = $this->_getParam('payment_day');
			$this->view->project_id = $i->filterProject = $project_id = $this->_getParam('project_id') ?: false;
			$this->view->condition = $condition = $this->getParam('condition') ?: 'show_due';
			$this->view->manage_type = $manage_type = $this->getParam('manage_type') ?: 'show_all';

			$this->view->projects = $i->getAllInvestorProjects();
			$this->view->onDate = $i->onDate = $this->onDate ?: false;
			$this->view->payment_days = $i->getAllPaymentDays();

			if(!is_null($this->_getParam('period_offset')))
				$this->view->investors = $i->getMonthlyProvision($period_offset, false);

			$this->view->investors = $this->filterQuarterInvestorsForNonQuarterPeriod($this->view->investors, $period_offset);

			$this->view->investors = $this->filterInvestorListForCondition($this->view->investors, $condition);
			$this->view->investors = $this->filterInvestorListForManageType($this->view->investors, $manage_type);

			$this->view->investors = $this->sortInvestors($this->view->investors);

			include 'library/PIDLock.php';
			$pid_lock = new PIDLockStatus('investor-provision-cache', ['period_offset' => $period_offset, 'project_id' => $project_id, 'payment-day' => $payment_day]);

			$this->view->regenerating = $pid_lock->isLocked() || $i->cacheReloading === true;
			$this->view->extraWidth = !$this->view->regenerating;

			$this->view->cacheTest = $i->getProvisionCacheAge($period_offset);

			$has_quarter_investors = false;
			if(is_array($this->view->investors))
				foreach($this->view->investors as $investor)
					if($investor['provisiondetails_interval'] == 'quarter')
						$has_quarter_investors = true;

			$this->view->hasQuarterInvestors = $has_quarter_investors;


			$this->render('provision/list');
		}

		protected function filterQuarterInvestorsForNonQuarterPeriod($investors, $period_offset){
			$i_model = new Investors();

			$period = $i_model->provisionPeriod($period_offset);
			$is_quarter_period = Investors::isDateQuarterPeriod($period['start']);

			if(is_array($investors))
				foreach($investors as $investor_key => $investor)
					if($investor['provisiondetails_interval'] == 'quarter' && $is_quarter_period === false)
						unset($investors[$investor_key]);

			return $investors;
		}

		private function sortInvestors($investors)
		{
			$sorted_array = [];
			if(is_array($investors))
				foreach($investors as $investor_key => $investor)
					$sorted_array[($investor['totals']['total'] == 0 ? 1 : 0) . '_' . $investor['name'] . '_' . $investor['id']] = $investor;

			ksort($sorted_array);

			return array_values($sorted_array);
		}

		protected function filterInvestorListForCondition($investors, $condition){

			if($condition == 'show_all') return $investors;

			$hide_send_and_zero_total = in_array($condition, ['show_due', 'show_due_with_payments', 'show_due_with_payments_higher_then_due']);
			$hide_zero_payments = in_array($condition, ['show_due_with_payments', 'show_due_with_payments_higher_then_due']);
			$hide_payments_less_then_due = in_array($condition, ['show_due_with_payments_higher_then_due']);

			if(is_array($investors))
				foreach($investors as $investor_key => $investor) {
					if ($hide_send_and_zero_total && $investor['provision_send'] == 1 && $investor['totals']['total'] == 0) {
						unset($investors[$investor_key]);
						continue;
					}

					if ($hide_zero_payments && $investor['totals']['payments'] == 0) {
						unset($investors[$investor_key]);
						continue;
					}

					if ($hide_payments_less_then_due && $investor['totals']['payments'] < $investor['totals']['due']) {
						unset($investors[$investor_key]);
						continue;
					}
				}

			return $investors;
		}

		protected function filterInvestorListForManageType($investors, $manage_type){

			if($manage_type == 'show_all') return $investors;

			if(is_array($investors))
				foreach($investors as $investor_key => $investor)
					if ($manage_type == 'technical_only' && $investor['manage_financial'] == 1){
						unset($investors[$investor_key]);
						continue;
					}

			return $investors;
		}

		public function investorProvisionOnDateAction(){

			$this->onDate = $this->_getParam('on-date');
			$this->investorProvisionAction();
		}

		public function importMatchAsCostsListAction(){
			$this->view->headLink()->appendStylesheet('media/style/transaction/import-match-list.css');

			$this->view->matchAsCosts = true;			

			$this->importMatchListAction();
		}

		protected function importDateFormatter($data){
            return (new \Transactions\ImportDateFormatterService())->execute($data);
		}

		protected function fixColumnWidth($columns){
			$max_characters_per_column = [];
			foreach($columns as $import_id => $import) {

				foreach ($import as $column_nr => $column_value) {
					if (!isset($max_characters_per_column[$column_nr]))
						$max_characters_per_column[$column_nr] = strlen($column_value);
					elseif ($max_characters_per_column[$column_nr] < strlen($column_value))
						$max_characters_per_column[$column_nr] = strlen($column_value);
				}
			}

			foreach($columns as $import_id => $import) {
				foreach ($import as $column_nr => $column_value)
					$import[$column_nr] = str_replace('$', "&euro;", str_pad($column_value, $max_characters_per_column[$column_nr], "_"));

				$columns[$import_id] = str_replace('_', "&#160", implode("&#160&#160&#160", $import));
			}

			return $columns;
		}

		public function importMatchListAction(){
			$this->view->Breadcrumbs()->addCrumb('Geïmporteerde betalingen' . ($this->view->matchAsCosts ? ' matchen als kosten' : ''));

			$this->view->headScriptHashed()->appendFile('media/javascript/transaction/import-match-delete-functions.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/UnidentifiedTransaction.js');

			$this->view->extraWidth = true;
			$match_as_costs = (bool) $this->view->matchAsCosts;

			if(is_null($this->_getParam('deleted')))
				$this->_setParam('deleted', '0');

			$select = db()->select()
				->from(['tip' => 'transactions_import_payments'], ['id', 'date', 'type', 'iban', 'hash_amount' => '(`amount` - `matched`)', 'amount' => 'tip.`amount` * IF(tip.`type` = "credit", 1, -1)', 'description', 'unmatched' => '(tip.`amount` - tip.`matched`) * IF(tip.`type` = "credit", 1, -1)', 'match_amount' => '(tip.`amount` - tip.`matched`) * IF(tip.`type` = "credit", 1, -1)', 'deleted', 'unmatched_reason', 'possible_duplicate', 'is_purchase_invoice_payment'])
				->joinLeft(['ti' => 'transactions_import'], 'ti.id = tip.transaction_import_id', ['import_date', 'import_id' => 'id'])
				->joinLeft(['cba' => 'corporations_bank_accounts'], 'cba.id = ti.account', ['account' => 'number', 'c_iban' => 'cba.iban'])
				->joinLeft(['c' => 'corporations'], 'c.id = cba.corporation_id', ['corporation_name' => 'c.name', 'corporation_id' => 'c.id'])
                ->having('unmatched != 0')
                ->where('tip.deleted = ?', $this->getParam('deleted'))
                ->where('c.archived = ? OR c.id IS NULL', 0)
                ->where('NOT (tip.deleted = 1 AND tip.deleted_reason IS NOT NULL)')
                ->order('tip.date')
				;

            if((new loginManager())->isProjectLimited()) {
                $corporationsIds = UsersProjects::getAllowedCorporationIds();

                if(count($corporationsIds) > 0) {
                    $select->where('c.id IN (' . implode_for_where_in($corporationsIds) . ')');
                } else {
                    $select->where('c.id IS NULL');
                }
            }



			$data = db()->fetchAssoc($select);

			$ibanData = [];

			$tip_model = new TransactionsImportPayments();
			$service = new \Transactions\Match\HashService();

			$data = $this->importDateFormatter($data);

			foreach($data as &$data_row){

				if($data_row['amount'] < 0)
					$data_row['listview_row_classes'] = array('status', 'orange');

				if($data_row['possible_duplicate'])
					$data_row['listview_row_classes'] = array('status', 'red');

				$data_row['positive_negative'] = $data_row['amount'] < 0 ? 'negative' : 'positive';

				$data_row['fully_matched'] = $data_row['unmatched'] == 0;

				if($data_row['unmatched'] == 0)
					$data_row['listview_row_classes'] = array('status', 'disabled');

				$p_status = $tip_model->getStatus($data_row);
				$data_row['payment_status'] = $p_status['name'];

				$hash = $service->execute(
					new \Transactions\Match\HashRequest(
						$data_row['id'],
						$data_row['unmatched'],
						$data_row['deleted'],
						$data_row['type'],
						$data_row['date'],
						$data_row['hash_amount'],
						$data_row['description'],
						$data_row['possible_duplicate']
					)
				);

				$data_row['hash'] = $hash;

				$ibanData[$data_row['account']] = [
					'title' => $data_row['corporation_name'] . ' - ' . $data_row['c_iban']
				];
			}

			$types = array(
				'corporation_id' => array('title' => '', 'width' => 'hidden corporation_id'),
				'fully_matched' => array('title' => '', 'width' => 'hidden'),
				'account'		=> array('title' => 'Rekening', 'width' => 'hidden'),
				'import_id'	=> array('title' => 'Afschrift', 'width' => 'hidden', 'type' => 'date'),
                'payment_status' => [
                    'title' => 'Matchingstatus',
                    'width' => 'hidden',
                    'format_current_page_items_only' => true,
                ],
				'date'			=> array('title' => 'Betaaldatum', 'width' => 'medium', 'type' => 'date'),
				'description'	=> array('title' => 'Omschrijving', 'width' => $this->view->matchAsCosts ? 'xxlarge multiline' : 'xxxlarge multiline'),
				'status_icon' 	=> ['title' => '', 'width' => 'xxxsmall'],
				'type'			=> array('title' => '', 'width' => 'hidden type'),
				'positive_negative'	=> array('title' => '', 'width' => 'hidden type'),
				'hash'	=> 		array('title' => '', 'width' => 'hidden hash'),
			);

			if(!$this->view->matchAsCosts){
				$types += array(
					'amount'		=> array('title' => 'Totaalbedrag', 'width' => (Settings::get('software_XXL') ? 'medium' : 'small') . ' right amount'),
					'unmatched'		=> array('title' => 'Ongematched', 'width' => (Settings::get('software_XXL') ? 'medium' : 'small') . ' right unmatched'),
					'unmatched_reason'	=> array('title' => '', 'width' => 'xxxxsmall'),
                    'search' => [
                        'title' => 'Zoeken',
                        'width' => 'large search',
                        'format_current_page_items_only' => true
                    ],
					'invoice'		=> array('title' => 'Facturen', 'width' => 'medium invoice'),
					'identify'		=> array('title' => 'Koppelen', 'width' => 'xsmall identify'),
				);			
			} else {
				$types += array(
					'amount'		=> array('title' => 'Totaalbedrag', 'width' => 'xsmall right amount'),
					'unmatched'		=> array('title' => 'Ongematched', 'width' => 'xsmall right unmatched'),
					'spacer2'		=> array('title' => '', 'width' => 'xxxxsmall'),
					'unmatched_reason'	=> array('title' => '', 'width' => 'hidden'),
					'search'		=> array('title' => 'Object', 'width' => 'medium search'),
					'component'		=> array('title' => 'Component', 'width' => 'small component'),
					'match_amount'	=> array('title' => 'Bedrag', 'width' => 'small match_amount'),
					'identify'		=> array('title' => 'Boeken', 'width' => 'xsmall identify'),
					'divide'		=> array('title' => 'Verdelen', 'width' => 'xsmall'),
				);	
			}

 				$types += [
					'delete'		=> ['title' => 'Verwijderen', 'width' => 'xsmall delete'],
					'blacklist'		=> ['title' => 'Blokkeren', 'width' => 'xxsmall blacklist']
				];

			$types += [
				'iban' => ['title' => 'IBAN', 'width' => 'xlarge', 'type' => 'date']
			];

			if(isset($types['component'])){
				$component_fetch = function($type = false){
					$select = db()->select()
						->from('components', array('id', 'name'))
						->where('deleted = ?', false)
						->order('name');

					if($type)
						$select->where('type = ?', $type);

					return db()->fetchAll($select);
				};

				$all_components = array(
					'Particulier' => $component_fetch('particulier'),
					'Commercieel' => $component_fetch('commercieel'),
					'Overig' => $component_fetch()
				);
			}

			$params = array();

			$this->view->listView = $this->view->ListView($data, $params)
				->setTypes($types)

				->addFormat('account', 'bank')

				->addFormat('iban', 'iban')

				->addFormat(array('delete', 'blacklist'), 'checkbox')

				->addFormat('unmatched_reason', function($value){
					if($value == 'wrong_bankaccount')
						return '<span style="display: inline-block; width: 20px" title="Deze betaling is niet automatisch gematched doordat deze huurder naar de verkeerde bankrekening betaald heeft.">&nbsp;</span>';
					else
						return '';
				})

				->addFormat('status_icon', function($value, $item){
					$html = '';
					if($item['possible_duplicate'])
						$html .= '<span style="display: inline-block; width: 20px" title="<h1><b>Let op!</b></h1>Deze betaling is mogelijk een duplicaat van een eerder ingelezen betaling, waarschijnlijk door een incidentele foutieve aanlevering door de bank.<br /><br />Indien deze betaling inderdaad dubbel aangeboden is dan kan deze verwijderd worden.">&nbsp;</span>';

					if($item['is_purchase_invoice_payment'] && Settings::get('software_type') != 'energy')
						$html .= '<span style="display: inline-block; width: 20px" title="<h1><b>Overboeking inkoopfactuur</b></h1>Deze betaling wordt herkend als een betalingsopdracht voor een inkoopfactuur vanuit Omniboxx.">&nbsp;</span>';

					return $html;
				})

				->addFormat('search', function($value, $item) use ($match_as_costs){

					if($match_as_costs)
						$hint = 'Zoeken op adres';
					else
						$hint = 'Zoeken op achternaam, bedrijfsnaam, straatnaam, bedrag of naam van een belegger';

                    $suggestion = false;
                    $suggestionName = '';

                    $findSuggestion = !$match_as_costs && $item['iban'] && abs($item['unmatched']) > 0;

                    if ($findSuggestion) {
                        $cleanIbanString = strtoupper(str_replace(' ', '', $item['iban']));

                        $suggestionRowSelect = db()->select()
                            ->from(['ba' => 'bank_account'], false)
                            ->joinLeft(
                                ['u' => 'users'],
                                'u.id = ba.map_to AND ba.type IN ("user", "user_secondary")',
                                ['userId' => 'id', 'userName' => 'rendered_name']
                            )
                            ->joinLeft(
                                ['i' => 'investor'],
                                'i.user = u.id',
                                ['investorId' => 'id', 'investorName' => 'name']
                            )
                            ->joinLeft(
                                ['c' => 'company'],
                                'c.id = ba.map_to AND ba.type IN ("company", "company_secondary")',
                                ['companyDummyUserId' => 'dummy_user', 'companyName' => 'name']
                            )
                            ->where('UCASE(REPLACE(ba.`iban`, " ", "")) = ?', $cleanIbanString);

                        $suggestionRow = db()->fetchRow($suggestionRowSelect);

                        if($suggestionRow['companyDummyUserId']){
                            $suggestion = $suggestionRow['companyDummyUserId'];
                            $suggestionName = $suggestionRow['companyName'];
                            $suggestionType = 'company';
                        } elseif($suggestionRow['investorId']){
                            $suggestion = $suggestionRow['investorId'];
                            $suggestionName = $suggestionRow['investorName'];
                            $suggestionType = 'investor';
                        } elseif($suggestionRow['userId']){
                            $suggestion = $suggestionRow['userId'];
                            $suggestionName = $suggestionRow['userName'];
                            $suggestionType = 'user';
                        }
                    }

					return '<span title="' . $hint . '" noHintButton="true">
						<input autocomplete="off" placeholder="Zoeken"' . ($suggestion > 0 ? ' suggestion="' . $suggestion . '" value="' . $suggestionName . '" . suggestionType="' . $suggestionType . '"' : '') . ' />
					</span>';
				})

				->addFormat('invoice', function(){
					return '<span>
						<input autocomplete="off" placeholder="Factuur" />
					</span>';
				})

				->addFormat('positive_negative', function($value){
					return $value == 'positive' ? 'Positieve bedragen' : 'Negatieve bedragen';
				})

				->addFormat('fully_matched', function($value){
					return $value ? 'Volledig gematched' : 'Niet (volledig) gematched';
				})

				->addFormat('match_amount', 'money_input')	

				->addFormat('deleted', 'bool')			

				->addFormat('description', function($value){

					$value_parts = array();
					foreach(explode(' ', $value) as $value_part){
						if(strlen($value_part) > 5){
							$value_split_parts = str_split($value_part, 5);

							$value_part = '';
							foreach($value_split_parts as $value_split_part)
								$value_part .= $value_split_part . "&#8203;";

						}

						$value_parts[] = $value_part;
					}

					$value = implode(' ', $value_parts);
				
					return '<span noHintButton="true" class="forceAutoHint description" title="' . nl2br($value) . '">' . $value . '</span>';
				})

				->addFormat('identify', function(){
					return '<a class="xxsmall button inactive identify" privilege="action:identify" resource="controller:transaction" hasacl="true" href="transaction/identify" nopositon="true">Koppelen</a>';
				})

				->addFormat('payment_status', function($value, $item) use ($tip_model){
					$status = $tip_model->getStatus($item);
					return $status ? $status['label'] : '-';
				})

				->addFormat('divide', function(){
					return '<span class="xxsmall button wrench divide" privilege="action:identifyCost" resource="controller:transaction" hasacl="true" nopositon="true">Verdelen</span>';
				})

				->addFormat('import_id', function($value, $row) {
					return $row['statement'];
				})

				->addFormat('component', function() use ($all_components){
					$select = '<select class="component">';

					foreach($all_components as $type => $components){
						$select .= '<optgroup label="' . $type . '">';

						foreach($components as $component){
							$select .= '<option value="' . $component['id'] . '">' . $component['name'] . '</option>';
						}
						$select .= '</optgroup>';
					}

					$select .= '</select>';

					return $select;
				})

				->addFormat(array('date', 'sent'), 'date')

				->addFormat(array('amount', 'unmatched'), 'moneyspan')

				->addLegend('import-match-list-legend')

				->setFilters([
					'amount' => ['type' => 'range'],
					'unmatched' => ['type' => 'range'],
					'account' => ['renderSeparately' => true, 'title' => 'Rekening', 'custom_options' => $ibanData],
					'import_id' => ['renderSeparately' => true, 'title' => 'Afschrift', 'order_by_title' => 'DSC'],
					'positive_negative' => ['renderSeparately' => true, 'title' => 'Bedragen'],
                    'deleted' => [
                        'renderSeparately' => !Settings::get('software_XXL'),
                        'title' => 'Verwijderde tonen',
                        'show_all_disabled' => true,
                        'custom_options' => ['0' => ['title' => 'Nee'], '1' => ['title' => 'Ja']],
                        'preApplied' => true,
                        'hideCount' => true,
                    ],
					'date' => ['type' => 'date_range'],
					'iban' => ['type' => 'input'],
					'description' => ['type' => 'input', 'comparison_operator' => 'in_raw']
				])

				->setOptions(array(
					'paginator_items_per_page' => 300
				))

				->render($this);


		}

		public function matchedListAction(){
			$this->view->extraWidth = true;

			$this->view->Breadcrumbs()->addCrumb('Overzicht gematchte betalingen');

			$u_lib = new User();
			
			$this->view->startDate = $startDate = max(strtotime('27-01-2016'), strtotime('-1 years'));

			$select = db()->select()
				->from(['tip' => 'transactions_import_payments'], ['date', 'type', 'iban', 'amount' => 'tip.`amount` * IF(tip.`type` = "credit", 1, -1)', 'description', 'match_amount' => '(tip.`amount` - tip.`matched`) * IF(tip.`type` = "credit", 1, -1)', 'deleted'])
				->joinLeft(['ti' => 'transactions_import'], 'ti.id = tip.transaction_import_id', ['import_date', 'import_id' => 'id'])
				->joinLeft(['cba' => 'corporations_bank_accounts'], 'cba.id = ti.account', ['account' => 'CONCAT(c.name, " - ", cba.iban)'])
				->joinLeft(['c' => 'corporations'], 'c.id = cba.corporation_id', false)
                ->joinLeft(['p' => 'projects'], 'p.corporation = c.id OR p.investor_corporation = c.id OR p.rental_corporation = c.id', false)
				->joinLeft(['tp' => 'transactions_payments'], 'tp.transactions_import_payments_id = tip.id', ['id', 'match_date' => 'date_created', 'matched' => '(tp.`amount`/100) * IF(tip.`type` = "credit", 1, -1)', 'match_user' => 'user', 'investor_payed'])
				->joinLeft(['t' => 'transactions'], 't.id = tp.transaction', ['user', 'investor'])
				->joinLeft(['i' => 'invoices'], 'i.id = t.invoice', ['match_invoice' => 'id', 'match_invoice_id' => 'CONCAT(ir.identifier, ".", IF(CHAR_LENGTH(i.identifier) < 4, LPAD(i.identifier,4,"0"), i.identifier))'])
				->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', false)
				->group('tp.id')
				->where('tp.id IS NOT NULL')
				->where('tp.date_created >= ?', date('Y-m-d', $startDate))
				->order('tp.date_created DESC')
			;
			
            UsersProjects::filterSelect($select, ['p.id']);

			$data = db()->fetchAll($select);


			$data = $this->importDateFormatter($data);

			$users = [];
			foreach($data as $data_row_key => $data_row){
				$data[$data_row_key]['positive_negative'] = $data_row['amount'] < 0 ? 'negative' : 'positive';
				$users[] = $data_row['user'];
				$users[] = $data_row['match_user'];
			}

			$select = db()->select()
				->from(['u'=>'users'], ['user_id' => 'id', 'user_type' => 'type', 'initials', 'firstname', 'middlename', 'name', 'gender', 'username', 'language'])
				->joinLeft(['c' => 'company'], 'u.id = c.dummy_user', ['company' => 'c.name'])
				->where('u.id IN (' . implode_for_where_in(array_unique($users)) . ')');
			
			$users_names = db()->fetchAll($select);

			$userData = [];

			foreach ($users_names as $row) {
				$userData[$row['user_id']] = $row;
			}

			foreach ($data as $key => $row) {
				$data[$key]['username'] = $u_lib->buildname($userData[$row['user']]);
			}

			$types = array(
				'match_date'	=> array('title' => 'Match datum', 'width' => 'medium', 'type' => 'date'),
				'match_invoice'	=> array('title' => 'Factuur', 'width' => 'medium button pdf'),
				'user'			=> array('title' => 'Factuur ontvanger', 'width' => 'medium'),
				'matched'		=> array('title' => 'Gematched', 'width' => 'medium right unmatched'),
				'spacer'		=> array('title' => '', 'width' => 'xxxxsmall'),
				'match_user'	=> array('title' => 'Match door', 'width' => 'medium'),
				'account'		=> array('title' => 'Rekening', 'width' => 'hidden'),
				'import_id'	=> array('title' => 'Afschrift', 'width' => 'hidden', 'type' => 'date'),
				'date'			=> array('title' => 'Betaaldatum', 'width' => 'xsmall', 'type' => 'date'),
				'amount'		=> array('title' => 'Totaalbedrag', 'width' => 'medium right amount'),				
				'spacer2'		=> array('title' => '', 'width' => 'xxxxsmall'),
				'description'	=> array('title' => 'Omschrijving betaling', 'width' => $this->view->matchAsCosts ? 'xxlarge multiline' : 'xxxlarge multiline'),
				'type'			=> array('title' => '', 'width' => 'hidden type'),
				'positive_negative'	=> array('title' => '', 'width' => 'hidden type'),
				'iban'			=> array('title' => 'Afzender IBAN', 'width' => 'medium', 'type' => 'date')
			);

			$user_formatter = function($value, $item){
				if($value > 0){
					return '<a href="support/show/id/' . $value . '/type/user" target="_blank">' . $item['username'] . '</a>';
				} elseif($item['investor'] > 0) {
					return '<a href="support/show/id/' . $item['investor'] . '/type/investor" target="_blank">' . Investors::getName($item['investor']) . '</a>';
				}
				return '-';
			};			

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(['date' => 'ASC',]));

			$params = array();

			$this->view->listView = $this->view->ListView($data, $params)
				->setTypes($types)

				->addFormat('iban', 'iban')

				->addFormat('positive_negative', function($value){
					return $value == 'positive' ? 'Positieve bedragen' : 'Negatieve bedragen';
				})

				->addFormat(array('user', 'previous_user'), $user_formatter)

				->addFormat('match_user', function ($value) use ($userData, $u_lib) {
					if ($value > 0) {

						$name = $u_lib->buildname($userData[$value]);
						return $name ? $name : '-';

					} else {
						return 'Automatische match';
					}
				})

				->addFormat('match_amount', 'money_input')	

				->addFormat('match_invoice', function($value, $row){
					return '<a target="_blank" href="invoice/export/id/' . $value . '/">' . $row['match_invoice_id'] . '</a>';
				})		

				->addFormat('description', function($value){

					$value_parts = array();
					foreach(explode(' ', $value) as $value_part){
						if(strlen($value_part) > 5){
							$value_split_parts = str_split($value_part, 5);

							$value_part = '';
							foreach($value_split_parts as $value_split_part)
								$value_part .= $value_split_part . "&#8203;";

						}

						$value_parts[] = $value_part;
					}

					$value = implode(' ', $value_parts);
				
					return '<span noHintButton="true" class="forceAutoHint description" title="' . nl2br($value) . '">' . $value . '</span>';
				})

				->addFormat('import_id', function($value, $row) {
					return $row['statement'];
				})

				->addFormat(array('date', 'match_date'), 'date')

				->addFormat(array('amount', 'matched'), 'moneyspan')

				->addLegend('matched-list-legend')

				->addActions([
					'disabled_check' => function($row){
						return $row['investor_payed'] != '1';
					},					
					'buttons' => [
						['title' => 'Match ongedaan maken', 'icon' => 'delete', 'action' => 'transaction/unmatch', 'confirm' => true]
					]
				])				

				->setFilters([
					'account' => ['renderSeparately' => true, 'title' => 'Rekening'],
					'import_id' => ['renderSeparately' => true, 'title' => 'Afschrift', 'order_by_title' => 'DSC'],
					'positive_negative' => ['renderSeparately' => true, 'title' => 'Bedragen'],
					'date' => ['type' => 'date_range'],
					'match_date' => ['type' => 'date_range'],
					'iban' => ['type' => 'input'],
					'match_invoice' => ['type' => 'input'],
					'match_user' => [],
					'user' => ['type' => 'input'],
					'description' => ['type' => 'input', 'comparison_operator' => 'in_raw'],
					'matched' => ['type' => 'range'],
					'amount' => ['type' => 'range'],
				])

				->addTotals(['amount', 'matched'])

				->setOptions(array(
					'paginator_items_per_page' => 300
				))

				->render($this);


		}

		private function getProjectId(){

			if(!Settings::get('investor_provision_filter_project')) return false;

			return $this->_getParam('project-id') ?: false;
		}

		public function investorProvisionDetailsAction(){

			$model = new Controllers_Transaction_InvestorProvisionDetails();
			$model->setInvestorId($this->_getParam('investor'));

			$model->setPeriodOffset($this->_getParam('period_offset') ? $this->_getParam('period_offset') : 0);

			if($project_id = $this->getProjectId())
				$model->setProjectId($project_id);

			$this->view->data = $model->getData();
			$this->view->periodOffset = $model->getPeriodOffset();

			$this->render('transaction/provision/details', null, true);
		}

		public function investorProvisionInvoicesAction(){
			$this->disableView();

			if(!$this->_getParam('investors')) die();

			$investors = json_decode($this->_getParam('investors'), false);
			$period_offset = $this->_getParam('period_offset') ? $this->_getParam('period_offset') : 0;

			$i = new Investors();

			if($project_id = $this->getProjectId())
				$i->filterProject = $project_id;

			$project_id = $this->_getParam('project-id') ? $this->_getParam('project-id') : false;
			$i->finalizeDirect = $this->_getParam('direct') == '1';

			
			foreach($investors as $investor)
				$ic_id = $i->finalizeProvisionInvoice($investor, $period_offset);

			$i->clearProvisionCache($period_offset);

			$url_params = ['period_offset' => $period_offset];

			if($project_id > 0)
				$url_params['project-id'] = $project_id;

			$this->_redirect('transaction/investor-provision', $url_params);
		}

		public function investorProvisionPreviewAction(){
			$this->disableView();

			foreach(array('investor', 'period_offset') as $param)
				if(is_null($this->_getParam($param))) die();


			$model = new Investors();
			if($project_id = $this->getProjectId())
				$model->filterProject = $project_id;

			$model->createProvisionPreview($this->_getParam('investor'), $this->_getParam('period_offset'), $this->_getParam('stationery'));
		}

		public function investorProvisionShiftAction(){
			$this->disableView();

			foreach(array('investor', 'period_offset') as $param)
				if(is_null($this->_getParam($param))) die();

			$model = new Investors();
			$model->periodShift($this->_getParam('investor'), $this->_getParam('period_offset'));

            $referrer = $_SERVER['HTTP_REFERER'];

            if ($referrer) {
                header('Location: ' . $_SERVER['HTTP_REFERER']);
            } else {
                $redirectParameters = [
                    'period_offset' => $this->getParam('period_offset'),
                ];

                if($this->getParam('project_id')){
                    $redirectParameters['project_id'] = $this->getParam('project_id');
                }

                $this->_redirect('transaction/investor-provision', $redirectParameters);
            }
		}

		public function toggleRemindEnabledAction(){
			$this->disableView();

			if(!$this->_getParam('id') || !$this->_getParam('toggle'))
				return;

			$ids = strpos($this->getParam('id'), ',') === false ? [$this->getParam('id')] : explode(',', $this->getParam('id'));

			$t = new Transactions();

			foreach($ids as $id) {
				$row = $t->fetchRow($t->select()->where('id = ?', $id));

				$row->remind_toggle = $this->_getParam('toggle') == 'true';

				$row->save();

				if (!$row->remind_toggle) {
					$ir = new InvoicesReminds();

					if ($remind = $ir->fetchRow($ir->select()->where('transaction = ?', $row->id)->where('send = ?', false)->order('penalty DESC')))
						$remind->delete();
				}
			}

			if($this->_getParam('redirect'))
				header('Location: ' . $_SERVER['HTTP_REFERER']);
		}

        public function collectionBundleAction()
        {
            $transactionCollectionsIds = $this->getParam('ids');
            $form = new Form($this, 'transaction/collectionBundle');
            $this->view->form = $form;

            if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
                $data = $form->getValues();
                $transactionCollection = (new \TransactionsCollection())->fetchRowById($transactionCollectionsIds[0]);

                try {
                    $service = new BundleCollectionsService();
                    $newCollectionId = $service->bundle(new BundleCollectionsRequest(
                        $transactionCollection['type'],
                        $data['process_date'],
                        $transactionCollection['corporation'],
                        $transactionCollectionsIds
                    ));

                    $this->redirect("transaction/collection-list/collection-id/$newCollectionId");
                } catch (InvalidArgumentException $e) {
                    die('Er is iets mis gegaan.');
                }
            }
		}

        public function unBundleCollectionAction()
        {
            $this->disableView();

            $collectionId = $this->getParam('collection-id');

            try {
                $service = new UnBundleCollectionService();
                $service->undo(new UnBundleCollectionRequest(
                    $collectionId
                ));

                $this->redirect("transaction/collection-list/");
            } catch (InvalidArgumentException $e) {
                die('Er is iets mis gegaan.');
            }
		}
		
		public function collectionListAction(){

			$this->view->extraWidth = true;
			$this->view->collectionId = $this->getParam('collection-id');
			
			$this->view->Breadcrumbs()
				->addCrumb('Incasso- en overboekingopdrachten');
			if(!$this->getParam('sorting'))

			if(!$this->_getParam('status'))
				$this->_setParam('status', 'new');

			if(!$this->_getParam('sorting'))
				$this->_setParam('sorting', json_encode(array('process_date' => 'DESC')));

			$actions = [
                'disabled_check' => function($row){
                    return !$row['is_bundle'];
                },
                'buttons' => [
                    [
                        'title' => 'Samenvoegen',
                        'icon' => 'run',
                        'params' => [],
                        'action' => 'transaction/collection-bundle/',
                        'confirm' => true,
                        'noAjax' => true
                    ]
                ]
            ];

			$this->view->modelListView('TransactionsCollection')
                ->addLegend('back-to-overview-legend')
				->setTypes(['corporation', 'iban', 'type',  'process_date',  'sepa_status', 'total', 'payments', 'spacer', 'status', 'is_bundle'])

				->addFormat('type', function($value){
					if($value == 'debet')
						$type = 'Overboeking';
					else
						$type = 'Incasso';

					return "<p style='margin: 0;' transaction-type='$value'>$type</p>";
				})
				
				->addFormat('corporation', function($value, $row){
                    $bundleIcon = $row['is_bundle'] ? '<i class="fa fa-cubes" aria-hidden="true" title="Samengevoegde opdrachten"></i>' : '';
					return $value  ? $bundleIcon . '<a href="corporation/edit/id/' . $value . '" target="_blank" corporationId="' . $value . '">' . $row['corporation_name'] . '</a>' : '-';
				})

				->addFormat('process_date', function($value){ return date('d-m-Y', strtotime($value)); })

				->addFormat('project', function($value, $row){
					return $value  ? '<a href="support/show/id/' . $value . '/type/project" target="_blank">' . $row['project_name'] . '</a>' : '-';
				})

				->addFormat('total', 'money')

				->addFormat('sepa_status', function($value, $item){
					if($item['type'] == 'debet')
						return '-';

					return "<p style='margin: 0;' sepa-status='$value'>" . TransactionsCollection::sepaStatusTitle($value) . "</p>";
				})

				->addFormat('status', function($value, $item){
					if($value == 'deleted')
						return 'Verwijderd';

					if($value == 'archived') {
                            return '<a href="transaction/collection-unarchive/id/' . $item['id'].'">Terugzetten</a>';
                    }

					return 'Nieuw';
				})
				
				->addButtons(array(
					'details' => 'Details',
					'download-sepa' => 'SEPA',
					'archive' => 'Archiveren',
					'delete' => 'Verwijderen'
				))
				
				->addTotals(['total'])

				->setFilters(array(
					'corporation' => [ 'renderSeparately' => true, 'title' => 'Entiteit' ],
					'type' => array(),
					'project' => ['renderSeparately' => true, 'title' => 'Project'],
                    'iban' => [ 'renderSeparately' => true, 'title' => 'IBAN' ],
					'sepa_status' => array(),
					'status' => array(
						'custom_options' => array(
							'new' => array('title' => 'Nieuw'),
							'archived' => array('title' => 'Gearchiveerd'),
							'deleted' => array('title' => 'Verwijderd'),
						),
					)
				))
				
				->setOptions(array(
					'item_title' => 'Incasso- of overboekingopdracht',
					'item_has_details' => true,
					'noEditButton' => true,
					'disabled_buttons_test' => function($row, $button){
						if($button == 'details') return false;

						return $row['status'] != 'Nieuw';
					}					
				))

                ->addActions($actions)
				
				->render($this);
		}	
		
		public function collectionDetailsAction(){
            $this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb('Incasso- en overboekingopdrachten', 'transaction/collection-list');
			
			$this->view->id = $id = $this->_getParam('id');
			
			$id = $this->_getParam('id');
			$id = (isset($id) && is_array($id))? array_shift($id): $id;

			$model = new TransactionsCollection();
			$this->view->details = $model->fetchRow($model->select()->where('id = ?', $id));

			if(!$this->view->details->creditor_id)
				$this->view->details->creditor_id = $this->view->details->kvk ? TransactionsCollection::generateCreditorId($this->view->details->kvk, $this->view->details->kvk_subcode) : 'Onbekend';
			
			$this->view->is_editable = $this->view->details->deleted == 0 && $this->view->details->archived == 0;

			$this->view->Breadcrumbs()->addCrumb(($this->view->details->type == 'credit' ? 'Incasso' : 'Overboeking') . ' opdracht');

			$this->render('collection/details');
		}

		public function collectionDetailsListviewAction(){
			$this->view->id = $id = $this->_getParam('id');
			$id = (isset($id) && is_array($id))? array_shift($id): $id;

			$tc_model = new TransactionsCollection();
			$details = $tc_model->fetchRow($tc_model->select()->where('id = ?', $id));
			$is_editable = $details->deleted == 0 && $details->archived == 0;

            new TransactionsCollectionPayments();
            $select = db()->select()
                ->from(['tcp' => 'transactions_collection_payments'], ['id', 'name', 'amount', 'iban', 'bic', 'city', 'description', 'invoice_id'])
                ->joinLeft(['i' => 'invoices'], 'i.id = tcp.invoice_id', null)
                ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', ['is_purchase', 'custom_id' => 'id'])
                ->joinLeft(['irows' => 'invoice_rows'], 'irows.invoice = tcp.invoice_id', null)
                ->joinLeft(['o' => 'objects'], 'o.id = irows.object', null)
                ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['projects' => 'GROUP_CONCAT(DISTINCT og.project)'])
                ->where('tcp.collection = ?', $id)
                ->group('tcp.id')
                ;

            $data = db()->fetchAll($select);
            foreach($data as $dataItemKey => $dataItem){
                $data[$dataItemKey]['projects'] = array_filter(explode(',', $dataItem['projects']) ?: []);
            }

            $data = (new \Controllers\Transaction\CollectionDetailsListAddPurchaseProjects())->getList($data);

            $projectIds = [];
            foreach($data as $dataItem){
               $projectIds = array_merge($projectIds, $dataItem['projects']);
            }
            $projectIds = array_filter(array_unique($projectIds));

            $projectNames = Projects::getNamesForProjectIds($projectIds);

			$this->view->listView($data, [
				'collection' => $id,
				'deleted' => false,
				'url-override' => 'transaction/collection-details-listview/'
            ])

                ->setTypes([
                    'name' => [
                        'title' => 'Naam',
                        'width' => 'xxxlarge truncate',
                    ],
                    'amount' => [
                        'title' => 'Bedrag',
                        'width' => 'xxsmall right',
                    ],
                    'spacer' => [
                        'title' => '&nbsp;',
                        'width' => 'xxxxsmall',
                    ],
                    'iban' => [
                        'title' => 'IBAN',
                        'width' => 'large',
                    ],
                    'bic' => [
                        'title' => 'BIC',
                        'width' => 'xsmall',
                    ],
                    'city' => [
                        'title' => 'Plaats',
                        'width' => 'medium truncate',
                    ],
                    'projects' => [
                        'title' => 'Project(en)',
                        'width' => 'large truncate',
                    ],
                    'description' => [
                        'title' => 'Omschrijving',
                        'width' => 'xxxlarge truncate',
                    ],
                ])
				->addFormat('amount', function($value){ return '&euro; ' . new StringFormat($value, 'money'); })
				
				->addFormat('description', function($value){ return str_replace("\n", ' - ', $value); })
				->addFormat('iban', function($value){ return (string) new StringFormat($value, 'iban'); })

				->addFormat('name', function($value, $item){
					if($item['user'] > 0)
						return '<a href="support/show/id/' . $item['user'] . '/type/user" target="_blank">' . $value . '</a>';
					else
						return $value;
				})

				->addFormat('description', function($value, $item){
					if($item['invoice_id'] > 0)
						return '<a href="invoice/export/id/' . $item['invoice_id'] . '/" target="_blank">' . $value . '</a>';
					else
						return $value;
				})

                ->addFormat('projects', function ($value) use ($projectNames) {
                    if (!$value) {
                        return '-';
                    }

                    return '<a href="project/details/id/' . $value . '/">' . $projectNames[$value] . '</a>';
                })

				->addButtons(array(
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen'
				))

				->setFilters(array(
					'name' => array('type' => 'input'),
					'iban' => array('type' => 'input'),
					'bic' => array(),
					'city' => array('type' => 'input'),
					'description' => array('type' => 'input'),
                    'projects' => ['order_by_value' == 'ASC'],
				))				
				
				->setOptions(array(
					'item_title' => 'Incasso- of overboekingopdracht',
					'editAction' => 'collection-payments-edit',
					'deleteAction' => 'collection-payments-delete',
					'show_title' => false,
					'disabled_buttons_test' => function() use ($is_editable){
						return !$is_editable;
					}						
				))

				->addTotals(array('amount'))
				
				->render($this);
		}
		
		public function collectionDownloadAction(){
			if($this->_getParam('download') == 'true'){
				$this->disableView();
				
				TransactionsCollection::download($this->_getParam('id'));			
			} else {
				$this->view->Breadcrumbs()
					->addCrumb('Incasso- en overboekingopdrachten', 'transaction/collection-list');

				$model = new TransactionsCollection();
				$this->view->details = $model->fetchRow($model->select()->where('id = ?', $this->_getParam('id')));
				$this->view->type_title = $this->view->details->type == 'credit' ? 'Incasso' : 'Overboeking';

				$this->view->Breadcrumbs()
					->addCrumb(($this->view->details->type == 'credit' ? 'Incasso' : 'Overboeking') . ' opdracht', 'transaction/collection-details/id/' . $this->_getParam('id') . '/')
					->addCrumb('CLIEOP-' . ($this->view->details->type == 'credit' ? 'incasso' : 'overboeking') . ' bestand downloaden');

				$this->view->check = true;
				
				$this->view->id = $this->_getParam('id');
				$this->view->type = 'clieop';
				
				$this->render('collection/download');
			}
		}

		public function collectionDownloadSepaAction(){
			if($this->_getParam('download') == 'true'){
				$this->disableView();
				
				TransactionsCollection::downloadSepa($this->_getParam('id'));			
			} else {
				$this->view->Breadcrumbs()
					->addCrumb('Incasso- en overboekingopdrachten', 'transaction/collection-list');

				$model = new TransactionsCollection();
				$this->view->details = $model->fetchRow($model->select()->where('id = ?', $this->_getParam('id')));
				$this->view->is_editable = $this->view->details->deleted == 0 && $this->view->details->archived == 0;

				if(!$this->view->details->creditor_id)
					$this->view->details->creditor_id = $this->view->details->kvk ? TransactionsCollection::generateCreditorId($this->view->details->kvk, $this->view->details->kvk_subcode) : 'Onbekend';

				$this->view->type_title = $this->view->details->type == 'credit' ? 'Incasso' : 'Overboeking';

				if($this->view->details->type == 'credit' && is_null($this->getParam('confirmed')))
					$this->view->firstCreditCollection = TransactionsCollection::checkFirstCreditCollection($this->view->details->creditor_id);

				if(!is_null($this->getParam('confirmed')))
					error_log('[Incassant-id] User: ' . loginManager::data()->id . ' heeft incassant-id: ' . $this->view->details->creditor_id . ' bevestigd voor incasso opdracht ' . $this->view->details->id . '.');

				$this->view->Breadcrumbs()
					->addCrumb(($this->view->details->type == 'credit' ? 'Incasso' : 'Overboeking') . ' opdracht', 'transaction/collection-details/id/' . $this->_getParam('id') . '/')
					->addCrumb('SEPA-' . ($this->view->details->type == 'credit' ? 'incasso' : 'overboeking') . ' bestand downloaden');

				$this->view->check = TransactionsCollection::checkSepa($this->_getParam('id'));	
				$this->view->usersWithSEPAStatus = TransactionsCollection::getUsersWithSEPAStatus($this->_getParam('id'));
				
				$this->view->id = $this->_getParam('id');
				$this->view->type = 'sepa';
				$this->render('collection/download');
			}
		}

		public function collectionEndFirstSepaStatusAction(){

			$this->disableView();

			$u_model = new Users();
			if($id = $this->_getParam('id'))
				foreach(TransactionsCollection::getUsersWithSEPAStatus($id) as $user){
					$u_row = $u_model->getById($user);

					$u_row->sepa_status = 'RCUR';

					$u_row->save();
				}

			if($this->_getParam('redirect'))
 				header('Location: ' . $_SERVER['HTTP_REFERER']);
		}
	
		public function collectionDeleteAction(){

			$this->view->id = $id = $this->_getParam('id');


 			if ($this->_getParam('deletepayments') != '') {
				$row = TransactionsCollection::get($this->_getParam('id'));
				$t_lib = new Transaction();

				$row->deleted = 1;
				$row->save();
				if ($this->_getParam('deletepayments') == 'yes') {
					$select = db()->select()
					->from('transactions_collection_payments')
					->where('`collection` = ?', $row->id)
					->where('invoice_id > ?', 0);

					foreach(db()->fetchAll($select) as $collection_payment)
						$t_lib->undoBankGroupedCollection($collection_payment['invoice_id']);
				}

				$this->_redirect('transaction/collection-list');	 

			} 


			


		 	


			
			
			
		}
	
		public function collectionArchiveAction(){
			$invoice_run_lib = new InvoiceRun();
			$invoice_run_lib->financialExportForCollection = $this->_getParam('id');

			$row = TransactionsCollection::get($this->_getParam('id'));
			$row->archived = 1;
			$row->save();

			$has_xml = false;

			if($row->invoice_run_id && Settings::get('financial_export_group_collection'))
				$has_xml = $invoice_run_lib->exportXmlDocs(array(array('id' => $row->invoice_run_id)), false);

			if($has_xml === false)
				$this->_redirect('transaction/collection-list');
			else
				$this->_redirect('transaction/collection-xml', array('id' => $this->_getParam('id')));

		}
        public function collectionUnarchiveAction(){
            $invoice_run_lib = new InvoiceRun();
            $invoice_run_lib->financialExportForCollection = $this->_getParam('id');

            $row = TransactionsCollection::get($this->_getParam('id'));
            $row->archived = 0;
            $row->save();

            $this->_redirect('transaction/collection-list');

        }

		public function collectionXmlAction(){
			$download = $this->_getParam('download') == 'true';
			$this->view->id = $id = $this->_getParam('id');

			if(!$id) return;

			if($download){
				$this->disableView();
				$invoice_run_lib = new InvoiceRun();
				$invoice_run_lib->financialExportForCollection = $id;

				$row = TransactionsCollection::get($id);

				if($row->invoice_run_id && Settings::get('financial_export_group_collection'))
					$invoice_run_lib->exportXmlDocs(array(array('id' => $row->invoice_run_id)), true);
			} else {
				$this->render('collection/xml');
			}

		}
				
		public function collectionEditAction(){
			$this->view->headScriptHashed()->appendFile('media/javascript/iban_to_bic.js');

			$this->view->Breadcrumbs()
				->addCrumb('Incasso- en overboekingopdrachten', 'transaction/collection-list')
				->addCrumb('Details', 'transaction/collection-details/id/' . $this->_getParam('id') . '/')
				->addCrumb('Bewerken');
			
			$this->view->form = $this->view->EditView($this, 'TransactionsCollection', array('id' => $this->_getParam('id')))
				
				->setOptions(array(
					'render_to_controller' => false,
				))
				
				->render($this);
				
			$this->render('collection/edit');
		}	
		
		public function collectionPaymentsEditAction(){
			$this->view->headScriptHashed()->appendFile('media/javascript/iban_to_bic.js');

			$model = new TransactionsCollectionPayments();
			$col_model = new TransactionsCollection();
			$row = $model->getById($this->_getParam('id'));

			$this->collection = $col_model->getById($row->collection);

			$this->view->Breadcrumbs()
				->addCrumb('Incasso- en overboekingopdrachten', 'transaction/collection-list')
				->addCrumb('Details', 'transaction/collection-details/id/' . $row->collection . '/')
				->addCrumb('Opdracht bewerken');
			
			$this->view->EditView($this, 'TransactionsCollectionPayments', array('id' => $this->_getParam('id')))
			
				->render($this);
		}	
		
		public function collectionPaymentsDeleteAction(){
			$this->disableView();
			
			$t_lib = new Transaction();
			$row = TransactionsCollectionPayments::get($this->_getParam('id'));

			if($row['invoice_id'] > 0)
				$t_lib->undoBankGroupedCollection($row['invoice_id']);

			$collection = $row->collection;
			$row->delete();
			
			$this->_redirect('transaction/collection-details', array('id' => $collection));
		}		
		
		public function interestListAction(){
			$this->view->Breadcrumbs()->addCrumb('Wettelijke rente', 'transaction/interest-list');
			
			$this->view->interest = Interests::getList();
			
			$this->render('interest/list');
		}
		
		public function interestEditAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Wettelijke rente', 'transaction/interest-list')
				->addCrumb($this->_getParam('id') ? 'Periode bewerken' : 'Periode toevoegen');
				
			$interestModel = new Interests();
				
			$form = new Form($this, 'interest');
			
			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
				if($id = $this->_getParam('id'))
					$row = $interestModel->fetchRow($interestModel->select()->where('id = ?', $id));
					
				$row = $row ? $row : $interestModel->createRow();

				foreach($form->getValues() as $key => $value)
					$row->$key = $value;
					
				$row->save();
				
				$this->_redirect('transaction/interest-list');
			}			
			
			if($id = $this->_getParam('id'))
				$form->populate($interestModel->fetchRow($interestModel->select()->where('id = ?', $id))->toArray());
				
			$this->view->form = $form;
			$this->render('interest/edit');
		}
		
		public function interestDeleteAction(){
			$this->disableView();
			
			$interestModel = new Interests();
			
			if($id = $this->_getParam('id'))
				$interestModel->delete('id = ' . $id);
				
			$this->_redirect('transaction/interest-list');
		}

		public function blockedListAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Gestorneerde incasso\'s');

			$model = new TransactionsBlocked();
			$this->view->list = $model->fetchAll($model->select()->where('closed = ?', false));
		}

		public function blockedDisableAction(){
			$id = $this->_getParam('id');

			if(!($id > 0)) return;

			$tb_model = new TransactionsBlocked();
			$tb_model->getById($id)->setFromArray(['closed' => true])->save();

			$this->_redirect('transaction/blocked-list');
		}		

		public function rebuildClieopAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Clieop bestanden aanmaken');

			if(!$this->_getParam('transactions'))
				return;

			$tr = new TransactionsRetry();
			$tr->add(explode(',', $this->_getParam('transactions')), $this->_getParam('close-blocked'));
		}

		public function retryMatchingAction(){
			$this->disableView();

			$t = new Transaction();
			$t->retryMatching();
		}
		
		public function toggleCreditAction(){
			$this->disableView();
			
			$t = new Transactions();
			$row = $t->fetchRow($t->select()->where('id = ?', $this->_getParam('id')));
			$row->credit = $row->credit == true ? false : true;
			$row->save();
			
			echo json_encode((bool) $row->credit);
		}

		public function debtorPaymentOverviewAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Omzet')
				->addCrumb('Debiteuren betalingsoverzicht');

			$this->view->headScriptHashed()
				->appendFile('media/javascript/chart/chart.js')
				->appendFile('media/javascript/chart/chart-bar-stacked.js');

			$this->view->year = $year = $this->_getParam('year') ?: date('Y');

			$t_lib = new Transaction();
			$this->view->data = $t_lib->getDebtorPaymentData($year);
		}


		public function collectionAgencyOverviewAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Omzet')
				->addCrumb('Incasso overzicht');

			$this->view->headScriptHashed()
				->appendFile('media/javascript/chart/chart.js')
				->appendFile('media/javascript/chart/chart-bar-stacked.js');

			$this->view->year = $year = $this->_getParam('year') ?: date('Y');

			$select = db()->select()
				->from(array('ir' => 'invoice_reminds'), array('month' => 'MONTH(ir.date)', 'count' => 'COUNT(ir.id)'))
				->joinLeft(array('t' => 'transactions'), 't.id = ir.transaction', array('amount' => '(SUM(t.amount - t.payed)/100)'))
				->where('YEAR(ir.date) = ?', $year)
				->where('ir.penalty = ?', 99)
				->where('ir.send = ?', true)
				->group('MONTH(ir.date)')
				;

			$data = array(
				'amounts' => array(),
				'counts' => array(),
			);

			foreach($data as $data_type => $data_amount)
				for ($i=1; $i <= 12; $i++)
					$data[$data_type][$i] = 0;

			foreach(db()->fetchAll($select) as $data_row){
				$data['amounts'][$data_row['month']] = $data_row['amount'];
				$data['counts'][$data_row['month']] = $data_row['count'];
			}

			$this->view->data = $data;
		}		
		
		public function getPayedInPeriodAction(){
			$this->disableView();
			
			$from = strtotime($this->_getParam('from'));
			$till = strtotime($this->_getParam('till'));
			
			$t = new Transaction();
			$db = db();
			
			$select = $db->select()
				->from(array('uo' => 'users_objects'), 		array('id'))
					
				->joinLeft(array('u' => 'users'),
					'u.id = uo.customer',					array('user' => 'id'))
					
				->joinLeft(array('o' => 'objects'),
					'o.id = uo.object',						false)
					
				->joinLeft(array('a' => 'address'),
					'a.type_id = o.id AND a.type ="object"',array('address', 'number'))
						
				->joinLeft(array('og' => 'objectgroup'),
					'og.id = o.objectgroup',				false)
				
				->where('og.project = ?', $this->_getParam('project'))
				->where('u.id');
				
			$users = array();
				
			foreach($db->fetchAll($select) as $uo){
			
				foreach($t->getPayedInPeriod($uo['id'], $from, $till) as $pay){ 
					if ($pay['adjustment'] > 0)
						$pay['advance'] += $pay['adjustment'];
					
					$users[$uo['id']]['name'] = User::buildname(false, $uo['user']);
					$users[$uo['id']]['address'] = $uo['address'] . ' ' . $uo['number'];
					$users[$uo['id']]['payed']['total'] += $pay['advance'];
					$users[$uo['id']]['payed']['periods'][] = $pay;
				}
			}
			echo '<pre>';
			print_r($users);
		}
		
		public function searchMt940Action(){
			$t = new Transaction();
			
			if($this->_getParam('search')){
				$this->disableView(true);
				$this->view->transactions = $t->mt940search($this->_getParam('search'));	
			} else {
				$this->view->transactions = false;
			}
			
			$this->view->ajax = $this->_getParam('ajax');
		}
		
		public function fixAmountsAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			
			$db = db();
			$i = new Invoice();
			$t = new Transactions();
			$runs = array();
			
			$select = $db->select()
				->from(array('i'=>'invoices'), array('id', 'run'))
				->joinLeft(array('ir' => 'invoices_run'),
					'ir.id = i.run', false)
				->where('ir.start = ?', '2011-06-01');
				
			foreach($db->fetchAll($select) as $invoice){
				if(!in_array($invoice['run'], $runs))
					$runs[] = $invoice['run'];
			
				$i->id = $invoice['id'];
				$data = $i->build();
				
				$row = $t->fetchRow($t->select()->where('invoice = ?', $invoice['id']));
				
				$row->amount = round(Invoice::calcTax('add', str_replace(',', '.', $data['totalamount'])), 2)*100;
				
				//$row->save();
			}
			
			$t = new Transaction();
			//$t->monthlyClieop($runs);
		}

		public function fixAmountsUsingInvoiceTableAction(){
			$this->disableView();
			return
			$i = new Invoices();
			
			foreach($i->fetchAll($i->select()->where('run = ?', 318)->where('final IS NOT NULL')) as $invoice){
				$select = db()->select()
					->from(array('i' => 'invoices'), false)
					
					->joinLeft(array('uo' => 'users_objects'),
						'uo.id = i.users_objects',	false)
						
					->joinLeft(array('o' => 'objects'),
						'o.id = uo.object',			false)
					->joinLeft(array('a' => 'address'),
						'a.type="object" AND a.type_id = o.id', 	array('address', 'number'))
					->where('i.id = ?', $invoice->id);
				
				$addr = db()->fetchRow($select);
				
				echo $addr['address'] . ' ' . $addr['number'] . '<br />';
				 
				echo "UPDATE `transactions` SET `amount` = '" . abs($invoice->advance + $invoice->final) . "', `type` = '" . (($invoice->advance + $invoice->final) > 0 ? 'c' : 'd') . "' WHERE `invoice` =" . $invoice->id . "; <br /><br />";
					
			}
			
			$t = new Transaction();
			//$t->monthlyClieop(array(318));
	
		}
		
		public function buildMissingTransactionsAction(){
			$this->disableView();
			
			$i = new Invoices();
			$t = new Transactions();
			$db = db();
			
			$select = $db->select()
		
				->from(array('i' => 'invoices'),							array('id'))
				
				->joinLeft(array('ir' => 'invoices_run'),
					'ir.id = i.run',										array('end'))
					
				->joinLeft(array('t' => 'transactions'),
					't.invoice = i.id',										false)
					
				->joinLeft(array('id' => 'invoice_details'),
					'id.invoice = i.id',									array('details' => 'id', 'amount'))
					
				->joinLeft(array('uo' => 'users_objects'),
					'uo.id = i.users_objects',								array('customer'))
					
				->joinLeft(array('ia' => 'invoice_adjustments'),
					'ia.invoice_group = ir.id',								array('adj_id' => 'id', 'adjustment' => 'amount', 'substract'))
					
				->joinLeft(array('ial2' => 'invoice_adjustments_link'),
					' ia.id = ial2.adjustment',								array('all' => 'id'))
					
				->joinLeft(array('ial' => 'invoice_adjustments_link'),
					' ia.id = ial.adjustment AND ial.object = uo.object',	array('adj_link' => 'id'))
				
				
					
				->where('id.id IS NOT NULL')
				->where('ir.status = 2')
				->where('i.users_objects != 0')
				->where('t.id IS NULL');
			
			$invoices = array();
			
			foreach($db->fetchAll($select) as $inv){
				$invoices[$inv['id']]['id'] = $inv['id'];
				$invoices[$inv['id']]['date'] = strtotime($inv['end']);
				$invoices[$inv['id']]['user'] = $inv['customer'];;
				
				$invoices[$inv['id']]['details'][$inv['details']] = $inv['amount'];
				
				if ($inv['adj_link'] || $inv['adj_id'] && !$inv['all']){
					$invoices[$inv['id']]['adjustment'][$inv['adj_id']] = array('adjustment' => $inv['adjustment'], 'substract' => $inv['substract']);
				}
			}
			foreach($invoices as $id => $inv){
				
				foreach($inv['details'] as $det){
					$inv['amount'] += $det;
				}
				
				foreach((array) $inv['adjustment'] as $adj){
					if ($adj['substract'] == 1){
						$inv['adj'] -= $adj['adjustment']*1000;
					} else{
						$inv['adj'] += $adj['adjustment']*1000;
					}
				}
				
				$inv['amount'] = $inv['advanceAmount'] = Invoice::calcTax('add', $inv['amount']);
			
				$inv['amount'] +=  $inv['adj'];
				
				$row = $i->fetchRow($i->select()->where('id = ?', $id));
				$row->advance = round($inv['advanceAmount']/10);
				//$row->save();
				
				$tr = $t->fetchRow($t->select()->where('invoice = ?', $id));
				$tr = $tr ? $tr : $t->createRow();
				
				$tr->user = $inv['user'];
				$tr->investor = 0;
				$tr->date = date('Y-m-d', $inv['date']);
				$tr->invoice = $id;
				$tr->type = 'c';
				$tr->method = 'ideal';
				
				$tr->amount = round($inv['amount']/10);
				$tr->adjustment = round((0 - $inv['adj'])/10);
				$tr->payed = $tr->amount;
				$tr->closed = true;
				
				$tr->invoicerun = $row->run;
				
				//$tr->save();
				
				
				//echo '<a href="/invoice/export/id/' . $id . '">' . $id . ': ' . new StringFormat($inv['amount']/1000, 'money') . '</a><br />'; 
				
			}	
			
		}

		/**
		 * getTransactions
		 */
		public function getAction() {

			$this->view->transactions = $transaction->getlist();

		}

		public function testAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

		}

		public function substractInvoicesAction(){

			if(!($user_id = $this->_getParam('user'))) return;

			if($_POST){
				$ic_lib = new InvoiceCustom();
				$ic_lib->customid = 0;
				$ic_lib->applySubstracts = true;

				$t_model = new Transactions();

				$credit = $t_model->getById($this->_getParam('credit'));
				$invoice = $t_model->getById($this->_getParam('invoice'));

				$corporation = $invoice->invoice_run_row->project_row->corporation;
				$corporation_account = Corporation::getCurrentAccount($corporation);

				$ic_lib->applyCustomSubstract($invoice->invoice, array(
					'paymenttotal' => $credit->amount - $credit->payed,
					'corporation' => $corporation,
					'credit' => true,
					'credits' => array(
						array('id' => $credit->invoice, 'identifier' => $credit->invoice_run_row->identifier . '.' . $credit->invoice_row->identifier),
					),
					'invoicedate' => date('d-m-Y'),
					'bankaccount' => $corporation_account ? $corporation_account['bank'] : false,
				));

				TransactionsPayments::add(array(
					'transaction' => 			$credit->id,
					'status' => 				'deducted',
					'investor_provision' =>		0,
					'date' => 					date('d-m-Y'),
					'direction' =>				'outgoing',
					'amount' =>					$credit->amount - $credit->payed,
					'bankaccount_customer' =>	$corporation_account ? $corporation_account['bank'] : false,
					'bankaccount' =>			$corporation_account ? $corporation_account['bank'] : false,
					'description' =>			'Verrekend met factuur: ' . $invoice->invoice_run_row->identifier . '.' . $invoice->invoice_row->identifier
				));		

				$credit->payed = $credit->amount;
				$credit->closed = true;
				$credit->save();			

				die();
			}


			$this->view->Breadcrumbs()
				->addCrumb('Facturen verrekenen van ' . User::buildname(false, $user_id));

			$this->view->user = $user_id;

			$t = new Transactions();
			$invoices = array();

			foreach(array('credit' => 'd', 'debet' => 'c') as $type => $transaction_type){
				$select = $t->select()
					->where('(amount - payed) > 0')
					->where('user = ?', $user_id)
					->where('type = ?', $transaction_type);

				$invoices[$type] = $t->fetchAll($select);
			}

			$this->view->invoices = $invoices;
		}

		public function fixAdjustmentsAction(){
			$this->disableView();
			
			$db = db();
			$t = new Transactions();
			$invoices = array();
			
			$select = $db->select()
				->from(array('ia' => 'invoice_adjustments'),		array('amount', 'substract'))
				->joinLeft(array('ial' => 'invoice_adjustments_link'),
					'ial.adjustment = ia.id', 						false)
				->joinLeft(array('uo' => 'users_objects'),
					'uo.object = ial.object', 						false)
				->joinLeft(array('i' => 'invoices'),
					'i.users_objects = uo.id', 						false)
				->joinLeft(array('t' => 'transactions'),
					't.invoice = i.id',								array('id', 'adjustment'))
				
				->where('t.id IS NOT NULL')
				->where('t.adjustment = 0')
				->where('t.adjustment != ia.amount');
				
			foreach($db->fetchAll($select) as $invoice){
				$invoice['amount'] = $invoice['substract'] == 1 ? 0 - $invoice['amount'] : $invoice['amount'];
				$invoice['amount'] *= 100;
				
				$invoices[$invoice['id']]['amount'] += $invoice['amount'];				
			}
			
			foreach($invoices as $id => $transaction){
				$row = $t->fetchRow($t->select()->where('id = ?', $id));
				$row->adjustment = $transaction['amount'];
				//p($row);
				//$row->save();
			}
		}
		
		public function fixBrokenAction(){
			$this->disableView();
			
			$db = db();
			$t = new Transactions();
			
			$select = $db->select()
				->from(array('t' => 'transactions'),		array('id', 'invoice'))
				->joinLeft(array('t2' => 'transactions2'),
					't.invoice = t2.invoice', 				array('amount', 'type'))
				->where('t.amount = ?', 0)
				->where('t.user > ?', 0);
				
			foreach($db->fetchAll($select) as $invoice){
				$row = $t->fetchRow($t->select()->where('id = ?', $invoice['id']));
				$row->amount = $invoice['amount'];
				$row->type = $invoice['type'];
				
				//$row->save();
			}				
		}
		
		public function setAllToPayedAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$t = new Transactions();
			$rows = $t->fetchAll($t->select()->where('date = ?', date('Y-m-d'))->where('payed < amount')->where('type = ?', 'c')->where('user != 0 OR investor != 0'));
			//	$rows = $t->fetchAll($t->select()->where('date = ?', '2010-08-10'));

			p($rows);
			foreach ($rows as $row) {
				$row->payed = $row->amount;
				$row->closed = 1;
				//$row->save();
				}
		}

		public function findReverseAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$t = new Transaction();

			$bankaccounts = $t->getVaansterBankAccounts();

			$MT940 = New MT940();

			$MT940->import(file_get_contents('_cache/mt940/MT940.STA'));
			// find transactions in Vaanster bank accounts
			$transactions = array();

			foreach ($MT940->getArray() as $account) {
				if (in_array($account['account'], $bankaccounts)) {
					foreach ($account['transactions'] as $item) {
						if ($item['storno'] && !strpos($item['description'], 'FACTUUR ')) {
							$transactions[] = $item;
						}
					}
				}
			}

		}

		public function setToPayedAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Transaction();
			$transaction->setToPayed();
		}

		public function setToClosedAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Invoice();
			$transaction->setToClosed();
		}

		/**
		 * addTransactions
		 */
		public function temp3Action() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Transaction();
			$transaction->temp3();
		}

		/**
		 * insertInvestortransacstions
		 */
		public function insertInvestorAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Transaction();
			$transaction->insertInvestorTransactions();
		}

		/**
		 * addTransactions
		 */
		public function addAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Transaction();
			$transaction->add($this->_getParam('id'));

			$this->_redirect('transaction/get');
		}

		public function tempAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$db = db();
			$t = new Transactions();

			$select = $db->select()
				->from(array('t' => 'transactions'))
				->joinLeft(array('ir' => 'invoices_run'),
				'ir.id = t.invoicerun', false)
				->where('ir.invoicedate LIKE ?', '2010-09-25');

			foreach ($db->fetchAll($select) as $item) {
				$row = $t->fetchRow($t->select()->where('id = ?', $item['id']));
				$row->date = '2010-09-25';
				$row->save();
			}
		}

		/**
		 * getTransactions
		 */
		public function getListAction() {
			$this->view->Breadcrumbs()->addCrumb('Transacties');

			if ($this->getRequest()->getPost('json')) {
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);

				$data = $this->getRequest()->getPost();
				$offset = is_numeric($data['from']) ? $data['from'] : false;

				$data = $this->transaction->find($data, $offset);

				echo Zend_Json::encode(count($data) > 0 ? $data : false);
			}

			$this->view->transactions = $this->transaction->find();
		}

		public function toggleReportGroupingAction(){
			$this->disableView();

			$_SESSION['transaction-report-toggle'] = !$_SESSION['transaction-report-toggle'];

			$this->redirect('transaction/get-report');
		}

		/**
		 * getTransactions
		 */
		public function getReportAction() {

			$this->view->headScriptHashed()->appendFile('media/javascript/transactions-report.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/transactions-payments.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/remind_toggles.js');
			$this->view->headLink()->appendStylesheet('media/style/transactions-report.css');
			$this->view->headLink()->appendStylesheet('media/style/transactions-payments.css');

    		ini_set('memory_limit', '20048M');

			$this->view->showClosed = $showClosed = $this->_getParam('showClosedTransactions') == '1';
			$this->view->showOpenAndClosed = $showOpenAndClosed = $this->_getParam('showOpenAndClosedTransactions') == '1';

			
			$this->view->extraWidth = true;

			if (!$showClosed && !$showOpenAndClosed) {
				$this->view->Breadcrumbs()->addCrumb('Openstaande posten overzicht');
			} else {
				$this->view->Breadcrumbs()->addCrumb('Gesloten posten overzicht');

				$closedYear = $this->_getParam('closedYear');

				$closedYear = isset($closedYear)? $closedYear : date('Y');

				$this->view->closedYear = $closedYear;
			}

			$defaults = [
				'filters' => [
					'expired' => 1,
					'is_purchase' => 0
				],
				'sorted' => [
					[ 'name' => 'project_name', 'direction' => 'asc' ]
				]
			];

			$userId = $this->_getParam('user');
			$nameDefault = User::buildName(false, $userId);
			if(intval($userId) > 0 && trim($nameDefault) != false && $nameDefault != '-') {
				$defaults['filters']['name'] = $nameDefault;
				$defaults['filters']['expired'] = false;
			}

			$this->view->defaults = $defaults;

			$showClosed = $this->_getParam('showClosedTransactions') == '1' || $this->_getParam('showClosedTransactions') == 'true';

			$closedYear = $this->_getParam('closedYear');
			$closed_date = $this->_getParam('closedDate') ? strtotime($this->_getParam('closedDate')) : false;

			$closedYear = isset($closedYear)? $closedYear : date('Y');
			
			$this->view->data = $this->transaction->loadReportData(array('showClosed' => $showClosed, 'showOpenAndClosed' => $showOpenAndClosed, 'closedYear' => $closedYear, 'closed_date' => $closed_date));
		}		

		public function getReportListAction(){
    		ini_set('memory_limit', '1768M');
			$this->view->extraWidth = true;

			$grouped = $_SESSION['transaction-report-toggle'] === true;

			$openAndClosed = $this->_getParam('showOpenAndClosedTransactions') == 'true' || $this->_getParam('showOpenAndClosedTransactions') == '1';

			if(!($this->transaction->reportData = $this->_getParam('data'))) {
                $cache = new Cache_TransactionReport();
                $cache->setIdParams([
                    'showClosedTransactions' => $this->_getParam('showClosedTransactions') == 'true',
                    'openAndClosed' => $openAndClosed,
                ]);
                $this->transaction->reportData = $cache->load();
            }

			$filters = $this->getReportFilters();
			$data = $this->transaction->getReportData($filters, $this->_getParam('sorted'), $grouped);

            //p($data,'die');

			$paginator = Zend_Paginator::factory($data['data']);
			$paginator->setCurrentPageNumber($this->_getParam('page'));
			$paginator->setItemCountPerPage($this->_getParam('print') == 'true' ? 10000000 : 25);

			$this->view->data = $paginator;
			$this->view->total = $data['total'];
			$this->view->totals = $data['totals'];
			$this->view->filteredTotals = $data['filtered_totals'];
			$this->view->filterCount = $this->transaction->reportFilterCount($data['data']);
			$this->view->grouped = $grouped;

			if($this->_getParam('print') == 'true')
				$this->render('print-report-list');

			if($this->_getParam('excel') == 'true')
				$this->getExcelReport($paginator, $grouped);
		}


        public function pomReportAction(){

            ini_set('memory_limit', '20048M');
            $this->view->extraWidth = true;

            $data  = $this->transaction->loadReportDataPom(array('showClosed' => $showClosed, 'showOpenAndClosed' => $showOpenAndClosed, 'closedYear' => $closedYear, 'closed_date' => $closed_date));
            $listviewData = $data['data'];
            //p($listviewData,'die');
            $this->view->listView($listviewData)

                ->setTypes([
                    'firstname' => [
                        'title' => 'Voornaam',
                        'width' => 'xxsmall truncate',
                    ],
                    'lastname' => [
                        'title' => 'Achternaam',
                        'width' => 'xxsmall truncate',
                    ],
                    'email' => [
                        'title' => 'Emailadres 1',
                        'width' => 'xxsmall truncate',
                    ],
                    'email_dummy' => [
                        'title' => 'Emailadres 2',
                        'width' => 'xxsmall truncate',
                    ],
                    'phone' => [
                        'title' => 'Telefoon 1',
                        'width' => 'xxsmall truncate',
                    ],
                    'phone_landline' => [
                        'title' => 'Telefoon 2',
                        'width' => 'xxsmall truncate',
                    ],
                    'address' => [
                        'title' => 'straat',
                        'width' => 'xxsmall truncate',
                    ],
                    'zipcode' => [
                        'title' => 'postcode',
                        'width' => 'xxsmall truncate',
                    ],
                    'city' => [
                        'title' => 'plaats',
                        'width' => 'xxsmall truncate',
                    ],
                    'bdate' => [
                        'title' => 'geboortedatum',
                        'width' => 'xxsmall truncate',
                    ],
                    'gender_user' => [
                        'title' => 'geslacht',
                        'width' => 'xxsmall truncate',
                    ],
                    'language' => [
                        'title' => 'taal',
                        'width' => 'xxsmall truncate',
                    ],
                    'investor_objects_name' => [
                        'title' => 'belegger',
                        'width' => 'xxsmall truncate',
                    ],
                    'company' => [
                        'title' => 'bedrijfsnaam',
                        'width' => 'xxsmall truncate',
                    ],
                    'user_identifier' => [
                        'title' => 'Debiteurnummer',
                        'width' => 'xxsmall truncate',
                    ],
                    'identifier' => [
                        'title' => 'Betalingskenmerk',
                        'width' => 'xxsmall truncate',
                    ],

                    'dummy_amount' => [
                        'title' => 'Totaal alle facturen',
                        'width' => 'xxsmall truncate',
                    ],

                    'title_dummy' => [
                        'title' => 'Factuur naam',
                        'width' => 'xxsmall truncate',
                    ],

                    'period' => [
                        'title' => 'Factuur omschrijving',
                        'width' => 'xxsmall truncate',
                    ],

                    'unpayed' => [
                        'title' => 'Factuur bedrag',
                        'width' => 'xxsmall truncate',
                    ],
                    'invoicedate' => [
                        'title' => 'Factuur datum',
                        'width' => 'xxsmall truncate',
                    ],
                    'expire_date' => [
                        'title' => 'Factuur vervaldatum',
                        'width' => 'xxsmall truncate',
                    ],
                    'remind_toggle' => [
                        'title' => 'Geen herinnering en aanmaning procedure',
                        'width' => 'hidden truncate',
                    ],

                ])

                ->addFormat('unpayed', function ($value) {
                    return '&euro; ' . new StringFormat($value/100, 'money');
                })

                ->addFormat('invoicedate', function($value, $item){
                    return intval($item['invoicedate']) > 0?
                        date('d-m-Y', strtotime($item['invoicedate'])): '-';
                })

                ->addFormat('expire_date', function($value, $item){
                    return intval($item['date']) > 0?
                        date('d-m-Y', strtotime($item['expire_date'])): '-';
                })

                ->addFormat('bdate', function($value, $item){
                    return intval($item['bdate']) > 0?
                        date('d-m-Y', strtotime($item['bdate'])): '-';
                })
                ->addFormat('address', function ($value,$item) {
                    return $value . ' ' .$item['number']. ' '. $item['build'];
                })
                ->addFormat('remind_toggle', function ($value) {
                    return $value == 0 ? 'Ja' : '';
                })
                ->addFormat('client', function () {
                    return '150';
                })
                ->addFormat('wik', function () {
                    return 'wik';
                })
                ->addFormat('periodvalue', function ($value,$item) {
                    return $value . '-' .$item['identifier_year'];
                })

                ->render($this);


        }


        public function payqReportAction(){

            ini_set('memory_limit', '1768M');
            $this->view->extraWidth = true;

            $data  = $this->transaction->loadReportData(array('showClosed' => $showClosed, 'showOpenAndClosed' => $showOpenAndClosed, 'closedYear' => $closedYear, 'closed_date' => $closed_date));
            $listviewData = $data['data'];
            //p($listviewData,'die');
            $this->view->listView($listviewData)

                ->setTypes([
                    'og_type' => [
                        'title' => 'soort',
                        'width' => 'medium',
                    ],
                    'client' => [
                        'title' => 'opdrachtgever',
                        'width' => 'xxsmall truncate',
                    ],
                    'user_identifier' => [
                        'title' => 'klantnr',
                        'width' => 'xxsmall truncate',
                    ],
                    'rendered_name' => [
                        'title' => 'naam',
                        'width' => 'xxsmall truncate',
                    ],
                    'partner_rendered_name' => [
                        'title' => 'naam_2',
                        'width' => 'xxsmall truncate',
                    ],
                    'attn' => [
                        'title' => 'tav',
                        'width' => 'xxsmall truncate',
                    ],
                    'address' => [
                        'title' => 'adres',
                        'width' => 'xxsmall truncate',
                    ],
                    'zipcode' => [
                        'title' => 'postcode',
                        'width' => 'xxsmall truncate',
                    ],
                    'city' => [
                        'title' => 'plaats',
                        'width' => 'xxsmall truncate',
                    ],
                    'phone_landline' => [
                        'title' => 'telefoon',
                        'width' => 'xxsmall truncate',
                    ],
                    'phone' => [
                        'title' => 'mobiel',
                        'width' => 'xxsmall truncate',
                    ],
                    'email' => [
                        'title' => 'email',
                        'width' => 'xxsmall truncate',
                    ],
                    'companyform' => [
                        'title' => 'bedrijfsvorm',
                        'width' => 'xxsmall truncate',
                    ],
                    'kvk' => [
                        'title' => 'kvk',
                        'width' => 'xxsmall truncate',
                    ],
                    'tax' => [
                        'title' => 'btw',
                        'width' => 'xxsmall truncate',
                    ],
                    'iban' => [
                        'title' => 'rekening',
                        'width' => 'xxsmall truncate',
                    ],
                    'identifier' => [
                        'title' => 'factuurnr',
                        'width' => 'xxsmall truncate',
                    ],
                    'invoicedate' => [
                        'title' => 'factuurdatum',
                        'width' => 'xxsmall truncate',
                    ],
                    'date' => [
                        'title' => 'vervaldatum',
                        'width' => 'xxsmall truncate',
                    ],
                    'unpayed' => [
                        'title' => 'bedrag',
                        'width' => 'xxsmall truncate',
                    ],
                    'wik' => [
                        'title' => 'incassokosten',
                        'width' => 'xxsmall truncate',
                    ],
                    'freefieldone' => [
                        'title' => 'vrijveld1',
                        'width' => 'xxsmall truncate',
                    ],
                    'freefieldtwo' => [
                        'title' => 'vrijveld2',
                        'width' => 'xxsmall truncate',
                    ],
                    'periodvalue' => [
                        'title' => 'periode',
                        'width' => 'xxsmall truncate',
                    ],
                    'objectType' => [
                        'title' => 'objecttype',
                        'width' => 'xxsmall truncate',
                    ],
                    'portefeuille' => [
                        'title' => 'portefeuille',
                        'width' => 'xxsmall truncate',
                    ],
                    'geboortedatum' => [
                        'title' => 'geboortedatum',
                        'width' => 'xxsmall truncate',
                    ],

                ])

                ->addFormat('unpayed', function ($value) {
                    return '&euro; ' . new StringFormat($value/100, 'money');
                })

                ->addFormat('invoicedate', function($value, $item){
                    return intval($item['invoicedate']) > 0?
                        date('d-m-Y', strtotime($item['invoicedate'])): '-';
                })

                ->addFormat('date', function($value, $item){
                    return intval($item['date']) > 0?
                        date('d-m-Y', strtotime($item['date'])): '-';
                })


                ->addFormat('address', function ($value,$item) {
                    return $value . ' ' .$item['number']. ' '. $item['build'];
                })

                ->addFormat('client', function () {
                    return '150';
                })
                ->addFormat('wik', function () {
                    return 'wik';
                })
                ->addFormat('periodvalue', function ($value,$item) {
                    return $value . '-' .$item['identifier_year'];
                })

                ->render($this);


        }

		protected function getExcelReport($paginator, $grouped){
			$this->view->Breadcrumbs()->addCrumb('Openstaande posten overzicht');
			
			$this->disableView();

			$data = $paginator->getAdapter()->getItems(0, null);

			if($grouped){
				$ungrouped_data = array();
				foreach($data as $recipient){
					foreach($recipient['data'] as $item)
						$ungrouped_data[] = $item;

					$ungrouped_data[] = array('project_name' => 'Aantal: ' . count($recipient['data'])) +  $recipient['totals'];

					$ungrouped_data[] = array();
				}

				$ungrouped_data[] = array('project_name' => 'Totaal') +  $this->view->total;

				$disable_excel_totals = true;

				$data = $ungrouped_data;
			}


			$this->view->data = $this->data = $data;

			$this->totals = array(
				'amount' => true,
				'payed' => true,
				'penalty' => true,
				'penalty_payed' => true,
				'unpayed' => true
			);

			$this->types = array(
				'project_name' => array('title' => 'Project'),
				'og_type' => array('title' => 'Type', 'type' => 'og_type'),
				'userId' => ['title' => 'Tenant id'],
				'user_identifier' => array('title' => 'Klantnr'),
				'name' => array('title' => 'Naam'),
                'phone' => array('title' => 'Telefoon', 'type' => 'text'),
				'address' => array('title' => 'Adres'),
				'number' => array('title' => 'Nr.'),
                'notes' => ['title' => 'Notities', 'type' => 'text'],
				'collection' => array('title' => 'Au Inc.', 'type' => 'bool'),
				'blocked' => array('title' => 'Storno', 'type' => 'bool'),
				'payment_date' => array('title' => 'Afspraak', 'type' => 'bool'),
				'count' => array('title' => '#'),
				'date' => array('title' => 'Vervaldatum', 'type' => 'date'),
				'period' => array('title' => 'Periode'),
				'identifier' => array('title' => 'Factuur'),
				'days' => array('title' => 'Dagen'),
				'ownership' => array('title' => 'Type', 'type' => 'ownership'),
				'amount' => array('title' => 'Bedrag', 'type' => 'money'),
				'payed' => array('title' => 'Betaald', 'type' => 'money'),
				'penalty' => array('title' => 'Boete', 'type' => 'money'),
				'penalty_payed' => array('title' => 'Boete bet.', 'type' => 'money'),
				'unpayed' => array('title' => 'Openstaand', 'type' => 'money'),
				'email' => array('title' => 'E-mail'),
			);

			foreach($this->types as $type_key => $type) $this->types[$type_key]['column'] = $type_key;

			foreach($this->view->data as $item_key => $item)
				foreach($this->types as $type)
					if(isset($item[$type['column']]))
						if($type['type'] == 'money')
							$this->data[$item_key][$type['column']] = '&euro; ' .  (string) new StringFormat($item[$type['column']]/100, 'money');
						elseif($type['type'] == 'bool')
							$this->data[$item_key][$type['column']] = $item[$type['column']] ? 'Ja' : 'Nee';
						elseif($type['type'] == 'date')
							$this->data[$item_key][$type['column']] = date('d-m-Y', strtotime($item[$type['column']]));
						elseif($type['type'] == 'ownership')
							$this->data[$item_key][$type['column']] = $item[$type['column']] == 'rental' ? 'Huur' : 'Koop';
						elseif($type['type'] == 'og_type')
							$this->data[$item_key][$type['column']] = ucfirst($item[$type['column']]);
                        elseif($type['type'] == 'text') {
                            $this->data[$item_key][$type['column']] = trim(str_replace('  ', ' ', str_replace("\n", ' ', $item[$type['column']])));
                        }

			ob_clean();
			//header('Content-type: application/vnd.ms-excel');
			//header('Cache-Control: private');
			//header('Pragma: private');
			//header('Content-disposition: attachment; filename="Openstaande posten.xls"');

			include('application/views/scripts//partials/default-view/list/excel.phtml');
		}

		public function getReportFiltersAction(){

			if(!($data = $this->_getParam('data')))
				$data = $this->transaction->getReportData($filters, $this->_getParam('sorted'));

			$this->view->filters = $this->transaction->getReportFilterData($data);
		}

		public function getReportFilters(){
			$types = array(
				'project' => 		array('exact' => true),
				'corporation' => 	['exact' => true],
				'og_type' => 		array('exact' => true),
				'project_type' => 	array('exact' => true),
				'name' => 			array(),
				'investor_objects'=>array('exact' => true),
				'payment' => 		array(),
				'blocked' => 		array('custom' => true),
				'expired' => 		array('exact' => true, 'onlyBool' => true),
				'date' => 			array('date' => true),
				'count' => 			array('exact' => true),
				'penalty' => 		array('exact' => true),
				'identifier' =>		array(),
				'ownership' => 		array('exact' => true),
				'period' => 		array('exact' => true),
				'days' => 			array('exact' => true),
				'remind' => 		array('exact' => true),
				'remind_special_status' => array('custom' => true),
				'has_penalty' => 	array('exact' => true, 'onlyBool' => true),
				'payment_date' => 	array('custom' => true),
				'arrangement' => 	array('exact' => true, 'onlyBool' => true),
				'is_purchase' => ['exact' => true, 'onlyBool' => true],
				'remind_toggle' => ['exact' => true, 'onlyBool' => true]
			);

			if(Settings::get('modules_contracts')) {
				$types['payment_discount_present'] = ['exact' => true];
				$types['contract_type'] = ['exact' => true];
			}

			foreach($types as $filtername => $options){
				if(is_null($this->_getParam($filtername))){
					if($filtername == 'remind'){
						$this->_setParam('remind_special_status', 'NULL');
					} else
						continue;
				}

				$url_encoded = $this->_getParam('excel') || $this->_getParam('print');

				$value = $this->_getParam($filtername);

				if($url_encoded){
					$json_encoded_array = json_decode($value);

					if(is_array($json_encoded_array))
						$value = $json_encoded_array;
				}

				if($filtername == 'remind' && in_array('false', (array) $value))
					$this->_setParam('remind_special_status', 'NULL');

				if(is_array($value)){
					if(count($value) > 1 || (count($value) == 1 && trim($value[0]) != '' && $value[0] != 'false'))
						$filters[$filtername] = array('value' => $value, 'options' => $options);
				} else {
					if(trim($value) != '' && $value !== 'false')
						$filters[$filtername] = array('value' => $value, 'options' => $options);
				}
			}

			return $filters;
		}

		public function getPaymentsAction(){
			$this->disableView(true);


			$tp_model = new TransactionsPayments();
			$t_model = new Transactions();

			$invoice_info_select = db()->select()
				->from(['t' => 'transactions'], false)
				->joinLeft(['i' => 'invoices'], 'i.id = t.invoice', ['invoiceId' => 'id','financial_invoice_id', 'financial_no_sync'])
				->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', ['date', 'invoiceRunId' => 'id'])
				->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', ['custom_title' => 'title'])
				->where('t.id = ?', $this->_getParam('transaction'));

			$select = $tp_model->select()
				->from(array('transactions_payments'))
				->where('transaction = ?', $this->_getParam('transaction'))
				->order('date_created ASC')
			;

			$this->view->info = db()->fetchRow($invoice_info_select);
			$this->view->payments = $tp_model->fetchAll($select);
			$this->view->transaction = $t_model->getById($this->_getParam('transaction'));

			$this->view->layout = $this->_getParam('layout');
		}


		public function getPaymentsListAction(){
			$this->view->headLink()->appendStylesheet('media/style/transaction/get-payments-list.css');

			$params = $this->getAllParams();
			$params['url-override'] = 'transaction/get-payments-list';
			if(trim($params['view_template']) == false)
				$params['view_template'] = 'support';

			$ibanParser = new Iban_Parser();
			$tplModel = new Controllers_Transaction_PaymentsLists();

			if ($userId = $this->_getParam('user')) {
				$payments = $tplModel->getPaymentsList($userId, false);
				$params['url-override'] .= "/user/$userId";

			} else if ($investorId = $this->_getParam('investor')) {
				$payments = $tplModel->getPaymentsList(false, $investorId);
				$params['url-override'] .= "/investor/$investorId";
			}

			$this->view->list = $this->view->listView($payments, $params)

				->setTypes([
					'details' => [
						'title' => '&nbsp;',
						'width' => 'xxxxsmall truncate',
					],
					'date' => [
						'title' => 'Datum',
						'width' => 'xsmall truncate',
						'order_using_raw' => true,
					],
					'amount' => [
						'title' => 'Bedrag',
						'width' => 'xxsmall truncate',
						'order_using_raw' => true,
					],

					'description' => [
						'title' => 'Omschr.',
						'width' => 'xlarge truncate',
					],
				])

				->addFormat('details', function($value){
					return "<div class='payment_details collapsed' payment_id='$value'>&nbsp;</div>";
				})

				->addFormat('date', 'date')

				->addFormat('amount', 'money_from_int_db')

				->addFormat('bankaccount_customer', function($value) use($ibanParser) {
					return ($ibanParser->Verify($value))?
						(string) new StringFormat($value, 'iban'):
						(string) new StringFormat($value, 'bank');
				})

                ->addTotals(['amount'])



                ->setOptions([
					'item_title' 					=> "&nbsp;",
					'show_title' 					=> false,
					'totals'					 	=> false,
					'render_to_controller'			=> false,
					'paginator_items_per_page'		=> 10,
				])

				->render($this)
			;
		}

		public function getPaymentsListPrintAction() {
			/*
				The next two lines are very ugly, but due to combination of time pressure and 
				having to circumvent the normal ListView functionality the normal/better ways
				are not viable.
			*/
			echo '<link href="/media/style/transaction/get-payments-list-print.css" media="screen, print" rel="stylesheet" type="text/css" />';
			echo '<link href="/OmniBoxx/media/style/transaction/get-payments-list-print.css" media="screen, print" rel="stylesheet" type="text/css" />';

			$params = $this->getAllParams();
			$params['view_template'] = 'print';

			$monthsAgo = is_numeric($params['months_ago']) && intval($params['months_ago']) > 0? intval($params['months_ago']): 0;

			$iLib = new Invoice();
			$tplModel = new Controllers_Transaction_PaymentsLists();
			$ctpd = new Controllers_Transaction_PaymentsDetails();

			$payments = $tplModel->getPaymentsList($params['user'], $this->_getParam('investor'));

			$output = [];
			$output[] = [
				'date' 					=> 'Betaalgeschiedenis',
				'amount' 				=> '',
				'bankaccount_customer' 	=> '',
				'status' 				=> '',
				'title'                 => '',
				'description' 			=> '',
			];

			$output[] = [
				'date'			        => 'Huidige datum:',
				'amount'		        => date('d-m-Y'),
				'bankaccount_customer' 	=> '',
				'status' 				=> '',
				'title'                 => '',
				'description' 			=> '',
			];

			if(is_numeric($params['user'])) {
				$output[] = [
					'date'			        => 'Klantnaam:',
					'amount'		        => User::buildname(false, $params['user']),
					'bankaccount_customer' 	=> '',
					'status' 				=> '',
					'title'                 => '',
					'description' 			=> '',
				];

				$output[] = [
					'date'			        => 'Klantnummer:',
					'amount'		        => User::getNumber($params['user']),
					'bankaccount_customer' 	=> '',
					'status' 				=> '',
					'title'                 => '',
					'description' 			=> '',
				];
			}

			$output[] = [ 'date' => '', 'amount' => '', 'bankaccount_customer' => '', 'status' => '', 'title' => '', 'description' => '', ];
			$output[] = [ 'date' => '', 'amount' => '', 'bankaccount_customer' => '', 'status' => '', 'title' => '', 'description' => '', ];

			foreach ($payments as $i => $payment) {
				if($monthsAgo > 0 && strtotime("$monthsAgo months ago") > strtotime($payment['date']))
					continue;

				$output[] = [
					'date' 					=> 'Transactie datum',
					'amount' 				=> 'Bedrag',
					'bankaccount_customer' 	=> 'IBAN/Bankrek.',
					'status' 				=> '',
					'title'                 => '',
					'description' 			=> 'Banktransactie omschrijving',
				];

				$output[] = [
					'date' 					=> (strtotime($payment['date'])? date('d-m-Y', strtotime($payment['date'])): '-'),
					'amount' 				=> '€ '.($payment['direction'] == 'incoming'? '': '- ').(new StringFormat($payment['amount'], 'money_from_int_db')),
					'bankaccount_customer'	=> (trim($payment['bankaccount_customer']) != false? $payment['bankaccount_customer']: '-'),
					'status'				=> '',
					'description' 			=> $payment['description'],
				];

				$details = $ctpd->getPaymentsDetails($payment['id']);
				if(count($details) > 0)
					$output[] = [
						'date' 					=> '',
						'amount' 				=> 'Verwerk datum',
						'bankaccount_customer' 	=> 'Geboekte bedrag',
						'status' 				=> 'Factuurnummer',
						'title'                 => 'Factuurtitel',
						'description' 			=> 'Type',
					];

				foreach ($details as $detail) {
					if(trim($detail['ic_title']) != false) {
						$title = $detail['ic_title'];
					} else {
						$title = ucfirst($iLib->translatePeroid($detail['ir_period'], $detail['ir_periodvalue']));
						$title .= ' '.date('Y', strtotime($detail['ir_start']));
					}

					$output[] = [
						'date' 					=> '',
						'amount' 				=> (strtotime($detail['date'])? date('d-m-Y', strtotime($detail['tp_date_created'])): '-'),
						'bankaccount_customer' 	=> '€ '.($detail['direction'] == 'incoming'? '': '- ').(new StringFormat($detail['amount'], 'money_from_int_db')),
						'status' 				=> $iLib->getIdentifier($detail['id']),
						'title'                 => $title,
						'description' 			=> TransactionsPayments::getStatusLabel($detail['tp_status'], [
														'user_buildname' 	=> User::buildname(false, $detail['tp_user']),
														'date_created'		=> $detail['tp_date_created'],
													]),
					];
				}

				if($i >= count($payments) -1 )
					break;

				$output[] = [ 'date' => '', 'amount' => '', 'bankaccount_customer' => '', 'status' => '', 'title' => '', 'description' => '', ];
				$output[] = [ 'date' => '', 'amount' => '', 'bankaccount_customer' => '', 'status' => '', 'title' => '', 'description' => '', ];
			}

			$this->view->list = $this->view->listView($output, $params)

				->setTypes([
					'date' => [
						'title' => 'Transactie datum',
						'width' => 'xxxxxlarge truncate',
					],
					'amount' => [
						'title' => 'Bedrag',
						'width' => 'xxxxxlarge truncate',
					],
					'bankaccount_customer' => [
						'title' => 'IBAN/Bankrek.',
						'width' => 'large truncate',
					],
					'status' => [
						'title' => '',
						'width' => 'medium truncate',
					],
					'title' => [
						'title' => '',
						'width' => 'medium truncate',
					],
					'description' => [
						'title' => 'Banktransactie omschrijving',
						'width' => 'xlarge truncate',
					],
				])

				->setOptions([
					'item_title' 					=> "Transactie",
					'show_title' 					=> false,
				])

				->render($this)
			;
		}

		public function paymentDetailsAction() {
			$payment_id = $this->_getParam('payment_id');

			if(intval($payment_id) <= 0)
				dieWithStatuscode();

			$ctpd = new Controllers_Transaction_PaymentsDetails();

			$this->view->rows = $ctpd->getPaymentsDetails($payment_id);
		}

		public function setPaymentInvestorProvisionAction(){
			$this->disableView();

			if(!$this->_getParam('id') || !$this->_getParam('toggle')) return;

			$tp_model = new TransactionsPayments();

			$tp_row = $tp_model->getById($this->_getParam('id'));

			$tp_row->investor_provision = $this->_getParam('toggle') == 'true';

			$tp_row->save();
		}

		public function getUnidentifiedData(){
			if ($this->getRequest()->getPost('json')) {
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);

				$data = $this->getRequest()->getPost();
				$offset = is_numeric($data['from']) ? $data['from'] : false;

				$data = $this->transaction->find($data, $offset);

				echo Zend_Json::encode(count($data) > 0 ? $data : false);
			}
			
			$this->view->filter_name = $filter_name = $this->_getParam('filter-name') ? $this->_getParam('filter-name') : false;
			$this->view->filter_type = $filter_type = $this->_getParam('filter-type') && $this->_getParam('filter-type') != 'false' ? $this->_getParam('filter-type') : false;
			$this->view->filter_amount = $filter_amount = $this->_getParam('filter-amount') ? $this->_getParam('filter-amount') : false;

			$unidentified = $this->transaction->findUnidentified($filter_name, $filter_type, $filter_amount);
			
			$paginator = Zend_Paginator::factory($unidentified);
			$paginator->setCurrentPageNumber($this->_getParam('page'));
			$paginator->setItemCountPerPage(25);

			return $paginator;
		}

		public function findUnidentifiedAction() {
			$this->disableView();

			ob_clean();
			$params = [
				'value' => false,
				'user' => false,
				'investor' => false,
				'debit' => false,
				'address' => false,
				'findInvoices' => false,
				'search_by_amount' => false,
				'corporation_id' => null,
                'search_by_invoice' => false,
                'invoice_identifier' => false
			];

			foreach($params as $param => $param_value)
				if($param_value = $this->_getParam($param))
					$params[$param] = $param_value;

			if($tri_id = $this->_getParam('tri_id'))
				if($tri_row = db()->fetchRow(db()->select()->from('transactions_import_payments')->where('id = ?', $tri_id)))
					$params['matching_iban'] = strtoupper(str_replace(' ', '', $tri_row['iban']));

			$results = $this->transaction->findUnpaid($params);

			echo Zend_Json::encode($results);
		}

		public function findObjectsAction() {
			$this->disableView();

			$string = $this->_getParam('string') ? $this->_getParam('string') : false;
			$string = $this->_getParam('value') && !$string ? $this->_getParam('value') : $string;
			$debet = $this->_getParam('debet') ? $this->_getParam('debet') : false;
			$limit = $this->getParam('limit') ? $this->getParam('limit') : false;
			$contractType = $this->_getParam('contractType') ? $this->_getParam('contractType') : false;
			$form_values = $this->_getParam('form_values') ? json_decode($this->_getParam('form_values'), true) : false;
			$contractType = $this->_getParam('contractType') ?: false;
			$findExternal = $this->getParam('find_external') ? true : false;
			$available_from = false;

			if($contractType == 'ContractsPeriodical')
				if($form_values)
					if(isset($form_values['contract_relation_uo_form']))
						if(isset($form_values['contract_relation_uo_form']['from']))
							$available_from = strtotime($form_values['contract_relation_uo_form']['from']);

			$objects = $this->transaction->findObjects($string, $debet, $contractType, $findExternal);

			if($available_from !== false){

				$ctModel = new ContractsTransient();

				foreach($objects as $object_key => $object){

					$result = $ctModel->isDateAvailable($object['id'], $available_from, true);

					if($result !== 0) unset($objects[$object_key]);
				}
			}

			$objects = orderArrayByLevenstein($objects, $string, $compare_key = 'name');

			if($limit > 0)
				$objects = array_slice($objects, 0, $limit);

			echo Zend_Json::encode($objects);
		}

		public function deleteUnidentifiedAction()
		{
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$delete = $this->_getParam('delete');
			$blacklist = $this->_getParam('blacklist');

			if (!$delete && !$blacklist) die();

			$delete_list = (array)json_decode($delete);
			$blacklist_list = (array)json_decode($blacklist);

			$blockIbanService = new \Transactions\Iban\BlockIbanService();
			$deletePaymentService = new \Transactions\Payment\DeletePaymentService();

			if (!empty($blacklist_list)) {
				foreach ($blacklist_list as $transactionImportPaymentId) {
					$blockIbanService->execute(new \Transactions\Iban\BlockIbanRequest($transactionImportPaymentId));
				}
			}

			if (!empty($delete_list)) {
				foreach ($delete_list as $transactionImportPaymentId) {
					$deletePaymentService->execute(new \Transactions\Payment\DeletePaymentRequest($transactionImportPaymentId));
				}
			}
		}

		public function unmatchAction(){
			$this->disableView();

			$ids = $this->_getParam('ids');

			if(!is_array($ids)) return;

			$tip_model = new TransactionsImportPayments();

			foreach($ids as $id)
				$tip_model->unmatch($id);
		}

		public function identifyAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$importPayments = $this->_getParam('import_payment');
			$identify = $this->_getParam('identify');
			$viewHash = $this->_getParam('hash');
			$save_iban = $this->_getParam('save_iban');

			if (!$importPayments || !$identify || !$viewHash) die();

			$this->transaction->identifyManually = true;
			$this->transaction->save_iban =  $save_iban === true || $save_iban === 'true' ? true : false;

			$response = $this->transaction->identify($importPayments, $identify, $viewHash);

			echo Zend_Json::encode($response);
		}

		public function identifyCostsAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = $this->_getParam('transaction');
			$object_id = $this->_getParam('object_id');
			$component = $this->_getParam('component');
			$description = $this->_getParam('description');
			$amount = $this->_getParam('amount') * 100;
			$hash = $this->_getParam('hash');

			if (!$transaction || !$amount || !$component || !$hash) die();

			$response = $this->transaction->identifyCost($transaction, $object_id, $component, $amount, $description, $hash);

			echo Zend_Json::encode($response);
		}		

		public function mollieIdealAction() {
            if (
                !Settings::get('mollie_ideal_enabled') ||
                (!$this->_getParam('identifier') && !$this->_getParam('COMPLUS') && !$this->_getParam('page'))
            ) {
                $this->render('ideal/error');
                return;
            }

            $hash = $this->_getParam('identifier') ?: $this->_getParam('COMPLUS');
            $transactionRowData = $this->getTransactionRowDataByHash($hash);
            $invoiceRunData =  InvoicesRuns::getInfo($transactionRowData['invoicerun']);

            if (empty($transactionRowData)) {
                throw new Exception('Transactie niet gevonden: ' . $hash);
            }

            $invoiceModel = new Invoices();
            $invoiceRow = $invoiceModel->getById($transactionRowData['invoice']);
            if ($invoiceRow === null) {
                throw new Exception('Factuur niet gevonden: ' . $transactionRowData['invoice']);
            }
            $identifier = $invoiceModel->getIdentifier($invoiceRow->id);

            $paymentTotal = $transactionRowData['amount'] - $transactionRowData['payed'];

            $paymentData = [
                'amount' => [
                    'currency' => 'EUR',
                    'value' => number_format($paymentTotal / 100, 2, '.', '')
                ],
                'description' => 'Factuur ' . $identifier,
                'metadata' => json_encode([
                    'transactionId' => $transactionRowData['id']
                ])
            ];
            $service = new \Ideal\Mollie\Application\CreateMolliePaymentService();
            try {
                $service->create($paymentData, $invoiceRunData['corporation_id']);
            } catch (Exception $e) {
                error_log($e->getMessage());
                $this->render('ideal/error');
                return;
            }
        }

        private function getTransactionRowDataByHash($hash) {
		    $transactionModel = new Transactions();
		    return $transactionModel->genMatchSelect(['hash' => $hash])->query()->fetch();
        }

        public function mollieIdealPaymentAction() {
            $this->disableView();
            $postParams = $this->getRequest()->getParams();
            $paymentId = $postParams['id'];

            if (!$paymentId) {
                \Logger::add(['mollie-payments', 'invoice-payments', 'payment-status-webhook'], 'PaymentId not set');
                dieWithStatuscode(500);
            }

            try {
                $service = new \Ideal\Mollie\Application\ProcessMolliePaymentService(
                    new \Transactions(),
                    new \TransactionsPayments()
                );

                $service->process($paymentId);
            } catch (Exception $e) {
                \Logger::add(['mollie-payments', 'invoice-payments', 'payment-status-webhook'], 'Problem with the processing part. PaymentId: ' . $paymentId . ' - Error: ' . $e->getMessage(), $e->getTraceAsString());
                dieWithStatuscode(500);
            }
        }

		public function idealAction() {
			if (
					(!$this->_getParam('identifier') && !$this->_getParam('COMPLUS') && !$this->_getParam('page')) ||
					(!Settings::get('ideal_enabled')) ||
					(Settings::get('ideal_ip_restricted_enabled') && $_SERVER['REMOTE_ADDR'] != Settings::get('ideal_ip_restricted'))
				)
					$this->render('ideal/error');

			$hash = $this->_getParam('identifier') ? $this->_getParam('identifier') : $this->_getParam('COMPLUS');

			$this->view->tempRights = 'guest';

			$t = new Transaction();

			if(!$this->_getParam('page')) {
				$this->view->transaction = $t->fetchByHash($hash);


				$corp = db()->select()
							->from(array('t' => 'transactions'), array())
							->where('t.id = ?', $this->view->transaction['id'] )
							->joinLeft(array('ir' => 'invoices_run'),	'ir.id = t.invoicerun', array())
							->joinLeft(array('p'  => 'projects'),		'p.id = ir.project', array())
							->joinLeft(array('c'  => 'corporations'),	'c.id = p.corporation', 
								array(
									'name',
									'ideal_enabled',
									'ideal_id',
									'ideal_secret',
									'ideal_bank',
								)
							)
							->query()
							->fetchAll()
						;

				$corp = $corp[0];

				if($corp['ideal_enabled'] !== '1') {
					$this->render('ideal/error');
					return;
				}

				$iDealSettings = array();

				if (isset($corp['ideal_id']) && isset($corp['ideal_secret']) && isset($corp['ideal_bank'])) {
					$iDealSettings['company_name']	= $corp['name'];
					$iDealSettings['ideal_id']		= $corp['ideal_id'];
					$iDealSettings['ideal_secret']	= $corp['ideal_secret'];
					$iDealSettings['ideal_bank']	= $corp['ideal_bank'];

				} else {
					$iDealSettings['company_name']	= Settings::get('general_company');
					$iDealSettings['ideal_id']		= Settings::get('ideal_id');
					$iDealSettings['ideal_secret']	= Settings::get('ideal_secret');
					$iDealSettings['ideal_bank']	= Settings::get('ideal_bank');
				}

				$this->view->iDealSettings = $iDealSettings;
			}

			$this->view->page = $this->_getParam('page');

			$this->view->Breadcrumbs()->disable();
			$this->view->ideal = array();
		}

		public function idealPaymentAction() {
			if ($this->_getParam('page') !== 'accept')
				die();

			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$post = $this->getRequest()->getPost();

			$sha_array = array('AAVADDRESS', 'AAVCHECK', 'AAVZIP', 'ACCEPTANCE', 'ALIAS', 'AMOUNT', 'BRAND', 'CARDNO', 'CCCTY', 'CN', 'COMPLUS', 'CURRENCY', 'CVCCHECK', 'DCC_COMMPERCENTAGE', 'DCC_CONVAMOUNT', 'DCC_CONVCCY', 'DCC_EXCHRATE', 'DCC_EXCHRATESOURCE', 'DCC_EXCHRATETS', 'DCC_INDICATOR', 'DCC_MARGINPERCENTAGE', 'DCC_VALIDHOUS', 'DIGESTCARDNO', 'ECI', 'ED', 'ENCCARDNO', 'IP', 'IPCTY', 'NBREMAILUSAGE', 'NBRIPUSAGE', 'NBRIPUSAGE_ALLTX', 'NBRUSAGE', 'NCERROR', 'ORDERID', 'PAYID', 'PM', 'SCO_CATEGORY', 'SCORING', 'STATUS', 'TRXDATE', 'VC');

			uksort($post, 'strnatcasecmp');

			foreach ($post as $key => $item) {
				if (in_array(strtoupper($key), $sha_array) && $item != '') {
					$shasign .= strtoupper($key) . '=' . $item . Settings::get('ideal_secret');
				}
			}

			if (strtoupper(sha1($shasign)) == strtoupper($post['SHASIGN']) && $post['STATUS'] == 9) {

				$t = new Transactions();
				$row = $t->fetchRow($t->select()->where('hash LIKE ?', $post['COMPLUS']));

				if ($row) {
					// ideally we'd get determine the payment amount from the iDeal response, but the 
					// below calculation is also used to determine the open amount when starting the iDeal transaction
					// so it only goes wrong if something changes during the iDeal procedure.
					$openAmount = ($row->amount + $row->penalty) - ($row->payed + $row->penalty_payed);

					$row->payed = $openAmount;
					$row->closed = 1;
					$row->save();
			
					TransactionsPayments::add(array(
						'transaction' => 			$row->id,
						'status' => 				'confirmed',
						'date' => 					date('d-m-Y'),
						'direction' =>				$row->type == 'c' ? 'incoming' : 'outgoing',
						'amount' =>					$openAmount,
						'description' =>			'Betaald via iDEAL'
					));
				}

			}

		}
	
		public function testImportAction(){
			$this->disableView();
			
			$t = new Transaction();
			$t->testImport($this->_getParam('string'));
		}

		public function listAction() {
			$data = array('status' => 'open');
			$this->view->transactions = $this->transaction->find($data, '0', '10');
		}

		
		public function payedAction() {
			
			$this->disableView();
			
			$t_model = new Transactions();
			$t_lib = new Transaction();
			$row = $t_model->getById($this->_getParam('id'));
			
			if (!$row)
				return;
			
			$t_lib->edit($row->id, array('payed' => ($row->amount/100)));	
		}

		public function setAllPayedAction(){
			$this->disableView();

			if(!DEBUGGING) return;

			$transaction_model = new Transactions();
			$transaction_lib = new Transaction();

			$select = $transaction_model->select()->where('invoice > 0')->where('payed < amount');

			foreach($transaction_model->fetchAll($select) as $transaction){

				$params = array(
					'payed' => $transaction->amount/100,
					'payed_date' => '07-04-2014',
					'payed_status' => 'confirmed',
					'payed_description' => 'Automatisch op betaald gezet'
				);

				$transaction_lib->edit($transaction->id, $params);
			}

		}

		public function editAction() {
			$this->view->Breadcrumbs()->addCrumb('Transactie bewerken', 'transaction/edit/id/' . $this->_getParam('id') . '/');

			//create form
			$form = new Form($this, 'transaction/edit');
			$transaction = new Transaction();
			$invoice = new Invoice();
			$saved = false;

			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())){
				$saved = true;
				$transaction->edit($this->_getParam('id'), $form->getValues());
			}

			$transactions = $transaction->find(array('id' => $this->_getParam('id')));
			$this->view->transaction = $transaction = $transactions['items'][0];
			
			$transaction['payed'] = $transaction['payed'] / 100;
			$transaction['penalty_payed'] = $transaction['penalty_payed'] / 100;

			$form->populate($transaction);

			$this->view->saved = $saved;
			$this->view->form = $form;
		}

		public function penaltyAction() {
			$this->view->Breadcrumbs()->addCrumb('Boete toevoegen of bewerken', 'transaction/penalty/transaction/' . $this->_getParam('transaction') . '/');

			//create form
			$form = new Form($this, 'transaction/penalty');

			$transaction = new Transaction();
			$user = new User();

			$data = $transaction->get($this->_getParam('transaction'));

			if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
				$this->disableView();

				$data = $form->getValues();
				$transaction->editPenalty($this->_getParam('transaction'), $data['fine']);

                header('Location: ' . $_SERVER['HTTP_REFERER']);
			}

			$form->populate(array('fine' => $data['penalty'] / 100));
			$this->view->transaction = $data;
			$this->view->form = $form;
		}

		public function rebuiltAmountsAction(){
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			
			$db = db();
			$t = new Transactions();
			
			$select = $db->select()
				->from(array('i' => 'invoices'),		array('id'))
				->joinLeft(array('t' => 'transactions'),
					't.invoice = i.id',					array('transaction' => 'id'))
				->where('i.run = ?', $this->_getParam('id'));
			
			foreach($db->fetchAll($select) as $item){
				$i = new Invoice();
				$i->id = $item['id'];
				$i->build();
				
				$row = $t->fetchRow($t->select()->where('id = ?', $item['transaction']));
				
				$row->amount = abs(round($i->total * 100));
				$row->adjustment = round($i->adjustmentTotal * 100);
				$row->type = $i->total > 0 ? 'c' : 'd';
				
				//$row->save();
			}
		}
		
		public function buildClieopAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$transaction = new Transaction();
			$transaction->force_create_new_collection = true;
			$transaction->monthlyClieop(array($this->_getParam('id')));
			$transaction->addBankGroupedCollection($this->_getParam('id'));
		}

	}
