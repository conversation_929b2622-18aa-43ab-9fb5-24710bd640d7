<?php


class Complaint_BudgetAuthorizationController extends \GlobalController
{
    private $categoryId;

    public function preDispatch()
    {
        parent::preDispatch();
        $this->view->Breadcrumbs()
            ->addCrumb('Complaint Budget Authorization', 'complaint_budget-authorization-controller');

    }


    public function indexAction()
    {
        $this->_helper->redirector(
            'list',
            'complaint_budget-authorization'
        );
        return;
    }

    public function listAllAction()
    {
        $supportComplaintsRows = $this->getSupportComplaintsNeedsAutorisation();
        $showSupportComplaintsRows = [];

        foreach ($supportComplaintsRows as $supportComplaintsRow) {
            $supportComplaintId = $supportComplaintsRow['id'];
            $supportComplaintsCategoryId = $supportComplaintsRow['category'];
            $nextInLineBudgetAuthorizationRuleRow = $this->getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId);
            $supportComplaintsRow['authorise'] = $nextInLineBudgetAuthorizationRuleRow['department_name'] . ' - ' . $nextInLineBudgetAuthorizationRuleRow['acl_role_name'] ;
            $showSupportComplaintsRows[] = $supportComplaintsRow;
        }

        $this->view->ListView($showSupportComplaintsRows)
            ->setTypes([
                'title' => ['title' => 'Titel', 'width' => 'xxlarge truncate'],
                'budget' => ['title' => 'Bedrag', 'width' => 'xxsmall truncate'],
                'date' => ['title' => 'Datum', 'width' => 'medium truncate'],
                'support_complaints_projects_title' => ['title' => 'Project', 'width' => 'xxlarge truncate'],
                'support_complaints_projects_budget' => ['title' => 'Project budget', 'width' => 'small truncate'],
                'authorise' => ['title' => 'Autorisatie door', 'width' => 'medium truncate'],
                'budgetAuthorizationHistory' => ['title' => '', 'width' => 'xxxxsmall'],
            ])
            ->setFilters([])
            ->addFormat('date', 'relativeDate')
            ->addFormat('budget', 'money')
            ->addFormat('support_complaints_projects_budget', 'money')
            ->addFormat('total_budget', 'money')
            ->addFormat('title', function($value, $row) {
                $htmlStr = '';
                $complaintURL ='support/show/type/complaint/id/'.$row['id'];

                $complaintLink = "<a href='{$complaintURL}' target='_tab'>{$value}</a>";

                $htmlStr .= $complaintLink;

                return $htmlStr;
            })
            ->addFormat('budgetAuthorizationHistory', function($value, $row) {
                $htmlStr = '';
                $htmlStr .= "<span title='{$value}'  hintOffset='{x:-25, y:0}'></span>";

                return $htmlStr;

            })

            ->render($this);
    }

    public function listAction()
    {

        $showSupportComplaintsRows = [];
        $supportComplaintsRows = $this->getSupportComplaintsNeedsAutorisation();
        foreach ($supportComplaintsRows as $supportComplaintsRow) {
            $supportComplaintId = $supportComplaintsRow['id'];
            $supportComplaintBudget= $supportComplaintsRow['budget'];
            $supportComplaintsCategoryId = $supportComplaintsRow['category'];

            if (!$supportComplaintBudget) {
                continue;
            }

            $budgetAuthorizationListRulesRows = $this->getBudgetAuthorizationRules($supportComplaintsCategoryId);
            if (!$budgetAuthorizationListRulesRows) {
                continue;
            }

            $budgetAuthorizationListRulesByUserRows = $this->getBudgetAuthorizationRulesByUser($supportComplaintsCategoryId);
            if (!$budgetAuthorizationListRulesByUserRows) {
                continue;
            }

            if ($this->isUnAutorisation($supportComplaintId)) {
                continue;
            }

            $nextInLineBudgetAuthorizationRuleRow = $this->getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId);

            if (!$nextInLineBudgetAuthorizationRuleRow) {
                $this->saveComplaintBudgetAuthorization($supportComplaintId, true, '');
                continue;
            }

            if (!$this->isThisAuthorizationRulesToDoByCurrentUser($nextInLineBudgetAuthorizationRuleRow)) {
                continue;
            }

            $supportComplaintProjectId = $supportComplaintsRow['support_complaint_project_id'];
            if ($supportComplaintProjectId) {
                $totalBudget = ComplaintBudgetAuthorization::getUsedProjectBudget($supportComplaintProjectId);
            } else {
                $totalBudget = '';
            }

            $supportComplaintsRow['total_budget'] = $totalBudget;


            $budgetAuthorizationHistoryHtmlStr = $this->reanderBudgetAuthorizationHistory($supportComplaintId);

            $supportComplaintsRow['budgetAuthorizationHistory'] = htmlentities($budgetAuthorizationHistoryHtmlStr);

            $showSupportComplaintsRows[] = $supportComplaintsRow;
        }


        $this->view->ListView($showSupportComplaintsRows)
            ->setTypes([
                'title' => ['title' => 'Titel', 'width' => 'xxlarge truncate'],
                'budget' => ['title' => 'Bedrag', 'width' => 'xxsmall truncate'],
                'date' => ['title' => 'Datum', 'width' => 'xxsmall truncate'],
                'support_complaints_projects_title' => ['title' => 'Project', 'width' => 'xxlarge truncate'],
                'support_complaints_projects_budget' => ['title' => 'Project budget', 'width' => 'small truncate'],
                'total_budget' => ['title' => 'Verbruikt', 'width' => 'small truncate'],
                'budgetAuthorizationHistory' => ['title' => '', 'width' => 'xxxxsmall'],
                'actions' => ['title' => '', 'width' => 'xxsmall'],
            ])
            ->setFilters([])
            ->addFormat('date', 'relativeDate')
            ->addFormat('budget', 'money')
            ->addFormat('support_complaints_projects_budget', 'money')
            ->addFormat('total_budget', 'money')
            ->addFormat('title', function($value, $row) {
                $htmlStr = '';
                $complaintURL ='support/show/type/complaint/id/'.$row['id'];

                $complaintLink = "<a href='{$complaintURL}' target='_tab'>{$value}</a>";

                $htmlStr .= $complaintLink;

                return $htmlStr;
            })
            ->addFormat('budgetAuthorizationHistory', function($value, $row) {
                $htmlStr = '';
                $htmlStr .= "<span title='{$value}'  hintOffset='{x:-25, y:0}'></span>";

                return $htmlStr;

            })
            ->addFormat('actions', function($value, $row) {
                $htmlStr = '';
                $approvedURL = 'complaint_budget-authorization/approved/complaintId/'.$row['id'];
                $deniedURL ='complaint_budget-authorization/denied/complaintId/'.$row['id'];

                $approvedLink = "<a href='{$approvedURL}'<i class='fa fa-thumbs-up tooltip' style='color:green; pointer' aria-hidden='true' title='Goedkeuren' rel=' '></i></a>";
                $deniedLink = "<a href='{$deniedURL}'<i class='fa fa-thumbs-down tooltip' style='color:red; cursor: pointer' aria-hidden='true' title='Afgekeuren' rel=' '></i></a>";

                $htmlStr .= $approvedLink;
                $htmlStr .= $deniedLink;

                return $htmlStr;
            })
            ->render($this);
    }

    public function approvedAction()
    {
        $this->disableView();
        $supportComplaintId = $this->getParam('complaintId');

        $this->saveComplaintBudgetAuthorization($supportComplaintId, true, '');

        $supportcomplaint = new SupportComplaints();

        $supportComplaintRow = $supportcomplaint->getById($supportComplaintId);
        if (!$supportComplaintRow) {
            return false;
        } else  {
            $complaint = new Complaint();
            if($supportComplaintRow->inform_contact)
                $complaint->informContact($supportComplaintRow->id, $supportComplaintRow->inform_contact, $supportComplaintRow->inform_contact_person);
        }

        $this->indexAction();
        return;
    }

    public function deniedAction()
    {
        $supportComplaintId = $this->getParam('complaintId');

        if (!$this->isThisAuthorizationRulesToDoByCurrentUserAndSupportComplaintId($supportComplaintId)) {
            $this->indexAction();
        }

        $form = new Form($this, 'ComplaintDeniedBudgetAuthorization');

        if ($this->getRequest()->isPost()) {
            $postData = $this->getRequest()->getPost();
            $description = $postData['main']['description'];
            $this->saveComplaintBudgetAuthorization($supportComplaintId, false, $description);

            $this->indexAction();
        }

        $this->view->form = $form;

        return;
    }

    private function reanderBudgetAuthorizationHistory($supportComplaintId)
    {
        $budgetAuthorizationRows = $this->getComplaintBudgetAuthorizationBySupportComplaintId($supportComplaintId);
        $budgetAuthorizationHistoryHtmlStr = '';
        $budgetAuthorizationHistoryHtmlStr .= '<lu>';
        foreach ($budgetAuthorizationRows as $budgetAuthorizationRow) {
            $budgetAuthorizationHistoryHtmlStr .= '<li>';
            $budgetAuthorizationHistoryHtmlStr .= sprintf(
                'Op: %s heeft: %s dit: %s',
                $budgetAuthorizationRow['created_at'],
                $budgetAuthorizationRow['user_rendered_name'],
                $budgetAuthorizationRow['is_autorisation']? 'Goedgekeurd' : 'Afgekeurd'

            );

            $budgetAuthorizationHistoryHtmlStr .= '<hr/>';
            $budgetAuthorizationHistoryHtmlStr .= '</li>';
        }
        $budgetAuthorizationHistoryHtmlStr .= '</lu>';
        return $budgetAuthorizationHistoryHtmlStr;
    }


    private function isThisAuthorizationRulesToDoByCurrentUserAndSupportComplaintId($supportComplaintId)
    {
        $supportComplaintRow = $this->getSupportComplaintById($supportComplaintId);
        if (!$supportComplaintRow) {
            return false;
        }

        $supportComplaintsCategoryId = $supportComplaintRow['category'];

        $nextInLineBudgetAuthorizationRuleRow = $this->getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId);

        if (!$this->isThisAuthorizationRulesToDoByCurrentUser($nextInLineBudgetAuthorizationRuleRow)) {
            return false;
        }

        return true;
    }

    private function saveComplaintBudgetAuthorization($supportComplaintId, $isAutorisation, $description = '')
    {
        $supportComplaintRow = $this->getSupportComplaintById($supportComplaintId);
        if (!$supportComplaintRow) {
            return false;
        }

        $supportComplaintsCategoryId = $supportComplaintRow['category'];

        $nextInLineBudgetAuthorizationRuleRow = $this->getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId);

        if (!$this->isThisAuthorizationRulesToDoByCurrentUser($nextInLineBudgetAuthorizationRuleRow)) {
            return false;
        }
        
        $currentUserId = loginManager::data()->id;

        $complaintBudgetAuthorizationModel = new ComplaintBudgetAuthorization();

        $complaintBudgetAuthorizationRow = $complaintBudgetAuthorizationModel->createRow();

        $complaintBudgetAuthorizationRow->support_complaints_id = $supportComplaintId;
        $complaintBudgetAuthorizationRow->support_complaints_categories_budget_authorization_rule_id = $nextInLineBudgetAuthorizationRuleRow['id'];
        $complaintBudgetAuthorizationRow->user_id = $currentUserId;
        $complaintBudgetAuthorizationRow->is_autorisation = $isAutorisation;
        $complaintBudgetAuthorizationRow->description = $description;

        $complaintBudgetAuthorizationRowId = $complaintBudgetAuthorizationRow->save();


        $nextInLineBudgetAuthorizationRuleRow = $this->getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId);

        if (true === $isAutorisation && $nextInLineBudgetAuthorizationRuleRow) {
            if ($supportComplaintRow['budget'] < $nextInLineBudgetAuthorizationRuleRow['from_budget'] ){
                $this->updateSupportComplaintsStatusType($supportComplaintId, 'open');
            }
        }

        if (true === $isAutorisation && !$nextInLineBudgetAuthorizationRuleRow) {
            $this->updateSupportComplaintsStatusType($supportComplaintId, 'open');
        }

        if (false === $isAutorisation) {

            if($complaintBudgetAuthorizationRowId) {
                $supportNotesModel = new SupportNotes();
                $supportNotesRow = $supportNotesModel->createRow();

                $supportNotesRow->type = 'complaints_budget_authorization';
                $supportNotesRow->map_to = $complaintBudgetAuthorizationRowId;
                $supportNotesRow->user = $currentUserId;
                $supportNotesRow->title = 'Complaint budget authorization declined';
                $supportNotesRow->message = $description;

                $supportNotesRow->save();
            }

            $this->updateSupportComplaintsStatusType($supportComplaintId, 'declined');
        }
    }


    private function updateSupportComplaintsStatusType($supportComplaintId, $SupportStatusTypesKey)
    {
        $supportComplaintsModel = new SupportComplaints();
        $supportComplaintsRow = $supportComplaintsModel->getById($supportComplaintId);
        $appStatusTypeArray = SupportStatusTypes::getByKey($SupportStatusTypesKey);

        if (!$appStatusTypeArray) {
            return;
        }
        
        $supportComplaintsRow->last_status = $appStatusTypeArray['id'];

        $supportComplaintsRow->save();
    }


    private function getSupportComplaintsNeedsAutorisation()
    {
        $select = db()->select()
            ->from(['sc' => 'support_complaints'], [
                'id',
                'title',
                'category',
                'date',
                'budget',
                'support_complaint_project_id',
                'complaint_type',
            ])
            ->joinLeft(['scp' => 'support_complaints_projects'],
                '
                sc.support_complaint_project_id = scp.id
                ',
                [
                    'support_complaints_projects_title' => 'title',
                    'support_complaints_projects_budget' => 'budget'
                ]
            )
            ->join(['sst' => 'support_status_types'],
                'sc.last_status = sst.id '
               .'AND sst.key = "needs_autorisation"'
                ,
                [])
            ->order('date DESC');

        $supportComplaintsRows = db()->fetchAll($select);

        return $supportComplaintsRows;
    }




    private function getSupportComplaintById($complaintId)
    {
        $select = db()->select()
            ->from(['sc' => 'support_complaints'], [
                'id',
                'title',
                'category',
                'date',
                'budget',
            ])
            ->where('sc.id = ?', $complaintId)
        ;

        $supportComplaintsRows = db()->fetchRow($select);

        return $supportComplaintsRows;
    }

    private function getNextInLineBudgetAuthorizationRule($supportComplaintId, $supportComplaintsCategoryId)
    {
        $maxApplyBudgetAuthorizationRow = $this->getMaxApplyBudgetAuthorization($supportComplaintId);

        if (!$maxApplyBudgetAuthorizationRow) {
            $maxApplyBudgetAuthorizationfromBudget = -1;
        } else {
            $maxApplyBudgetAuthorizationfromBudget =  $maxApplyBudgetAuthorizationRow['from_budget'];
        }


        $select = db()->select()
            ->from(['sccba' => 'support_complaints_categories_budget_authorization_rules'])
            ->joinLeft(['dp' => 'departments'],
                'sccba.department_id = dp.id',
                ['department_name' => 'name']
            )
            ->joinLeft(['ar' => 'acl_roles'],
                'sccba.acl_role_id = ar.id',
                ['acl_role_name' => 'name']
            )
            ->where('support_complaints_category_id = ?' , $supportComplaintsCategoryId)
            ->where('from_budget > ?' , $maxApplyBudgetAuthorizationfromBudget)
            ->order('sccba.from_budget')
            ->limit(1)
        ;

        $budgetAuthorizationListRulesRows = db()->fetchRow($select);
        return $budgetAuthorizationListRulesRows;
    }

    private function getComplaintBudgetAuthorizationBySupportComplaintId($supportComplaintId)
    {
        $select = db()->select()
            ->from(['scba' => 'support_complaints_budget_authorization'])
            ->join(['u' => 'users'],
                'scba.user_id = u.id',
                ['user_rendered_name' => 'rendered_name']
            )
            ->join(['sccba' => 'support_complaints_categories_budget_authorization_rules'],
                'sccba.id = scba.support_complaints_categories_budget_authorization_rule_id',
                []
            )
            ->join(['dp' => 'departments'],
                'sccba.department_id = dp.id',
                ['department_name' => 'name']
            )
            ->join(['ar' => 'acl_roles'],
                'sccba.acl_role_id = ar.id',
                ['acl_role_name' => 'name']
            )
            ->where('support_complaints_id = ?' , $supportComplaintId)
            ->order('scba.created_at')
        ;

        $budgetAuthorizationRows = db()->fetchAll($select);
        return $budgetAuthorizationRows;
    }

    private function getBudgetAuthorizationRules($supportComplaintsCategoryId)
    {
        $select = db()->select()
            ->from(['sccba' => 'support_complaints_categories_budget_authorization_rules'])
            ->joinLeft(['dp' => 'departments'],
                'sccba.department_id = dp.id',
                ['department_name' => 'name']
            )
            ->joinLeft(['ar' => 'acl_roles'],
                'sccba.acl_role_id = ar.id',
                ['acl_role_name' => 'name']
            )

            ->where('support_complaints_category_id = ?' , $supportComplaintsCategoryId)
            ->order('sccba.from_budget')
            ->order('sccba.department_id')
            ->order('sccba.acl_role_id')
        ;

        $budgetAuthorizationListRulesRows = db()->fetchAssoc($select);
        return $budgetAuthorizationListRulesRows;
    }

    private function getUserAclRolesByRef($currentUserAclRoleRef)
    {
        $select = db()->select()
            ->from(['ar' => 'acl_roles']
            )
            ->where('role = ?', $currentUserAclRoleRef)
        ;
        $aclRoleRow =  db()->fetchRow($select);
        return $aclRoleRow;
    }


    private function getBudgetAuthorizationRulesByUser($supportComplaintsCategoryId)
    {
        $currentUserDepartmentId = loginManager::data()->info['department'];
        $currentUserAclRoleRef = loginManager::data()->info['type'];

        $aclRoleRow = $this->getUserAclRolesByRef($currentUserAclRoleRef);
        $currentUserAclRoleId = $aclRoleRow['id'];

        $select = db()->select()
            ->from(['sccba' => 'support_complaints_categories_budget_authorization_rules'])
            ->joinLeft(['dp' => 'departments'],
                'sccba.department_id = dp.id',
                ['department_name' => 'name']
            )
            ->joinLeft(['ar' => 'acl_roles'],
                'sccba.acl_role_id = ar.id',
                ['acl_role_name' => 'name']
            )

            ->where('sccba.support_complaints_category_id = ?' , $supportComplaintsCategoryId)
            ->where('sccba.department_id = ?' , $currentUserDepartmentId)
            ->where('sccba.acl_role_id = ?' , $currentUserAclRoleId)
            ->order('sccba.from_budget')
            ->order('sccba.department_id')
            ->order('sccba.acl_role_id')
        ;

        $budgetAuthorizationListRulesRows = db()->fetchAssoc($select);
        return $budgetAuthorizationListRulesRows;
    }

    private function isThisAuthorizationRulesToDoByCurrentUser($budgetAuthorizationRuleRow)
    {
        $currentUserDepartmentId = loginManager::data()->info['department'];
        $currentUserAclRoleRef = loginManager::data()->info['type'];

        $aclRoleRow = $this->getUserAclRolesByRef($currentUserAclRoleRef);
        $currentUserAclRoleId = $aclRoleRow['id'];

        if (
            $currentUserDepartmentId == $budgetAuthorizationRuleRow['department_id']
            && $currentUserAclRoleId == $budgetAuthorizationRuleRow['acl_role_id']
        ) {
            return true;
        }
        return false;

    }




    private function isUnAutorisation($supportComplaintsId)
    {
        $select = db()->select()
            ->from(['scba' => 'support_complaints_budget_authorization'])
            ->where('support_complaints_id = ?' , $supportComplaintsId)
            ->where('is_autorisation = ?' , 0)
        ;

        $budgetAuthorizationRow = db()->fetchRow($select);
        if (!$budgetAuthorizationRow) {
            return false;
        }

        return true;
    }

    private function isAutorisation($supportComplaintsId)
    {
        $select = db()->select()
            ->from(['scba' => 'support_complaints_budget_authorization'])
            ->where('support_complaints_id = ?' , $supportComplaintsId)
            ->where('is_autorisation = ?' , 1)
        ;

        $budgetAuthorizationRow = db()->fetchRow($select);
        if (!$budgetAuthorizationRow) {
            return false;
        }

        return true;
    }

    private function getBudgetAuthorization($supportComplaintsId)
    {

        $select = db()->select()
            ->from(['scba' => 'support_complaints_budget_authorization'])
            ->where('support_complaints_id = ?' , $supportComplaintsId)
        ;

        $budgetAuthorizationRows = db()->fetchAll($select);
        return $budgetAuthorizationRows;
    }

    private function getMaxApplyBudgetAuthorization($supportComplaintsId)
    {

        $select = db()->select()
            ->from(['scba' => 'support_complaints_budget_authorization'])
            ->join(['sccba' => 'support_complaints_categories_budget_authorization_rules'],
                'sccba.id = scba.support_complaints_categories_budget_authorization_rule_id',
                ['from_budget']
            )
            ->where('support_complaints_id = ?' , $supportComplaintsId)
            ->order(['scba.created_at DESC'])
            ->order('sccba.from_budget')
            ->limit(1)
        ;


        $budgetAuthorizationRows = db()->fetchRow($select);
        return $budgetAuthorizationRows;
    }
}
