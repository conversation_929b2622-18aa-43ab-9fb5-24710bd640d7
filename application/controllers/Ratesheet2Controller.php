<?

	class Ratesheet2Controller extends GlobalController {

		/**
		 *
		 */
		public function categoryListAction(){
			$this->disableView();

			$rc = new Ratesheet2_Category();
			$categories = $rc->getList($this->_getParam('ratesheet'));

			echo json_encode($categories);

		}

		public function listAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Tariefbladen');

			$this->view->types = array(
				'private' => array('singular' => 'particulier', 'adjective' => 'particuliere'),
				'commercial' => array('singular' => 'commercieel', 'adjective' => 'commerciële')
			);

			$this->render('ratesheet/list', false, true);
		}

		public function listviewAction(){
			RatesheetController::listviewAction();
		}

		public function checkFixAction(){
			$this->disableView();

			$c_model = new Components();

			$select = db()->select()
				->from(array('og' => 'objectgroup'), array('rate2', 'objectgroup' => 'id'))
				->joinLeft(array('rc' => 'ratesheets2_categories'), 'rc.ratesheet = og.rate2 AND rc.component = "0"', false)
				->where('rc.id IS NULL')
				->where('og.rate2 > 0');

			foreach(db()->fetchAll($select) as $ratesheet)

				foreach(array('DESC', 'ASC') as $mysql_order_type){

					$select = db()->select()
						->from(array('o' => 'objects'), array('id'))
						->where('o.objectgroup = ?', $ratesheet['objectgroup'])
						->order('o.m2 ' . $mysql_order_type);

					$object = db()->fetchOne($select);

					if(!$object) return;

					if(!isset($results[$ratesheet['rate2']][$object]))
						$results[$ratesheet['rate2']][$object] = $c_model->prepareUpdate($object, $ratesheet['objectgroup'], $ratesheet['rate2'], 2, date('d-m-Y'), false);
				}

			echo json_encode($results);
		}

		public function fixAction(){
			$this->disableView();

			$rgi_model = new Ratesheets2GroupsItemsTable();
			$rg_model = new Ratesheets2GroupsTable();

			$select = db()->select()
				->from(array('rgi' => 'ratesheets2_groups_items'), array('id'))
				->joinLeft(array('rg' => 'ratesheets2_groups'), 'rg.id = rgi.group', false)
				->where('rg.id IS NULL');

			foreach(db()->fetchAll($select) as $remove_group_item)
				$rgi_model->delete('id = ' . $remove_group_item['id']);

			$select = db()->select()
				->from(array('r2' => 'ratesheets2'), array('id'));

			foreach(db()->fetchAll($select) as $ratesheet){

				$select = db()->select()
					->from(array('ratesheets2_periods'), array('id'))
					->where('ratesheet = ?', $ratesheet['id']);

				foreach(db()->fetchAll($select) as $ratesheet_period){
					$select = db()->select()
						->from(array('rg' => 'ratesheets2_groups'), false)
						->joinLeft(array('rgi' => 'ratesheets2_groups_items'), 'rgi.group = rg.id', array('id'))
						->joinLeft(array('rv' => 'ratesheets2_values'), 'rv.id = rgi.value', false)
						->joinLeft(array('rc' => 'ratesheets2_categories'), 'rc.id = rv.category', false)
						->joinLeft(array('c' => 'components'), 'c.id = rc.component', false)
						->joinLeft(array('p' => 'products'), 'p.id = c.product', array('type', 'group_type' => 'name'))
						->where('rc.ratesheet = ?', $ratesheet['id'])
						->where('rg.period = ?', $ratesheet_period['id'])
					;

					$ratesheet_group_items = db()->fetchAll($select);

					$group_types = array();

					foreach(array('fixed', 'variable') as $type){

						if(!isset($group_types['type']))
							$group_types[$type] = array(
								'1' => false,
								'2' => false
							);

						$invalid_groups = array();

						$select = db()->select()
							->from(array('ratesheets2_groups'), array('id', 'group_type'))
							->where('period = ?', $ratesheet_period['id'])
							->where('type = ?', $type);


						foreach(db()->fetchAll($select) as $group){

							if($group['group_type'] > 0)
								if($group_types[$type][$group['group_type']] === false){
									$group_types[$type][$group['group_type']] = $group['id'];
									continue;
								}

							$invalid_groups[] = $group;
						}

						foreach($invalid_groups as $invalid_group)
							$rg_model->delete('id = ' . $invalid_group['id']);
					}

					foreach($group_types as $type => $gtypes)
						foreach($gtypes as $gtype => $group){
							if($group !== false) continue;

							$group_params = array(
								'period' => $ratesheet_period['id'],
								'group_type' => $gtype,
								'type' => $type
							);

							$rg_row = $rg_model->matchRow($group_params);

							if(!$rg_row){
								$rg_row = $rg_model->createRow($group_params);
								$rg_row->save();
							}

							$group_types[$type][$gtype] = $rg_row->id;
						}

					foreach($ratesheet_group_items as $ratesheet_group_item){
						$ratesheet_group_item['group_type'] = $ratesheet_group_item['group_type'] == 'cold' ? 2 : 1;

						$rgi_row = $rgi_model->getById($ratesheet_group_item['id']);

						$rgi_row->group = $group_types[$ratesheet_group_item['type']][$ratesheet_group_item['group_type']];

						$rgi_row->save();
					}
				}
			}

		}

		/**
		 *
		 */
		public function categoryAction(){

			$r = new Ratesheet2();
			$rc = new Ratesheet2_Category();
			$if = new Indexes_Formula();
			$it = new Indexes_Type();
			$ifp = new Indexes_Formula_Part();
			$rv = new Ratesheet2_Value();

			$ratesheet_id = $this->getParam('ratesheet');
			$ratesheet = $r->get($ratesheet_id);

			$params = [
				'type' => $ratesheet['general']['type'] == 'commercial' ? 'commercieel' : 'particulier'
			];


			// If editing an existing category than shorten the components list used in the view
			$type = $this->getParam('type');
			$product = $this->getParam('product');

			if ($type && $product) {
				$where = [
					'type = ?' => $type,
					'name = ?' => $product
				];

				$products = new Products();
				$product = $products->fetchRow($where);

				$params['category'] = $type;
				$params['product'] = $product['id'];
			}

			$categoryForm = new Form($this, 'ratesheets2/category');
			$categoryForm->general->component->setMultiOptions([false => 'Selecteer een component'] + Components::getFormSelectList($params));

			$id = $this->_getParam('id');

			if($this->getRequest()->isPost() && $categoryForm->isValid($this->getRequest()->getPost())){

				$post = $this->getRequest()->getPost();

				$componentId = $post['general']['component'];

				$select = db()->select()
					->from(['c' => 'components'], ['type' => 'category'])
					->joinLeft(['p' => 'products'], 'p.id = c.product', ['product' => 'name'])
					->where('c.id = ?', $componentId);

				$component_info = db()->fetchRow($select);


				# Formula
				$formula = $if->edit($post['formula'], $post['general']['formula']);
				$post['general']['formula'] = $formula;

				$parts = $post['parts'];
				foreach($parts['id'] as $k => $partId){
					$formulaData = [
						'id' => $partId ? $partId : false,
						'formula' => $formula,
						'percentage' => $parts['percentage'][$k],
						'indexes_type' => $parts['type'][$k]
					];

					if($formulaData['percentage']){
						$ifp->save($formulaData, $formulaData['id']);
					}
					elseif($formulaData['id']) {
						$ifp->delete($formulaData['id']);
					}
				}
				# Category
				$id = $rc->edit(array_merge($post['general'], ['ratesheet' => $this->_getParam('ratesheet')]), $id);

				# Values
				$values = $post['values'];

				$saved_values = [];
				foreach($values['id'] as $k => $valueId){
					$data = [
						'id' => $valueId ? $valueId : false,
						'category' => $id,
						'formula' => $formula,
						'year' => $values['year'][$k],
						'quarter' => $values['quarter'][$k],
						'value' => $values['value'][$k],
						'from' => $values['from'][$k],
						'till' => $values['till'][$k]
					];

					if($data['year'] && $data['value']){
						$saved_values[] = $rv->save($data, $data['id']);
					}
					elseif($data['id']) {
						$rv->delete($data['id']);
					}
				}

				$period_id = db()->fetchOne(db()->select()->from('ratesheets2_periods', ['id'])->where('ratesheet = ?', $ratesheet_id));

				$rg_model = new Ratesheets2GroupsTable();
				$rg = new Ratesheet2_Group();

				$group_values = [
					'type' => $component_info['type'],
					'group_type' => $rg->getType($component_info['product']),
					'period' => $period_id,
				];

				$group_row = $rg_model->findRowWithHighestIdOrCreate($group_values);
				$group_row->save();

				$ri = new Ratesheets2GroupsItemsTable();

				foreach($saved_values as $saved_value_id) {
					$ri_row = $ri->findRowWithHighestIdOrCreate( [
						'group' => $group_row->id,
						'value' => $saved_value_id
					] );
					$ri_row->save();
				}

				if($this->isAjaxRequest === true){
					echo json_encode($id);
					die();
				} else {
					$this->_redirect('edit', ['id' => $ratesheet_id]);
				}
			}
			elseif($id){
				$data = $rc->get($id);
				$categoryForm->populate($data);
			}

			$this->view->form = $categoryForm;
			$this->view->category = $data;
			$this->view->formula = $if->get($data['general']['formula']);

			$this->view->indexTypes = $it->getList()->toArray();
		}

		/**
		 * getRates
		 */
		public function getAction() {
			$ratesheets = new Ratesheet();
			$type = ($_POST['Type']) ? $_POST['Type'] : false;

			$this->view->ratesheets = $ratesheets->getList($this->_getParam('project'), $type);
			$this->view->type = $type;

			$this->_helper->layout->disableLayout();
		}

		public function saveGeneralDataItemAction(){
			$this->disableView();

			$ratesheet_id = $this->getParam('id');
			$data = $this->getRequest()->getPost();

			$r = new Ratesheets2Table();

			$r
				->getById($ratesheet_id)
				->setFromArray($data)
				->save();
		}

		public function deleteInvoiceComponentAction()
		{
			$this->disableView();

			$invoiceComponentId = $this->getParam('invoice_component_id');

			$ratesheet2Category = new Ratesheet2_Category();
			$data = $ratesheet2Category->get($invoiceComponentId);

			Journal::getForLinks([
				['type' => 'ratesheets2_categories', 'map_to' => $invoiceComponentId, 'role' => 'removed_item']
			])->record('Deze tariefblad categorie is verwijderd.', $data);

			$service = new \application\models\Ratesheet2\Application\Services\DeleteInvoiceComponentService();
			$service->execute(new \application\models\Ratesheet2\Application\Services\DeleteInvoiceComponentRequest(
				$invoiceComponentId
			));
		}

		public function editAction()
		{
			$this->view->extraWidth = true;
			$this->view->headScriptHashed()->appendFile('media/javascript/datalist.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/ratesheet2category.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/indexesformula.js');
			$this->view->headLink()->appendStylesheet('media/style/ratesheet2.css');

			$rateSheet2Id = $this->_getParam('id');
			$year = $this->getParam('year') ?: date('Y');

			$r = new Ratesheet2();
			$rateSheet = $r->get($rateSheet2Id);

			// Create a new RateSheet
			if (!is_numeric($rateSheet2Id)) {
				$service = new \application\models\Ratesheet2\Application\Services\CreateNewRateSheet2Service();
				$rateSheet2Id = $service->execute(new \application\models\Ratesheet2\Application\Services\CreateNewRateSheet2Request(
					$this->getParam('project'),
					$this->getParam('type')
				));

				$this->_redirect('edit', ['id' => $rateSheet2Id]);
			}

			// General rateSheet form
			$rateForm = new Form($this, 'ratesheets2');
			$rateForm->populate($rateSheet);


			// Retrieve data for the view
			$service = new \application\models\Ratesheet2\Application\Services\ViewRateSheet2Service();
			$data = $service->execute(new \application\models\Ratesheet2\Application\Services\ViewRateSheet2Request(
				$this->getParam('id'),
				$year
			));

			// set view params
			$this->view->year = $year;
			$this->view->rateform = $rateForm;
			$this->view->categories = $data;
			$this->view->rateSheetId = $this->getParam('id');


			// Set breadCrumb
			$name = $rateSheet['general']['name'];
			$project = $rateSheet['general']['project'];

			$projectClass = new Project();
			$project = is_numeric($this->_getParam('project')) ? $this->_getParam('project') : $project;
			$project = is_numeric($project) ? $projectClass->getDetails($project) : false;

			if($project['general']['id']) {
				$this->view->Breadcrumbs()
					->addCrumb('Projecten', 'project/')
					->addCrumb($project['general']['name'], 'project/details/id/' . $project['general']['id'] . '/');
			} else {
				$this->view->Breadcrumbs()
					->addCrumb('Tariefbladen', 'ratesheet2/list');
			}

			$this->view->Breadcrumbs()
				->addCrumb(is_numeric($this->_getParam('id')) ? "Tariefblad $name bewerken" : "tariefblad toevoegen", 'project/');
		}
		
		public function forUserAction(){
			$r2 = new Ratesheet2();
			
			if(!$this->_getParam('user'))
				return;
			
			$this->view->rates = $r2->forUser($this->_getParam('user'));
		}

		public function printAction() {
			if (!$this->_getParam('id'))
				die();

			$r = new Ratesheet();
			$this->view->ratesheet = $r->getDetails($this->_getParam('id'));

		}

		/**
		 * Delete ratesheet
		 */
		public function deleteAction() {
			$this->disableView();

			if (is_numeric($this->_getParam('id'))) {
				$ratesheet = new Ratesheet();
				$ratesheet->delete($this->_getParam('id'));
			}

			$this->_redirect('project/details', array('id' => $this->_getParam('project')));
		}

	}
