<?
	class CompanyTypeController extends GlobalController {


		/**
		 * Redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function indexAction() {
			$this->_helper->redirector('list', 'companytype');
		}

		/**
		 * Lists all Companies using a Listview
		 * 
		 * @return null
		 */
		public function listAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Bedrijfstypes overzicht', '');

			$this->view->modelListView('CompanyType')

				->setTypes(array('name', 'commercial_user', 'technical_contact', 'description', ))

				->addFormat('commercial_user', function($value){ return ($value == 1)? 'Ja': 'Nee'; })

				->addFormat('technical_contact', function($value){ return ($value == 1)? 'Ja': 'Nee'; })

				->setOptions(array(
					'item_title' => 'Bedrijfs type',
				))

				->addButtons(array(
					'add' => 'Toevoegen',
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen'
				),
				array(
					'id' => 'id',
					'redirect' => 'true'
				))

				->render($this);
		}

		/**
		 * Edit the CompanyType indicated by the id parameter
		 * using an EditView
		 * 
		 * @return null
		 */
		public function editAction() {
			$this->view->Breadcrumbs()
				->addCrumb('Bedrijfstypes overzicht', 'company-type/list/')
				->addCrumb('Bedrijfs type bewerken', '');

			// this is to have the userId available in the form
			// through $this->getOwner()->id
			$this->companytypeid = $this->_getParam('id');

			$this->view->form = $this->view->EditView($this, 'CompanyType',
				array('id' => $this->_getParam('id')))
				->render($this);
		}

		/**
		 * Deletes the CompanyType indicated by the id parameter
		 * and redirects to the 'list' action
		 * 
		 * @return null
		 */
		public function deleteAction() {
			$this->disableView();

			$ct = new CompanyType();
			$ct->delete($this->_getParam('id'));

			$this->_helper->redirector('list', 'company');
		}
	}
?>