<?php

class Api_Upgrade_ExportController extends \GlobalController
{
    public function preDispatch()
    {
        $this->disableView();

        $basicAuthenticationLoginService = new \Api\BasicAuthenticationLoginService();

        try {
            $basicAuthenticationLoginService->execute(
                $_SERVER['PHP_AUTH_USER'],
                $_SERVER['PHP_AUTH_PW']
            );
        } catch (Exception $exception) {
            header('WWW-Authenticate: Basic realm="Omniboxx Upgrade"');
            header('HTTP/1.0 401 Unauthorized');
            echo $exception->getMessage();
            die();
        }
    }



    public function generateUpgradeRealEstateReportAction(){
        ini_set('memory_limit', '8000M');
        $report = new UpgradeReport();
        $o_model = new Objects();
        $todayDateString = date('Y-m-d');
        $select = db()->select()
            ->from(['o' => 'objects'], ['*', 'object_type' => 'type'])
            ->joinLeft(
                ['og' => 'objectgroup'],
                'og.id = o.objectgroup',
                ['objectgroup_label' => 'description', 'project']
            )
            ->joinLeft(
                ['i1' => 'investor'],
                'i1.id = og.investor',
                [
                    'og_investor' => 'user',
                    'og_investor_provision' => 'provision',
                    'og_investor_provision_amount' => 'provisionamount',
                    'og_name' => 'name',
                    'og_investor_manager' => 'investor_manager',
                    'og_investor_manager_account' => 'investor_manager_account',
                    'og_id' => 'id'
                ]
            )
            ->joinLeft(
                ['i2' => 'investor'],
                'i2.id = o.investoroverride',
                [
                    'o_investor' => 'user',
                    'o_investor_provision' => 'provision',
                    'o_investor_provision_amount' => 'provisionamount',
                    'o_name' => 'name',
                    'o_investor_manager' => 'investor_manager',
                    'o_investor_manager_account' => 'investor_manager_account',
                    'o_id' => 'id'
                ]
            )
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project_label' => 'name', 'project_id' => 'id', 'project_cost_center' => 'cost_center'])
            ->joinLeft(['a' => 'address'],
                'a.type_id = o.id AND a.type ="object"',
                ['has_address' => 'id', 'address', 'zipcode', 'number', 'city']
            )
            ->joinLeft(['ot' => 'object_type'], 'o.type = ot.id ', ['object_type_name' => 'name'])
            ->joinLeft(['uo' => 'users_objects'],
                "uo.object = o.id AND (uo.`till` >= '{$todayDateString}' || uo.`till` IS NULL) AND uo.role = 'normal'",
                [
                    'uo_id' => 'id',
                    'from',
                    'till',
                    'is_current_period' => "IF(uo.from < '{$todayDateString}', 1, 0)",
                    'contract' => 'IF(uo.contract > 0, 1, 0)'
                ]
            )
            ->joinLeft(['u' => 'users'], 'u.id = uo.customer', [
                'user' => 'id',
                'debtorcode' => 'number',
                'user_name' => 'rendered_name',
                'language',
                'firstname',
                'middlename',
                'name',
                'type',
                'bdate',
                'olddebtorcode',
                'profile_completion',
                'client_id',
                'client_number',
                'school',
                'school_input',
                'study_direction',
                'nationality',
                'first',
                'created_on',
                'code',
            ])
            ->joinLeft(['ba' => 'bank_account'], 'ba.map_to = u.id AND ba.type = "user" ', ['iban'])
            ->joinLeft(['partner' => 'users'], 'partner.id = u.partner', [
                'partner' => 'id',
                'partner_name' => 'rendered_name',
                'partner_language' => 'language',
                'type' => 'type',
                'partner_bdate' => 'bdate',
                'profile_completion' => 'profile_completion',
            ])
            ->joinLeft(['partner2' => 'users'], 'partner2.id = u.partner_2', [
                'partner_2' => 'id',
                'partner_2_name' => 'rendered_name',
                'partner_2_language' => 'language',
                'partner_2_type' => 'type',
                'partner_2_bdate' => 'bdate',
                'partner_2_profile_completion' => 'profile_completion',
            ])
            ->joinLeft(['partner3' => 'users'], 'partner3.id = u.partner_3', [
                'partner_3' => 'id',
                'partner_3_name' => 'rendered_name',
                'partner_3_language' => 'language',
                'partner_3_type' => 'type',
                'partner_3_bdate' => 'bdate',
                'partner_3_profile_completion' => 'profile_completion',
            ])
            ->joinLeft(['c' => 'company'], 'c.dummy_user = u.id AND u.type = "company"', ['company_id' => 'id'])
            ->where('p.sales_status != ?', 'Exploitation_ended')
            ->where('og.description NOT LIKE ?', '%Gemene%')
            ->order('p.name')
            ->order('o.rendered_address')
            ->order('uo.from');
        
        $data = $o_model->getListAdvanced($select);
        $report->outputJSON($data);
    }


    public function generateUpgradeUnitReportAction()
    {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForUnit();
            $report->outputJSON($data);
        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForUnit) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generateUpgradeLeaseReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForLease();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLease) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generateUpgradeLeaseUnitReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForLeaseUnit();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLeaseUnit) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generateUpgradeRecurringBillingReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForRecurringBilling();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForRecurringBilling) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }
    public function generateUpgradeOptionReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForOption();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForOption) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generateUpgradeLegalEntityReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForLegalEntityTenant();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLegalEntityTenant) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generateUpgradeRentEscalationReportAction() {
        // die('disabled for now');

        try {
            $report = new UpgradeReport();
            $data = $report->gatherDataForRentEscalation();
            $report->outputJSON($data);
        } catch (Exception $e) {

            $clientEmailAddr = [
                'name' => 'Klantenservice ' . Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            ];

            $supportEmailAddr = [
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            ];

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForRentEscalation) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function apiCallExampleAction()
    {
        $domain = 'bluebird.omniboxx.nl';
        $requestString = json_encode($this->testData);

        $username = 'Upgrade_Report';
        $password = '>2dp9Ir5Lq9kEF6G';

        $curlResource = curl_init();

        /* for dev environment self-signed ssl certificate */
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYPEER, 0);

        /* set POST and response options */
        curl_setopt($curlResource, CURLOPT_RETURNTRANSFER, 1);

        /* setup basic authentication login  */
        curl_setopt($curlResource, CURLOPT_HEADER, 1);
        curl_setopt($curlResource, CURLOPT_USERPWD, $username . ":" . $password);

        /* make request to get JSON data  */
        curl_setopt(
            $curlResource,
            CURLOPT_URL,
            'https://' . $domain . '/api_pears-global_export/generate-pears-global-unit-report'
        );

        curl_setopt($curlResource, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curlResource, CURLOPT_POSTFIELDS, $requestString);

        $responseText = curl_exec($curlResource);
        $httpCode = curl_getinfo($curlResource, CURLINFO_HTTP_CODE);

        /* close the session */
        curl_close($curlResource);

        /* handle exceptions */
        if (in_array($httpCode, [401, 404])) {
            echo $responseText;
            die();
        }

        /* parse the response */
        $responseJson = json_decode($responseText, true);

        echo $responseText;


    }

}
