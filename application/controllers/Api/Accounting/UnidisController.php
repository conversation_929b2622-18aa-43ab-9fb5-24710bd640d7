<?php

use Accounting\Infrastructure\ExactOnline\Model\PurchaseEntryLine;
use Unidis\Application\ImportTransactionsService;
use Unidis\Application\SyncOutStandingItemsService;
use Accounting\Domain\Model\PurchaseInvoice\PurchaseInvoiceRepository;


class Api_Accounting_UnidisController extends GlobalController {
    public function preDispatch()
    {
        $this->disableView();

        $basicAuthenticationLoginService = new \Api\BasicAuthenticationLoginService();

        try {
            $basicAuthenticationLoginService->execute(
                $_SERVER['PHP_AUTH_USER'],
                $_SERVER['PHP_AUTH_PW']
            );
        } catch (Exception $exception) {
            header('WWW-Authenticate: Basic realm="Omniboxx Accounting Axians"');
            header('HTTP/1.0 401 Unauthorized');
            echo $exception->getMessage();
            die();
        }
    }

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_bc/get-all-new-invoices
     * @return void
     */
    public function getAllNewInvoicesAction()
    {
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $this->disableView();

        $divisioncode = $this->getParam('divisioncode');

        if (!is_numeric($divisioncode)){
            die('no valid divisioncode');
        }

        $invoicesSelect = db()->select()
            ->from(['i' => 'invoices'], ['id','customid'])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', ['runid'=> 'id'])
            ->joinLeft(['c' => 'corporations'], 'ir.corporation_id = c.id', false)
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', 'is_purchase')
            ->where('i.financial_invoice_id IS NULL')
            ->where('i.financial_no_sync = ?', false)
            ->where('ir.status = ?', 2)
            ->where('ic.id IS NULL OR ic.is_purchase = 0')
            ->where('c.administration = ?', $divisioncode)
            ->order('i.id DESC')
            ->limit(100)
        ;

        $invoices = db()->fetchAll($invoicesSelect);

        if (count($invoices) == 0)  {
            die('No invoices to exchange for division code' .$divisioncode);
        }

        (new Unidis())->generateXml($invoices, true);
    }

    /**
     * @Inject Controllers\Api\Accounting\ReadInvoicesIdService.php\ReadInvoicesIdService
     * @var \Controllers\Api\Accounting\Unidis\ReadInvoicesIdService
     */
    private $readInvoicesIdService;
    /**
     * @Inject Unidis\Application\SaveInvoiceIdMappingDataService
     * @var \Unidis\Application\SaveInvoiceIdMappingDataService
     */
    private $saveInvoiceIdMappingDataService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_bc/read-invoices-id
     * @return void
     */
    public function readInvoicesIdAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $this->saveInvoiceIdMappingDataService->execute($inputData);

        $this->readInvoicesIdService->execute();
    }

    public function pushPurchaseInvoiceActionAction()
    {

        $inputData = file_get_contents('php://input');

        $inputData = json_decode($inputData, true);

        $purchaseInvoice = $this->build($inputData);
        $this->purchaseInvoiceRepository->save($purchaseInvoice);


        //status codes retour
    }

    /**
     * @Inject ExactGlobeAxians\Application\SaveInvoiceIdMappingDataService
     * @var \Unidis\Application\SaveTransactionsPaymentsDataService
     */
    private $saveTransactionsPaymentsDataService;

    /**
     * @Inject ExactGlobeAxians\Application\ImportTransactionsService
     * @var ImportTransactionsService
     */
    private $importTransactionsService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/read-transactions-payments
     * @return
     */
    public function readTransactionsPaymentsAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $transactionJsonFile = $this->saveTransactionsPaymentsDataService->execute($inputData);

        try {
            $this->importTransactionsService->execute($transactionJsonFile);
            echo('call succesful');

        } catch (\Exception $exception) {
            error_log($exception->getMessage());
        }
    }


    /**
     * @Inject Unidis\Infrastructure\PurchaseInvoiceRepository
     * @var \Unidis\Infrastructure\PurchaseInvoiceRepository
     */
    private $purchaseInvoiceRepository;


    /**
     * @Inject Unidis\Application\SaveInvoiceIdMappingDataService
     * @var \Unidis\Application\SaveOutstandingAmountsDataService
     */
    private $saveOutstandingAmountsDataService;

    /**
     * @Inject Unidis\Application\SyncOutStandingItemsService
     * @var SyncOutStandingItemsService
     */
    private $syncOutStandingItemsService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/read-outstanding-amounts
     *
     * $transactionJsonFile = '_data/exact_globe_axians/read-outstanding-amounts/1666753499_6358a3dba8dec.json';
     * @return
     */
    public function readOutstandingAmountsAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $transactionJsonFile = $this->saveOutstandingAmountsDataService->execute($inputData);

        try {
            $this->syncOutStandingItemsService->execute($transactionJsonFile);
            echo('call succesful');
        } catch (\Exception $exception) {
            error_log($exception->getMessage());
        }
    }


    public function build($data)
    {



        $purchaseInvoice = [
            'financial_invoice_id' => $data['financial_invoice_id'],
            'type' => 'purchase',
            'select' => [
                'type' => 'contact',
                'attach' => 'contact_' . $this->findCompanyOrNull($data['select']['attach']),
                'project' => $this->findProjectIdByShortname($data['select']['project']),
            ],
            'total' => [
                'collection' => false,
                'substract' => []
            ],
            'general' => [
                'title' => $data['general']['title'],
                'description' => $data['general']['description'],
                'invoicedate' => $data['general']['invoicedate'],
                'expiredate' => $data['general']['expiredate'],
                'investor_provision_deduction' => (bool) \Settings::get('import_third_party_purchase_invoices_persist_send_attachment')
            ],
            'corporation' => [
                'corporation' => $this->findCorporation($data['corporation']['administration']),
                'administration' => $data['corporation']['administration']
            ],
            'purchase_status' => [
                'validation_user' => null,
                'validating' => false
            ],
            'rows' => $this->buildAll($data['rows']),
            'fiscal' => [
                'period' => 'm_' .  $data['fiscal']['period'],
                'year' =>   $data['fiscal']['year']
            ]
        ];

        if ($data['document']['filename'] != '') {
            $purchaseInvoice['document'] = [
                'filename' => $data['document']['filename'],
                'file' => $data['document']['base64']
            ];
        }



        return $purchaseInvoice;
    }

    public function buildAll($purchaseInvoiceLines)
    {
        $lines = [];

        foreach ($purchaseInvoiceLines as $purchaseInvoiceLine) {
            $lines[] = $this->buildPurchaseInvoiceLine($purchaseInvoiceLine);
        }

        //p(json_encode($lines),'die');
        return json_encode($lines);
    }

    public function buildPurchaseInvoiceLine($purchaseInvoiceLine)
    {


        $componentId = $this->tryFindComponentOrNull($purchaseInvoiceLine['ledger'], $purchaseInvoiceLine['cost_center'], $purchaseInvoiceLine['taxrate']);

        return [
            'description' => $purchaseInvoiceLine['description'],
            'price' => $purchaseInvoiceLine['price'],
            'taxprice' => $purchaseInvoiceLine['price'] + $purchaseInvoiceLine['taxprice'],
            'taxrate' => $purchaseInvoiceLine['taxrate'],
            'component' => $componentId,
            'ledger' => $purchaseInvoiceLine['ledger'],
            'cost_objects' => '',
            'cost_center' => $purchaseInvoiceLine['cost_center']
        ];
    }


    private function tryFindComponentOrNull($ledger = null , $cost_center = null , $percentage = null)
    {
        if (null === $ledger || null === $percentage) {
            return null;
        }

        $taxRate = $this->convertTaxPercentageToTaxRate(strval($percentage));

        if (null === $taxRate) {
            return null;
        }

        $components = db()->select()
            ->from('components', 'id')
            ->where('ledger = ?', $ledger)
            ->where('tax_rate = ?', $taxRate)
            ->where('code = ?', $cost_center)
            ->where('deleted = ?', 0)
            ->query()
            ->fetchAll();



        return count($components) > 1 ? null : $components[0]['id'];
    }


    private function convertTaxPercentageToTaxRate($percentage)
    {
        $taxRates = [
            0 => 0,
            6 => 1,
            21 => 2,
            9 => 3
        ];

        return array_key_exists($percentage, $taxRates) ? $taxRates[$percentage] : null;
    }

    private function findCorporation($administration)
    {
        $result = db()->select()
            ->from('corporations', 'id')
            ->where('administration = ?', $administration)
            ->query()
            ->fetch();

        return $result['id'];
    }


    private function findCompanyOrNull($financialRelationCode)
    {
        if (!$financialRelationCode) {
            return null;
        }

        $usersRow = (new \Users())->fetchRow(['olddebtorcode = ?' => $financialRelationCode]);
        if (null === $usersRow) {
            return null;
        }
        $companyRow = (new \Company())->fetchRow(['dummy_user = ?' => $usersRow->id]);

        if (null === $companyRow) {
            return null;
        }

        return $companyRow->id;
    }

    private function findProjectIdByShortname($shortname)
    {

        // search by project number
        $projectRow = db()->select()
            ->from(['p' => 'projects'], 'id')
            ->where('p.shortname = ?', $shortname)
            ->query()
            ->fetch();

        if ($projectRow) {
            return $projectRow['id'];
        }

        return null;
    }






}
