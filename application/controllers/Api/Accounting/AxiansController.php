<?php

use ExactGlobeAxians\Application\ImportTransactionsService;
use ExactGlobeAxians\Application\SyncOutStandingItemsService;

class Api_Accounting_AxiansController extends GlobalController {
    public function preDispatch()
    {
        $this->disableView();

        $basicAuthenticationLoginService = new \Api\BasicAuthenticationLoginService();

        try {
            $basicAuthenticationLoginService->execute(
                $_SERVER['PHP_AUTH_USER'],
                $_SERVER['PHP_AUTH_PW']
            );
        } catch (Exception $exception) {
            header('WWW-Authenticate: Basic realm="Omniboxx Accounting Axians"');
            header('HTTP/1.0 401 Unauthorized');
            echo $exception->getMessage();
            die();
        }
    }

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/get-all-new-invoices
     * @return void
     */
    public function getAllNewInvoicesAction()
    {
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 0);

        $this->disableView();

        $invoicesSelect = db()->select()
            ->from(['i' => 'invoices'], ['id'])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', false)
            ->where('i.financial_invoice_id IS NULL')
            ->where('i.financial_no_sync = ?', false)
            ->where('ir.status = ?', 2)
            ->limit(100)
        ;

        $invoices = db()->fetchAll($invoicesSelect);

        (new ExactGlobeAxians())->generateXml($invoices, true);
    }

    /**
     * @Inject Controllers\Api\Accounting\Axians\ReadInvoicesIdService
     * @var \Controllers\Api\Accounting\Axians\ReadInvoicesIdService
     */
    private $readInvoicesIdService;
    /**
     * @Inject ExactGlobeAxians\Application\SaveInvoiceIdMappingDataService
     * @var \ExactGlobeAxians\Application\SaveInvoiceIdMappingDataService
     */
    private $saveInvoiceIdMappingDataService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/read-invoices-id
     * @return void
     */
    public function readInvoicesIdAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $this->saveInvoiceIdMappingDataService->execute($inputData);

        $this->readInvoicesIdService->execute();
    }

    /**
     * @Inject ExactGlobeAxians\Application\SaveInvoiceIdMappingDataService
     * @var \ExactGlobeAxians\Application\SaveTransactionsPaymentsDataService
     */
    private $saveTransactionsPaymentsDataService;

    /**
     * @Inject ExactGlobeAxians\Application\ImportTransactionsService
     * @var ImportTransactionsService
     */
    private $importTransactionsService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/read-transactions-payments
     * @return
     */
    public function readTransactionsPaymentsAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $transactionJsonFile = $this->saveTransactionsPaymentsDataService->execute($inputData);

        try {
            $this->importTransactionsService->execute($transactionJsonFile);
            echo('call succesful');

        } catch (\Exception $exception) {
            error_log($exception->getMessage());
        }
    }

    /**
     * @Inject ExactGlobeAxians\Application\SaveInvoiceIdMappingDataService
     * @var \ExactGlobeAxians\Application\SaveOutstandingAmountsDataService
     */
    private $saveOutstandingAmountsDataService;

    /**
     * @Inject ExactGlobeAxians\Application\SyncOutStandingItemsService
     * @var SyncOutStandingItemsService
     */
    private $syncOutStandingItemsService;

    /**
     * http://dev.omniboxx.johan.loc/api_accounting_axians/read-outstanding-amounts
     *
     * $transactionJsonFile = '_data/exact_globe_axians/read-outstanding-amounts/1666753499_6358a3dba8dec.json';
     * @return
     */
    public function readOutstandingAmountsAction()
    {
        $this->disableView();

        $inputData = file_get_contents('php://input');
        $transactionJsonFile = $this->saveOutstandingAmountsDataService->execute($inputData);

        try {
            $this->syncOutStandingItemsService->execute($transactionJsonFile);
            echo('call succesful');
        } catch (\Exception $exception) {
            error_log($exception->getMessage());
        }
    }
}
