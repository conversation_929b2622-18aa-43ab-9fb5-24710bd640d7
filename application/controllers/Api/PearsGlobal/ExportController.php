<?php

class Api_PearsGlobal_ExportController extends \GlobalController
{
    public function preDispatch()
    {
        $this->disableView();

        $basicAuthenticationLoginService = new \Api\BasicAuthenticationLoginService();

        try {
            $basicAuthenticationLoginService->execute(
                $_SERVER['PHP_AUTH_USER'],
                $_SERVER['PHP_AUTH_PW']
            );
        } catch (Exception $exception) {
            header('WWW-Authenticate: Basic realm="Omniboxx PearsGlobal"');
            header('HTTP/1.0 401 Unauthorized');
            echo $exception->getMessage();
            die();
        }
    }

    public function generatePearsGlobalUnitReportAction()
    {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForUnit();
            $report->outputJSON($data);
        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForUnit) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generatePearsGlobalLeaseReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForLease();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLease) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generatePearsGlobalLeaseUnitReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForLeaseUnit();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLeaseUnit) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generatePearsGlobalRecurringBillingReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForRecurringBilling();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForRecurringBilling) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }
    public function generatePearsGlobalOptionReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForOption();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForOption) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generatePearsGlobalLegalEntityReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForLegalEntityTenant();
            $report->outputJSON($data);


        } catch (Exception $e) {

            $clientEmailAddr = array(
                'name' => 'Klantenservice '.Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            );

            $supportEmailAddr = array(
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            );

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForLegalEntityTenant) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function generatePearsGlobalRentEscalationReportAction() {
        // die('disabled for now');

        try {
            $report = new PearsGlobalReport();
            $data = $report->gatherDataForRentEscalation();
            $report->outputJSON($data);
        } catch (Exception $e) {

            $clientEmailAddr = [
                'name' => 'Klantenservice ' . Settings::get('general_company'),
                'email' => Settings::get('general_email'),
            ];

            $supportEmailAddr = [
                'name' => 'Klantenservice Omniboxx',
                'email' => '<EMAIL>',
            ];

            new EmailOutgoing(array(
                'to'		=> $supportEmailAddr,
                'from'		=> $clientEmailAddr,
                'subject'	=> 'Pears Global rapport fout bij '.Settings::get('general_company').' / '.Settings::get('general_company_shortname'),
                'text'		=> 'Er is iets mis gegaan bij het genereren van het '
                    .'Pears Global (gatherDataForRentEscalation) rapport. De volgende exceptie trad op, zie mogelijk '
                    .'het errorlog (rond '.date('H:i d-m-Y').') van de klant voor meer details:<br>'.$e->getMessage(),
            ));

            p($e, 'die');

        }
    }

    public function apiCallExampleAction()
    {
        $domain = 'bluebird.omniboxx.nl';
        $requestString = json_encode($this->testData);

        $username = 'PearsGlobal_Report';
        $password = '>2dp9Ir5Lq9kEF6G';

        $curlResource = curl_init();

        /* for dev environment self-signed ssl certificate */
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYPEER, 0);

        /* set POST and response options */
        curl_setopt($curlResource, CURLOPT_RETURNTRANSFER, 1);

        /* setup basic authentication login  */
        curl_setopt($curlResource, CURLOPT_HEADER, 1);
        curl_setopt($curlResource, CURLOPT_USERPWD, $username . ":" . $password);

        /* make request to get JSON data  */
        curl_setopt(
            $curlResource,
            CURLOPT_URL,
            'https://' . $domain . '/api_pears-global_export/generate-pears-global-unit-report'
        );

        curl_setopt($curlResource, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curlResource, CURLOPT_POSTFIELDS, $requestString);

        $responseText = curl_exec($curlResource);
        $httpCode = curl_getinfo($curlResource, CURLINFO_HTTP_CODE);

        /* close the session */
        curl_close($curlResource);

        /* handle exceptions */
        if (in_array($httpCode, [401, 404])) {
            echo $responseText;
            die();
        }

        /* parse the response */
        $responseJson = json_decode($responseText, true);

        echo $responseText;


    }

}
