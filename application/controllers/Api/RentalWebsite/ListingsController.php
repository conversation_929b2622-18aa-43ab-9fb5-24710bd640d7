<?php

class Api_RentalWebsite_ListingsController extends GlobalController {
    public function preDispatch()
    {
        $this->disableView();

        $basicAuthenticationLoginService = new \Api\BasicAuthenticationLoginService();

//        try {
//            $basicAuthenticationLoginService->execute(
//                $_SERVER['PHP_AUTH_USER'],
//                $_SERVER['PHP_AUTH_PW']
//            );
//        } catch (Exception $exception) {
//            header('WWW-Authenticate: Basic realm="Omniboxx Accounting Axians"');
//            header('HTTP/1.0 401 Unauthorized');
//            echo $exception->getMessage();
//            die();
//        }
    }

    /**
     * http://dev.omniboxx.johan.loc/api_rental-website_listings/create-json
     * @return void
     */
    public function createJsonAction()
    {
        ini_set('max_input_time', 10000);
        ini_set('memory_limit', '8000M');
        ini_set('max_execution_time', 60 * 60 * 2); // 2 hours

        $website_export = new Controllers\Website\Export();
        $website_export->execute();

        ob_clean();

        $xmldata = simplexml_load_string($website_export->getExportXml());

        $jsondata = json_encode($xmldata,  JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $arrayData = json_decode($jsondata, true);
        $arrayData = $this->convertEmptyToNull($arrayData);

        $jsondata = json_encode($arrayData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        // Display json data
        header('Content-Type: application/json');
        echo $jsondata;
    }

    private function convertEmptyToNull($data) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->convertEmptyToNull($value);
            } elseif ($value === '' || strtolower($value) === 'null') {
                if (substr($key, strpos($key, "_")) == '_integer') {
                    $newKey = strstr($key, "_", true);
                    unset($data[$key]);
                    $data[$newKey] =  Null;
                } else {
                    $data[$key] = Null;
                }
            }  elseif (is_numeric($value) &&  substr($key, strpos($key, "_")) == '_integer') {
                $newKey = strstr($key, "_", true);
                unset($data[$key]);
                $data[$newKey] =  intval($value);
            }
        }
        return $data;
    }

    public function getPhotoAction(){
        $file_types = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
        ];

        $photo_path = substr($_SERVER['REQUEST_URI'], strlen('/pararius/get-photo'));
        $photo_path = str_replace('../', '', $photo_path);

        $full_path = Main::app()->getDir('pararius_export') . 'photos' . $photo_path;
        $full_path = urldecode($full_path);

        $render_error = function(){
            $this->getResponse()
                ->setHttpResponseCode(404)
                ->sendResponse();
            exit();
        };

        if(!is_readable($full_path))
            $render_error();

        if($file_type = $file_types[pathinfo($full_path, PATHINFO_EXTENSION)]) {
            header("Content-Type: " . $file_type);
            echo file_get_contents($full_path);
        } else {
            $render_error();
        }
    }
}
