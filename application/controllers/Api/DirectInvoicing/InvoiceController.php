<?php


use Api\DirectInvoicing\CreateDirectInvoiceRequestFactory as CreateDirectInvoiceRequestFactory;
use Api\DirectInvoicing\CreateDirectInvoiceService as CreateDirectInvoiceService;
use Api\DirectInvoicing\Invoice\CreateInvoiceResponse as CreateInvoiceResponse;

class Api_DirectInvoicing_InvoiceController extends \GlobalController
{
    private $testData = [
        "invoice" => [
            "period" => 11,
            "period_type" => "monthly",
            "year" => 2021,
            "identifier" => "1023",
            "language" => "nl",
            "is_credit" => false
        ],
        "corporation" => [
            "name" => "Omniboxx B.V.",
            "bankaccount" => "******************",
            "bic" => "RABONL2U",
            "kvk" => "********",
            "vat_number" => "852992907B01",
            "phone_number" => "**********",
            "website" => "www.omniboxx.nl",
            "email" => "<EMAIL>",
            "address" => [
                "address" => "Langeweg",
                "number" => 123,
                "number_addition" => "B",
                "zipcode" => "1234AZ",
                "city" => "Amsterdam"
            ]
        ],
        "tenant" => [
            "gender" => "male",
            "initials" => "T.R.B.",
            "firstname" => "Tim",
            "middlename" => "",
            "name" => "Bijkerk",
            "bankaccount" => "******************",
            "address" => [
                "address" => "Dorpstraat",
                "number" => "3",
                "number_addition" => "C",
                "zipcode" => "4321ZA",
                "city" => "Utrecht"
            ],
            "email" => "<EMAIL>"
        ],
        "invoice_rows" => [
            ['description' => 'Huur', 'amount' => '523.00', 'vat_percentage' => 21],
            ['description' => 'Servicekosten', 'amount' => '323.92', 'vat_percentage' => 0],
        ],
        "order_lines" => [
            "general" => [
                "periodType" => 1,
                "period" => 1,
                "language" => "nl"
            ],
            "leaseInformation" => [
                "firstDayOfTerm" => "2021-09-21T13:16:32.558Z",
                "lastDayOfTerm" => "2021-09-21T13:16:32.558Z",
                "leaseInterestChargeable" => true,
                "leaseVAT" => 32.34,
                "leaseVATOption" => true,
                "contractRentPerPeriod" => 523,
                "grossRentPerPeriod" => 523,
                "serviceCostPerPeriod" => 323.92,
                "rentPeriodType" => 1,
                "incentives" => [
                    [
                        "startDate" => "2021-09-21T13:16:32.558Z",
                        "endDate" => "2021-09-21T13:16:32.558Z",
                        "explanation" => "Huurkorting",
                        "pricePerPeriod" => 236.34,
                        "rentPeriodType" => 1
                    ]
                ],
                "indexations" => [
                    [
                        "indexEffectiveDate" => "2021-11-01T13:16:32.558Z",
                        "nameIndexUsed" => "cpiConsumers"
                    ]
                ]
            ]
        ]
    ];


    public function preDispatch()
    {
        $this->disableView();
    }


    public function createAction()
    {
        try {
            $response = $this->createInvoice();

            header('content-type: application/json');
            echo json_encode(
                $response->toArray()
            );
        } catch (Exception $exception) {
            echo $exception;
            http_response_code(404);
        }
    }

    public function getInvoicePdfAction()
    {
        try {
            $response = $this->createInvoice();

            header('Content-Type:', 'application/pdf');
            header('Content-Disposition:', 'inline;');
            echo base64_decode($response->toArray()['pdfString']);
        } catch (Exception $exception) {
            echo $exception;
            http_response_code(404);
        }
    }

    /**
     * @throws Exception
     */
    public function getOrderLinesAction()
    {
        try {
            $response = $this->createInvoice();

            header('content-type: application/json');
            echo json_encode(
                $response->getInvoiceRows()
            );
        } catch (Exception $exception) {
            echo $exception;
            http_response_code(404);
        }
    }

    /**
     * @return CreateInvoiceResponse
     * @throws Exception
     */
    private function createInvoice()
    {
        $requestString = file_get_contents('php://input');
//        $requestString = json_encode($this->testData);
        $requestData = json_decode($requestString, true);

        if ($dataFromRequestLog = $this->getRequestDataFromRequestLog()) {
            $requestData = $dataFromRequestLog;
        }

        $createRequest = (new CreateDirectInvoiceRequestFactory())->build(
            loginManager::data()->id,
            $_SERVER['REMOTE_ADDR'],
            $requestData
        );

        return (new CreateDirectInvoiceService())
            ->execute($createRequest);
    }

    private function getRequestDataFromRequestLog()
    {
        $requestLogId = $this->getParam('request-log-id');

        if (!$requestLogId) {
            return false;
        }

        $directInvoicingRequestLogModelRow = (new DirectInvoicingRequestLogModel())->getById($requestLogId);

        return json_decode($directInvoicingRequestLogModelRow->request_data, true);
    }

    public function apiCallExampleAction()
    {
        $domain = 'directinvoicing.omniboxx.nl';
        $requestString = json_encode($this->testData);

        $curlResource = curl_init();

        /* for dev environment self-signed ssl certificate */
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curlResource, CURLOPT_SSL_VERIFYPEER, 0);

        /* set POST and response options */
        curl_setopt($curlResource, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curlResource, CURLOPT_POST, true);

        /* setup some options for maintaining session cookie  */
        curl_setopt($curlResource, CURLOPT_COOKIESESSION, true);
        curl_setopt($curlResource, CURLOPT_COOKIEJAR, 'omniboxx-login-cookie');
        curl_setopt($curlResource, CURLOPT_COOKIEFILE, tmpfile());

        /* make request to login  */
        curl_setopt($curlResource, CURLOPT_URL, 'https://' . $domain . '/user/login');
        curl_setopt($curlResource, CURLOPT_POSTFIELDS, [
            'username'=>'PropertyPass',
            'password'=>'>TbU.dxr>9JZ#5%}'
        ]);
        curl_exec($curlResource);

        /* make request to get invoice  */
        curl_setopt($curlResource, CURLOPT_URL, 'https://' . $domain . '/api_direct-invoicing_invoice/create');
        curl_setopt($curlResource, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($curlResource, CURLOPT_POSTFIELDS, $requestString);
        $responseText = curl_exec($curlResource);
        $httpCode = curl_getinfo($curlResource, CURLINFO_HTTP_CODE);

        /* close the session */
        curl_close($curlResource);

        /* handle exceptions */
        if ($httpCode === 404) {
            echo $responseText;
            die();
        }

        /* parse the response */
        $responseJson = json_decode($responseText, true);

        $pdf = base64_decode($responseJson['pdfString']);
        $invoiceIdentifier = $responseJson['identifier'];
        $invoiceRows = $responseJson['invoiceRows'];
    }

}
