<?

	include_once('Task.php');

	class TaskController extends GlobalController {

		public function indexAction() {
			$project = new Project();
			$ip = new InternalProject();
			$t = new TaskList();
			
			$id = $this->_getParam('id')?: loginManager::data()->id;
			$department = loginManager::data()->info['department'];

 			$user = TaskList::model()
				->forUser($id)
				->department($department)
				->order('important', 'DESC')
				->till(strtotime('+4 weeks'))
				->completed(0)
				->get();

			if (!$user) {
				$user = ['id' => $id];
			}
		
			$user = $t->groupWeek('dashboard', $user);
			$u_model = new Users();

			$this->view->user = $user;
			$this->view->users = array_values($u_model->getInternalUsers());
			$this->view->internalprojects = $ip->listAll();
			$this->view->projects = $project->getList([0 => ['field' => 'name']], false, true);
		}

		public function listAction(){
			$this->view->Breadcrumbs()->addCrumb('Taken');

			$this->view->headScriptHashed()->appendFile('media/javascript/task.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/task/list.js');

			$params = array('url-override' => 'task/list');

			foreach(array('view_template', 'id', 'user', 'status', 'map_to', 'type') as $param)
				if(!is_null($this->_getParam($param)))
					$params[$param] = $this->_getParam($param);

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('important' => 'DESC')));

			$this->view->modelListView('TasksTable', $params)

				->setTypes(array('tick', 'important', 'phone', 'title', 'user', 'dueDate'))

				->addFormat('tick', function($active, $item){
					return '<span' . ($item['completed'] ? ' class="completed"' : '') . '><span class="icon tick task_icon' . ($active ? ' active ' : '') . '">&nbsp;</span></span>';
				})

				->addFormat('important', function($active){
					return '<span class="icon important task_icon' . ($active ? ' active ' : '') . '"><span>&nbsp;</span></span>';
				})

				->addFormat('dueDate', 'date')

				->addFormat('user', 'buildshortname')

				->addFormat('phone', function($active){
					return '<span class="icon phone task_icon' . ($active ? ' active ' : '') . '"><span>&nbsp;</span></span>';
				})

				->addButtons(array(
					'add' => 'Toevoegen',
				))

				->setOptions(array(
					'item_title' => 'Taak',
					'paginator_items_per_page' => 5,
					'editAction'
				))

				->addButtons(array(
					'add' => 'Toevoegen'
				))

				->render($this);
		}

		public function findMapToAction(){
			$this->disableView();

			$post = $this->getRequest()->getPost();

			$find_map_to_service = new \Controllers\Task\FindMapToService($post['value']);
			$results = $find_map_to_service->execute();

            echo json_encode($results);
		}
		
		public function getDetailsAction(){
			$this->disableView();
			
			$project = new Project();
			$ip = new InternalProject();
			$array = array('id' => loginManager::data()->id);
			
			if(!$this->_getParam('id')){
				$array['internalprojects'] = $ip->listAll();
				$array['projects'] = $project->getList(array(0 => array('field' => 'name')), false, true);
				$array['users'] = $this->view->users = TaskList::model()->allUsers();
			} else {
				$user = TaskList::model()
					->getId($this->_getParam('id'))
					->get();

				$array['user'] = $user ? end($user['users']) : false;
			}

			echo json_encode(utf8_encode_recursive($array));
		}

		public function saveAction() {
			$this->disableView();

			$data = $this->_getParam('task');
			$task = new Task();
			$task->edit($data);

			$task->save();

			echo json_encode($task->toArray());

		}

		public function deleteAction() {
			$this->disableView();

			$data = $this->_getParam('task');
			$data['status'] = 'deleted';

			$task = new Task();
			$task->edit($data);
			$task->save();

		}
		
		public function reportAction(){
			$t = new TaskReport();
			$tp = new TaskReportPopulate();
			
			$this->hasTasks();
			
			$this->view->filterPopulate = $tp->get();
			
			$this->view->filters = $t->getFilters();
			$this->view->options = $t->getOptions();
			
			$this->render('report/page');
		}
		
		public function overviewAction() {
			$mat = new MapsAndTasks();
			$usr = new Users();
			new Quarterly(); // Make zend load the Quarterly.php file, from which we need the QuarterlyQuarter class
			$quarter = new QuarterlyQuarter();
			
			if (!($userId = $this->_getParam('user')))
				$userId = Zend_Auth::getInstance()->getIdentity()->id;
				
			if (($weekCount = $this->_getParam('weeks')) === null)
				$weekCount = 2;
				
			if (!($showIncomplete = $this->_getParam('incomplete')))
				$showIncomplete = false;
			
			$this->view->weekCount      = $weekCount;
			$this->view->userId         = $userId;
			$this->view->showIncomplete = $showIncomplete;
			
			$this->view->quarter        = $quarter->getQuarter();
			$this->view->map            = $quarter->forUser($userId);
			$this->view->tasks          = $mat->getUpcomingTasksByUser($userId, $weekCount);
			$this->view->unfinished     = $showIncomplete ? $mat->getUnfinishedTasksByUser($userId) : false;
			
			$this->view->userOpts = $usr->getInternalUsersOptions();
			$this->view->weekOpts = array(
				 0 => "Deze week",
				 1 => "Komende week",
				 2 => "Komende twee weken",
				 3 => "Komende drie weken",
				 4 => "Komende vier weken",
				-1 => "Alles",
			);
		}
    
		public function meetingAction(){
			$this->view->Breadcrumbs()->addCrumb('Taken')->addCrumb(ucFirst($departments[$this->_getParam('department')]) . ' overleg');
			
			$tabParam = $this->_getParam('tab');
			
			$this->view->activeTab = $tabParam ? $tabParam : 'agenda';
			$this->view->tabs = array(
				'agenda'        => "Agenda",
				'maps'          => "Maps",
				'priorities'    => "Prioriteiten",
				'tasks'         => "Taken",
				'support'       => "Klantenservice",
				'project-tasks' => "Projecttaken",
				'projects'      => "Projecten",
			);
		}
		
		public function meetingTabAgendaAction() {
			$this->view->departmentid = 1;
			$this->render('meeting-tabs/agenda');
		}
		
		public function meetingTabMapsAction() {
			$quarterly = new Quarterly();
			$usersModel = new Users();
			$users = $usersModel->getInternalUsers();
			$departments = $usersModel->getDepartments();
			$usersByDep = $usersModel->getUsersByDepartment();
			//var_dump($usersByDep);
			/*
			foreach ($usersByDep as $depId => $users) {
				if (!isset($maps[$depId]))
					$maps[$depId]
			}
			*/
			
			$maps = array();
			foreach ($usersByDep as $depId => $users) {
				$depLabel = $departments[$depId]['name'];
				$maps[$depLabel] = array();
				foreach ($users as $userId => $u) {
					$latestTaskDate = $quarterly->getLatestQuarterlyTaskForUser($userId);
					$userLabel = $u['full_name'];
					if ($latestTaskDate)
						$userLabel .= " <i>(" . date('j-n-Y', strtotime($latestTaskDate)) . ")</i>";
					$maps[$depLabel][$userLabel] = $quarterly->getQuartersForUser($u['id']);//$quarter->forUser($u['id']);
				}
			}
			$this->view->maps = $maps;
			$this->render('meeting-tabs/maps');
		}
		
		protected function getIncompleteTaskOptions () {
			return array(
				'group' => 'week',
			);
		}
		
		protected function getIncompleteTaskFilters ($depId, $important) {
			$filters = array(
				'department' => $depId,
				//'important'  => $important,
				'completed'  => false,
				'till'       => time(),
			);
			
			if ($important)
				$filters['important'] = $important;
				
			return $filters;
		}
		
		protected function getIncompleteTasksByDep ($depId, $important = false) {
			$taskReport = new TaskReport();
			$taskReport->noClosed = true;
			$taskReport->override['options'] = $this->getIncompleteTaskOptions();
			$taskReport->override['filters'] = $this->getIncompleteTaskFilters($depId, $important);
			
			return $taskReport->get();
		}
		
		protected function getCompleteTasksByDep ($depId, $important = false, $weeksAgo = 0) {
			$taskReport = new TaskReport();
			$taskReport->noClosed = true;
			$taskReport->override['options'] = array(
				'group' => 'week',
			);
			$taskReport->override['filters'] = array(
				'department' => $depId,
				//'important'  => $important,
				//'completed'  => true,
				'from'       => strtotime((date('N') == 1 ? "today" : "last monday") . " -{$weeksAgo} weeks"),
				'till'       => strtotime('next Friday +1 week'),
			);
			
			if ($important)
				$taskReport->override['filters']['important'] = $important;
			
			return $taskReport->get();
		}
		
		protected function getTasks ($important = 0, $completedThreshold = 0) {
			$usersModel = new Users();
			$departments = $usersModel->getDepartments();
			
			$incompleteTasksByDep = array();
			$completeTasksByDep = array();
			foreach ($departments as $dep) {
				$incompleteTasksByDep[$dep['name']] = $this->getIncompleteTasksByDep($dep['id'], $important);
				$completeTasksByDep[$dep['name']] = $this->getCompleteTasksByDep($dep['id'], $important, $completedThreshold);
			}
			
			$tasks = $this->mergeDoubles(array_merge_recursive($incompleteTasksByDep, $completeTasksByDep));
			return $this->sortWeeks($tasks);
		}
		
		protected function sortWeeks ($tasksByDep) {
			foreach ($tasksByDep as $depName => &$tasksByUser) {
				foreach ($tasksByUser as $userName => &$tasks) {
					ksort($tasks['weeks']);
				}
			}
			
			return $tasksByDep;
		}
		
		protected function allArrItemsNumKeysAndMatch ($arr) {
			foreach (array_keys($arr) as $key) {
				if (!is_numeric($key))
					return false;
			}
			
			$firstVal = array_shift($arr);
			while ($val = array_shift($arr)) {
				if ($val !== $firstVal)
					return false;
			}
			
			return true;
		}
		
		protected function mergeDoubles ($arr) {
			if ($this->allArrItemsNumKeysAndMatch($arr)) {
				return $arr[0];
			} else {	
				foreach ($arr as $key => &$val) {
					if (is_array($val)) {
						$val = $this->mergeDoubles($val);
					}
				}
			}
			
			return $arr;
		}
		
		public function meetingTabPrioritiesAction() {
			$this->meetingTabTasksAction(1);
		}
		
		public function meetingTabTasksAction($important = 0) {
			$thresholdParam = $this->_request->getParam('completed-tasks-threshold');
			$completedThreshold = $thresholdParam ? $thresholdParam : 0;
			$this->view->filters = $this->getIncompleteTaskFilters();
			$this->view->options = $this->getIncompleteTaskOptions();
			$this->view->compact = false;
			
			$thresholdOptions = array(
				0 => "voor deze week",
				1 => "tot 1 week geleden",
				2 => "tot 2 weken geleden",
				3 => "tot 3 weken geleden",
				4 => "tot 4 weken geleden",
			);
			
			$this->view->completedThreshold = $completedThreshold;
			$this->view->thresholdOptions = $thresholdOptions;
			
			$this->view->tasksByDep = $this->getTasks($important, $completedThreshold);
			$this->render('meeting-tabs/tasks');
		}
		
		public function meetingTabSupportAction() {
			$this->render('meeting-tabs/support');
		}
		
		public function meetingTabProjectTasksAction() {
			$this->render('meeting-tabs/project-tasks');
		}
		
		public function meetingTabProjectsAction() {
			$this->render('meeting-tabs/projects');
		}
		
		public function meetingCalendarAction(){
			if(is_numeric($this->_getParam('department')) === false)
				die();
			
			$tc = new TaskCalendars();
				
			if ($this->getRequest()->isPost()){
				$post = $this->getRequest()->getPost();
				
				if(is_numeric($this->_getParam('delete'))){
					$tc->delete('id = ' . $this->_getParam('delete'));
				} else {
					if(is_numeric($this->_getParam('update'))) {
						$row = $tc->fetchRow('id = ' .$this->_getParam('update'));
					} else {
						$row = $tc->createRow();
					}
				
					$row->department = 		isset($post['department']) ? $post['department'] : $this->_getParam('department');
					$row->description = 	$post['description'];
					$row->user = 			$post['user'];
					$row->interval = 		$post['repeat'] == 'true' && is_numeric($post['interval']) ? $post['interval'] : 1;
					$row->show =			$row->show ? $row->show : ($row->interval ? date('Y-m-d', strtotime('next monday')) : false);

					$row->save();
				}
			}
				
			foreach($tc->fetchAll($tc->select()->where('department = ?', $this->_getParam('department'))) as $point)
				if($point['interval'] == 1 || $this->getNextWeek($point))
					$calendar[$point['interval'] == 1 ? 'normal' : 'scheduled'][] = $point->toArray();
			

			$this->view->calendar = $calendar;	
			$this->view->users = (new Users())->getInternalUsersOptions();
			$this->view->department = $this->_getParam('department');
				
			$this->render($this->_getParam('ajax') ? 'calendar/list' : 'calendar/page');
		}

		public function getNextWeek($calendar){
			$tc = new TaskCalendars();

			// als show in het verleden ligt, dan bijwerken
			if(strtotime($calendar->show) < mktime(0, 0, 0)){
				$calendar->show = date('Y-m-d', strtotime($calendar->interval . ' weeks', strtotime($calendar->show)));
				$calendar->save();
			}

			// als show in de toekomst ligt, maar niet meer dan 4 dagen
			return strtotime($calendar->show) >= mktime(0, 0, 0) && strtotime('-4 days', strtotime($calendar->show)) < time();
		}
    
		public function reportListAction() {
			$t = new TaskReport();
			
			$t->department = $this->_getParam('department');
			$t->noClosed = $this->_getParam('noClosed') ? $this->_getParam('noClosed') : false;

			if ($this->hasTasks() == true){
	
				if ($this->getRequest()->isPost()){
					$_SESSION['taskreportoptionshastasks'] = $post['hasTasks'] ? $post['hasTasks'] : 'true';
					$post = $this->getRequest()->getPost();
					$filters = $t->setFilters($post['filters'])->setOptions($post['options']);
				}
			} else {
				$te = new TaskReportEmpty();
			}
			
			if ($this->_getParam('override') && $this->_getParam('options'))
				$t->override['options'] = $this->_getParam('options');
				
			if ($this->_getParam('override') && $this->_getParam('filters'))
				$t->override['filters'] = $this->_getParam('filters');
      
			$this->view->filters = $t->getFilters();
			$this->view->options = $t->getOptions();
			$this->view->compact = $this->_getParam('compact') ? $this->_getParam('compact') : false;
			
			$this->view->report = $this->hasTasks() ? $t->get() : $te->get() ;
			
			$this->render($this->hasTasks() ? 'report/list' : 'report/no-tasks-list');
		}
    
		public function reportProjectsAction(){
			$tp = new TaskProjects();
			$tp->department = $this->_getParam('department');
			$tp->noClosed = $this->_getParam('noClosed') ? $this->_getParam('noClosed') : false;
			
			if ($this->_getParam('override') && $this->_getParam('options'))
				$tp->override['options'] = $this->_getParam('options');
				
			if ($this->_getParam('override') && $this->_getParam('filters'))
				$tp->override['filters'] = $this->_getParam('filters');
						
			$this->view->report = $tp->get();
			$this->view->folded = is_array($this->_getParam('folded')) ? $this->_getParam('folded') : array();
			$this->view->foldedType = is_array($this->_getParam('foldedType')) ? $this->_getParam('foldedType') : array();   
			 
			$this->render('report/projects');			
		}
		
		public function hasTasks(){
			$var = 'trHasTasks';
			$postvar = 'hasTasks';
			
			if ($this->getRequest()->isPost()){
				$post = $this->getRequest()->getPost();
				$_SESSION[$var] = $post[$postvar] ? $post[$postvar] : $_SESSION[$var];
			} else {
				$_SESSION[$var] = isset($_SESSION[$var]) ? $_SESSION[$var] : 'true'; 
			}
			
			$hasTasks = $this->_getParam('override') && !is_null($this->_getParam($postvar)) ? $this->_getParam($postvar) : $_SESSION[$var];
			
			$hasTasks = $hasTasks == 'true' ? true : false;
				
			$this->view->hasTasks = $hasTasks;
			return $hasTasks;
		}
		

	}

?>
