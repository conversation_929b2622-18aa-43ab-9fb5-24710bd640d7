<?
	class BrancheLocationsController extends GlobalController {


        public function preDispatch()
        {
            //$this->renderWithNewLayout();

        }
		/**
		 * Redirects to the 'list' action
		 * 
		 */
		public function indexAction() {
			$this->_helper->redirector('list', 'branche-locations');
		}

		/**
		 * Lists all Object types using a Listview
		 * 
		 */
        public function listAction() {
            $this->view->Breadcrumbs()
                ->addCrumb('Vestigingen  overzicht', '');

            $data = BrancheLocations::getList();

            $this->view->listView($data)

                ->setTypes(array(
                    'description' => array(
                        'title' => 'Omschrijving',
                        'width' => 'xxxxxlarge truncate',
                    ),
                ))
                ->setOptions(array(
                    'item_title' => 'Vestiging',
                    'disabled_buttons_test' => function($row, $button){
                        if($button !== 'delete')
                            return false;

                        if(intval($row['id']) < 1)
                            return true;

                        $snModel = new SupportNotes();
                        $notes = $snModel->fetchAll($snModel->select()->where('category = ?', $row['id']));
                        return (count($notes) > 0);
                    }
                ))

                ->addButtons(array(
                    'add' => 'Toevoegen',
                    'edit' => 'Bewerken',
                    'delete' => 'Verwijderen',
                ),
                    array(
                        'id' => 'id',
                    ))

                ->render($this);
        }

        /**
         * Deletes the ObjectType indicated by the id parameter
         * and redirects to the 'list' action
         *
         * @return null
         */
        public function deleteAction() {
            $this->disableView();
            $id = $this->_getParam('id');
            $bl = new BrancheLocations();
            $snc = new SupportNotesCategories();

            if(intval($id) < 1)
                $this->_helper->redirector('list', 'support-notes-categories');


            $notes = $sn->fetchAll($sn->select()->where('category = ?', $id));

            if(isset($notes) && count($notes) > 0)
                $this->_helper->redirector('list', 'support-notes-categories');

            $snc = $snc->getById($id);

            if(isset($snc))
                $snc->delete();

            $this->_helper->redirector('list', 'support-notes-categories');
        }



		public function editAction() {

			$this->view->Breadcrumbs()
				->addCrumb('Vestigingen overzicht', 'branche-locations/list/')
				->addCrumb('Vestiging '.($this->_getParam('id') ? 'bewerken' : 'toevoegen') ,'');

			// this is to have the objecttypeId available in the form
			// through $this->getOwner()->id
			$this->supportNotesCategoriesid = $this->_getParam('id');

			$this->view->form = $this->view->EditView($this, 'BrancheLocations',
				array('id' => $this->_getParam('id')))
				->render($this);

			// TODO: echt editen, maar alleen als mag
			// $this->_helper->redirector('list', 'object-type');

		}
	}
?>
