<?

	class RentalController extends GlobalController
	{

		protected function getPublishStatusForObjects($object_ids){

			$select = db()->select()
				->from(['o' => 'objects'], ['id'])
				->joinLeft(['op' => 'object_publish'], 'op.object_id = o.id', ['status_override', 'publish_website', 'publish_pararius', 'publish_funda'])
				->where('op.enabled = ?', true)
				->where('op.publish_website = ? OR op.publish_pararius = ?', true)
				->where('op.start_date <= ?', date('Y-m-d'))
				->where('op.end_date >= ? OR op.end_date IS NULL', date('Y-m-d'))
				->where('o.id IN (' . implode_for_where_in($object_ids) . ')');

            $data = db()->fetchAll($select);
			return array_combine(
				array_column($data, 'id'),
				$data
			);
		}

		public function objectsAction()
		{
			ini_set('memory_limit', '8000M');

			if (is_null($this->getParam('og_type')))
				$this->setParam('og_type', 'particulier');

			$ignogerfilterbranches = $this->getParam('ignorebranches');

			$spec_model = new ObjectsSpecificationsValues();

			$this->view->extraWidth = true;
			$this->view->Breadcrumbs()
				->addCrumb(ucfirst(translate()->_('rental')))
				->addCrumb(ucfirst(translate()->_('listings')));

			$select = db()->select()
				->from(['o' => 'objects'], ['id', 'size' => 'm2', 'build'])
				->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['og_type' => 'type'])
				->joinLeft(['a' => 'address'], 'a.type_id = o.id AND a.type = "object"', ['address', 'number', 'city', 'zipcode'])
				->joinLeft(['ot' => 'object_type'], 'ot.id = o.type', ['object_type' => 'name'])
                ->joinLeft(
                    ['rd' => 'rental_deals'],
                    'rd.object_id = o.id AND ' .
                    'rd.cancelled = "0" AND ' .
                    'rd.archived = "0" AND ' .
                    'rd.website_deal = "no"',
                    ['number_of_active_deals' => 'COUNT(rd.id)']
                )
                ->joinLeft(['p' => 'projects'], 'p.id = og.project', false)
                ->joinLeft(['u' => 'users'], 'p.responsible_commercial = u.id', ['accountmanager' => 'rendered_name'] )
                ->where('p.sales_status = ?', 'Closed')
				->where('o.inactive = ?', false)
				->where('og.id IS NOT NULL')
				->group('o.id');

			if (!$ignogerfilterbranches)
			    UsersProjects::filterSelect($select, ['og.project']);

			$data = db()->fetchAll($select);

			$object_ids = [];
			foreach ($data as $data_item)
				$object_ids[] = $data_item['id'];

			$publish_data = $this->getPublishStatusForObjects($object_ids);


			$uo_model = new Objectusers();
			$uo_data = $uo_model->getMostRecentForObjects($object_ids);

			$ocv_model = new ObjectsComponentsValues();
			$ocv_data = $ocv_model->getMostRecentTotalForObjects($object_ids);

            $object_ids = [];
			foreach ($data as $data_key => $data_item) {
				$uo_item = $uo_data[$data_item['id']];
				$ocv_item = $ocv_data[$data_item['id']];
				$publish_row = $publish_data[$data_item['id']];

				$data[$data_key]['total_amount'] = $ocv_item ? $ocv_item['total'] : false;

				if (!$uo_item) {
					unset($data[$data_key]);
					continue;
				}

				if ($uo_item['object'] != $data_item['id']) continue;

				$uo_status = $uo_model->getStateAndAvailableDateForUoRow($uo_item);



				if($uo_status === false) {
                    unset($data[$data_key]);
                    continue;
                } else{
					$data[$data_key]['state'] = $uo_status['state'];

					if($data[$data_key]['state'] == 'available_soon'){
                        $data[$data_key]['state'] = 'available';
					}

					$data[$data_key]['available_from'] = $uo_status['available_from'];
				}

				if ($data_item['number_of_active_deals'] > 0) {
                    $data[$data_key]['state'] = 'concept';
				}

				$object_ids[] = $data_item['id'];
				$publish_status = $publish_row['status_override'];

                $noPublishStatusOverride = in_array($publish_status, ['default', null]);
                $objectStateIsAvailable = in_array($data[$data_key]['state'], ['available', 'available_soon']);
                $objectStateIsConcept = $data[$data_key]['state'] === 'concept';
                $hasActiveDeals = $data_item['number_of_active_deals'] > 0;
                $hasNoActiveDeals = !$hasActiveDeals;

                if ($noPublishStatusOverride && $objectStateIsAvailable && $hasNoActiveDeals) {
                    $data[$data_key]['publication_status'] = 'available';
                } elseif ($noPublishStatusOverride && ($objectStateIsConcept || $hasActiveDeals)) {
                    $data[$data_key]['publication_status'] = 'option';
                } elseif ($publish_status) {
                    $data[$data_key]['publication_status'] = $publish_status;
                } else {
                    $data[$data_key]['publication_status'] = null;
                }

                $data[$data_key]['publication_type'] = [];

				foreach(['website', 'pararius', 'funda', 'vidii'] as $publish_type) {
                    if ($publish_row['publish_' . $publish_type]) {
                        $data[$data_key]['publication_type'][] = $publish_type;
                    }
                }
			}

			foreach ($spec_model->getSpecificationsForAllObjectsFlat($object_ids) as $spec_object_id => $specification_object) {

                foreach ($data as $data_item_key => $data_item) {
                    if ($data_item['id'] == $spec_object_id) {
                        foreach ($specification_object as $specification) {
                            if ($specification['input_type'] == 'select' && !is_null($specification['option_value']) && is_numeric($specification['value'])) {
                                $data[$data_item_key][$specification['type_name']] = $specification['option_value'];
                            } else {
                                $data[$data_item_key][$specification['type_name']] = $specification['value'];
                            }
                        }
                    }
                }
            }

			$types = array(
				'address' => array('title' => ucfirst(translate()->_('address')), 'width' => 'medium truncate'),
				'number' => array('title' => ucfirst(translate()->_('number')), 'width' => 'xsmall left'),
				'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') :  '#spcf.', 'width' => 'small'),
				'zipcode' => array('title' => ucfirst(translate()->_('postal')), 'width' => 'xsmall'),
				'city' => array('title' => ucfirst(translate()->_('city')), 'width' => 'medium'),
				'state' => array('title' => ucfirst(translate()->_('status')), 'width' => 'xsmall'),
                'publication_type' => ['title' => ucfirst(translate()->_('publication')), 'width' => 'xsmall'],
				'publication_status' => array('title' => ucfirst(translate()->_('publication_status')), 'width' => 'xsmall'),
				'available_from' => ['title' => ucfirst(translate()->_('from')), 'width' => 'xsmall', 'type' => 'date'],
				'size' => array('title' => ucfirst(translate()->_('square_size')), 'width' => 'xxsmall right'),
				'total_amount' => array('title' => ucfirst(translate()->_('rent_price')), 'width' => 'xsmall right'),
				'has_carpeting' => array('title' => ucfirst(translate()->_('upholstered')), 'width' => 'xsmall right'),
				'has_furniture' => array('title' => ucfirst(translate()->_('furnished')), 'width' => 'xsmall right'),
				'spacer' => array('title' => '', 'width' => 'xxxxsmall'),
				'make_deal' => ['title' => ucfirst(translate()->_('make_deal')), 'width' => 'xxsmall']
			);

			$specification_filters = [];
			$specification_types = [];

			$publication_type_options = [
                'website' => ['title' => 'Website'],
                'pararius' => ['title' => 'Pararius'],
                'funda' => ['title' => 'Funda']
            ];

			if(Settings::get('modules_vidii')){
                $publication_type_options['vidii'] = ['title' => 'vidii'];
			}

			$specificationTypes = $spec_model->getAllSpecificationTypes();

			foreach ($specificationTypes as $specification)
				if ($specification['object_page_filter']) {
					$specification_filters[$specification['type_name']] = ['renderSeparately' => true, 'title' => $specification['type_title'], 'type' => $specification['filter_type'], 'class' => 'specification_filter_type_' . $specification['type']];
					$specification_types[] = $specification['type_name'];
				}

			//p($data,'die');
			$this->view->ListView($data)
				->setTypes($types)
				->addFormat('inactive', 'bool')
				->addFormat('zipcode', 'zipcode')
				->addFormat('city', function($value){
					return $value && $value != '' ? $value : ucfirst(translate()->_('unknown'));
				})
				->addFormat($specification_types, function($value){
					return $value && $value != '' ? $value : ucfirst(translate()->_('unknown'));
				})
				->addFormat('size', function ($value) {
					return $value > 0 ? number_format($value, 0, ',', '.') : '-';
				})
				->addFormat('state', function ($value, $item) {
					if ($value == 'available') {
						return ucfirst(translate()->_('available'));
					} elseif ($value == 'concept') {
						return ucfirst(translate()->_('option'));
					} elseif ($value == 'tenant') {
						return ucfirst(translate()->_('rented'));
					} else {
						return ucfirst(translate()->_('unknown'));
					}
				})
				->addFormat(['publication_type'], function($value,$item){
					$publication_type_images = [
						'website' => '<i title="Website" class="publication_type_website fa fa-sitemap"></i>',
                        'pararius' => '<img class="publication_type_pararius" title="Pararius" src="/media/images/icons/pararius.png" />',
                        'funda' => '<img class="publication_type_funda" title="Funda" src="/media/images/icons/funda.png" />',
						'vidii' => '<img class="publication_type_vidii" title="Vidii" src="/media/images/icons/vidii.png" />'
					];

					if(count($item['publication_type']) <= 0){
						return '-';
					} else {
						$formatted = '';
                        if (is_array($item['publication_type'])) {
                            foreach ($item['publication_type'] as $publication_type) {
                                $formatted .= $publication_type_images[$publication_type];
                            }
                        }
					}

					return $formatted;
				})
				->addFormat(['available_from'], 'date')
				->addFormat(['og_type', 'object_type'], function ($value) {
					return $value && $value != '' ? ucfirst($value) : ucfirst(translate()->_('unknown'));
				})
				->addFormat('publication_status', function($value){
					$publication_statusses = [
						'available' => ucfirst(translate()->_('available')),
						'option' => ucfirst(translate()->_('option')),
						'occupied' => ucfirst(translate()->_('rented')),
					];

					return isset($publication_statusses[$value]) ? $publication_statusses[$value] : '-';
				})
				->addFormat('investor', function ($value) {
					return $value > 0 ? '<a href="support/show/id/' . $value . '/type/investor" target="_blank">' . Investors::getName($value) . '</a>' : '-';
				})
				->addFormat('address', function ($value, $item) {
					return '<a href="rental-deals_timeline/for-object/id/' . $item['id'] . '/" target="_blank">' . $value . '</a>';
				})
				->addFormat('make_deal', function ($value, $item) {
					return '<a title="' . ($item['number_of_active_deals'] > 0 ? $item['number_of_active_deals'] : 'Nog geen') . ' actieve deal voor dit object"><i class="fa fa-tasks" style="padding-right: 3px;" aria-hidden="true"></i>' . ($item['number_of_active_deals'] > 0 ? ' ' . $item['number_of_active_deals'] : '') . '</a>';
				})
				->addFormat('total_amount', 'money')
				->addActions([
					'buttons' => [
						['title' => 'Etalage sheet', 'icon' => 'report', 'action' => 'rental/get-window-sheets', 'confirm' => false, 'noAjax' => true],
						['title' => 'Brochure downloaden', 'icon' => 'report', 'action' => 'rental/get-brochure', 'noAjax' => true],
						['title' => 'Brochure mailen', 'icon' => 'report', 'action' => 'rental/brochure-email', 'javascript_function' => 'email_compose_function', 'confirm' => false],
						['title' => 'Route maken', 'icon' => 'house_go', 'action' => 'rental/make-route', 'confirm' => false, 'noAjax' => true],
					]
				])
				->setFilters(array_merge([

					// renderSeparately
					'city' => ['title' => ucfirst(translate()->_('city')), 'renderSeparately' => true, 'type' => 'select_multiple', 'order_by_title' => 'ASC'],
                    'accountmanager' => ['title' => 'Accountmanager', 'renderSeparately' => true, 'type' => 'select_multiple', 'order_by_title' => 'ASC'],
					'og_type' => ['renderSeparately' => true, 'title' => ucfirst(translate()->_('residential_commercial')), 'show_all_disabled' => true, 'custom_options' => ['particulier' => ['title' => 'Particulier'], 'commercieel' => ['title' => 'Commercieel']]],
					'available_from' => ['renderSeparately' => true, 'title' => ucfirst(translate()->_('from')), 'type' => 'date_range'],
					'total_amount' => ['renderSeparately' => true, 'title' => ucfirst(translate()->_('rent_price')), 'type' => 'range'],
					'object_type' => ['renderSeparately' => true, 'title' => 'Type object'],
					'size' => ['renderSeparately' => true, 'title' => ucfirst(translate()->_('square_size')), 'type' => 'range'],


					'address' => ['type' => 'input'],
					'number' => ['type' => 'number'],
					'build' => ['type' => 'input'],
					'zipcode' => ['type' => 'input'],
                    'publication_type' => [
                    	'custom_options' =>	$publication_type_options,
						'hideCount' => true,
						'custom_options_only' => true,
                        'comparison_operator' => 'in_raw'
					],
					'publication_status' => [],
					'state' => [],

				], $specification_filters))
				->setOptions(array('custom_class' => 'rentalObjectsList'))
				->render($this);

		}


        public function allObjectsAction()
        {
            ini_set('memory_limit', '8000M');

            if (is_null($this->getParam('og_type')))
                $this->setParam('og_type', 'particulier');

            $ignogerfilterbranches = $this->getParam('ignorebranches');

            $spec_model = new ObjectsSpecificationsValues();

            $this->view->extraWidth = true;
            $this->view->Breadcrumbs()
                ->addCrumb('Overzichten')
                ->addCrumb('Actueel overzicht niet verhuurd aanbod');

            $select = db()->select()
                ->from(['o' => 'objects'], ['id', 'size' => 'm2', 'build'])
                ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['og_type' => 'type'])
                ->joinLeft(['a' => 'address'], 'a.type_id = o.id AND a.type = "object"', ['address', 'number', 'city', 'zipcode'])
                ->joinLeft(['ot' => 'object_type'], 'ot.id = o.type', ['object_type' => 'name'])
                ->joinLeft(
                    ['rd' => 'rental_deals'],
                    'rd.object_id = o.id AND ' .
                    'rd.cancelled = "0" AND ' .
                    'rd.archived = "0" AND ' .
                    'rd.website_deal = "no"',
                    ['number_of_active_deals' => 'COUNT(rd.id)']
                )
                ->joinLeft(['p' => 'projects'], 'p.id = og.project', false)
                ->joinLeft(['blp' => 'branch_locations_projects'], 'p.id = blp.project_id', false)
                ->joinLeft(['bl' => 'branch_locations'], 'bl.id = blp.branch_location_id', ['branche_location' => 'description'])

                ->joinLeft(['u' => 'users'], 'p.responsible_commercial = u.id', ['accountmanager' => 'rendered_name'] )
                ->where('p.sales_status = ?', 'Closed')
                ->where('o.inactive = ?', false)
                ->where('og.id IS NOT NULL')
                ->group('o.id');

            if (!$ignogerfilterbranches)
                UsersProjects::filterSelect($select, ['og.project']);

            $data = db()->fetchAll($select);



            $object_ids = [];
            foreach ($data as $data_item)
                $object_ids[] = $data_item['id'];

            $publish_data = $this->getPublishStatusForObjects($object_ids);

            $investorModel = new Investors();
            $investorData = $investorModel->getInvestorsForObjectIds($object_ids);
            $allInvestorIds = array_unique(array_values(array_flatten($investorData)));
            $investorNames = (new Investors())->getInvestorNames($allInvestorIds);

            $uo_model = new Objectusers();
            $uo_data = $uo_model->getMostRecentForObjects($object_ids);

            $ocv_model = new ObjectsComponentsValues();
            $ocv_data = $ocv_model->getMostRecentRentTotalForObjects($object_ids);

            $object_ids = [];
            foreach ($data as $data_key => $data_item) {
                $uo_item = $uo_data[$data_item['id']];
                $ocv_item = $ocv_data[$data_item['id']];
                $publish_row = $publish_data[$data_item['id']];

                $data[$data_key]['total_amount'] = $ocv_item ? $ocv_item['total'] : false;

                if (!$uo_item) {
                    unset($data[$data_key]);
                    continue;
                }

                if ($uo_item['object'] != $data_item['id']) continue;

                $uo_status = $uo_model->getStateAndAvailableDateForUoRow($uo_item);



                if($uo_status === false) {
                    unset($data[$data_key]);
                    continue;
                } else{
                    $data[$data_key]['state'] = $uo_status['state'];

                    if($data[$data_key]['state'] == 'available_soon'){
                        $data[$data_key]['state'] = 'available';
                    }

                    $data[$data_key]['available_from'] = $uo_status['available_from'];
                }

                if ($data_item['number_of_active_deals'] > 0) {
                    $data[$data_key]['state'] = 'concept';
                }

                $object_ids[] = $data_item['id'];
                $publish_status = $publish_row['status_override'];

                $noPublishStatusOverride = in_array($publish_status, ['default', null]);
                $objectStateIsAvailable = in_array($data[$data_key]['state'], ['available', 'available_soon']);
                $objectStateIsConcept = $data[$data_key]['state'] === 'concept';
                $hasActiveDeals = $data_item['number_of_active_deals'] > 0;
                $hasNoActiveDeals = !$hasActiveDeals;

                if ($noPublishStatusOverride && $objectStateIsAvailable && $hasNoActiveDeals) {
                    $data[$data_key]['publication_status'] = 'available';
                } elseif ($noPublishStatusOverride && ($objectStateIsConcept || $hasActiveDeals)) {
                    $data[$data_key]['publication_status'] = 'option';
                } elseif ($publish_status) {
                    $data[$data_key]['publication_status'] = $publish_status;
                } else {
                    $data[$data_key]['publication_status'] = null;
                }

                $data[$data_key]['publication_type'] = [];

                foreach(['website', 'pararius', 'funda', 'vidii'] as $publish_type) {
                    if ($publish_row['publish_' . $publish_type]) {
                        $data[$data_key]['publication_type'][] = $publish_type;
                    }
                }

                if (isset($investorData[$data_item['id']])) {
                    $data[$data_key]['investors'] = $investorData[$data_item['id']];
                }
            }

            foreach ($spec_model->getSpecificationsForAllObjectsFlat($object_ids) as $spec_object_id => $specification_object) {

                foreach ($data as $data_item_key => $data_item) {
                    if ($data_item['id'] == $spec_object_id) {
                        foreach ($specification_object as $specification) {
                            if ($specification['input_type'] == 'select' && !is_null($specification['option_value']) && is_numeric($specification['value'])) {
                                $data[$data_item_key][$specification['type_name']] = $specification['option_value'];
                            } else {
                                $data[$data_item_key][$specification['type_name']] = $specification['value'];
                            }
                        }
                    }
                }
            }



            $types = array(

                'address' => array('title' => 'Adres', 'width' => 'medium truncate'),
                'number' => array('title' => 'Nummer', 'width' => 'xxsmall left'),
                'build' => array('title' => Settings::get('build_label')  ? Settings::get('build_label') :  '#spcf.', 'width' => 'xxsmall'),
                'zipcode' => array('title' => 'Postcode', 'width' => 'xsmall'),
                'city' => array('title' => 'Plaats', 'width' => 'medium'),
                'investors' => ['title' => 'Beleggers', 'width' => 'xxlarge'],
                'state' => array('title' => 'Objectstatus', 'width' => 'xsmall'),
                'available_from' => ['title' => 'Vanaf', 'width' => 'xsmall', 'type' => 'date'],
                'publication_type' => ['title' => 'Publicatie', 'width' => 'xsmall'],
                'publication_status' => array('title' => 'Pub. status', 'width' => 'xsmall'),
                'size' => array('title' => 'm2', 'width' => 'xxxsmall'),
                'accountmanager' => array('title' => 'Accountmanager', 'width' => 'medium truncate'),
                'total_amount' => array('title' => 'Huurprijs', 'width' => 'xsmall right'),
                'spacer' => array('title' => '', 'width' => 'xxxxsmall'),
                 'number_of_active_deals' => ['title' => 'Deals', 'width' => 'xxsmall'],

            );

            $specification_filters = [];
            $specification_types = [];

            $publication_type_options = [
                'website' => ['title' => 'Website'],
                'pararius' => ['title' => 'Pararius'],
                'funda' => ['title' => 'Funda']
            ];

            if(Settings::get('modules_vidii')){
                $publication_type_options['vidii'] = ['title' => 'vidii'];
            }

            $specificationTypes = [];

            foreach ($specificationTypes as $specification)
                if ($specification['object_page_filter']) {
                    $specification_filters[$specification['type_name']] = ['renderSeparately' => true, 'title' => $specification['type_title'], 'type' => $specification['filter_type'], 'class' => 'specification_filter_type_' . $specification['type']];
                    $specification_types[] = $specification['type_name'];
                }


            $this->view->ListView($data)
                ->setTypes($types)
                ->addFormat('inactive', 'bool')
                ->addFormat('zipcode', 'zipcode')
                ->addFormat('city', function($value){
                    return $value && $value != '' ? $value : 'Onbekend';
                })
                ->addFormat($specification_types, function($value){
                    return $value && $value != '' ? $value : 'Onbekend';
                })
                ->addFormat('size', function ($value) {
                    return $value > 0 ? number_format($value, 0, ',', '.') : '-';
                })
                ->addFormat('state', function ($value, $item) {
                    if ($value == 'available') {
                        return 'Beschikbaar';
                    } elseif ($value == 'concept') {
                        return 'In optie';
                    } elseif ($value == 'tenant') {
                        return 'Verhuurd';
                    } else {
                        return 'Onbekend';
                    }
                })
                ->addFormat(['publication_type'], function($value){
                    $publication_type_images = [
                        'website' => '<i title="Website" class="publication_type_website fa fa-sitemap"></i>',
                        'pararius' => '<img class="publication_type_pararius" title="Pararius" src="/media/images/icons/pararius.png" />',
                        'funda' => '<img class="publication_type_funda" title="Funda" src="/media/images/icons/funda.png" />',
                        'vidii' => '<img class="publication_type_vidii" title="Vidii" src="/media/images/icons/vidii.png" />'
                    ];

                    if(count($value) <= 0){
                        return '-';
                    } else {
                        $formatted = '';
                        if (is_array($value)) {
                            foreach ($value as $publication_type) {
                                $formatted .= $publication_type_images[$publication_type];
                            }
                        }
                    }

                    return $formatted;
                })
                ->addFormat(['available_from'], 'date')
                ->addFormat(['og_type', 'object_type'], function ($value) {
                    return $value && $value != '' ? ucfirst($value) : 'Onbekend';
                })

                ->addFormat('investors', function ($value) use ($investorNames) {
                    if(!$value){
                        return '-';
                    }

                    $investorName = isset($investorNames[$value]) ? $investorNames[$value] : '-';

                    return '<a href="/support/show/id/' . $value . '/type/investor">' . $investorName . '</a>';
                })
                ->addFormat('publication_status', function($value){
                    $publication_statusses = [
                        'available' => 'Beschikbaar',
                        'option' => 'In optie',
                        'occupied' => 'Verhuurd',
                    ];

                    return isset($publication_statusses[$value]) ? $publication_statusses[$value] : '-';
                })

                ->addFormat('address', function ($value, $item) {
                    return '<a href="rental-deals_timeline/for-object/id/' . $item['id'] . '/" target="_blank">' . $value . '</a>';
                })

                ->addFormat('total_amount', 'money')

                ->setFilters(array_merge([

                    // renderSeparately
                    'accountmanager' => ['title' => 'Accountmanager', 'renderSeparately' => true, 'type' => 'select', 'order_by_title' => 'ASC'],
                    'investors' => ['title' => 'Beleggers', 'type' => 'input'],
                    'number_of_active_deals' => ['title' => 'Actieve deals', 'renderSeparately' => true, 'type' => 'select', 'order_by_title' => 'ASC'],
                    'branche_location' => ['title' => 'Vestiging', 'renderSeparately' => true, 'type' => 'select', 'order_by_title' => 'ASC'],
                    'address' => ['type' => 'input'],
                    'number' => ['type' => 'number'],
                    'build' => ['type' => 'input'],
                    'zipcode' => ['type' => 'input'],

                    'publication_type' => [
                        'custom_options' =>	$publication_type_options,
                        'hideCount' => true,
                        'custom_options_only' => true,
                        'comparison_operator' => 'in_raw'
                    ],
                    'publication_status' => [],
                    'state' => [],

                ], $specification_filters))
                ->setOptions(array('custom_class' => 'rentalObjectsList'))
                ->render($this);

        }


        public function conceptDealsAction() {



            foreach(scandir('media/javascript/dashboard/rental/element') as $file)
                if(str_replace('.', '', $file) !== '')
                    $this->view->headScriptHashed()->appendFile('media/javascript/dashboard/rental/element/' . $file);

            $this->view->headLink()->appendStylesheet('media/style/rental-deals/details.css');

            if($this->cached_view !== false){
                echo $this->cached_view;
                return;
            }

            # Departments
            //$this->view->departments = TaskList::model()->departments();

            $this->render('deals');
            //$this->renderAndSaveCache('dashboard/finance/index');




		}

		public function getWindowSheetsAction()
		{
			$this->disableView();
			ini_set('memory_limit', '8000M');

			$a_model = new Address();

			$object_ids = (array)$this->getParam('ids');

			$objects = [];
			foreach ($object_ids as $object_id) {
				$data = Address::get($object_id, 'object');
				$data['type_id'] = $object_id;
				$data['address'] = trim($data['address']);
				$data['zipcode'] = '';
				$city = trim($data['city']);
				//unset($data['city']);
				$objects['new_' . $object_id] = ['type_id' => $object_id, 'item_value' => trim($a_model->buildname(false, 'object', $data)) . ' ' . $city];
			}

            //p($data,'die');

			$model = new Controllers_Rental_Objects();

			$pdf = $model
				->setIds($object_ids)
				->fetchData()
				->createWindowSheetPdf();



			$pdf->browseroutput('sheet.pdf');
		}

		protected function createBrochure(){
			$object_ids = (array)$this->getParam('ids');

			$model = new Controllers_Rental_Objects();

			return $model
				->setIds($object_ids)
				->fetchData()
				->createBrochurePdf();
		}

		public function getBrochureAction()
		{
			$this->disableView();
			ini_set('memory_limit', '8000M');

			$pdf = $this->createBrochure();

			$pdf->browseroutput('Brochure.pdf');
		}

		public function brochureEmailAction()
		{
			$this->disableView();

			$pdf = $this->createBrochure();

			$ea_class = new EmailOutgoing([
				'concept' => true,
				'subject' => 'Route',
				'notemplate' => ''
			]);

			$a = new EmailAttachment($ea_class->id);
			$a->add(['data' => $pdf->zendpdf->render(), 'file' => 'Brochure.pdf']);

			ob_clean();
			echo json_encode($ea_class->id);
		}

		public function makeRouteAction()
		{
			$this->disableView();

			$a_model = new Address();

			$object_ids = (array)$this->getParam('ids');

			$objects = [];
			foreach ($object_ids as $object_id) {
				$data = Address::get($object_id, 'object');
				$data['type_id'] = $object_id;
				$data['address'] = trim($data['address']);
				$data['zipcode'] = '';
				$city = trim($data['city']);
				unset($data['city']);
				$objects['new_' . $object_id] = ['type_id' => $object_id, 'item_value' => trim($a_model->buildname(false, 'object', $data)) . ' ' . $city, 'search_value' => trim($a_model->buildname(false, 'object', $data)) . ' ' . $city];
			}


			$model = new Controllers_Rental_Route();
			$r_model = new Routes();
			$id = $r_model->createRow()->save();

			$model
				->setId($id)
				->saveData([
					'date' => date('d-m-Y'),
					'items' => [
						'schedule' => $objects,
						'user' => ['new_0' => ['type_id' => loginManager::data()->id, 'item_value' => loginManager::data()->info['name'], 'sub_type' => 'object']]
					]
				]);

			$this->_redirect('rental/route-edit', ['id' => $id]);
		}

		public function routeListAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Routeoverzicht', 'rental/route-list');

			if(is_null($this->getParam('status')))
				$this->setParam('status', 'current');

			$this->view->list = $this->view->modelListView('Routes')

			->setTypes(['status', 'identifier', 'date', 'users', 'registrants', 'objects'])

			->addFormat('status', function($value){
				if($value == 'current') return 'Actueel';

				return 'Historisch';
			})

			->setOptions(array(
				'item_title' => 'Route',
				'disabled_buttons_test' => function($row, $button){
					return false;
				}
			))
			->setFilters([
				'status' => ['title' => 'Status', 'renderSeparately' => true],
				'identifier' => ['type' => 'input'],
				'date' => ['type' => 'date'],
				'users' => [],
				'registrants' => ['type' => 'input'],
				'objects' => ['type' => 'input'],
			])

			->addButtons(array(
				'add' => 'Toevoegen',
				'edit' => 'Bewerken',
				'delete' => 'Verwijderen',
			))
			->render($this);
		}

		public function routeEditAction(){
			$this->view->headScriptHashed()
				->appendFile('media/javascript/datepicker.js')
				->appendFile('media/javascript/html2canvas.js');

			$this->view->Breadcrumbs()
				->addCrumb('Overzichten')
				->addCrumb('Routeoverzicht', 'rental/route-list')
				->addCrumb('Route samenstellen');

			$this->view->id = $id = $this->getParam('id') ?: false;

			$model = new Controllers_Rental_Route();

			if($id === false){
				$r_model = new Routes();
				$id = $r_model->createRow()->save();

				$model
					->setId($id)
					->saveData([
						'date' => date('d-m-Y'),
						'items' => [
							'user' => ['new_0' => ['type_id' => loginManager::data()->id, 'item_value' => loginManager::data()->info['name'], 'sub_type' => 'object']]
						]
					]);

				$this->_redirect('rental/route-edit', ['id' => $id]);
			}

			if($id) $model->setId($id);

			if($_POST){
				$_POST['items'] = json_decode($_POST['items'], true);
				$model->saveData($_POST);
				return;
			}

			$this->view->data = $id ? $model->getData() : false;
		}

		public function routeDeleteAction(){
			$id = $this->getParam('id');

			if(!($id > 0)) return;

			$r_model = new Routes();
			$ri_model = new RoutesItems();
			$r_model->delete('id = ' . $id);
			$ri_model->delete('route_id = ' . $id);

			$this->_redirect('rental/route-list');
		}

		public function routePdfAction(){
			$this->disableView();

			$this->view->id = $id = $this->getParam('id') ?: false;

			$model = new Controllers_Rental_Route();

			if($id) $model->setId($id);

			$model
				->createPdf()
				->browseroutput('Route.pdf');
		}

		public function routeEmailAction(){
			$this->disableView();

			$this->disableView();

			$this->view->id = $id = $this->getParam('id') ?: false;

			$model = new Controllers_Rental_Route();

			if($id) $model->setId($id);

			$pdf = $model->createPdf();

			$data = $model->getData();

			$to = [];
			foreach($data['items']['registrant'] as $registrant)
				if($registrant['type_id'] > 0)
					$to[] = ['id' => $registrant['type_id']];

			$ea_class = new EmailOutgoing([
				'concept' => 		true,
				'to' => 			$to,
				'subject' =>        'Route',
				'notemplate' =>		''
			]);

			$a = new EmailAttachment($ea_class->id);
			$a->add(['data' => $pdf->zendpdf->render(), 'file' => 'Route.pdf']);

			ob_clean();
			echo json_encode($ea_class->id);
		}


	 	public function addsearchAction() {


	 	}


	 	public function addleadAction() {


	 	}

	 	public function listAction() {
			$u_model = new Users();
			$data = $u_model->getTenantList($this->getAllUrlParams(), false , true );
		 

		}




	}

?>
