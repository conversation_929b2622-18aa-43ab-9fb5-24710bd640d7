<?php

use Vidii\Application\Service\SendAccessCodeRequest;
use Vidii\Application\Service\SendAccessCodeService;
use Vidii\Domain\Exception\DataIncompleteException;
use Vidii\Infrastructure\Domain\Factory\RecipientFactory;
use Vidii\Infrastructure\Domain\Factory\VidiiAccessCodeFactory;
use Vidii\Infrastructure\Domain\Repository\VidiiAccessCodesZendDbRepository;

class VidiiController extends GlobalController
{

	public function sendAccessCodeAction() {
		try {
			$recipient = (new RecipientFactory())->buildFromUserId($this->getParam('id'));
			$lifetime = Settings::get('modules_vidii_access_code_lifetime') ?: 21;

			$request = new SendAccessCodeRequest($recipient, $lifetime);
			$service = new SendAccessCodeService(
				new VidiiAccessCodesZendDbRepository(),
				new VidiiAccessCodeFactory(),
				new User(),
				new Projects(),
				Settings::get('modules_rental_website_default_email_account'),
				new Email(),
				new EmailAccounts(),
				new EmailAccount()
			);

			$service->execute($request);
		} catch (DataIncompleteException $e) {
		} catch (Exception $e) {
		}

		$params = $this->getAllParams();
		unset($params['module'], $params['controller'], $params['action']);
		$this->_redirect('support/show', $params);
	}

    public function quickPublishObjectAction()
    {
        $this->disableView();

        $objectId = $this->getParam('object_id');

        if (!$objectId) {
            dieWithStatuscode(500);
        }

        $name = 'quick-publish-object/objectId/' . $objectId;

        startWorker($name, 'cronjob_vidii', 'direct', true);
    }
}
