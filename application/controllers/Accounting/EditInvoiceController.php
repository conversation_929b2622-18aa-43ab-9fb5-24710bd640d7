<?php

use Accounting\Application\Service\UpdateInvoiceLineRequest;
use Accounting\Application\Service\UpdateInvoiceLineService;
use Accounting\Application\Service\ViewEditInvoiceService;

class Accounting_EditInvoiceController extends GlobalController
{
    public function viewAction()
    {
        $this->renderWithNewLayout();

        $invoiceId = $this->getParam('invoice_id');

        $viewEditInvoiceService = new ViewEditInvoiceService();
        $viewEditInvoiceService->execute($invoiceId);

        $this->view->assign('invoiceLink', $viewEditInvoiceService->getInvoiceLink());
        $this->view->assign('invoiceLineRows', $viewEditInvoiceService->getInvoiceLineRows());
    }

    public function editLineAction()
    {
        $this->renderWithNewLayout();

        $invoiceLineId = $this->getParam('invoice_line_id');

        $editInvoiceLineForm = new Form($this, 'Accounting/EditInvoice/edit-invoice-line');
        $this->view->assign('edit_invoice_line_form', $editInvoiceLineForm);
        $invoicesLineRow = (new InvoiceRows())->fetchRowById($invoiceLineId);

        if (null === $invoicesLineRow) {
            throw new \RuntimeException('Invoice line row now found');
        }

        $editInvoiceLineForm->populate([
            'invoice_line_id' => $invoicesLineRow->id,
            'ledger' => $invoicesLineRow->ledger,
            'cost_center' => $invoicesLineRow->cost_center,
        ]);

        if ($this->getRequest()->isPost()) {
            $postData = $this->getRequest()->getPost();

            $updateInvoiceLineService = new UpdateInvoiceLineService();
            $invoiceId = $updateInvoiceLineService->execute(new UpdateInvoiceLineRequest(
                $postData['invoice_line_id'],
                $postData['ledger'],
                $postData['cost_center']
            ));

            $this->redirect('accounting_edit-invoice/view/invoice_id/' . $invoiceId);
        }
    }
}
