<?php

use Accounting\Application\Service\UpdateCustomInvoiceLineRequest;
use Accounting\Application\Service\UpdateCustomInvoiceLineService;
use Accounting\Application\Service\ViewEditCustomInvoiceService;

class Accounting_EditCustomInvoiceController extends GlobalController
{
    public function viewAction()
    {
        $this->renderWithNewLayout();

        $customInvoiceId = $this->getParam('custom_invoice_id');

        $viewEditCustomInvoiceService = new ViewEditCustomInvoiceService();
        $viewEditCustomInvoiceService->execute($customInvoiceId);

        $this->view->assign('invoiceCustomLink', $viewEditCustomInvoiceService->getInvoiceCustomLink());
        $this->view->assign('invoiceCustomLineRows', $viewEditCustomInvoiceService->getInvoiceCustomLineRows());
    }

    public function editLineAction()
    {
        $this->renderWithNewLayout();

        $customInvoiceLineId = $this->getParam('custom_invoice_line_id');

        $editCustomInvoiceLineForm = new Form($this, 'Accounting/EditCustomInvoice/edit-custom-invoice-line');
        $this->view->assign('edit_custom_invoice_line_form', $editCustomInvoiceLineForm);
        $invoicesCustomLineRow = (new InvoicesCustomsRows())->fetchRowById($customInvoiceLineId);

        if (null === $invoicesCustomLineRow) {
            throw new \RuntimeException('Custom invoice line row now found');
        }

        $editCustomInvoiceLineForm->populate([
            'custom_invoice_line_id' => $invoicesCustomLineRow->id,
            'ledger' => $invoicesCustomLineRow->ledger,
            'cost_center' => $invoicesCustomLineRow->cost_center,
        ]);

        if ($this->getRequest()->isPost()) {
            $postData = $this->getRequest()->getPost();

            $updateCustomInvoiceLineService = new UpdateCustomInvoiceLineService();

            try {
                db()->beginTransaction();

                $customInvoiceId = $updateCustomInvoiceLineService->execute(new UpdateCustomInvoiceLineRequest(
                    $postData['custom_invoice_line_id'],
                    $postData['ledger'],
                    $postData['cost_center']
                ));

                $this->updateInvoiceRow($invoicesCustomLineRow->id, $postData);

                db()->commit();
            } catch (\Exception $exception) {
                db()->rollBack();
            }


            $this->redirect('accounting_edit-custom-invoice/view/custom_invoice_id/' . $customInvoiceId);
        }
    }

    private function updateInvoiceRow($invoicesCustomLineRowId, $postData)
    {
        $invoiceRow = InvoiceRows::findInvoiceRowByInvoiceCustomRowId($invoicesCustomLineRowId);

        if(!$invoiceRow){
            throw new \Exception('No invoice row found by invoice_custom_row_id:  '. $invoicesCustomLineRowId );
        }

        $invoiceRowModel = new InvoiceRows();
        $invoiceRowModel
            ->getById($invoiceRow['id'])
            ->setFromArray([
                'ledger' => $postData['ledger'],
                'cost_center' => $postData['cost_center'],
            ])
            ->save();
    }

}
