<?

	class ArrangementController extends GlobalController {

		public function preDispatch() {
			$this->view->Breadcrumbs()->addCrumb('Betalingsregelingen', 'arrangement/');
			$this->id = $this->view->id = $this->_getParam('id');
			$this->lib = new Arrangement();
			$this->form = new Form($this, 'arrangement');
		}

		public function indexAction(){
			$this->view->list = $this->lib->getList();			
		}
		
		public function editAction(){
			$this->view->Breadcrumbs()->addCrumb('Betalingsregeling ' . ($this->id ? 'bewerken' : 'toevoegen'));

			if ($this->getRequest()->getPost()) {
				$post = $this->getRequest()->getPost();
				$this->lib->save($this->id, $post);
				$this->_redirect('arrangement/index');
			}
			
			if($this->_getParam('user'))
				$this->populateWithUser($this->_getParam('user'));

			if($this->id)
				$this->populateForm();

			$this->view->data = $this->data;
			$this->view->form = $this->form;
		}

		public function deleteAction(){
			$this->disableView();

			if(!is_numeric($this->id))
				return;

			$a = new Arrangement();
			$a->delete($this->id);

			$this->_redirect('arrangement/index');
		}

		public function hasCollectionPaymentAction(){
			$this->disableView();
			$a = new Arrangement();

			if(!$this->_getParam('user'))
				return false;

			echo json_encode($a->hasCollectionPayment($this->_getParam('user')));
		}

		public function populateWithUser($user){
			$u = new Users();

			$user = $u->fetchRow($u->select()->where('id = ?', $user));

			$this->form->populate(array('user' => $user->toArray()));

			$this->populateWithInvoices($user->id);
		}


		public function populateWithInvoices($user, $arrangement = false){
			$a = new Arrangement();
			$this->view->hasCollectionPayment = $a->hasCollectionPayment($user);

			$aoi = new ArrangementOpenInvoices();

			foreach((array) $aoi->find($user, $arrangement) as $invoice){
				$invoice['amount'] = ($invoice['type'] == 'd' ? $invoice['amount'] *-1 : $invoice['amount']);
				$this->data['invoicesdetails'][$invoice['invoice']] = $invoice;

				$options = $this->form->invoices->list->getAttrib('options');
				
				if(key($options) == 0)
					$options = array();
					
				$options[$invoice['invoice']] = $invoice['name'];

				$this->form->invoices->list->setAttrib('options', $options);
			}
		}


		public function userListAction(){
			if(!$this->_getParam('user'))
				return;

			$this->view->user = $this->_getParam('user');
			$this->view->list = $this->lib->getList($this->_getParam('user'));			
		}

		public function populateForm(){

			$this->data = $this->formArray($this->lib->getById($this->id));

			if($this->data['user']['id'])
				$this->populateWithInvoices($this->data['user']['id'], $this->id);

			$this->form->populate($this->data);
		}

		public function getUsersAction(){
			$this->disableView();

			$aoi = new ArrangementOpenInvoices();

			echo json_encode($aoi->findForUser($this->_getParam('value')));
		}

		public function getInvoicesAction(){
			$this->disableView();

			$aoi = new ArrangementOpenInvoices();

			echo json_encode($aoi->find($this->_getParam('user')));
		}

		public function formArray($data){
			$array['general']['name'] = $data['name'];
			$array['general']['disabled'] = $data['disabled'];

			$array['user']['id'] = $data['user'];
			
			$array['frequence']['total_preview'] = '€ ' . new StringFormat($data['total_preview']/100, 'money');
			$array['frequence']['total'] = '€ ' . new StringFormat($data['total']/100, 'money');
			$array['frequence']['total_unformatted'] = $data['total'];
			$array['frequence']['number'] = $data['number'];
			$array['frequence']['period'] = '€ ' . new StringFormat($data['period']/100, 'money');

			foreach(array('gender', 'initials', 'firstname', 'middlename', 'name') as $field)
				$array['user'][$field] = $data['u_' . $field];

			$array['invoices']['list'] = $data['invoices'];

			return $array;
		}

	}
