<?php

include_once 'library/Broker.php';

class BrokerController extends GlobalController {

	public function preDispatch(){
		$params = $this->getAllParams();

		if(isset($params['selected_broker_publish'])) {
			$_SESSION['selected_broker_publish'] = $params['selected_broker_publish'];

			$this->_setParam('selected_broker_publish', NULL);
			unset($params['selected_broker_publish']);
			$this->_redirect(($params['controller'].'/'.$params['action']), $params);
		}

		if( BrokerPublish::isIpAddressAllowedForBroker($_SERVER['REMOTE_ADDR'], loginManager::data()->info['investor_company_id']) )
			BrokerPublish::setSessionSelected(
				BrokerPublish::getSelectList(
					BrokerPublish::getPublishedForBroker( loginManager::data()->info['investor_company_id']) ) );


		$this->view->isInvestor = $this->isInvestor = acl()->hasRole('investor');
		$this->view->ajax = $this->ajax = $this->_getParam('ajax') == 'true';
        $this->data_model = new \Controllers\Invoice\ComponentSpecificationReport(false);

		if($this->ajax)
			$this->disableView();
	}

	public function getModel()
	{
		if ($this->isInvestor) {
			return new BrokerInvestor(false, loginManager::data()->id);
		}

		return new Broker(false, loginManager::data()->id);
	}

	public function welcomeAction() {

		if (Settings::get('modules_kpi')) {
            $this->redirect('kpi-rapport_portal/dashboard');
		}

		if(acl()->hasRole('investor'))
			$this->getModel()->setTotalUnpayed();
		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'welcome';

		$welcometxt =  Settings::get('welcometext_investor_'.loginManager::data()->language.'') ? Settings::get('welcometext_investor_'.loginManager::data()->language.'') :Settings::get('welcometext_investor_nl') ;

		$this->view->welcometext = TextReplacer::replaceText($welcometxt);

	}

    public function emailListAction(){


        if (Settings::get('brokerportal_show_emails')) {
			$this->_helper->viewRenderer->setNoRender(true);

			$allowed_params = array('ajax', 'page', 'query','matchedignore', 'thisUserOnly' , 'filterComplaint');

			foreach($this->_getAllParams() as $param_name => $param_value)
				if(!in_array($param_name, $allowed_params)){
					$this->_setParam($param_name, NULL);
					unset($_POST[$param_name]);
				}

			echo $this->view->action('list', 'email', null, array(
				'title' => translate()->_('email_messages'),
				'filterUser' => loginManager::data()->id,
				'object' => true,
				'folder' => 'all',
				'ajax' => (bool) $this->_getParam('ajax'),
				'matchedignore' => true,
				'thisUserOnly' => true,
				'showFolderlist' => false,
				'filterComplaint' => $this->_getParam('filterComplaint'),
				'concept' => false,
				'limit' => 10,
				'showSearch' => true,
				'unsend' => false,
				'showMemo' => false,
				'url' => 'broker/email-list/',
				'detailsUrl' => 'broker/email-details/',
				'addClass' => Settings::get('modules_tenantLogin_new_styling') ? 'infobox' : ''
			));
        }


    }

    public function emailDetailsAction(){
        $this->_helper->viewRenderer->setNoRender(true);

        $allowed_params = array('id');

        foreach($this->_getAllParams() as $param_name => $param_value)
            if(!in_array($param_name, $allowed_params)){
                $this->_setParam($param_name, NULL);
                unset($_POST[$param_name]);
            }

        if(!$this->_getParam('id'))
            return;

        echo $this->view->action('details', 'email', null, array(
            'filterUser' => loginManager::data()->id,
            'ajax' => true,
            'id' => $this->_getParam('id'),
        ));
    }

    public function yearOverviewAction(){

        $this->view->Breadcrumbs()->addCrumb('Jaaroverzicht');

        //Query -> SEND: waar belegger id is voor die belegger. Dat id joinen op rows en costs voor de objects -> Filter standaard op YTD - per adres op alfabet en met totalen onderaan. Omzet en kosten inclusief de provisie. Beleggecombinaties / Check.

        $investorId = loginManager::data()->info['investor_company_id'];

        if (is_null($this->getParam('year')) && !$this->isAjaxRequest) {
            $this->setParam('year', date('Y'));
        }

        $investorYearOverviewDataService = (new \Controllers\Broker\InvestorYearOverviewDataService());
        $years = $investorYearOverviewDataService->getYears($investorId);
        if ($years) {

            $yearFilterOptions = [];
            foreach($years as $year){
                $yearFilterOptions[$year] = ['title' => $year];
            }

            $data = $investorYearOverviewDataService->execute($investorId, $this->getParam('year'));

            $this->view->listView($data)

                ->setTypes([
                    'object' => ['title' => '', 'width' => 'objectAddressRow', 'group_equal_rows' => true],
                    'project' => ['title' => '', 'width' => 'hidden'],
                    'component_name' => [ 'title' => 'Component', 'width' => 'xlarge ellipsis'],
                    'value_excl' => [  'title' => 'Excl. BTW', 'width' => 'large right rowTotal'],
                    'value_tax' => [  'title' => 'BTW', 'width' => 'medium right rowTotal'],
                    'value_incl' => [  'title' => 'Incl. BTW', 'width' => 'large right rowTotal'],
                    'subtotal_value_excl' => [  'title' => '', 'width' => 'large right subTotal'],
                    'subtotal_value_tax' => [  'title' => '', 'width' => 'medium right subTotal'],
                    'subtotal_value_incl' => [  'title' => '', 'width' => 'large right subTotal'],
                ])
                ->addFormat('object', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    }

                    return $item['object_address'];
                })

                ->addFormat('project', function($value, $item){
                    return $value ? $item['project_name'] : '-';
                })

                ->addFormat(['value_excl', 'value_tax', 'value_incl', 'subtotal_value_excl', 'subtotal_value_tax', 'subtotal_value_incl'], function($value){
                    if(!$value || $value == 0){
                        return '';
                    }

                    return '&euro; ' . new StringFormat($value, 'money');
                })

                ->addTotals(['value_excl', 'value_tax', 'value_incl'])
                ->setFilters([
                    'year' => [
                        'renderSeparately' => true,
                        'title' => 'Jaar',
                        'hideCount' => true,
                        'preApplied' => true,
                        'custom_options' => $yearFilterOptions,
                        'custom_options_only' => true,
                    ],
                    'project' => ['renderSeparately' => true, 'title' => 'Project', 'type' => 'select', 'order_by_title' => 'ASC'],
                    'object' => ['renderSeparately' => true, 'title' => 'Object', 'type' => 'select'],
                ])


                ->render($this);
        }


    }

	public function financialImportAction(){
		$this->disableView();

		/* EDIT THESE! */
		$file = 'Woonpartners Bijz.Expl BV - kwartaalrapportage - v20150708 (Standaard Rapport).xls';
		$project_id = 285;

		/* EDIT IF NEEDED */
		$label_period_row = 10;

		$rows_from = 11;
		$rows_till = 165;

		$account_column = 0;

		$columns_from = 41;
		$columns_till = 54;

		/* NO NEED TO EDIT BELOW */

		$columns = range($columns_from, $columns_till);
		$rows = range($rows_from, $rows_till);

		require_once("PHPExcel.php");
		$account_model = new BrokerFinancialReportAccount();
		$period_model = new BrokerFinancialReportPeriod();
		$value_model = new BrokerFinancialReportValue();


		$excelReader = PHPExcel_IOFactory::createReaderForFile($file);
		$excelReader->setReadDataOnly(false);
		$objPHPExcel = $excelReader->load($file);
		$worksheet = $objPHPExcel->getActiveSheet();
		

		/* PERIODS */
			$periods = array();
			foreach($columns as $column)
				if($period = $worksheet->getCellByColumnAndRow($column, $label_period_row)->getValue())
					$periods[$column] = array(
						'period_name' => $worksheet->getCellByColumnAndRow($column, $label_period_row)->getOldCalculatedValue(),
						'accounts' => array()
					);
		
			foreach($periods as $period_column_id => $period){
				$attributes = array('name' => $period['period_name'], 'year' => substr($period['period_name'], -4));
				if($period_row = $period_model->matchRow($attributes)){
					$period_id = $period_row->id;
				} else {
					$period_id = $period_model->createRow()->setFromArray($attributes)->save();
				}

				$periods[$period_column_id]['period_id'] = $period_id;
			}


		/* ACCOUNTS */
			$accounts = array();
			$has_indented_parent = false;
			foreach($rows as $row)
				if($account = $worksheet->getCellByColumnAndRow($account_column, $row)->getValue()){
					$column_string = $worksheet->getCellByColumnAndRow($account_column, $row)->stringFromColumnIndex();
					$indent = $worksheet->getStyle($column_string . $row)->getAlignment()->getIndent();

					if($indent > 0 && $has_indented_parent !== false){
						$account = $has_indented_parent . ' - ' . $account;
					} elseif($indent > 0){
						$account = $account;
					} else {
						$has_indented_parent = $account;
					}

					$accounts[$row] = $account;
				}
	
			foreach($accounts as $account_row_id => $account){
				$attributes = array('name' => $account);
				if($account_row = $account_model->matchRow($attributes)){
					$account_id = $account_row->id;
				} else {
					$account_row = $account_model->createRow();
					$account_id = $account_row->setFromArray($attributes)->save();
				}

				if($account == 'BM I [%]')
					$account_row->setFromArray(array('is_bm1' => true))->save();

				$accounts[$account_row_id] = $account_id;
			}

		/* initialize empty values array */
		foreach($periods as $period_column_id => $period)
			foreach($accounts as $account_id => $account)
				$periods[$period_column_id]['accounts'][$account_id] = 0;				
		
		/* VALUES */
			foreach ($rows as $row)
				foreach ($columns as $column){
					$value = $worksheet->getCellByColumnAndRow($column, $row)->getValue();
					if($value && !is_null($value)){
						$value = $worksheet->getCellByColumnAndRow($column, $row)->getOldCalculatedValue();

						if(abs($value) > 0)
							$periods[$column]['accounts'][$accounts[$row]] += $value;
					}
				}

		foreach($periods as $period_array)
			foreach($period_array['accounts'] as $account_id => $value){
				$value_attributes = array(
					'project_id' => $project_id,
					'period_id' => $period_array['period_id'],
					'account_id' => $account_id,
				);

				if(!($value_row = $value_model->matchRow($value_attributes)))
					$value_row = $value_model->createRow()->setFromArray($value_attributes);
				
				$value_row->value = $value;
				$value_row->save();
			}
	}

	public function reportMigrationAction(){
		$this->disableView(true);

		$form = new Form($this, 'user/migrate');
		$this->view->form = $form;
		$this->view->headerOnly = true;
		
		foreach($form->userAddress as $field)
			$field->setRequired(false);

		foreach($form->nextTenant as $field)
			$field->setRequired(false);

		if ($this->getRequest()->isPost() && $form->isValid($this->getRequest()->getPost())) {
			$data = $form->getValues();

			$u = new User();
			$u->migration_reported_by_broker = true;

			$u->migrate($this->_getParam('uoid'), $data);

			$this->render('user/migrate/broker_done', null, true);
		} else {
			$this->render('user/migrate/broker_form', null, true);
		}
	}
	
	public function contactAction() {
		if(acl()->hasRole('investor'))
  			$contact = new Form($this, 'broker/contact');
 			$contact->setAction('broker/contact');
 			$this->view->contact = $contact;

			if ($this->getRequest()->isPost() && $contact->isValid($_POST)) {

				$data = $_POST;
				if($data['about']) {
					$icc = InvestorContactCategories::getById($data['about']);
					$data['about'] = $icc['name_' . loginManager::data()->language . ''];

					$to = EmailAccount::getDefault();
					if (is_numeric($icc['receive_mailaccount']) && $icc['receive_mailaccount'] > 0) {
						$to = EmailAccount::getById($icc['receive_mailaccount']);
					}
				} else {
					$to = EmailAccount::getDefault();
				}

                $user = new User();
                $user->user =  loginManager::data()->id;
                $profile = $user->getProfile();

				new EmailOutgoing(array(
                    'from' 			=>  array('email' => $to['address'], 'name' => $to['name']),
                    'to' 			=> 	array('email' => $to['address'], 'name' => $to['name']),
 					'subject'     	=> 	"Vraag/opmerkingen via belegger portal",
					'data' 			=> 	$data,
					'template'    	=> 	'broker/contact.phtml',
				));

                new EmailOutgoing(array(
                    'from' 			=>  array('email' => $to['address'], 'name' => $to['name']),
                    'to' 			=> 	['email' => $profile['general']['email'], 'name' => $profile['general']['rendered_name']],
                    'subject'     	=> 	ucfirst(translate()->_('subject_broker_form')),
                    'data' 			=> 	$data,
                    'template'    	=> 	'broker/contact_client.phtml',
                ));

				$this->render('thanks');

			}
			else {
				$investor = new Investors();
				$investorrow = $investor->getById(loginManager::data()->info['investor_company_id']);
				$user = new User();

                if (is_numeric($investorrow['investor_manager'])){
                    $user->user = $investorrow['investor_manager'];
                    $investor_manager = $user->getProfile();
                    $this->view->investormanager = $investor_manager;
                }
				if (is_numeric($investorrow['investor_manager_account'])){
					$user->user = $investorrow['investor_manager_account'];
					$investor_manager_account = $user->getProfile();
					$this->view->investormanageraccount = $investor_manager_account;
           	 	}


				$this->view->activeMainTab = 'contact';
				$welcometxt =  Settings::get('contacttext_investor_'.loginManager::data()->language.'') ? Settings::get('contacttext_investor_'.loginManager::data()->language.'') :Settings::get('contacttext_investor_nl') ;
				$this->view->text = TextReplacer::replaceText($welcometxt);
		}
	}

	public function tenantinfoAction() {
			$this->view->id = $this->_getParam('id');
			$this->view->type = $this->_getParam('type');
			$this->view->object = $this->_getParam('object');
	}

    public function supportDetailsInvoicesAction()
    {
        $this->getRequest()->setControllerName('support');

        $supportController = new SupportController(
            $this->getRequest(),
            $this->getResponse()
        );
        $supportController->invoicesAction();
    }

	public function vacancyReportAction() {
        $this->view->headLink()->appendStylesheet('media/style/kpi-rapport_portal/dashboard.css');

        $select = db()->select()
            ->from(['icl' => 'investor_combination_link'], false)
            ->joinLeft(['ic' => 'investor_combination'], 'ic.id = icl.investor_combination_id', ['id', 'investor_combination_name' => 'name'])
            ->where('icl.investor_id = ?', loginManager::data()->info['investor_company_id'])
            ->group('ic.id');

        $investor_combinations = db()->fetchAssoc($select);

        if(count($investor_combinations) === 0){
            $this->redirect('broker/vacancy-report-list');
            return;
        }

		$objects = db()->select()
            ->distinct()
			->from(['o' => 'objects'], ['id'])
			->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
			->joinLeft(
				['icl' => 'investor_combination_link'],
				(new \Investors())->getInvestorCombinationLinkJoinCondition(),
				'investor_combination_id'
			)
			->joinLeft(['ic' => 'investor_combination'], 'ic.id = icl.investor_combination_id', false)
			->joinLeft(['p' => 'projects'], 'p.id = og.project', false)
			->where('p.id IS NOT NULL')
			->where('p.sales_status = ?', 'Closed')
            ->where('(og.investor = ? AND o.investoroverride = 0 AND o.investoroverride_combination_id IS NULL) OR icl.investor_id = ? OR o.investoroverride = ?', loginManager::data()->info['investor_company_id'])
			->query()
			->fetchAll();

		$vacancy_data = (new Object())->getVacancy(
			false,
			date('Y') . '-01-01',
			loginManager::data()->info['investor_company_id']
		);

		$no_combination = [[
			'investor_combination_name' => 'Volledig eigendom',
			'total_costs' => 0,
			'total_days' => 0,
		]];

		$vacancy_data_per_combination = $no_combination + $investor_combinations;

		foreach ($vacancy_data as $vacancy_item) {
			if(!$vacancy_item['investor_combination_id']){
				$vacancy_item['investor_combination_id'] = 0;
			}

			if(!isset($vacancy_data_per_combination[$vacancy_item['investor_combination_id']])){
				$vacancy_data_per_combination[$vacancy_item['investor_combination_id']] = [
					'investor_combination_name' => $vacancy_item['investor_combination_name'],
					'total_costs' => 0,
					'total_days' => 0,
				];
			}

			$vacancy_data_per_combination[$vacancy_item['investor_combination_id']]['total_costs'] += (($vacancy_item['year_rent']/365)*$vacancy_item['days']);
			$vacancy_data_per_combination[$vacancy_item['investor_combination_id']]['total_days'] += $vacancy_item['days'];
		}

		foreach ($vacancy_data_per_combination as $vacancy_data_per_combination_key => $vacancy_data_per_combination_item) {
		    $objects_for_combination = array_filter($objects, function($object) use ($vacancy_data_per_combination_key){
		        return $object['investor_combination_id'] == $vacancy_data_per_combination_key;
            });
			$vacancy_data_per_combination[$vacancy_data_per_combination_key]['percentage'] =
                ($vacancy_data_per_combination_item['total_days'] / (count($objects_for_combination) * 365)) * 100;
		}

		$this->view->data = $vacancy_data_per_combination;
	}

	public function vacancyReportListAction() {

        $this->_helper->viewRenderer->setNoRender(true);


        echo $this->view->action(
            'vacancy-report',
            'report',
            null,
            $this->getAllUrlParams() + ['broker' => true]
        );

	}


	public function transactionsReportAction() {
		$this->view->headLink()->appendStylesheet('media/style/kpi-rapport_portal/dashboard.css');

		$select = db()->select()
			->from(['icl' => 'investor_combination_link'], false)
			->joinLeft(['ic' => 'investor_combination'], 'ic.id = icl.investor_combination_id', ['id', 'investor_combination_name' => 'name'])
			->where('icl.investor_id = ?', loginManager::data()->info['investor_company_id'])
			->group('ic.id');

		$investor_combinations = db()->fetchAssoc($select);

		if(count($investor_combinations) === 0){
			$this->redirect('broker/transactions-report-list');
			return;
		}

		$data = (new Transaction)->findUnpayedBroker([
			'investor'=>loginManager::data()->info['investor_company_id'],
			'investor_manage_start_date' => isValidTimeStamp(strtotime(loginManager::data()->info['investor_manage_start_date'])) ? strtotime(loginManager::data()->info['investor_manage_start_date']) : false,
			'filter_project_corporation_id' => Settings::get('broker_page_filter_project_corporation_id')
		]);

		$no_combination = [[
			'investor_combination_name' => 'Volledig eigendom',
			'total_costs' => 0,
			'total_days' => 0,
		]];

		$data_per_combination = $no_combination + $investor_combinations;

		foreach($data as $data_item){

			if(!$data_item['investor_combination_id']){
				$data_item['investor_combination_id'] = 0;
			}

			if(!isset($data_per_combination[$data_item['investor_combination_id']])){
				$data_per_combination[$data_item['investor_combination_id']] = [
					'investor_combination_name' => $data_item['investor_combination_name'],
					'amount' => 0
				];
			}

			$data_per_combination[$data_item['investor_combination_id']]['amount'] += $data_item['unpayed'] / 100;
		}

		$this->view->data = $data_per_combination;
	}

	public function transactionsReportListAction() {

	    $manage_start_date_timestamp = strtotime(loginManager::data()->info['investor_manage_start_date']);

        $params = [
            'investor' => loginManager::data()->info['investor_company_id'],
            'investor_manage_start_date' =>
                isValidTimeStamp($manage_start_date_timestamp) ? $manage_start_date_timestamp : false,
            'filter_project_corporation_id' => Settings::get('broker_page_filter_project_corporation_id')
        ];

        if(!is_null($this->getParam('combination_id'))) {
            $params['filter_investor_combination_id'] = $this->getParam('combination_id');
        }

		$data = (new Transaction)->findUnpayedBroker($params);

		$types = array(
			'name' => array('title' => 'Achternaam', 'width' => 'medium truncate', 'group_equal_rows' => true),
			'rendered_name' => array('title' => 'Huurder', 'width' => 'medium truncate'),
			'address' => array('title' => 'Adres', 'width' => 'medium truncate'),
			'number' => array('title' => '#', 'width' => 'medium truncate'),
			'city' => array('title' => 'Plaats', 'width' => 'medium truncate'),
			'unpayed' => array('title' => 'Achterstand', 'width' => 'medium truncate'),
			'periodvalue' => array('title' => 'Periode', 'width' => 'medium truncate'),

		);

		$filters =  array(
			'project' => array('renderSeparately' => true, 'title' => 'Project', 'order_by_title' => 'ASC'),
			'rendered_name' => array('renderSeparately' => true, 'title' => 'Huurder', 'order_by_title' => 'ASC'),
            'year' => array('renderSeparately' => true, 'title' => 'Jaar', 'order_by_title' => 'DESC'),
		);

		$this->view->ListView($data)

			->setTypes($types)

			->addFormat('unpayed', function($value, $item){
				return $value > 0 ? '€ '. new StringFormat((($value/100)),'money') : '-';
			})

			->addFormat('periodvalue', function($value, $item){
				return Invoice::translatePeroid($item['period'],$value). " ". date('Y', strtotime($item['start']));
			})

			->setFilters($filters)

			->setOptions(array())

			->addTotals(array('unpayed'))

			->render($this);

	}

    public function vidiiDashboardAction()
    {
        $this->appendJqueryFiles($this->view);

        $investorId = loginManager::data()->info['investor_company_id'];

        $investorModel = new \Investors();
        $investorProjects = $investorModel->getProjects($investorId);
        $this->view->projects = array_column($investorProjects, 'name', 'id');
    	
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/age-chart.css');
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/gender-chart.css');
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/language-chart.css');
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/source-chart.css');
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/geo-google-maps.css');
        $this->view->headLink()->appendStylesheet('media/style/vidii_chart/vidii-dashboard.css');
	}

	public function yournawAction() {
		$this->view->activeMainTab = 'naw';

		if (acl()->hasRole('investor')) {
			$user = new User();
			$user->user = loginManager::data()->id;
			$this->view->profile = $user->getProfile();
		}

		$language = loginManager::data()->language;
		$yournawtxt = Settings::get("yourinfotext_investor_{$language}") ?:
			Settings::get('yourinfotext_investor_nl');
		$this->view->text = TextReplacer::replaceText($yournawtxt);

		$yournawUserpassTxt = Settings::get("yourinfotext_userpass_change_investor_{$language}") ?:
			Settings::get('yourinfotext_userpass_change_investor_nl');
		$this->view->userpassText = TextReplacer::replaceText($yournawUserpassTxt);
	}
	
	public function listAction() {
		$this->view->Breadcrumbs()
			->addCrumb("Makelaars")
		;
		$broker = new Broker();
		
		$this->view->brokers = $broker->getBrokers();
	}
	
	public function editAction() {
		$brokerId = $this->_getParam('id');
		
		$broker  = new Broker($brokerId);
		$project = new Project();
		
		$brokerInfo = $broker->getInfo();
		
		$this->view->Breadcrumbs()
			->addCrumb("Makelaars", 'broker/list/')
			->addCrumb("{$brokerInfo['name']} bewerken")
		;
		
		$selected = $broker->getObjectsByProjectAndGroup();
		if ($this->getRequest()->isPost()) {
			$objects = $this->objectsForProjects($this->_getParam('projects'));
		
		} else {
			$projectIds = array();
			foreach ($selected as $projId => $objects) {
				$projectIds[] = $projId;
			}
			$objects = $this->objectsForProjects($projectIds);
		}
		
		$projects = array();
		foreach ($project->getSimpleList() as $pr) {
			$projects[$pr['id']] = $pr;
		}
		
		$objectGroups = array();
		foreach ($projects as $projId => $pr) {
			$objectGroups = $project->getObjectGroups($projId) + $objectGroups;
		}
		
		
		$this->view->objects      = $objects;
		$this->view->selected     = $selected;
		$this->view->projects     = $projects;
		$this->view->objectGroups = $objectGroups;
		$this->view->broker       = $brokerInfo;
	}

	/**
	 * @throws Zend_Form_Exception
	 */
	public function loginDataAction()
	{
		$this->view->activeMainTab = 'naw';
		$loginDataForm = new Form($this, 'broker/login-data');

		if ($this->getRequest()->isPost() && $loginDataForm->isValid($this->getRequest()->getPost())) {
			$post = $this->getRequest()->getPost();

			$currentUserRow = Users::get(loginManager::data()->id);
			if (!$currentUserRow || $post['user_id'] !== $currentUserRow->id || !acl()->hasRole('investor')) {
				$this->redirect('broker/yournaw');
				return;
			}

			$isEmpty = function ($field) use ($post) {
				return empty(trim($post[$field]));
			};

			$hasErrors = false;

			if (!$isEmpty('username')) {
				$currentUserRowWithName = (new Users())->matchRowWithHighestId([
					'username' => $post['username'],
				]);

				if ($currentUserRowWithName && $currentUserRowWithName->id !== $currentUserRow->id) {
					$loginDataForm->username->addError(translate()->_('username_taken'));
					$hasErrors = true;
				} else {
					$currentUserRow->username = $post['username'];
				}
			}

			if (!($isEmpty('password') && $isEmpty('password_confirm'))) {
				if ($post['password'] !== $post['password_confirm']) {
					$loginDataForm->password->addError(translate()->_('passwords_dont_match'));
					$loginDataForm->password_confirm->addError(translate()->_('passwords_dont_match'));
					$hasErrors = true;
				} else {
					$currentUserRow->password = sha1($post['password']);
				}
			}

			if ($hasErrors) {
				$this->view->form = $loginDataForm;
				return;
			}

			$currentUserRow->save();
			$this->redirect('broker/yournaw');
		} else {
			$this->view->activeMainTab = 'naw';

			$loginDataForm->username->setValue(loginManager::data()->info['username']);
			$loginDataForm->user_id->setValue(loginManager::data()->id);
			$this->view->form = $loginDataForm;
		}
	}
	
	protected function objectsForProjects($projectIds) {
		$p = new Project();
		$objects = array();
		foreach ($projectIds as $projId) {
			$objects[$projId] = $p->getObjectsByGroup($projId);
		}
		
		return $objects;
	}
	
	public function bindObjectsAction() {
		$this->disableView();
		
		$brokerId = (int) $this->_getParam('broker');
		$brokerName = $this->_getParam('broker_name');
		
		$broker = new Broker($brokerId);
		
		$objects = array();
		foreach ($this->_getParam('objects') as $objId) {
			$objects[] = (int) $objId;
		}
		
		$broker->bindObjects($objects);
		$broker->updateName($brokerId, $brokerName);
		
		echo "true";
	}

	/**
	 * @param Broker $broker
	 * @param int $page
	 * @return Zend_Paginator
	 * @throws Zend_Paginator_Exception
	 */
	private function getObjectPaginator(Broker $broker, $page) {

		$objects = (array) $broker->getObjects();

		$paginator = Zend_Paginator::factory($objects);
		$paginator->setCacheEnabled(true);
		$paginator->setCurrentPageNumber($page);
		$paginator->setItemCountPerPage(10);

		return $paginator;
	}

	public function getFilters(){
		$allowed_filters = [
			'year' => ['type' => 'int', 'default' => null],
			'project' =>  ['type' => 'int', 'default' => null],
			'address' => ['type' => 'string', 'default' => null],
			'invoice' => ['type' => 'string', 'default' => null],
			'show_rent' => ['type' => 'bool', 'default' => 'true'],
			'show_empty' => ['type' => 'bool', 'default' => 'true']
		];

		$filters = [];
		foreach ($allowed_filters as $filtername => $filter_settings) {
			$value = $this->_getParam($filtername);

			if(null === $value) {
				if ($value = $filter_settings['default']) {
					$filters[$filtername] = $value;
				} else {
					continue;
				}
			}

			if(is_numeric($value) && $filter_settings['type'] === 'int') {
				$filters[$filtername] = $value;
			}

			if($filter_settings['type'] === 'bool' && in_array($value, ['true', 'false'])) {
				/** @noinspection TypeUnsafeComparisonInspection */
				$filters[$filtername] = $value == 'true';
			}

			if($filter_settings['type'] === 'string' && trim($value) !== '') {
				$filters[$filtername] = trim($value);
			}
		}
		
		return $filters;
	}

	public function meterUsageAction (){

        $this->view->activeMainTab = 'meter-usage';

        $model = new Controllers\Broker\MeterUsageList(
            loginManager::data()->info['investor_company_id']
        );
        $data = $model->getList();


        $types = [
            'meternummer' => ['title' => 'Meter#', 'width' => 'medium truncate'],
            'meternaam' => ['title' => 'Meternaam', 'width' => 'medium truncate'],
        	'opnamedatum' => ['title' => 'datum', 'width' => 'medium truncate'],
            'dagen_tussen_stand' => ['title' => 'dagen', 'width' => 'medium '],
            'opnamewaarde' => ['title' => 'waarde' , 'width' => 'large truncate'],
            'verbruik' => ['title' => 'verbruik', 'width' => 'medium'],
            '30_day_moving_average' => ['title' => 'gem. 30 dagen', 'width' => 'medium']
        ];

        $this->view->ListView($data)

            ->setTypes($types)

            ->addFormat('opnamewaarde', function($value){
                return round($value,2);
            })

            ->setFilters([
                'project_name' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Project', 'order_by_value' == 'ASC'],
                'meternaam' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'meter', 'order_by_value' == 'ASC'],
                'meternummer' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'meternummer'],
                'date' => ['type' => 'range', 'renderSeparately' => true, 'title' => 'datum'],
            ])

            ->setOptions([
                'show_title' => false
            ])

            ->render($this);
	}

    public function objectsAction()
    {
        // Set the active tab for the layout at: layouts/navigatie/broker.phtml:
        $this->view->activeMainTab = 'objects';

        $model = new Controllers\Broker\ObjectList(
            loginManager::data()->info['investor_company_id']
        );
        $data = $model->getList();

        $types = [
            'object' => ['title' => ucfirst(translate()->_('address')), 'width' => 'xlarge truncate'],
            'tenant' => ['title' => (Settings::get('modules_vacancy_management') ? 'Beschermer'  : ucfirst(translate()->_('tenant')))  , 'width' => 'xlarge truncate'],
            'amount' => ['title' => ucfirst(translate()->_('rent')) . ' ' . translate()->_('total'), 'width' => 'medium right'],
            'spacer' => ['title' => ' ', 'width' => 'xxxsmall'],
			'rate' => ['title' => ucfirst(translate()->_('per')), 'width' => 'medium'],
            'unpayed' => ['title' => ucfirst(translate()->_('subtracted_unpayed')), 'width' => 'medium right'],
        ];

        if(Settings::get('brokerportal_hide_payment_problems')){
            unset($types['unpayed']);
            unset($types['amount']);
            unset($types['rate']);

        }

        $rate_labels = UserInvoicePreferences::getInvoicePeriodOptions();


        $this->view->ListView($data)

            ->setTypes($types)

            ->addFormat('object', function($value, $item){
				if (Settings::get('brokerportal_hide_tenant_object_details')) {
                    return
                        $item['address'] . ' ' . $item['number'] . ' ' . ($item['build'] ? '('.$item['build'].')' : '') . '<br />' . $item['zipcode'] . '<br />' . $item['city'];
				} else {
                    return
                        '<a href="broker/tenantinfo/id/' . $value . '/type/object/object/' . $value . '">' .
                        $item['address'] . ' ' . $item['number'] . ' ' . ($item['build'] ? '('.$item['build'].')' : '') . '<br />' . $item['zipcode'] . '<br />' . $item['city'] .
                        '</a>';
				}

            })

            ->addFormat('project', function($value, $item){
                return $item['project_name'];
            })

            ->addFormat('state', function($value){
                return $value === 'tenant' ?  translate()->_('inhabited') : translate()->_('empty');
            })

            ->addFormat('tenant', function($value, $item){
                if (!Settings::get('brokerportal_hide_tenant_object_details')) {
                    $tenant_string =
                        '<a href="broker/tenantinfo/id/' . $value . '/type/user/object/' . $item['object'] . '">' .
                        $item['tenant_name'] . '</a><br />' .
                        ucfirst(translate()->_('from')) . ' ' . date('d-m-Y', strtotime($item['from'])) . ' ' .
                        ($item['till'] ? translate()->_('till') . ' ' . date('d-m-Y', strtotime($item['till'])) : '') . '<br /><br />';

                    if ($item['future_uo_id']) {
                        $tenant_string .=
                            '<a href="broker/tenantinfo/id/' . $item['future_tenant'] . '/type/user/object/' . $item['object'] . '">' .
                            $item['future_tenant_name'] . '</a><br />' .
                            ucfirst(translate()->_('per')) . ' ' . date('d-m-Y', strtotime($item['future_from'])) . ' ' .
                            ($item['future_till'] ? translate()->_('till') . ' ' . date('d-m-Y', strtotime($item['future_till'])) : '');
                    }
                } else {
                    $tenant_string =
                        $item['tenant_name'] . '<br />' .
                        ucfirst(translate()->_('from')) . ' ' . date('d-m-Y', strtotime($item['from'])) . ' ' .
                        ($item['till'] ? translate()->_('till') . ' ' . date('d-m-Y', strtotime($item['till'])) : '') . '<br /><br />';

                    if ($item['future_uo_id']) {
                        $tenant_string .=
                            $item['future_tenant_name'] . '<br />' .
                            ucfirst(translate()->_('per')) . ' ' . date('d-m-Y', strtotime($item['future_from'])) . ' ' .
                            ($item['future_till'] ? translate()->_('till') . ' ' . date('d-m-Y', strtotime($item['future_till'])) : '');
                    }
                }

                return $tenant_string;
            })

            ->addFormat('rate', function($value) use ($rate_labels){
                return $rate_labels[$value];
            })


            ->addFormat(['amount', 'unpayed'], 'money')

            ->setFilters([
                'project' => ['type' => 'select', 'renderSeparately' => true, 'title' => 'Project', 'order_by_value' == 'ASC'],
                'object' => ['type' => 'input', 'renderSeparately' => true, 'title' => ucfirst(translate()->_('rental_address')), 'order_by_value' == 'ASC'],
                'state' => ['type' => 'select', 'renderSeparately' => true, 'title' => ucfirst(translate()->_('status'))],
                'tenant' => ['type' => 'input', 'renderSeparately' => true, 'title' => (Settings::get('modules_vacancy_management') ? 'Beschermer'  : ucfirst(translate()->_('tenant')))],

                /*
                'start' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('period'), 'order_by_value' == 'ASC'],
                'title' => ['type' => 'input', 'renderSeparately' => true, 'title' => translate()->_('invoice_subject')],
                'amount' => ['type' => 'range', 'renderSeparately' => true, 'title' => ucfirst(translate()->_('rent'))],
                'identifier' => ['type' => 'input', 'renderSeparately' => true, 'title' => translate()->_('invoicenumber')],
                'amount' => ['type' => 'range', 'renderSeparately' => true, 'title' => translate()->_('total')]
                */
            ])

            ->setOptions([
                'show_title' => false
            ])

            ->addTotals(['amount', 'unpayed'])

            ->render($this);
    }

	public function reportsAction() {
		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'reports';

		$this->_redirect('broker/report-management', []);
	}

	public function projectsForInvestor() {
		$iModel = new Investors();
		$objects = (array) $iModel->getObjectsForInvestor(loginManager::data()->info['investor_company_id']);
		$projects = Objects::getProjects($objects);

		return $projects;
	}

	public function projectsForInvestorByCorp() {
		$corpModel = new Corporations();
		$projModel = new Projects();

		$ics = db()->fetchAll(db()->select()->from(['ic' => 'investor_corporations'], '*')->where('investor = ?', loginManager::data()->info['investor_company_id']));

		$output = [];
		foreach ($ics as $ic) {
			$corp = $corpModel->getById($ic['corporation']);
			$entry = [
				'type'	=> 'corporation',
				'id'	=> $ic['corporation'],
				'name'	=> "<b>".$corp['name']."</b>",
			];
			$projectIds = db()->fetchPairs(db()->select()->from(['p' => 'projects'], ['id'])->where('corporation = ?', $ic['corporation']));

			$entry['projectIds'] = array_keys($projectIds);

			$output[] = $entry;

			foreach ($entry['projectIds'] as $projectId) {
				$project = Projects::get($projectId);
				if(!empty($project) && is_object($project)) {
					$output[] = [
						'type'			=> 'project',
						'id'			=> $projectId,
						'name'			=> $project['name'],
						'projectIds'	=> [$projectId],
					];
				}
			}
		}

		return $output;
	}

	public function docDownloadAction(){
		$this->disableView();

		$id = $this->_getParam('id');

		$row = db()->fetchRow(db()->select()->from('broker_publish_docs')->where('hash = ?', $id));

		if(!$row) return;

		$fileTypes = array (
			'pdf' => 'application/pdf',
			'doc' => 'application/msword',
			'docx' => 'application/msword',
			'xls' => 'application/vnd.ms-excel',
			'ppt' => 'application/vnd.ms-powerpoint',
			'gif' => 'image/gif',
			'png' => 'image/png',
			'jpg' => 'image/jpeg',
			'jpeg' => 'image/jpeg',
		);

		$doc = Main::app()->getDir('document').'broker-publish-docs/' . $row['broker_publish'] . '/' . $row['filename'];

		if (is_readable($doc)) {
			$type = $fileTypes[pathinfo($doc, PATHINFO_EXTENSION)];
			$size = filesize($doc);
			// set headers
			header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
			header("Cache-Control: public");
			header("Content-Description: File Transfer");
			header("Content-Type: {$type}");
			header("Content-Disposition: attachment; filename=" . basename($doc));
			header("Content-Transfer-Encoding: binary");
			header("Content-Length: {$size}");
		
			// Output file data
			echo file_get_contents($doc);
		}
	}

	public function reportManagementAction(){
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		$this->view->introText 				= $publish['text_intro_management'];
		$this->view->transcript 			= $publish['text_transcript'];
		$this->view->publish 				= $publish;
		$this->view->docs = db()->fetchAll(db()->select()->from('broker_publish_docs')->where('broker_publish = ?', $publish['id']));
		
		$this->render('reports/management');
	}

	public function reportBalanceAction(){
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		$this->view->introText 				= $publish['text_intro_balance'];
		$this->render('reports/balance');
	}

	public function reportBalanceListAction(){
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_balance_action');

		if(is_null($this->getParam('project_id')) && count($data['projects']) > 0)
			$this->setParam('project_id', false);

		$values 			= $data['values'];
		$types 				= $data['types'];
		$amount_columns 	= $data['amount_columns'];

		$this->view->listview = $this->view->listView($values, ['url-override' => 'broker/report-balance-list', 'project_id' => $this->getParam('project_id')])

				->setTypes($types)

				->addFormat('account', function($value, $item){
					return ($item['account_indent'] ? "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" : '') . $value . $item['account_id'];
				})

				->addFormat('project_id', function($value, $item){
					return $item['project'];
				})

				->addFormat($amount_columns, function($value, $item){
					if($item['account_subheader'])
						return $value;

					if($value == 0) return '-';

					if(strpos($item['account'], '%') !== false)
						return round($value) . ' %';
					else
						return number_format(round($value), 0, ',', '.');
				})

				->setFilters([
					'project_id' => [
						'title' => 'Project',
						'renderSeparately' => true,
						'show_all_disabled' => true,
						'hideCount' => true,
						'sorted' => true
					],
				])

				->setOptions([
					'item_title' => 'Adres',
					'show_title' => false,
					'paginator' => false,
					'show_view_template_buttons' => false
				])

				->render($this)
			;
	}

	public function reportTechnicalAction() {
		$this->view->headScriptHashed()->appendFile('media/javascript/chart/chart.js')
				->appendFile('media/javascript/chart/chart-line-second-y-axis.js');

		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'reports';

		$this->view->projects = BrokerPublish::projectsByCorp([$publish->id]);

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_technical_table_data');

		$this->view->introText 			= $publish['text_intro_technical'];
		$this->view->years				= $data['years'];
		$this->view->sourceBalances		= $data['sourceBalances'];
		$this->view->fuelMixes			= $data['fuelMixes'];
		$this->view->systemEfficiencies = $data['systemEfficiencies'];

		$this->render('reports/technical');
	}

	public function reportServiceAction() {
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'reports/service';

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_service_action');

		$this->view->introText 				= $publish['text_intro_service'];
		$this->view->publishYear 			= $data['publishYear'];
		$this->view->empty_rate 			= $data['empty_rate'];
		$this->view->dispute_count 			= !empty($data['dispute_count'])? $data['dispute_count']: 0;
		$this->view->complaint_count 		= $data['complaint_count'];
		$this->view->defects_per_project 	= $data['defects_per_project'];
		$this->view->costs_per_defect 		= $data['costs_per_defect'];
		$this->view->defect_counts 			= $data['defect_counts'];
		$this->view->ratings 				= $data['ratings'];
		$this->view->debtor_payment 		= $data['debtor_payment'];

		$projects = BrokerPublish::projectsByCorp([$publish->id]);
		unset($projects[0]);
		$this->view->project_labels = array_combine(array_column($projects, 'id'), array_column($projects, 'name'));

		$this->render('reports/service');
	}

  	public function reportTechnicalChartNoneAction() {
		$this->_helper->layout->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);
		echo "<h2>Geen grafiek beschikbaar</h2>";
	}
	
 	public function reportTechnicalChartFuelMixAction() {
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);

		$projectIds = $this->_getParam('projectIds');
		$ident = implode('-', $projectIds);

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_technical_chart_fuel_mix');
		$data = $data[$ident];

		$this->view->label		= $data['label'];
		$this->view->graph_min 	= $data['graph_min'];
		$this->view->graph_max 	= $data['graph_max'];
		$this->view->fuelMixes 	= $data['fuelMixes'];
		$this->view->years		= $data['years'];

		$this->render('reports/technical-chart/fuel-mix');
 	}

	public function reportTechnicalChartSourceBalanceAction() {
		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'reports';

		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);
		$this->view->projects = BrokerPublish::projectsByCorp([$publish->id]);

		$projectIds = $this->_getParam('projectIds');
		$ident = implode('-', $projectIds);

		if(is_array($projectIds) && count($projectIds) > 1)
			dieWithStatuscode();

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_technical_chart_source_balance');
		$data = $data[$ident];

		$this->view->label =			$data['label'];
		$this->view->graphTypes =		$data['graphTypes'];
		$this->view->graphData =		$data['graphData'];
		$this->view->graph_max =		$data['graph_max'];
		$this->view->graph_min =		$data['graph_min'];

		$this->render('reports/technical-chart/source-balance');
	}

	public function reportTechnicalChartSystemEfficiencyAction() {
		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);
		$this->view->projects = BrokerPublish::projectsByCorp([$publish->id]);

		$projectIds = $this->_getParam('projectIds');
		$ident = implode('-', $projectIds);

		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_technical_chart_system_efficiency');
		$data = $data[$ident];

		$this->view->label =		$data['label'];
		$this->view->graphTypes =	$data['graphTypes'];
		$this->view->graphData =	$data['graphData'];
		$this->view->graph_max =	$data['graph_max'];
		$this->view->graph_min =	$data['graph_min'];

		$this->render('reports/technical-chart/system-efficiency');
	}

 	public function reportFinancialAction() {
		$this->view->activeMainTab = 'reports/financial';

		$publish = BrokerPublish::get($_SESSION['selected_broker_publish']);
		$dataSource = new BrokerPublishData($publish->id);
		$data = $dataSource->call('report_financial_action');

		$this->view->introText						= $publish['text_intro_finance'];
		$this->view->corporation_label				= $data['corporation_label'];
		$this->view->data_project_report 			= $data['data_project_report'];
		$this->view->project_labels 				= $data['project_labels'];
		$this->view->bm1_value 						= $data['bm1_value'];
		$this->view->bm1_total 						= $data['bm1_total'];
		$this->view->nm_total 						= $data['nm_total'];
		$this->view->regular_maintenance 			= $data['regular_maintenance'];
		$this->view->bind_maintenance 				= $data['bind_maintenance'];
		$this->view->maintenance_percentage 		= $data['maintenance_percentage'];
		$this->view->objects 						= $data['objects'];
		$this->view->income_ratios_piechart_array 	= $data['income_ratios_piechart_array'];
		$this->view->income_ratios 					= $data['income_ratios'];
		$this->view->data_project_prospect_report 	= $data['data_project_prospect_report'];
	    $this->view->publish                        = $publish;

		$this->render('reports/financial');
	}

	public function reportIpAddressAction() {
		echo $_SERVER['REMOTE_ADDR'];
		die();
	}
 
	public function invoicesAction() {

		if (Settings::get('broker_invoices_maintenance')) {
			$this->render('invoices-maintenance');
		} else {

            ini_set('max_input_time', 10000);
            ini_set('memory_limit', '8000M');
            ini_set('max_execution_time', 0);

            // Set the active tab for the layout at: layouts/navigatie/broker.phtml:
            $this->view->activeMainTab = 'invoices';

            $broker = $this->getModel();
            $broker->setFilters(
                $this->getFilters()
            );

            $data = $broker->getInvoicesForInvestor();

            $types = [
                'date' => ['title' => translate()->_('date'), 'width' => 'medium truncate'],
                'title' => ['title' => translate()->_('invoice_subject'), 'width' => 'xlarge truncate'],
                'identifier' => ['title' => translate()->_('invoicenumber'), 'width' => 'medium truncate'],
                'amount' => ['title' => translate()->_('total'), 'width' => 'medium right'],
                'payed' => ['title' => translate()->_('payed'), 'width' => 'medium right'],
                'unpayed' => ['title' => translate()->_('subtracted_unpayed'), 'width' => 'medium right'],
            ];

            if(Settings::get('brokerportal_hide_payment_problems')){
                unset($types['payed'], $types['unpayed']);
            }


            $this->view->ListView($data)

                ->setTypes($types)

                ->addFormat('date', 'date')

                ->addFormat('identifier', function($value, $item){
                    return '<a href="invoice/export/id/' . $item['id'] . '/">' .  $item['identifier'] . '</a>';
                })

                ->addFormat(['amount', 'payed', 'unpayed'], 'money')

                ->setFilters([
                    'title' => ['type' => 'input', 'renderSeparately' => true, 'title' => translate()->_('invoice_subject')],
                    'identifier' => ['type' => 'input', 'renderSeparately' => true, 'title' => translate()->_('invoicenumber')],
                    'amount' => ['type' => 'range', 'renderSeparately' => true, 'title' => translate()->_('total')]
                ])

                ->setOptions([
                    'show_title' => false
                ])

                ->addTotals(['amount','payed', 'unpayed'])

                ->render($this);

		}




	}

	public function rentReckoningAction(){

			$this->investor_id = loginManager::data()->info['investor_company_id'];

        $this->view->Breadcrumbs()->addCrumb('Huurafrekeningen');
        $params['view_template'] = 'support';

        $list_model = new Controllers_Investor_Provision_List();
        $list_model->setInvestorId($this->investor_id);
        $data = $list_model->getList();



        $this->view->list = $this->view->listView($data, ['investor_id' => $this->investor_id, 'view_template' => $this->_getParam('view_template')])

                ->setTypes([
                    'period' => ['title' => 'Periode', 'width' => 'medium truncate'],
                    'date' => ['title' => 'Datum', 'width' => 'medium'],
                    'amount' => ['title' => 'Bedrag', 'width' => 'medium'],
                    'spacer' => ['title' => ' ', 'width' => 'xxsmall'],
                    'closed' => ['title' => 'Doorgestort', 'width' => 'medium'],
                    'invoice' => ['title' => 'Download', 'width' => 'medium']
                ])

                ->addFormat('date', 'date')

                ->addFormat('invoice', function($value, $row){
                    if($row['invoice'] > 0)
                        return
                            '<a target="_blank" href="invoice/export/id/' . $row['invoice'] . '">' .
                            '<span class="fa fa-file-pdf-o" aria-hidden="true"></span>Klik hier</a>';
                    else
                        return '-';
                })

                ->addFormat('period', function ($value, $row) {
                    return	ucfirst(strftime('%B %Y', strtotime($value)));
                })

                ->addFormat('amount', 'money_from_int_db')

                ->addFormat('closed', function($value, $item){
                    if(is_null($value)) return '-';

                    $value = $value  === true || $value === 'true' || $value === '1' || $item['unpayed'] <= 0;

                    return $value ? 'Ja' : 'Nee';
                })

                ->setOptions([
                    'item_title' => 'Afrekening',
                    'paginator_items_per_page' => 5,
                    'show_title' => true,
                ])


                ->render($this);
        }

	protected function getChosenProjectIds($projects) {
		$projectsParam = $this->_getParam('projects');
		
		if ($projectsParam) {
			return explode(',', $projectsParam);
			
		} else {
			return $projectsParam === NULL ? array_keys($projects) : false;
		}
	}
	
	protected function getChosenGroupIds($groups) {
		$groupsParam = $this->_getParam('groups');
		
		if ($groupsParam) {
			return explode(',', $groupsParam);
		
		} else {
			return $groupsParam === NULL ? $this->getGroupIds($groups) : false;
		}	
	}
	
	protected function getGroupIds($objGroupsByProj) {
		$groupIds = array();
		foreach ($objGroupsByProj as $projId => $groups) {
			foreach ($groups as $group) {
				$groupIds[] = $group['id'];
			}
		}
		
		return $groupIds;
	}

	public function documentsAction() {

		if (Settings::get('broker_documents_maintenance')) {
			$this->render('documents-maintenance');
		} else {
            $welcometxt = Settings::get('documentstext_investor_' . loginManager::data()->language . '') ? Settings::get('documentstext_investor_' . loginManager::data()->language . '') : Settings::get('documentstext_investor_nl');
            $this->view->text = TextReplacer::replaceText($welcometxt);
        }
    }

    public function projectDocumentsListAction() {
        // Set the active tab for the layout at: layouts/navigatie/broker.phtml:
        $this->view->activeMainTab = 'documents';
        include_once "application/views/helpers/FileIcon.php";
        $document = new Document();

        $docsParams = array();
        $docsParams['language'] = loginManager::data()->language;

        $projects = Investors::getProjects(loginManager::data()->info['investor_company_id']);
        $projectids = array_column($projects,'id');
        $objectgroupids = (new Investors)->getObjectGroupsForInvestor(loginManager::data()->info['investor_company_id']);
		$objectids = (new Investors)->getObjectsForInvestor(loginManager::data()->info['investor_company_id']);

        $data           = $document->projectBrokerList($projectids, $objectgroupids, $objectids, $docsParams);

        $types = [
            'year' => ['title' => translate()->_('year'), 'width' => 'medium truncate'],
            'date' => ['title' => translate()->_('date'), 'width' => 'medium truncate'],
            'title' => ['title' => translate()->_('title'), 'width' => 'xlarge truncate'],
            'categoryname' => ['title' => translate()->_('category'), 'width' => 'medium right'],
            'documenttitle' => ['title' => translate()->_('sub_category'), 'width' => 'medium right']

        ];

        $this->view->listView($data, ['url-override' => 'broker/project-documents-list'])

            ->setTypes($types)

            ->addFormat('date', function($value, $item){
                return date('d-m-Y', strtotime($item['date']));
            })

            ->addFormat('title', function($value, $item){
                return '<a href="document/download/id/' . $item['id'] . '/version/' . $item['version_id'] . '" target="_blank">
						<img src='.Zend_View_Helper_FileIcon::fileIcon($item['document']).' /> ' . $value . '
						</a>';
            })

            ->setFilters([
                'project_name' => ['type' => 'select', 'renderSeparately' => true, 'title' => ucfirst(translate()->_('project')), 'order_by_value' == 'ASC'],
                'object_name' => ['type' => 'select', 'renderSeparately' => true, 'title' => ucfirst(translate()->_('object')), 'order_by_value' == 'ASC'],
                'year' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('year'), 'order_by_value' == 'ASC'],
                'categoryname' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('category'), 'order_by_value' == 'ASC'],
                'documenttitle' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('sub_category'), 'order_by_value' == 'ASC'],
                'title' => ['type' => 'input',  'renderSeparately' => true,  'title' => translate()->_('title')],

            ])
            ->setOptions([
                'show_title' => false,
                'item_title' => 'projectdocument'
            ])

            ->render($this);

    }

	public function personalDocumentsListAction() {
		// Set the active tab for the layout at: layouts/navigatie/broker.phtml:
		$this->view->activeMainTab = 'documents';
        include_once "application/views/helpers/FileIcon.php";

        $document = new Document();

        $docsParams = array();
        $docsParams['language'] = loginManager::data()->language;

		$data           = $document->getpersonalBrokerList(loginManager::data()->info['investor_company_id'], 'investor', $docsParams, true, loginManager::data()->info['commercial']);
         $types = [
            'year' => ['title' => translate()->_('year'), 'width' => 'medium truncate'],
            'from_date' => ['title' => translate()->_('date'), 'width' => 'medium truncate'],
            'title' => ['title' => translate()->_('title'), 'width' => 'xlarge truncate'],
            'categoryname' => ['title' => translate()->_('category'), 'width' => 'medium right'],
            'documenttitle' => ['title' => translate()->_('sub_category'), 'width' => 'medium right']
        ];

        $this->view->listView($data, ['url-override' => 'broker/personal-documents-list'])

            ->setTypes($types)

			->addFormat('date', function($value, $item){
                return date('d-m-Y', strtotime($item['date']));
            })

            ->addFormat('title', function($value, $item){
                return '<a href="document/download/id/' . $item['id'] . '/version/' . $item['version_id'] . '" target="_blank">
						<img src='.Zend_View_Helper_FileIcon::fileIcon($item['document']).' /> ' . $value . '
						</a>';
            })

            ->setFilters([
                'year' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('year'), 'order_by_value' == 'ASC'],
                'categoryname' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('category'), 'order_by_value' == 'ASC'],
                'documenttitle' => ['type' => 'select', 'renderSeparately' => true, 'title' => translate()->_('sub_category'), 'order_by_value' == 'ASC'],
                'title' => ['type' => 'input',  'renderSeparately' => true,  'title' => translate()->_('title')],

            ])
            ->setOptions([
                'show_title' => false
            ])

            ->render($this);

	}

	private function removeNonInvestorTypeDocuments($supportDocuments) {
		foreach ($supportDocuments as $catId => $category) {
			foreach ($category as $id => $supportDocument) {
				if ($supportDocument['type'] != 'investor') {
					unset($category[ $id ]);
				}
			}

			if (count($category) <= 0) {
				unset($supportDocuments[ $catId ]);
			}
		}

		return $supportDocuments;
	}

	public function emailDocumentsAction() {
		$this->_helper->layout()->setLayout('popup');
		
		$broker = $this->getModel();
		$document = new Document();
		
		$projects = $broker->getProjectsById();
		$groups   = $broker->getObjGroupsByProject();
	
		$chosenProjects = $this->getChosenProjectIds($projects);
		$chosenGroups   = $this->getChosenGroupIds($groups);
		
		$docsByCategory = $broker->getDocumentsByCategory($chosenProjects, $chosenGroups);
		$msg = false;
		if ($this->getRequest()->isPost()) {
			$documents = isset($_POST['documents'])     ? $_POST['documents']     : array();
			$email     = isset($_POST['email-address']) ? $_POST['email-address'] : false;
			
			if (!$documents) {
				$msg = "Geen documenten geselecteerd";
			}
			
			$validator = new Zend_Validate_EmailAddress();
			if (!$email || !$validator->isValid($email)) {
				if ($email) {
					$msg = "'{$email}' is geen geldig e-mail adres";
				} else {
					$msg = "Vul A.U.B. een e-mail adres in";
				}
			}
			
			if (!$msg) {
				$attachments = array();
				foreach ($documents as $docId) {
					$doc = $document->getCurrentVersion($docId);
					$attachments[] = array(
						'file' => $doc['document'],
					);
				}
				
				new EmailOutgoing(array(
					'to'          => array('email' => $email),
					'subject'     => "Informatie Vaanster Energie",
					'attachments' => $attachments,
					'template'    => 'broker/send_documents.phtml',
				));
				
				$msg = "Documenten verstuurd naar: {$email}";
				$email = "";
			}
			
			$selected = $documents;
		} else {
			$email = "";
			
			$selected = array();
			foreach ($docsByCategory as $catId => $documents) {
				foreach ($documents as $doc) {
					$selected[] = $doc['document_id'];
				}
			}
		}
		
		$this->view->docsByCategory = $docsByCategory;
		$this->view->categoryTitles = $document->getTypes(false, true);
		$this->view->message        = $msg;
		$this->view->email          = $email;
		$this->view->selected       = $selected;
	}
	
	public function sendLoginEmailAction($inputVals = array(), $message = false) {
		$this->_helper->layout()->setLayout('popup');
		
		$objectId = $this->_getParam('object');
		
		$object = new Object();
		$details = $object->getDetails($objectId);
		
		$this->view->inputVals = $inputVals;
		$this->view->message   = $message;
		$this->view->objectId  = $objectId;
		$this->view->address   = $details['address'];
		
		$this->render('send-login-email');
	}
	
	public function sendLoginEmailSubmitAction() {
		$this->_helper->layout()->setLayout('popup');
		
		$objectId = $this->_getParam('object');
		
		$projectModel = new Project();
		
		$userTable       = new Users();
		$userObjectTable = new Objectusers();
		$addrTable       = new Address();
		
		$email = $_POST['email'];
		
		$validator = new Zend_Validate_EmailAddress();
		if (!$validator->isValid($email)) {
			$msg = "'{$email}' is geen geldig e-mail adres";
			$this->sendLoginEmailAction(array('email' => $email), $msg);
			return;
		}
		
		$object = $projectModel->getUniqueCode($objectId);
		$forthird = $projectModel->isForthirdProject($objectId);
		$c = new Corporation();
		$corp = $c->getCorporationFromProject($object['project_id']);
	
		new EmailNotification(array(
			'type'       => 'notify_account_code',
			'object_id'  => $objectId,
			'identifier' => $object['code'],
		), array(
			'to'       => array('email' => $email),
			'subject'  => "Digitaal ondertekenen leveringsovereenkomst en persoonlijke code",
			'object'   => $object,
			'corp'   => $corp,
			'forthird'   => $forthird,
		));
		
		$this->render('send-login-email-submit');
	}
	
	public function sendLoginLetterAction() {
		$this->_helper->layout()->setLayout('popup');
		
		$objectId = $this->_getParam('object');
		
		$object = new Object();
		$details = $object->getDetails($objectId);
		
		$this->view->objectId = $objectId;
		$this->view->address  = $details['address'];
	}
	
	public function generateLoginLetterAction() {
		$this->_helper->layout->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);
		
		$projModel = new Project();
		
		$objectId = $this->_getParam('object');
		$projModel->exportCode($objectId, 'new', 'codes');
	}
	
	public function editAcceptanceInfoAction() {
		$this->_helper->layout()->setLayout('popup');
		
		$objectId = $this->_getParam('object');
		
		$broModel  = new Broker(false, loginManager::data()->id);
		$objModel  = new Object();
		$consModel = new Consumption();
		
		if ($this->getRequest()->isPost()) {
			
			if (isset($_POST['date']) && $rawDate = $_POST['date']) {
				$date = implode('-', array_reverse(explode('/', $rawDate)));
				
				$objModel->setDeliveryDate($objectId, $acceptanceDate);
				
				if (isset($_POST['meter_value'])) {
					//	var_dump($_POST);
				
					foreach ($_POST['meter_value'] as $meterId => $meterValue) {
						
						if ($meterValue)
							$consModel->add($meterId, $objectId, $date, $meterValue);
					}
				}
			}
		}
		
		//$acceptanceDate = $objModel->objectStatus($objectId, 'true');
  		//$acceptanceDate = $broModel->getAcceptanceDate($objectId);
		// quick and dirty fix not time to do it other way
		$this->view->acceptanceDate = $this->_getParam('date');
		
		//$this->view->acceptanceDate = implode('/', array_reverse(explode('-', $acceptanceDate)));
		$this->view->meters         = $objModel->getMeters($objectId);
		$this->view->consumption    = $consModel->fetchByObject($objectId, $acceptanceDate);
	}
	
	public function editMemosAction() {
		$objectId = (int) $this->_getParam('object');
		$this->_helper->layout()->setLayout('popup');

		$broker = $this->getModel();

		if ($this->getRequest()->isPost() && $broker->hasObject($objectId)) {
			$broker->addMemo($objectId, $_POST['title'], $_POST['body']);
		}
		
		$this->view->memos = $broker->getMemosForObject($objectId);
	}
	
	public function deleteMemoAction() {
		$memoId = (int) $this->_getParam('id');
		
		$broker = new Broker(false, loginManager::data()->id);
		$memo = $broker->getMemo($memoId);
		
		$broker->deleteMemo($memoId);
		
		$this->_redirect('broker/edit-memos', array('object' => $memo['object_id']));
	}
}

?>
