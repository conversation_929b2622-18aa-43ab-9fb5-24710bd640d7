<?

	class PricelistsController extends GlobalController {

		public function init(){
			$this->view->Breadcrumbs()
				->addCrumb('Prijslijsten', 'pricelists');

			return parent::init();
		}

		public function indexAction(){

			$this->view->modelListView('Pricelists')

				->setTypes(array('name', 'date'))

				->addFormat('date', 'date')

				->setOptions(array(
					'item_title' => 'Prijslijst',
					'disabled_buttons_test' => function($row, $button){
						return $button == 'delete' && $row['is_applied'];
					}
				))



				->setFilters([
					'date' => ['type' => 'date_range']
				])

				->addButtons(array(
					'add' => 'Toevoegen',
					'details' => 'Details',
					'delete' => 'Verwijderen'
				),
				array(
					'id' => 'id',
					'redirect' => 'true'
				))

				->render($this);			
		}

		public function detailsAction(){
			$this->view->Breadcrumbs()->addCrumb('Prijslijst details');

			$id = $this->view->id = $this->_getParam('id');

			$model = new Pricelists();

			$this->view->pricelist = $model->getById($id);
			$this->view->is_applied = $model->isApplied($id);

		}			

		public function detailsListAction() {
			$model = new Pricelists();
			$id = $this->_getParam('id');

			$this->view->modelListView('PricelistsRows', ['pricelist' => $id, 'url-override' => 'pricelists/details-list',])
	
				->setTypes(array('component', 'unit', 'object_type', 'price', 'price_discount', 'spacer'))

				->addFormat('component', function($id){ 
					$model = new Components();

					if($id)
						$row = $model->getById($id);

					return $row ? $row->name : '-';
				})

				->addFormat('unit', function($value){
					$pr_model = new PricelistsRows();
					return $pr_model->unitLabel($value);
				})

				->addFormat('object_type', function($value){
					$ot_model = new ObjectType();
					if($value > 0)
						if($ot_row = $ot_model->getById($value))
							return $ot_row->name;

					return '-';
				})	

				->addFormat(array('price', 'price_discount'), 'money')

				->addTotals(['price', 'price_discount']) 

				->setFilters([
					'component' => [], 'unit' => [], 'object_type' => []
				]) 

				->addButtons(array(
					'edit' => 'Bewerken',
					'delete' => 'Verwijderen',
					'add' => 'Toevoegen'
				), array(
					'id' => 'id',
					'pricelist' => $id
				))

				->setOptions(array(
					'item_title' => 'Prijsregel',
					'editAction' => 'row-edit',
					'deleteAction' => 'row-delete',
					'show_title' => false,
					'disabled_buttons_test' => function($row, $button) use ($model, $id) {
						return $button == 'delete' && $model->isApplied($id);
					}
				))

				->render($this);
		}

		public function editAction(){
			$this->view->Breadcrumbs()->addCrumb('Bewerken');

			$this->id = $this->_getParam('id');

			$this->view->form = $this->view->EditView($this, 'Pricelists', ['id' => $this->_getParam('id')])->render($this);
		}	

		public function rowEditAction(){
			$id = $this->_getParam('id');
			$pricelist = $this->_getParam('pricelist');

			$this->view->Breadcrumbs()
				->addCrumb('Details', 'pricelists/details/id/' . $pricelist . '/')
				->addCrumb('Prijsregels ' . ($id > 0 ? 'bewerken' : 'toevoegen'));


			$this->view->form = $this->view->EditView($this, 'PricelistsRows',
				['id' => $id, 'pricelist' => $pricelist]
			)->render($this);
		}	

		public function rowDeleteAction(){
			$this->disableView();

			if(!$this->_getParam('id')) return;

			$pr_model = new PricelistsRows();
			$pr_model->delete('id = ' . $this->_getParam('id'));

			$this->getHelper('Redirector')->gotoUrl($_SERVER['HTTP_REFERER']);
		}

		public function deleteAction(){
			$this->disableView();

			if(!$this->_getParam('id')) return;

			$p_model = new Pricelists();
			$p_model->delete('id = ' . $this->_getParam('id'));

			$pr_model = new PricelistsRows();
			$pr_model->delete('pricelist = ' . $this->_getParam('id'));

			$this->getHelper('Redirector')->gotoUrl($_SERVER['HTTP_REFERER']);
		}
	}
