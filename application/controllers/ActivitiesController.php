<?php

class ActivitiesController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb("Herhalende activiteiten", 'activities/list');
    }

    public function testDayMaskAction()
    {
        $this->disableView();

        $act = new Activities();
        $mask = Activities::MON | Activities::WED | Activities::THR | Activities::FRI;
        echo "{$mask}<br />";
        $dayNums = $act->getDayNums($mask);
        foreach ($dayNums as $dayNum) {
            switch ($dayNum) {
                case 1:
                    echo 'MON';
                    break;
                case 2:
                    echo 'TUE';
                    break;
                case 3:
                    echo 'WED';
                    break;
                case 4:
                    echo 'THU';
                    break;
                case 5:
                    echo 'FRI';
                    break;
                case 6:
                    echo 'SAT';
                    break;
                case 7:
                    echo 'SUN';
                    break;
            }

            echo '<br />';
        }

        echo $act->getDaysMask($dayNums) . '<br />';
    }

    public function testAction()
    {
        $this->disableView();
        $actModel = new Activities();
        echo '<pre>';
        var_dump($actModel->getList());
        echo '</pre>';
    }

    public function listAction()
    {
        $actModel = new Activities();
        $this->view->Breadcrumbs()
            ->addCrumb('Herhalend schema overzicht', '');

        $data = $actModel->fetchList($this->_getParam('type'));

        $this->view->listView($data)

            ->setTypes([
                'title' => [
                    'title' => 'Omschrijving',
                    'width' => 'xxxlarge ',
                ],
                'interval' => [
                    'title' => 'Interval',
                    'width' => 'xxxlarge ',
                ],
                'shortname' => [
                    'title' => 'Medewerker',
                    'width' => 'large',
                ],
            ])

            ->addFormat('interval', function ($value, $item) {
                return Activities::getDescription($item);
            })

            ->addFormat('activity_type', function ($value) {
                $types = Activities::getLabels('nl');
                return $types[$value];
            })

            ->addFormat('start_date', function($value, $item){
                return intval($item['start_date']) > 0?
                    date('d-m-Y', strtotime($item['start_date'])): '-';
            })

            ->addFormat('end_date', function($value, $item){
                return intval($item['end_date']) > 0?
                    date('d-m-Y', strtotime($item['end_date'])): '-';
            })
            
            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
                'delete' => 'Verwijderen',
            ],
                [
                    'id' => 'id',
                ])

            ->setFilters([
                'activity_type' => [
                    'title' => 'Type schema',
                    'renderSeparately' => true,
                    'order_by_value' => 'ASC',
                ]
            ])
            
            ->render($this);

    }

    public function editAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Bewerken', 'activities/edit');
        $actModel = new Activities();

        if ($id = $this->_getParam('id')) {
            $activity = $actModel->fetchRowById($id);
        } else {
            $activity = $actModel->createRow();
        }

        $isValidPost = true;
        if ($_POST['interval-type'] === 'weekly' && $_POST['days'] === null) {
            $this->view->hasMissingWeekDay = true;
            $isValidPost = false;
        }

        if ($this->getRequest()->isPost() && $isValidPost) {
            $intervalType = $_POST['interval-type'];

            $daysMask = new Zend_Db_Expr('NULL');
            $type = new Zend_Db_Expr('NULL');
            $dayOfMonth = new Zend_Db_Expr('NULL');
            $dayOfWeek = new Zend_Db_Expr('NULL');
            $nthWeekDayOfMonth = new Zend_Db_Expr('NULL');
            $month = new Zend_Db_Expr('NULL');

            $title = $_POST['title'];
            $activity_type = $_POST['activity_type'];
            $description = $_POST['description'];
            $email_notify = $_POST['email_notify'];
            $notification_additional_email = $_POST['notification_additional_email'];
            $userId = $_POST['user'];

            switch ($intervalType) {
                case 'weekly':
                    $interval = $_POST['interval-weekly'];
                    $daysMask = $actModel->getDaysMask($_POST['days']);
                    break;
                case 'monthly':
                    $type = $_POST['pattern-type-monthly'];
                    if ($type === 'day-of-month') {
                        $dayOfMonth = $_POST['day-of-month-monthly'];
                        $interval = $_POST['interval-monthly-day-of-month'];
                    } else {
                        $nthWeekDayOfMonth = $_POST['day-number-monthly'];
                        $dayOfWeek = $_POST['week-day-monthly'];
                        $interval = $_POST['interval-monthly-week-day'];
                    }
                    break;
                case 'yearly':
                    $interval = 1;
                    $type = $_POST['pattern-type-yearly'];
                    if ($type === 'day-of-month') {
                        $month = $_POST['month-day-of-month'];
                        $dayOfMonth = $_POST['day-of-month-yearly'];
                    } else {
                        $month = $_POST['month-week-day'];
                        $nthWeekDayOfMonth = $_POST['day-number-yearly'];
                        $dayOfWeek = $_POST['week-day-yearly'];
                    }
                    break;
            }

            $startDate = $this->reverseDate($_POST['start-date']);

            $endDate = new Zend_Db_Expr('NULL');
            $endTimes = new Zend_Db_Expr('NULL');
            $endType = $_POST['end-type'];
            switch ($endType) {
                case 'times':
                    $endTimes = $_POST['end-times'];
                    break;
                case 'date':
                    $endDate = $this->reverseDate($_POST['end-date']);
                    break;
            }

            $activity->title = $title;
            $activity->activity_type = $activity_type;

            $activity->description = $description;
            $activity->email_notify = $email_notify;
            $activity->notification_additional_email = $notification_additional_email;
            $activity->user_id = $userId;

            $activity->interval_type = $intervalType;
            $activity->interval = $interval;
            $activity->week_days_mask = $daysMask;
            $activity->day_of_month = $dayOfMonth;
            $activity->day_of_week = $dayOfWeek;
            $activity->nth_week_day_of_month = $nthWeekDayOfMonth;
            $activity->month = $month;
            $activity->start_date = $startDate;
            $activity->end_date = $endDate;
            $activity->end_times = $endTimes;

            if (is_numeric($endTimes)) {
                $actModel->setEndDate($activity);
            }
            
            $id = $activity->save();
            startWorker('check-activities/id/'. $id , 'cronjob', 'direct');


            $this->_redirect('activities/list', array('user' => $userId , 'type' => $this->_getParam('type')));
        }

        $userInfo = Zend_Auth::getInstance()->getIdentity();
        $loggedInUserId = $userInfo->id;

        if ($activity->end_times) {
            $endType = 'times';
        } else {
            if ($activity->end_date) {
                $endType = 'date';
            } else {
                $endType = 'none';
            }
        }

        $eu = new EmailUser('to', array('id' => loginManager::data()->id));
        $this->view->user_email = $eu->params['email'];

        $this->view->title = $activity['title'];
        $this->view->activity_type = $activity['activity_type'] ? : $this->_getParam('type') ;
        $this->view->description = $activity['description'];
        $this->view->email_notify = $activity['email_notify'];
        $this->view->notification_additional_email = $activity['notification_additional_email'] ? $activity['notification_additional_email'] : false;
        $this->view->userId = $this->trueVal($activity->user_id, $loggedInUserId);

        $this->view->days = $actModel->getDayNums($activity->week_days_mask);
        $this->view->intervalType = $this->trueVal($activity->interval_type, 'weekly');
        $this->view->interval = $this->trueVal($activity->interval, 1);
        $this->view->patternType = $activity->day_of_week ? 'week-day' : 'day-of-month';
        $this->view->dayOfMonth = $this->trueVal($activity->day_of_month, 1);
        $this->view->dayOfWeek = $this->trueVal($activity->day_of_week, 1);
        $this->view->month = $this->trueVal($activity->month, 1);
        $this->view->nthWeekDayOfMonth = $this->trueVal($activity->nth_week_day_of_month, 1);

        $this->view->startDate = $this->reverseDate($activity->start_date);
        $this->view->endType = $endType;
        $this->view->endDate = $this->reverseDate($activity->end_date);
        $this->view->endTimes = $this->trueVal($activity->end_times, 1);

        $this->view->userOpts = $this->getUserOptions();
        $this->view->monthOpts = $this->getMonthOptions();
        $this->view->dayOpts = $this->getDayOptions();
        $this->view->ordinalOpts = $this->getOrdinalOptions();
    }

    protected function trueVal()
    {
        foreach (func_get_args() as $arg) {
            if ($arg) {
                return $arg;
            }
        }
    }

    protected function reverseDate($date)
    {
        return implode('-', array_reverse(explode('-', $date)));
    }

    protected function getMonthOptions()
    {
        return [
            1 => 'januari',
            2 => 'februari',
            3 => 'maart',
            4 => 'april',
            5 => 'mei',
            6 => 'juni',
            7 => 'juli',
            8 => 'augustus',
            9 => 'september',
            10 => 'oktober',
            11 => 'november',
            12 => 'december',
        ];
    }

    protected function getDayOptions()
    {
        return [
            1 => 'maandag',
            2 => 'dinsdag',
            3 => 'woensdag',
            4 => 'donderdag',
            5 => 'vrijdag',
            6 => 'zaterdag',
            7 => 'zondag',
        ];
    }

    protected function getOrdinalOptions()
    {
        return [
            1 => 'eerste',
            2 => 'tweede',
            3 => 'derde',
            4 => 'vierde',
            -1 => 'laatste',
        ];
    }

    protected function deleteAction()
    {
        $actModel = new Activities();
        $activity = $actModel->fetchRowById($this->_getParam('id'));
        $userId = $activity->user_id;

        $activity->delete();

        $this->_redirect('activities/list', ['user' => $userId]);
    }

    protected function getUserOptions()
    {
        $userModel = new Users();

        $userOpts = [];
        foreach ($userModel->getInternalUsers() as $user) {
            $userOpts[$user['id']] = $user['full_name'];
        }

        return $userOpts;
    }

    protected function getOffsetOptions()
    {
        $offsetOpts = [];
        foreach (range(0, 15) as $offset) {
            $dayNum = $offset + 1;
            $offsetOpts[$offset] = "{$dayNum}e dag van de maand";
        }

        foreach (range(-1, -16) as $dayNum) {
            $absDayNum = abs($dayNum);
            if ($absDayNum == 1) {
                $offsetOpts[$dayNum] = "{$absDayNum} dag voor einde van de maand";
            } else {
                $offsetOpts[$dayNum] = "{$absDayNum} dagen voor einde van de maand";
            }
        }

        return $offsetOpts;
    }
}
