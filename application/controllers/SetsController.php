<?
class SetsController extends GlobalController
{

	public function listAction()
	{
		$this->view->headLink()->appendStylesheet('media/style/sets/list.css');

		$params = array('url-override' => 'sets/list/');
		foreach(array('id', 'project') as $param_name)
			if($param_value = $this->_getParam($param_name))
				$params[$param_name] = $param_value;

		$this->view->list = $this->view->modelListView('Sets', $params)

			->setTypes(array('name', 'type'))

			->addFormat('type', function ($value) {
				return $value == 'commercieel' ? 'Commercieel' : 'Particulier';
			})

			->setOptions(array(
				'show_title' => false,
				'custom_class' => 'sets-list',
				'item_title' => 'Afgifteset',
				'show_view_template_buttons' => false,
				'paginator_items_per_page' => 5
			))

			->addButtons(array(
				'details' => 'Details',
				'add' => 'Toevoegen',
				'edit' => 'Bewerken'
			), array('id' => 'id', 'project' => $this->_getParam('project')))

			->setFilters(array('name' => array('type' => 'input'), 'type' => array('type' => 'select')))

			->render($this);
	}

	public function detailsAction()
	{
		$s_model = new Sets();
		$p_model = new Projects();
		$this->view->set = $s_model->getById($this->_getParam('id'));
		$this->view->project = $p_model->getById($this->_getParam('project'));

		$applied_objects = $s_model->findAppliedObjects($this->view->set->id) ?:array();
		$this->view->applied_objects = count($applied_objects);

		$this->view->Breadcrumbs()
			->addCrumb('Projecten')
			->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id)
			->addCrumb('Afgifteset ' . $this->view->set->name);


		$this->view->id = $this->_getParam('id');
	}

	public function editAction()
	{
		$this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');

		$s_model = new Sets();
		$p_model = new Projects();

		$set_id = $this->_getParam('id') ?: false;

		$this->view->set = $set_id ? $s_model->getById($set_id) : false;
		$this->view->project = $p_model->getById($this->_getParam('project'));

		$this->view->Breadcrumbs()
			->addCrumb('Projecten')
			->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id);

		if ($this->view->profile)
			$this->view->Breadcrumbs()->addCrumb('Afgifteset ' . $this->view->profile->name, 'set/details/id/' . $this->view->profile->id . '/project/' . $this->view->project->id);

		$this->view->Breadcrumbs()->addCrumb('Afgifteset bewerken/toevoegen');


		$this->view->form = $this->view->EditView($this, 'Sets', array('set' => $this->_getParam('id')))

			->setOptions(array(
				'redirect_to' => 'referer'
			))

			->render($this);
	}

	public function doUpdateAction(){
		$this->disableView();

		$id = $this->_getParam('id');
		$s_model = new Sets();

		$s_row = $s_model->getById($id);
		$s_row->status = 'concept';
		$s_row->save();



		$this->redirect('sets/multi-edit/id/' . $id . '/update/true/', array('id' => $id));
	}

	public function multiEditAction(){

		$s_model = new Sets();
		$p_model = new Projects();

		if(!$this->_getParam('sorting'))
			$this->_setParam('sorting', json_encode(array('object_id' => 'ASC')));

		$this->view->id = $id = $this->_getParam('id');
		$apply = $this->_getParam('apply') == 'true';
		$this->view->is_update = $is_update = $this->_getParam('update') == 'true';
		$set_row = $s_model->getById($id);
		$project_row = $p_model->getById($set_row->project);

		$this->view->Breadcrumbs()
			->addCrumb('Project')
			->addCrumb($project_row->name, 'project/details/id/' . $project_row->id)
			->addCrumb('Afgifteset ' . $this->view->set->name, 'sets/details/id/' . $id . '/project/' . $project_row->id)
			->addCrumb($is_update ? 'Update doorvoeren' : 'Onderdelen bewerken');

		if($is_update)
			$s_model->createUpdate($id, $apply);

		if($apply)
			return $this->getHelper('Redirector')->gotoUrl($this->view->Breadcrumbs()->getParentUrl());

		$sp_model = new SetsParts();

		$button_params = array('id' => 'id', 'project' => $set_row->project);

		if($is_update)
			$params = array('set' => $id, 'status' => 'update');
		else
			$params = array('set' => $id, 'status' => 'applied', 'deleted' => false, 'current' => true);

		if(isset($params['set'])) $button_params['set'] = $params['set'];

		$this->view->list = $this->view->listView($sp_model->getList($params), $params)

			->setTypes($sp_model->attributes)

			->addFormat('build_date', function ($value) {
				return $value ? date('Y', strtotime($value)) : '-';
			})

			->addFormat('warranty', function ($value) {
				return $value > 0 ? $value . ' maanden' : 'Geen';
			})

			->addFormat('object_id', function($value){
				$a = new Address();
				$address_row = $a->get($value, 'object');

				return $address_row ? $address_row->address . ' ' . $address_row->number : '-';
			})

			->addFormat('demarcation', 'bool')

			->addButtons(array(
				'edit' => 'Bewerken',
				'delete' => 'Verwijderen'
			), $button_params)

			->setOptions(array(
				'item_title' => 'Onderdeel',
				'render_to_controller' => false,
				'editAction' => 'part-edit',
				'deleteAction' => 'part-delete',
				'show_title' => false,
			))

			->render($this);
	}

	public function getBrandsAction(){
		$this->autocompleteForParts('brand');
	}

	public function getNamesAction(){
		$this->autocompleteForParts('name');
	}

	public function getProductCodesAction(){
		$this->autocompleteForParts('product_code', array('brand', 'name'));
	}

	protected function autocompleteForParts($property = 'name', $arguments = array()){
		$this->disableView();

		$select = db()->select()
			->from('sets_parts', array('name' => $property))
			->group($property)
			->where('`' . $property . '` IS NOT NULL')
			->where('`' . $property . '` != ?', '')
			->where('`' . $property . '` LIKE "%' . $this->_getParam('value') . '%"');

		$form_values = json_decode($this->_getParam('form_values'), true);

		foreach($arguments as $argument_name)
			if(isset($form_values['general'][$argument_name]))
				$select->where('`' . $argument_name . '` = ?', $form_values['general'][$argument_name]);

		echo json_encode(db()->fetchAll($select));
	}

	public function costOverviewAction(){
		$this->view->Breadcrumbs()->addCrumb('Overzicht kosten');

		$types = array(
			'project' => array('title' => 'Project', 'width' => 'medium', 'group_equal_rows' => true),
			'objectgroup' => array('title' => 'Objectgroep', 'width' => 'medium', 'group_equal_rows' => true),
			'brand' => array('title' => 'Merk', 'width' => 'medium'),
			'part' => array('title' => 'Onderdeel', 'width' => 'medium'),
			'year' => array('title' => 'Jaar', 'width' => 'medium'),
			'amount' => array('title' => 'Geboekte kosten', 'width' => 'medium')
		);

		$select = db()->select()
			->from(array('ic' => 'invoice_custom'), array('year' => 'fiscal_year'))
			->joinLeft(array('icr' => 'invoice_custom_rows'), 'icr.custom = ic.id', array('amount' => 'SUM(taxprice)'))
			->joinLeft(array('sp' => 'sets_parts'), 'sp.id = icr.part', array('brand', 'part' => 'id', 'part_name' => 'name'))
			->joinLeft(array('o' => 'objects'), 'o.id = ic.object', false)
			->joinLeft(array('og' => 'objectgroup'), 'og.id = o.objectgroup', array('objectgroup' => 'id', 'objectgroup_name' => 'description'))
			->joinLeft(array('p' => 'projects'), 'p.id = og.project', array('project' => 'id', 'project_name' => 'name'))
			->group('sp.set')
			->group('sp.brand')
			->group('sp.name')
			->group('sp.product_code')
			->where('ic.is_sets_costs = ?', true);
		;

		$data = db()->fetchAll($select);

		$this->view->list = $this->view->listView($data, array())

			->setTypes($types)

			->addFormat('build_date', function ($value) {
				return $value ? date('Y', strtotime($value)) : '-';
			})

			->addFormat('part', function ($value, $item) {
				return $value ? $item['part_name'] : '-';
			})

			->addFormat('objectgroup', function ($value, $item) {
				return $value ? $item['objectgroup_name'] : '-';
			})

			->addFormat('project', function ($value, $item) {
				return $value ? $item['project_name'] : '-';
			})

			->addFormat('amount', 'money')

			->addTotals(array('amount'))

			->setFilters(array(
				'project' => array(),
				'objectgroup' => array(),
				'brand' => array(),
				'part' => array(),
				'year' => array()
			))



			->setOptions(array(
				'item_title' => 'Kosten',
				'paginator' => false
			))

			->render($this);
	}

	public function partListAction(){
		$this->view->Breadcrumbs()->addCrumb('Afgifteset');

		$sp_model = new SetsParts();

		$types = array_intersect_key($sp_model->attributes, array_flip(array('name', 'brand', 'product_code', 'build_date', 'number', 'demarcation')));

		$types['name']['width'] = 'xsmall truncate';
		$types['brand']['width'] = 'xxxsmall truncate';
		$types['demarcation']['width'] = 'xxsmall truncate';

		$params = array(
			'object_id' => $this->_getParam('object_id'),
			'status' => 'applied',
			'deleted' => false,
			'current' => true,
			'url-override' => 'sets/part-list'
		);

		if($this->_getParam('view_template'))
			$params['view_template'] = $this->_getParam('view_template');

		$sp_model = new SetsParts();

		$button_params = array('id' => 'id');

		if(isset($params['set'])) $button_params['set'] = $params['set'];

		$this->view->list = $this->view->listView($sp_model->getList($params), $params)

			->setTypes($types)

			->addFormat('build_date', function ($value) {
				return $value ? date('Y', strtotime($value)) : '-';
			})

			->addFormat('warranty', function ($value) {
				return $value > 0 ? $value . ' maanden' : 'Geen';
			})

			->addFormat('object_id', function($value){
				$a = new Address();
				$address_row = $a->get($value, 'object');

				return $address_row ? $address_row->address . ' ' . $address_row->number : '-';
			})

			->addFormat('demarcation', 'bool')

			->addButtons(array(
				'edit' => '',
				'delete' => ''
			), $button_params)

			->setOptions(array(
				'item_title' => 'Onderdeel',
				'editAction' => 'part-edit',
				'deleteAction' => 'part-delete',
				'paginator_items_per_page' => 5,
				'show_title' => true,
			))

			->render($this);
	}

	public function complaintPartListAction(){
		$select = db()->select()
			->from(array('sp' => 'sets_parts'))
			->where('sp.object_id = ?', $this->_getParam('object'))
			->where('sp.status = ?', 'applied')
			->where('sp.deleted = ?', false)
			->where('sp.current = ?', true)
		;

		$this->view->parts = is_numeric($this->_getParam('object')) ? db()->fetchAll($select) : array();
	}

	public function partListForObjectAction(){
		$this->disableView();

		$select = db()->select()
			->from(array('sp' => 'sets_parts'), array('*', 'warranty_till' => '(`build_date` + INTERVAL `warranty` MONTH)', 'warranty'))
			->where('sp.object_id = ?', $this->_getParam('object'))
			->where('sp.status = ?', 'applied')
			->where('sp.deleted = ?', false)
			->where('sp.current = ?', true)
		;

		$part_list = db()->fetchAll($select);

		foreach($part_list as $part_list_id => $part)
			$part_list[$part_list_id]['warranty_till'] = strtotime($part['warranty_till']);

		echo json_encode($part_list);
	}

	public function findComplaintAction(){
		$this->disableView();

		$select = db()->select()
			->from(array('sc' => 'support_complaints'), array('id', 'name' => 'CONCAT(`identifier`, " - ", `title`)', 'map_to', 'type', 'contact_id' => 'inform_contact', 'date'))
			->joinLeft(array('scp' => 'support_complaints_parts'), 'scp.complaint = sc.id', false)
			->group('sc.id')
			->where('scp.id IS NOT NULL')
			->where('sc.identifier LIKE "' . $this->_getParam('value') . '%"')
		;

		$complaints = db()->fetchAll($select);

		foreach($complaints as $complaint_id => $complaint){
			$h = new Hierarchy($complaint['map_to'], $complaint['type']);
			$complaints[$complaint_id]['project'] = $h->_data['project'];
			$complaints[$complaint_id]['object'] = $h->_data['object'];

			$complaints[$complaint_id]['date'] = strtotime($complaint['date']);

			if($complaint['contact_id'])
				$complaints[$complaint_id]['contact_name'] = db()->fetchOne(db()->select()->from('support_contacts', array('name'))->where('id = ?', $complaint['contact_id']));
		}


		echo json_encode($complaints);
	}

	public function getComplaintPartsAction(){
		$this->disableView();

		$select = db()->select()
			->from(array('scp' => 'support_complaints_parts'), false)
			->joinLeft(array('sp' => 'sets_parts'), 'sp.id = scp.part', array('id', 'name'))
			->where('scp.complaint = ?', $this->_getParam('id'));
		;

		echo json_encode(db()->fetchAll($select));
	}

	public function partEditAction(){
		$s_model = new Sets();
		$sp_model = new SetsParts();
		$p_model = new Projects();
		$set_id = $this->_getParam('set');
		$project_id = $this->_getParam('project');
		$part_id = $this->_getParam('id');

		if(!is_null($project_id)){
			$this->view->project = $p_model->getById($this->_getParam('project'));
			$this->view->Breadcrumbs()
				->addCrumb('Project')
				->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id);
		}

		if(!is_null($set_id)){
			$this->view->set = $s_model->getById($this->_getParam('set'));
			$this->view->type = $this->_getParam('type');

			$this->view->Breadcrumbs()
				->addCrumb('Afgifteset ' . $this->view->set->name, 'sets/details/id/' . $this->view->set->id . (!is_null($project_id) ? '/project/' . $project_id : ''));

			if($part_id){
				$part = $sp_model->getById($part_id);
				if($part->status == 'update')
					$this->view->Breadcrumbs()->addCrumb('Update doorvoeren', 'sets/multi-edit/id/' . $this->_getParam('set') . '/update/true/');
			}
		}

		$this->view->Breadcrumbs()->addCrumb('Onderdeel bewerken/toevoegen');

		$this->view->form = $this->view->EditView($this, 'SetsParts', array('id' => $this->_getParam('id')))

			->setOptions(array(
				'redirect_to' => 'referer'
			))

			->render($this);
	}

	public function partDeleteAction() {
		$this->_helper->layout->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);

		if(!$this->_getParam('id')) return;

		$sp_model = new SetsParts();
		$part = $sp_model->getById($this->_getParam('id'));

		if($part->status == 'applied'){
			$part->setFromArray(array('deleted' => true))->save();
		} else {
			$sp_model->delete('id = ' . $this->_getParam('id'));
		}

		$this->getHelper('Redirector')->gotoUrl($_SERVER['HTTP_REFERER']);
	}

	public function chooseAction() {
		$s_model = new Sets();

		$type = $this->_getParam('Type');
		$project = $this->_getParam('project');

		$this->view->sets = $s_model->getList(array('type' => $type, 'project' => $project));
		$this->view->type = $type;
		$this->view->selected = $this->_getParam('Profiel');

		$this->_helper->layout->disableLayout();
	}

	public function createDefaultSetsAction(){
		$this->disableView();

		$s_model = new Sets();
		$sp_model = new SetsParts();
		$og_model = new Objectgroups();

		$projects_select = db()->select()
			->from(array('p' => 'projects'), array('id', 'name'))
			->joinLeft(array('og' => 'objectgroup'), 'og.project = p.id', false)
			->where('p.exploitation = ?', true)
			->where('og.set IS NULL')
			->where('p.sales_status = ?', 'Closed')
			->limit(1);

		$parts = array(
			array(
				'name' => 'CI Warmtepomp',
				'demarcation' => 1
			),
			array(
				'name' => 'CI Bronnen',
				'demarcation' => 1
			),
			array(
				'name' => 'CI Regelinstallatie',
				'demarcation' => 1
			),
			array(
				'name' => 'CI Overig',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Afgifteset',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Tapwater',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Ruimteverwarming',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Hoofdthermostaat',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Overig',
				'demarcation' => 1
			),
			array(
				'name' => 'BI Afgiftesysteem',
				'demarcation' => 0
			),
			array(
				'name' => 'BI Overig',
				'demarcation' => 0
			)
		);

		foreach(db()->fetchAll($projects_select) as $project){

			$objectgroup_select = db()->select()
				->from('objectgroup', array('id', 'type'))
				->where('project = ?', $project['id']);

			$objectgroups = db()->fetchAll($objectgroup_select);

			$objectgroup_types = array(
				'particulier' => false,
				'commercieel' => false
			);

			foreach($objectgroups as $objectgroup)
				foreach($objectgroup_types as $objectgroup_type => $objectgroup_type_value)
					if($objectgroup['type'] == $objectgroup_type){
						if($objectgroup_types[$objectgroup_type] === false)
							$objectgroup_types[$objectgroup_type] = array();


						$objectgroup_types[$objectgroup_type][] = $objectgroup['id'];
					}

			foreach($objectgroup_types as $objectgroup_type => $objectgroup_type_value){

				if($objectgroup_type_value === false) continue;

				$set_id = $s_model->save(
					array(
						'general' => array(
							'name' => $project['name'] . ' ' . $objectgroup_type,
							'date' => '01-10-2014',
							'type' => $objectgroup_type
						)
					),
					array('project' => $project['id'])
				);

				foreach($parts as $part)
					$sp_model->save(
						array(
							'general' => array(
								'name' => $part['name'],
								'brand' => '',
								'product_code' => '',
								'number' => 1,
							),
							'details' => array(
								'build_date' => '01-10-2014',
								'warranty' => 0,
								'demarcation' => $part['demarcation']
							)
						),
						array('project' => $project['id'], 'set' => $set_id)
					);

				foreach($objectgroup_type_value as $objectgroup)
					$og_model
						->getById($objectgroup)
						->setFromArray(array('set' => $set_id))
						->save();

				$s_model->createUpdate($set_id, false);
				$s_model->createUpdate($set_id, true);
			}
		}
		p('klaar');
	}
}