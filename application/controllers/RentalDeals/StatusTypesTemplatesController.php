<?php

class RentalDeals_StatusTypesTemplatesController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->Breadcrumbs()->addCrumb('Deal status templates', 'rental-deals_status-types-templates');
    }

    public function indexAction()
    {
        $select = db()->select()
            ->from('rental_deals_status_templates', ['default_status' => 'default', '*']);

        $data = db()->fetchAll($select);

        if(is_null($this->getParam('archived')) && !$this->isAjaxRequest){
            $this->setParam('archived', false);
        }
        /**
         * @var $listView Zend_View_Helper_ListView
         */
        $this->view->ListView($data)
            ->setTypes([
                'default' => ['title' => '', 'width' => 'xxxxsmall'],
                'name' => ['title' => 'Naam', 'width' => 'xxxlarge'],
                'status' =>  ['title' => 'Status', 'width' => 'small'],
                'edit_statusses' =>  ['title' => 'Statussen bewerken', 'width' => 'large'],
                'finalized' =>  ['width' => 'hidden'],
            ])

            ->addFormat('default', function($value, $item){
                if($value){
                    return '<i style="color: #84B83D;" class="fa fa-star" title="Standaard template"></i>';
                } elseif($item['finalized']) {
                    return '<a style="opacity: 0.3;" href="/rental-deals_status-types-templates/make-default/id/' . $item['id'] . '/" title="Standaard template maken"><i class="fa fa-star"></i></a>';
                } else {
                    return '<span noHint="1" class="fa fa-ellipsis-h" style="opacity: 0.3;" title="Concept template kan niet de standaard template gemaakt worden."></span>';
                }
            })

            ->addFormat('edit_statusses', function($value, $item){
                    return '<a href="rental-deals_status-types/index/template_id/' . $item['id'] . '/"><i class="fa fa-list-ol"></i> Statussen bewerken</a>';
            })

            ->addFormat(['finalized', 'archived'], 'bool')

            ->addFormat('status', function($value, $item){
                if($item['archived']){
                    return 'Gearchiveerd';
                } elseif ($item['finalized']){
                    return 'Definitief';
                }

                return 'Concept';
            })

            ->setFilters([
                'name' => ['type' => 'input'],
                'finalized' => ['renderSeparately' => true, 'title' => 'Definitief'],
                'archived' => ['renderSeparately' => true, 'title' => 'Gearchiveerd'],
            ])
            ->setOptions([
                'item_title' => 'Deal status template',
                'disabled_buttons_test' => function($row, $button){
                    if($button == 'copy'){
                        return false;
                    }

                    if($button == 'edit'){
                        return false;
                    }

                    if ($button === 'finalize') {
                        if (!$row['start_status_type_id'] || !$row['start_status_type_id_group_viewing']) {
                            return true;
                        }
                    }

                    if($row['archived']){
                        return true;
                    }

                    if($button == 'archive') {
                        return $row['default_status'];
                    }

                    return $row['finalized'] == 'Ja';
                }
            ])

            ->addButtons([
                'add' => 'Toevoegen',
                'finalize' => 'Definitief',
                'edit' => 'Bewerken',
                'archive' => 'Archiveren',
                'copy' => 'Dupliceren'
            ])

            ->render($this);
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()->addCrumb('Bewerken / toevoegen');

        $this->id = $this->getParam('id');

        $this->view->EditView($this, 'RentalDealsStatusTemplates')
            ->setOptions([])
            ->render($this);
    }

    public function archiveAction()
    {
        $this->disableView();

        $model = new RentalDealsStatusTemplates();

        $model
            ->getById($this->getParam('id'))
            ->setFromArray(['archived' => true])
            ->save();

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }

    public function finalizeAction()
    {
        $this->disableView();

        $model = new RentalDealsStatusTemplates();

        $model
            ->getById($this->getParam('id'))
            ->setFromArray(['finalized' => true])
            ->save();

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }

    public function makeDefaultAction()
    {
        $this->disableView();

        $model = new RentalDealsStatusTemplates();

        foreach($model->matchAll(['default' => true]) as $previous_default) {
            $previous_default
                ->setFromArray(['default' => false])
                ->save();
        }

        $model
            ->getById($this->getParam('id'))
            ->setFromArray(['default' => true])
            ->save();

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }

    public function copyAction()
    {
        $this->disableView();

        $model = new RentalDealsStatusTemplates();
        $types_model = new RentalDealsStatusTypes();

        $original = $model->getById($this->getParam('id'));

        $new_id = $model->createRow(['name' => $original->name . ' (kopie)'])->save();

        foreach($types_model->matchAll(['template_id' => $original->id]) as $original_type){
            $original_type = $original_type->toArray();

            unset($original_type['id']);
            $original_type['template_id'] = $new_id;

            $types_model->createRow($original_type)->save();
        }

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }
}
