<?php

class RentalDeals_TimelineController extends GlobalController
{
    public function preDispatch()
    {
        $this->view->headLink()
            ->appendStylesheet('media/style/rental-deals_timeline/style.css')
            ->appendStylesheet('media/style/task.css')
            ->appendStylesheet('media/style/datalist.css')
            ->appendStylesheet('media/style/popup.css')
            ->appendStylesheet('media/style/datepicker/vlaCal-v2.1.css')
        ;

        $this->view->headScriptHashed()
            ->appendFile('media/javascript/rental-deals_timeline/list.js')
            ->appendFile('media/javascript/supportDatePicker.js')
        ;

        $this->renderWithNewLayout();
    }

    public function forUserAction()
    {
        $this->view->id = $user_id = $this->getParam('id');

        $this->setParam('user', null);

        if (!$user_id) {
            die();
        }

        $timeline_model = new \Controllers\RentalDeals\Timeline\ForUser(
            $user_id
        );

        $this->applyFilterValues($timeline_model);

        $this->view->data = $timeline_model->getData();

        $this->view->meta_data = $timeline_model->getMetaData();
        $this->view->filterOptionsData = $timeline_model->getFilterOptionData();

        $this->view->timeline_type = 'for-user';
        $this->render('timeline');
    }

    public function forObjectAction()
    {
        $this->view->id = $object_id = $this->getParam('id');

        $this->setParam('object', null);

        if (!$object_id) {
            die();
        }

        $timeline_model = new \Controllers\RentalDeals\Timeline\ForObject(
            $object_id
        );

        $this->applyFilterValues($timeline_model);

        $this->view->data = $timeline_model->getData();

        $this->view->meta_data = $timeline_model->getMetaData();
        $this->view->has_recently_received_single_mailing = $timeline_model->hasRecentlyReceivedSingleMailing();
        $this->view->filterOptionsData = $timeline_model->getFilterOptionData();

        $this->view->timeline_type = 'for-object';
        $this->render('timeline');
    }

    private function applyFilterValues(\Controllers\RentalDeals\Timeline\AbstractTimeline $timeline_model)
    {
        $filter_values = [];
        $timelineType = $timeline_model->getTimelineType();

        foreach (['object', 'user', 'status'] as $filter_name) {
            if (!is_null($this->getParam($filter_name))) {
                $filter_values[$filter_name] = $this->getParam($filter_name);
            } elseif($timelineType === 'object' && $filter_name === 'user') {
                $filter_values['user'] = $timeline_model->getUserIdFromCurrentDeal();
            } elseif($timelineType === 'user' && $filter_name === 'object') {
                $filter_values['object'] = $timeline_model->getObjectIdFromCurrentDeal();
            }
        }

        $timeline_model->setFilterValues($filter_values);

        $this->view->filter_values = $filter_values;
    }
}
