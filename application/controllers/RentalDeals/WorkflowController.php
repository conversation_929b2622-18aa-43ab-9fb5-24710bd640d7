<?

use RentalDeals\CancelConceptDeal\CancelOtherRentalDealsForSameObjectService;
use RentalDeals\CancelConceptDeal\SendDeniedDealMailService;

class RentalDeals_WorkflowController extends GlobalController
{

    public function preDispatch()
    {

        $this->view->Breadcrumbs()
            ->addCrumb(ucfirst(translate()->_('rental')))
            ->addCrumb('Workflow');
    }

    public function workflowAction()
    {

        $this->renderWithNewLayout();


        $this->redirectToDefaultWorkflow();

        $model = new \Controllers\RentalDeals\Workflow\WorkflowList(
            $this->getParam('id')
        );

        $status_type_list = new \Controllers\RentalDeals\Workflow\StatusTypeList(
            $this->getParam('id')
        );

        $this->view->Breadcrumbs()
            ->addCrumb('Workflow: ' . $model->getWorkflowName(),
                'deals_workflow/workflow/id/' . $model->getWorkflowId() . '/');


        $this->view->ListView($status_type_list->getList(), [])
            ->setTypes([

                'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
                'description' => ['title' => ucfirst(translate()->_('workflow_step')), 'width' => 'xxlarge truncate'],
                'count' => ['title' => ucfirst(translate()->_('count')), 'width' => 'medium ']
            ])
            ->addFormat('description', function ($value, $item) {
                if ($item['count'] > 0) {
                    return
                        '<a href="rental-deals_workflow/list/status_type_id/' . $item['id'] . '/">' .
                        $value . ' <small>( ' . $item['count'] . ' )</small>
                        </a>';
                } else {
                    return
                        '<span style="color: rgba(0,0,0,0.3);">' .
                        $value . ' <small>( 0 )</small>
                        </span>';
                }
            })
            ->setOptions([
                'item_title' => 'Taak',
                'paginator' => false,
            ])
            ->render($this);
    }

    private function redirectToDefaultWorkflow()
    {

        if (!is_null($this->getParam('id'))) {
            return;
        }

        $select = db()->select()
            ->from('rental_deals_status_templates', ['id'])
            ->where('`default` = ?', true);

        if ($default_workflow_id = db()->fetchOne($select)) {
            $this->redirect('rental-deals_workflow/workflow/id/' . $default_workflow_id . '/');
        } else {
            die('Nog standaard workflow aangemaakt.');
        }
    }

    private function redirectToWorkflowId()
    {
        $workflowId = $this->getParam('workflow_id');
        $statusTypeId = $this->getParam('status_type_id');

        if (is_null($workflowId)) {
            return;
        }

        $statusTypeListClass = new \Controllers\RentalDeals\Workflow\StatusTypeList(
            $workflowId
        );

        $statusTypeList = array_column($statusTypeListClass->getList(), 'id');

        if (in_array($statusTypeId, $statusTypeList)) {
            return;
        }

        $this->resetSessionSavedFilterSettings();

        $firstStatusType = array_shift($statusTypeList);

        if (!$firstStatusType) {
            return;
        }

        $this->redirect('rental-deals_workflow/list/status_type_id/' . $firstStatusType . '/');
    }

    private function redirectToDefaultStatusType(){

        if (!is_null($this->getParam('status_type_id'))) {
            return;
        }

        $select = db()->select()
            ->from('rental_deals_status_templates', ['id'])
            ->where('`default` = ?', true);

        if ($default_workflow_id = db()->fetchOne($select)) {

            $status_type_list = new \Controllers\RentalDeals\Workflow\StatusTypeList(
                $default_workflow_id
            );

            if($first_status_type = array_shift($status_type_list->getList())){
                $this->redirect('rental-deals_workflow/list/status_type_id/' . $first_status_type['id'] . '/');
            }
        }

        die('Nog standaard workflow aangemaakt.');
    }

    public function listAction()
    {
        $this->view->headScriptHashed()
            ->appendFile('media/javascript/rental-deals_workflow/deal_status_assign.js')
            ->appendFile('media/javascript/rental-deals_workflow/DealStatusAssignActions/forwardToStepAction.js')
            ;

        $this->view->headLink()->appendStylesheet('media/style/rental-deals/details.css');

        ini_set('memory_limit', '2000M');

        $this->handleSessionSavedFilterSettings([
            'status_type_id'
        ]);

        $this->redirectToWorkflowId();

        $statusTypeId = $this->getParam('status_type_id');

        $this->redirectToDefaultStatusType();

        $listHelper = new \Controllers\RentalDeals\Workflow\ListHelper(
            $statusTypeId,
            new Users()
        );

        $dealListService = new \Controllers\RentalDeals\Workflow\DealListService(
            new Investors()
        );

        $workflowId = $listHelper->getWorkflowId();

        $statusTypeList = new \Controllers\RentalDeals\Workflow\StatusTypeList(
            $workflowId
        );

        $this->view->Breadcrumbs()
            ->addCrumb(
                'Workflow: ' . $listHelper->getWorkflowName(),
                'rental-deals_workflow/workflow/id/' . $workflowId . '/'
            )
            ->addCrumb($listHelper->getStatusTypeDescription());

        $this->view->statusTypeText = $listHelper->getStatusTypeText();

        $params = [
            'created_user_id' => $this->_getParam('created_user_id'),
            'status_type_id' => $this->getParam('status_type_id'),
            'website_deal' => 'yes',
            'vidii_deal' => 'no'
        ];

        $admin_user_list = $listHelper->getAdminUserList();
        $isManualForwardAllowed = $listHelper->getStatusTypeManualForwardAllowed();

         $this->view->ListView($dealListService->execute($statusTypeId), $params)
            ->setTypes([
                'actions' => ['title' => '', 'width' => 'xxsmall'],
                'checksum' => ['title' => '', 'width' => 'hidden'],
                'status_checksum' => ['title' => '', 'width' => 'hidden'],
                'object_id' => ['title' => '', 'width' => 'hidden object_id'],
                'user_id' => ['title' => '', 'width' => 'hidden user_id'],
                'select_button' => ['title' => '', 'width' => 'xxxxsmall select_button'],
                'object' => ['title' => 'Adres', 'width' => 'xxxlarge truncate'],
                'user' => ['title' => ucfirst(translate()->_('candidate')), 'width' => 'xlarge truncate userColumn'],
                'contact-date' => ['title' => ucfirst(translate()->_('contact_date')), 'width' => 'xsmall'],
                'from' => ['title' => ucfirst(translate()->_('from')), 'width' => 'xsmall'],
                'age_in_days' => ['title' => ucfirst(translate()->_('days_in_step')), 'width' => 'xsmall'],
                'days_until_deadline' => [
                    'title' => ucfirst(translate()->_('days_to_deadline')) .' (' . $listHelper->getStatusDeadlineWorkdays() . ')',
                    'width' => 'xsmall'
                ],
                'assigned_user_id' => ['title' => '', 'width' => 'hidden'],
                'publish_status_override' => ['title' => '', 'width' => 'hidden publish_status_override'],
            ])
            ->addFormat('object', function ($value, $item) {
                return '<a href="rental-deals_timeline/for-object/id/' . $item['object_id'] . '/"  >' . ucfirst($value) . '</a>';
            })
            ->addFormat('user', function ($value, $item) {
                $counterHtml = '';
                if($item['numberOfActiveDealsForUser'] > 1){
                    $title = ucfirst(translate()->_('other_active_deals')) . ': ' .
                        ($item['numberOfActiveDealsForUser'] - 1);

                    $counterHtml =
                        '<div noHint="1" class="otherDealCounter" title="' . $title . '">' .
                        ($item['numberOfActiveDealsForUser'] - 1) .
                        '</div>';
                }

                return
                    '<a href="rental-deals_timeline/for-user/id/' . $item['user_id'] . '/"  >' .
                    $value . ' ' . $counterHtml .
                    '</a>';
            })
            ->addFormat('checksum', function ($value) {
                return '<span class="checksum">' . $value . '</span>';
            })
            ->addFormat('status_checksum', function ($value) {
                return '<span class="status_checksum">' . $value . '</span>';
            })
            ->addFormat('actions', function ($null, $row) use ($isManualForwardAllowed) {
                $dom = new DOMDocument('1.0');

                $user_id = loginManager::data()->id;
                $status_type_id = $this->getParam('status_type_id');

                if ($row['assigned_user_id'] !== $user_id) {
                    $assign_to_me_element = $dom->createElement('i');
                    $assign_to_me_element->setAttribute('class', 'assign_to_me fas fa-user-lock');
                    $assign_to_me_element->setAttribute('title', ucfirst(translate()->_('open_deal')));
                    $assign_to_me_element->setAttribute('data-user_id', $user_id);
                    $assign_to_me_element->setAttribute('data-status_type_id', $status_type_id);
                    $dom->appendChild($assign_to_me_element);


                    
                } else {
                    $unassign_element = $dom->createElement('i');
                    $unassign_element->setAttribute('class', 'unassign fas fa-save');
                    $unassign_element->setAttribute('title', ucfirst(translate()->_('save_deal')));
                    $unassign_element->setAttribute('data-user_id', $user_id);
                    $unassign_element->setAttribute('data-status_type_id', $status_type_id);
                    $dom->appendChild($unassign_element);



                    if($isManualForwardAllowed) {
                        $mark_as_done_element = $dom->createElement('i');
                        $mark_as_done_element->setAttribute('class', 'mark_as_done fas fa-step-forward');
                        $mark_as_done_element->setAttribute('title', ucfirst(translate()->_('forward_deal')));
                        $mark_as_done_element->setAttribute('data-status_type_id', $status_type_id);
                        $dom->appendChild($mark_as_done_element);

                        $forwardToStepElement = $dom->createElement('i');
                        $forwardToStepElement->setAttribute('class', 'forward_to_step fas fa-fast-forward');
                        $forwardToStepElement->setAttribute('title', translate()->_('forward_to_step'));
//                        $forwardToStepElement->setAttribute('data-status_type_id', $status_type_id);
                        $dom->appendChild($forwardToStepElement);
                    }
                }



                if ($row['assigned_user_id'] === $user_id && $row['backwards_allowed']) {
                    if ($row['previous_status_id']) {
                        $revertToPreviousStatusButton = $dom->createElement('i');
                        $revertToPreviousStatusButton->setAttribute(
                            'class',
                            'revertToPreviousStatus fas fa-step-backward'
                        );
                        $revertToPreviousStatusButton->setAttribute(
                            'title',
                            ucfirst(translate()->_('rewind_deal'))
                        );
                        $revertToPreviousStatusButton->setAttribute(
                            'data-current_status_type_id',
                            $status_type_id
                        );
                        $revertToPreviousStatusButton->setAttribute(
                            'data-previous_status_type_id',
                            $row['previous_status_id']
                        );
                        $revertToPreviousStatusButton->setAttribute(
                            'data-backwards_set_profile',
                            $row['backwards_set_profile']
                        );
                        $revertToPreviousStatusButton->setAttribute(
                            'data-userid',
                            $row['userid']
                        );
                        $dom->appendChild($revertToPreviousStatusButton);
                    }
                }

                return $dom->saveHTML();
            })
            ->addFormat('assigned_user_id', function ($assigned_user_id) use ($admin_user_list) {
                $dom = new DOMDocument('1.0');

                $select = $dom->createElement('select');
                $select->setAttribute('class', 'assign_select');
                $dom->appendChild($select);

                $option = $dom->createElement(
                    'option',
                    '-'
                );

                $option->setAttribute('value', 0);
                $select->appendChild($option);

                foreach ($admin_user_list as $admin_user_group) {

                    $optgroup = $dom->createElement('optgroup');
                    $optgroup->setAttribute('label', $admin_user_group['label']);
                    $select->appendChild($optgroup);

                    foreach ($admin_user_group['users'] as $admin_user_id => $admin_user) {


                        $option = $dom->createElement(
                            'option',
                            $admin_user['name']
                        );

                        $option->setAttribute('value', $admin_user_id);

                        if ($admin_user_id == $assigned_user_id) {
                            $option->setAttribute('selected', 'selected');
                        }

                        $optgroup->appendChild($option);
                    }
                }

                return $dom->saveHTML();
            })
             ->addFormat('from', function ($value, $row) {
                     return $value ? date('d-m-Y', strtotime($value)) : '-';

             })
            ->addFormat('contact-date', function ($value, $row) {
                if ($row['assigned_user_id'] !== loginManager::data()->id) {
                    return $value ? date('d-m-Y', strtotime($value)) : '-';
                } else {
                    $dom = new DOMDocument('1.0');

                    $select = $dom->createElement('input');
                    $select->setAttribute('class', 'contact_date');
                    $select->setAttribute('type', 'date');
                    $select->setAttribute('value', $value);
                    $dom->appendChild($select);
                    return $dom->saveHTML();
                }
            })
            ->addDetails('rental-deals/details', ['style' => 'popup'])
            ->addLegend('status-text')
            ->setFilters([
                'object_id' => ['type' => 'json_array', 'preApplied' => true],
                'user_id' => ['type' => 'json_array', 'preApplied' => true],
                'object' => ['type' => 'input'],
                'investor_name' => [
                    'type' => 'select',
                    'title' => ucfirst(translate()->_('investor')),
                    'renderSeparately' => true,
                    'order_by_title' => 'ASC',
                ],
                'user' => ['type' => 'input'],
                'status_type_id' => [
                    'renderSeparately' => true,
                    'title' => ucfirst(translate()->_('workflow_step')),
                    'hideCount' => true,
                    'custom_options' => $statusTypeList->getListviewFilterOptions(),
                    'custom_options_only' => true,
                    'preApplied' => true,
                    'show_all_disabled' => true,
                    'noAjax' => true
                ],
            ])
            ->setOptions([
                'item_title' => 'Deal',
                'paginate' => false,
            ])
            ->render($this);

    }

    public function forwardToStepAction()
    {

        $dealId = $this->getParam('deal-id');

        if (!$dealId) {
            throw new InvalidArgumentException('Missing argument: deal-id');
        }

        $this->view->dealId = $dealId;


        if($_POST){
            $forwardToStatusId = $_POST['forwardToStatusId'];

            if(!$forwardToStatusId){
                throw new InvalidArgumentException('Missing argument: forwardToStatusId');
            }

            $sendDeniedDealMailService = new SendDeniedDealMailService(
                new \Objects(),
                new \Email(),
                new \User()
            );

            $forwardToNextStatusService = new \Controllers\RentalDeals\Workflow\ForwardToNextStatus(
                new RentalDeals(),
                new RentalDealsStatusTypes(),
                new CancelOtherRentalDealsForSameObjectService(
                    $sendDeniedDealMailService
                )
            );

            $forwardToStepActionHelper = new \Controllers\RentalDeals\Workflow\ForwardToStep\HandleForwardToNextStepService(
                new \Controllers\RentalDeals\Workflow\ForwardToStep\StatusList(),
                $forwardToNextStatusService
            );

            $forwardToStepActionHelper->execute($dealId, $forwardToStatusId);

            if($_POST['contactDateMode'] === 'manual') {
                $contactDateModeManualValue = new DateTime($_POST['contactDateModeManualValue']);

                if ($contactDateModeManualValue) {
                    $updateContactDateService = new \Controllers\RentalDeals\Workflow\UpdateContactDateService(new RentalDeals());
                    $updateContactDateService->execute(
                        $dealId,
                        $contactDateModeManualValue
                    );
                }
            }

        } else {
            $this->_helper->layout->disableLayout();
            $forwardToStepActionHelper = new \Controllers\RentalDeals\Workflow\ForwardToStep\ActionSelectionListHelper(
                new \Controllers\RentalDeals\Workflow\ForwardToStep\StatusList()
            );

            $this->view->statusList = $forwardToStepActionHelper->getNextStatusListForDealId($dealId);
            $this->view->dealDetails = $forwardToStepActionHelper->getDealDetails($dealId);
        }
    }

    public function checkDealStatusAction(){
        $this->disableView();

        $deal_ids = explode(',', $this->getParam('ids'));
        $checksum = $this->getParam('checksum');

        if(count($deal_ids) > 0){
            $deal_status = new \Controllers\RentalDeals\Workflow\CheckDealStatus(
                $deal_ids
            );

            if(!$deal_status->checkListChecksum($checksum)){
                echo json_encode([
                    'list_checksum' => $deal_status->getListChecksum(),
                    'deal_checksums' => $deal_status->getData(),
                ]);
                return;
            }
        }

        /** @noinspection PhpUnhandledExceptionInspection */
        $this
            ->getResponse()
            ->setHttpResponseCode(304);
    }

    private function handleSessionSavedFilterSettings($filters)
    {
        foreach ($filters as $filter_name) {
            if (!is_null($this->getParam($filter_name))) {
                $_SESSION[$filter_name . '_value'] = $this->getParam($filter_name);
            } elseif (!$this->isAjaxRequest && $_SESSION[$filter_name . '_value']) {
                $this->setParam($filter_name, $_SESSION[$filter_name . '_value']);
            }
        }
    }

    private function resetSessionSavedFilterSettings()
    {
        $filters = [
            'status_type_id'
        ];

        foreach ($filters as $filter_name) {
            unset($_SESSION[$filter_name . '_value']);
        }
    }

    public function forwardToNextStatusAction()
    {
        $this->disableView();

        $sendDeniedDealMailService = new SendDeniedDealMailService(
            new \Objects(),
            new \Email(),
            new \User()
        );

        $next_status_model = new \Controllers\RentalDeals\Workflow\ForwardToNextStatus(
            new RentalDeals(),
            new RentalDealsStatusTypes(),
            new \RentalDeals\CancelConceptDeal\CancelOtherRentalDealsForSameObjectService(
                $sendDeniedDealMailService
            )
        );

        $next_status_model->forwardUsingWorkflowActionButton(
            $this->getParam('id'),
            $this->getParam('type')
        );
    }

    public function testAction()
    {
        $this->disableView();


        $sendDeniedDealMailService = new SendDeniedDealMailService(
            new \Objects(),
            new \Email(),
            new \User()
        );

        $next_status_model = new \Controllers\RentalDeals\Workflow\ForwardToNextStatus(
            new RentalDeals(),
            new RentalDealsStatusTypes(),
            new CancelOtherRentalDealsForSameObjectService(
                $sendDeniedDealMailService
            )
        );

        $next_status_model->findDealsAndForward('documents_received', 750);
    }

    public function revertToPreviousStatusAction()
    {
        $this->disableView();

        $next_status_model = new \Controllers\RentalDeals\Workflow\RevertToPreviousStatus();

        $next_status_model->revertUsingWorkflowActionButton(
            $this->getParam('id'),
            $this->getParam('current_status_type_id'),
            $this->getParam('previous_status_type_id')
        );

        if (is_numeric($this->getParam('backwards_set_profile'))) {
            $user = Users::get($this->getParam('userid'));
            $user['profile_completion'] =  $this->getParam('backwards_set_profile');
            $user->save();
        }

    }
}
