<?php

class RentalDeals_StatusTypesController extends GlobalController
{
    public $template_id;

    public function preDispatch()
    {
        if($template_id = $this->getParam('template_id')){
            $this->template_id = $template_id;
        } else {
            die('no template chosen');
        }

        $select = db()->select()
            ->from('rental_deals_status_templates')
            ->where('id = ?', $this->template_id);

        $data = db()->fetchRow($select);

        $this->view->Breadcrumbs()
            ->addCrumb('Deal statussen templates', 'rental-deals_status-types-templates/index')
            ->addCrumb('Status template "' . $data['name'] . '"', 'rental-deals_status-types-templates/edit/id/' . $this->template_id . '/');
    }

    public function indexAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Deal statussen bewerken');

        $select = db()->select()
            ->from('rental_deals_status_types')
            ->joinLeft(array('rdst' => 'rental_deals_status_templates'), 'rdst.id = rental_deals_status_types.template_id', array('finalized'))
            ->where('template_id = ?', $this->template_id)
            ->order('order')
            ->where('deleted = ?', false);

        $data = db()->fetchAll($select);


        $this->setParam('sorting', json_encode(['nr' => 'ASC']));
        /**
         * @var $listView Zend_View_Helper_ListView
         */

        $types = [
            'order' => ['title' => '', 'width' => 'xxxxsmall right'],
            'spacer' => ['title' => '', 'width' => 'xxxxsmall'],
            'description' => ['title' => 'Status', 'width' => 'xxxxlarge truncate'],
            'allow_concept_deal' => ['title' => 'Concept aanmaken', 'width' => 'small'],
            'allow_final_deal' => ['title' => 'Definitief maken', 'width' => 'small'],
            'workflow_columns' => ['title' => '| Workflow instellingen: ', 'width' => 'medium'],
            'workflow_manual_forward' => ['title' => 'Handmatig doorzetten', 'width' => 'medium'],
            'workflow_forward_on_event' => ['title' => 'Automatisch doorzetten', 'width' => 'medium truncate'],
            'workflow_additional_action' => ['title' => 'Actie uitvoeren', 'width' => 'medium truncate'],
        ];

        if (Settings::get('modules_rental_workflow_deals')) {
            $this->view->extraWidth = true;
        } else {
            unset(
                $types['workflow_columns'],
                $types['workflow_manual_forward'],
                $types['workflow_forward_on_event'],
                $types['workflow_additional_action']
            );
        }

        $forwardOnEventTitles = RentalDealsStatusTypes::getForwardOnEventValues();
        $additionalActionTitles = RentalDealsStatusTypes::getAdditionalActionTitles();

        $this->view->ListView($data)
            ->setTypes($types)

            ->addFormat('order', function($value){
                return $value > 0 ? $value . '.' : '';
            })

            ->addFormat(['allow_concept_deal', 'allow_final_deal', 'workflow_manual_forward'], 'bool')

            ->addFormat('workflow_forward_on_event', function ($value) use ($forwardOnEventTitles) {
                if (isset($forwardOnEventTitles[$value])) {
                    return $forwardOnEventTitles[$value];
                } else {
                    return '-';
                }
            })
            ->addFormat('workflow_additional_action', function ($value) use ($additionalActionTitles) {
                if (isset($additionalActionTitles[$value])) {
                    return $additionalActionTitles[$value];
                } else {
                    return '-';
                }
            })

            ->setFilters([
                'description' => ['type' => 'input'],
                'allow_concept_deal' => [],
                'allow_final_deal' => [],
                'workflow_manual_forward' => [],
                'workflow_forward_on_event' => [],
                'workflow_additional_action' => [],
            ])
            ->setOptions([
                'item_title' => 'Deal status',
                'paginator' => false,
                'disabled_buttons_test' => function($row, $button){


                    if($button == 'delete') {
                        return $row['finalized'];
                    }

                    return $row['finalized'] == 'Ja';
                }
            ])

            ->addButtons([
                'add' => 'Toevoegen',
                'edit' => 'Bewerken',
                'delete' => 'Verwijderen'
            ], [
                'id' => 'id',
                'template_id' => $this->template_id
            ])

            ->render($this);
    }

    public function editAction()
    {
        $this->view->Breadcrumbs()
            ->addCrumb('Deal statussen bewerken', 'rental-deals_status-types/index/template_id/' . $this->template_id . '/')
            ->addCrumb('Status bewerken / toevoegen');

        $this->view->EditView($this, 'RentalDealsStatusTypes')
            ->setOptions([])
            ->render($this);
    }

    public function deleteAction()
    {
        $this->disableView();

        $model = new RentalDealsStatusTypes();

        $model
            ->getById($this->getParam('id'))
            ->setFromArray(['deleted' => true])
            ->save();

        if($referer = $_SERVER['HTTP_REFERER'])
            header('Location: '. $referer);
    }

    public function getListForTemplateIdAction(){
        $this->disableView();

        $statusTypes = RentalDealsStatusTypes::getListForTemplateId($this->template_id);

        echo json_encode($statusTypes);
    }
}
