<?

class ProjectController extends GlobalController {

		public function preDispatch() {

			$this->view->Breadcrumbs()->addCrumb('Projecten', 'project/');
		}

		/**
		 * Project
		 * Project list (index)
		 *
		 */
		public function indexAction() {
			$showall = $this->_getParam('showall');
			$project = new Project();
			$cLib = new Corporation();

			if ($_POST['field'] && $_POST['order']){
				$order = array(array('field' => $_POST['field'], 'direction' => $_POST['order']));
			} else {
			 	$order = false;
            }

			if(!$this->getParam('sorting'))
				$this->setParam('sorting', json_encode(array('name' => 'ASC')));


			$this->view->ListView($project->getList(false, false, false, $showall))
				->setTypes(array(
                    'full_identifier' => 	    array('title' => 'Nummer', 'width' => 'xxsmall'),
                    'id' => 	                array('title' => 'Project id', 'width' => 'hidden'),
                    'name' => 				    array('title' => 'Naam', 'width' => 'xxxllarge truncate'),
                    'corporation_name' => 	    array('title' => 'Juridische entiteit', 'width' => 'xxlarge truncate'),
                    'city' => 				    array('title' => 'Stad', 'width' => 'large truncate'),
                    'type' => 			    	array('title' => 'Type', 'width' => 'hidden'),
                    'responsible_commercial' => array('title' => 'Accountmanager', 'width' => 'hidden'),
                    'responsible_technical' => 	array('title' => 'Technisch manager', 'width' => 'hidden'),
                    'responsible_financial' => 	array('title' => 'Financieel manager', 'width' => 'hidden'),
					'invoiceable' => 		    array('title' => 'Factureren', 'width' => 'xxsmall'),
                    'purchase_price' => 	array('title' => 'Aankoopsom', 'width' =>  'hidden'),
                    'purchase_costs' => 	array('title' => 'Aanschafkosten', 'width' => 'hidden'),
				))

			->addFormat('invoiceable', 'bool')

			->addFormat('name', function($value, $item){
				return '<a href="project/details/id/' . $item['id'] . '/">' . ($value ? $value : '-') . '</a>';
			})

			->addFormat('city', function($value){ return $value ? $value : '-';	})

                ->addFormat('responsible_commercial', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    }

                    return $item['responsible_commercial_name'];
                })

                ->addFormat('responsible_technical', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    }

                    return $item['responsible_technical_name'];
                })

                ->addFormat('responsible_financial', function ($value, $item) {
                    if (!$value) {
                        return '-';
                    }

                    return $item['responsible_financial_name'];
                })


			->setFilters([
				'corporation_name' => ['renderSeparately' => true, 'order_by_value' => 'ASC', 'title' => 'Juridische entiteit'],
                'responsible_commercial' => ['renderSeparately' => true, 'order_by_value' => 'ASC', 'title' => 'Accountmanager'],
				'full_identifier' => ['type' => 'input'],
				'name' => ['type' => 'input'],
				'city' => [],
				'type' => [],
				'invoiceable' => []
			])

			->addButtons(array(
				'add' => 'Toevoegen',
				'delete' => 'Verwijderen',
			), array(
				'id' => 'id'
			))

			->setOptions(array(
				'item_title' => 'Project',
				'disabled_buttons_test' => function($row, $button){
					if($button == 'delete')
						return !$row['deleteable'];

					return false;
				}
			))

			->render($this);

	}

		public function brokerRatingsListAction(){
			
			$this->view->Breadcrumbs()
				->addCrumb('Belegger pagina')
				->addCrumb('Klantenservice ratings', 'project/broker-ratings-list');

			if(is_null($this->_getParam('sorting')))
				$this->_setParam('sorting', json_encode( [ 'project_id' => 'ASC' ] ));


			$this->view->ModelListView('ProjectsBrokerRatings')
				->setTypes(['project_id', 'type', 'year', 'rating', 'spacer'])

				->addFormat('project_id', function($value){
					return $value > 0 ? db()->fetchOne(db()->select()->from('projects', ['name'])->where('id = ?', $value)) : '-';
				})

				->addFormat('type', function($value){
					$model = new ProjectsBrokerRatings();
					return isset($model->types[$value]) ? $model->types[$value] : '-'; 
				})

				->setFilters([
					'project_id' => [],
					'type' => [],
					'year' => []
				])

				->addButtons(array(
					'edit' => 'Bewerken',
					'add' => 'Toevoegen'
				), array(
					'id' => 'id'
				))

				->setOptions(array(
					'item_title' => 'Rating',
					'disabled_buttons_test' => function($row, $button){
						if($button == 'delete')
							return !$row['deleteable'];

						return false;
					}
				))

				->render($this);			
		}

		public function brokerRatingsEditAction(){

			$this->view->Breadcrumbs()
				->addCrumb('Belegger pagina')
				->addCrumb('Klantenservice ratings', 'project/broker-ratings-list')
				->addCrumb('Rating bewerken / toevoegen');

			$this->view->form = $this->view->EditView($this, 'ProjectsBrokerRatings', array('id' => $uoid, 'object' => $object))
							
				->setOptions([
					'redirect_to' => 'breadcrumbs'
				])
				
				->render($this);			
		}
		
		public function emptyRateExportAction(){
			$this->disableView(true);
		
			$r = new Ratesheets();
			$select = db()->select()
				->from(array('r' => 'ratesheets'), array('name', 'emptyrate'))
				->joinLeft(array('og' => 'objectgroup'), 'og.rate = r.id', false)
				->joinLeft(array('p' => 'projects'), 'p.id = og.project', array('project' => 'name'))
				->where('p.id IS NOT NULL')
				->order('p.name ASC');
			
			$this->view->ratesheets = db()->fetchAll($select);
			
			header('Content-type: application/vnd.ms-excel');
			header('Content-disposition: attachment; filename=MijnVaanster_export.csv');
		}
		
		public function financialExportAction(){
			$this->disableView(true);
			
			$i = new Invoice();
			
			$q = dateToQuarter(strtotime('+1 months'));
		
			$select = db()->select()
				
				->from(array('o' => 'objects'),		array('id', 'm2_override' => 'm2', 'tapkloverride'))
				
				->joinLeft(array('a' => 'address'),
					'a.type = "object" AND a.type_id = o.id',	array('address', 'number'))
					
				->joinLeft(array('io' => 'invoice_objects'), 'io.object = o.id', array('total'))
				
				->joinLeft(array('i' => 'invoices'), 'i.id = io.invoice', array('investor', 'advance'))
				
				->joinLeft(array('ir' => 'invoices_run'), 'ir.id = i.run', array('start', 'end'))
				
				->joinLeft(array('og' => 'objectgroup'), 'og.id = o.objectgroup', array('type', 'rate', 'profile', 'm2', 'tapkl'))
				
				->joinLeft(array('p' => 'projects'), 'p.id = og.project',	array('name'))
				
				->order('p.id')
				->order('a.address')
				->order('CAST(a.number as SIGNED)')
				->where('p.id IN (33, 178, 179, 32)')
				->where('(ir.period = "monthly" AND ir.start = "' . date('Y-m-d', mktime(0, 0, 0, date('m')+1, 1)) . '") OR (ir.period = "quarterly" AND ir.start = ?)', date('Y-m-d', quarterToDate($q['year'], $q['quarter'])));
				
			$data = array();
			foreach(db()->fetchAll($select) as $row){
				$class = $row['tapkloverride'] ? $row['tapkloverride'] : $row['tapkl'];
				$size = $row['m2_override'] ? $row['m2_override'] : $row['m2'];
				$row['usage'] = Usage::getUsage($row['profile'], $row['id']);
		
				$row['advance'] = $row['investor'] > 0 && $row['total'] > 0 ? Invoice::calcTax('add', $row['total']) : $row['advance'];
				
				foreach((array) $row['usage'] as $type => $units)
       				foreach(array('GJ', 'M3', 'KW') as $unit)
       					if($units[$unit])
       						$row['usage'][$type][$unit]['value'] = $units[$unit]['value'] ? ($units[$unit]['value'] * $size)  : 0;
				
				/*
				$ratesheet = Ratesheet::getDetails($row['rate'], false, false, true);
				$i->og_type = $row['type'];
				$i->calcPeriod($row['start'], $row['end'], $row['start'], $row['end']);
				
				$row['ratesheet'] = $i->usage($ratesheet, $row['usage'], $size, $class));
				*/
				
				$data[] = $row;
			}
			$this->view->data = $data;
	
			header('Content-type: application/vnd.ms-excel');
			header('Content-disposition: attachment; filename=MijnVaanster_export.csv');
		}

		public function objectComponentsVersionsAction(){
			$this->view->Breadcrumbs()
				->addCrumb('Factuurcomponenten versies');

			if(!($project_id = $this->_getParam('project'))) return;

			$select = db()->select()
				->from(array('o' => 'objects'), array('object' => 'id'))
				->joinLeft(array('og' => 'objectgroup'), 'og.id = o.objectgroup', false)
				->joinLeft(array('a' => 'address'), 'a.type_id = o.id AND a.type = "object"', array('address' => 'CONCAT(a.address, " ", a.`number`)'))
				->joinLeft(array('ocv' => 'objects_components_versions'), 'ocv.object = o.id', array('future' => 'MAX(date)', 'number' => 'COUNT(date)'))
				->group('o.id')
				->where('og.project = ?', $project_id);

			$versions = db()->fetchAll($select);

			$this->view->list = $this->view->listView($versions)

				->setTypes(array(
					'address' => array('title' => 'Object', 'column' => 'address', 'width' => 'large'),
					'number' => array('title' => 'Aantal', 'width' => 'small right'),
					'current' => array('title' => 'Huidige', 'width' => 'small right'),
					'future' => array('title' => 'Toekomstige versie', 'width' => 'small right'),

				))

				->addFormat('address', 'object_support_link')

				->setOptions(array(
					// disable edit and delete buttons if row['done'] is true
					'item_title' => 'Versie'
				))

				->render($this);
		}


		public function exportAction() {
				$this->disableView();
				$p = new Project();
				
				$this->view->data = $p->dataForExport();
				
				header('Content-type: application/vnd.ms-excel');
				header('Content-disposition: attachment; filename=MijnVaanster_export.csv');
				$this->render('export_csv');
		}

		public function checkIdentifierAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$projectid = $_POST['projectid'] ? $_POST['projectid'] : false;

			echo json_encode(Project::checkIdentifier($_POST['corporation'], $_POST['identifier'], $projectid));
		}
		
		public function applyStartdateAction(){
			$this->disableView();
			
			if (!$this->_getParam('project'))
				die();
				
			$p = new Project();
			$p->applyStartdate($this->_getParam('project'));
		
			$this->_redirect('project/details', array('id' => $this->_getParam('project')));
		}

		public function setProjectToNotFinalDatesAction() {
			if (!$this->_getParam('id'))
				die();

			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$db = db();
			$uo = new Objectusers();

			$select = $db->select()
				->from(array('og' => 'objectgroup'), false)
				->joinLeft(array('o' => 'objects'), 'o.objectgroup = og.id', false)
				->joinLeft(array('uo' => 'users_objects'), 'uo.object = o.id')
				->where('og.project = ?', $this->_getParam('id'))
				->where('uo.id');
			$rows = $db->fetchAll($select);
			$objects = array();

			foreach ($rows as $usersobject) {
				/*
				 $row = $uo->fetchRow($uo->select()->where('id = ?', $usersobject['id']));

				 $row->from = '2010-06-21';
				 $row->save();
				 */
			}

		}

		public function testAction() {
			if (!$this->_getParam('id'))
				die();

			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$db = db();
			$uo = new Objectusers();

			$select = $db->select()
				->from(array('og' => 'objectgroup'), false)
				->joinLeft(array('o' => 'objects'), 'o.objectgroup = og.id', false)
				->joinLeft(array('uo' => 'users_objects'), 'uo.object = o.id AND uo.customer != 0', false)
				->joinLeft(array('uo2' => 'users_objects'), 'uo2.object = uo.object AND uo2.customer = 0 AND uo2.from = uo.from')
				->where('og.project = ?', $this->_getParam('id'))
				->where('uo.id')
				->where('uo2.id');
			$rows = $db->fetchAll($select);

			foreach ($rows as $usersobject) {

				$row = $uo->delete('id = ' . $usersobject['id']);

			}

		}

		public function convertProfileAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$profile = new UsageProfile();
			$profile->convert();
		}


		/**
		 * Details
		 */
		public function detailsAction() {
			if (is_numeric($this->_getParam('id'))) {

				if(!$this->validateProjectAllowed($this->_getParam('id'))) return;

				$project = new Project();
				$object = new Object();
				$ratesheets = new Ratesheet();
				$ratesheets2 = new Ratesheet2();
				$s_model = new Sets();
				$ps = new TechnicalSpecs('project', $this->_getParam('id'));

				$this->view->ratesheets2 = $ratesheets2->forProject($this->_getParam('id'));
				$this->view->ratesheets = $ratesheets->getList($this->_getParam('id'));
				$this->view->id = $this->_getParam('id');
				$this->view->project = $projectDetails = $project->getDetails($this->_getParam('id'));
				$this->view->specs = $ps->getArray();
				$this->view->autotasks_status = AutotaskApplied::getStatus(1, 'project', $this->_getParam('id'));
				$this->view->current_set = $s_model->getCurrentApplied($this->_getParam('id'));

				$this->view->objectgrouplijst = $object->getObjectGroups($this->_getParam('id'));

				$this->view->vidiiWordpressImportIsEnabled = $this->importingIntoVidiiWordpressIsPossible( $projectDetails );

				$this->view->Breadcrumbs()->addCrumb($this->view->project['general']['name'], 'project/details/id/' . $this->_getParam('id') . '/');
			}
		}

		private function importingIntoVidiiWordpressIsPossible( array $projectDetails ) {
			return Settings::get( 'modules_vidii' ) && $projectDetails['vidii']['vidii_enabled']
			       && filter_var( $projectDetails['vidii']['vidii_wordpress_import_trigger_url'], FILTER_VALIDATE_URL )
			       && filter_var( $projectDetails['vidii']['vidii_wordpress_import_process_url'], FILTER_VALIDATE_URL )
			       && filter_var( $projectDetails['vidii']['vidii_wordpress_availability_trigger_url'], FILTER_VALIDATE_URL )
			       && filter_var( $projectDetails['vidii']['vidii_wordpress_availability_process_url'], FILTER_VALIDATE_URL );
		}
		
		public function setSpecsAction(){
			$this->disableView();
			
			$ps = new TechnicalSpecs('project', $this->_getParam('project'));
			$post = $this->getRequest()->getPost();

			$ps->set(json_decode($post['specs']));
		}
		
		public function specsSuggestAction(){
			$this->disableView();
			
			$ps = new TechnicalSpecs('project', $this->_getParam('project'));
			$post = $this->getRequest()->getPost();
			$type = $this->_getParam('type') ? $this->_getParam('type') : false;
			
			echo json_encode($ps->suggest($this->_getParam('suggest'), $post['value'], $type));
		}
		
		public function specsDeleteAction(){
			$this->disableView();
			
			$ps = new TechnicalSpecs('project', $this->_getParam('project'));
			$post = $this->getRequest()->getPost();
			
			$ps->delete($post['type'], $post['value']);
		}
		
		public function getCategoryTitles(){
			$categories = array();
			$select = db()->select()
				->from(array('ppct'=>'photo_categories_titles'), array('*'))
				->order('ppct.title');
				
			foreach(db()->fetchAll($select) as $item)
				$categories[$item['photo_category']][] = $item;
			
			return $categories;
		}
		
		public function invitationsAction() {
			if (!is_numeric($this->_getParam('project')))
				die();

			$p = new Project();
			$this->view->invitations = $p->getInvitations($this->_getParam('project'));
			$this->view->id = $this->_getParam('project');

			$this->view->Breadcrumbs()->addCrumb('Project', 'project/details/id/' . $this->_getParam('project') . '/')->addCrumb('Uitnodiging persoonlijke pagina');
		}

		/**
		 * [mailInvitationsAction description]
		 * @return [type] [description]
		 */
		public function mailInvitationsAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (!is_numeric($this->_getParam('project')))
				die();

			$p = new Project();
			if (!$p->getInvitations($this->_getParam('project'))) {
				$p->mailInvitations($this->_getParam('project'),$this->_getParam('invitation'));
			}
			$this->_helper->redirector('invitations', 'project', false, array('project' => $this->_getParam('project')));
		}



		/**
		 * [mailInvitationsAction description]
		 * @return [type] [description]
		 */
		public function mailObjectselectAction() {
			
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (!is_numeric($this->_getParam('project')))
				die();

			$p = new Project();
			
			$p->mailObjectselection($this->_getParam('project'),$this->_getParam('invitation'));
			
			$this->_helper->redirector('invitations', 'project', false, array('project' => $this->_getParam('project')));
		}

		/**
		 * Edit
		 */
		public function editAction() {

			$this->view->headScriptHashed()
				->appendFile('media/javascript/datepicker.js')
				->appendFile('media/javascript/image-upload.js')
				->appendFile('media/javascript/warning2.js')
				->appendFile('media/javascript/popup.js')
				->appendFile('media/javascript/multiSelectFilter.js');

			$projectId = $this->_getParam('id');

			$this->view->autotasks_status = $projectId ? AutotaskApplied::getStatus(1, 'project', $projectId) : false;
			
			//create form
			$projectform = new Form($this, 'project/edit');

			$projectform->project->setLabel('Opslaan');

			// corporate data
			$corporation = new Corporation();
			$projectform->general->corporation->setMultiOptions($corporation->getSimpleList(false, 5));
			$corporations = ['default' => 'Gelijk aan juridische entiteit project'] + ['Afwijkend' => $corporation->getSimpleList(false, 5)];
			$projectform->general->investor_corporation->setMultiOptions($corporations);
            $projectform->general->rental_corporation->setMultiOptions($corporations);


			$templatesComm = ContractTemplate::getTemplateTitlesForProject($this->_getParam('id'), 'contract_template_commercial');
			$templatesPriv = ContractTemplate::getTemplateTitlesForProject($this->_getParam('id'), 'contract_template_private');
			$templatesInvite = ContractTemplate::getTemplateTitlesForProject($this->_getParam('id'), 'contract_template_invite');


			$templatesComm[0] = (count($templatesComm) == 0) ?
				'Geen contract templates geupload':
				'Contract niet ingesteld - Contracteren niet mogelijk';


			$templatesPriv[0] = (count($templatesPriv) == 0) ?
				'Geen contract templates geupload':
				'Contract niet ingesteld - Contracteren niet mogelijk';


			$templatesInvite[0] = (count($templatesInvite) == 0) ?
			'Geen uitnodiging templates geupload':
			'Uitnodiging niet ingesteld - Uitnodigen niet mogelijk';
			
			ksort($templatesPriv);
			ksort($templatesComm);
			ksort($templatesInvite);

			$projectform->roomselector->contract_template_commercial->setMultiOptions($templatesComm);
			$projectform->roomselector->contract_template_commercial_small->setMultiOptions($templatesComm);
			$projectform->roomselector->contract_template_private->setMultiOptions($templatesPriv);

			$projectform->roomselector->contract_template_invite->setMultiOptions($templatesInvite);

			// user data
			$project = new Project();
			$ca = new CollectionAgency();
			$details = $this->view->data = $projectId ? $project->getDetails($projectId) : array();

			$projectform->general->ca->options = array(0 => 'Nog niet ingesteld') + $ca->getSelectList();


            $ratesheets = new Ratesheet();
            $ratesheetoptions = [];

            foreach($ratesheets->getList($project, false, $new) as $ratesheet)
                $ratesheetoptions[$ratesheet['id']] = $ratesheet['name'];

            $projectform->general->ratesheet->options = array(0 => 'Nog niet ingesteld') + $ratesheetoptions;

			$report = new InstallationReport();
			$projectform->general->investor_tech_report_building_id->setMultiOptions($report->getAvailableBuildingKeyValues([$projectId]));
			$projectform->general->investor_tech_report_building_id->setAttrib('disable', $report->getAvailableBuildingKeyValues([$projectId], true));
		
			$projectform->populate($details);

			//Check form
			if ($this->getRequest()->isPost() && $projectform->isValid($this->getRequest()->getPost())) {
					
				//post data
				$data = $projectform->getValues();

				$savedProjectId = $project->edit($data, $projectId);

                if ($data['invoice']['create_initial_project_runs'] === '1') {
                    $action_params = [
                        'project_id',
                        $savedProjectId,
                        'date',
                        $data['invoice']['startdate'],
                    ];

                    startWorker(
                        'create-initial-project-runs/' . implode('/', $action_params) . '/',
                        'project'
                    );
                }

				
				if($data['general']['date'] && $data['general']['date'] != '')
					AutotaskApplied::saveEvent(1, 'project', $savedProjectId, $data['general']['date']);

				if($projectId)
					$this->_redirect('project/details', array('id' => $projectId));
				else
					$this->_redirect('project/index');
			}

			$this->view->next_identifier = $projectId ? $project->nextAvailableIdentifier($details['general']['corporation'], $projectId) : 1;
			$this->view->project = $projectform;
			$this->view->projectid = $projectId;

			$ceapPlaceholder = Settings::get('invoice_consumption_estimate_adjust_percentage');
			$this->view->ceapPlaceholder = (float) ($ceapPlaceholder?: 0.0);

			if($projectId)
				$this->view->Breadcrumbs()
					->addCrumb($details['general']['name'], 'project/details/id/' . $projectId . '/')
					->addCrumb('Project bewerken', 'project/edit/id/' . $projectId . '/');
			else
				$this->view->Breadcrumbs()->addCrumb('Nieuw project toevoegen');

			$this->render('edit');
		}

        public function createInitialProjectRunsAction()
        {
            $this->disableView();

            $pid_lock = new PIDLock(
                'project/create-initial-project-runs',
                ['project_id' => $this->getParam('project_id')]
            );

            $create_run_service = new \Invoice\CreateInitialProjectRuns(
                $this->getParam('project_id'),
                $this->getParam('date')
            );

            $create_run_service->execute();

            $pid_lock->remove();
        }

		public function getNextAvailableIdentifierAction(){
			$this->disableView();

			$p = new Project();

			echo json_encode($p->nextAvailableIdentifier($this->_getParam('corporation'), $this->_getParam('project')));
		}

		/**
		 * Delete project
		 */
		public function deleteAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if (is_numeric($this->_getParam('id'))) {
				$project = new Project();
				$project->delete($this->_getParam('id'));
			}
			$this->_redirect('project/index');
		}

		public function exportcodesAction() {
			ini_set('memory_limit', '512M');
			
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			$project = new Project();

			$project->exportCodes($this->_getParam('id'), $this->_getParam('invitation'), $this->_getParam('stationary'));

			
		}

		/**
		 * Edit objects within this project
		 */
		/*
		 public function editobjectAction(){
		 if (is_numeric($this->_getParam('projectgroup'))){
		 //create form
		 $objectform = new Form($this, 'project/editobject');
		 $adduserform = new Form($this, 'user/add');

		 // user data
		 $object = new Object();
		 $project = new Project();

		 $objectlijst = array();

		 foreach ($project->getList() as $temp){
		 array_push($objectlijst, $temp);
		 }

		 //Check form
		 if($this->_getParam('project') && $objectform->isValid($this->getRequest()->getPost())){

		 //post data
		 $request = $this->getRequest()->getPost();
		 $object->save($request, $this->_getParam('projectgroup'), $this->_getParam('id'));
		 } else {
		 // populate form
		 $objectform->populate($object->getDetails($this->_getParam('id')));
		 }

		 //view
		 $this->view->projecten = $objectlijst;
		 $this->view->project = $objectform;

		 $this->render('editobject');
		 }
		 }	*/

		 
		/**
		 * newUsageProfile
		 */
		public function newusageprofileAction() {
			$this->disableView();
			
			$project = false;
			
			$u = new UsageProfile();
			foreach($u->getList(false, false, false, $project) as $type => $usageprofiles){
				if($type != 'project')
					continue;
					
					
				foreach($usageprofiles as $profile){
					if(!$profile['meter'])
						continue;
						
					$profiles[$profile['project']]['name'] = $profile['projectname'];
					$profiles[$profile['project']]['profiles'][] = $profile;
				}
				
			}
			
			$this->view->profiles = $profiles;
			$this->render('usageprofile/new');
		}
		
		/**
		 * newUsageProfile
		 */
		public function previewprofileAction() {
			$this->disableView();
			
			if(!$this->_getParam('id'))
				die();
			
			$u = new UsageProfile();
			
			$this->view->profile = $u->getDetails($this->_getParam('id'));
			$this->render('usageprofile/preview');
		}		
	
		public function removeCopyProfileAction() {
			$this->disableView();
			
			if(!$this->_getParam('id'))
				die();
			
			$u = new UsageProfile();
			$u->delete($this->_getParam('id'));
		}			

		public function usageprofileListAction(){
			$this->view->headLink()->appendStylesheet('media/style/project/usageprofile-list.css');

			$this->view->list = $this->view->modelListView('UsageProfiles', array('project' => $this->_getParam('project'), 'url-override' => 'project/usageprofile-list/'))
		
				->setTypes(array('name', 'type'))

				->addFormat('type', function($value){ return $value == 'commercial' ? 'Commercieel' : 'Particulier'; })

				->setOptions(array(
					'show_title' => false,
					'custom_class' => 'usageprofile-list',
					'item_title' => 'Gebruikersprofiel',
					'show_view_template_buttons' => false,
					'paginator_items_per_page' => 5
				))
				
				->addButtons(array(
	 				'details' => 'Details',
					'add' => 'Toevoegen',
					'edit' => 'Bewerken'
				), array('id' => 'id', 'project' => $this->_getParam('project')))

				->setFilters(array('name' => array('type' => 'input'), 'type' => array('type' => 'select')))
					
				->render($this);
		}

		public function usageprofileDetailsAction(){
			$u_model = new UsageProfiles();
			$p_model = new Projects();
			$this->view->profile = $u_model->getById($this->_getParam('id'));
			$this->view->project = $p_model->getById($this->_getParam('project'));

			$this->view->Breadcrumbs()
					->addCrumb('Projecten')
					->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id)
					->addCrumb('Gebruikersprofiel ' . $this->view->profile->name);
			

			$this->view->id = $this->_getParam('id');
		}

		public function usageprofileEditAction(){
			$this->view->headScriptHashed()->appendFile('media/javascript/datepicker.js');
			$this->view->headScriptHashed()->appendFile('media/javascript/project/usageprofile-edit.js');

			$u_model = new UsageProfiles();
			$p_model = new Projects();

			$this->view->profile = $this->_getParam('id') ? $u_model->getById($this->_getParam('id')) : false;
			$this->view->project = $p_model->getById($this->_getParam('project'));

			$this->view->Breadcrumbs()
					->addCrumb('Projecten')
					->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id);

			if($this->view->profile)
				$this->view->Breadcrumbs()->addCrumb('Gebruikersprofiel ' . $this->view->profile->name, 'project/usageprofile-details/id/' . $this->view->profile->id . '/project/' . $this->view->project->id);

			$this->view->Breadcrumbs()->addCrumb('Gebruikersprofiel bewerken/toevoegen');


			$this->view->form = $this->view->EditView($this, 'UsageProfiles', array('usageprofile' => $this->_getParam('id')))
								
					->setOptions(array(
					))
					
					->render($this);
		}	

		public function meterEditAction(){
			$u_model = new UsageProfiles();
			$p_model = new Projects();
			$this->view->profile = $u_model->getById($this->_getParam('usageprofile'));
			$this->view->project = $p_model->getById($this->_getParam('project'));
			$this->view->type = $this->_getParam('type');

			$this->view->Breadcrumbs()
					->addCrumb('Project')
					->addCrumb($this->view->project->name, 'project/details/id/' . $this->view->project->id)
					->addCrumb('Gebruikersprofiel ' . $this->view->profile->name, 'project/usageprofile-details/id/' . $this->view->profile->id . '/project/' . $this->view->project->id)
					->addCrumb('Gebruikersprofiel bewerken/toevoegen');

			$this->view->form = $this->view->EditView($this, 'Meters', array('id' => $this->_getParam('id')))
								
					->setOptions(array(
					))
					
					->render($this);
		}

		public function meterDeleteAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);

			if(!$this->_getParam('id')) return;

			$m_model = new Meters();
			$m_model->delete('id = ' . $this->_getParam('id'));

			$this->getHelper('Redirector')->gotoUrl($_SERVER['HTTP_REFERER']);
		}

		public function getUsageProfilesAction() {
			$up_model = new UsageProfiles();

			$type = $this->_getParam('Type') == 'commercieel' ? 'commercial'  : 'private';
			$project = $this->_getParam('project');	

			$projects = array();
            foreach ($up_model->getList(['type' => $type]) as $profile) {
                $projects[$profile->has('objectgroup') ? 'Dit project' : 'Nog niet gekoppeld'][] = $profile;
            }

			$this->view->profiles = $projects;
			$this->view->type = $type;
			$this->view->selected = $this->_getParam('Profiel');

			$this->_helper->layout->disableLayout();
		}

		/*

		public function addusageprofileAction() {
			
			$this->_helper->layout->disableLayout();
    
			$this->view->type = $type = $this->_getParam('Type');
			
			$this->view->project = $project = $this->_getParam('project');
			$this->view->ratesheet = $ratesheet = $this->_getParam('Tariefblad');
			$this->view->id = $id = $this->_getParam('id');
			$this->view->copy = $copy = $this->_getParam('copy') == 'false' ? false : $this->_getParam('copy');
			
			$usageprofile = new UsageProfile();
			$r = new Ratesheet();
			
			if($copy)
				$this->view->id = $id = $usageprofile->copyProfile($copy);
			
			
		    foreach ($r->getProducts($ratesheet) as $i => $item)
		    	$products[$i] = array('id' => $item['id'], 'title' => ucfirst($item['title']), 'in_rs' => $item['in_rs'] ? true : false);
			 
			 
			 
			$units = $r->getUnits($ratesheet);
			
			
			if (!$this->isAjaxRequest || (!$id && $this->_getParam('mode') == 'popup')){
				
				$this->view->profile = $this->_getParam('Profiel');
				$this->view->usageprofiles = $usageprofile->getList(false, $type, 10, $this->_getParam('project'));

				$this->render('usageprofile/pagina');
			
			} else {
				// form
				$usageprofileform = new Form($this, 'project/usageprofile');
				
				$this->view->products = $products;
				$this->view->units = $units;
					
				if (is_numeric($id)) {
					$data = $usageprofile->getFormDetails($id);

					$this->view->meters = $data['meters'];
					$usageprofileform->populate($data);
				}

				$this->view->form = $usageprofileform;
				// render
				$this->render('usageprofile/commercial');
			}
		}
		 */
		
		/**
		 * saveUsageProfile
		 */
		public function saveusageprofileAction() {
				
			$usageprofileform = new Form($this, 'project/usageprofile');
			
			if($this->getRequest()->isPost() && $usageprofileform->isValid($this->getRequest()->getPost())){
				$data = $usageprofileform->getValues();
				
				$meterdata = json_decode(stripslashes($data['meters']));
				$usageprofile = new UsageProfile();
				$usageprofile->save($this->_getParam('id'), $data, $meterdata);
			}
			
			$this->disableView();
		}

		/**
		 * deleteUsageProfile
		 */
		public function deleteusageprofileAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$usageprofile = new UsageProfile();
			$usageprofile->delete($_POST['id']);
		}


		/**
		 * Show the periods of time objects of a certain investor 
		 * were empty during a certain year.
		 * NOTE: this action is likely only used for a short period of time,
		 * so removing it if it is no longer in use might be tidy.
		 * 
		 * <AUTHOR> van Overbeeke
		 * 
		 * @return null this action just renders a page
		 */
		public function vacancyOverviewAction() {

			$investors = new Investors();

			$this->view->investorNames = $investors->getNamesById();
			$allObjects = array();

			foreach ($this->view->investorNames as $invId => $name) {
				$tempObjects = $investors->getObjects($invId);

				foreach ($tempObjects as &$object) {
					$object['investor_id'] = $invId;
					$object['investor_name'] = $name;
				}
				unset($object);

				$allObjects = array_merge($allObjects, $tempObjects);
			}

			$allProjects = array();
			$allByProject = array();

			foreach ($allObjects as $object) {
				$pId = $object['project_id'];
				$iId = $object['investor_id'];

				if (!isset($allByProject[$pId])) {
					$allByProject[$pId] = array(
						'name' => $object['project_name'],
						'investors' => array(),
					);
				}

				$allByProject[$pId]['investors'][$object['id']] = $object;

				if (!isset($allProjects[$pId])) {
					$allProjects[$pId] = $object['project_name'];
				}
			}

			// remove empty projects and sort them by name
			foreach ($allProjects as $key => &$value) {
				if (is_null($value) || $value == '') {
					unset($allProjects[$key]);
				}
			}

			natcasesort($allProjects);

			$this->view->projectNames = $allProjects;

			if (isset($_GET['project']) && isset($_GET['year'])) {
				$projId = $_GET['project'];
				$year = $_GET['year'];
				$obj = new Object();

				$objects = $allByProject[$projId];

				$investors = array();
				foreach ($objects['investors'] as $object) {
					if (!isset($investors[$object['investor_id']])) {
						$investors[$object['investor_id']] = array(
							'name'		=> $object['investor_name'],
							'objects'	=> array(),
						);
					}

					// get all relevant dates for this object
					$db = Zend_Db_Table::getDefaultAdapter();

					$select = $db->select()
						->from(array('uo' => 'users_objects'), array('customer', 'from', 'till'))
						->where('uo.object = ?', $object['id'])
						->where('uo.role = \'normal\'')
						->order('uo.from ASC')
					;

					$dates = $db->fetchAll($select);

					// remove all customers except the 'vacant' customer
					foreach ($dates as $key => $date) {
						if ($date['customer'] != 0) {
							unset($dates[$key]);
						}
					}

					// reindex the array to make sure all values are contiguous
					$dates = array_values($dates);

					for ($i=0; $i < count($dates); $i++) {
						if (!isset($dates[$i]['till'])) {
							$dates[$i]['till'] = date('Y-m-d');
						}

						// convert the from date to the right format
						$from = date('Y-m-d', (strtotime($dates[$i]['from'])));
						$fromDate = strtotime($from);
						$fromYear = substr($from, 0, 4);

						// convert the till date to the right format, adding 1
						// day so these dates fit the 'until and including' 
						// expectations
						$till = date('Y-m-d', (strtotime($dates[$i]['till'])));
						$tillDate = strtotime($till) + 86399;
						$tillYear = substr($till, 0, 4);

						// include only the dates that have some overlap with 
						// the given year
						if ( ($fromYear == $year || $tillYear == $year) 
								|| (intval($fromYear) < intval($year) && intval($tillYear) > intval($year))) {
							$tempObject = $object;

							$tempObject['fromDate'] = $from;
							$tempObject['tillDate'] = $till;
							$tempObject['nrOfDays'] = days_between($fromDate, $tillDate);

							// set the tapklasse to the right value
							$tempObject['tapklasse'] = '-';

							if(isset($tempObject['objectgroup_tapkl']) 
								&& strcmp($tempObject['objectgroup_tapkl'], '0') != 0) {
								$tempObject['tapklasse'] = 'CW'.$tempObject['objectgroup_tapkl'];
							}		

							if(isset($tempObject['tapkloverride']) 
								&& strcmp($tempObject['tapkloverride'], '0') != 0) {
								$tempObject['tapklasse'] = 'CW'.$tempObject['tapkloverride'];
							}

							$meters = $obj->getMeters($tempObject['id']);

							// set the meters-related values (hot & cold) to default or
							// their actual values (if available)
							$tempObject['heat'] = '-';
							$tempObject['cold'] = '-';
							foreach ($meters as $meter) {
								if ($meter['meterunit'] == 'KW' ) {
									if($meter['metertype'] == 'Warmte') {
										$type = 'heat';
									} else if ($meter['metertype'] == 'Koude') {
										$type = 'cold';
									} else {
										continue;
									}

									$tempObject[$type] = floatval($meter['metervalue']) * intval($tempObject['m2']);

									if (isset($meter['meteroverride'])) {
										$tempObject[$type] = $meter['meteroverride'];
									}

									$tempObject[$type] = $tempObject[$type].' '.$meter['meterunit'];
								}
							}

							$investors[$object['investor_id']]['objects'][] = $tempObject;
						}
					}
				}
			}

			$this->view->investors = $investors;
		}

		/**
		 * Small form that allows users to pick which support documents should
		 * be used as templates for the contracts that users of a particular 
		 * project
		 * 
		 * @return null (renders a view)
		 */
		public function setContractTemplatesAction() {
			$this->view->Breadcrumbs()
					->addCrumb('Project', '/project/details/id/'.$this->_getParam('project').'/')
					->addCrumb('Contract templates instellen');

			$templatesComm = ContractTemplate::getTemplateTitlesForProject($this->_getParam('id'), 'contract_template_commercial');
			$templatesPriv = ContractTemplate::getTemplateTitlesForProject($this->_getParam('id'), 'contract_template_private');

			// tell the user he has nothing to choose or give him the option to do so instead
			// of forcing him to pick one of the available ones
            if(count($templatesComm) == 0){
                $templatesComm[0] = 'Geen contract templates ingevoerd';
            } else {
                array_add_at_pos($templatesComm, [0 => 'Geen project-specifiek contract template gebruiken'], 0);
            }

            if(count($templatesPriv) == 0){
                $templatesPriv[0] = 'Geen contract templates ingevoerd';
            } else {
                array_add_at_pos($templatesPriv, [0 => 'Geen project-specifiek contract template gebruiken'], 0);
            }

			$this->view->form = $this->view->EditView(
					$this,
					'project/set-contract-templates',
					array(
						'project_id' => $this->_getParam('project'),
						'modeloverride' => 'Projects',
						'contractTemplatesCommercial' => $templatesComm,
						'contractTemplatesPrivate' => $templatesPriv,
					)
				)
				->render($this);
		}

		/**
		 * Manage (via the next few actions) which meters are used for a certain
		 * project in the calculations that create the technical graphs in the
		 * broker report pages.
		 */
		public function manageBrokerReportMetersAction() {
			$projectLib = new Project();
			$installationReport = new InstallationReport();
			$pmModel = new ProjectMeters();
			$projects = $projectLib->getSimpleList(true);

			$projectId = $this->_getParam('project');

			if(empty($projectId) || intval($projectId) <= 0) {
				$first = reset($projects);

				if(empty($first) || intval($first['id']) <= 0)
					$this->_redirect('dashboard/index', []);

				$projectId = $first['id'];
			}

			$this->view->projectId = $projectId;
			$this->view->projects = $projects;
			$this->view->values = ProjectMeters::$valuesStructure;

			$meterRows = $installationReport->getMeterDefinitionsForProject($projectId);
			$meters = [];
			foreach ($meterRows as $meterRow)
				$meters[$meterRow['PK_ID']] = $meterRow['DESCRIPTION'];

			natcasesort($meters);
			$this->view->meters = $meters = !empty($meters)? $meters: ['-1' => 'Geen meters gevonden'];

			$specVals = $pmModel->matchAll(['project' => $projectId]);
			$specs = [];
			foreach ($specVals as $specVal) {
				$specs[$specVal['meter']][$specVal['value_ident']] = abs(floatval(str_replace(',', '.', $specVal['value']))) > 0?
					floatval(str_replace(',', '.', $specVal['value'])): null;
			}

			$this->view->specs = $specs;

			$project = Projects::get($projectId);
			if(intval($project['investor_tech_report_building_id']) > 0) {
				$buildingCodes = $installationReport->getBuildingKeyValues();
				$buildingCode = $buildingCodes[$project['investor_tech_report_building_id']];
			}

			$this->view->buildingCode = (!empty($buildingCode) && trim($buildingCode) !== false)?
				$buildingCode: '-';
		}

		/**
		 * Save the meter values for a certain project
		 */
		public function saveBrokerReportMeterValuesAction() {
			$this->_helper->layout->disableLayout();
			$this->_helper->viewRenderer->setNoRender(true);
			$pmModel = new ProjectMeters();

			$meterValues = $this->_getParam('meterValues');

			if(empty($meterValues) || !is_array($meterValues) || intval(key($meterValues)) <= 0)
				dieWithStatuscode();

			$projectId = key($meterValues);
			$meterValues = reset($meterValues);

			foreach ($meterValues as $meterId => $values) {
				if(intval($meterId) <= 0)
					continue;

				foreach ($values as $ident => $value) {
					$pmRow = $pmModel->matchRow([
						'project' => $projectId,
						'meter' => $meterId,
						// 'value_ident' => '', // TODO: checken of rekening moet houden met meer dan 1 meter per ident
						'value_ident' => $ident,
					]);

					$pmRow = $pmRow?: $pmModel->createRow();

					$pmModel->setFields($pmRow, [
						'project' => $projectId,
						'meter' => $meterId,
						// 'value_ident' => '', // TODO: checken of rekening moet houden met meer dan 1 meter per ident
						'value_ident' => $ident,
						'value' => $value,
					]);

					$pmRow->save();
				}
			}
		}

		public function vidiiUpdateWordpressAction(  ) {
			$projectId = $this->_getParam( 'project' );
			startWorker("publish-project/projectId/{$projectId}", 'cronjob_vidii', 'direct', true);
			$this->_redirect('project/details', [ 'id' => $projectId ] );
		}
	}
