<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="nl">
	<head>
		<base href="<?= $this->secure ? 'https://' : 'http://' ?><?= $this->server ?><?=$this->baseDir ?>/" />
<?
	require 'Minify/Build.php';
	$groups = require('media/minifyGroups.php');
	$cssBuild = new Minify_Build($groups['css']);
	$cssPrint = new Minify_Build($groups['cssprint']);
	//$jsBuild = new Minify_Build($groups['js']);

	if(DEBUGGING){
		foreach($groups['css'] as $group)
			$this->headLink()->appendStylesheet($group/*.'?'.filemtime($group)*/, 'screen, print');

		foreach($groups['cssprint'] as $group)
			$this->headLink()->appendStylesheet($group/*.'?'.filemtime($group)*/, 'print');				
	} else {
		$this->headLink()->appendStylesheet($cssBuild->uri($this->baseDir.'/minify/css'), 'screen, print');
		$this->headLink()->appendStylesheet($cssPrint->uri($this->baseDir.'/minify/cssprint'), 'print');
	}
	
	$this->headScript()->prependFile('media/javascript/mootools.js');
	$this->headScript()->appendFile('media/javascript/mootools.more.js');
	$this->headScript()->appendFile('media/javascript/mootips.js');
	$this->headScript()->appendFile('media/javascript/onhashchange.js');
	$this->headScript()->appendFile('media/javascript/functions.js');
	$this->headScript()->appendFile('media/javascript/warning.js');
	$this->headScript()->appendFile('media/javascript/hint.js');
	$this->headScript()->appendFile('media/javascript/completer.js');
	$this->headScript()->appendFile('media/javascript/observer.js');
	$this->headScript()->appendFile('media/javascript/nicedit.js');
	$this->headScript()->appendFile('media/javascript/tags/GrowingInput.js');
	$this->headScript()->appendFile('media/javascript/tags/TextboxList.js');
	$this->headScript()->appendFile('media/javascript/tags/TextboxList.Autocomplete.js');
?>
		<?= $this->headLink() ?>
		<style type="text/css">

body {
	width: auto;
}

		</style>
        <script type="text/javascript">
            var ah;
        </script>

	</head>
	<body class="<?=$this->user_type?><?=$this->user_type == 'broker' ? ' tenant' : ''?> popup">
		<?= $this->headScript() ?>
		<?=$this->layout()->content?>
	</body>
</html>
