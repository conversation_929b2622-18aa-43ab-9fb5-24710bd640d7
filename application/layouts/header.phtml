<?  // make head title
    $headTitle = null;

    if (trim(loginManager::data()->project['roomselector_projectname']) != false)
        $headTitle = loginManager::data()->project['roomselector_projectname'];
    else
        $headTitle = Settings::get('portal_website') ? Settings::get('portal_website') : $this->software->url;

    $this->headTitle($headTitle);
    $this->headTitle()->setSeparator(' - ');
?>

	<head>
		<meta http-equiv="Content-type" content="text/html; charset=UTF-8" useOmniboxxPlugin="true" />
		<meta name="useOmniboxxPlugin"/>
		<meta charset="UTF-8">
		<?= $this->headTitle() ?>
		<base href="<?= $this->secure ? 'https://' : 'http://' ?><?= $this->server ?><?=$this->baseDir ?>/" />
<?
		$cssBuild = new Minify_Build($this->minifyGroups['css']);
		$cssPrint = new Minify_Build($this->minifyGroups['cssprint']);
		$jsBuild = new Minify_Build($this->minifyGroups['js']);

if((in_array($usertype, ['tenant', 'registrant', 'registrant', 'investor','company']) || loginManager::data()->rights == 'call_center') && Settings::get('modules_tenantLogin_new_styling')):
	$cssTenant = new Minify_Build($this->minifyGroups['css_portal']);
    $this->headLink()->prependStylesheet($cssTenant->uri($this->baseDir.'/minify/css_portal', false), 'screen, print');
else:
		$this->headLink()->prependStylesheet($cssBuild->uri($this->baseDir.'/minify/css', false), 'screen, print');
		$this->headLink()->prependStylesheet($cssPrint->uri($this->baseDir.'/minify/cssprint', false), 'print');
endif;

		if(!$this->noJS)
			$this->headScript()->prependFile($jsBuild->uri($this->baseDir.'/minify/js'));

		$this->headScript()->prependFile('api/get-settings/');

?>
		<?= $this->headLink() ?>
		<?= $this->headScriptHashed() ?>

        <link href="media/fonts/font-awesome/css/font-awesome.css" media="screen" rel="stylesheet" type="text/css" />

        <?php /** empty fav icon, to prevent the browser fron trying to fetch favicon.ico */ ?>
        <link rel="icon" href="data:;base64,iVBORw0KGgo=">

	</head>
