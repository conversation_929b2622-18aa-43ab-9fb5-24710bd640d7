<? $usertype = $this->login->rights ? $this->login->rights : 'guest'; ?>
<? $usertype = $this->login->rights == 'company'? 'tenant' : $usertype ?>
<? $usertype = $this->tempRights ? $this->tempRights : $usertype; ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="nl">

<?  require('application/layouts/parts/hybrid/header.phtml'); ?>
<?  require('application/layouts/parts/hybrid/body.phtml'); ?>

</html>
