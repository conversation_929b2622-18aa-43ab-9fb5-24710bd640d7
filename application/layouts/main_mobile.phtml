    <? $jsBuild = new Minify_Build($this->minifyGroups['js']);
      $this->headScript()->prependFile($jsBuild->uri($this->baseDir.'/minify/js')); ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="nl">
  <head>
    <meta charset="utf-8">
    <title>Omniboxx</title>

	<base href="<?= $this->secure ? 'https://' : 'http://' ?><?= $this->server ?><?=$this->baseDir ?>/" />    

    <!-- Sets initial viewport load and disables zooming  -->
    <meta name="viewport" content="initial-scale=1, maximum-scale=1">

    <!-- Makes your prototype chrome-less once bookmarked to your phone's home screen -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">

<? if(!$this->disableRatchet): ?>
    <!-- Include the compiled Ratchet CSS -->
    <link href="media/ratchet/css/ratchet.css" rel="stylesheet">

    <!-- Include the compiled Ratchet JS -->
    <script src="media/ratchet/js/ratchet.js"></script>
<? else: ?>
    <link href="media/ratchet/css/fallback.css" rel="stylesheet">
 <? endif; ?>

  </head>
  <body>
<?= $this->layout()->content ?>
  </body>
</html>