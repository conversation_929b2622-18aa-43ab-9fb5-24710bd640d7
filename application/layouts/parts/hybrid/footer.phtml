<?
if (acl()->hasRole('admin') || acl()->hasRole('internal')):
    require 'application/layouts/parts/hybrid/footer/internal.phtml';
elseif(acl()->hasRole('tenant') && $this->user_type == 'tenant' || acl()->hasRole('broker')):
    require 'application/layouts/parts/hybrid/footer/tenant.phtml';
elseif(acl()->hasRole('guest')):
    require 'application/layouts/parts/hybrid/footer/guest.phtml';
endif;

if(DEBUGGING && $this->profiler):
    require('application/layouts/parts/hybrid/footer/debugging.php');
endif;
