<?
if (acl()->hasRole('admin') || acl()->hasRole('internal')):
    require 'application/layouts/parts/hybrid/topbars/internal.phtml';
elseif (acl()->hasRole('broker')):
    require 'application/layouts/parts/hybrid/topbars/broker.phtml';
elseif(( (acl()->hasRole('tenant') && $this->user_type == 'tenant') || acl()->hasRole('broker')) && loginManager::data()->info['company_type']['technical_contact'] !== '1'):
     require 'application/layouts/parts/hybrid/topbars/tenant.phtml';
elseif(acl()->hasRole('registrant')):
    require 'application/layouts/parts/hybrid/topbars/registrant.phtml';
elseif(loginManager::data()->rights == 'company' && loginManager::data()->info['company_type']['technical_contact'] === '1'):
    require 'application/layouts/parts/hybrid/topbars/company.phtml';
elseif(acl()->hasRole('guest')):
    require 'application/layouts/parts/hybrid/topbars/guest.phtml';
endif;

if(acl()->hasRole('tenant') && $this->user_type == 'tenant' && loginManager::data()->info['company_type']['technical_contact'] !== '1'):
    require 'application/layouts/parts/hybrid/topbars/navigation_menus/tenant.phtml';
elseif(acl()->hasRole('registrant') && $this->user_type == 'registrant' && loginManager::data()->info['company_type']['technical_contact'] !== '1'):
    require 'application/layouts/parts/hybrid/topbars/navigation_menus/registrant.phtml';
//elseif (acl()->hasRole('broker')):
//    require 'application/layouts/parts/hybrid/topbars/navigation_menus/broker.phtml';
elseif (loginManager::data()->rights == 'company' && loginManager::data()->info['company_type']['technical_contact'] === '1') :
    require 'application/layouts/parts/hybrid/topbars/navigation_menus/company.phtml';

elseif (loginManager::data()->rights == 'call_center' && loginManager::data()->info['company_type']['call_center'] === '1') :
    require 'application/layouts/parts/hybrid/topbars/tenant.phtml';
    require 'application/layouts/parts/hybrid/topbars/navigation_menus/call_center.phtml';
endif;
?>
