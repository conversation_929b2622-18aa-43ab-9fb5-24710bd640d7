				<script type="text/javascript">
					var ah;

					window.addEvent('domready', function(){
<? if(isAllowed('rights:edit')): ?>
						new Acl(<?= Zend_Json::encode($this->pageInfo); ?>);
<? endif; ?>
						ah = new AutoHints();
					});

				</script>

<? if($this->messages): ?>
				<script type="text/javascript">
					window.addEvent('domready', function(){
						$('closeflashmessage').addEvent('click', function(){
							$('flashmessage').setStyle('display', 'none');
						});
					});
				</script>
				<ul class="flashmessage message" id="flashmessage">
					<li class="close"><a id="closeflashmessage">[x]</a></li>
<? foreach($this->messages as $message): ?>
					<li><?= $message ?></li>
<? endforeach; ?>
				</ul>
<? endif; ?>
