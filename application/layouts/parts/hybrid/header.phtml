
	<head>
		<meta http-equiv="Content-type" content="text/html; charset=UTF-8" useOmniboxxPlugin="true" />
        <meta name="robots" content="noindex"/>
		<meta name="useOmniboxxPlugin"/>
		<meta charset="UTF-8">
		<?= $this->headTitle() ?>
		<base href="<?= $this->secure ? 'https://' : 'http://' ?><?= $this->server ?><?=$this->baseDir ?>/" />

<? if($this->new_layout === true) {
		$cssBuild = new Minify_Build($this->minifyGroups['css_minton']);
		$cssPrint = new Minify_Build($this->minifyGroups['cssprint']);
		$jsBuild = new Minify_Build($this->minifyGroups['js']);
}  else {
         $cssBuild = new Minify_Build($this->minifyGroups['css']);
         $cssPrint = new Minify_Build($this->minifyGroups['cssprint']);
         $jsBuild = new Minify_Build($this->minifyGroups['js']);
}


if(in_array($usertype, ['tenant', 'registrant', 'investor']) || loginManager::data()->rights == 'call_center' && Settings::get('modules_tenantLogin_new_styling')):
	$cssTenant = new Minify_Build($this->minifyGroups['css_portal']);
    $this->headLink()->prependStylesheet($cssTenant->uri($this->baseDir.'/minify/css_portal', false), 'screen, print');
else:
    if($this->new_layout === true) {
        $this->headLink()->prependStylesheet($cssBuild->uri($this->baseDir.'/minify/css_minton', false), 'screen, print');

    } else {
        $this->headLink()->prependStylesheet($cssBuild->uri($this->baseDir.'/minify/css', false), 'screen, print');

    }
	$this->headLink()->prependStylesheet($cssPrint->uri($this->baseDir.'/minify/cssprint', false), 'print');
endif;


if (!$this->noJS) {
    $this->headScript()->prependFile($jsBuild->uri($this->baseDir . '/minify/js'));
}

include "settings.phtml";
?>

		<?= $this->headLink() ?>
		<?= $this->headScriptHashed() ?>

        <link rel="icon" type="image/x-icon" href="media/images/favicon.ico">
	</head>
