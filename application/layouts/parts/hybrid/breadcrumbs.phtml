
<? $breadcrumbs = $this->breadcrumbs()->toArray(); ?>

<? if (is_array($breadcrumbs)){ ?>
<ul class="breadcrumbs">
<? foreach($breadcrumbs as $i => $breadcrumb){ ?>
	<li>
<? if ($breadcrumb['url'] && (!$breadcrumb['last'] || $i == 0)){ ?>
		<a href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['title'] ?></a>
<? } else { ?>
		<span><?= $breadcrumb['title'] ?></span>
<? } ?>

<? if (!$breadcrumb['last']){ ?>
		<span>&raquo;</span>
<? } ?>
	</li>
<? } ?>
</ul>
<? } ?>
