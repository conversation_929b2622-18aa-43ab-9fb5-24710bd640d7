<div class="navigation">
	<ul id="navigatie">
        <? if (
            (!acl()->hasRole('broker')) &&
 
            (loginManager::data()->info['first'] != '1' || loginManager::data()->rights == 'registrant' )
        ): ?>
			<li <?=$this->pageInfo['action'] == 'dashboard'   ? ' class="active"' : ''?>><a href="user/dashboard/"><?= (translate()->_('home')); ?></a></li>
			<? foreach ((array) $this->menuoptions as $menuoption => $menuitem ) { ?>

		<? 
		$controller = 'user';
		if(strpos($menuitem, '/') !== false){
			$controller = substr($menuitem, 0, strpos($menuitem, '/'));
			$menuitem = substr($menuitem, strpos($menuitem, '/')+1);
		}
		?>
				<li <?=$this->pageInfo['action'] == $menuitem   ? ' class="active"' : ''?> ><a href="<?= $controller ?>/<?=$menuitem?>/"><?= ucfirst(translate()->_($menuitem)); ?></a></li>
			<? }?>

        <?php if (Settings::get('module_reception_telephone_support_enabled') && 'company' == loginManager::data()->rights) { ?>
            <li <?=$this->pageInfo['action'] == 'reception-instructions'   ? ' class="active"' : ''?>><a href="reception-telephone-support_voip/reception-instructions">Receptie instructies</a></li>
            <li <?=$this->pageInfo['action'] == 'employee-instructions'   ? ' class="active"' : ''?>><a href="reception-telephone-support_voip/employee-instructions">Medewerker instructies</a></li>
        <?php } ?>

		<? if($this->is_migrating && Settings::get('general_company_shortname') != 'ravel'): ?>
		 		<li><a href="migration/meters/">Eindstand doorgeven</a></li>
		<? endif; ?>


        <? if ((!$this->hideComplaintFunctionality === true || Settings::get('modules_tenantLogin_hide_complaints')) && !Settings::get('software_only_rental') && !loginManager::data()->rights == 'registrant') {?>
                <li <?=$this->pageInfo['action'] == 'by-user'   ? ' class="active"' : ''?>><a href="complaint/by-user/"><?= translate()->_('add_complaint_defect_short') ?></a></li>
        <? } ?>
            <li <?=$this->pageInfo['action'] == 'contact'   ? ' class="active"' : ''?>><a href="info/contact/"><?= (translate()->_('contact')); ?></a></li>
 
			<li <?=$this->pageInfo['action'] == 'logout'   ? ' class="active"' : ''?>> <a href="user/logout/"><?= (translate()->_('logout')); ?></a></li>	
		<? endif; ?>
	</ul>
</div>
