<li class="has-submenu">
    <a href="dashboard/index/">
        <i class="ti-dashboard"></i>
        <span>Dashboard</span>
    </a>
    <ul class="submenu">
        <? if (!Settings::get('software_only_rental')): ?>
            <li>
                <a href="dashboard/index/normal/true/">Algemeen</a>
            </li>
        <? endif; ?>
        <li>
            <a href="email_dashboard/">Email </a>
        </li>
        <? if (!Settings::get('software_only_rental') &&  (!Settings::get('omniboxx_start')) ): ?>
            <?
            // to be set on user edit screen for hiding financial info
            if (loginManager::data()->hidefinancial != 1 && !loginManager::hasProjectLimitedMenu()) { ?>
                <li>
                    <a href="dashboard/finance/">Financieel</a>
                </li>
            <? } ?>

        <? endif; ?>
            <li class="has-submenu">
                <a href="#">Meldingen</a>
                <ul class="submenu">
                    <li>
                        <a href="complaint/unfinished-work/for_complaint_type/malfunction_or_request/open_all/true">Open</a>
                    </li>
                    <li>
                        <a href="complaint/unfinished-work/for_complaint_type/malfunction_or_request/closed_only/true">Gesloten</a>
                    </li>
                    <li>
                        <a href="complaint/unfinished-work/for_complaint_type/malfunction_or_request">Open &
                            gesloten</a>
                    </li>

                </ul>
            </li>
            <li>
                <a href="complaint/unfinished-work/for_complaint_type/complaint/open_all/true">Klachten</a>
            </li>

        <li>
            <a href="dashboard/task-dashboard">Taken & prioriteiten</a>
        </li>
        <? if(!Settings::get('omniboxx_start')) { ?>
        <li>
            <a href="calendar/dashboard/"><?= ucfirst(translate()->_('calendar')); ?></a>
        </li>
        <? } ?>
    </ul>
</li>
