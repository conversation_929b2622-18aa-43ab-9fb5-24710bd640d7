<? if (Settings::get('modules_rental') && !Settings::get('omniboxx_start')): ?>
    <li class="has-submenu">
        <a>
            <i class="ti-angle-down"></i>
            <span><?= ucfirst(translate()->_('rental')); ?></span>
        </a>
        <ul class="submenu">

            <li>
                <a href="rental-dashboard/website-deals/"><?= ucfirst(translate()->_('website_requests')); ?> </a>
            </li>
            <? if (Settings::get('modules_rental_dashboard_interhouse')): ?>
                <li>
                    <a href="dashboard/rentali/">Dashboard </a>
                </li>
            <? else: ?>
                <? if (Settings::get('modules_rental_workflow_deals')): ?>
                    <? if (count($this->activeWorkflows) > 1): ?>
                        <li class="has-submenu">
                            <a><?= ucfirst(translate()->_('workflow_rental')); ?></a>
                            <ul class="submenu">
                                <? foreach ($this->activeWorkflows as $activeWorkflowId => $activeWorkflowName): ?>
                                    <li>
                                        <a href="rental-deals_workflow/list/workflow_id/<?= $activeWorkflowId ?>"><?= $activeWorkflowName ?></a>
                                    </li>
                                <? endforeach; ?>
                            </ul>
                        </li>
                        <li class="has-submenu">
                            <a><?= ucfirst(translate()->_('workflow_load')); ?></a>
                            <ul class="submenu">
                                <? foreach ($this->activeWorkflows as $activeWorkflowId => $activeWorkflowName): ?>
                                    <li>
                                        <a href="rental-deals_workflow/workflow/id/<?= $activeWorkflowId ?>"><?= $activeWorkflowName ?></a>
                                    </li>
                                <? endforeach; ?>
                            </ul>
                        </li>
                    <? else: ?>
                        <li>
                            <a href="rental-deals_workflow/list/"><?= ucfirst(translate()->_('workflow_rental')); ?></a>
                        </li>
                        <li>
                            <a href="rental-deals_workflow/workflow/"><?= ucfirst(translate()->_('workflow_load')); ?></a>
                        </li>
                    <? endif; ?>
                <? else: ?>
                    <li>
                        <a href="user/edit-crm/type/registrant"><?= ucfirst(translate()->_('add_lead')); ?></a>
                    </li>

                    <li>
                        <a href="dashboard/rental/"><?= ucfirst(translate()->_('deals_and_leads')); ?> </a>
                    </li>
                <? endif; ?>
                <li class="has-submenu">
                    <a> Leads</a>
                    <ul class="submenu">
                        <li>
                            <a href="user/find-lead/">Zoek lead</a>
                        </li>
                        <li>
                            <a href="rental-dashboard/leads/">Recent aangemaakt</a>
                        </li>
                        <li>
                            <a href="user/edit-crm/type/registrant">Lead invoeren</a>
                        </li>
                    </ul>
                </li>
            <? endif; ?>
            <li>
                <a href="calendar/dashboard/"><?= ucfirst(translate()->_('calendar')); ?></a>
            </li>
            <? if (\library\TiaraWonen\Service\ConnectionInfo::isNVMVestigingsNrAvedebalForCurrentUser()): ?>
                <li class="has-submenu">
                    <a>Funda</a>
                    <ul class="submenu">
<!--                        <li><a href="funda_project/list/">Projectoverzicht </a></li>-->
<!--                        <li><a href="funda_object-type/list/">Object type overzicht </a></li>-->
                        <li class="has-submenu">
                            <a>Objecten</a>
                            <ul class="submenu">
                                <li><a href="funda_object/list/">Objecten actueel</a></li>
                                <li><a href="funda_object/list-archived/">Objecten archief</a></li>
                            </ul>
                        </li>
                        <!--                            <li><a href="tiara_distribution">Distributie</a></li>-->
<!--                        <li><a href="funda_nvm-vestigingen/list">NVM Vestigingen</a></li>-->
<!--                        <li class="has-submenu">-->
<!--                            <a>Openhuizendagen</a>-->
<!--                            <ul class="submenu">-->
<!--                                <li><a href="tiara_openhuizendagen/object-list">Objecten</a></li>-->
<!--                                <li><a href="tiara_openhuizendagen/project-list">Projecten</a></li>-->
<!--                            </ul>-->
<!--                        </li>-->
                    </ul>
                <li>
                    <a href="tiara_distribution/">NVM Rapportage</a>
                </li>
                </li>
            <? endif; ?>



            <li class="has-submenu <? if (!Settings::get('wonen31_module')): ?> <? endif; ?>">
                    <a>Wonen31</a>
                    <? if (Settings::get('wonen31_module')): ?>
                    <ul class="submenu">
                        <li><a href="wonen31_property/list/">Objecten overzicht </a></li>

                        <li><a href="wonen31_lead/list/">Leads overzicht </a></li>
                    </ul>
                    <? endif; ?>
            </li>

            <li>
                <a href="rental/objects/"><?= ucfirst(translate()->_('listings')); ?> </a>
            </li>

            <li>
                <a href="object/add-single/type/rental"><?= ucfirst(translate()->_('add_object')); ?></a>
            </li>
            <? if (Settings::get('software_only_rental')): ?>
                <li>
                    <a href="object/move-single/"><?= ucfirst(translate()->_('move_object')); ?></a>
                </li>
            <? endif; ?>
            <li>
                <a href="external-objects/list"><?= ucfirst(translate()->_('third_party_listings')); ?></a>
            </li>
            <li>
                <a href="key-management_keyring"><?= ucfirst(translate()->_('key_management')); ?></a>
            </li>
            <li>
                <a href="rental/route-list"><?= ucfirst(translate()->_('rental_route')); ?></a>
            </li>
            <? if (Settings::get('modules_tenantLogin_contract_validating')): ?>
                <li>
                    <a href="user_contract_validate/list">Contractvalidatie-lijst</a>
                </li>
                <li>
                    <a href="user_contract_validate/investors-list">Investeerders handtekening</a>
                </li>
            <? endif; ?>

            <? if (Settings::get('modules_rental_workflow_deals')): ?>
                <li>
                    <a href="rental-deals/edit/"><?= ucfirst(translate()->_('add_manual_deal')); ?></a>
                </li>
            <? endif; ?>

            <li>
                <a href="rental-dashboard/website-deals-cancelled/">Geannuleerde website aanvragen </a>
            </li>
        </ul>
    </li>
<? endif; ?>
