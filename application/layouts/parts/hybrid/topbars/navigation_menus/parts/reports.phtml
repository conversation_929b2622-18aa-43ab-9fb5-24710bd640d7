<li class="has-submenu">
    <a>
        <i class="ti-stats-up"></i>
        <span>Overzichten</span>
    </a>
    <ul class="submenu">
        <?php if (Settings::get('modules_hospitality')): ?>
            <li class="has-submenu">
                <a>Hospitality</a>
                <ul class="submenu">
                    <li>
                        <a href="hospitality_activity-item/index/hide_expired/0">Activiteitenoverzicht</a>
                    </li>
                    <li>
                        <a href="hospitality_news-item/index/hide_expired/0">Nieuwsoverzicht</a>
                    </li>
                </ul>
            </li>
        <?php endif; ?>

        <li>
        <li class="has-submenu">
            <a><?= (Settings::get('resident_label') ? Settings::get('resident_label') : 'Huurders') ?>
            </a>

            <ul class="submenu">
                <?
                // to be set on user edit screen for hiding financial info
                if (loginManager::data()->hidefinancial != 1) { ?>
                    <li>
                        <a href="user/tenant-list/">Huurderslijst beknopt</a>
                    </li>
                    <li>
                        <a href="user/tenant-list-advanced/">Huurderslijst uitgebreid</a>
                    </li>
                    <li>
                        <a href="object/list-advanced/">Vastgoed en NAW huurders</a>
                    </li>
                <? } ?>
                <li>
                    <a href="report/departing/">Vertrekkende huurders</a>
                </li>
                <li>
                    <a href="report/departing/departed/true">Vertrokken huurders</a>
                </li>
                <li>
                    <a href="user/tenant-list-advanced/archived/true">Vertrokken huurders [uitgebreid]</a>
                </li>
                <li>
                    <a href="report/arriving/">Aankomende huurders</a>
                </li>

                <? /*
                    <li>
                        <a href="report/arriving-income/">Aankomende huurders inkomsten</a>
                    </li>
                    */ ?>

                <? if (!Settings::get('modules_deposit_korfine_enabled')): ?>
                    <li>
                        <a href="report/deposit/">Waarborgsomoverzicht</a>
                    </li>
                    <li>
                        <a href="report/deposit-payout/">Waarborguitkeringoverzicht</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('modules_deposit_korfine_enabled')): ?>
                    <li>
                        <a href="user/remaining-deposit-documents">Korfinevoorsteloverzicht</a>
                    </li>
                    <li>
                        <a href="user/deposit-payout-list/">Korfineopheffingoverzicht</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('modules_Loyalty4G_enabled')) : ?>
                    <li>
                        <a href="loyalty4g">Loyalty4G error overzicht</a>
                    </li>
                <? endif; // Hopibon -> Loyalty error overzicht ?>
                <li>
                    <a href="consumption/">Meterstanden</a>
                </li>
            </ul>
        </li>
        <?php if (Settings::get('modules_surveys_enabled') && Settings::get('modules_surveys_show_scores')) : ?>
            <li class="has-submenu">
                <a>Klanttevredenheid</a>
                <ul class="submenu">
                    <li>
                        <a href="surveys/overview-technical-contacts">Servicepartijen</a>
                    </li>
                    <li>
                        <a href="surveys/overview-support">Klantenservice</a>
                    </li>
                </ul>

            </li>
        <?php endif; ?>
        <li class="has-submenu">
            <a>Leegstand</a>
            <ul class="submenu">
                <li>
                    <a href="report/vacancies/">Actuele leegstand</a>
                </li>
                <li>
                    <a href="report/vacancy-report/">Leegstandrapportage</a>
                </li>
                <? if (Settings::get('modules_rental')): ?>
                <li>
                    <a href="rental/all-objects/ignorebranches/true">Aanbod NL </a>
                </li>
                <? endif; ?>
                <li>
                    <a href="object-status/list/">Status inactieve objecten</a>
                </li>
                <? if (Settings::get('usage_report')) : ?>
                    <li>
                        <a href="user/tenant-list-usage/">Bezetting</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('usage_report') == "uitzoeken") : ?>
                    <li>
                        <a href="components/report-occupation">Bezettingoverzicht</a>
                    </li>
                <? endif; ?>

            </ul>
        </li>
        <? if(Settings::get('for_third_party')) :?>
        <li class="has-submenu">
            <a>Beleggers</a>
            <ul class="submenu">
                <li>
                    <a href="investor/list-advanced/">Beleggeroverzicht</a>
                </li>
            </ul>
        </li>
        <? endif; ?>



        <li class="has-submenu">
            <a>Vastgoed</a>
            <ul class="submenu">
                <li>
                    <a href="object/list-advanced/">Objectenoverzicht</a>
                </li>
                <li>
                    <a href="object_woz/list">WOZ-overzicht</a>
                </li>
                <? if (Settings::get('extended_realEstate_overview')): ?>
                    <li>
                        <a href="report/custom-object-overview/">Uitgebreid objecten overzicht</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('modules_rental')): ?>
                    <li>
                        <a href="media-ordered/list">Bestelde media</a>
                    </li>
                <? endif; ?>
            </ul>
        </li>


        <li class="has-submenu">
            <a> Taken &amp; maps</a>
            <ul class="submenu">

                <li>
                    <a href="task/report/">Taken</a>
                </li>
                <? if(!Settings::get('omniboxx_start')) { ?>
                <li>
                    <a href="task/meeting/">Weekoverleg</a>
                </li>
                <? /*  deprecated
           			<li>
 <a href="task/overview/">Maps en taken</a>
 </li>
*/ ?>
                <li>
                    <a href="activities/list/type/task">Herhalende taken</a>
                </li>
                <? if (!loginManager::hasProjectLimitedMenu()): ?>
                    <li>
                        <a href="priority/users/">Prioriteiten</a>
                    </li>
                    <li>
                        <a href="quarterly/users/">Maps</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('software_type') == "energy"): ?>
                    <li>
                        <a href="auto-task/">Geautomatiseerde taken</a>
                    </li>
                <? endif; ?>

                <? if (Settings::get('mailings_enabled')): ?>
                    <li>
                        <a href="mailing/authorisation-list/">Mailings</a>
                    </li>
                <? endif; ?>
                <li>
                    <a href="email/review-scheduled-emails/">Gereedstaande automated mails</a>
                </li>
                <? //<li>
                //<a href="reminds/overview/">Betalingsherinneringen</a>
                //</li> //?>
                <? } ?>
            </ul>
        </li>


        <? if (!loginManager::hasProjectLimitedMenu() && !Settings::get('omniboxx_start')): ?>

            <li class="has-submenu">
                <a>Performance</a>
                <ul class="submenu">
                    <li>
                        <a href="dashboard/overview/">Taken</a>
                    </li>
                    <li>
                        <a href="report/customer-support/">Klantenservice </a>
                    </li>
                    <li>
                        <a href="report/customer-email/">E-mailafhandeling </a>
                    </li>
                </ul>
            </li>
        <? endif; ?>
        <?
        // to be set on user edit screen for hiding financial info
        if (loginManager::data()->hidefinancial != 1 && (!loginManager::hasProjectLimitedMenu())) { ?>
            </li>

            <li class="has-submenu">
                <a> Omzet</a>
                <ul class="submenu">
                    <li>
                        <a href="components/report-objectgroup/">Overzicht per objectgroep</a>
                    </li>
                    <li>
                        <a href="components/report-object/">Overzicht per object</a>
                    </li>
                    <li>
                        <a href="invoice_component-specification-report/">Omzetspecificatie component</a>
                    </li>
                    <li>
                        <a href="components/report-total/">Totaaloverzicht</a>
                    </li>
                    <li>
                        <a href="transaction/debtor-payment-overview">Debiteurenbetalingsoverzicht</a>
                    </li>
                    <? /*uitzoeken
                    <li>
 <a href="transaction/collection-agency-overview/">Incasso overzicht</a>
 </li>
                    */ ?>
                    <? if (Settings::get('vat_report')): ?>
                        <li>
                            <a href="components/vat-report/status/2">BTW verhuuroverzicht</a>
                        </li>
                    <? endif; ?>
                </ul>
            </li>

            <li class="has-submenu">
                <a> Financieel</a>
                <ul class="submenu">
                    <li>
                        <a href="transactions_import-files/list">Geuploade bankbestanden
                        </a>
                    </li>
                    <li>
                        <a href="indexes/list-used-index-types">Indexatietypes aankomende periode
                        </a>
                    </li>
                    <? if (Settings::get('testing_new_components_reports')): ?>
                    <li>
                        <a href="invoice/list-object-component-versions-amounts">Ingestelde facturatieversies
                        </a>
                    </li>
                    <? endif; ?>
                    <li>
                        <a href="invoice/list-object-component-versions-indexes">Ingestelde Indexatietypes
                        </a>
                    </li>
                    <li>
                        <a href="arrangement/">Betalingsregelingen</a>
                    </li>
                    <li>
                        <a href="investor/provision-overview">Beheerinkomstenoverzicht</a>
                    </li>
                    <? if (Settings::get('general_company_shortname') == 'tweel'): ?>
                        <li>
                            <a href="investor/details-overview">Beheerinkomstendetailoverzicht</a>
                        </li>
                    <? endif; ?>
                    <li>
                        <a href="credit-check/overview">Creditcheckoverzicht</a>
                    </li>
                    <li>
                        <a href="report/custom-object-overview">Kerndata vastgoed</a>
                    </li>
                    <li>
                        <a href="finance/overview/">Financieringsoverzicht</a>
                    </li>
                    <? if (in_array(Settings::get('general_company_shortname'), ['Omniboxx'])): ?>
                        <li>
                            <a href="report/budget-graphs/">Budget grafieken</a>
                        </li>

                    <? endif; ?>
                    <li>
                        <a href="transaction/get-report/showClosedTransactions/1/">Gesloten facturen</a>
                    </li>
                    <li>
                        <a href="indexes/send-overview">Verzonden huurverhogingen</a>
                    </li>
                    <li>
                        <a href="indexes/rent-limits/">Huurprijstabellen</a>
                    </li>
                    <? if (Settings::get('investor_vat_overview_enabled')): ?>
                        <li>
                            <a href="investor_vat_overview/">Belegger BTW overzicht</a>
                        </li>
                    <? endif; ?>
                    <li>
                        <a href="invoice_purchase_overview/validated-list">Gevalideerde inkoopfacturen</a>
                    </li>
                    <li>
                        <a href="invoice_sales_overview/finalized-list">Verzonden verkoopfacturen</a>
                    </li>
                </ul>

            </li>
            <? if (Settings::get('modules_kpi')): ?>
                <li class="has-submenu">
                    <a> KPI rapportages</a>
                    <ul class="submenu">
                        <li>
                            <a href="kpi-rapport_exploitation/rental-income">Exploitatie - Huuromzet</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_exploitation/costs">Exploitatie - Kosten</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_return/direct-return">Rendement - Direct rendement</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_return/free-cash-flow">Rendement - Free cashflow</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_return/indirect-return">Rendement - Indirect rendement</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_obligo/total-outstanding-bond">Obligo - Aflossing</a>
                        </li>
                        <li>
                            <a href="kpi-rapport_investment-value/list">Beleggingswaarde</a>
                        </li>
                    </ul>
                </li>
            <? endif; ?>
             <? if(!Settings::get('omniboxx_start')) { ?>
            <li>
                <a href="contract-variables/list">Template variabelen</a>
            </li>
                 <? } ?>
            <? if (Settings::get('general_company_shortname') == 'ravel'): ?>
                <li class="has-submenu">
                    <a>Debiteuren</a>
                    <ul class="submenu">
                        <li>
                            <a href="transaction/control-account/">Controleoverzicht</a>
                        </li>
                        <li>
                            <a href="transaction/unpayed-on-date/">Oudendomsoverzicht</a>
                        </li>
                        <li>
                            <a href="transaction/debtor-overview/">Exportoverzicht</a>
                        </li>
                    </ul>

                </li>
            <? endif; ?>
        <? } ?>
        <li class="has-submenu">
            <a> Techniek</a>
            <ul class="submenu">
                <li>
                    <a href="complaint/project-report/">Meldingen</a>
                </li>
                <li>
                    <a href="maintenance/overview/">Onderhoud overzicht</a>
                </li>
                <li>
                    <a href="maintenance/overview-archive/">Historisch onderhoud overzicht</a>
                </li>
                <li>
                    <a href="maintenance/technical-specs-list/">Technische specifcaties</a>
                </li>
                <li>
                    <a href="support-contacts-map/list/">Gekoppelde relaties</a>
                </li>
                <? if (Settings::get('modules_contracts') &&  (!Settings::get('omniboxx_start'))): ?>
                <li>
                    <a href="contract/list-yearly-costs/">Overzicht servicecontracten</a>
                </li>
                <? endif; ?>
                <? if (Settings::get('support_complaint_projects') ): ?>
                    <li>
                        <a href="complaint-projects/list">Werken</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('support_complaint_autorisation') ): ?>
                    <li>
                        <a href="complaint_budget-authorization/list">Autoriseren meldingen</a>
                    </li>
                    <li>
                        <a href="complaint_budget-authorization/list-all">Overzicht autorisatie vereist</a>
                    </li>
                <? endif; ?>
                <? if (Settings::get('energy_module') ): ?>
                    <li>
                        <a href="consumption/">Meterstanden</a>
                    </li>
                    <li>
                        <a href="consumption/overview">Meterstandenoverzicht</a>
                    </li>
                <? endif; ?>
            </ul>
        </li>
        <li class="has-submenu">
            <a>Ontbrekende invoerdata</a>
            <ul class="submenu">
                <li>
                    <a href="report/input/type/tenants">Ontbrekende
                        invoerdata <?= (Settings::get('resident_label') ? Settings::get('resident_label') :  'huurders') ?></a>
                </li>
                <li>
                    <a href="report/input/type/objects">Ontbrekende invoerdata objecten</a>
                </li>

            </ul>
        </li>
        <li class="has-submenu">
            <a>Documenten</a>
            <ul class="submenu">
                <li>
                    <a href="document/overview/">Totaaloverzicht</a>
                </li>
            </ul>

        </li>
        <li>
            <a href="contract-conditions/list-mapped/">Bijzondere bepalingen</a>
        </li>
        <li class="has-submenu">
            <a>Notities</a>
            <ul class="submenu">
                <li>
                    <a href="note/list-advanced/">Totaaloverzicht</a>
                </li>
                <li>
                    <a href="note/add-direct/">Toevoegen</a>
                </li>
            </ul>
        </li>

        <? if (!loginManager::hasProjectLimitedMenu()) { ?>
            <li>
                <a href="manage/post-list/">Postcorrespondentie</a>
            </li>
        <? } ?>
        </li>
        <? if (Settings::get('lucidchart_in_use')): ?>
            <li>
                <a href='diagram/list/'>Procedures</a>
            </li>
        <? endif; ?>
        <li class="has-submenu">
            <a>Inspecties</a>
            <ul class="submenu">
                <li>
                    <a href="inspection/list">Inspectielijst</a>
                </li>
                <li>
                    <a href="inspection/edit">Inspectie toevoegen</a>
                </li>
                <li>
                    <a href="inspection/list-defects">Inspectie gebreken</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="key-management_keyring">Sleutelbeheer</a>
        </li>


        <? if (Settings::get('mplus_enabled')): ?>
            <li>
                <a>Mplus koppeling</a>
                <ul class="submenu">
                    <li>
                        <a href="mplus/invoices/">Facturen-status</a>
                    </li>
                    <li>
                        <a href="mplus/payments/">Betalingen-status</a>
                    </li>
                </ul>
            </li>
        <? endif; ?>

        <? if (Settings::get('modules_tenantLogin_roomselector_manually_control_availability')): ?>
            <? if (Settings::get('modules_app_alert')): ?>
                <li class="has-submenu">
                    <a>Alert App</a>
                    <ul class="submenu">
                        <li>
                            <a href="object/alert-list">Objectbeschikbaarheid</a>
                        </li>
                    </ul>
                </li>

            <? else: ?>
                <li class="has-submenu">
                    <a>Vidii</a>
                    <ul class="submenu">
                        <li>
                            <a href="object/alert-list">Objecten beschikbaar maken</a>
                        </li>
                    </ul>
                </li>
            <? endif; ?>

        <? endif; ?>


    </ul>
</li>
