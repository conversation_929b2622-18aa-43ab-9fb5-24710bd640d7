<div class="navigation" style="display: none">
    <ul id="navigatie">
        <li <?= $this->pageInfo['action'] == 'welcome' ? ' class="active"' : '' ?> >
            <a href="broker/welcome"><?= (translate()->_('welcome')); ?></a>
        </li>
        <li <?= $this->pageInfo['action'] == 'objects' ? ' class="active"' : '' ?> >
            <a href="broker/objects"><?= (translate()->_('in_management')); ?></a>
        </li>

        <? $publishes = BrokerPublish::getPublishedForBroker(loginManager::data()->info['investor_company_id']); ?>
        <? if (BrokerPublish::isIpAddressAllowedForBroker($_SERVER['REMOTE_ADDR'],
                loginManager::data()->info['investor_company_id']) && !empty($publishes) && is_array($publishes)): ?>
            <li <?= $this->pageInfo['action'] == 'reports' ? ' class="active"' : '' ?> >
                <a href="broker/reports">Rapportages</a>
            </li>
        <? endif; ?>

        <?php if (Settings::get('brokerportal_show_emails')) : ?>
            <li <?= $this->pageInfo['action'] == 'email-list' ? ' class="active"' : '' ?> >
                <a href="broker/email-list"><?= (translate()->_('email-list')); ?></a>
            </li>
        <?php endif; ?>

        <li <?= $this->pageInfo['action'] == 'documents' ? ' class="active"' : '' ?> >
            <a href="broker/documents"><?= ucfirst(translate()->_('documents')); ?></a>
        </li>
        <li <?= $this->pageInfo['action'] === 'yournaw' || $this->activeMainTab === 'naw' ? ' class="active"' : '' ?> >
            <a href="broker/yournaw"><?= (translate()->_('your_data')); ?></a>
        </li>

        <?php if (Settings::get('vidii_analytics')) : ?>
            <li <?= $this->pageInfo['action'] == 'vidii-dashboard' ? ' class="active"' : '' ?>><a
                    href="broker/vidii-dashboard">Vidii dashboard</a></li>
        <?php endif; ?>

        <?php if (Settings::get('meter_usage_broker_portal')) : ?>
            <li <?= $this->pageInfo['action'] == 'meter-usage' ? ' class="active"' : '' ?>><a
                        href="broker/meter-usage">Meterstanden</a></li>
        <?php endif; ?>

        <?php if (Settings::get('modules_kpi') && (loginManager::data()->info['investor_default_kpi'] || loginManager::data()->info['investor_extended_kpi'])) : ?>
            <li<?= $this->pageInfo['controller'] == 'kpi-rapport_portal' ? ' class="active"' : '' ?>><a
                    href="kpi-rapport_portal/dashboard">Kpi rapports</a>
            </li>
        <?php endif; ?>

        <li <?= $this->pageInfo['action'] == 'contact' ? ' class="active"' : '' ?>>
            <a href="broker/contact">Contact</a>
        </li>
        <li <?= $this->pageInfo['action'] == 'logout' ? ' class="active"' : '' ?>>
            <a href="user/logout">Uitloggen</a>
        </li>
    </ul>
</div>
