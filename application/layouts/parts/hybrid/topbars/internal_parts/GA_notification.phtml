<style>
    .GANotificationButton {
        top: 49px !important;
        border: 0;
        border-radius: 4px;
        background: #666;
        padding: 4px 10px;
    }

    .GANotificationButton:hover {
        background: darkgray;
    }

    .admin.sticky .GANotificationButton {
        display: none;
    }
</style>

<?php
$internalUsers = (new Acl())->getInternalRights();

if (!loginManager::data()->info['two_fa_enabled']) {
    $isTwoFactorAuthentication = db()->select()
        ->from('users', 'two_fa_enabled')
        ->where('id = ?', loginManager::data()->id)
        ->query()
        ->fetchColumn();
} else {
    $isTwoFactorAuthentication = true;
}

if (in_array(loginManager::data()->rights, $internalUsers, true) && !$isTwoFactorAuthentication) {
  global $GA_final_date;
  $now = time(); // or your date as well.
  $GAFinalDate = strtotime("2023-09-31");
  $dateDiff = $GAFinalDate - $now;
  if ($dateDiff < 0) {
    $GADaysLeft = -1;
  } else {
    $GADaysLeft = floor($dateDiff / 86400);
  }
  ?>

  <?php if ($GADaysLeft > 0) { ?>
    <div class="GA-notification" noHint="1" onclick="window.location.href='user/admin-edit/id/<?= $this->login->info['id'] ?>/'"
         title="Vanuit een beveiligingsperspectief zullen wij tweefactorauthenticatie doorvoeren binnen de applicatie, om te voorkomen dat ongenodigden toegang kunnen krijgen tot de accounts van onze gebruikers.">
      <button class="GANotificationButton">
        <i class="fa fa-user-shield" aria-hidden="true"></i>
        <span><?= "Nog " . $GADaysLeft . " dagen om 2FA in te stellen" ?></span>
      </button>
    </div>
    <?php
  }
}
?>
