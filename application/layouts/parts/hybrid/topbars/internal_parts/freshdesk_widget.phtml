
<style type="text/css">
    #freshworks-container #freshworks-frame-wrapper {
        width: 90% !important;
        min-height: 90% !important;
    }
</style>
<script>
    window.fwSettings={
        'widget_id':26000000295
    };
    !function(){if("function"!=typeof window.FreshworksWidget){var n=function(){n.q.push(arguments)};n.q=[],window.FreshworksWidget=n}}()
</script>

<script type='text/javascript' src='https://widget.freshworks.com/widgets/26000000295.js' async defer></script>
<script type='text/javascript'>
    FreshworksWidget('prefill', 'ticketForm', {
        name: '<?= loginManager::data()->info['rendered_name'] ?>',
        email: '<?= loginManager::data()->info['email'] ?>',
        custom_fields: {
            cf_url: '<?= 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']?>',
            cf_telefoon: '<?= loginManager::data()->info['phone_user'] ?>',
        }
    });
</script>
