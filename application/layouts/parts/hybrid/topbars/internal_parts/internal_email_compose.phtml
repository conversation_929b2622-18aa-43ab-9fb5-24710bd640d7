<? if (!loginManager::isProjectLimited() || loginManager::hasAllowedMailAccounts()): ?>
    <div id="email_compose">
        <?php if (Settings::get('email_process_queue_enabled')) : ?>
            <i class="fa fa-envelope" aria-hidden="true"></i>
        <?php else: ?>
            <span title="Email verzending uitgeschakeld" noHintButton="1"">
                <i class="fa fa-exclamation-triangle" style="color: #B33A3A;" aria-hidden="true"></i>
            </span>
        <?php endif; ?>
        <span>E-mail</span>
    </div>

    <script type="text/javascript">
        window.addEvent('domready', function(){
            new EmailComposePopup('email_compose');
        })
    </script>
<? endif; ?>
