<script>
    window.addEvent('domready', function(){
        if(branch_location_selector = $('branch_location_selector')) {
            branch_location_selector.addEvent('change', function () {
                if (confirm('Weet u zeker dat u wil omschakelen naar deze vestiging? De pagina zal worden herladen en niet-opgeslagen gegevens zullen verloren gaan.')) {
                    window.location = 'branch-locations/select/id/' + branch_location_selector.get('value') + '/';
                }
            });
        }
    });
</script>

<? if(count(loginManager::data()->allowed_branch_locations) > 0): ?>
<select id="branch_location_selector">
<? foreach(loginManager::data()->allowed_branch_locations as $allowed_branch_location_id => $allowed_branch_location_name): ?>
    <? $selected = $allowed_branch_location_id == loginManager::data()->selected_branch_location; ?>
    <option <?= $selected ? 'selected="selected" ' : '' ?>value="<?= $allowed_branch_location_id ?>">
        Vestiging: <?= $allowed_branch_location_name ?>
    </option>
<? endforeach; ?>
</select>
<? endif; ?>
