<? $logo = loginManager::data()->project['logo_background'] ? loginManager::data()->project['logo_background'] : loginManager::data()->corporation['logo_background']; ?>
<? $background = loginManager::data()->project['header_background'] ? loginManager::data()->project['header_background'] : loginManager::data()->corporation['header_background']; ?>
<? $logout = translate()->_('logout'); ?>
<link rel='stylesheet' type='text/css'
      href='media/style/dynamicstyle.php?headerfoto=<?= $background ?>&logo=<?= $logo ?>'/>

<? if (Settings::get('modules_tenantLogin_new_styling')): ?>

    <? if ($tenant_portal_logo = Settings::get('tenant_portal_logo')):
        $images_row = (new Images())->get($tenant_portal_logo);
        $portallogo = '/' . $images_row['filename'];
    endif; ?>

    <style>
        #topwelcome > p:last-child a:before {
            content: "<? echo($logout);?>";
        }
    </style>
    <? include('views/scripts/portal/customstyle.phtml'); ?>
<? endif; ?>

<? if (loginManager::data()->info['first'] == 1 && $usertype != 'investor'): ?>
    <? include('views/scripts/user/register/language_switch.phtml'); ?>
<? endif; ?>

<? if (Settings::get('modules_tenantLogin_new_styling')): ?>
    <div class="portal_bar">
            <a <?= $this->pageInfo['action'] == 'welcome' ? ' class="active"' : '' ?> href="broker/welcome">
                <?= (translate()->_('welcome')); ?>
            </a>
            <a <?= $this->pageInfo['action'] == 'objects' ? ' class="active"' : '' ?> href="broker/objects">
                <?= (translate()->_('in_management')); ?>
            </a>

        <a <?= $this->pageInfo['action'] == 'vacancy-report-list' ? ' class="active"' : '' ?> href="broker/vacancy-report-list">
            <?= (ucfirst(translate()->_('vacancy'))); ?>
        </a>

        <a <?= $this->pageInfo['action'] == 'transactions-report-list' ? ' class="active"' : '' ?> href="broker/transactions-report-list">
            <?= (ucfirst(translate()->_('debtors'))); ?>
        </a>

        <? $publishes = BrokerPublish::getPublishedForBroker(loginManager::data()->info['investor_company_id']); ?>
        <? if (BrokerPublish::isIpAddressAllowedForBroker($_SERVER['REMOTE_ADDR'],
                loginManager::data()->info['investor_company_id']) && !empty($publishes) && is_array($publishes)): ?>
                <a <?= $this->pageInfo['action'] == 'reports' ? ' class="active"' : '' ?> href="broker/reports">
                    Rapportages
                </a>
        <? endif; ?>
        <a <?= $this->pageInfo['action'] == 'rent-reckoning' ? ' class="active"' : '' ?> href="broker/rent-reckoning">
            <?= translate()->_('rent_receipt'); ?>
        </a>
        <? if (Settings::get('brokerportal_show_year_overview')): ?>
            <a <?= $this->pageInfo['action'] == 'year-overview' ? ' class="active"' : '' ?> href="broker/year-overview">
                <?= translate()->_('year_overview'); ?>
            </a>
        <? endif; ?>

        <?php if (Settings::get('brokerportal_show_emails')) : ?>
            <a <?= $this->pageInfo['action'] == 'email-list' ? ' class="active"' : '' ?> href="broker/email-list">
                <?= (translate()->_('email-list')); ?>
            </a>
        <?php endif; ?>
        <a <?= $this->pageInfo['action'] == 'documents' ? ' class="active"' : '' ?> href="broker/documents">
            <?= ucfirst(translate()->_('documents')); ?>
        </a>

        <a <?= $this->pageInfo['action'] === 'yournaw' || $this->activeMainTab === 'naw' ? ' class="active"' : '' ?> href="broker/yournaw">
            <?= (translate()->_('your_data')); ?>
        </a>

        <?php if (Settings::get('vidii_analytics')) : ?>
            <a <?= $this->pageInfo['action'] == 'vidii-dashboard' ? ' class="active"' : '' ?> href="broker/vidii-dashboard">
                Vidii dashboard
            </a>
        <?php endif; ?>

        <a <?= $this->pageInfo['action'] == 'contact' ? ' class="active"' : '' ?> href="broker/contact">
            Contact
        </a>
    </div>
<? endif; ?>

<div id="bovenbalk" class="<?= strtolower((str_replace(' ', '', (loginManager::data()->project['name'])))); ?>">

    <div class="headerright">
        <a href="http://<?= (loginManager::data()->language == 'en' && strlen(Settings::get('general_website_en')) > 0) ?
            Settings::get('general_website_en') : Settings::get('general_website'); ?>">
            <div class="logo" id="<?= str_replace(" ", "_", loginManager::data()->project['name']) ?>">
            </div>
        </a>


    </div>


    <div class="welcomeuser" id="topwelcome">
        <? if (acl()->hasRole('broker')): ?>
            <h1><?= (translate()->_('welcome')); ?> <?= loginManager::data()->info['rendered_name'] ? loginManager::data()->info['rendered_name'] : loginManager::data()->info['investor_company_name'] ?>  <?= (translate()->_('on_investor_portal')); ?>
                .</h1>

        <? else: ?>
            <h1><?= (translate()->_('welcome')); ?> <?= User::buildnameAlt(false, loginManager::data()->info['id'], true) ?> <?= (translate()->_(Settings::get('modules_tenantLogin_firstnamebasis') ? 'at_your_digital_page_firstnamebasis' : 'at_your_digital_page')); ?>
                .</h1>
        <? endif; ?>

        <? if (loginManager::data()->lastlogin && loginManager::data()->lastlogin > 0): ?>
            <? // change the locale to ensure correct datetime formatting
            if (loginManager::data()->language == 'en') {
                setlocale(LC_TIME, array('en_US.utf8', 'en_US.utf-8'));
                $lastloginDateString = strftime('%A %e %B %Y at %R', loginManager::data()->lastlogin);
                setlocale(LC_TIME, null);
            } else if (loginManager::data()->language == 'fr') {
                setlocale(LC_TIME, array('fr_FR.utf8', 'fr_FR.utf-8'));
                $lastloginDateString = strftime('%A %e %B %Y à %R', loginManager::data()->lastlogin);
                setlocale(LC_TIME, null);
            } else {
                $lastloginDateString = strftime('%A %e %B %Y om %R', loginManager::data()->lastlogin);
            }
            ?>
            <p style="font-size: 10px;"><?= (translate()->_('last_login')); ?> <?= $lastloginDateString ?>.</p>
        <? endif; ?>
        <p> <?= (translate()->_('click')); ?> <a href="user/logout/"
                                                 style="text-decoration: underline;"><?= strtolower((translate()->_('here'))); ?></a> <?= (translate()->_('to_logout')); ?>
        </p>
    </div>


</div>

<div id="headerback">
    <div id="headerfoto"></div>
</div>

<? if (loginManager::data()->info['investor_logo_filename']): ?>
    <img src="<?= loginManager::data()->info['investor_logo_filename'] ?>">
<? endif; ?>
