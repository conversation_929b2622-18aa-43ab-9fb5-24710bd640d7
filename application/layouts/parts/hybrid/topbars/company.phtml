
<? $logo = loginManager::data()->project['logo_background'] ? loginManager::data()->project['logo_background'] : loginManager::data()->corporation['logo_background']; ?>
<? $background = loginManager::data()->project['header_background'] ? loginManager::data()->project['header_background'] : loginManager::data()->corporation['header_background']; ?>
<? $logout = translate()->_('logout');?>
<link rel='stylesheet' type='text/css' href='media/style/dynamicstyle.php?headerfoto=<?= $background ?>&logo=<?= $logo ?>' />

<? if(Settings::get('modules_tenantLogin_new_styling')): ?>

<? if ($tenant_portal_logo = Settings::get('tenant_portal_logo')):
    $images_row = (new Images())->get($tenant_portal_logo);
    $portallogo = '/' . $images_row['filename'];
endif; ?>

<style>
    #topwelcome > p:last-child a:before {
        content: "<? echo($logout);?>";
    }
</style>
<? include('views/scripts/portal/customstyle.phtml'); ?>
<? endif; ?>

<? if(Settings::get('modules_tenantLogin_new_styling')): ?>
<div class="portal_bar">
    <a href="info/contact/"><?= (translate()->_('contact')); ?></a>
</div>
<? endif; ?>
        <div id="bovenbalk" class="<?= strtolower((str_replace(' ','',(loginManager::data()->project['name'])))); ?>">
        <div class="headerright">
           <a href="http://<?= (loginManager::data()->language == 'en' && strlen(Settings::get('general_website_en')) > 0)?
 				Settings::get('general_website_en'): Settings::get('general_website'); ?>">
           <div class="logo" id="<?= str_replace(" ", "_", loginManager::data()->project['name'])?>">
           </div>
           </a>
        </div>
            <div class="welcomeuser" id="topwelcome">

        <h1><?= (translate()->_('welcome')); ?> <?= User::buildnameAlt(false, loginManager::data()->info['id'], true) ?> <?= (translate()->_(Settings::get('modules_tenantLogin_firstnamebasis') ? 'at_your_digital_page_firstnamebasis' : 'at_your_digital_page')); ?>.</h1>

<?	if(loginManager::data()->lastlogin && loginManager::data()->lastlogin > 0): ?>			
				<?	// change the locale to ensure correct datetime formatting
					if(loginManager::data()->language == 'en') {
						setlocale(LC_TIME, array('en_US.utf8', 'en_US.utf-8'));
						$lastloginDateString = strftime('%A %e %B %Y at %R', loginManager::data()->lastlogin);
						setlocale(LC_TIME, null);
					} else if (loginManager::data()->language == 'fr') {
                        setlocale(LC_TIME, array('fr_FR.utf8', 'fr_FR.utf-8'));
                        $lastloginDateString = strftime('%A %e %B %Y à %R', loginManager::data()->lastlogin);
                        setlocale(LC_TIME, null);
                    } else {
						$lastloginDateString = strftime('%A %e %B %Y om %R', loginManager::data()->lastlogin);
					}
				?>
				<p style="font-size: 10px;"><?= (translate()->_('last_login')); ?> <?= $lastloginDateString ?>.</p>
<?	endif; ?>
				<p> <?= (translate()->_('click')); ?> <a href="user/logout/" style="text-decoration: underline;"><?= strtolower((translate()->_('here'))); ?></a> <?= (translate()->_('to_logout')); ?></p>
			</div>
        </div>
        <div id="headerback">
        	<div id="headerfoto"></div>
        </div>
