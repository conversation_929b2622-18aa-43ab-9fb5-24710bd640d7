
<? $logo = loginManager::data()->project['logo_background'] ? loginManager::data()->project['logo_background'] : loginManager::data()->corporation['logo_background']; ?>
<? $background = loginManager::data()->project['header_background'] ? loginManager::data()->project['header_background'] : loginManager::data()->corporation['header_background']; ?>
<? $logout = translate()->_('logout');?>
<link rel='stylesheet' type='text/css' href='media/style/dynamicstyle.php?headerfoto=<?= $background ?>&logo=<?= $logo ?>' />

<? if(Settings::get('modules_tenantLogin_new_styling')): ?>

<? if ($tenant_portal_logo = Settings::get('tenant_portal_logo')):
    $images_row = (new Images())->get($tenant_portal_logo);
    $portallogo = '/' . $images_row['filename'];
endif; ?>

<style>
    #topwelcome > p:last-child a:before {
        content: "<? echo($logout);?>";
    }
</style>
<? include('views/scripts/portal/customstyle.phtml'); ?>
<? endif; ?>

<? if(loginManager::data()->info['first'] == 1 && $usertype != 'investor'):?>
<? include('views/scripts/user/register/language_switch.phtml'); ?>
<? endif; ?>

<? if(Settings::get('modules_tenantLogin_new_styling')): ?>
<div class="portal_bar">
<? if($this->user_type == 'investor'): ?>
    <a <?= $this->pageInfo['action'] == 'welcome' ? ' class="active"' : '' ?> href="broker/welcome/"><?= ucfirst(translate()->_('dashboard')); ?></a>
    <? if (Settings::get('modules_kpi') && loginManager::data()->info['investor_vacancy_report']) { ?>
          <a <?= $this->pageInfo['action'] == 'vacancy-report' ? ' class="active"' : '' ?> href="broker/vacancy-report">Leegstand</a>
    <? }
    if (Settings::get('modules_kpi') && loginManager::data()->info['investor_transaction_report']) { ?>
      <a <?= $this->pageInfo['action'] == 'transactions-report' ? ' class="active"' : '' ?> href="broker/transactions-report">Debiteuren</a>
    <? } ?>
    <a <?= $this->pageInfo['action'] == 'objects' ? ' class="active"' : '' ?>href="broker/objects"><?= (translate()->_('in_management')); ?></a>

    <? if (Settings::get('for_third_party') && loginManager::data()->info['provisiondetails_type'] == 'passthrough'): ?>
        <a <?= $this->pageInfo['action'] == 'rent-reckoning' ? ' class="active"' : '' ?> href="broker/rent-reckoning"><?= translate()->_('rent_receipt'); ?></a>
    <? endif; ?>
    <a href="broker/invoices"><?= (translate()->_('invoices')); ?></a>
    <a href="broker/contact/"><?= (translate()->_('contact')); ?></a>
    <a href="user/logout"><?= translate()->_('logout'); ?></a>



<? elseif(!loginManager::data()->info['first']): ?>
    <a <?= ($this->pageInfo['action']!= 'by-user' && $this->pageInfo['action']!= 'contact'  ? ' class="active"' : '') ?> href="user/dashboard/" ><?= ucfirst(translate()->_('portal')); ?></a>

    <a <?= $this->pageInfo['action'] == 'by-user' ? ' class="active"' : '' ?> href="<?= $this->url(array('action' => 'by-user', 'controller' => 'complaint')) ?>"
		<?= ($this->hideComplaintFunctionality === true || Settings::get('modules_tenantLogin_hide_complaints') )? "style=\"display: none;\"": '' ?>
        ><?= (translate()->_('add_complaint_defect_short')); ?></a>

    <a <?= $this->pageInfo['action'] == 'contact' ? ' class="active"' : '' ?> href="info/contact/"><?= (translate()->_('contact')); ?></a>
<? endif; ?>
</div>
<? endif; ?>

        <div id="bovenbalk" class="<?= strtolower((str_replace(' ','',(loginManager::data()->project['name'])))); ?>">
 
        <div class="headerright">
           <a href="http://<?= (loginManager::data()->language == 'en' && strlen(Settings::get('general_website_en')) > 0)?
 				Settings::get('general_website_en'): Settings::get('general_website'); ?>">
           <div class="logo" id="<?= str_replace(" ", "_", loginManager::data()->project['name'])?>">
           </div>
           </a>


        </div>



        <div class="welcomeuser" id="topwelcome">
<? if(acl()->hasRole('broker')): ?>
     <h1><?= (translate()->_('welcome')); ?>  <?= (translate()->_('on_investor_portal')); ?>.</h1>

<? elseif (Settings::get('modules_tenantLogin_hide_welcome_name')): ?>
        <h1><?= (translate()->_('welcome')); ?> <?= (translate()->_(Settings::get('modules_tenantLogin_firstnamebasis') ? 'at_your_digital_page_firstnamebasis' : 'at_your_digital_page')); ?>.</h1>
<? else: ?>
        <h1><?= (translate()->_('welcome')); ?> <?= User::buildnameAlt(false, loginManager::data()->info['id'], true) ?> <?= (translate()->_(Settings::get('modules_tenantLogin_firstnamebasis') ? 'at_your_digital_page_firstnamebasis' : 'at_your_digital_page')); ?>.</h1>
<? endif; ?>
			
<?	if(loginManager::data()->lastlogin && loginManager::data()->lastlogin > 0): ?>			
				<?	// change the locale to ensure correct datetime formatting
					if(loginManager::data()->language == 'en') {
						setlocale(LC_TIME, array('en_US.utf8', 'en_US.utf-8'));
						$lastloginDateString = strftime('%A %e %B %Y at %R', loginManager::data()->lastlogin);
						setlocale(LC_TIME, null);
					} else if (loginManager::data()->language == 'fr') {
                        setlocale(LC_TIME, array('fr_FR.utf8', 'fr_FR.utf-8'));
                        $lastloginDateString = strftime('%A %e %B %Y à %R', loginManager::data()->lastlogin);
                        setlocale(LC_TIME, null);
                    } else {
						$lastloginDateString = strftime('%A %e %B %Y om %R', loginManager::data()->lastlogin);
					}
				?>
				<p style="font-size: 10px;"><?= (translate()->_('last_login')); ?> <?= $lastloginDateString ?>.</p>
<?	endif; ?>
				<p> <?= (translate()->_('click')); ?> <a href="user/logout/" style="text-decoration: underline;"><?= strtolower((translate()->_('here'))); ?></a> <?= (translate()->_('to_logout')); ?></p>
			</div>



        </div>
        
        <div id="headerback">
        	<div id="headerfoto"></div>
        </div>

<? if(loginManager::data()->info['investor_logo_filename']): ?>
        <img src="<?= loginManager::data()->info['investor_logo_filename'] ?>" >
<? endif; ?>        
