
<!-- profiling -->

<? if(count($this->profiler->getQueryProfiles()) > 1): ?>
    <table class="log" width="100%" cellpadding="2" style="border-spacing:1px;font:11px Verdana, Arial, Helvetica, sans-serif;background:#EEEEEE;color:#666666;">
        <tr>
            <th>Nr.</th>
            <th>Queries</th>
            <th>Time</th>
        </tr>
        <? foreach($this->profiler->getQueryProfiles() as $n => $p): ?>
            <tr class="item">
                <td><?= $n; ?></td>
                <td><?= $p->getQuery(); ?></td>
                <td><?= round($p->getElapsedSecs(), 5); ?></td>
            </tr>
        <? endforeach; ?>
        <tr>
            <th></th>
            <th style="text-align:right;">Total</th>
            <th><?= round($this->profiler->getTotalElapsedSecs(), 5); ?></th>
        </tr>
    </table>
<? endif; ?>

<style type="text/css">
    .log tr th{
        background: lightgrey;
    }
    .log tr td{
        margin: 2px 0;
        padding: 10px;
    }
    .log tr:nth-child(even){
        background: white;
    }
</style>

<!-- /profiling -->

 