<? if ($this->maintenance && DEBUGGING){ ?>
    <div class="maintenance errors">Let op! Deze pagina staat nog in onderhoud modus!<?= $this->maintenanceTime ? ' De inschatting staat op: \'' . $this->maintenanceTime . '\'' : '' ?></div>
<? } ?>

<? if($this->new_layout): ?>
    <? require 'application/layouts/parts/minton/page_title.phtml'; ?>
<? else: ?>
    <? require 'application/layouts/parts/hybrid/breadcrumbs.phtml'; ?>
<? endif; ?>

<?
if (acl()->hasRole('broker')) {
    $controllerName = Zend_Controller_Front::getInstance()->getRequest()->getControllerName();
    $actionName = Zend_Controller_Front::getInstance()->getRequest()->getActionName();
    if( BrokerPublish::isIpAddressAllowedForBroker($_SERVER['REMOTE_ADDR'], loginManager::data()->info['investor_company_id']) && in_array("$controllerName/$actionName", ['broker/reports', 'broker/report-management', 'broker/report-balance', 'broker/report-financial', 'broker/report-technical', 'broker/report-service', ])) {
        require 'application/views/scripts/broker/filters.phtml';
        require 'application/views/scripts/broker/subnav.phtml';
    }
}
?>

<?= $this->layout()->content ?>
