<? if (acl()->hasRole('admin') || acl()->hasRole('internal')): ?>
    <body class="admin <?= $this->user_type ?><?= $this->extraWidth ? ' extraWidth' : ''; ?>" id="dashboard">
<? else: ?>
    <body class="<?= implode( ' ', $this->body_classes) ?>">
<? endif; ?>

<?  require('application/layouts/parts/hybrid/cleanup.php'); ?>

<?php
if (!in_array(loginManager::data()->rights, ['guest', 'tenant', 'company', 'call_center', 'registrant'])) {
    $browserName = (new \Sinergi\BrowserDetector\Browser())->getName();

    $supportedBrowsers = [
        \Sinergi\BrowserDetector\Browser::FIREFOX,
        \Sinergi\BrowserDetector\Browser::CHROME,
        \Sinergi\BrowserDetector\Browser::SAFARI
    ];

    if (!in_array($browserName, $supportedBrowsers, false)) {
        echo '<div style="text-align: center; padding: 5px; background: #FFF3CD; color: #927C50;">
                    De browser waarvan u gebruikt maakt wordt niet officieel ondersteund door het systeem. 
                    Mogelijk functioneert de pagina niet zo als deze bedoeld is. <br />
                    Installeer s.v.p. <a href="https://www.google.com/chrome/">Chrome</a> of <a href="https://www.mozilla.org/nl/firefox/new/">Firefox</a>
                    </div>';
    }
}
?>

<? if($this->new_layout === true): ?>
    <? require('application/layouts/parts/minton/page_container.phtml'); ?>
<? else:  ?>
    <? require('application/layouts/parts/mootools/page_container.phtml'); ?>
<? endif; ?>

<?  require('application/layouts/parts/hybrid/footer.phtml'); ?>

    </body>
