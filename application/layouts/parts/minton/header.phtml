
	<head>
		<meta http-equiv="Content-type" content="text/html; charset=UTF-8" useOmniboxxPlugin="true" />
		<meta name="useOmniboxxPlugin"/>
		<meta charset="UTF-8">
		<?= $this->headTitle() ?>
		<base href="<?= $this->secure ? 'https://' : 'http://' ?><?= $this->server ?><?=$this->baseDir ?>/" />
        <link href="<?=$this->baseDir ?>/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/media/style/hybrid.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/media/fonts/font-awesome/css/font-awesome.css" media="screen" rel="stylesheet" type="text/css" />

        <? if($this->new_layout === true) { ?>
        <link href="<?=$this->baseDir ?>/media/style/hybrid.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/assets/plugins/summernote/summernote-bs4.css" rel="stylesheet" />
        <link href="<?=$this->baseDir ?>/assets/plugins/bootstrap-tagsinput/dist/bootstrap-tagsinput.css" rel="stylesheet" />
        <link href="<?=$this->baseDir ?>/assets/plugins/multiselect/css/multi-select.css"  rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/assets/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
        <link href="<?=$this->baseDir ?>/assets/plugins/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.css" rel="stylesheet" />
        <link href="<?=$this->baseDir ?>/assets/plugins/mjolnic-bootstrap-colorpicker/dist/css/bootstrap-colorpicker.min.css" rel="stylesheet">
        <link href="<?=$this->baseDir ?>/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet">
        <link href="<?=$this->baseDir ?>/assets/plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet">
        <link href="<?=$this->baseDir ?>/assets/plugins/switchery/switchery.min.css" rel="stylesheet" />

<? } ?>

<? if($this->new_layout === true) {
		$cssBuild = new Minify_Build($this->minifyGroups['css_minton']);
		$cssPrint = new Minify_Build($this->minifyGroups['cssprint']);
		$jsBuild = new Minify_Build($this->minifyGroups['js']);
}  else {
         $cssBuild = new Minify_Build($this->minifyGroups['css']);
         $cssPrint = new Minify_Build($this->minifyGroups['cssprint']);
         $jsBuild = new Minify_Build($this->minifyGroups['js']);
}


if(in_array($usertype, ['tenant', 'registrant', 'investor']) && Settings::get('modules_tenantLogin_new_styling')):
	$cssTenant = new Minify_Build($this->minifyGroups['css_portal']);
    $this->headLink()->prependStylesheet($cssTenant->uri($this->baseDir.'/minify/css_portal', false), 'screen, print');
else:
    if($this->new_layout === true) {
        $this->headLink()->prependStylesheet($cssBuild->uri($this->baseDir.'/minify/css_minton', false), 'screen, print');

    } else {
        $this->headLink()->prependStylesheet($cssBuild->uri($this->baseDir.'/minify/css', false), 'screen, print');

    }
		//$this->headLink()->prependStylesheet($cssPrint->uri($this->baseDir.'/minify/cssprint', false), 'print');
endif;

        $this->headScript()->prependFile($jsBuild->uri($this->baseDir.'/minify/js'));
        include "settings.phtml";

?>
		<?= $this->headLink() ?>
		<?= $this->headScriptHashed() ?>

        <?php /** empty fav icon, to prevent the browser fron trying to fetch favicon.ico */ ?>
        <link rel="icon" href="data:;base64,iVBORw0KGgo=">
	</head>
