<?php

namespace Investors\Combination\Service;


class CalculateCurrentPercentageService
{
    public function calculate($investorCombinationId = null, $investorCombinationLinkId = null)
    {
        if (is_null($investorCombinationId))
            return null;

        $query = db()->select()
            ->from(['ic' => 'investor_combination'], false)
            ->joinLeft(['icl' => 'investor_combination_link'], 'icl.investor_combination_id = ic.id', 'percentage')
            ->where('ic.id = ?', $investorCombinationId);

        if (!is_null($investorCombinationLinkId))
            $query->where('icl.id != ?', $investorCombinationLinkId);

        $investorCombination = $query
            ->query()
            ->fetchAll();

        return array_sum(array_column($investorCombination, 'percentage'));
    }
}