<?
class Investors_Combination_Link extends GlobalDbClass
{

	protected $_name = 'investor_combination_link';

    public function save($data, $params)
    {
        $data['general']['percentage'] = formatMoneyInputForDatabase($data['general']['percentage']);

        $totalCurrentPercentage = (new \Investors\Combination\Service\CalculateCurrentPercentageService())->calculate($data['general']['investor_combination_id'], $params['id']);

        if (($totalCurrentPercentage + $data['general']['percentage']) == 100) {
            $row = (new Investors_Combination())->fetchRowById($data['general']['investor_combination_id']);
            $row->concept = 0;
            $row->save();
        } else {
            $row = (new Investors_Combination())->fetchRowById($data['general']['investor_combination_id']);
            $row->concept = 1;
            $row->save();
        }

        return parent::save($data, $params);
    }

    public function toFormArray($form, $params, $data = array()){
        $form_array = parent::toFormArray($form, $params, $data);

        $form_array['general']['percentage'] = (string) new StringFormat($form_array['general']['percentage'], 'money');

        return $form_array;
    }
}