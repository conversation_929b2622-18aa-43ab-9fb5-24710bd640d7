<?php

namespace Investors\Provision\Preview\UpdateTriggers;

class UpdateCommandList
{
    private $items = [];

    public function appendItemIfNotExists($item)
    {
        if ($this->itemExists($item)) {
            return;
        }

        $this->appendItem($item);
    }

    private function itemExists($item)
    {
        return isset($this->items[$item->getId()]);
    }

    public function appendItem($item)
    {
        $this->items[$item->getId()] = $item;
    }

    public function getItems()
    {
        return $this->items;
    }
}
