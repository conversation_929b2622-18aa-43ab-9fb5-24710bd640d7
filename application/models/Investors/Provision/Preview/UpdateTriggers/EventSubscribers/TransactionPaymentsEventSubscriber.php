<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use Exception as Exception;

class TransactionPaymentsEventSubscriber extends EventSubscriber
{
    /**
     * @param $transactionPaymentIds
     * @return void
     * @throws Exception
     */
    public function execute($transactionPaymentIds)
    {
        if (!is_array($transactionPaymentIds)) {
            throw new \InvalidArgumentException('Argument $transactionPaymentIds must be an array');
        }

        if (count($transactionPaymentIds) === 0) {
            throw new \InvalidArgumentException('Argument $transactionPaymentIds is empty');
        }

        $invoiceIds = $this->findInvoiceIdsPerTransactionPayment($transactionPaymentIds);

        $invoiceEventSubscriber = new InvoiceEventSubscriber($this->updateCommandList);
        $invoiceEventSubscriber->execute($invoiceIds);
    }

    private function findInvoiceIdsPerTransactionPayment($transactionPaymentIds)
    {
        $select = db()->select()
            ->from(['tp' => 'transactions_payments'], ['id'])
            ->joinLeft(['t' => 'transactions'], 't.id = tp.transaction', ['invoice'])
            ->where('tp.id IN (?)', $transactionPaymentIds);

        return db()->fetchPairs($select);
    }
}
