<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use DateTime as DateTime;
use Exception as Exception;
use Investors as InvestorModel;
use Investors\Provision\Period;
use Investors\Provision\Preview\UpdateTriggers\CurrentPeriodsPerInvestorService;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommand;

class InvestorEventSubscriber extends EventSubscriber
{
    /**
     * @param $investorIds
     * @return void
     * @throws Exception
     */
    public function execute($investorIds)
    {
        if (!is_array($investorIds)) {
            throw new \InvalidArgumentException('Argument $investorIds must be an array');
        }

        if (count($investorIds) === 0) {
            throw new \InvalidArgumentException('Argument $investorIds is empty');
        }

        $periodPerInvestorId = (new CurrentPeriodsPerInvestorService())->execute($investorIds);

        foreach ($investorIds as $investorId) {
            $this->addUpdateRequestPerPeriod($investorId, $periodPerInvestorId);
        }
    }

    private function addUpdateRequestPerPeriod($investorId, $periodPerInvestorId)
    {
        if (!isset($periodPerInvestorId[$investorId])) {
            return;
        }

        foreach ($periodPerInvestorId[$investorId] as $period) {
            $this->addUpdateRequest(
                $investorId,
                $period,
                new DateTime()
            );
        }
    }
}
