<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use DateTime as DateTime;
use Exception as Exception;
use Investors as InvestorModel;
use Investors\Provision\Period;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommand;

class InvoiceEventSubscriber extends EventSubscriber
{
    /**
     * @param $invoiceIds
     * @return void
     * @throws Exception
     */
    public function execute($invoiceIds)
    {
        if (!is_array($invoiceIds)) {
            throw new \InvalidArgumentException('Argument $invoiceIds must be an array');
        }

        if (count($invoiceIds) === 0) {
            throw new \InvalidArgumentException('Argument $invoiceIds is empty');
        }

        $invoiceIds = $this->filterFinalizedInvoiceIdsForTenants($invoiceIds);

        if (!$invoiceIds || count($invoiceIds) === 0) {
            return;
        }

        $objectIds = $this->findObjectIdsPerInvoice($invoiceIds);
        $investorIds = $this->findInvestorIdsPerObjectIds($objectIds);
        $updateDatesPerInvoice = $this->getUpdateDatesPerInvoice($invoiceIds);
        $periodsPerInvoice = $this->findPeriodsPerInvoice($updateDatesPerInvoice);
        $triggerDatesPerInvoice = $this->getTriggerDatesPerInvoice($updateDatesPerInvoice);

        foreach ($invoiceIds as $invoiceId) {

            if (!isset($objectIds[$invoiceId])) {
                continue;
            }

            if (!isset($periodsPerInvoice[$invoiceId])) {
                continue;
            }

            if (!isset($triggerDatesPerInvoice[$invoiceId])) {
                continue;
            }

            $objectIdsForInvoice = $objectIds[$invoiceId];
            $periodsForInvoice = $periodsPerInvoice[$invoiceId];
            $triggerDatesForInvoice = $triggerDatesPerInvoice[$invoiceId];

            $this->createUpdateRequests(
                $objectIdsForInvoice,
                $investorIds,
                $periodsForInvoice,
                $triggerDatesForInvoice
            );
        }
    }

    private function getInvestorIdsForObjectId($investorIds, $objectId)
    {
        if (!isset($investorIds[$objectId])) {
            return null;
        }

        return $investorIds[$objectId];
    }

    private function createUpdateRequests(
        $objectIds,
        $investorIds,
        $periods,
        $triggerDate
    ) {
        foreach ($objectIds as $objectId) {
            $investorIdsForObjectId = $this->getInvestorIdsForObjectId($investorIds, $objectId);

            if (!$investorIdsForObjectId) {
                continue;
            }

            foreach ($investorIdsForObjectId as $investorId) {
                foreach ($periods as $period) {
                    $this->addUpdateRequest(
                        $investorId,
                        $period,
                        $triggerDate
                    );
                }
            }
        }
    }

    private function getPeriods($minDate, $maxDate)
    {
        $periods = [];
        $period = Period::fromTimestamp($minDate);
        $endPeriod = Period::fromTimestamp($maxDate);

        while ($period->getTimestamp() <= $endPeriod->getTimestamp()) {
            $periods[] = $period;
            $period = $period->getNextPeriod();
        }

        return $periods;
    }

    private function findInvestorIdsPerObjectIds($objectIds)
    {
        return InvestorModel::getInvestorsForObjectIds($objectIds);
    }

    private function filterFinalizedInvoiceIdsForTenants($invoiceIds){
        $select = db()->select()
            ->from(['i' => 'invoices'], ['invoiceId' => 'id'])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', false)
            ->joinLeft(['t' => 'transactions'], 't.invoice = i.id', false)
            ->where('i.id IN (?)', $invoiceIds)
            ->where('ir.status = ?', 2)
            ->where('t.user > 0')
            ;

        return db()->fetchCol($select);
    }

    private function findObjectIdsPerInvoice($invoiceIds)
    {
        $select = db()->select()
            ->from(
                ['i' => 'invoices'],
                [
                    'invoiceId' => 'id',
                    'objectId' => 'COALESCE(irows.object, io.object, ic.object)'
                ]
            )
            ->joinLeft(['io' => 'invoice_objects'], 'io.invoice = i.id', false)
            ->joinLeft(['irows' => 'invoice_rows'], 'irows.invoice = i.id', false)
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', false)
            ->where('i.id IN (?)', $invoiceIds);

        $objectIdsPerInvoice = [];
        foreach (db()->fetchAll($select) as $objectInvoiceRow) {
            if (!isset($objectIdsPerInvoice[$objectInvoiceRow['invoiceId']])) {
                $objectIdsPerInvoice[$objectInvoiceRow['invoiceId']] = [];
            }

            if (in_array($objectInvoiceRow['objectId'], $objectIdsPerInvoice[$objectInvoiceRow['invoiceId']])) {
                continue;
            }

            $objectIdsPerInvoice[$objectInvoiceRow['invoiceId']][] = $objectInvoiceRow['objectId'];
        }

        return $objectIdsPerInvoice;
    }

    private function getUpdateDatesPerInvoice($invoiceIds)
    {
        $select = db()->select()
            ->from(['i' => 'invoices'], [
                'invoiceId' => 'id',
                // normally would use a normal LEAST and GREATEST here, but it does not work for our mysql version (https://bugs.mysql.com/bug.php?id=15610)
                'minDate' => 'LEAST(COALESCE(ir.start, tp.date, tp.date_created), COALESCE(tp.date, tp.date_created, ir.start), COALESCE(tp.date_created, ir.start, tp.date))',
                'maxDate' => 'GREATEST(COALESCE(ir.start, tp.date, tp.date_created), COALESCE(tp.date, tp.date_created, ir.start), COALESCE(tp.date_created, ir.start, tp.date))'
            ])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', false)
            ->joinLeft(['t' => 'transactions'], 't.invoice = i.id', false)
            ->joinLeft(
                ['tp' => 'transactions_payments'],
                'tp.transaction = t.id AND tp.investor_provision = "1" AND tp.investor_payed = "0"',
                null
            )
            ->where('i.id IN (?)', $invoiceIds)
            ->having('maxDate');

        return db()->fetchAssoc($select);
    }

    private function findPeriodsPerInvoice($updateDatesPerInvoice)
    {
        $periodsPerInvoice = [];

        foreach ($updateDatesPerInvoice as $invoiceId => $dateRangeForInvoice) {
            $minDate = strtotime($dateRangeForInvoice['minDate']);
            $minDate = mktime(0, 0, 0, date('m', $minDate), 1, date('Y', $minDate));
            $maxDate = strtotime($dateRangeForInvoice['maxDate']);
            $maxDate = mktime(0, 0, 0, date('m', $maxDate), 1, date('Y', $maxDate));
            $currentMonthTimestamp = mktime(0, 0, 0, date('m') + 1, 1, date('Y'));
            $maxDate = max([$maxDate, $currentMonthTimestamp]);

            if ($minDate >= $maxDate) {
                continue;
            }

            $periodsPerInvoice[$invoiceId] = $this->getPeriods($minDate, $maxDate);
        }

        return $periodsPerInvoice;
    }

    private function getTriggerDatesPerInvoice($updateDatesPerInvoice)
    {
        $triggerDatesPerInvoice = [];
        foreach ($updateDatesPerInvoice as $invoiceId => $dateRangeForInvoice) {
            try {
                $dateTime = new DateTime($dateRangeForInvoice['maxDate']);
            } catch (Exception $exception) {
                continue;
            }
            $triggerDatesPerInvoice[$invoiceId] = $dateTime;
        }

        return $triggerDatesPerInvoice;
    }

}
