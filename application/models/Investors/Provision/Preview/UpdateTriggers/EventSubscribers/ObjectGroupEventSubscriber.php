<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use DateTime as DateTime;
use Exception as Exception;
use Investors as InvestorModel;
use Investors\Provision\Period;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommand;

class ObjectGroupEventSubscriber extends EventSubscriber
{
    /**
     * @param $objectGroupIds
     * @return void
     * @throws Exception
     */
    public function execute($objectGroupIds)
    {
        if (!is_array($objectGroupIds)) {
            throw new \InvalidArgumentException('Argument $objectGroupIds must be an array');
        }

        if (count($objectGroupIds) === 0) {
            throw new \InvalidArgumentException('Argument $objectGroupIds is empty');
        }

        $objectIds = $this->getObjectIds($objectGroupIds);

        if (count($objectIds) === 0) {
            throw new \Exception('Objectgroups contain no objects');
        }

        (new ObjectEventSubscriber($this->updateCommandList))->execute($objectIds);
    }

    private function getObjectIds($objectGroupIds)
    {
        return [];
    }
}
