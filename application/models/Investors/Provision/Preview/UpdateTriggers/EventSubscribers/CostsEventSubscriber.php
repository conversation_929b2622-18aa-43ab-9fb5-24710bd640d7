<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use Exception as Exception;

class CostsEventSubscriber extends EventSubscriber
{
    /**
     * @param $costIds
     * @return void
     * @throws Exception
     */
    public function execute($costIds)
    {
        if (!is_array($costIds)) {
            throw new \InvalidArgumentException('Argument $costIds must be an array');
        }

        if (count($costIds) === 0) {
            throw new \InvalidArgumentException('Argument $costIds is empty');
        }

        $objectIds = $this->getObjectIdsForCostIds($costIds);

        (new ObjectEventSubscriber($this->updateCommandList))->execute($objectIds);
    }

    private function getObjectIdsForCostIds($costIds)
    {
        $select = db()->select()
            ->distinct()
            ->from(['co' => 'costs_objects'], ['object'])
            ->where('co.cost IN (?)', $costIds);

        return db()->fetchCol($select);
    }
}
