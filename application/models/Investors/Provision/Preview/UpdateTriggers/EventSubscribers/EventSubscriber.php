<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use Investors\Provision\Period;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommand;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommandList;

class EventSubscriber
{
	/**
	 * @var UpdateCommandList $updateCommandList ;
	 */
	protected $updateCommandList;

	public function __construct(UpdateCommandList $updateCommandList)
	{
		$this->updateCommandList = $updateCommandList;
	}

	protected function addUpdateRequest($investorId, Period $period, $triggerTimestampDateTime = null)
	{
		$updateCommand = UpdateCommand::make(
			$investorId,
			$period
		);

		if ($triggerTimestampDateTime) {
			$updateCommand->setUpdateTimestamp($triggerTimestampDateTime);
		}

		$this->updateCommandList->appendItemIfNotExists($updateCommand);
	}
}
