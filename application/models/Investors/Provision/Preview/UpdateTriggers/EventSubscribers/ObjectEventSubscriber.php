<?php

namespace Investors\Provision\Preview\UpdateTriggers\EventSubscribers;

use DateTime as DateTime;
use Exception as Exception;
use Investors as InvestorModel;
use Investors\Provision\Period;
use Investors\Provision\Preview\UpdateTriggers\CurrentPeriodsPerInvestorService;
use Investors\Provision\Preview\UpdateTriggers\UpdateCommand;

class ObjectEventSubscriber extends EventSubscriber
{
    /**
     * @param $objectIds
     * @return void
     * @throws Exception
     */
    public function execute($objectIds)
    {
        if (!is_array($objectIds)) {
            throw new \InvalidArgumentException('Argument $objectIds must be an array');
        }

        if (count($objectIds) === 0) {
            throw new \InvalidArgumentException('Argument $objectIds is empty');
        }

        $investorIds = $this->findInvestorIdsPerObjectIds($objectIds);

        $allInvestorIdsFlat = $this->getAllInvestorIdsFlat($investorIds);
        $currentPeriodsPerInvestorIds = (new CurrentPeriodsPerInvestorService())->execute($allInvestorIdsFlat);

        $this->createUpdateRequests(
            $objectIds,
            $investorIds,
            $currentPeriodsPerInvestorIds
        );
    }

    private function getInvestorIdsForObjectId($investorIds, $objectId)
    {
        if (!isset($investorIds[$objectId])) {
            return null;
        }

        return $investorIds[$objectId];
    }

    private function getAllInvestorIdsFlat($investorIds){
        $allInvestorIdsFlat = [];

        foreach($investorIds as $investorsPerObject){
            $allInvestorIdsFlat = array_merge($allInvestorIdsFlat, $investorsPerObject);
        }

        return $allInvestorIdsFlat;
    }

    private function createUpdateRequests(
        $objectIds,
        $investorIds,
        $currentPeriodsPerInvestorIds
    ) {
        foreach ($objectIds as $objectId) {
            $investorIdsForObjectId = $this->getInvestorIdsForObjectId($investorIds, $objectId);

            if (!$investorIdsForObjectId) {
                continue;
            }

            foreach ($investorIdsForObjectId as $investorId) {
                if (!isset($currentPeriodsPerInvestorIds[$investorId])) {
                    return;
                }

                foreach ($currentPeriodsPerInvestorIds[$investorId] as $period) {
                    $this->addUpdateRequest($investorId, $period, new DateTime());
                }
            }
        }
    }

    private function findInvestorIdsPerObjectIds($objectIds)
    {
        return InvestorModel::getInvestorsForObjectIds($objectIds);
    }

}
