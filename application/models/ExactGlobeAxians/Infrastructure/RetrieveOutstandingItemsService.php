<?php

namespace ExactGlobeAxians\Infrastructure;

class RetrieveOutstandingItemsService
{
    public function retrieve() {
        $select = db()->select()
            ->from(['i' => (new \Invoices())->getTableName()], 'i.*')
            ->joinLeft(['t' => (new \Transactions())->getTableName()], 't.invoice = i.id', false)
            ->joinLeft(['ir' => (new \InvoicesRuns())->getTableName()], 'ir.id = i.run', false)
            ->joinLeft(['c' => (new \Corporations())->getTableName()], 'c.id = ir.corporation_id', false)
            ->joinLeft(['ic' => (new \InvoicesCustoms())->getTableName()], 'ic.id = i.customid', false)
            ->where('i.financial_invoice_id IS NOT NULL','')
            ->where('i.financial_no_sync != 1','')
            ->where('t.closed != 1', '')
            ->where('ic.is_purchase = 0 OR  ic.is_purchase IS NULL', '');

        return db()->fetchAll($select);
    }
}
