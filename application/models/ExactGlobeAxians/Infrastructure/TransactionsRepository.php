<?php


namespace ExactGlobeAxians\Infrastructure;


class TransactionsRepository
{

    /** @var \Transactions $transactionsModel */
    protected $transactionsModel;

    public function __construct()
    {
        $this->transactionsModel = new \Transactions();
    }

    /**
     * @param int $transactionsId
     * @return \Zend_Db_Table_Row_Abstract
     */
    public function retrieve($transactionsId)
    {
        return $this->transactionsModel->getById($transactionsId);
    }

    /**
     * @param $invoiceId
     * @return array|null
     * @throws \Zend_Db_Statement_Exception
     */
    public function retrieveByInvoiceId($invoiceId)
    {
        return $this->transactionsModel->genMatchSelect(['invoice' => $invoiceId])->query()->fetch();
    }

    /**
     * @param \Zend_Db_Table_Row_Abstract[] $rows
     * @return void
     * @throws \Exception
     */
    public function persistAll($rows)
    {
        try {
            db()->beginTransaction();

            foreach ($rows as $row) {
                $this->persist($row);
            }

            db()->commit();
        } catch (\Exception $e) {
            db()->rollBack();
            throw $e;
        }
    }

    public function persist(\Zend_Db_Table_Row_Abstract $row)
    {
        $row->save();
    }
}
