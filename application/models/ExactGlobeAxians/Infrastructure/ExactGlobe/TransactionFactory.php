<?php

namespace ExactGlobeAxians\Infrastructure\ExactGlobe;

use ExactGlobeAxians\Domain\ExactGlobe\Transaction;
use SharedKernel\Domain\Model\Money;

class TransactionFactory
{

    public function make($transactionEntry)
    {
        $directionOptions = [
            Transaction::DIRECTION_INCOMING,
            Transaction::DIRECTION_OUTGOING
        ];

        if (!in_array($transactionEntry->direction, $directionOptions)) {
            throw new \Exception('ExactGlobeAxians transaction invalid direction option: '. $transactionEntry->direction);
        }

        $transaction = new Transaction(
            $transactionEntry->transaction_id,
            $transactionEntry->invoice_id,
            $transactionEntry->direction,
            new Money($transactionEntry->amount),
            $transactionEntry->bankaccount_customer,
            $transactionEntry->description,
            new \DateTime($transactionEntry->date)
        );

        return $transaction;
    }
}
