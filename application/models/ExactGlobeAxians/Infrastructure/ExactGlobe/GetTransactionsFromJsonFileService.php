<?php

namespace ExactGlobeAxians\Infrastructure\ExactGlobe;

use ExactGlobeAxians\Domain\ExactGlobe\Transaction;
use SharedKernel\Domain\Model\Money;

class GetTransactionsFromJsonFileService
{

    /**
     * @var TransactionFactory
     */
    private $transactionFactory;

    public function __construct(
        TransactionFactory $transactionFactory
    )
    {
        $this->transactionFactory = $transactionFactory;
    }

    public function execute($transactionJsonFile)
    {
        if (!file_exists($transactionJsonFile)) {
            throw new \Exception('File not fount: '. $transactionJsonFile);
        }

        $jsonString = file_get_contents($transactionJsonFile);

        $transactionJsonData = json_decode($jsonString);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Contant of the file is not JSON :'. $transactionJsonFile);
        }

        $result = [];
        foreach ($transactionJsonData->entry as $transactionEntry) {
            try {
                $result[] = $this->transactionFactory->make($transactionEntry);
            } catch (\Exception $exception) {
                error_log($exception->getMessage());
            }
        }

        return $result;
    }

}
