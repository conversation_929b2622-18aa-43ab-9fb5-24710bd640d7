<?php

namespace ExactGlobeAxians\Infrastructure\ExactGlobe;

class GetOutStandingItemsFromJsonFileService
{
    /**
     * @var OutStandingItemFactory
     */
    private $outStandingItemFactory;

    public function __construct(
        OutStandingItemFactory $outStandingItemFactory
    )
    {
        $this->outStandingItemFactory = $outStandingItemFactory;
    }

    public function execute($outStandingItemJsonFile)
    {
        if (!file_exists($outStandingItemJsonFile)) {
            throw new \Exception('File not found: '. $outStandingItemJsonFile);
        }

        $jsonString = file_get_contents($outStandingItemJsonFile);

        $outStandingItemJsonData = json_decode($jsonString);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Content of the file is not JSON: '. $outStandingItemJsonFile);
        }

        $result = [];
        foreach ($outStandingItemJsonData->entry as $outStandingItemEntry) {
            try {
                $result[] = $this->outStandingItemFactory->make($outStandingItemEntry);
            } catch (\Exception $exception) {
                error_log($exception->getMessage());
            }
        }

        return $result;
    }
}
