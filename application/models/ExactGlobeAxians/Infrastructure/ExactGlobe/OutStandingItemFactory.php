<?php

namespace ExactGlobeAxians\Infrastructure\ExactGlobe;

use ExactGlobeAxians\Domain\ExactGlobe\OutstandingInvoice;
use ExactGlobeAxians\Domain\ExactGlobe\Transaction;
use SharedKernel\Domain\Model\Money;

class OutStandingItemFactory
{
    public function make($outstandingInvoiceEntry)
    {
        $outstandingInvoice = new OutstandingInvoice(
            $outstandingInvoiceEntry->invoice_id,
            new Money($outstandingInvoiceEntry->outstanding_amount)
        );

        return $outstandingInvoice;
    }
}
