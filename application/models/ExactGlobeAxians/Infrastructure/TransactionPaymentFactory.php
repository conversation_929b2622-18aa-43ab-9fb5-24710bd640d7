<?php


namespace ExactGlobeAxians\Infrastructure;


use Afas\Domain\TransactionPayment;
use SharedKernel\Domain\Model\Money;

class TransactionPaymentFactory
{

    public function make(Money $amount, $transactionId, $description, $transactionDate = null, $externalId = null)
    {
        $amountCents = $amount->getAmountInCents();
        if (!$transactionDate) {
            $transactionDate = new \DateTime();
        }

        return new TransactionPayment(
            $transactionId,
            $transactionDate,
            $amountCents,
            $description,
            $externalId
        );
    }
}
