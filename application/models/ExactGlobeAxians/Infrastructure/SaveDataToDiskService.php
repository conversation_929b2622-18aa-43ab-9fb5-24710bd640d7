<?php

namespace ExactGlobeAxians\Infrastructure;

class SaveDataToDiskService
{
    public function save($filePath, $fileExtension, $inputData)
    {
        $filename = sprintf(
            '%s_%s',
            time(),
            uniqid()
        );

        $saveToFilename = sprintf(
            '%s/%s.%s',
            $filePath,
            $filename,
            $fileExtension
        );

        createFolder($saveToFilename);
        file_put_contents($saveToFilename, $inputData);

        return $saveToFilename;
    }
}
