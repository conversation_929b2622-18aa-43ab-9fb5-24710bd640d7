<?php


namespace ExactGlobeAxians\Infrastructure;

use Invoices;

class InvoiceRepository
{
    /**
     * @var Invoices
     */
    private $invoiceModel;

    public function __construct(Invoices $invoiceModel)
    {
        $this->invoiceModel = $invoiceModel;
    }

    public function findByFinancialId($financialInvoiceId)
    {
        return $this->invoiceModel->genMatchSelect(['financial_invoice_id' => $financialInvoiceId])
            ->query()
            ->fetchObject();
    }

    public function findById($invoiceId)
    {
        return $this->invoiceModel->fetchRowById($invoiceId);
    }

    public function persist(\Zend_Db_Table_Row_Abstract $row)
    {
        return $row->save();
    }
}

