<?php


namespace ExactGlobeAxians\Infrastructure;

use Afas\Domain\TransactionPayment;
use TransactionsPayments;

class TransactionsPaymentsRepository
{
    /**
     * @var TransactionsPayments
     */
    private $transactionPaymentModel;
    /**
     * @var \Transactions
     */
    private $transactionModel;

    public function __construct(TransactionsPayments $transactionPaymentModel, \Transactions $transactionModel)
    {
        $this->transactionPaymentModel = $transactionPaymentModel;
        $this->transactionModel = $transactionModel;
    }

    /**
     * @param \Zend_Db_Table_Row_Abstract[] $rows
     * @throws \Exception
     */
    public function persistAll($rows)
    {
        try {
            db()->beginTransaction();

            foreach ($rows as $row) {
                $this->persist($row);
            }

            db()->commit();
        } catch (\Exception $e) {
            db()->rollBack();
            throw $e;
        }
    }

    public function persist(\Zend_Db_Table_Row_Abstract $row)
    {
        $row->save();
    }

    public function findAllForTransaction($transactionId)
    {
        return $this->transactionPaymentModel->genMatchSelect(['transaction' => $transactionId])
            ->query()
            ->fetchAll();
    }

    public function findNewestByExternalId($externalId)
    {
        return $this->transactionPaymentModel
            ->genMatchSelect(['external_id' => $externalId])
            ->order('date_created DESC')
            ->query()->fetch();
    }

    public function removeByIdOrFail($transactionPaymentId)
    {
        $transactionPaymentRow = $this->transactionPaymentModel->getById($transactionPaymentId);
        if (!$transactionPaymentRow) {
            throw new \RuntimeException('Transaction Payment not fond with id: ' . $transactionPaymentId);
        }

        if ($transactionPaymentRow->investor_payed) {
            throw new \RuntimeException('Transaction Payment with id: ' . $transactionPaymentId . ' have investor_payed true');
        }

        $transactionPaymentRow->delete();

    }

    public function insertByTransactionPaymentData(array $data)
    {
        $this->transactionPaymentModel->createRow($data)->save();
    }

    public function findCounterTransactionPaymentByTransactionPayment($transactionPaymentId)
    {
        return $this->transactionPaymentModel->fetchRow([
            'counter_transaction_payment_id = ?' => $transactionPaymentId
        ]);
    }

    public function insert(TransactionPayment $transactionsPayment)
    {
        $transactionId = $transactionsPayment->getTransactionId();
        $transactionDate = date('Y-m-d', $transactionsPayment->getDate()->getTimestamp());
        $amountSign = $transactionsPayment->getAmountSign();
        $amount = $transactionsPayment->getAmountCents();
        $description = $transactionsPayment->getDescription();
        $externalId = $transactionsPayment->getExternalId();

        if (TransactionPayment::AMOUNT_SIGN_POSITIVE == $amountSign) {
            $direction = TransactionPayment::DORECTION_OUTGOING;
        } else if (TransactionPayment::AMOUNT_SIGN_NEGATICE == $amountSign) {
            $direction = TransactionPayment::DORECTION_INCOMING;
        }

        return $this->transactionPaymentModel->createRow([
            'transaction' => $transactionId,
            'date_created' => date('Y-m-d H:i:s'),
            'status' => 'confirmed',
            'date' => $transactionDate,
            'direction' => $direction,
            'amount' => $amount,
            'description' => $description,
            'external_id' => $externalId
        ])->save();
    }
}
