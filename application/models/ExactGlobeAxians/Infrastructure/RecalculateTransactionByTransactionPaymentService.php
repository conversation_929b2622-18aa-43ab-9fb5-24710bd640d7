<?php


namespace ExactGlobeAxians\Infrastructure;


use Afas\Domain\TransactionPayment;

class RecalculateTransactionByTransactionPaymentService
{
    private $transactionsModel;
    /**
     * @var \TransactionsPayments
     */
    private $transactionsPaymentsModel;

    public function __construct()
    {
        $this->transactionsModel = new \Transactions();
        $this->transactionsPaymentsModel = new \TransactionsPayments();
    }

    /**
     * @throws \Zend_Db_Adapter_Exception
     */
    public function execute($transactionPaymentId)
    {
        $transactionRow = $this->transactionsModel->getById($transactionPaymentId);

        $totalAmountTransactionsPayment = $this->calculateTotalAmount($transactionRow['id'], $transactionRow['type']);
        $latestTransactionsPaymentCreateAtTime = $this->calculateCloseDateTime($transactionRow['id']);

        $transactionRow->payed = $totalAmountTransactionsPayment;

        // if transaction is payed
        if ($transactionRow->closed != 1 && (int)$transactionRow->payed == (int)$transactionRow->amount) {
            $transactionRow->closed = 1;
            $transactionRow->closed_date = date('Y-m-d', $latestTransactionsPaymentCreateAtTime);
        } else {
            $transactionRow->closed = 0;
            $transactionRow->closed_date = nullValue();
        }

        // hack fix to update closed_date
        db()->update('transactions',
            [
                'payed' => $transactionRow->payed,
                'closed' => $transactionRow->closed,
                'closed_date' => $transactionRow->closed_date
            ],
            ['id = ?' => $transactionRow->id]
        );
    }

    private function calculateTotalAmount($transactionId, $transactionType)
    {
        $transactionsPaymentRows = $this->transactionsPaymentsModel->getByTransactionId(
            $transactionId
        );

        $totalAmountTransactionsPayment = 0;
        foreach ($transactionsPaymentRows as $transactionsPaymentRow) {
            $transactionsPaymentRowAmountCents = abs((int)$transactionsPaymentRow['amount']);
            if ('c' == $transactionType) {
                if (TransactionPayment::DORECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                } else if (TransactionPayment::DORECTION_INCOMING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                }
            } else if ('d' == $transactionType) {
                if (TransactionPayment::DORECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                } else if (TransactionPayment::DORECTION_INCOMING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                }
            }
        }
        return $totalAmountTransactionsPayment;
    }

    private function calculateCloseDateTime($transactionId)
    {
        $transactionsPaymentRows = $this->transactionsPaymentsModel->getByTransactionId(
            $transactionId
        );

        $latestTransactionsPaymentCreateAtTime = 0;
        foreach ($transactionsPaymentRows as $transactionsPaymentRow) {
            if (strtotime($transactionsPaymentRow['date']) > $latestTransactionsPaymentCreateAtTime) {
                $latestTransactionsPaymentCreateAtTime = strtotime($transactionsPaymentRow['date']);
            }
        }

        return $latestTransactionsPaymentCreateAtTime;
    }

}
