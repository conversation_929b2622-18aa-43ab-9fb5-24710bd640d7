<?php


namespace ExactGlobeAxians\Infrastructure;

use Invoices;

class CorporationRepository
{
    /**
     * @var Invoices
     */
    private $corporationsModel;

    public function __construct(\Corporations $corporations)
    {
        $this->corporationsModel = $corporations;
    }

    public function findByInvoiceId($invoiceId)
    {
        $select = db()->select()
            ->from(['i' => (new Invoices())->getTableName()], false)
            ->joinLeft(['ir' => (new \InvoicesRuns())->getTableName()], 'ir.id = i.run', false)
            ->joinLeft(['c' => $this->corporationsModel->getTableName()], 'c.id = ir.corporation_id', true)
            ->where('i.id = ?', $invoiceId);

        $id = db()->fetchOne($select);
        return $this->corporationsModel->fetchRowById($id);

    }

    public function findByIdOrFail($corporationId)
    {
        $corporationRow = $this->corporationsModel->getById($corporationId);
        if (!$corporationRow) {
            throw new \Exception('No corporation fond by id:' . $corporationId);
        }
        return $corporationRow;
    }

    public function findAllFinancialImportTransactionsEnabled()
    {
        return $this->corporationsModel->fetchAll([
            'financial_import_transactions = ?' => 'yes',
            'administration IS NOT NULL' => ''
        ]);
    }

    public function findAllFinancialExportEnabled()
    {
        return $this->corporationsModel->fetchAll([
            'financial_export_enabled = ?' => 'yes',
            'administration IS NOT NULL' => ''
        ]);
    }

    public function findById($corporationId)
    {
        return $this->corporationsModel->fetchRowById($corporationId);
    }
}
