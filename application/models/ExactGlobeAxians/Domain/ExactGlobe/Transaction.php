<?php

namespace ExactGlobeAxians\Domain\ExactGlobe;

use SharedKernel\Domain\Model\Money;
/*
 {
    "transaction_id": "Unieke id van de transactie (numeriek) van Exact Globe",
    "invoice_id": "Unieke id van de invoice (globe id dat we in eerder call hebben doorgekregen vanuit Globe) (numeriek)",
    "direction": "incoming/outgoing incoming: positief bedrag - outgoing is negatief bedrag",
    "amount": "in hele centen zonder punten of komma's",
    "bankaccount_customer": "IBAN waarvan betaald is (klant)",
    "description": "Omschrijving van de betaling",
    "date": "Datum van de betaling: YYYY-MM-DD 00:00:00"
},
 */
class Transaction
{

    const DIRECTION_INCOMING = 'incoming';
    const DIRECTION_OUTGOING = 'outgoing';

    private $transactionId;
    private $invoiceId;
    private $direction;
    /**
     * @var Money
     */
    private $amount;
    private $bankaccountCustomer;
    private $description;
    /**
     * @var \DateTime
     */
    private $date;

    public function __construct(
        $transactionId,
        $invoiceId,
        $direction,
        Money $amount,
        $bankaccountCustomer,
        $description,
        \DateTime $date
    )
    {
        $this->transactionId = $transactionId;
        $this->invoiceId = $invoiceId;
        $this->direction = $direction;
        $this->amount = $amount;
        $this->bankaccountCustomer = $bankaccountCustomer;
        $this->description = $description;
        $this->date = $date;
    }

    /**
     * @return mixed
     */
    public function getTransactionId()
    {
        return $this->transactionId;
    }

    /**
     * @return mixed
     */
    public function getInvoiceId()
    {
        return $this->invoiceId;
    }

    /**
     * @return mixed
     */
    public function getDirection()
    {
        return $this->direction;
    }

    /**
     * @return Money
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @return mixed
     */
    public function getBankaccountCustomer()
    {
        return $this->bankaccountCustomer;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @return \DateTime
     */
    public function getDate()
    {
        return $this->date;
    }
}
