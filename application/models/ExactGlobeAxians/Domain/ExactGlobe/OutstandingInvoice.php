<?php

namespace ExactGlobeAxians\Domain\ExactGlobe;

use SharedKernel\Domain\Model\Money;

class OutstandingInvoice
{
    private $invoiceId;
    /**
     * @var Money
     */
    private $outstandingAmount;

    public function __construct(
        $invoiceId,
        Money $outstandingAmount
    )
    {
        $this->invoiceId = $invoiceId;
        $this->outstandingAmount = $outstandingAmount;
    }

    /**
     * @return mixed
     */
    public function getInvoiceId()
    {
        return $this->invoiceId;
    }

    /**
     * @return Money
     */
    public function getOutstandingAmount()
    {
        return $this->outstandingAmount;
    }
}
