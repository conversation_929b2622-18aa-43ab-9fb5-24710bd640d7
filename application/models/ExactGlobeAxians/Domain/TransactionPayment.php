<?php


namespace ExactGlobeAxians\Domain;


class TransactionPayment
{
    const STATUS_CONFIRMD = 'confirmed';
    const DORECTION_INCOMING = 'incoming';
    const DORECTION_OUTGOING = 'outgoing';
    const AMOUNT_SIGN_POSITIVE = 'positive';
    const AMOUNT_SIGN_NEGATICE = 'negative';

    private $transactionId;
    private $dateCreated;
    private $status;
    private $date;
    private $amountSign;
    private $amountCents;
    private $description;
    private $externalId;

    public function __construct(
        $transactionId,
        $date,
        $amountCents,
        $description,
        $externalId
    )
    {
        if (!is_int($amountCents)) {
            throw new \Exception('Transaction payment amount isent a int!');
        }

        if (0 <= $amountCents) {
            $amountSign = self::AMOUNT_SIGN_POSITIVE;
        } else {
            $amountSign = self::AMOUNT_SIGN_NEGATICE;
        }

        $amountCents = abs($amountCents);

        $status = self::STATUS_CONFIRMD;
        $dateCreated = date('Y-m-d H:i:s');

        $this->transactionId = $transactionId;
        $this->dateCreated = $dateCreated;
        $this->status = $status;
        $this->date = $date;
        $this->amountSign = $amountSign;
        $this->amountCents = $amountCents;
        $this->description = $description;
        $this->externalId = $externalId;
    }

    /**
     * @return mixed
     */
    public function getTransactionId()
    {
        return $this->transactionId;
    }

    /**
     * @return mixed
     */
    public function getDateCreated()
    {
        return $this->dateCreated;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @return mixed
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * @return mixed
     */
    public function getAmountSign()
    {
        return $this->amountSign;
    }

    /**
     * @return mixed
     */
    public function getAmountCents()
    {
        return $this->amountCents;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @return mixed
     */
    public function getExternalId()
    {
        return $this->externalId;
    }


}
