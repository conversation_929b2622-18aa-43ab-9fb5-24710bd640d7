<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Infrastructure\SaveDataToDiskService;

class SaveTransactionsPaymentsDataService
{
    const PATH_SAVE_IN = '_data/exact_globe_axians/read-transactions-payments';

    /**
     * @var SaveDataToDiskService
     */
    private $dataToDiskService;

    public function __construct(SaveDataToDiskService $dataToDiskService)
    {
        $this->dataToDiskService = $dataToDiskService;
    }

    public function execute($inputData)
    {
        $filePath = $this->dataToDiskService->save(self::PATH_SAVE_IN, 'json', $inputData);
        return  $filePath;
    }
}
