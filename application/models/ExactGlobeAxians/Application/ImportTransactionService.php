<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Domain\ExactGlobe\Transaction;
use ExactGlobeAxians\Infrastructure\CorporationRepository;
use ExactGlobeAxians\Infrastructure\InvoiceRepository;
use ExactGlobeAxians\Infrastructure\RecalculateTransactionByTransactionPaymentService;
use ExactGlobeAxians\Infrastructure\TransactionPaymentFactory;
use ExactGlobeAxians\Infrastructure\TransactionsPaymentsRepository;
use ExactGlobeAxians\Infrastructure\TransactionsRepository;
use SharedKernel\Domain\Model\Money;

class ImportTransactionService
{
    /**
     * @var CorporationRepository
     */
    private $corporationRepository;
    /**
     * @var InvoiceRepository
     */
    private $invoiceRepository;
    /**
     * @var TransactionsRepository
     */
    private $transactionsRepository;
    /**
     * @var TransactionsPaymentsRepository
     */
    private $transactionsPaymentsRepository;
    /**
     * @var TransactionPaymentFactory
     */
    private $transactionPaymentFactory;
    /**
     * @var RecalculateTransactionByTransactionPaymentService
     */
    private $recalculateTransactionByTransactionPaymentService;

    public function __construct(
        CorporationRepository $corporationRepository,
        InvoiceRepository $invoiceRepository,
        TransactionsRepository $transactionsRepository,
        TransactionsPaymentsRepository $transactionsPaymentsRepository,
        TransactionPaymentFactory $transactionPaymentFactory,
        RecalculateTransactionByTransactionPaymentService $recalculateTransactionByTransactionPaymentService
    )
    {
        $this->corporationRepository = $corporationRepository;
        $this->invoiceRepository = $invoiceRepository;
        $this->transactionsRepository = $transactionsRepository;
        $this->transactionsPaymentsRepository = $transactionsPaymentsRepository;
        $this->transactionPaymentFactory = $transactionPaymentFactory;
        $this->recalculateTransactionByTransactionPaymentService = $recalculateTransactionByTransactionPaymentService;
    }

    public function execute(Transaction $exactGlobeTransaction)
    {
        if (!$this->isInvoiceKnown($exactGlobeTransaction->getInvoiceId())) {
            throw new \Exception(
                'There is no invoice found with id :' .
                $exactGlobeTransaction->getInvoiceId() .
                ' of transaction :'.
                $exactGlobeTransaction->getTransactionId()
            );
        }

        if (!$this->isImportTransactionAllowedByAdministration($exactGlobeTransaction->getInvoiceId())) {
            throw new \Exception(
                'This invoice cant be update by the settings of the administration: ' .
                $exactGlobeTransaction->getTransactionId()
            );
        }

        if (!$this->isInvoiceSynchronized($exactGlobeTransaction->getInvoiceId())) {
            throw new \Exception(
                'This invoice cant be update by "financial_no_sync" :' .
                $exactGlobeTransaction->getInvoiceId() .
                ' of transaction :'.
                $exactGlobeTransaction->getTransactionId()
            );
        }

        if ($this->isTransactionPaymentKnown($exactGlobeTransaction->getTransactionId())) {
            throw new \Exception(
                'This transaction payment is known :' .
                $exactGlobeTransaction->getTransactionId()
            );
        }

        $invoiceRow = $this->getInvoiceByFinancialId($exactGlobeTransaction->getInvoiceId());

        $transactionsRow = $this->transactionsRepository->retrieveByInvoiceId($invoiceRow->id);
        if (!$transactionsRow) {
            throw new \Exception(
                'Omniboxx error invoce whitout transaction:' .
                $exactGlobeTransaction->getInvoiceId() .
                ' of transaction :'.
                $exactGlobeTransaction->getTransactionId()
            );
        }


        $amountCents = 0;
        $globeAmountCents = $exactGlobeTransaction->getAmount()->getAmountInCents();

        if ('c' == $transactionsRow['type']) {
            if (Transaction::DIRECTION_INCOMING == $exactGlobeTransaction->getDirection()) {
                $amountCents -= $globeAmountCents;
            } elseif (Transaction::DIRECTION_OUTGOING == $exactGlobeTransaction->getDirection()) {
                $amountCents += $globeAmountCents;
            }
        } else if ('d' == $transactionsRow['type']) {
            if (Transaction::DIRECTION_INCOMING == $exactGlobeTransaction->getDirection()) {
                $amountCents += $globeAmountCents;
            } elseif (Transaction::DIRECTION_OUTGOING == $exactGlobeTransaction->getDirection()) {
                $amountCents -= $globeAmountCents;
            }
        }

        $transactionPayment = $this->transactionPaymentFactory->make(
            new Money($amountCents),
            $transactionsRow['id'],
            $exactGlobeTransaction->getDescription(),
            $exactGlobeTransaction->getDate(),
            $exactGlobeTransaction->getTransactionId()
        );

        $this->transactionsPaymentsRepository->insert($transactionPayment);

        $this->recalculateTransactionByTransactionPaymentService->execute($transactionsRow['id']);
    }

    private function isImportTransactionAllowedByAdministration($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        $corporationRow = $this->corporationRepository->findByInvoiceId($invoiceRow->id);
        if (!$corporationRow) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_export_enabled']) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_import_transactions']) {
            return false;
        }

        return true;
    }

    private function  getInvoiceByFinancialId($exactGlobeInvoiceId)
    {
        return $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
    }

    private function isInvoiceKnown($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        if (!$invoiceRow) {
            return false;
        }
        return true;
    }


    private function isInvoiceSynchronized($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        if('0' != $invoiceRow->financial_no_sync) {
            return false;
        }
        return true;
    }


    private function isTransactionPaymentKnown($transactionPaymentId)
    {
        $transactionPaymentRow = $this->transactionsPaymentsRepository->findNewestByExternalId($transactionPaymentId);
        if (!$transactionPaymentRow) {
            return false;
        }
        return true;
    }



}
