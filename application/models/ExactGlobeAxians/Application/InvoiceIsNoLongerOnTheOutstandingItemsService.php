<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Infrastructure\CorporationRepository;
use ExactGlobeAxians\Infrastructure\InvoiceNotFoundException;
use ExactGlobeAxians\Infrastructure\InvoiceRepository;
use ExactGlobeAxians\Infrastructure\RecalculateTransactionByTransactionPaymentService;
use ExactGlobeAxians\Infrastructure\TransactionPaymentFactory;
use ExactGlobeAxians\Infrastructure\TransactionsPaymentsRepository;
use ExactGlobeAxians\Infrastructure\TransactionsRepository;
use SharedKernel\Domain\Model\Money;

class InvoiceIsNoLongerOnTheOutstandingItemsService
{
    /**
     * @var InvoiceRepository
     */
    private $invoiceRepository;
    /**
     * @var CorporationRepository
     */
    private $corporationRepository;
    /**
     * @var TransactionsRepository
     */
    private $transactionsRepository;
    /**
     * @var RecalculateTransactionByTransactionPaymentService
     */
    private $recalculateTransactionByTransactionPaymentService;
    /**
     * @var TransactionPaymentFactory
     */
    private $transactionPaymentFactory;
    /**
     * @var TransactionsPaymentsRepository
     */
    private $transactionsPaymentsRepository;

    public function __construct(
        CorporationRepository $corporationRepository,
        InvoiceRepository $invoiceRepository,
        TransactionsRepository $transactionsRepository,
        TransactionPaymentFactory $transactionPaymentFactory,
        TransactionsPaymentsRepository $transactionsPaymentsRepository,
        RecalculateTransactionByTransactionPaymentService $recalculateTransactionByTransactionPaymentService
    )
    {
        $this->corporationRepository = $corporationRepository;
        $this->invoiceRepository = $invoiceRepository;
        $this->transactionsRepository = $transactionsRepository;
        $this->transactionPaymentFactory = $transactionPaymentFactory;
        $this->transactionsPaymentsRepository = $transactionsPaymentsRepository;
        $this->recalculateTransactionByTransactionPaymentService = $recalculateTransactionByTransactionPaymentService;
    }

    public function execute($invoiceId)
    {

        if (!$this->isInvoiceKnown($invoiceId)) {
            throw new InvoiceNotFoundException(
                'There is no invoice found with id:' .
                $invoiceId
            );
        }

        if (!$this->isImportTransactionAllowedByAdministration($invoiceId)) {
            throw new \Exception(
                'This invoice cant be updated by the settings of the administration:' .
                $invoiceId
            );
        }

        if (!$this->isInvoiceSynchronized($invoiceId)) {
            throw new \Exception(
                'This invoice cant be updated by "financial_no_sync" :' .
                $invoiceId
            );
        }



        $transactionsRow = $this->transactionsRepository->retrieveByInvoiceId($invoiceId);
        if (!$transactionsRow) {
            throw new \Exception('Error, ths is not passable: '. $invoiceId);
        }

        $transactionId = $transactionsRow['id'];
        $transactionType = $transactionsRow['type'];
        $transactionAmount = new Money($transactionsRow['amount']);
        $transactionPayed = new Money($transactionsRow['payed']);

        if ("c" == $transactionType) {
            $diffAmount = $transactionPayed->subtract($transactionAmount);
        } else if ("d" == $transactionType) {
            $diffAmount = $transactionAmount->subtract($transactionPayed);
        }

        if (0 != $diffAmount->getAmountInCents()) {
            $transactionPayment = $this->transactionPaymentFactory->make(
                $diffAmount,
                $transactionsRow['id'],
                'Sync outStanding Exact Globe Axians is no longer on the outstanding list'
            );

            $this->transactionsPaymentsRepository->insert($transactionPayment);
        }

        $this->recalculateTransactionByTransactionPaymentService->execute($transactionId);

    }

    private function isImportTransactionAllowedByAdministration($invoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findById($invoiceId);
        $corporationRow = $this->corporationRepository->findByInvoiceId($invoiceRow->id);
        if (!$corporationRow) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_export_enabled']) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_import_transactions']) {
            return false;
        }

        return true;
    }

    private function isInvoiceKnown($invoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findById($invoiceId);
        if (!$invoiceRow) {
            return false;
        }
        return true;
    }


    private function isInvoiceSynchronized($invoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findById($invoiceId);
        if('0' != $invoiceRow->financial_no_sync) {
            return false;
        }
        return true;
    }

}
