<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Infrastructure\CorporationRepository;
use ExactGlobeAxians\Infrastructure\InvoiceNotFoundException;
use ExactGlobeAxians\Infrastructure\InvoiceRepository;
use ExactGlobeAxians\Infrastructure\RecalculateTransactionByTransactionPaymentService;
use ExactGlobeAxians\Infrastructure\TransactionPaymentFactory;
use ExactGlobeAxians\Infrastructure\TransactionsPaymentsRepository;
use ExactGlobeAxians\Infrastructure\TransactionsRepository;
use SharedKernel\Domain\Model\Money;

class SyncOutStandingInvoiceAmountService
{
    /**
     * @var InvoiceRepository
     */
    private $invoiceRepository;
    /**
     * @var TransactionsPaymentsRepository
     */
    private $transactionsPaymentsRepository;
    /**
     * @var TransactionsRepository
     */
    private $transactionsRepository;
    /**
     * @var RecalculateTransactionByTransactionPaymentService
     */
    private $recalculateTransactionByTransactionPaymentService;
    /**
     * @var TransactionPaymentFactory
     */
    private $transactionPaymentFactory;
    /**
     * @var CorporationRepository
     */
    private $corporationRepository;

    public function __construct(
        CorporationRepository $corporationRepository,
        InvoiceRepository $invoiceRepository,
        TransactionsRepository $transactionsRepository,
        TransactionsPaymentsRepository $transactionsPaymentsRepository,
        TransactionPaymentFactory $transactionPaymentFactory,
        RecalculateTransactionByTransactionPaymentService $recalculateTransactionByTransactionPaymentService
    )
    {
        $this->corporationRepository = $corporationRepository;
        $this->invoiceRepository = $invoiceRepository;
        $this->transactionsRepository = $transactionsRepository;
        $this->transactionsPaymentsRepository = $transactionsPaymentsRepository;
        $this->transactionPaymentFactory = $transactionPaymentFactory;
        $this->recalculateTransactionByTransactionPaymentService = $recalculateTransactionByTransactionPaymentService;
    }

    public function execute($financialId, Money $amount)
    {
        if (!$this->isInvoiceKnown($financialId)) {
            throw new InvoiceNotFoundException(
                'There is no invoice found with id:' .
                $financialId
            );
        }

        if (!$this->isImportTransactionAllowedByAdministration($financialId)) {
            throw new \Exception(
                'This invoice cant be updated by the settings of the administration: ' .
                $financialId
            );
        }

        if (!$this->isInvoiceSynchronized($financialId)) {
            throw new \Exception(
                'This invoice cant be updated by "financial_no_sync" :' .
                $financialId
            );
        }

        try {
            $invoiceRow = $this->invoiceRepository->findByFinancialId($financialId);
        } catch (\Exception $exception) {
            throw $exception;
        }


        try {
            $transactionsRow = $this->transactionsRepository->retrieveByInvoiceId($invoiceRow->id);
        } catch (\Exception $exception) {
            throw $exception;

        }

        if (!$transactionsRow) {
            throw new \Exception('Error: this is not passable: '. $invoiceRow->id);
        }


        $transactionsType = $transactionsRow['type'];
        $moneyPayed = new Money($transactionsRow['payed']);
        $moneyAmount = new Money($transactionsRow['amount']);

        $amountInCents = $amount->getAmountInCents();
        if ("c" == $transactionsType) {
            if (0 > $amountInCents) {
                $amountInCents = abs($amountInCents);
                $amount = new Money($amountInCents);

                $tempMoneyDiff = $moneyAmount->add($amount);
                $moneyDiff = $moneyPayed->subtract($tempMoneyDiff);
            } else {
                $tempMoneyDiff = $moneyAmount->subtract($amount);
                $moneyDiff = $moneyPayed->subtract($tempMoneyDiff);
            }
        } else if ("d" == $transactionsType) {
            if (0 > $amountInCents) {
                $amountInCents = abs($amountInCents);
                $amount = new Money($amountInCents);

                $tempMoneyDiff = $moneyAmount->subtract($amount);
                $moneyDiff = $tempMoneyDiff->subtract($moneyPayed);
            } else {
                $tempMoneyDiff = $moneyAmount->subtract($amount);
                $moneyDiff = $moneyPayed->subtract($tempMoneyDiff);
            }
        }

        if ($moneyDiff->getAmountInCents() == 0) {
            return true;
        }

        $transactionPayment = $this->transactionPaymentFactory->make(
            $moneyDiff,
            $transactionsRow['id'],
            'Sync outStanding Exact Globe Axians'
        );

        $this->transactionsPaymentsRepository->insert($transactionPayment);

        $this->recalculateTransactionByTransactionPaymentService->execute($transactionsRow['id']);
        return true;
    }

    private function isImportTransactionAllowedByAdministration($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        $corporationRow = $this->corporationRepository->findByInvoiceId($invoiceRow->id);
        if (!$corporationRow) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_export_enabled']) {
            return false;
        }

        if ('yes' !== $corporationRow['financial_import_transactions']) {
            return false;
        }

        return true;
    }

    private function isInvoiceKnown($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        if (!$invoiceRow) {
            return false;
        }
        return true;
    }


    private function isInvoiceSynchronized($exactGlobeInvoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($exactGlobeInvoiceId);
        if('0' != $invoiceRow->financial_no_sync) {
            return false;
        }
        return true;
    }
}
