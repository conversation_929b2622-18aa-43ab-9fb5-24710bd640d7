<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Infrastructure\SaveDataToDiskService;

class SaveInvoiceIdMappingDataService
{
    const PATH_SAVE_IN = '_data/exact_globe_axians/read-outstanding-amounts';
    /**
     * @var SaveDataToDiskService
     */
    private $dataToDiskService;

    public function __construct(SaveDataToDiskService $dataToDiskService)
    {
        $this->dataToDiskService = $dataToDiskService;
    }

    public function execute($inputData)
    {
        $filePath = $this->dataToDiskService->save(self::PATH_SAVE_IN, 'xml', $inputData);
        return  $filePath;
    }
}
