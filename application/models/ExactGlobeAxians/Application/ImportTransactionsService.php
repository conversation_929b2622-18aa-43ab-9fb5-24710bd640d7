<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Domain\ExactGlobe\Transaction;
use ExactGlobeAxians\Infrastructure\ExactGlobe\GetTransactionsFromJsonFileService;
use phpDocumentor\Reflection\DocBlock\Tags\Throws;

class ImportTransactionsService
{

    /**
     * @var GetTransactionsFromJsonFileService
     */
    private $getTransactionsFromJsonFileService;
    /**
     * @var ImportTransactionService
     */
    private $importTransactionService;

    public function __construct(
        GetTransactionsFromJsonFileService $getTransactionsFromJsonFileService,
        ImportTransactionService           $importTransactionService
    )
    {
        $this->getTransactionsFromJsonFileService = $getTransactionsFromJsonFileService;
        $this->importTransactionService = $importTransactionService;
    }

    public function execute($transactionJsonFile)
    {
        try {
            $transactions = $this->getTransactionsFromJsonFileService->execute($transactionJsonFile);
        } catch (\Exception $exception) {
            throw $exception;
        }


        /**
         * @var $transaction Transaction
         */
        foreach ($transactions as $transaction) {
            try {
                $this->importTransactionService->execute($transaction);

            } catch (\Exception $exception) {
                \Logger::add(['ExactGlobe' , 'ImportTransactions'], $exception->getMessage());
            }
        }
    }
}



