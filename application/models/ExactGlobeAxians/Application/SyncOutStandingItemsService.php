<?php

namespace ExactGlobeAxians\Application;

use ExactGlobeAxians\Domain\ExactGlobe\OutstandingInvoice;
use ExactGlobeAxians\Infrastructure\ExactGlobe\GetOutStandingItemsFromJsonFileService;
use ExactGlobeAxians\Infrastructure\InvoiceNotFoundException;
use ExactGlobeAxians\Infrastructure\RetrieveOutstandingItemsService;
use SharedKernel\Domain\Model\Money;

class SyncOutStandingItemsService
{

    /**
     * @var GetOutStandingItemsFromJsonFileService
     */
    private $getOutStandingItemsFromJsonFileService;
    /**
     * @var SyncOutStandingInvoiceAmountService
     */
    private $syncOutStandingInvoiceAmountService;
    /**
     * @var RetrieveOutstandingItemsService
     */
    private $retrieveOutstandingItemsService;
    /**
     * @var InvoiceIsNoLongerOnTheOutstandingItemsService
     */
    private $invoiceIsNoLongerOnTheOutstandingItemsService;

    public function __construct(
        GetOutStandingItemsFromJsonFileService $getOutStandingItemsFromJsonFileService,
        SyncOutStandingInvoiceAmountService $syncOutStandingInvoiceAmountService,
        RetrieveOutstandingItemsService $retrieveOutstandingItemsService,
        InvoiceIsNoLongerOnTheOutstandingItemsService $invoiceIsNoLongerOnTheOutstandingItemsService
    )
    {
        $this->getOutStandingItemsFromJsonFileService = $getOutStandingItemsFromJsonFileService;
        $this->syncOutStandingInvoiceAmountService = $syncOutStandingInvoiceAmountService;
        $this->retrieveOutstandingItemsService = $retrieveOutstandingItemsService;
        $this->invoiceIsNoLongerOnTheOutstandingItemsService = $invoiceIsNoLongerOnTheOutstandingItemsService;
    }

    public function execute($outStandingItemsJsonFile)
    {
        try {
            $outStandingItems = $this->getOutStandingItemsFromJsonFileService->execute($outStandingItemsJsonFile);
        } catch (\Exception $exception) {
            throw $exception;
        }

        /**
         * @var $outStandingItem OutstandingInvoice
         */
        foreach ($outStandingItems as $outStandingItem) {
            try {
                $this->syncOutStandingInvoiceAmountService->execute(
                    $outStandingItem->getInvoiceId(),
                    $outStandingItem->getOutstandingAmount()
                );
            } catch (InvoiceNotFoundException $invoiceNotFoundException) {
                continue;
            } catch (\Exception $exception) {
                \Logger::add(['ExactGlobe' , 'SyncOutStanding'], $exception->getMessage());
            }
        }

        $OBoutStandingItems = $this->retrieveOutstandingItemsService->retrieve();

        foreach ($OBoutStandingItems as $OBoutStandingItem) {
            if ($this->isItOnTheExactGlobeAxiansOutStandingItemsList($outStandingItems, $OBoutStandingItem)) {
                continue;
            }

            try {
                $this->invoiceIsNoLongerOnTheOutstandingItemsService->execute($OBoutStandingItem['id']);
            } catch (\Exception $exception) {
                \Logger::add(['ExactGlobe' , 'SyncOutStanding'], $exception->getMessage());
                error_log($exception->getMessage());
            }
        }
    }

    private function isItOnTheExactGlobeAxiansOutStandingItemsList($outStandingItems, $OBoutStandingItem)
    {
        /**
         * @var $outStandingItem OutstandingInvoice
         */
        foreach ($outStandingItems as $outStandingItem) {
            if ($outStandingItem->getInvoiceId() === $OBoutStandingItem['financial_invoice_id']) {
                return true;
            }
        }
        return false;
    }

}
