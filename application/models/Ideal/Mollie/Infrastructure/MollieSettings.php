<?php

namespace Ideal\Mollie\Infrastructure;

use Settings;

/**
 * Class MollieSettings
 * @package Ideal\Mollie\Infrastructure
 */
class MollieSettings
{
    public static function getApiKey($corporationId = false) {
        if (!$corporationId) {
            return Settings::get('mollie_ideal_api_key');
        }

        $corporationMollieKey = (new \Corporations())->getMollieKey($corporationId);
        $mollieKey = $corporationMollieKey != '' ? $corporationMollieKey : Settings::get('mollie_ideal_api_key');
        return $mollieKey;
    }

    public static function getWebhookUrl() {
        global $config;
        $baseUrl = ($config->app->secure ? 'https://' : 'http://') . $config->app->server;
        return $baseUrl . '/transaction/mollie-ideal-payment';
    }
    public static function getRedirectUrl() {
        global $config;
        return ($config->app->secure ? 'https://' : 'http://') . $config->app->server;
    }

    public static function getActivateAccountWebhookUrl() {
        global $config;
        $baseUrl = ($config->app->secure ? 'https://' : 'http://') . $config->app->server;
        return $baseUrl . '/user/activate-account-callback';
    }

    public static function getActivateAccountRedirectUrl() {
        global $config;
        $baseUrl = ($config->app->secure ? 'https://' : 'http://') . $config->app->server;
        return $baseUrl . '/user/dashboard';
    }
}
