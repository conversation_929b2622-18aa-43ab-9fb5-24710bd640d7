<?php

namespace Ideal\Mollie\Application;

use Ideal\Mollie\Infrastructure\MollieSettings;
use Mollie\Api\MollieApiClient;
use Users;

/**
 * Class ProcessActivateAccountPaymentService
 * @package Ideal\Mollie\Application
 */
class ProcessActivateAccountPaymentService
{
    /**
     * @var Users
     */
    private $userModel;

    /**
     * ProcessActivateAccountPaymentService constructor.
     * @param Users $userModel
     */
    public function __construct(Users $userModel) {
        $this->userModel = $userModel;
    }

    public function process($molliePaymentId) {
        $mollieApi = $this->initializeMollieApi();
        $molliePayment = $mollieApi->payments->get($molliePaymentId);
        $paymentMetadata = json_decode($molliePayment->metadata, true);
        $userId = $paymentMetadata['userId'];
        if ($molliePayment->isPaid()) {
            $this->enableUserPayedAccount($userId, $molliePayment);
        }
    }

    private function initializeMollieApi() {
        $mollieApi = new MollieApiClient();
        $mollieApi->setApiKey(MollieSettings::getApiKey());
        return $mollieApi;
    }

    private function enableUserPayedAccount($userId, $molliePayment) {
        $userRow = $this->userModel->getById($userId);
        if ($userRow === null) {
            error_log('User ' . $userId . ' not found for payment activation, payment id ' . $molliePayment->id);
        }

        if (empty($userRow->payed_account_start_date)) {
            $newPayedAccountStartSate = date('Y-m-d');
        } else if (strtotime('+1 year', strtotime($userRow->payed_account_start_date)) < time()) {
            $newPayedAccountStartSate = date('Y-m-d');
        } else {
            $newPayedAccountStartSate = date(
                'Y-m-d',
                strtotime('+1 year', strtotime($userRow->payed_account_start_date))
            );
        }

        $userRow->payed_account_start_date = $newPayedAccountStartSate;
        $userRow->payed_account = 1;
        $userRow->save();
    }
}
