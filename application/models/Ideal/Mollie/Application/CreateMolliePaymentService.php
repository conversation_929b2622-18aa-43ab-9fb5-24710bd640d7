<?php

namespace Ideal\Mollie\Application;

use Mollie\Api\Exceptions\ApiException;
use Mollie\Api\MollieApiClient;
use Ideal\Mollie\Infrastructure\MollieSettings;

/**
 * Class CreateMolliePaymentService
 * @package Ideal\Mollie\Application
 */
class CreateMolliePaymentService
{
    /**
     * @param array $paymentData
     * @param string|null $corporationId
     * @throws ApiException
     * @throws \Mollie\Api\Exceptions\IncompatiblePlatform
     */
    public function create(array $paymentData, $corporationId = false) {

        $this->addUrlDataToPaymentData($paymentData);
        $mollieApi = $this->initializeMollieApi($corporationId);
        $payment = $mollieApi->payments->create($paymentData);
        \Logger::add(['mollie-payments', 'create-payment'], 'PaymentId: ' . $payment->id, $paymentData);
        header('Location: ' . $payment->getCheckoutUrl(), true, 303);
        exit;
    }

    /**
     * @throws ApiException
     * @throws \Mollie\Api\Exceptions\IncompatiblePlatform
     * @return MollieApiClient
     * @param string|null $corporationId
     */
    private function initializeMollieApi($corporationId = false) {
        $mollieApi = new MollieApiClient();
        $mollieApi->setApiKey(MollieSettings::getApiKey($corporationId));
        return $mollieApi;
    }

    /**
     * @param array &$paymentData
     */
    private function addUrlDataToPaymentData(array &$paymentData) {
        if (!empty($paymentData['activate_account_payment']) && $paymentData['activate_account_payment'] === 1) {
            $paymentData['webhookUrl'] = MollieSettings::getActivateAccountWebhookUrl();
            $paymentData['redirectUrl'] = MollieSettings::getActivateAccountRedirectUrl();
            unset($paymentData['activate_account_payment']);
        } else {
            $paymentData['redirectUrl'] = MollieSettings::getRedirectUrl();
            $paymentData['webhookUrl'] = MollieSettings::getWebhookUrl();
        }
    }
}
