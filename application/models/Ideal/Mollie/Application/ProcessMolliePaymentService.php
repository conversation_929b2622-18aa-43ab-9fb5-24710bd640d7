<?php

namespace Ideal\Mollie\Application;

use Ideal\Mollie\Infrastructure\MollieSettings;
use Invoice\RecalculateInvoicePaymentsService;
use Mollie\Api\MollieApiClient;
use Transactions;
use TransactionsPayments;

/**
 * Class ProcessMolliePaymentService
 * @package Ideal\Mollie\Application
 */
class ProcessMolliePaymentService
{
    /**
     * @var Transactions
     */
    private $transactionsModel;

    /**
     * @var TransactionsPayments
     */
    private $transactionPaymentModel;

    /**
     * ProcessMolliePaymentService constructor.
     * @param Transactions $transactionsModel
     * @param TransactionsPayments $transactionPaymentModel
     */
    public function __construct(
        Transactions $transactionsModel,
        TransactionsPayments $transactionPaymentModel
    ) {
        $this->transactionsModel = $transactionsModel;
        $this->transactionPaymentModel = $transactionPaymentModel;
    }

    public function process($molliePaymentId) {
        $mollieApi = $this->initializeMollieApi();
        $molliePayment = $mollieApi->payments->get($molliePaymentId);
        $paymentMetadata = json_decode($molliePayment->metadata, true);
        $transactionId = $paymentMetadata['transactionId'];
        if ($molliePayment->isPaid()) {
            $transactionsPaymentRow = $this->transactionPaymentModel->findByExternalId($molliePayment->id);
            if ($transactionsPaymentRow) {
                error_log('Mollie payen is known: '. $molliePayment->id);
                return;
            }

            $this->addPaymentToInvoice($transactionId, $molliePayment);
        }
    }

    private function initializeMollieApi() {
        $mollieApi = new MollieApiClient();
        $mollieApi->setApiKey(MollieSettings::getApiKey());
        return $mollieApi;
    }

    private function addPaymentToInvoice($transactionId, $molliePayment) {
        $transactionsRow = $this->transactionsModel->getById($transactionId);
        $molliePaymentAmount = (int) ($molliePayment->amount->value * 100);

        if (!$transactionsRow) {
            return false;
        }

        $transactionsRow->payed += $molliePaymentAmount;
        $totoalAmount = $transactionsRow->amount + $transactionsRow->penalty;
        if ($totoalAmount === $transactionsRow->payed) {
            $transactionsRow->closed = 1;
        }
        $transactionsRow->save();

        $this->transactionPaymentModel->add([
            'transaction' => 			$transactionsRow->id,
            'status' => 				'confirmed',
            'date' => 					date('d-m-Y'),
            'direction' =>				$transactionsRow->type === 'c' ? 'incoming' : 'outgoing',
            'amount' =>					$molliePaymentAmount,
            'description' =>			'Betaald via iDEAL - Mollie',
            'external_id' =>            $molliePayment->id,
        ]);

        (new RecalculateInvoicePaymentsService())->execute($transactionsRow->invoice);
    }
}
