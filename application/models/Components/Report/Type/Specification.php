<?
	class Components_Report_Type_Specification extends Components_Report {

		protected function getData(){

			$data = array();

			foreach($this->items as &$item){

				$objectgroup_id = $item->getObjectgroupId();

				foreach($item->loadData() as $component_id => $component){
					$og_type = $item->getObjectgroupType();


					$tax = tax()->set($component['tax_rate'])->setAmount($component['value']);

					$item_key = $component['component_id'] . '_' . $og_type . '_' . $energy_law;
					$data[$item_key]['component'] = $component['name'];
					$data[$item_key]['component_id'] = $component['component_id'];
					$data[$item_key]['objectgroup_type'] = $og_type;
					$data[$item_key]['ledger'] = $component['ledger'];
					$data[$item_key]['value_excl'] += $component['value'];
					$data[$item_key]['value_incl'] += $tax->add();
					$data[$item_key]['value_tax'] = $data[$item_key]['value_incl'] - $data[$item_key]['value_excl'];

					$data[$item_key]['project'] = $item->getProjectName();
					$data[$item_key]['objectgroup'] = $item->getObjectgroupName();
					$data[$item_key]['objectgroup_id'] = $item->getObjectgroupId();
					$data[$item_key]['source'] = $item->getSource();
				}

			}

			return $data;
		}
	}