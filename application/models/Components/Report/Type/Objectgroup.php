<?
	class Components_Report_Type_Objectgroup extends Components_Report {

		protected function getData(){

			$data = array();

			foreach($this->items as &$item){

				$objectgroup_id = $item->getObjectgroupId();

				foreach($item->loadData() as $component){

					if(!($component['component_id'] > 0))
						$component['component_id'] = 'false_' . $component['description'];

					$data[$objectgroup_id]['project'] = $item->getProjectName();
					$data[$objectgroup_id]['objectgroup'] = $item->getObjectgroupName();
					$data[$objectgroup_id]['objectgroup_id'] = $item->getObjectgroupId();
					$data[$objectgroup_id]['source'] = $item->getSource();
					$data[$objectgroup_id]['component_' . $component['component_id']] += $component['value'];
					$data[$objectgroup_id]['component_total'] += $component['value'] + $component['tax_amount'];

					if(abs($component['tax_amount']) > 0)
						$data[$objectgroup_id]['component_tax_rate_' . $component['tax_rate']] += $component['tax_amount'];
				}
			}

			return $data;
		}


		public function getComponentLabels(){

			$components = array();
			$tax_rates = [];

			foreach($this->items as &$item)
				foreach($item->loadData() as $component) {

					if(!($component['component_id'] > 0)) {
						$component['component_id'] = 'false_' . $component['description'];
						$component['name'] = $component['description'];
					}

					$components[$component['component_id']] = $component['name'];
					if(abs($component['tax_amount']) > 0)
						$tax_rates[] = $component['tax_rate'];
				}

			foreach(array_unique($tax_rates) as $tax_rate)
				$components['tax_rate_' . $tax_rate] = 'BTW ' . $tax_rate . '%';

			$components['total'] = 'Totaal';

			return $components;
		}

	}