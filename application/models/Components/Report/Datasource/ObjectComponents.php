<?
	class Components_Report_Datasource_ObjectComponents extends Components_Report_Datasource {

		private $version_id;
		public $period_type;
		public $number_of_periods = 12;

		protected function hasTenant(){
			$select = db()->select()
				->from('users_objects', array('id'))
				->where('customer > 0')
				->where('finaldate = ?', '1')
				->where('role = ?', 'normal')
				->where('object = ?', $this->object_id)
				->where('`from` <= ?', $this->year . '-' . $this->month . '-01')
				->where('till IS NULL OR till > ?', $this->year . '-' . $this->month . '-01');

			$uo_row = db()->fetchRow($select);

			return $uo_row ? true : false;
		}
		
		public function fetchAllData($object_ids){
			$this->data = [];

			if(!$this->year) return;

			$select = db()->select()
				->from(array('ocv' => 'objects_components_versions'), ['id'])
				->where('ocv.concept = ?', false)
				->where('ocv.date IS NULL OR ocv.date <= ?', date('Y-m-d', mktime(0, 0, 0, 1, 0, $this->year+1)))
				->where('ocv.object IN (' . implode_for_where_in($object_ids) . ')')
                ->order(['ocv.date ASC', 'ocv.number ASC'])
			;

			$version_ids = db()->fetchCol($select);

			$select = db()->select()
				->from(array('ocv' => 'objects_components_values'), array('value' => 'value_excl', 'tax_amount' => '(ocv.`value_incl` - ocv.`value_excl`)', 'object'))
				->joinLeft(array('ver' => 'objects_components_versions'), 'ver.id = ocv.version', ['ocv_date' => 'date'])
				->joinLeft(array('c' => 'components'), 'c.id = ocv.component', array('name', 'component_id' => 'id', 'component_category' => 'category', 'tax_rate', 'ledger' ))
				->joinLeft(array('u' => 'units'), 'u.id = c.unit', array('unit' => 'name'))
				->where('ocv.version IN (' . implode_for_where_in($version_ids) . ')')
			;

			if(!is_null($this->filters['component_category']))
				$select->where('c.category = ?', $this->filters['component_category']);

			if(!is_null($this->filters['component_category']))
				$select->where('c.category = ?', $this->filters['energy_law_component']);

			$values = db()->fetchAll($select);

			$values_per_period = [];

			foreach(range(1,$this->number_of_periods) as $period)
				foreach($values as $value){
					if($this->period_type == 'fourweekly')
						$period_start_date = strtotime($this->year . 'W' . str_pad(($period * 4)-3, 2, '0', STR_PAD_LEFT));
					else
						$period_start_date = mktime(0, 0, 0, $period, 1, $this->year);

					$ocv_start_date = strtotime($value['ocv_date']);

					if($period_start_date < $ocv_start_date) continue;

					$value['period'] = $period;

					$values_pp_key = $period . '_' . $value['component_id'] . '_' . $value['object'];
					if(!isset($values_per_period[$values_pp_key]))
						$values_per_period[$values_pp_key] = $value;
					else {
						$values_per_period[$values_pp_key]['value'] = $value['value'];
						$values_per_period[$values_pp_key]['tax_amount'] = $value['tax_amount'];
					}
				}

			return $values_per_period;
		}

		public function loadData(){
			foreach($this->data as $data_key => $data_row){
				if(!$this->hasTenant()) unset($this->data[$data_key]);

				$this->data[$data_key]['tax_rate'] = tax()->setLevel($data_row['tax_rate'])->get()->getRate();

				if($this->divider >= 1) {
					$this->data[$data_key]['value'] /= ($this->number_of_periods / $this->divider);
					$this->data[$data_key]['tax_amount'] /= ($this->number_of_periods / $this->divider);
				}

				if(abs($this->data[$data_key]['value']) <= 0)  unset($this->data[$data_key]);

				//p($this->data[$data_key]['value']);
			}
			//p($this->data, 'die');
		}

	}

