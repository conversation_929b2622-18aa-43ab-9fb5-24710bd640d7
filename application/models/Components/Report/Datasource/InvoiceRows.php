<?
	class Components_Report_Datasource_InvoiceRows extends Components_Report_Datasource {

		public $period_type;
		public $number_of_periods = 12;
		public $corporations = [];

		public function fetchAllData($object_ids = []){

			$select = db()->select()

                ->from(['i' => 'invoices'], ['customid', 'invoice' => 'id', 'run'])
                ->joinLeft(['icc' => 'invoice_custom_credit'], 'icc.custom = i.customid', ['test' => 'id'])
                ->joinLeft(
                    ['irows' => 'invoice_rows'],
                    'irows.invoice = COALESCE(icc.credit, i.id)',
                    [
                        'value' => 'SUM(amount_ex_vat * IF(icc.id, -1, 1))',
                        'tax_rate' => 'vat_percentage',
                        'tax_amount' => 'SUM(amount_vat * IF(icc.id, -1, 1))',
                        'description',
                    ]
                )
				->joinLeft(
				    ['ir' => 'invoices_run'],
                    'ir.id = i.run',
                    [
                        'periodType' => 'period',
                        'period' => 'MONTH(ir.start)',
                        'start',
                        'end'
                    ]
                )
				->joinLeft(['p' => 'projects'], 'p.id = ir.project', false)
				->joinLeft(array('ic' => 'invoice_custom'), 'ic.id = i.customid', ['ic_object' => 'object'])
				->joinLeft(array('uo' => 'users_objects'), 'uo.id = i.users_objects', ['uo_object' => 'object'])
				->joinLeft(array('ocv' => 'objects_components_values'), 'ocv.id = irows.object_component_values_id', ['ocv_object' => 'object'])
				->joinLeft(array('c' => 'components'), 'c.id = ocv.component OR c.id = irows.component_id', array('name', 'component_id' => 'id', 'component_category' => 'category', 'ledger'))
				->joinLeft(array('u' => 'units'), 'u.id = c.unit', array('unit' => 'name'))
				->where($this->getObjectWhereStatement($object_ids), $this->object_id)
				->where('YEAR(ir.start) = ?', $this->year)
				->where('irows.is_input = ?', true)
				->where('ic.is_purchase = ? OR ic.is_purchase IS NULL', false)
				->where('ir.status = ?', '2')
				->group('ocv.object')
				->group('uo.object')
				->group('ic.object')
				->group('ir.start')
				->group('c.id')
			;

			if(count($this->corporations) > 0) {
                $corporation_columns = 'ir.corporation_id, ic.corporation, p.corporation';
                $where_in_query_part = ' (' . implode_for_where_in($this->corporations) . ')';

                $select->where('COALESCE(' . $corporation_columns . ') IN ' . $where_in_query_part);
            }


			if(!is_null($this->filters['component_category']))
				$select->where('c.category = ?', $this->filters['energy_law_component']);

			if(!is_null($this->filters['energy_law']))
				$select->where('c.energy_law = ?', $this->filters['energy_law']);

			$values = db()->fetchAll($select);

			$values_per_period = [];

			$multiplierToYearlyAmountPerInvoiceType = InvoiceRun::getAllRates();

			foreach(range(1,$this->number_of_periods) as $period)
				foreach($values as $value){
					if($this->period_type == 'fourweekly')
						$period_start_date = strtotime($this->year . 'W' . str_pad(($period * 4)-3, 2, '0', STR_PAD_LEFT));
					else
						$period_start_date = mktime(0, 0, 0, $period, 1, $this->year);

					$ocv_start_date = strtotime($value['start']);
					$ocv_end_date = strtotime($value['end']);

					if($period_start_date < $ocv_start_date) continue;
					if($period_start_date > $ocv_end_date) continue;

                    $value['multiplierToYearlyAmount'] = $multiplierToYearlyAmountPerInvoiceType[$value['periodType']];
                    $value['dividerToMonthlyAmount'] = $this->number_of_periods / $value['multiplierToYearlyAmount'];

                    $value['value'] /= $value['dividerToMonthlyAmount'];
                    $value['tax_amount'] /= $value['dividerToMonthlyAmount'];

					$value['period'] = $period;

					$value['object_id'] = $value['ocv_object'] > 0 ? $value['ocv_object'] : false;

					if(!$value['object_id'])
						$value['object_id'] = $value['uo_object'] > 0 ? $value['uo_object'] : false;

					if(!$value['object_id'])
						$value['object_id'] = $value['ic_object'] > 0 ? $value['ic_object'] : false;

					$values_pp_key = $period . '_' . $value['component_id'] . '_' . $value['object_id'];
					if(!isset($values_per_period[$values_pp_key]))
						$values_per_period[$values_pp_key] = $value;
					else {
						$values_per_period[$values_pp_key]['value'] += $value['value'];
						$values_per_period[$values_pp_key]['tax_amount'] += $value['tax_amount'];
					}
				}

			return $values_per_period;
		}

        private function getObjectWhereStatement($object_ids)
        {
            $objects_in = ' IN (' . implode_for_where_in($object_ids) . ')';

            return
                'ocv.object ' . $objects_in . ' OR ' .
                'uo.object ' . $objects_in . ' OR ' .
                'irows.object ' . $objects_in . ' OR ' .
                '(ic.object ' . $objects_in . ' AND ic.user > 0)';
		}
	}
