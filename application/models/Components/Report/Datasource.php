<?	
	class Components_Report_Datasource {

		public $data = array();

		protected $object_id;
		protected $year;
		protected $month;		

		protected $filters;

		public function __construct($object_id, $month, $year, $filters){
			$this->object_id = $object_id;
			$this->month = $month;
			$this->year = $year;
			
			$this->filters = $filters;
		}

		public function isAvailable(){
			return count($this->data) > 0;
		}

		public function getData(){
			return $this->data;
		}

        public function loadData()
        {

        }
	}
