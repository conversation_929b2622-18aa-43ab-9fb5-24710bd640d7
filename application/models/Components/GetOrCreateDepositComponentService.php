<?php

namespace Components;

use Components as ComponentModel;

class GetOrCreateDepositComponentService
{
    /**
     * @var ComponentModel $componentModel;
     */
    private $componentModel;

    public function __construct(ComponentModel $componentModel)
    {
        $this->componentModel = $componentModel;
    }

    public function execute()
    {
        $componentRow = $this->findDepositComponentRow();

        if ($componentRow) {
            return $componentRow->id;
        }

        $componentRow = $this->findMatchingComponentRow();

        if ($componentRow) {
            $this->markComponentRowAsDepositComponent($componentRow);
            return $componentRow->id;
        }

        $componentRow = $this->createComponentRow();

        return $componentRow->id;
    }

    private function findDepositComponentRow()
    {
        return $this->componentModel->matchRow(['is_deposit' => true]);
    }

    private function findMatchingComponentRow()
    {
        $componentModelSelect = $this->componentModel->select()
            ->where('name LIKE "%Waarborg%"')
            ->where('cost_type = ? OR cost_type IS NULL OR cost_type = ""', 'turnover')
            ->order('deleted ASC')
            ->order('(project = 0 OR project IS NULL) DESC')
            ->order('(`cost_type` = "turnover") DESC')
            ;

        return $this->componentModel->fetchRow($componentModelSelect);
    }

    private function markComponentRowAsDepositComponent($componentRow)
    {
        $componentRow
            ->setFromArray([
                'is_deposit' => true,
            ])
            ->save();
    }

    private function createComponentRow()
    {
        $componentRow = $this->componentModel->createRow([
            'cost_type' => 'turnover',
            'type' => 'particulier',
            'name' => 'Waarborgsom',
            'name_en' => 'Deposit',
            'name_fr' => 'dépôt',
            'is_rental' => false,
            'tax_rate' => 0,
            'is_deposit' => true,
        ]);

        $componentRow->save();

        return $componentRow;
    }
}
