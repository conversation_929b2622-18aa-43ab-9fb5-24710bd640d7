<?php

namespace Inspection;

use InspectionItem as InspectionItemModel;

class CopyInspectionItemsFromPreviousInspectionService
{
    public function execute($newInspectionId, $newInspectionTypeId)
    {
        if ($this->newInspectionTypePreventsCopyingOfItems($newInspectionTypeId)) {
            return;
        }
        
        $inspectionData = $this->getInspectionData($newInspectionId);

        if ($inspectionData['type'] === 'first') {
            $inspectionData['users_objects_id'] = $this->getPreviousUserObjectId($inspectionData['users_objects_id']);
        }

        if (!($inspectionData['users_objects_id'] > 0)) {
            return;
        }

        $previousInspectionId = $this->getPreviousInspectionIdForUo($inspectionData['users_objects_id']);

        if (!$previousInspectionId) {
            return;
        }

        $previousItems = $this->getCopyableItemsFromPreviousInspection($previousInspectionId, $newInspectionTypeId);

        $this->copyAndPersistItemsForNewInspection($previousItems, $newInspectionId);
    }

    private function newInspectionTypePreventsCopyingOfItems($newInspectionTypeId)
    {
        $allowedSelect = db()->select()
            ->from('inspection_types', ['allow_copying_from_previous_inspection'])
            ->where('id = ?', $newInspectionTypeId);

        $isAllowed = db()->fetchOne($allowedSelect);

        return !$isAllowed;
    }

    private function getInspectionData($inspectionId)
    {
        $inspection_data_select = db()->select()
            ->from(['i' => 'inspection'])
            ->where('id = ?', $inspectionId);

        return db()->fetchRow($inspection_data_select);
    }

    private function getPreviousUserObjectId($currentUserObjectId)
    {
        $userObjectSelect = db()->select()
            ->from('users_objects')
            ->where('id = ?', $currentUserObjectId);

        $currentUserObjectRow = db()->fetchRow($userObjectSelect);

        $previousUserObjectSelect = db()->select()
            ->from('users_objects', ['id'])
            ->where('object = ?', $currentUserObjectRow['object'])
            ->where('`till` <= ?', $currentUserObjectRow['from'])
            ->where('role = ?', 'normal')
            ->order('till DESC');

        return db()->fetchOne($previousUserObjectSelect);
    }

    private function getPreviousInspectionIdForUo($userObjectId)
    {
        $select = db()->select()
            ->from(['i' => 'inspection'], ['id'])
            ->joinLeft(
                ['is' => 'inspection_status'],
                'is.inspection_id = i.id AND is.type = "inspection_planned"',
                false
            )
            ->where('i.users_objects_id = ?', $userObjectId)
            ->where('i.deleted = ?', false)
            ->where('i.synced = ?', true)
            ->where('is.date IS NOT NULL')
            ->order('is.date DESC');

        return db()->fetchOne($select);
    }

    private function getCopyableItemsFromPreviousInspection($previousInspectionId, $newTypeId)
    {
        $inspectionItemTypesForNewInspectionIds = $this->getItemTypesForInspectionTypeId($newTypeId);

        if (count($inspectionItemTypesForNewInspectionIds) === 0) {
            return [];
        }

        $items_select = db()->select()
            ->from('inspection_item')
            ->where('inspection_id = ?', $previousInspectionId)
            ->where('type IN (' . implode_for_where_in($inspectionItemTypesForNewInspectionIds) . ')')
            ->group('type')
            ;

        return db()->fetchAll($items_select);
    }

    private function getItemTypesForInspectionTypeId($inspectionTypeId)
    {
        $inspectionItemTypesForNewInspectionSelect = db()->select()
            ->from('inspection_item_type', ['id', 'inspection_type']);

        $inspectionItemTypesForNewInspection = db()->fetchAssoc($inspectionItemTypesForNewInspectionSelect);

        $inspectionItemTypesForNewInspection = array_filter(
            $inspectionItemTypesForNewInspection,
            function ($typeRow) use ($inspectionTypeId) {
                if (is_null($typeRow['inspection_type'])) {
                    return true;
                }

                $inspectionTypeIds = json_decode($typeRow['inspection_type'], true);

                return in_array($inspectionTypeId, $inspectionTypeIds);
            }
        );

        return array_column($inspectionItemTypesForNewInspection, 'id');
    }

    private function copyAndPersistItemsForNewInspection($previousItems, $newInspectionId)
    {
        foreach ($previousItems as $previousItem) {
            unset($previousItem['id'], $previousItem['picture']);

            (new InspectionItemModel())
                ->createRow($previousItem)
                ->setFromArray(['inspection_id' => $newInspectionId])
                ->save();
        }
    }
}
