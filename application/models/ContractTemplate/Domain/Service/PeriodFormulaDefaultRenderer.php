<?php

namespace ContractTemplate\Domain\Service;

use Object;

class PeriodFormulaDefault<PERSON>enderer extends PeriodFormulaRenderer
{
	/** @var Object */
	private $objectLib;

	/**
	 * @param Object $objectLib
	 */
	public function __construct($objectLib)
	{
		$this->objectLib = $objectLib;
	}

	public function renderFormula($formulaString, $from, $notice_period)
	{
		$output = '';

		if (strlen(trim($formulaString)) <= 0) {
			return $output;
		}

		$formulaArray = $this->objectLib->parseFormula($formulaString, $from, $notice_period);

		$partStrings = [];
		foreach ($formulaArray as $formulaPart) {
			$formulaPart['interval_type'] = $formulaPart['interval_type'] ?: 'month';

			if ($this->isNoticePeriodPart($formulaPart)) {
				if ($this->shouldGetNoticePeriodFromFormula($notice_period)) {
					$partStrings[] = $this->lastNoticePeriodFromFormula($formulaPart);
				} else {
					$partStrings[] = $this->lastNoticePeriodFromDefault($formulaPart, $notice_period);
				}
			} elseif ($this->isFourweeklyPart($formulaPart)) {
				$partStrings[] = $this->buildFourweeklyPartString($formulaPart);
			} else {
				$partStrings[] = $this->buildTypicalPartString($formulaPart);
			}
		}

		return implode(', ', $partStrings);
	}

	/**
	 * @param string $formulaString
	 * @param int $from
	 * @param int $notice_period
	 * @return string
	 */
	public function renderNoticePeriod($formulaString, $from, $notice_period)
	{
		return $notice_period .' '. $this->buildIntervalTypeString([
			'interval_type' => 'month',
			'interval_count' => $notice_period,
		]);
	}

	private function isNoticePeriodPart(array $formulaPart)
	{
		return $formulaPart['multiplier'] === '999';
	}

	private function shouldGetNoticePeriodFromFormula($notice_period)
	{
		return $this->hasNoNoticePeriod($notice_period);
	}

	private function hasNoNoticePeriod($notice_period)
	{
		return trim($notice_period) === '' || $notice_period === null || $notice_period <= 0;
	}

	private function lastNoticePeriodFromFormula($formulaPart)
	{
		return "verlengd met een opzegtermijn van {$formulaPart['interval_count']} "
			. $this->buildIntervalTypeString($formulaPart);
	}

    private function lastNoticePeriodFromDefault(array $formulaPart, $noticePeriod)
    {
        $buildIntervalTypeString = $this->buildIntervalTypeString([
            'interval_type' => 'month',
            'interval_count' => $noticePeriod
        ]);

        $periodDescriptionParts = [
            'daarna telkens verlengd met een periode van ',
            $formulaPart['interval_count'],
            ' ',
            $this->buildIntervalTypeString($formulaPart),
            ', met een opzegtermijn van ',
            $noticePeriod,
            ' ',
            $buildIntervalTypeString
        ];

        return implode('', $periodDescriptionParts);
    }

	private function buildFourweeklyPartString(array $formulaPart)
	{
		return $formulaPart['multiplier'] . ' ' . $this->buildPeriodLabel($formulaPart) . ' van '
			. $formulaPart['interval_count'] * 4 . ' weken';
	}

	private function buildTypicalPartString(array $formulaPart)
	{
		return $formulaPart['multiplier'] . ' ' . $this->buildPeriodLabel($formulaPart) . ' van '
			. $formulaPart['interval_count'] . ' ' . $this->buildIntervalTypeString($formulaPart);
	}
}
