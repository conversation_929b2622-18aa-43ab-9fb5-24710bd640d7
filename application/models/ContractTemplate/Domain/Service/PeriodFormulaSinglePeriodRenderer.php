<?php

namespace ContractTemplate\Domain\Service;

class PeriodFormulaSinglePeriodRenderer extends PeriodFormulaRenderer
{
	/** @var Object */
	private $objectLib;

	/**
	 * @param Object $objectLib
	 */
	public function __construct($objectLib)
	{
		$this->objectLib = $objectLib;
	}

	/**
	 * @param string $formulaString
	 * @param int $from
	 * @param int $notice_period
	 * @return string
	 * @throws \Exception
	 */
	public function renderFormula($formulaString, $from, $notice_period)
	{
		$emptyString = $yearPart = $monthPart = $dayPart = '';

		if (strlen(trim($formulaString)) <= 0) {
			return $emptyString;
		}

		$formulaArray = $this->objectLib->parseFormula($formulaString, $from, $notice_period);

		if (!$this->formulaHasParts($formulaArray)) {
			return $emptyString;
		}

		$prefix = $this->buildPrefix($formulaArray);

		$intervals = $this->convertFormulaToIntervals($formulaArray);
		$totalInterval = $this->sumIntervals($intervals);

		if ($totalInterval) {
			list($yearPart, $monthPart, $dayPart) = $this->convertTotalIntervalToPeriodParts($totalInterval);
		}

		return $prefix . $this->buildPeriodString($yearPart, $monthPart, $dayPart);
	}

	/**
	 * @param string $formulaString
	 * @param int $from
	 * @param int $notice_period
	 * @return string
	 */
	public function renderNoticePeriod($formulaString, $from, $notice_period)
	{
		$emptyString = '';

		$formulaArray = $this->objectLib->parseFormula($formulaString, $from, $notice_period);

		if (!$this->formulaHasParts($formulaArray)) {
			return $emptyString;
		}

		if (!$this->formulaHasEndlessPart($formulaArray)) {
			return $emptyString;
		}

		$endlessParts = $this->getOnlyEndlessParts($formulaArray);

		return $this->formatAsNoticePeriod($endlessParts);
	}

	private function formulaHasParts(array $formulaArray)
	{
		return count($formulaArray) > 0;
	}

	/**
	 * @param array $formulaArray
	 * @return string
	 */
	private function buildPrefix(array $formulaArray)
	{
		if ($this->formulaHasEndlessPart($formulaArray)) {
			$prefix = 'onbepaalde tijd';

			if ($this->formulaHasNonEndlessPart($formulaArray)) {
				$prefix .= ', met een minimale periode van ';
			}
		} else {
			$prefix = 'bepaalde tijd, met een periode van ';
		}
		return $prefix;
	}

	private function formulaHasEndlessPart(array $formulaArray)
	{
		return (boolean)count(array_filter($formulaArray, function ($formulaPart) {
			return $this->isEndlessPart($formulaPart);
		}));
	}

	private function isEndlessPart(array $formulaPart)
	{
		return (int)$formulaPart['multiplier'] === 999;
	}

	private function formulaHasNonEndlessPart(array $formulaArray)
	{
		return (boolean)count(array_filter($formulaArray, function ($formulaPart) {
			return !$this->isEndlessPart($formulaPart);
		}));
	}

	/**
	 * @param array $formulaArray
	 * @return \DateInterval[]
	 */
	private function convertFormulaToIntervals(array $formulaArray)
	{
		$intervals = [];

		$typeMap = [
			'day' => 'D',
			'week' => 'W',
			'fourweek' => 'W',
			'month' => 'M',
			'year' => 'Y',
		];

		foreach ($formulaArray as $formulaPart) {
			if ($this->isEndlessPart($formulaPart)) {
				continue;
			}

			$type = $typeMap[$formulaPart['interval_type']];
			$amount = $formulaPart['multiplier'] * $formulaPart['interval_count'];

			if ($formulaPart['interval_type'] === 'fourweek') {
				$amount *= 4;
			}
			try {
				$intervals[] = new \DateInterval("P{$amount}{$type}");
			} catch (\Exception $e) {
			}
		}

		return $intervals;
	}

	/**
	 * @param \DateInterval[] $intervals
	 * @return \DateInterval
	 * @throws \Exception
	 */
	private function sumIntervals(array $intervals)
	{
		$totalInterval = null;
		/** @var \DateInterval $interval */
		foreach ($intervals as $interval) {
			$totalInterval = $totalInterval ? $this->addIntervals($totalInterval, $interval) : $interval;
		}
		return $totalInterval;
	}

	private function addIntervals(\DateInterval $a, \DateInterval $b)
	{
		$a->y += $b->y;
		$a->m += $b->m;
		$a->d += $b->d;

		$a->h += $b->h;
		$a->i += $b->i;
		$a->s += $b->s;
		$a->f += $b->f;

		return $a;
	}

	private function convertTotalIntervalToPeriodParts(\DateInterval $totalInterval)
	{
		while ($totalInterval->m > 12) {
			$totalInterval->y++;
			$totalInterval->m-= 12;
		}

		return [
			$this->formatValue($totalInterval->y, 'jaar', 'jaar'),
			$this->formatValue($totalInterval->m, 'maand', 'maanden'),
			$this->formatValue($totalInterval->d, 'dag', 'dagen'),
		];
	}

	private function formatValue($value, $single, $multiple)
	{
		switch ((int)$value) {
			case (0):
				return '';
				break;

			case (1):
				return "$value $single";
				break;

			default:
				return "$value $multiple";
		}
	}

	/**
	 * @param string $yearPart
	 * @param string $monthPart
	 * @param string $dayPart
	 * @return string
	 */
	private function buildPeriodString($yearPart, $monthPart, $dayPart)
	{
		$parts = array_values(array_filter([$yearPart, $monthPart, $dayPart]));

		if (count($parts) === 3) {
			$output = $parts[0] . ', ' . $parts[1] . ' en ' . $parts[2];
		} elseif (count($parts) === 2) {
			$output = $parts[0] . ' en ' . $parts[1];
		} else {
			$output = $parts[0];
		}

		return $output;
	}

	private function getOnlyEndlessParts(array $formulaArray) {
		return array_filter($formulaArray, function ($formulaPart) {
			return $this->isEndlessPart($formulaPart);
		});
	}

	private function formatAsNoticePeriod(array $endlessParts) {
		return array_reduce($endlessParts, function ($carry, $item) {
			return $carry . $item['interval_count'] . ' ' . $this->buildIntervalTypeString($item);
		}, '');
	}
}
