<?php

namespace Sms\Application\Service;

class SendInvoiceReminderSmsRequest
{
    private $userIds;
    private $message;
    private $title;

    public function __construct($userIds, $message, $title)
    {
        $this->userIds = $userIds;
        $this->message = $message;
        $this->title = $title;
    }

    public function getUserIds()
    {
        return $this->userIds;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function getTitle()
    {
        return $this->title;
    }
}
