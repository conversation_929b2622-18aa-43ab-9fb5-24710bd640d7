<?php

namespace Sms\Application\Service;

class SendInvoiceReminderSmsService
{
    private $sendSmsService;

    public function __construct()
    {
        $this->sendSmsService = new SendSmsService();
    }

    public function execute(SendInvoiceReminderSmsRequest $request)
    {
        $sendSmsRequest = new SendSmsRequest(
            $request->getMessage(),
            $request->getTitle()
        );

        $sendSmsRequest->setUserIds($request->getUserIds());
        $this->sendSmsService->execute($sendSmsRequest);
    }
}
