<?php

namespace Sms\Application\Service;

class SendSmsRequest
{
    private $message;
    private $title;
    private $userIds = [];
    private $phoneNumbers = [];
    private $emailGroupIds = [];

    public function __construct($message, $title = null)
    {
        $this->message = $message;
        $this->title = $title;
    }

    public function getUserIds()
    {
        return $this->userIds;
    }

    public function setUserIds($userIds)
    {
        $this->userIds = $userIds;
    }

    public function getPhoneNumbers()
    {
        return $this->phoneNumbers;
    }

    public function setPhoneNumbers($phoneNumbers)
    {
        $this->phoneNumbers = $phoneNumbers;
    }

    public function getEmailGroupIds()
    {
        return $this->emailGroupIds;
    }

    public function setEmailGroupIds($emailGroupIds)
    {
        $this->emailGroupIds = $emailGroupIds;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function getTitle()
    {
        return $this->title;
    }
}
