<?php

namespace Sms\Application\Service;

class ViewListService
{
    public function getSelectMessagesStatement()
    {
        $select = \db()->select()
            ->from(['sms' => 'sms_log'])
            ->join(['rec' => 'sms_log_recipients'], 'rec.sms_log_id = sms.id')
            ->joinLeft(['uo' => 'users_objects'], 'uo.customer = rec.user_id', false)
            ->joinLeft(['o' => 'objects'], 'o.id = uo.object', false)
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
            ->group('sms.id')
            ->order('sms.date DESC');

        \UsersProjects::filterSelect($select, ['og.project']);

        return $select;
    }

    public function getSelectMessagesByUserStatement($userId)
    {
        return $this->getSelectMessagesStatement()
            ->where('rec.user_id = ?', $userId);
    }

    public function populateWithRecipients($messages)
    {
        foreach ($messages as &$msg) {
            $msg['recipients'] = \db()->fetchAll($this->selectRecipients($msg['id']));
        }

        return $messages;
    }

    private function selectRecipients($messageId)
    {
        return \db()->select()
            ->from(['rec' => 'sms_log_recipients'])
            ->join(['usr' => 'users'], 'rec.user_id = usr.id')
            ->where('rec.sms_log_id = ?', $messageId);
    }
}
