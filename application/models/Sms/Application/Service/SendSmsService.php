<?php

namespace Sms\Application\Service;

use Sms\Domain\Model\Message;
use Sms\Domain\Model\Recipient;
use Sms\Domain\Model\RecipientFactory;
use Sms\Domain\Service\SmsService;

class SendSmsService
{
    private $recipientFactory;
    private $smsService;

    public function __construct()
    {
        $this->recipientFactory = new RecipientFactory();
        $this->smsService = new SmsService();
    }

    public function execute(SendSmsRequest $request)
    {
        /** @var Recipient[] */
        $recipients = [];
        $recipients += $this->recipientFactory->buildAllByUserId($request->getUserIds());
        $recipients += $this->recipientFactory->buildAllByPhoneNumber($request->getPhoneNumbers());
        $recipients += $this->recipientFactory->buildAllByEmailGroups($request->getEmailGroupIds());

        $message = $this->buildMessage($request->getMessage(), $request->getTitle());
        $this->smsService->send($message, $recipients);
    }

    private function buildMessage($body, $title)
    {
        return new Message($body, $title);
    }
}
