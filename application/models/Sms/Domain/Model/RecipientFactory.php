<?php

namespace Sms\Domain\Model;

class RecipientFactory
{
    public function buildAllByUserId($userIds = [])
    {
        $recipients = [];

        foreach ($userIds as $userId) {
            if (!is_numeric($userId)) {
                continue;
            }

            $recipient = $this->buildByUserId($userId);

            if (null === $recipient) {
                continue;
            }


            $recipients[] = $recipient;
        }

        return $recipients;
    }

    private function buildByUserId($userId)
    {
        $userRow = (new \Users())->getById($userId);

        if (null === $userRow) {
            return null;
        }

        $phone1 = ($userRow->has('phone_primary') && isset($userRow->phone_primary->number) && !empty($userRow->phone_primary->number)) ?
            $userRow->phone_primary->number :
            '';

        $phone2 = ($userRow->has('phone_secondary') && isset($userRow->phone_secondary->number) && !empty($userRow->phone_secondary->number)) ?
            $userRow->phone_secondary->number :
            '';

        if (substr($phone1, 0, 2) == '06') {
            $mobilePhone = $phone1;
        } else {
            if (substr($phone2, 0, 2) == '06') {
                $mobilePhone = $phone2;
            } else {
                $mobilePhone = false;
            }
        }

        if ($mobilePhone) {
            $phoneNumber = $this->formatPhoneNumber($mobilePhone);
            return new Recipient($phoneNumber, $userId);
        }

        return null;
    }

    public function buildAllByPhoneNumber($phoneNumbers = [])
    {
        $recipients = [];

        foreach ($phoneNumbers as $phoneNumber) {
            $phoneNumber = $this->formatPhoneNumber($phoneNumber);
            $recipients[] = new Recipient($phoneNumber);
        }

        return $recipients;
    }

    public function buildAllByEmailGroups($emailGroupIds)
    {
        $recipients = [];
        $emailUsersGroupApplied = new \EmailUsersGroupApplied();

        foreach ($emailGroupIds as $emailGroupId) {
            $userIds = $emailUsersGroupApplied->getUsers($emailGroupId);

            foreach ($userIds as $userId) {
                $userId = (is_array($userId) && intval($userId['id']) > 0) ? $userId['id'] : $userId;
                $recipient = $this->buildByUserId($userId);

                if (null === $recipient) {
                    continue;
                }

                $recipients[] = $recipient;
            }
        }

        return $recipients;
    }

    private function formatPhoneNumber($phoneNr)
    {
        return '31' . strip_non_numeric_chars(substr($phoneNr, 1));
    }
}
