<?php

namespace Sms\Domain\Service;

use MessageBird\Client;
use MessageBird\Objects\Message;
use Sms\Domain\Model\Recipient;

class SmsService
{
    private $client;
    private $logService;

    public function __construct()
    {
        $this->client = new Client(\Settings::get('sms_access_key'));
        $this->logService = new LogService();
    }

    /**
     * @param \Sms\Domain\Model\Message $message
     * @param $recipients Recipient[]
     * @return void
     */
    public function send(\Sms\Domain\Model\Message $message, $recipients)
    {
        if (!$this->isActualSendingAllowed()) {
            $this->logService->log(
                $message->getBody(),
                $message->getTitle(),
                count($recipients),
                0,
                'Dummy, message not really sent',
                $recipients
            );

            return;
        }

        if (empty($recipients)) {
            $this->logService->log(
                $message->getBody(),
                $message->getTitle(),
                0,
                -1,
                'No recipients set, message not sent'
            );

            return;
        }

        $mbMessage = new Message();
        $mbMessage->originator = \Settings::get('sms_originator');

        $phoneNumbers = array_map(static function ($recipient) {
            return $recipient->getPhoneNumber();
        }, $recipients);

        $mbMessage->recipients = $phoneNumbers;
        $mbMessage->body = $message->getBody();

        try {
            $messageResult = $this->client->messages->create($mbMessage);

            $this->logService->log(
                $mbMessage->body,
                $message->getTitle(),
                $messageResult->recipients->totalSentCount,
                10,
                null,
                $recipients
            );
        } catch (\MessageBird\Exceptions\AuthenticateException $e) {
            $this->logService->log(
                $message->getBody(),
                $message->getTitle(),
                0,
                -1,
                'No acces key found.'
            );
        } catch (\MessageBird\Exceptions\BalanceException $e) {
            $this->logService->log(
                $message->getBody(),
                $message->getTitle(),
                0,
                -1,
                'No available balance.'
            );
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    private function isActualSendingAllowed()
    {
        return \Settings::get('sms_enabled')
            && (ENVIRONMENT === 'production' || ENVIRONMENT === 'productionpp')
            && !TESTING;
    }
}
