<?php

namespace Sms\Domain\Service;

use DbTable\SmsLog;
use DbTable\SmsLogRecipients;

class LogService
{
    private $smsLogModel;
    private $smsLogRecipientModel;

    public function __construct()
    {
        $this->smsLogModel = new SmsLog();
        $this->smsLogRecipientModel = new SmsLogRecipients();
    }

    public function log($message, $title, $succesCount, $resultCode, $resultMessage, $recipients = [])
    {
        $smsLogRow = $this->smsLogModel->createRow();
        $smsLogRow->date = (new \DateTime())->format('Y-m-d H:i:s');
        $smsLogRow->message = $message;
        $smsLogRow->title = $title;
        $smsLogRow->success_count = $succesCount;
        $smsLogRow->result_code = $resultCode;
        $smsLogRow->result_message = $resultMessage;
        $smsLogRowId = $smsLogRow->save();

        foreach ($recipients as $recipient) {
            $smsLogRecipientRow = $this->smsLogRecipientModel->createRow();
            $smsLogRecipientRow->sms_log_id = $smsLogRowId;
            $smsLogRecipientRow->phone_number = $recipient->getPhoneNumber();
            $smsLogRecipientRow->user_id = $recipient->getUserId();
            $smsLogRecipientRow->save();
        }
    }
}
