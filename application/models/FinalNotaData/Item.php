<?

class FinalNotaData_Item extends GlobalDbClass
{
	protected $_name = 'final_nota_data_item';

	protected $id;
	public $uo_id;
	public $year;
	public $data = [];
	public $from;
	public $till;
    public $created_on;
	public $detail_data;

	public function add(){
		$value_model = new FinalNotaData_Value();

		$attributes = ['uo_id' => $this->uo_id, 'year' => $this->year];

		if(!($row = $this->matchRow($attributes))) {
			$row = $this->createRow($attributes);
			$row->save();
		}

		$row->from = date('Y-m-d', strtotime($this->from));
		$row->till = date('Y-m-d', strtotime($this->till));

		if($this->detail_data){
		    $row->detail_data = json_encode($this->detail_data);
        }

        $row->last_calculated = date("Y-m-d H:i:s");
		$row->save();

		$this->id = $row->id;

		$value_model->delete('item_id = ' . $row->id);

		$this->addComponents();

		return $row->id;
	}

	public function addComponents(){
		$value_model = new FinalNotaData_Value();
		$component_model = new FinalNotaData_Component();

		foreach(['fixed', 'variable'] as $type) {
			$component_order = 0;

			if(!isset($this->data[$type])) continue;

			foreach ($this->data[$type] as $meter)
				foreach ($meter as $amount_type)
					foreach ($amount_type as $component_items) {

						foreach ($component_items as $component_item) {
							$component_id = $component_item['component'];

							$component_attributes = [
								'component_id' => $component_id,
								'matched_component_id' => $component_item['matched_component_id']
							];

							if (!($component_row = $component_model->matchRow($component_attributes))) {
								$component_row = $component_model->createRow($component_attributes);
								$component_row->save();
							}

							$component_row
								->setFromArray(['name' => $component_item['name'], 'order' => $component_order])
								->save();

							$component_order++;

							$value_attributes = [
								'item_id' => $this->id,
								'data_component_id' => $component_row->id,
								'type' => $type,
								'year' => $this->year
							];


							$value_row = $value_model->add(array_merge($value_attributes));

							if(isset($component_item['payed']))
								$value_row->advance +=  $component_item['payed'];

							if(isset($component_item['price']['total']['totalPrice']))
								$value_row->price +=  $component_item['payed'] + $component_item['price']['total']['totalPrice'];

							if(isset($component_item['usage_start']))
								$value_row->usage_start = str_replace(',', '.', $component_item['usage_start']);

							if(isset($component_item['usage_end']))
								$value_row->usage_end +=  $component_item['usage_end'];

							if(isset($component_item['usage_total']))
								$value_row->usage_total +=  $component_item['usage_total'];

							$value_row->save();
						}
					}
		}
	}
}