<?

class FinalNotaData_Value extends GlobalDbClass
{
	protected $_name = 'final_nota_data_value';

	public function add($attributes){
		if (!($value_row = $this->matchRow($attributes))) {
			$value_row = $this->createRow($attributes);
			$value_row->save();
		}
		return $value_row;
	}

	public function insert(array $data)
	{
		$data = $this->setTotal($data);
		return parent::insert($data);
	}
	public function update(array $data, $where)
	{
		$data = $this->setTotal($data);
		return parent::update($data, $where);
	}

	protected function setTotal($data){
		if(!isset($data['total']))
			$data['total'] = $data['price'] - $data['advance'];

		return $data;
	}
}