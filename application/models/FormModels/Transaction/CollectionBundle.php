<?php

namespace FormModels\Transaction;


class CollectionBundle
{
    private $collectionIds;
    private $type;
    private $sepaStatus;

    /**
     * @var \DateTime
     */
    private $minProcessDate;
    private $errorMessage;

    public function __construct($collectionIds)
    {
        $this->collectionIds = $collectionIds;
        $collections = $this->retrieveCollections();
        $this->setType($collections);
        $this->setSepaStatus($collections);
        $this->setMinProcessDate();
        $this->setErrorMessage();
    }

    public function getErrorMessage()
    {
        return $this->errorMessage;
    }

    public function getMinProcessDate()
    {
        return $this->minProcessDate;
    }

    private function retrieveCollections()
    {
        $transactionCollections = new \TransactionsCollection();
        $ids = implode_for_where_in($this->collectionIds);
        return $transactionCollections->select()
            ->where("id IN ($ids)")
            ->query()
            ->fetchAll();
    }

    private function setType($collections)
    {
        $this->type = $collections[0]['type'];
    }

    private function setSepaStatus($collections)
    {
        $this->sepaStatus = $collections[0]['sepa_status'];
    }

    private function setMinProcessDate()
    {
        $minProcessDate = new \DateTime();

        if ($this->type == 'credit') {
            $this->determineMinProcessDateForCreditCase($minProcessDate);
        } else {
            $minProcessDate->add(new \DateInterval('P0D'));
        }

        $this->minProcessDate = $minProcessDate;
    }

    private function determineMinProcessDateForCreditCase(\DateTime $minProcessDate)
    {
        switch ($this->sepaStatus) {
            case "FRST":
                $minProcessDate->add(new \DateInterval('P6D'));
                break;
            case "RCUR":
                $minProcessDate->add(new \DateInterval('P3D'));
                break;
            case "FNAL":
                $minProcessDate->add(new \DateInterval('P3D'));
                break;
            case "OOFF":
                $minProcessDate->add(new \DateInterval('P6D'));
                break;
            default;
                $minProcessDate->add(new \DateInterval('P3D'));
                break;
        }
    }

    private function setErrorMessage()
    {
        $now = new \DateTime();
        $daysInFuture = $now->diff($this->getMinProcessDate())->days;
        $dayText = $daysInFuture > 0 ? $daysInFuture .' dagen' : '';

        $this->errorMessage = "De verwerkings datum moet minimaal $dayText in de toekomst liggen.";
    }
}