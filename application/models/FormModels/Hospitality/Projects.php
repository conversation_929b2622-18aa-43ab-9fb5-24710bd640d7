<?php

namespace FormModels\Hospitality;


use Hospitality\Products;

class Projects
{
    public function getAllCategories($projectId, $hospitalityProjectId = null)
    {
        if (is_numeric($hospitalityProjectId)) {
            $select = db()->select()
                ->from(['hp' => 'hospitality_projects'], false)
                ->joinLeft(['hc' => 'hospitality_categories'], 'hc.id = hp.category_id', ['id', 'name'])
                ->where('hp.id = ?', $hospitalityProjectId);
            $categories = db()->fetchAll($select);

            return array_column($categories, 'name', 'id');
        }

        $select = db()->select()
            ->from(['hc' => 'hospitality_categories'], ['id', 'name'])
            ->joinLeft(['hp' => 'hospitality_projects'], 'hc.id = hp.category_id AND hp.project_id = ' . $projectId, false)
            ->where('hp.category_id IS NULL');

        $categories = db()->fetchAll($select);

        return array_column($categories, 'name', 'id');
    }

    public function getAllActiveProducts()
    {
        $products = $this->findAllActiveProducts()->toArray();
        return !empty($products) ? $this->makeSelectRows($products) : [];
    }
    
    private function findAllActiveProducts()
    {
        $where = ['active = ?' => 1];
        return (new Products())->fetchAll($where);
    }

    private function makeSelectRows($products)
    {
        $new = [];
        foreach ($products as $product)
            $new[$product['category']] = $this->makeSelectRow($product, $new);

        return $new;
    }

    private function makeSelectRow($product, $new)
    {
        if (!array_key_exists($product['category'], $new))
            return [$product['id'] => $product['title']  . $this->getCompanyName($product['company_id'])];
        else
            return $new[$product['category']] + [$product['id'] => $product['title'] . $this->getCompanyName($product['company_id'])];
    }

    private function getCompanyName($companyId)
    {
        if (!$companyId)
            return '';

        $select = db()->select()
            ->from('company', 'name')
            ->where('id = ?', $companyId);

        return ' - ' . db()->fetchOne($select);
    }
}