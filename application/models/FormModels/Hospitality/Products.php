<?php

namespace FormModels\Hospitality;


class Products
{
    public function getAllCategories()
    {
        $select = db()->select()
            ->from(['hc' => 'hospitality_categories'], ['id', 'name']);

        $categories = db()->fetchAll($select);

        return array_column($categories, 'name', 'id');
    }

    public function getCompanyIdByUserId($userId)
    {
        $company = \Company::getByDummyUserId($userId);

        return $company->id;
    }
}