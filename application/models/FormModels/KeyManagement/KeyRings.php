<?php

namespace FormModels\KeyManagement;


use KeyManagement\Groups;

class KeyRings
{
    public function getObjectAddressForKeyring($objectId)
    {
        if (!is_numeric($objectId)) return null;

        $select = db()->select()
            ->from('objects', ['rendered_address'])
            ->where('id = ?', $objectId);

        return db()->fetchOne($select);
    }

    public function getLabel($keyRingId)
    {
        if (!is_numeric($keyRingId)) return null;

        $select = db()->select()
            ->from('km_key_rings', 'label')
            ->where('id = ?', $keyRingId);

        return db()->fetchOne($select);
    }

    public function isValidLabel($label, $keyRingId, $locationId)
    {
        $where = [
            'location_id = ?' => $locationId,
            'label = ?' => $label
        ];

        if (is_numeric($keyRingId))
            $where['id != ?'] = $keyRingId;

        $keyRings = new \KeyManagement\KeyRings();
        $label = $keyRings->fetchRow($where);

        return empty($label);
    }

    public function getGroupsForSelect($locationId)
    {
        $where = [
            'location_id = ?' => $locationId
        ];

        $groupsModel = new Groups();
        $rows = $groupsModel->fetchAll($where);

        $groups = ['Geen'];
        foreach ($rows as $group)
            $groups[$group['id']] = $group['name'];

        return $groups;
    }

    public function getSelectedGroup($keyringId)
    {
        if (!is_numeric($keyringId)) return null;

        $keyRings = new \KeyManagement\KeyRings();
        $row = $keyRings->fetchRowById($keyringId);

        return !empty($row) ? $row['group_id'] : null;
    }
}