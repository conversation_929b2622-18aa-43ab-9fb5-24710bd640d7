<?php

namespace FormModels\KeyManagement;


class Locations
{
    public function getProjectNameForLocation($locationId)
    {
        if (!$this->isEditCase($locationId)) return null;

        $select = db()->select()
            ->from(['l' => 'km_locations'], false)
            ->joinLeft(['p' => 'projects'], 'p.id = l.project_id', ['name'])
            ->where('l.id = ?', $locationId);

        return db()->fetchOne($select);
    }

    private function isEditCase($locationId)
    {
        return $locationId > 0;
    }
}