<?php

namespace Indexation\Domain\Service;

use Dompdf\Dompdf;
use Indexation\Domain\Model\Recipient\Recipient;
use Indexation\Domain\Model\Variables\VariablesDTO;

class IndexationLetterService
{
    private $mustache;

    public function __construct()
    {
        $this->mustache = new \Mustache_Engine();
    }

    public function makePdf(Recipient $recipient, VariablesDTO $variables, $templatePath)
    {
        $template = file_get_contents($templatePath);
        $html = $this->bindDataToTemplate($template, $recipient, $variables);
        return $this->buildPdf($html);
    }

    private function bindDataToTemplate($template, Recipient $recipient, VariablesDTO $variable)
    {
        return $this->mustache->render($template, [
            'recipient' => $recipient,
            'var' => $variable
        ]);
    }

    private function buildPdf($html)
    {
        $domPdf = new Dompdf();
        $domPdf->loadHtml($html);
        $domPdf->setPaper('A4', 'portrait');
        $domPdf->render();
        return $domPdf->output();
    }
}
