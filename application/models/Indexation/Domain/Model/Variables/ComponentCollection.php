<?php

namespace Indexation\Domain\Model\Variables;

use SharedKernel\Domain\Model\Money;

class ComponentCollection
{
    private $indexComponents = [];
    private $summaryOfOtherComponent;
    private $totalPreviousAmount;
    private $totalNewAmount;
    private $totalNewAmountInclVat;
    private $totalNewAmountExclVat;
    private $totalNewVatAmount;
    private $totalPreviousIndexAmount;

    public function __construct(Money $totalNewAmountInclVat, Money $totalNewAmountExclVat, Money $totalNewVatAmount)
    {
        $this->totalPreviousAmount = Money::makeFromEuroInput(0);
        $this->totalNewAmount = Money::makeFromEuroInput(0);
        $this->totalNewAmountInclVat = $totalNewAmountInclVat;
        $this->totalNewAmountExclVat = $totalNewAmountExclVat;
        $this->totalPreviousIndexAmount = Money::makeFromEuroInput(0);
        $this->totalNewVatAmount = $totalNewVatAmount;
    }

    public function addIndexComponent(IndexComponent $indexComponent)
    {
        $this->indexComponents[] = $indexComponent;
        $this->totalPreviousAmount = $this->totalPreviousAmount->add($indexComponent->getPreviousAmount());
        $this->totalNewAmount = $this->totalNewAmount->add($indexComponent->getNewAmount());
        $this->totalPreviousIndexAmount = $this->totalPreviousIndexAmount->add($indexComponent->getPreviousAmount());
    }

    public function setSummaryOfOtherComponents(SummaryOfOtherComponent $summaryOfOtherComponent)
    {
        $this->summaryOfOtherComponent = $summaryOfOtherComponent;
        $this->totalPreviousAmount = $this->totalPreviousAmount->add($summaryOfOtherComponent->getPreviousAmount());
        $this->totalNewAmount = $this->totalNewAmount->add($summaryOfOtherComponent->getNewAmount());
    }

    public function getIndexComponents()
    {
        return $this->indexComponents;
    }

    public function getSummaryOfOtherComponent()
    {
        return $this->summaryOfOtherComponent;
    }

    public function getTotalPreviousAmount()
    {
        return $this->totalPreviousAmount;
    }

    public function getTotalNewAmount()
    {
        return $this->totalNewAmount;
    }

    public function getRentIncreasedAmount()
    {
        return $this->totalNewAmount->subtract($this->totalPreviousAmount);
    }

    public function getPercentageRentIncreased()
    {
        $amountIncreasedBy = $this->getRentIncreasedAmount()->getAmountInCents();
        $totalPreviousAmount = $this->totalPreviousIndexAmount->getAmountInCents();

        $percentage = ($amountIncreasedBy / $totalPreviousAmount) * 100;
        return round($percentage, 2);
    }

    public function getTotalNewAmountInclVat()
    {
        return $this->totalNewAmountInclVat;
    }

    public function getTotalNewAmountExclVat()
    {
        return $this->totalNewAmountExclVat;
    }

    public function getTotalNewVatAmount()
    {
        return $this->totalNewVatAmount;
    }

    public function hasVat()
    {
        return !$this->totalNewAmountInclVat->equals($this->totalNewAmountExclVat);
    }
}
