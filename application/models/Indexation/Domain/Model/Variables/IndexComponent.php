<?php

namespace Indexation\Domain\Model\Variables;

use SharedKernel\Domain\Model\Money;

class IndexComponent
{
    private $name;
    private $englishName;
     /**
     * @var Money $previousAmount
     */
    private $previousAmount;
    /**
     * @var Money $newAmount
     */
    private $newAmount;
    private $previousCBSIndex;
    private $newCBSIndex;

    public function __construct(
        $name,
        Money $previousAmount,
        Money $newAmount,
        $previousCBSIndex,
        $newCBSIndex
    ) {
        $this->name = $name;
        $this->previousAmount = $previousAmount;
        $this->newAmount = $newAmount;
        $this->previousCBSIndex = $previousCBSIndex;
        $this->newCBSIndex = $newCBSIndex;
    }

    public function getName()
    {
        return ucfirst($this->name);
    }

    public function getEnglishName()
    {
        return $this->englishName;
    }

    public function setEnglishName($englishName)
    {
        $this->englishName = $englishName;
    }

    public function getPreviousAmount()
    {
        return $this->previousAmount;
    }

    public function getNewAmount()
    {
        return $this->newAmount;
    }

    public function getPreviousCBSIndex()
    {
        return $this->previousCBSIndex;
    }

    public function getNewCBSIndex()
    {
        return $this->newCBSIndex;
    }
}
