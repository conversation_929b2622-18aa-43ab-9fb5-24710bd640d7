<?php

namespace Indexation\Domain\Model\Variables;

class SummaryOfOtherComponent
{
    private $name;
    private $previousAmount;
    private $newAmount;

    public function __construct($name, $previousAmount, $newAmount)
    {
        $this->name = $name;
        $this->previousAmount = $previousAmount;
        $this->newAmount = $newAmount;
    }

    public function getName()
    {
        return $this->name;
    }

    public function getPreviousAmount()
    {
        return $this->previousAmount;
    }

    public function getNewAmount()
    {
        return $this->newAmount;
    }
}
