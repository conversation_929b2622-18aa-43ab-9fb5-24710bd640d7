<?php

namespace Indexation\Domain\Model\Variables;

class VariablesDTO
{
    private $effectiveDateOfRentIncrease;
    private $closureName;
    private $currentDate;
    private $componentCollection;
    private $companyEmail;
    private $companyPhone;
    private $previousCBSIndex;
    private $newCBSIndex;
    private $contractuallyAgreedMinimumIndexationPercentage;
    private $contractuallyAgreedMaximumIndexationPercentage;

    public function __construct(
        \DateTime $effectiveDateOfRentIncrease,
        $closureName,
        ComponentCollection $componentCollection,
        $companyEmail,
        $companyPhone,
        $previousCBSIndex,
        $newCBSIndex,
        $contractuallyAgreedMinimumIndexationPercentage,
        $contractuallyAgreedMaximumIndexationPercentage,
        $limitedMaximumIndexationPercentage,
        $percentageCalculated,
        $description

    ) {
        $this->effectiveDateOfRentIncrease = $effectiveDateOfRentIncrease;
        $this->closureName = $closureName;
        $this->currentDate = new \DateTime();
        $this->componentCollection = $componentCollection;
        $this->roundingtext = \Settings::get('invoice_indexation_commercial_no_rounding') ? 'niet afgerond' : 'afgerond op 3 decimalen' ;
        $this->roundingtextEnglish = \Settings::get('invoice_indexation_commercial_no_rounding') ? 'no rounding' : 'rounded at 3 decimals' ;
        $this->companyEmail = $companyEmail;
        $this->companyPhone = $companyPhone;
        $this->previousCBSIndex = $previousCBSIndex;
        $this->newCBSIndex = $newCBSIndex;
        $this->contractuallyAgreedMinimumIndexationPercentage = $contractuallyAgreedMinimumIndexationPercentage;
        $this->contractuallyAgreedMaximumIndexationPercentage = $contractuallyAgreedMaximumIndexationPercentage;
        $this->limitedMaximumIndexationPercentage = $limitedMaximumIndexationPercentage;
        $this->percentageCalculated = $percentageCalculated;
        $this->description = $description;
    }

    public function getEffectiveDateOfRentIncrease()
    {
        return $this->effectiveDateOfRentIncrease->format('d-m-Y');
    }

    public function getClosureName()
    {
        return $this->closureName;
    }

    public function getCurrentDate()
    {
        return $this->currentDate->format('d-m-Y');
    }
    public function getRoundingTextEnglish()
    {
        return $this->roundingtextEnglish;
    }

    public function getRoundingText()
    {
        return $this->roundingtext;
    }

    public function getComponentCollection()
    {
        return $this->componentCollection;
    }

    public function getCompanyEmail()
    {
        return $this->companyEmail;
    }

    public function getCompanyPhone()
    {
        return $this->companyPhone;
    }

    public function getPreviousCBSIndex()
    {
        return $this->previousCBSIndex;
    }

    public function getNewCBSIndex()
    {
        return $this->newCBSIndex;
    }

    public function getContractuallyAgreedMinimumIndexationPercentage()
    {
        return $this->contractuallyAgreedMinimumIndexationPercentage > 0 ?
        $this->contractuallyAgreedMinimumIndexationPercentage : null;
    }

    public function getContractuallyAgreedMaximumIndexationPercentage()
    {
        return $this->contractuallyAgreedMaximumIndexationPercentage > 0 ?
            $this->contractuallyAgreedMaximumIndexationPercentage : null;
    }
}
