<?php

namespace Indexation\Domain\Model\Recipient;

class Address
{
    private $streetAndNumber;
    private $zipCodeAndCity;

    public function __construct($streetAndNumber, $zipCodeAndCity)
    {
        $this->streetAndNumber = $streetAndNumber;
        $this->zipCodeAndCity = $zipCodeAndCity;
    }

    public function getStreetAndNumber()
    {
        return $this->streetAndNumber;
    }

    public function getZipCodeAndCity()
    {
        return $this->zipCodeAndCity;
    }
}
