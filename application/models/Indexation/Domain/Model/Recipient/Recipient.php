<?php

namespace Indexation\Domain\Model\Recipient;

class Recipient
{
    private $name;
    private $address;
    private $objectAddress;
    private $contactPerson;

    public function __construct(
        $name,
        Address $address,
        ContactPersonDTO $contactPerson,
        $language,
        $objectAddress
    )
    {
        $this->name = $name;
        $this->address = $address;
        $this->contactPerson = $contactPerson;
        $this->language = $language;
        $this->objectAddress = $objectAddress;
    }

    public function getName()
    {
        return $this->name;
    }

    public function getAddress()
    {
        return $this->address;
    }

    public function getObjectAddress()
    {
        return $this->objectAddress;
    }

    public function getContactPerson()
    {
        return $this->contactPerson;
    }

    public function getLanguage()
    {
        return $this->language;
    }
}
