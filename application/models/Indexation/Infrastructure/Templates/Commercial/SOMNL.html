<!DOCTYPE HTML>
<html>
<head>
    <style>
        #letter-wrapper {
            margin-top: 150px;
            font-family: trebuchet, sans-serif;
            font-size: 11px;
        }

        #recipient ul {
            list-style-type: none;
            padding-left: 0;
            line-height: 2;
        }

        #subject {
            margin-top: 30px;
            margin-bottom: 30px;
        }

        #index-summary {
            margin-top: 20px;
            list-style-type: none;
            padding-left: 0;
            line-height: 1.5;
        }

        #index-components {
            width: 100%;
            margin-top: 20px;
            margin-bottom: 30px;
        }

        #index-components td:nth-child(1){
            width: 50%;
        }

        #index-components td:nth-child(2){
            width: 25%;
        }

        #index-components td:nth-child(3){
            width: 25%;
        }

        #index-components thead {
            text-decoration: underline !important;
            text-align: right;
        }

        .money {
            text-align: right;
        }

        .total .money {
            text-decoration: overline;
        }

        #info-total-new-rent {
            width: 100%;
            margin-top: 30px;
            margin-bottom: 30px;
            font-weight: bold;
        }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Your Website</title>
</head>
<body>

<div id="letter-wrapper">
    <div id="recipient">
        <ul>
            <li>{{ recipient.getName }}</li>
            <li>{{ recipient.getAddress.getStreetAndNumber }}</li>
            <li>{{ recipient.getAddress.getZipCodeAndCity }}</li>
        </ul>
    </div>

    <div id="subject">
        <p>Betreft: huurprijsaanpassing</p>
        <p>Datum: {{var.getCurrentDate }}</p>
    </div>

    <div id="salutation">
        <p>Geachte huurder,</p>
    </div>

    <div id="content">

        <p>Conform de met u gesloten huurovereenkomst, zullen wij de huurprijs als volgt indexeren.</p>

        <ul id="index-summary">
            <li>Ingangsdatum: {{var.getEffectiveDateOfRentIncrease}}</li>
            <li>Bedrag verhoging: {{var.getComponentCollection.getRentIncreasedAmount}}</li>
            <li>Percentage: {{var.getComponentCollection.getPercentageRentIncreased}} %</li>
        </ul>

        <table id="index-components">
            <thead>
            <tr>
                <td></td>
                <td>Oude huurprijs excl.:</td>
                <td>Nieuwe huurprijs excl.:</td>
            </tr>
            </thead>

            <tbody>
            {{#var.getComponentCollection.getIndexComponents}}
            <tr>
                <td>{{ getName }}</td>
                <td class="money">{{ getPreviousAmount }}</td>
                <td class="money">{{ getNewAmount }}</td>
            </tr>
            {{/var.getComponentCollection.getIndexComponents}}

            <tr>
                <td>{{ var.getComponentCollection.getSummaryOfOtherComponent.getName }}</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getNewAmount }}</td>
            </tr>

            <tr class="total">
                <td>Totale verplichting:</td>
                <td class="money">{{ var.getComponentCollection.getTotalPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmount }}</td>
            </tr>
            </tbody>
        </table>

        <table id="info-total-new-rent">
            <tr>
                <td>Uw nieuwe huur inclusief BTW bedraagt:</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmountInclVat }}</td>
            </tr>
        </table>

        <p>U ontvangt binnenkort de factuur voor de komende maand met daarop de aangepaste huurprijzen per {{var.getEffectiveDateOfRentIncrease}}.  Wij verzoeken u deze op de gebruikelijke wijze aan ons te voldoen.</p>

        <p>Deze berekening is gebaseerd op de CBS berekening ({{var.getNewCBSIndex}}/{{var.getPreviousCBSIndex}} * uw
            huidige huur). U kunt de berekening controleren op www.cbs.nl</p>

        {{#var.getContractuallyAgreedMinimumIndexationPercentage}}
        <p>De CBS berekening is lager dan het afgesproken minimum percentage
            {{var.getContractuallyAgreedMinimumIndexationPercentage}} in uw contract. Uw huur is derhalve met het
            minimum percentage verhoogd.</p>
        {{/var.getContractuallyAgreedMinimumIndexationPercentage}}

        {{#var.getContractuallyAgreedMaximumIndexationPercentage}}
        <p>De CBS berekening is hoger dan het afgesproken maximum percentage
            {{var.getContractuallyAgreedMaximumIndexationPercentage}} in uw contract. Uw huur is derhalve met het
            maximum percentage verhoogd.</p>
        {{/var.getContractuallyAgreedMaximumIndexationPercentage}}

        <p>Mocht u naar aanleiding van deze brief nog vragen hebben, dan kunt u ons bereiken op telefoonnummer {{
            var.getCompanyPhone }} of per email via {{ var.getCompanyEmail }}.</p>

        <br/>

        <p>Wij vertrouwen er op u hiermee voldoende te hebben geïnformeerd.</p>
    </div>

    <br/>

    <div id="closure">
        <p>Met vriendelijke groet,</p>
        <p>{{ var.getClosureName }}</p>
    </div>
</div>

</body>
</html>
