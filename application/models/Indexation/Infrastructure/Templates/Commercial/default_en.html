<!DOCTYPE HTML>
<html>
<head>
    <style>
        #letter-wrapper {
            margin-top: 150px;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px;
        }

        #recipient ul {
            list-style-type: none;
            padding-left: 0;
            line-height: 2;
        }

        #subject {
            margin-top: 30px;
            margin-bottom: 30px;
        }

        #index-summary {
            margin-top: 20px;
            list-style-type: none;
            padding-left: 0;
            line-height: 1.5;
        }

        #index-components {
            width: 100%;
            margin-top: 20px;
            margin-bottom: 30px;
        }

        #index-components td:nth-child(1){
            width: 50%;
        }

        #index-components td:nth-child(2){
            width: 25%;
        }

        #index-components td:nth-child(3){
            width: 25%;
        }

        #index-components thead {
            text-decoration: underline !important;
            text-align: right;
        }

        .money {
            text-align: right;
        }

        .total .money {
            text-decoration: overline;
        }

        #info-total-new-rent {
            width: 100%;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .total-amount-line {
            border-top: 1px solid black;
            font-weight: bold;
        }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>

</head>
<body>

<div id="letter-wrapper">
    <div id="recipient">
        <ul>
            <li>{{ recipient.getName }}</li>
            <li>{{ recipient.getAddress.getStreetAndNumber }}</li>
            <li>{{ recipient.getAddress.getZipCodeAndCity }}</li>
        </ul>
    </div>

    <div id="subject">
        <p>Concerns: rental adjustment {{ recipient.getObjectAddress }}</p>
        <p>Date: {{var.getCurrentDate }}</p>
    </div>

    <div id="salutation">
        <p>Dear tenant,</p>
    </div>

    <div id="content">


        <ul id="index-summary">
            <li>Effective date of the increase: {{var.getEffectiveDateOfRentIncrease}}</li>
            <li>Amount increase: {{var.getComponentCollection.getRentIncreasedAmount}}</li>
            <li>Percentage: {{var.getComponentCollection.getPercentageRentIncreased}} %</li>
        </ul>

        <table id="index-components">
            <thead>
            <tr>
                <td></td>
                <td>Old rental price:</td>
                <td>New rental price:</td>
            </tr>
            </thead>

            <tbody>
            {{#var.getComponentCollection.getIndexComponents}}
            <tr>
                <td>{{ getEnglishName }}</td>
                <td class="money">{{ getPreviousAmount }}</td>
                <td class="money">{{ getNewAmount }}</td>
            </tr>
            {{/var.getComponentCollection.getIndexComponents}}

            <tr>
                <td>Other components</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getNewAmount }}</td>
            </tr>

            <tr class="total">
                <td>Total obligation:</td>
                <td class="money">{{ var.getComponentCollection.getTotalPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmount }}</td>
            </tr>
            </tbody>
        </table>

        <table id="info-total-new-rent">
            <tr>
                <td style="width: 65%">New rental amount:</td>
                <td>Total excl. Vat:</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmountExclVat }}</td>
            </tr>
            <tr>
                <td></td>
                <td>Vat:</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewVatAmount }}</td>
            </tr>
            <tr>
                <td></td>
                <td class="total-amount-line">Total incl. Vat:</td>
                <td class="money total-amount-line">{{ var.getComponentCollection.getTotalNewAmountInclVat }}</td>
            </tr>
        </table>


        <p>We kindly ask you to pay us the adjusted rental price per {{var.getEffectiveDateOfRentIncrease}}  in the usual way.</p>

        <p>
            {{{var.description}}}
        </p>


        <p>We trust that we have informed you sufficiently.</p>
    </div>

    <br/>

    <div id="closure">
        <p>Yours sincerely,</p>
        <p>{{ var.getClosureName }}</p>
    </div>
</div>

</body>
</html>
