<!DOCTYPE HTML>
<html>
<head>
    <style>
        #letter-wrapper {
            margin-top: 150px;
            font-family: Arial, Helvetica, sans-serif;
            font-size: 12px;
        }

        #recipient ul {
            list-style-type: none;
            padding-left: 0;
            line-height: 2;
        }

        #subject {
            margin-top: 30px;
            margin-bottom: 30px;
        }

        #index-summary {
            margin-top: 20px;
            list-style-type: none;
            padding-left: 0;
            line-height: 1.5;
        }

        #index-components {
            width: 100%;
            margin-top: 20px;
            margin-bottom: 30px;
        }

        #index-components td:nth-child(1){
            width: 50%;
        }

        #index-components td:nth-child(2){
            width: 25%;
        }

        #index-components td:nth-child(3){
            width: 25%;
        }

        #index-components thead {
            text-decoration: underline !important;
            text-align: right;
        }

        .money {
            text-align: right;
        }

        .total .money {
            text-decoration: overline;
        }

        #info-total-new-rent {
            width: 100%;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .total-amount-line {
            border-top: 1px solid black;
            font-weight: bold;
        }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Your Website</title>
</head>
<body>

<div id="letter-wrapper">
    <div id="recipient">
        <ul>
            <li>{{ recipient.getName }}</li>
            <li>{{ recipient.getAddress.getStreetAndNumber }}</li>
            <li>{{ recipient.getAddress.getZipCodeAndCity }}</li>
        </ul>
    </div>

    <div id="subject">
        <p>Betreft: huurprijsaanpassing {{ recipient.getObjectAddress }} </p>
        <p>Datum: {{var.getCurrentDate }}</p>
    </div>

    <div id="salutation">
        <p>Geachte huurder,</p>
    </div>

    <div id="content">

        <p>Conform de met u gesloten huurovereenkomst, zullen wij de huurprijs als volgt indexeren.</p>

        <ul id="index-summary">
            <li>Ingangsdatum: {{var.getEffectiveDateOfRentIncrease}}</li>
            <li>Bedrag verhoging: {{var.getComponentCollection.getRentIncreasedAmount}}</li>
            <li>Percentage: {{var.getComponentCollection.getPercentageRentIncreased}} %</li>
        </ul>

        <table id="index-components">
            <thead>
            <tr>
                <td></td>
                <td>Oude huurprijs excl.:</td>
                <td>Nieuwe huurprijs excl.:</td>
            </tr>
            </thead>

            <tbody>
            {{#var.getComponentCollection.getIndexComponents}}
            <tr>
                <td>{{ getName }}</td>
                <td class="money">{{ getPreviousAmount }}</td>
                <td class="money">{{ getNewAmount }}</td>
            </tr>
            {{/var.getComponentCollection.getIndexComponents}}

            <tr>
                <td>{{ var.getComponentCollection.getSummaryOfOtherComponent.getName }}</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getSummaryOfOtherComponent.getNewAmount }}</td>
            </tr>

            <tr class="total">
                <td>Totale verplichting:</td>
                <td class="money">{{ var.getComponentCollection.getTotalPreviousAmount }}</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmount }}</td>
            </tr>
            </tbody>
        </table>

        <table id="info-total-new-rent">
            <tr>
                <td style="width: 65%">Uw nieuwe huur bedraagt:</td>
                <td>Totaal excl. BTW:</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewAmountExclVat }}</td>
            </tr>
            <tr>
                <td></td>
                <td>BTW:</td>
                <td class="money">{{ var.getComponentCollection.getTotalNewVatAmount }}</td>
            </tr>
            <tr>
                <td></td>
                <td class="total-amount-line">Totaal incl. BTW:</td>
                <td class="money total-amount-line">{{ var.getComponentCollection.getTotalNewAmountInclVat }}</td>
            </tr>
        </table>

        <p>Wij verzoeken u vriendelijk om de aangepaste huurprijs per {{var.getEffectiveDateOfRentIncrease}} op de
            gebruikelijke wijze aan ons te voldoen.</p>

        <p>
            {{{var.description}}}
        </p>


        <p>Mocht u naar aanleiding van deze brief nog vragen hebben, dan kunt u ons per email bereiken via {{ var.getCompanyEmail }}.</p>

        <br/>

        <p>Wij vertrouwen er op u hiermee voldoende te hebben geïnformeerd.</p>
    </div>

    <br/>

    <div id="closure">
        <p>Met vriendelijke groet,</p>
        <p>{{ var.getClosureName }}</p>
    </div>
</div>

</body>
</html>
