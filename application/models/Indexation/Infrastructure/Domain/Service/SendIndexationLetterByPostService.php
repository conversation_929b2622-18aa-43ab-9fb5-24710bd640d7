<?php

namespace Indexation\Infrastructure\Domain\Service;

use DbTable\IndexationLetterSend;
use Indexation\Domain\Service\SendIndexationLetterByPostService as ISendIndexationLetterByPostService;

class SendIndexationLetterByPostService implements ISendIndexationLetterByPostService
{
    private $postId;
    private $zip;
    private $indexationLetterSendModel;

    public function __construct()
    {
        $this->indexationLetterSendModel = new IndexationLetterSend();
    }

    public function sent($pdf, $data)
    {
        $filename = $this->makeFilename($data);

        $post = new \Post();

        if (!$this->zip) {
            $postFileLocation = $post->add('Huurindexatie', strftime('%B %Y', time()));
            $this->postId = $post->postId;
            $this->zip = new \ZipArchive();
            $this->zip->open($postFileLocation, \ZipArchive::CREATE);
        }

        $post->postId = $this->postId;
        $this->zip->addFromString("$filename.pdf", $pdf);
        $postId = $post->addRecipient($data['user_id'], $data['object_id'], $filename . '.pdf');

        if ($postId) {
            $this->logToDb($data, 'mail');
        }
    }

    private function makeFilename($data)
    {
        return 'Huurindexatie ' . $data['object_address'] . ' ' . str_replace('/', '_',
                $data['object_number']) . '_' . $data['object_id'];
    }

    private function logToDb($object, $sendType)
    {
        $data = [
            'run_id' => $object['run_id'],
            'period' => $object['run_period'],
            'period_value' => $object['run_period_value'],
            'users_objects' => $object['users_objects'],
            'send_type' => $sendType,
        ];

        $this->indexationLetterSendModel->insert($data);
    }
}
