<?php

namespace Indexation\Infrastructure\Domain\Service;

use DbTable\IndexationLetterSend;
use Indexation\Domain\Service\SendIndexationLetterByEmailService;

class ZendSendIndexationLettersByEmailService implements SendIndexationLetterByEmailService
{
    private $indexationLetterSendModel;

    public function __construct()
    {
        $this->indexationLetterSendModel = new IndexationLetterSend();
    }

    public function sent($pdf, $data, $indexationType)
    {
        $filename = $this->makeFilename($data);
        $options = $this->getEmailOptions($data, $pdf, $filename, $indexationType);

        $email = new \EmailOutgoing($options);

        if ($email) {
            $this->logToDb($data, 'email');
        }
    }

    private function makeFilename($data)
    {
        if (\Settings::get('modules_vacancy_management'))  {
            return 'Indexatie ' . $data['object_address'] . ' ' . str_replace('/', '_',
                    $data['object_number']) . '_' . $data['object_id'];
        } else {
            return 'Huurindexatie ' . $data['object_address'] . ' ' . str_replace('/', '_',
                    $data['object_number']) . '_' . $data['object_id'];
        }

    }

    private function getEmailOptions($data, $pdf, $filename, $indexationType)
    {
        $emailAccountId = \EmailAccount::getForInvoice()->id;

        if ($indexationType === 'liberal') {

            $template =  (\Settings::get('modules_vacancy_management') ? '/indexation/indexatie-leegstandbeheer.phtml' : '/indexation/huurindexatie.phtml');
            $subject = (\Settings::get('modules_vacancy_management') ? 'Indexatie' : 'Huurindexatie');

            return [
                'from' => $emailAccountId,
                'to' => $data['user_id'],
                'subject' => $subject,
                'data' => $data,
                'template' => $template ,
                'attachments' => [['data' => $pdf, 'file' => $filename . '.pdf']]
            ];
        }

        if ($indexationType === 'commercial') {

            $template =  (\Settings::get('modules_vacancy_management') ? '/indexation/indexatie-leegstandbeheer.phtml' : '/indexation/huurindexatie.phtml');
            $subject = (\Settings::get('modules_vacancy_management') ? 'Indexatie ' : 'Huurindexatie');

            return [
                'from' => $emailAccountId,
                'to' => $data['user_id'],
                'subject' => $subject,
                'data' => $data,
                'template' => $template,
                'attachments' => [['data' => $pdf, 'file' => $filename . '.pdf']]
            ];
        }

        throw new \RuntimeException('Should never come here');
    }

    private function logToDb($object, $sendType)
    {
        $data = [
            'run_id' => $object['run_id'],
            'period' => $object['run_period'],
            'period_value' => $object['run_period_value'],
            'users_objects' => $object['users_objects'],
            'send_type' => $sendType,
        ];

        $this->indexationLetterSendModel->insert($data);
    }
}
