<?php

namespace Indexation\Infrastructure\Domain\Model\Variables;

use Indexation\Domain\Model\Variables\ComponentCollection;
use Indexation\Domain\Model\Variables\IndexComponent;
use Indexation\Domain\Model\Variables\SummaryOfOtherComponent;
use Indexation\Domain\Model\Variables\VariablesDTO;
use Indexation\Domain\Model\Variables\VariablesRepository;
use SharedKernel\Domain\Model\Money;

class LegacyVariablesRepository implements VariablesRepository
{
    public function ofInvoiceId($invoiceId)
    {
        $new_indexation_data_row = $this->fetchIndexationDataOfInvoiceId($invoiceId);

        if (!is_array($new_indexation_data_row['indexation_data'])) {
            throw new \RuntimeException('Indexation data not found');
        }

        $totalAmountIncludingVat = Money::makeFromEuroInput(
            $this->getVersionTotalAmountIncludingVat($new_indexation_data_row['new_version_id'])
        );

        $totalAmountExcludingVat = Money::makeFromEuroInput(
            $this->getVersionTotalAmountExcludingVat($new_indexation_data_row['new_version_id'])
        );

        $totalVatAmount = $totalAmountIncludingVat->subtract($totalAmountExcludingVat);

        $componentCollection = new ComponentCollection(
            $totalAmountIncludingVat,
            $totalAmountExcludingVat,
            $totalVatAmount
        );

        $this->addIndexationComponents($componentCollection, $new_indexation_data_row['indexation_data']);
        $this->addSummaryOfOtherComponents($componentCollection, $new_indexation_data_row['indexation_data']);

        $invoice_data = $this->fetchInvoiceDataOfInvoiceId($invoiceId);
        $stationary_data = json_decode($invoice_data['stationary_data'], true);
        $ea = new \EmailAccount();
        $invoice_mail_account = $ea->getForInvoice();

        $this->addLinebreakTagsToDescriptionText($new_indexation_data_row['indexation_data']['description']);

        return new VariablesDTO(
            new \DateTime($new_indexation_data_row['indexation_data']['date']),
            $stationary_data['name'],
            $componentCollection,
            $invoice_mail_account['address'],
            $stationary_data['general_phone'],
            $new_indexation_data_row['indexation_data']['older_index'],
            $new_indexation_data_row['indexation_data']['recent_index'],
            $new_indexation_data_row['indexation_data']['minimum_indexation'],
            $new_indexation_data_row['indexation_data']['maximum_indexation'],
            $new_indexation_data_row['indexation_data']['limit_indexation'],
            $new_indexation_data_row['indexation_data']['percentage_calculated'],
            $new_indexation_data_row['indexation_data']['description']
        );
    }

    private function addLinebreakTagsToDescriptionText(&$descriptionText)
    {
        $descriptionText = str_replace(
            '[break]',
            '<br />',
            $descriptionText
        );
    }

    /**
     * @param $componentCollection ComponentCollection
     * @param $indexation_data array
     */
    private function addIndexationComponents($componentCollection, $indexation_data)
    {
        foreach ($indexation_data['data_per_component'] as $indexation_data_per_component) {
            $indexComponent = new IndexComponent(
                $indexation_data_per_component['component_name'],
                Money::makeFromEuroInput($indexation_data_per_component['previous_amount']),
                Money::makeFromEuroInput($indexation_data_per_component['amount']),
                $indexation_data_per_component['older_index'],
                $indexation_data_per_component['recent_index']
            );


            if (is_numeric($indexation_data_per_component['component'])) {
                $indexation_data_per_component['component_name_english'] = \Components::getEnglishName($indexation_data_per_component['component']) ;
                $indexComponent->setEnglishName($indexation_data_per_component['component_name_english']);
            }


            $componentCollection->addIndexComponent(
                $indexComponent
            );
        }
    }

    /**
     * @param $componentCollection ComponentCollection
     * @param $indexation_data array
     */
    private function addSummaryOfOtherComponents($componentCollection, $indexation_data)
    {
        $non_indexed_components_total = $this->fetchSummaryOfOtherComponents(
            $indexation_data['indexation_source_version_id'],
            array_column($indexation_data['data_per_component'], 'component')
        );

        if ($non_indexed_components_total === '0.00') {
            return;
        }

        $componentCollection->setSummaryOfOtherComponents(
            new SummaryOfOtherComponent(
                'Overige componenten',
                Money::makeFromEuroInput($non_indexed_components_total),
                Money::makeFromEuroInput($non_indexed_components_total)
            )
        );
    }

    private function fetchSummaryOfOtherComponents($source_version_id, $indexed_component_ids)
    {
        $select = db()->select()
            ->from('objects_components_values', ['value_excl' => 'SUM(`value_excl`)'])
            ->where('version = ?', $source_version_id);

        if (count($indexed_component_ids) > 0) {
            $select->where('component NOT IN (' . implode_for_where_in($indexed_component_ids) . ')');
        }
        return db()->fetchOne($select);
    }

    private function fetchIndexationDataOfInvoiceId($invoiceId)
    {
        $select = db()->select()
            ->from(['ir' => 'invoice_rows'], false)
            ->joinLeft(['ocv' => 'objects_components_values'], 'ocv.id = ir.object_component_values_id', false)
            ->joinLeft(['ocversion' => 'objects_components_versions'], 'ocversion.id = ocv.version',
                ['indexation_data', 'new_version_id' => 'id'])
            ->where('ocversion.indexation_data IS NOT NULL')
            ->where('ir.invoice = ?', $invoiceId)
			->order('ocversion.date desc');
        if ($json_encoded_indexation_data_row = db()->fetchRow($select)) {
            $json_encoded_indexation_data_row['indexation_data'] = json_decode(
                $json_encoded_indexation_data_row['indexation_data'],
                true
            );

            $json_encoded_indexation_data_row = $this->checkAndApplyMinimumAndMaximumPercentages(
                $json_encoded_indexation_data_row
            );

            return $json_encoded_indexation_data_row;
        }

        return false;
    }

    private function checkAndApplyMinimumAndMaximumPercentages($data){
        if ($data['indexation_data']['minimum_indexation']) {
            $minimum_indexation_multiplier =
                1 + ($data['indexation_data']['minimum_indexation'] / 100);

            $minimum_indexation_index =
                $data['indexation_data']['older_index'] *
                $minimum_indexation_multiplier;

            if ($data['indexation_data']['recent_index'] > $minimum_indexation_index) {
                $data['indexation_data']['minimum_indexation'] = null;
            }
        }

        if ($data['indexation_data']['maximum_indexation']) {
            $maximum_indexation_multiplier =
                1 + ($data['indexation_data']['maximum_indexation'] / 100);

            $maximum_indexation_index =
                $data['indexation_data']['older_index'] *
                $maximum_indexation_multiplier;

            if ($data['indexation_data']['recent_index'] < $maximum_indexation_index) {
                $data['indexation_data']['maximum_indexation'] = null;
            }
        }

        return $data;
    }

    private function fetchInvoiceDataOfInvoiceId($invoiceId)
    {
        $select = db()->select()
            ->from('invoice_data', ['corporation_name', 'stationary_data'])
            ->where('invoice = ?', $invoiceId);

        return db()->fetchRow($select);
    }

    private function getVersionTotalAmountIncludingVat($version_id)
    {
        $select = db()->select()
            ->from('objects_components_values', ['total' => 'SUM(value_incl)'])
            ->where('version = ?', $version_id)
        ;

        return db()->fetchOne($select);
    }

    private function getVersionTotalAmountExcludingVat($version_id)
    {
        $select = db()->select()
            ->from('objects_components_values', ['total' => 'SUM(value_excl)']) // TODO[Menno] testen
            ->where('version = ?', $version_id)
        ;

        return db()->fetchOne($select);
    }

    private function makeCBSCalculation($indexationData)
    {
        if (!$indexationData) {
            return null;
        }

        $previousAmount = '&euro; ' . new \StringFormat($indexationData['previous_amount'], 'money');
        $newAmount = '&euro; ' . new \StringFormat($indexationData['amount'], 'money');
        return "{$indexationData['recent_index']}/{$indexationData['older_index']} * {$previousAmount} = {$newAmount}";
    }
}
