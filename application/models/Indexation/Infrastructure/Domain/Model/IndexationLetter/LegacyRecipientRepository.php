<?php

namespace Indexation\Infrastructure\Domain\Model\IndexationLetter;

use Indexation\Domain\Model\Recipient\Address;
use Indexation\Domain\Model\Recipient\ContactPersonDTO;
use Indexation\Domain\Model\Recipient\Recipient;
use Indexation\Domain\Model\Recipient\RecipientRepository;

class LegacyRecipientRepository implements RecipientRepository
{
    public function ofInvoiceId($invoiceId)
    {
        $i = new \Invoice();
        $i->id = $invoiceId;
        $invoice = $i->build();

        $naw = $i->buildNaw($invoice);

        if (!$naw) {
            return null;
        }

        if (\Settings::get('invoice_notify_partner')) {
            $naw = $this->reformatNotifyPartnerInNawData($naw, $invoice);
        }

        $naw = $this->reformatNawDataWithTAVAddressing($naw, $invoice);

        $rawName = $naw[0];
        $streetAndNumber = $naw[1];
        $zipCodeAndCity = $naw[2];
        $objectAddress = $invoice['addressstring'];


        $zipCodeAndCity = $this->addInvoiceAddressOverruleLinesToZipcodeAndCity(
            $zipCodeAndCity,
            $invoice,
            $naw
        );

        $address = new Address($streetAndNumber, $zipCodeAndCity);

        $fullName = $this->salutationsReplace($rawName);
        $contactPerson = new ContactPersonDTO($fullName);

        return new Recipient(
            $naw[0],
            $address,
            $contactPerson,
            $invoice['language'] ?: 'nl',
            $objectAddress
        );
    }

    private function reformatNawDataWithTAVAddressing($naw, $invoice)
    {
        if (!isset($naw[1])) {
            return $naw;
        }

        $isTavAddressing = substr($naw[1], 0, 6) === 'T.a.v.';

        if (!$isTavAddressing) {
            return $naw;
        }

        $naw[0] .= ' ' . lcfirst($naw[1]);

        unset($naw[1]);
        return array_values($naw);
    }

    private function addInvoiceAddressOverruleLinesToZipcodeAndCity($zipCodeAndCity, $invoiceData, $naw)
    {
        if ($this->isNotInvoiceAddressOverrule($invoiceData)) {
            return $zipCodeAndCity;
        }

        for ($i = 3; $i <= count($naw); $i++) {
            $zipCodeAndCity .= ' ' . $naw[$i];
        }

        $zipCodeAndCity = $this->removeEmailFromZipCodeAndCity($zipCodeAndCity, $invoiceData);

        return $zipCodeAndCity;
    }

    private function removeEmailFromZipCodeAndCity($zipCodeAndCity, $invoiceData)
    {
        $emailValueKeys = ['u_email', 'c_email', 'u_email_original'];

        foreach ($emailValueKeys as $emailValueKey) {
            if (!$invoiceData[$emailValueKey]) {
                continue;
            }

            $zipCodeAndCity = str_replace($invoiceData[$emailValueKey], '', $zipCodeAndCity);
        }

        return trim($zipCodeAndCity);
    }

    private function isNotInvoiceAddressOverrule($invoiceData){
        return $invoiceData['invoice_overrule'] !== '1';
    }

    private function reformatNotifyPartnerInNawData($naw_data, $invoice_data)
    {
        $notify_partner_name = $invoice_data['notify_partner']['rendered_name'];

        if ($naw_data[1] === $notify_partner_name) {
            $naw_data[0] .= ' en ' . $this->salutationsReplace($notify_partner_name);

            unset($naw_data[1]);
            $naw_data = array_values($naw_data);
        }

        return $naw_data;
    }

    private function salutationsReplace($rawName)
    {
        $salutationsArr = array(
            'Heer' => 'heer',
            'Mevrouw' => 'mevrouw',
            'dhr.' => 'heer',
            'Dhr.' => 'heer',
            'mevr.' => 'mevrouw',
            'Mevr.' => 'mevrouw'
        );

        $rawName = str_replace(array_keys($salutationsArr), $salutationsArr, $rawName);

        return $rawName;
    }
}
