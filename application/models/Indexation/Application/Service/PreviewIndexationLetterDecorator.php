<?php

namespace Indexation\Application\Service;

class PreviewIndexationLetterDecorator
{
    private $indexationLetterService;

    public function __construct(CreateIndexationLetterService $indexationLetterService)
    {
        $this->indexationLetterService = $indexationLetterService;
    }

    /**
     * @param $request CreateCommercialIndexationLetterRequest|CreateLiberalIndexationLetterRequest
     */
    public function execute($request)
    {
        $pdf = $this->indexationLetterService->execute($request);

        ob_clean();
        ob_end_clean();
        header('Content-type: application/pdf');
        header('Cache-Control: private');
        header('Pragma: private');
        header('Content-disposition: inline; filename="preview.pdf"');
        echo $pdf;
    }
}
