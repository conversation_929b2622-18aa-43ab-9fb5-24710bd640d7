<?php

namespace Indexation\Application\Service;


use Indexation\Domain\Service\SendIndexationLetterByEmailService;
use Indexation\Domain\Service\SendIndexationLetterByPostService;

class SendCommercialLetterService
{
    private $createCommercialIndexationLetterService;
    private $sendIndexationLetterByEmailService;
    private $sendIndexationLetterByPostService;

    public function __construct(
        CreateCommercialIndexationLetterService $createCommercialIndexationLetterService,
        SendIndexationLetterByEmailService $sendIndexationLetterByEmailService,
        SendIndexationLetterByPostService $sendIndexationLetterByPostService
    ) {
        $this->createCommercialIndexationLetterService = $createCommercialIndexationLetterService;
        $this->sendIndexationLetterByEmailService = $sendIndexationLetterByEmailService;
        $this->sendIndexationLetterByPostService = $sendIndexationLetterByPostService;
    }

    public function execute($invoiceIds, $generalSendMethod)
    {
        foreach ($invoiceIds as $invoiceId) {
            $this->handleIndexationLetter($invoiceId, $generalSendMethod);
        }
    }

    private function handleIndexationLetter($invoiceId, $generalSendMethod)
    {
        $data = $this->retrieveData($invoiceId, $generalSendMethod);

        $pdf = $this->createCommercialIndexationLetterService->execute(new CreateCommercialIndexationLetterRequest(
            $invoiceId
        ));

        if ($this->isSendMethodEmail($generalSendMethod, $data)) {
            try {
                $this->sendIndexationLetterByEmailService->sent($pdf, $data, 'commercial');
            } catch (\Exception $e) {
            }
        } else {
            try {
                $this->sendIndexationLetterByPostService->sent($pdf, $data);
            } catch (\Exception $e) {
            }
        }
    }

    private function retrieveData($invoiceId, $generalSendMethod)
    {
        $i_lib = new \Invoice();
        $i_lib->id = $invoiceId;
        $invoice_data = $i_lib->getData();

        return [
            // nodig om te bepalen hoe het verzonden moet worden
            'email' => $invoice_data['u_email'],

            // nodig voor de filename
            'object_address' => $invoice_data['o_address'],
            'object_number' => $invoice_data['o_number'],
            'object_id' => $invoice_data['obj_id'],

            // nodig voor het email verzenden
            'user_id' => $invoice_data['userid'],

            // nodig voor het loggen
            'run_id' => $invoice_data['invoicerun'],
            'run_period' => $invoice_data['period'],
            'run_period_value' => $invoice_data['numeric_periodvalue'],
            'users_objects' => $invoice_data['users_objects'],
            'send_type' => $generalSendMethod
        ];
    }

    private function isSendMethodEmail($generalSendMethod, $data)
    {
        return $generalSendMethod === 'email' && $data['email'];
    }
}
