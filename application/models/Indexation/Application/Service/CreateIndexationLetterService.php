<?php

namespace Indexation\Application\Service;

use Indexation\Domain\Model\Recipient\RecipientRepository;
use Indexation\Domain\Model\Variables\VariablesRepository;
use Indexation\Domain\Service\IndexationLetterService;
use SharedKernel\Domain\Service\StationaryPdfService;

abstract class CreateIndexationLetterService
{
    protected $recipientRepository;
    protected $variablesRepository;
    protected $indexationLetterService;
    protected $stationaryPdfService;

    public function __construct(
        RecipientRepository $recipientRepository,
        VariablesRepository $variablesRepository,
        IndexationLetterService $indexationLetterService,
        StationaryPdfService $stationaryPdfService
    ) {
        $this->recipientRepository = $recipientRepository;
        $this->variablesRepository = $variablesRepository;
        $this->indexationLetterService = $indexationLetterService;
        $this->stationaryPdfService = $stationaryPdfService;
    }

    /**
     * @param $request CreateLiberalIndexationLetterRequest|CreateCommercialIndexationLetterRequest
     * @return string
     */
    final public function execute($request)
    {
        $invoiceId = $request->getInvoiceId();

        $recipient = $this->recipientRepository->ofInvoiceId($invoiceId);

        if (null === $recipient) {
            throw new \RuntimeException('recipient not found');
        }

        $variables = $this->variablesRepository->ofInvoiceId($invoiceId);

        if (null === $variables) {
            throw new \RuntimeException('Variables not found');
        }

        $templatePath = $this->getTemplatePath($recipient->getLanguage());

        $pdf = $this->indexationLetterService->makePdf($recipient, $variables, $templatePath);
        $pdf = $this->stationaryPdfService->mergePdfStringWithStationary($pdf, $invoiceId);

        return $pdf;
    }

    abstract protected function getTemplatePath($userLanguage);
}
