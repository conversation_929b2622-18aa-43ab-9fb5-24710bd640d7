<?php

namespace Indexation\Application\Service;

class UpdateIndexationDateOfNotIndexedObjectsService
{
    /**
     * @info When an object is not indexed the previous year the indexation date doesn't get updated to next year.
     * This results in that those objects aren't eligible for next years indexation.
     * This service is ment to correct that.
     * This service should be called once at the start of every new year
     * @return void
     */
    public function execute()
    {
        $currentYear = date('Y');

        $query = "UPDATE `users_objects` SET `indexation_date`=DATE_FORMAT(`indexation_date`,'" . $currentYear . "-%m-%d') WHERE YEAR(`indexation_date`) < " . $currentYear . "";

        db()->query($query);
    }
}

