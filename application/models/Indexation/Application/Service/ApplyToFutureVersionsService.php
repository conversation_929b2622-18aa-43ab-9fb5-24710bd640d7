<?php

namespace Indexation\Application\Service;

use ObjectsComponentsValues as ObjectComponentValueModel;
use ObjectsComponentsVersions as ObjectComponentVersionModel;
use Settings as SettingsHelpeer;
use SharedKernel\Domain\Model\Money;

class ApplyToFutureVersionsService
{
    /**
     * @var ObjectComponentValueModel
     */
    private $objectComponentValueModel;

    /**
     * @var ObjectComponentVersionModel
     */
    private $objectComponentVersionModel;

    public function __construct(
        ObjectComponentValueModel $objectComponentValueModel,
        ObjectComponentVersionModel $objectComponentVersionModel
    ) {
        $this->objectComponentValueModel = $objectComponentValueModel;
        $this->objectComponentVersionModel = $objectComponentVersionModel;
    }


    public function execute(
        $objectId,
        $indexedObjectComponentVersionId,
        $componentId,
        $indexId,
        Money $increasedAmount
    ) {
        if (!$this->isEnabled()) {
            return;
        }

        $indexedObjectComponentVersionData = $this->getIndexedObjectComponentVersionData($indexedObjectComponentVersionId);
        $allFutureVersionIds = $this->getAllFutureVersionIds($objectId, $indexedObjectComponentVersionData);

        foreach ($allFutureVersionIds as $versionId) {
            $allMatchingValueRows = $this->getAllMatchingValueRows(
                $versionId,
                $componentId,
                $indexId
            );

            foreach ($allMatchingValueRows as $matchingValueRow) {
                $this->applyIndexationToValueRow(
                    $matchingValueRow->id,
                    $increasedAmount
                );
            }

            $this->updateIndexationData($versionId, $indexedObjectComponentVersionData);
        }

    }

    private function isEnabled()
    {
        return SettingsHelpeer::get('invoice_indexation_commercial_apply_to_future_versions');
    }

    private function getIndexedObjectComponentVersionData($indexedObjectComponentVersionId)
    {
        $select = db()->select()
            ->from('objects_components_versions', ['date', 'number'])
            ->where('id = ?', $indexedObjectComponentVersionId);

        return db()->fetchRow($select);
    }

    private function getAllFutureVersionIds($objectId, $indexedObjectComponentVersionData)
    {
        $allFutureVersionIdsWithHigherDate = $this->getAllFutureVersionIdsWithHigherDate($objectId,
            $indexedObjectComponentVersionData['date']
        );

        $allFutureVersionIdsWithHigherVersionNumber = $this->getAllFutureVersionIdsWithHigherVersionNumber(
            $objectId,
            $indexedObjectComponentVersionData['date'],
            $indexedObjectComponentVersionData['number']
        );

        return array_merge(
            $allFutureVersionIdsWithHigherDate,
            $allFutureVersionIdsWithHigherVersionNumber
        );
    }

    private function getAllFutureVersionIdsWithHigherDate($objectId, $indexationDateString)
    {
        $select = db()->select()
            ->from('objects_components_versions', ['id'])
            ->where('object = ?', $objectId)
            ->where('date > ?', date('Y-m-d', strtotime($indexationDateString)));

        return db()->fetchCol($select);
    }

    private function getAllFutureVersionIdsWithHigherVersionNumber($objectId, $indexationDateString, $versionNumber)
    {
        $select = db()->select()
            ->from('objects_components_versions', ['id'])
            ->where('object = ?', $objectId)
            ->where('date = ?', date('Y-m-d', strtotime($indexationDateString)))
            ->where('number > ?', $versionNumber);

        return db()->fetchCol($select);
    }

    private function getAllMatchingValueRows(
        $versionId,
        $componentId,
        $indexId
    ) {
        return $this->objectComponentValueModel->matchAll([
            'version' => $versionId,
            'component' => $componentId,
            'index' => $indexId
        ]);
    }

    private function applyIndexationToValueRow($objectComponentValueId, $increasedAmount)
    {
        $objectComponentValueModelRow = $this->objectComponentValueModel
            ->getbyid($objectComponentValueId);

        $indexationBaseValue = $this->getIndexationBaseValue($objectComponentValueModelRow);

        $newValueInCents = Money::makeFromEuroInput($indexationBaseValue)
            ->add($increasedAmount)
            ->getAmountInCents();

        $newValueIncludingVatInCents = $newValueInCents * (1 + ($objectComponentValueModelRow->vat_percentage) / 100);

        $objectComponentValueModelRow
            ->setFromArray([
                'value_excl' => $newValueInCents / 100,
                'value_incl' => round($newValueIncludingVatInCents / 100, 2),
                'pre_indexation_value_excl' => $indexationBaseValue,
            ])
            ->save();
    }

    private function getIndexationBaseValue($objectComponentValueModelRow)
    {
        if ($objectComponentValueModelRow->pre_indexation_value_excl) {
            return $objectComponentValueModelRow->pre_indexation_value_excl;
        }

        return $objectComponentValueModelRow->value_excl;
    }

    private function updateIndexationData($versionId, $indexedObjectComponentVersionData)
    {
        $indexationData = [
            'type' => 'updateFutureIndexation',
            'date' => $indexedObjectComponentVersionData['date']
        ];

        $this->objectComponentVersionModel
            ->getById($versionId)
            ->setFromArray([
                'indexation_data' => json_encode($indexationData)
            ])
            ->save();
    }


}
