<?php

namespace Indexation\Application\Service;

abstract class ViewObjectListService
{
    final public function execute()
    {
        $select = $this->getSelect();
        $objectData = $this->retrieveData($select);
        return $this->modifyData($objectData);
    }

    protected function getSelect()
    {
        return db()->select()
            ->from(['i' => 'invoices'], ['id', 'indexation_status'])
            ->joinLeft(['ir' => 'invoice_rows'], 'ir.invoice = i.id', false)
            ->joinLeft(['i_run' => 'invoices_run'], 'i_run.id = i.run', ['run' => 'i_run.id'])
            ->joinLeft(['uo' => 'users_objects'], 'uo.id = i.users_objects', [
                'user_object_id' => 'uo.id',
                'object_id' => 'uo.object',
                'indexation_date' => 'uo.indexation_date',
                'from' => 'uo.from'
            ])
            ->joinLeft(['o' => 'objects'], 'o.id = uo.object',
                ['object_investor' => 'o.investoroverride', 'object_address' => 'o.rendered_address'])
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', [
                'objectgroup_id' => 'og.id',
                'objectgroup_name' => 'og.description',
                'objectgroup_investor' => 'og.investor'
            ])
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project_id' => 'p.id', 'project_name' => 'p.name'])
            ->joinLeft(['u' => 'users'], 'u.id = uo.customer',
                ['user_id' => 'u.id', 'user_rendered_name' => 'u.rendered_name'])
            ->joinLeft(['oc_values' => 'objects_components_values'], 'oc_values.id = ir.object_component_values_id',
                false)
            ->joinLeft(['oc_version' => 'objects_components_versions'], 'oc_version.id = oc_values.version',
                ['oc_version_indexation_data' => 'oc_version.indexation_data'])
            ->joinLeft(['ils' => 'indexation_letter_send'], 'ils.users_objects = uo.id AND ils.run_id = i_run.id AND YEAR(ils.created_at) >= "' . (new \DateTime())->format('Y') . '"', ['send_type'])
            ->where('i.indexation_status IN ("done", "previous")')
            ->where('i_run.status = ?', 0)
            ->order('indexation_date ASC')
            ->order('o.rendered_address ASC')
            ->group(
                $this->getListGroupingColumn()
            );
    }

    private function getListGroupingColumn()
    {
        return \Settings::get('invoice_corporation_per_component') ? 'i.id' : 'object_id';
    }

    private function retrieveData(\Zend_Db_Select $select)
    {
        return $select->query()
            ->fetchAll();
    }

    protected function modifyData($objects)
    {
        foreach ($objects as $key => $object) {
            $object = $this->selectInvestor($object);
            $object = $this->handleIndexationData($object);
            $object = $this->addCalculation($object);
            $object = $this->calculateIndexPercentage($object);
            $object = $this->handleStatus($object);
            $objects[$key] = $object;
        }

        $objects = $this->handleInvestors($objects);

        return $objects;
    }

    private function handleStatus($object){

    	if($object['send_type']){
    		$object['indexation_status'] = 'sent_using_' . $object['send_type'];
		}

    	return $object;
	}

    private function calculateIndexPercentage($object)
    {
        if (!$object['recent_index'] || !$object['older_index']) {
            return $object;
        }

        $percentage = (($object['recent_index'] / $object['older_index'] - 1) * 100);

        $object['index_percentage'] = round($percentage, 2);

        return $object;
    }

    private function handleInvestors($objects)
    {
        $investorIds = $this->extractInvestorsIds($objects);
        $investors = $this->retrieveInvestors($investorIds);
        $objects = $this->addInvestorsNames($objects, $investors);

        return $objects;
    }

    private function addInvestorsNames($objects, $investors)
    {
        foreach ($objects as &$object) {
            $object['investor_name'] = $investors[$object['investor_id']];
        }

        return $objects;
    }

    private function retrieveInvestors($ids)
    {
        $where = ['id IN(' . implode_for_where_in($ids) . ')'];
        $rows = (new \Investors())->fetchAll($where);

        $investors = [];
        foreach ($rows as $row) {
            $investors[$row['id']] = $row['name'];
        }

        return $investors;
    }

    private function extractInvestorsIds($objects)
    {
        $investors = [];
        foreach ($objects as $object) {
            $investors[$object['investor_id']] = $object['investor_id'];
        }

        return $investors;
    }

    private function selectInvestor($object)
    {
        if (is_numeric($object['object_investor'])) {
            $object['investor_id'] = $object['object_investor'];
        } else {
            $object['investor_id'] = $object['objectgroup_investor'];
        }

        return $object;
    }

    private function handleIndexationData($object)
    {
        $data = json_decode($object['oc_version_indexation_data'], true);

        if (is_array($data)) {
            $object = array_merge($object, $data);
        }

        return $object;
    }

    private function addCalculation($object)
    {
        if ($object['oc_version_indexation_data']) {
            $object['calculation'] = "{$object['recent_index']}/{$object['older_index']} * {$this->moneyFormat($object['previous_amount'])} = {$this->moneyFormat($object['amount'])}";
        }

        return $object;
    }

    private function moneyFormat($value)
    {
        return '&euro; ' . new \StringFormat($value, 'money');
    }
}
