<?php

namespace Indexation\Application\Service;

class CreateLiberalIndexationLetterService extends CreateIndexationLetterService
{
    protected function getTemplatePath($userLanguage)
    {
        $templateBaseDir = $this->getTemplateBasePath();

        $companyShortName = \Settings::get('general_company_shortname');

        if ($this->hasCompanyASpecificTemplate($templateBaseDir, $companyShortName)) {
            return $this->makeCompanySpecificTemplatePath($templateBaseDir, $companyShortName);
        }
        $template = ($userLanguage == 'en' ?  'default_en.html' : 'default.html');

        return $templateBaseDir . $template;
    }

    private function hasCompanyASpecificTemplate($templateBaseDir, $companyShortName)
    {
        if (!$companyShortName) {
            return false;
        }

        return file_exists($this->makeCompanySpecificTemplatePath($templateBaseDir, $companyShortName));
    }

    private function getTemplateBasePath()
    {
        global $omniboxx_base_dir;
        return "$omniboxx_base_dir/application/models/Indexation/Infrastructure/Templates/Liberal/";
    }

    private function makeCompanySpecificTemplatePath($templateBaseDir, $companyShortName)
    {
        return $templateBaseDir . $companyShortName . '.html';
    }
}
