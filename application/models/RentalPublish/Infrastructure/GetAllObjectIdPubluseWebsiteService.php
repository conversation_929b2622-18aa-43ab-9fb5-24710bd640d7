<?php

namespace RentalPublish\Infrastructure;

use RentalPublish\Infrastructure\Repository\ObjectPublishRepository;

class GetAllObjectIdPubluseWebsiteService
{

    /**
     * @var ObjectPublishRepository
     */
    private $objectPublishRepository;

    public function __construct(
        ObjectPublishRepository $objectPublishRepository
    )
    {
        $this->objectPublishRepository = $objectPublishRepository;
    }

    public function execute()
    {
        $objectIds = $this->objectPublishRepository->getObjectIdsByWebsitePublishEnabled();

        return $objectIds;
    }

}
