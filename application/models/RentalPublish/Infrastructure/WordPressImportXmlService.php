<?php

namespace RentalPublish\Infrastructure;

class WordPressImportXmlService
{
    const TRIGGER_URL = '%s/wp-load.php?import_key=%s&import_id=%s&action=trigger';
    const PROCESSING_URL = '%s/wp-load.php?import_key=%s&import_id=%s&action=processing';

    private $numberOfProcessingImportRons = 0;


    public function execute($baseUrl, $importKey, $importId)
    {
        \Logger::add(['publish', 'rental'], 'RentalWordPressImportXmlService ' . $baseUrl . ' :: ' . $importId);

        $this->triggerImport($baseUrl, $importKey, $importId);

        $this->whileLoopProcessingImport($baseUrl, $importKey, $importId);

        \Logger::add(['publish', 'rental'], 'RentalWordPressImportXmlService doen');
    }

    private function triggerImport($baseUrl, $importKey, $importId)
    {
        \Logger::add(['publish', 'rental'], 'Trigger import');

        $url = sprintf(
            self::TRIGGER_URL,
            $baseUrl,
            $importKey,
            $importId
        );

        $respons = $this->doColl($url);
        \Logger::add(['publish', 'rental'], 'Trigger import respons', $respons);

        if (403 == $respons->status &&
            strpos($respons->message, 'already processing. Request skipped') !== false
        ) {
            $this->stopImport();
        }
    }

    private function whileLoopProcessingImport($baseUrl, $importKey, $importId)
    {
        $numberOfProcessingImportRuns = 0;

        while (true) {
            if (10000 < $numberOfProcessingImportRuns) {
                \Logger::add(['publish', 'rental'], 'NumberOfProcessingImportRuns is 20000', $numberOfProcessingImportRuns);
                $this->stopImport();
            }
            $numberOfProcessingImportRuns++;

            \Logger::add(['publish', 'rental'], 'Start processing import ');

            $respons = $this->processingImport($baseUrl, $importKey, $importId);
            \Logger::add(['publish', 'rental'], 'Start processing import respons', $respons);

            if (403 == $respons->status) {
                //sprintf('\033[%sm %s \n \033[0m', 96, 'Code sleep for 10 seconds');
                sleep(10);
            }
        }
    }

    private function processingImport($baseUrl, $importKey, $importId)
    {
        $url = sprintf(
            self::PROCESSING_URL,
            $baseUrl,
            $importKey,
            $importId
        );

        $respons = $this->doColl($url);

        if (403 == $respons->status &&
            strpos($respons->message, 'is not triggered. Request skipped') !== false
        ) {
            \Logger::add(['publish', 'rental'], 'processingImport fail', $respons);
            $this->stopImport();
        }

        return $respons;
    }


    private function stopImport()
    {
        \Logger::add(['publish', 'rental'], 'Stop import');
        die("\n stop import \n");
    }

    private function doColl($url)
    {
        $ctx = stream_context_create(
            ['http' => [
                'timeout' => 4800,  //1200 Seconds is 20 Minutes
            ]
        ]);

        \Logger::add(['publish', 'rental', 'call'], 'reqwest ', $url);
        $responsBody = file_get_contents($url, false, $ctx);
        \Logger::add(['publish', 'rental', 'call'], 'Respons body', $responsBody);

        if (!$responsBody) {
            \Logger::add(['publish', 'rental'], 'Connection fatal! ');

            sleep(10);
            $responsJson = $this->doColl($url);
        } else {
            $responsJson = json_decode($responsBody);
        }

        if (!isset($responsJson->status) ||
            !isset($responsJson->message)
        ) {
            \Logger::add(['publish', 'rental'], 'Woeps !!! ', $responsJson);
        }

        return $responsJson;
    }
}
