<?php

namespace RentalPublish\Infrastructure;

use Dompdf\Exception;
use ObjectAvailability\ObjectAvailability;
use RentalPublish\Infrastructure\Repository\ObjectPublishRepository;
use RentalPublish\Infrastructure\Repository\RantalObjectRepository;
use RentalPublish\Infrastructure\Repository\RowNotFoundException;
use RentalPublish\Infrastructure\Repository\UserObjectRepository;

class GetRentalObjectAvailabilityStatusService
{


    /**
     * @var ObjectPublishRepository
     */
    private $objectPublishRepository;
    /**
     * @var UserObjectRepository
     */
    private $userObjectRepository;
    /**
     * @var RantalObjectRepository
     */
    private $rantalObjectRepository;

    public function __construct(
        ObjectPublishRepository $objectPublishRepository,
        UserObjectRepository $userObjectRepository,
        RantalObjectRepository $rantalObjectRepository
    ) {
        $this->objectPublishRepository = $objectPublishRepository;
        $this->userObjectRepository = $userObjectRepository;
        $this->rantalObjectRepository = $rantalObjectRepository;
    }

    public function execute($objectId)
    {
        $rantalObject = $this->rantalObjectRepository->getByObjectId($objectId);

        if ($rantalObject->getInactive()) {
            return 'withdrawn';
        }

        try {
            $objectPublish = $this->objectPublishRepository->getByObjectId($objectId);

        } catch (RowNotFoundException $rowNotFoundException) {
            return 'withdrawn';

        } catch (\Exception $exception) {
            throw $exception;
        }

        if ($objectPublish->getPublishEnabled() === false) {
            return 'withdrawn';
        }


        if ($objectPublish->getStatusOverride() == 'default') {
            return $this->baseAvailabilityOnContract($objectId);
        } else {
            return $this->baseAvailabilityOnverrideObjectPublish($objectId);
        }

    }

    // is used by 'publish-rental-object', 'quick-publish-rental-object'
    private function baseAvailabilityOnContract($objectId)
    {
//        $objectPublish = $this->objectPublishRepository->getByObjectId($objectId);

        try {
            $atDate = new \DateTime();
            $userObject = $this->userObjectRepository->getCurrentForObjectByDate($objectId, $atDate);
        } catch (RowNotFoundException $rowNotFoundException) {
            return 'withdrawn';

        } catch (\Exception $exception) {
            throw $exception;

        }

        if ($this->isAvailableSoon($objectId)) {
             return 'current';
        }

        if ($userObject->getFinaldate()) {
            return 'leased';
        }

        if (0 != $userObject->getCustomerId()) {
            return 'under-reservation';
        }

        if (0 != $userObject->getContractId()) {
            return 'under-reservation';
        }

        return 'current';

        try {
            $userObjectCurrentContract = $this->getCurrentContract($userObject->getCustomerId());
        } catch (RowNotFoundException $rowNotFoundException) {
            return 'under-reservation';

        } catch (\Exception $exception) {
            throw $exception;

        }

        if ($userObjectCurrentContract) {
            return 'leased';
        }

    }

    private function baseAvailabilityOnverrideObjectPublish($objectId)
    {
        $objectPublish = $this->objectPublishRepository->getByObjectId($objectId);

        if ($objectPublish->getStatusOverride() === false) {
            return 'withdrawn';
        }

        if (!$objectPublish->getPublishOnWebsite()) {
            return 'withdrawn';
        }

        if ($objectPublish->getStartDate() !== null
            && $objectPublish->getStartDate() > new \DateTime()) {
            return 'withdrawn';
        }

        if ($objectPublish->getEndDate() !== null
            && $objectPublish->getEndDate() < new \DateTime()) {
            return 'withdrawn';
        }

        switch ($objectPublish->getStatusOverride()) {
            case 'available':
                $status = ObjectAvailability::STATUS_AVAILABLE;
                $status = 'current';
                break;

            case 'option':
                $status = ObjectAvailability::STATUS_UNDER_OPTION;
                $status = 'under-reservation';
                break;

            case 'occupied':
                $status = ObjectAvailability::STATUS_RENTED;
                $status = 'leased';
                break;

            case 'default':
            default:
                return 'withdrawn';
        }

        return $status;
    }


    private function getCurrentContract($customerId)
    {
        $contract = (new \User())->getCurrentContract($customerId);
        if (!$contract) {
            throw new RowNotFoundException();
        }
        return $contract;
    }

    private function isAvailableSoon($objectId)
    {
        $usersObjectsModel = new \Objectusers();
        $usersObjectsData = $usersObjectsModel->getMostRecentForObjects([$objectId]);
        $userObjectData = end($usersObjectsData);
        $state = $usersObjectsModel->getStateAndAvailableDateForUoRow($userObjectData)['state'];

        return $state === 'available_soon';
    }
}
