<?php

namespace SharedKernel\Domain\Model;

class Money
{
    private $amountInCents;
    private $amountInEuros;

    public static function makeFromEuroInput($amountInEuro = 0)
    {
//        $this->guardAgainstUnSupportedInput($amount);

        $cents = self::convertAnyInputToCents($amountInEuro);
        return new self($cents);
    }

    public function __construct($amount = 0)
    {
        if (is_null($amount)) {
            $amount = 0;
        }

        $this->guardAgainstNonValidCentInput($amount);

        $this->setAmountInCents($amount);
        $this->setAmountInEuros();
    }

    private static function handleFloat($amountInEuro)
    {
        $cent = number_format($amountInEuro, 2, ',', '');
        $cent = (int)str_replace(',', '', $cent);
        return $cent;
    }

    private static function stripOfCharacters($amountInEuro)
    {
        $characters = ['&euro;', ' ', '€', ];
        $amountInEuro = strtolower($amountInEuro);
        return str_replace($characters, '', $amountInEuro);
    }

    public function add(Money $money)
    {
        return new self($this->getAmountInCents() + $money->getAmountInCents());
    }

    public function subtract(Money $money)
    {
        return new self($this->getAmountInCents() - $money->getAmountInCents());
    }

    public function equals(Money $money)
    {
        return $this->getAmountInCents() === $money->getAmountInCents();
    }

    private function setAmountInCents($amount)
    {
        $this->amountInCents = (int)$amount;
    }

    private static function convertAnyInputToCents($amountInEuro)
    {
        $cent = 0;
        if (is_int($amountInEuro)) {
            $cent = $amountInEuro * 100;
            return $cent;
        }

        if (is_float($amountInEuro)) {
            return self::handleFloat($amountInEuro);
        }

        if (is_string($amountInEuro) && is_numeric($amountInEuro)) {
            $float = (float)$amountInEuro;
            return self::handleFloat($float);
        }

        if (is_string($amountInEuro)) {
            // TODO[Menno] guardAgainstInvalidCharacters

            $amountInEuro = self::stripOfCharacters($amountInEuro);

            $a = str_replace(['.', ','], ['', '.'], $amountInEuro);
            $float = (float)$a;
            return self::handleFloat($float);
        }

        return $cent;
    }

    public function getAmountInCents()
    {
        return $this->amountInCents;
    }

    private function setAmountInEuros()
    {
        $this->amountInEuros = $this->getAmountInCents() / 100;
    }

    public function __toString()
    {
        return '€ ' . number_format($this->amountInEuros, 2, ',', '.');
    }

    private function guardAgainstUnSupportedInput($amount)
    {
        // '€', '&euro;', ' ', '.', ',', 1, 2, 3, 4, 5, 6, 7, 8 ,9, 0, '-'
        $pattern = '/(((?:€|&euro;{1})[\ ])?(-{1})?((((\d{1,3})?([.,]\d{3}))?((\d+)?(([.,]\d{1,3})|([,.]\-))))|(\d+)))+$/';
        $raw = preg_replace($pattern, '', $amount);

        if ($raw != '') {
            throw new UnSupportedInputTypeException();
        }
    }

    private function guardAgainstNonValidCentInput($amount)
    {
        if (!is_int($amount) && (!$this->doesContainOnlyDigits($amount) || $this->doesAmountEndPartContainADotAndAZero($amount))) {

            throw new UnSupportedInputTypeException($amount);
        }
    }

    private function doesContainOnlyDigits($amount)
    {
        return preg_match('/^(-{1})?[0-9]*$/', $amount);
    }

    private function doesAmountEndPartContainADotAndAZero($amount)
    {
        return $amount !== (string)(int)$amount;
    }
}
