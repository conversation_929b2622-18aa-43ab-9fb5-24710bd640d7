<?php

namespace SharedKernel\Infrastructure;

class Config
{
    private static $instance;
    private $settings;

    const PROD = 'production';
    const LOCAL = 'local';
    const ENVIRONMENT = self::PROD;

    private function __construct()
    {
        $this->settings = parse_ini_file($this->getPathToConfig(), true);
    }

    private function getPathToConfig()
    {
        global $site_base_dir;

        $path = [
            self::PROD => "/var/www/$site_base_dir/config.ini",
            self::LOCAL => __DIR__ . "/../../../../config.ini",
        ];

        return $path[self::ENVIRONMENT];
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new \SharedKernel\Infrastructure\Config();
        }

        return self::$instance;
    }

    public function get($key)
    {
        if (isset($this->settings[self::ENVIRONMENT][$key])) {
            return $this->settings[self::ENVIRONMENT][$key];
        }

        return null;
    }

    public function __clone()
    {
        throw new \BadMethodCallException('Clone is not supported');
    }
}
