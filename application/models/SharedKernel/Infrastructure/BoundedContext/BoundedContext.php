<?php

namespace SharedKernel\Infrastructure\BoundedContext;

use ReflectionClass;
use SharedKernel\Infrastructure\Environment;

abstract class BoundedContext implements BoundedContextInterface
{
	protected $environment;

	protected $controllerPaths = [
		'/Infrastructure/Application/Delivery/Http/Zend/controllers',
	];

	public function __construct(Environment $environment)
	{
		$this->environment = $environment;
	}

	final public function getPhpDiConfigFilePaths()
	{
		return $this->baseDirectoriesOnBasePath($this->getRelativePhpDiConfigFilePaths());
	}

	protected function getRelativePhpDiConfigFilePaths()
	{
		return [
			'/Infrastructure/Application/DependencyInjection/PHP-DI/config.php',
			"/Infrastructure/Application/DependencyInjection/PHP-DI/config.{$this->environment->getEnvironment()}.php",
		];
	}

	final public function getTacticianConfigFilePaths()
	{
		return $this->baseDirectoriesOnBasePath(
			$this->getRelativeTacticianMappingFilePaths()
		);
	}

	protected function getRelativeTacticianMappingFilePaths()
	{
		return [
			'/Infrastructure/Application/CommandBus/Tactician/mapping.php',
		];
	}

	final public function getDomainEventConfigFilePaths()
	{
		return $this->baseDirectoriesOnBasePath(
			$this->getRelativeDomainEventSubscriberConfigFilePaths()
		);
	}

	protected function getRelativeDomainEventSubscriberConfigFilePaths()
	{
		return [
			'/Infrastructure/Application/DomainEvents/DomainEvents/mapping.php',
		];
	}

	public function getControllerPaths()
	{
		return $this->getDefaultControllerPaths();
	}

	final protected function getDefaultControllerPaths()
	{
		$output = [];
		$controllerPaths =  $this->baseDirectoriesOnBasePath($this->controllerPaths);
		$nonNamespacePart = dirname($this->getBasePath());

		foreach ($controllerPaths as $controllerPath) {
			$output[] = [
				'controller_path' => $controllerPath,
				'non_namespace_part' => $nonNamespacePart,
				'module' => 'default',
			];
		}

		return $output;
	}

	protected function baseDirectoriesOnBasePath($directories)
	{
		return array_map(function ($directory) {
			return $this->getBasePath() . $directory;
		}, $directories);
	}

	protected function getBasePath()
	{
		$basePath = __DIR__;

		try {
			$reflector = new ReflectionClass(get_class($this));
			$basePath = $reflector->getFileName();
			$basePath = dirname($basePath);
		} catch (\ReflectionException $e) {
		}

		return $basePath;
	}

	private function getNamespace()
    {
        try {
            $reflector = new ReflectionClass(get_class($this));
            $namespace = $reflector->getNamespaceName();
        } catch (\ReflectionException $e) {
            $namespace = get_class($this);
            $namespace = preg_replace('/\\\\(.*)/', '', $namespace);
        }

        return $namespace;
    }

	/**
	 * @inheritdoc
	 */
    public function registerWithAutoloader($autoloader)
	{
		$contextBasePath = $this->getBasePath();
		$contextNamespaceName = $this->getNamespace();
		$autoloader->addPsr4("{$contextNamespaceName}\\", [ $contextBasePath ]);
	}
}
