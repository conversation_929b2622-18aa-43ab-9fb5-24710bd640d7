<?php

namespace SharedKernel\Infrastructure\BoundedContext;

interface BoundedContextInterface
{
	/**
	 * @return array
	 */
	public function getPhpDiConfigFilePaths();

	/**
	 * @return array
	 */
	public function getTacticianConfigFilePaths();

	/**
	 * @return array
	 */
	public function getDomainEventConfigFilePaths();

	/**
	 * @return array
	 */
	public function getControllerPaths();

	/**
	 * @param object $autoloader
	 */
	public function registerWithAutoloader($autoloader);
}
