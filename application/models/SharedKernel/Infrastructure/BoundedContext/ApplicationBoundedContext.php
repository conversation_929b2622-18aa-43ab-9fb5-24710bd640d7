<?php

namespace SharedKernel\Infrastructure\BoundedContext;

class ApplicationBoundedContext implements BoundedContextInterface
{
	/** @var BoundedContextInterface[] */
	private $boundedContexts;

    /**
	 * @param BoundedContextInterface[] $boundedContexts
	 */
	public function __construct(array $boundedContexts)
	{
		$this->boundedContexts = $boundedContexts;
	}

    /**
     * @return BoundedContextInterface[]
     */
    public function getBoundedContexts()
    {
        return $this->boundedContexts;
    }

    public function getPhpDiConfigFilePaths()
	{
		return $this->combineOutputOfAllContexts(__FUNCTION__);
	}

	public function getTacticianConfigFilePaths()
	{
		return $this->combineOutputOfAllContexts(__FUNCTION__);
	}

	public function getDomainEventConfigFilePaths()
	{
		return $this->combineOutputOfAllContexts(__FUNCTION__);
	}

	public function getControllerPaths()
	{
		return $this->combineOutputOfAllContexts(__FUNCTION__);
	}

	private function combineOutputOfAllContexts($functionName)
	{
		$output = [];

		array_walk(
			$this->boundedContexts,
			function (BoundedContextInterface $context) use (&$output, $functionName) {
                $output = array_merge($output, $context->$functionName());
			}
		);

		return $output;
	}

	/**
	 * @inheritdoc
	 */
	public function registerWithAutoloader($autoloader)
	{
		$boundedContexts = $this->getBoundedContexts();
		/** @var object $autoloader */
		array_walk($boundedContexts, function (BoundedContext $boundedContext) use ($autoloader) {
			$boundedContext->registerWithAutoloader($autoloader);
		});
	}
}
