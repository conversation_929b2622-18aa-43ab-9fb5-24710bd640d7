<?php

namespace SharedKernel\Infrastructure\CommandBus;

use League\Tactician\CommandBus;
use League\Tactician\Container\ContainerLocator;
use League\Tactician\Handler\CommandHandlerMiddleware;
use League\Tactician\Handler\CommandNameExtractor\ClassNameExtractor;
use Psr\Container\ContainerInterface;

class CommandBusFactory
{
    private $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function build($configFilePaths)
    {
        $mapping = $this->buildMapping($configFilePaths);

        $handlerLocator = new ContainerLocator($this->container, $mapping);
        $handlerLocator = new AutoFindApplicationServiceLocatorDecorator($handlerLocator, $this->container);

        $handlerMiddleware = new CommandHandlerMiddleware(
            new ClassNameExtractor(),
            $handlerLocator,
            new ExecuteInflector()
        );

        return new CommandBus([new AsyncWorkerMiddleware(), $handlerMiddleware]);
    }

	protected function buildMapping(array $configFilePaths)
	{
		$mapping = [];

		foreach ($configFilePaths as $mappingFilePath) {
			if (is_readable($mappingFilePath)) {
				/** @noinspection PhpIncludeInspection */
				include $mappingFilePath;
			}

			/** @noinspection PhpUndefinedVariableInspection */
			if ($getMapping !== null && is_callable($getMapping)) {
				/** @noinspection SlowArrayOperationsInLoopInspection */
				$mapping = array_merge($mapping, $getMapping());
				unset($getMapping);
			}
		}

		return $mapping;
	}
}
