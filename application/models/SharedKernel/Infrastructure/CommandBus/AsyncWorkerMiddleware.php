<?php

namespace SharedKernel\Infrastructure\CommandBus;

use League\Tactician\Middleware;
use SharedKernel\Application\Service\AsyncWorkerRequest;

class AsyncWorkerMiddleware implements Middleware
{
    public function execute($command, callable $next)
    {
        if (get_class($command) === AsyncWorkerRequest::class) {
            $request = serialize($command);
            $request = urlencode($request);

            return startWorker('worker-request/request/' . $request);
        }

        return $next($command);
    }
}
