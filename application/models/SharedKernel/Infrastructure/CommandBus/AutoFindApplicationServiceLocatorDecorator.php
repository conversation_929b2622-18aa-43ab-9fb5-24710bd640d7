<?php

namespace SharedKernel\Infrastructure\CommandBus;

use League\Tactician\Handler\Locator\HandlerLocator;
use Psr\Container\ContainerInterface;

class AutoFindApplicationServiceLocatorDecorator implements HandlerLocator
{
    private $handlerLocator;

    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @param HandlerLocator $handlerLocator
     * @param ContainerInterface $container
     */
    public function __construct(HandlerLocator $handlerLocator, ContainerInterface $container)
    {
        $this->handlerLocator = $handlerLocator;
        $this->container = $container;
    }

    /**
     * Retrieves the handler for a specified command
     * @param string $commandName
     * @return object
     */
    public function getHandlerForCommand($commandName)
    {
        $serviceId = $this->makeServiceId($commandName);

        if (null === $serviceId) {
            return $this->handlerLocator->getHandlerForCommand($commandName);
        }

        if (!$this->container->has($serviceId)) {
            return $this->handlerLocator->getHandlerForCommand($commandName);
        }

        return $this->container->get($serviceId);
    }

    private function makeServiceId($commandName)
    {
        if (substr($commandName, -7) !== 'Request') {
            return null;
        }

        return str_replace('Request', 'Service', $commandName);
    }
}
