<?php

namespace SharedKernel\Infrastructure;

class Environment
{
    const DEVELOPMENT = 'development';
    const PRODUCTION = 'production';
	const TESTING = 'testing';

    private $environments = [
        self::DEVELOPMENT,
		self::PRODUCTION,
		self::TESTING,
    ];

    private $environment;

	/**
	 * Environment constructor.
	 * @param string $environment
	 * @throws \Exception
	 */
	public function __construct($environment)
    {
        $this->setEnvironment($environment);
    }

	/**
	 * @param string $environment
	 * @throws \Exception
	 */
	private function setEnvironment($environment)
    {
		if (!in_array($environment, $this->environments, true)) {
			throw new \UnexpectedValueException(sprintf(
            	'Invalid environment %s provided, valid options are: %s',
				$environment,
				implode(', ', $this->environments)
			));
        }

        $this->environment = $environment;
    }

    public function getEnvironment()
    {
        return $this->environment;
    }
}
