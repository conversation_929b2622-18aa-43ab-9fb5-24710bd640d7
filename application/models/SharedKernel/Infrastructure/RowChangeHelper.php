<?php

namespace SharedKernel\Infrastructure;

use GlobalDbRowClass;
use ReflectionClass;
use ReflectionException;

class RowChangeHelper {
	private static $NEWNESS_IGNORE = 0;
	private static $NEWNESS_NEW_ONLY = 1;
	private static $NEWNESS_EXISTING_ONLY = 2;
	/** @var GlobalDbRowClass */
	private $row;
	/** @var array */
	private $whiteList = [];
	/** @var array */
	private $blackList = [];
	/** @var int */
	private $newNess;

	private function __construct(GlobalDbRowClass $row) {
		$this->row = $row;
		$this->newNess = self::$NEWNESS_IGNORE;
	}

	public static function ifThisRowChanged(GlobalDbRowClass $row) {
		return new RowChangeHelper($row);
	}

	public function ignoringTheseColumns(array $columns) {
		$this->blackList = $columns;
		return $this;
	}

	public function lookingOnlyAtTheseColumns(array $columns) {
		$this->whiteList = $columns;
		return $this;
	}

	public function andItsANewRow() {
		$this->newNess = self::$NEWNESS_NEW_ONLY;
		return $this;
	}

	public function andItsAnExistingRow() {
		$this->newNess = self::$NEWNESS_EXISTING_ONLY;
		return $this;
	}

	public function andThis(callable $theThing) {
		return $this->doThis($theThing);
	}

	public function doThis(callable $theThing) {
		if ($this->theRowWasModified()) {
			$rowChangeDTO = $this->buildRowChangeDTO();
			$theThing($rowChangeDTO);
		}

		return $this;
	}

	private function theRowWasModified() {
		return $this->didFieldsWeWatchChange()
			&& $this->isNewNessCorrect();
	}

	private function didFieldsWeWatchChange() {
		return count($this->filterWatchedFieldNames($this->getModifiedFieldNames())) > 0;
	}

	private function filterWatchedFieldNames($fieldNames) {
		if (count($this->whiteList) > 0) {
			$fieldNames = $this->removeNonWhitelistedFields($fieldNames);
		}

		if (count($this->blackList) > 0) {
			$fieldNames = $this->removeBlacklistedFields($fieldNames);
		}

		return $fieldNames;
	}

	private function removeNonWhitelistedFields($modifiedFields) {
		return array_intersect($modifiedFields, $this->whiteList);
	}

	private function removeBlacklistedFields($modifiedFields) {
		return array_diff($modifiedFields, $this->blackList);
	}

	private function getModifiedFieldNames() {
		return array_keys($this->getProperty('_modifiedFields') ?: []);
	}

	private function getProperty($propertyName) {
		try {
			$reflectionClass = new ReflectionClass(GlobalDbRowClass::class);
			$reflectionProperty = $reflectionClass->getProperty($propertyName);
			$reflectionProperty->setAccessible(true);

			return $reflectionProperty->getValue($this->row);
		} catch (ReflectionException $e) {
			return null;
		}
	}

	private function isNewNessCorrect() {
		$rowIsNew = $this->rowIsNew();

		return $this->newNess === self::$NEWNESS_IGNORE
			|| ($rowIsNew && $this->newNess === self::$NEWNESS_NEW_ONLY)
			|| (!$rowIsNew && $this->newNess === self::$NEWNESS_EXISTING_ONLY);
	}

	private function rowIsNew() {
		$newData = $this->getProperty('_data') ?: [];
		return (int)$newData['id'] <= 0;
	}

	/**
	 * @return RowChangeDTO
	 */
	private function buildRowChangeDTO() {
		$modifiedFieldNames = $this->getProperty('_modifiedFields') ?: [];
		$checkedFieldNames = $this->filterWatchedFieldNames(
			array_keys($this->getProperty('_data') ?: [])
		);
		$checkedFieldNames = array_fill_keys($checkedFieldNames, true);

		$old = $this->getProperty('_cleanData') ?: [];
		$oldDiff = array_intersect_key($old, $modifiedFieldNames);
		$oldChecked = array_intersect_key($old, $checkedFieldNames);
		$oldDiffChecked = array_intersect_key($oldDiff, $oldChecked);

		$new = $this->getProperty('_data') ?: [];
		$newDiff = array_intersect_key($new, $modifiedFieldNames);
		$newChecked = array_intersect_key($new, $checkedFieldNames);
		$newDiffChecked = array_intersect_key($newDiff, $newChecked);

		return new RowChangeDTO(
			$old,
			$oldDiff,
			$oldChecked,
			$oldDiffChecked,
			$new,
			$newDiff,
			$newChecked,
			$newDiffChecked,
			$this->rowIsNew()
		);
	}
}
