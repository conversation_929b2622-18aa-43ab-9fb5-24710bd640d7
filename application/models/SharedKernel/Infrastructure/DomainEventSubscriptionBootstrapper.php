<?php

namespace SharedKernel\Infrastructure;

use SharedKernel\Domain\DomainEventPublisher;
use SharedKernel\Domain\DomainEventSubscriber;

class DomainEventSubscriptionBootstrapper
{
	/** @var \DI\Container */
	private $container;

	/**
	 * DomainEventSubscriptionBootstrapper constructor.
	 * @param \DI\Container $container
	 */
	public function __construct(\DI\Container $container)
	{
		$this->container = $container;
	}

	/**
	 * @param array $configFilePaths
	 * @throws \DI\DependencyException
	 * @throws \DI\NotFoundException
	 */
	public function subscribeAll(array $configFilePaths)
	{
		$subscriptions = $this->gatherSubscriptions($configFilePaths);

		foreach ($subscriptions as $subscription) {
			$type = gettype($subscription);
			$type = is_callable($subscription) ? 'callable' : $type;

			switch ($type) {
				case ('string'):
					$domainEventSubscriber = $this->subscribeString($subscription);
					break;

				case ('callable'):
					$domainEventSubscriber = $this->subscribeCallable($subscription);
					break;

				default:
			}

			if (!empty($domainEventSubscriber)) {
				if (!$this->isValidSubscriber($domainEventSubscriber)) {
					throw new \Exception('Not a valid subscriber!');
				}

				DomainEventPublisher::instance()->subscribe($domainEventSubscriber);
			}
		}
	}

	/**
	 * @param $subscription
	 * @return mixed
	 * @throws \DI\DependencyException
	 * @throws \DI\NotFoundException
	 */
	private function subscribeString($subscription)
	{
		return $this->container->get($subscription);
	}

	private function subscribeCallable($subscription)
	{
		return $subscription($this->container);
	}

	private function isValidSubscriber($domainEventSubscriber)
	{
		return $domainEventSubscriber instanceof DomainEventSubscriber;
	}

	protected function gatherSubscriptions(array $configFilePaths)
	{
		$mapping = [];

		foreach ($configFilePaths as $mappingFilePath) {
			if (is_readable($mappingFilePath)) {
				/** @noinspection PhpIncludeInspection */
				include $mappingFilePath;
			}

			/** @noinspection PhpUndefinedVariableInspection */
			if ($getMapping !== null && is_callable($getMapping)) {
				/** @noinspection SlowArrayOperationsInLoopInspection */
				$mapping = array_merge($mapping, $getMapping());
				unset($getMapping);
			}
		}

		return $mapping;
	}
}
