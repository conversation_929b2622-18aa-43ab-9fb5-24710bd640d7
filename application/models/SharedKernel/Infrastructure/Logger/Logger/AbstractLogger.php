<?php

namespace SharedKernel\Infrastructure\Logger\Logger;

use Monolog\Formatter\LineFormatter;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;
use Monolog\Logger as MonologLogger;
use SharedKernel\Infrastructure\Logger\AdvancedSqlLoggerHandler;

abstract class AbstractLogger
{
    /**
     * @var Logger
     */
    private $fileLogger;

    /**
     * @var Logger
     */
    private $sqlLogger;
    private $requestResponseCycleId;
    private $useCase;

    protected function __construct()
    {
        $this->requestResponseCycleId = uniqid(mt_rand(), true);
        $this->fileLogger = $this->buildFileLogger();
        $this->sqlLogger = $this->buildSqlLogger();
    }

    protected function buildFileLogger($channelName = null)
    {
        $filePath = "_data/log/{$this->getModuleName()}/{$this->getLogFileName()}";
        $maxFiles = $this->getMaxFiles();
        $rotatingFileHandler = new RotatingFileHandler($filePath, $maxFiles);
        $rotatingFileHandler->setFilenameFormat('{date}-{filename}', 'Y-m-d');

        if (null === $channelName) {
            $channelName = $this->getModuleName();
        }

        $fileLog = new MonologLogger($channelName);
        $fileLog->pushHandler($rotatingFileHandler);

        return $fileLog;
    }

    protected function buildSqlLogger()
    {
        $advancedSqlLoggerHandler = new AdvancedSqlLoggerHandler(
            $this->requestResponseCycleId,
            $this->getModuleName(),
            $this->useCase,
            $this->getLogTableName()
        );

        $logger = new MonologLogger('sql_logger');
        $logger->pushHandler($advancedSqlLoggerHandler);

        return $logger;
    }

    public function info($message, $context = [])
    {
        $context['calling_path'] = $this->makeCallingPath();
        $context['use_case'] = $this->useCase;
        $this->sqlLogger->info($message, $context);
        $this->fileLogger->info($message, $context);

        return $this;
    }

    public function error($message, $context = [])
    {
        $context['calling_path'] = $this->makeCallingPath();
        $context['request_response_cycle_id'] = $this->requestResponseCycleId;
        $this->fileLogger->error($message, $context);

        return $this;
    }

    public function warning($message, $context = [])
    {
        $context['calling_path'] = $this->makeCallingPath();
        $context['request_response_cycle_id'] = $this->requestResponseCycleId;
        $this->fileLogger->warning($message, $context);

        return $this;
    }

    public function useCase($useCase)
    {
        $this->useCase = $useCase;
        $this->fileLogger = $this->buildFileLogger($useCase);

        return $this;
    }

    public function clearUseCase()
    {
        $this->useCase = null;
        $this->fileLogger = $this->buildFileLogger();

        return $this;
    }

    abstract protected function getModuleName();

    protected function getMaxFiles()
    {
        return 30;
    }

    abstract protected function getLogFileName();

    protected function getLogTableName()
    {
        return 'logs';
    }

    protected function makeCallingPath()
    {
        $traceList = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        return $traceList[2]['class'] ? $traceList[2]['class'] . ':' . $traceList[2]['line']: 'Unknown';
    }
}
