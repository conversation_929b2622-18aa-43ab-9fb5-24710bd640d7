<?php

namespace SharedKernel\Infrastructure\Logger;

use loginManager;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Logger as MonologLogger;

class AdvancedSqlLoggerHandler extends AbstractProcessingHandler
{
    protected $tableName;
    protected $module;
    protected $requestResponseCycleId;
    protected $useCase;

    public function __construct($requestResponseCycleId, $moduleName, $useCase, $tableName, $level = MonologLogger::DEBUG, $bubble = true)
    {
        $this->tableName = $tableName;
        $this->requestResponseCycleId = $requestResponseCycleId;
        $this->module = $moduleName;
        $this->useCase = $useCase;

        parent::__construct($level, $bubble);
    }

    protected function write(array $record)
    {
        $callingPath = $record['context']['calling_path'];
        $useCase = $record['context']['use_case'];
        unset($record['context']['calling_path'], $record['context']['use_case']);

        $statement = db()->prepare("INSERT INTO $this->tableName (request_response_cycle_id, module, use_case, message, level, calling_path, user, extras, created_at) VALUES (:requestResponseCycleId, :module, :useCase, :message, :level, :callingPath, :user, :extras, :createdAt)");
        $statement->execute([
            'requestResponseCycleId' => $this->requestResponseCycleId,
            'module' => $this->module,
            'useCase' => $useCase,
            'message' => $record['message'],
            'level' => $record['level_name'],
            'callingPath' => $callingPath,
            'user' => loginManager::data()->id,
            'extras' => json_encode($record['context']),
            'createdAt' => (new \DateTime())->format('Y-m-d H:i:s')
        ]);
    }
}
