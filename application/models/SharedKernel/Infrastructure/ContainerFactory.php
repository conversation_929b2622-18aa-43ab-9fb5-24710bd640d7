<?php

namespace SharedKernel\Infrastructure;

use DI\Container;
use DI\ContainerBuilder;
use Doctrine\Common\Cache\FilesystemCache;

class ContainerFactory
{
    /**
     * @param Environment $environment
     * @param $configFilePaths string[]
     * @return Container
     */
	public function build(Environment $environment, $configFilePaths)
    {
        global $omniboxx_base_dir;
        $containerBuilder = new ContainerBuilder();

        if ($environment->getEnvironment() !== Environment::DEVELOPMENT) {
            $containerBuilder->setDefinitionCache(new FilesystemCache($omniboxx_base_dir. '/var/cache/'));
        }

        $containerBuilder->useAnnotations(true);

        array_walk($configFilePaths, function ($dir) use ($containerBuilder) {
            if (is_readable($dir)) {
                $containerBuilder->addDefinitions($dir);
            }
        });

        return $containerBuilder->build();
    }
}
