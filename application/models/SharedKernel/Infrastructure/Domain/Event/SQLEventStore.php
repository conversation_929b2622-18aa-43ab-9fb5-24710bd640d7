<?php

namespace SharedKernel\Infrastructure\Domain\Event;

use DbTable\DomainEvent as DomainEventTable;
use <PERSON><PERSON>\Serializer\Serializer;
use <PERSON><PERSON>\Serializer\SerializerBuilder;
use SharedKernel\Application\EventStore;
use SharedKernel\Domain\DomainEvent;
use SharedKernel\Domain\Event\StoredEvent;

class SQLEventStore implements EventStore
{
    /**
     * @var Serializer
     */
    private $serializer;
    private $eventModel;

    public function __construct(DomainEventTable $eventModel)
    {
        $this->serializer = SerializerBuilder::create()->build();
        $this->eventModel = $eventModel;
    }

    public function append(DomainEvent $aDomainEvent)
    {
        $storedEvent = new StoredEvent(
            get_class($aDomainEvent),
            $aDomainEvent->occurredOn(),
            $this->serializer->serialize($aDomainEvent, 'json')
        );

        $eventData = [
            'event_body' => $storedEvent->eventBody(),
            'occurred_on' => $storedEvent->occurredOn()->format('Y-m-d H:i:s'),
            'type_name' => $storedEvent->typeName()
        ];

        $eventRow = $this->eventModel->createRow($eventData);
        $eventRow->save();
    }

    public function allStoredEventsSince($anEventId)
    {
        $select = $this->eventModel->select();

        if ($anEventId) {
            $select->where('id > ?', $anEventId);
        }

        $select->order('id');

        $eventRows = $select->query()->fetchAll();

        return $this->reconstituteStoredEvents($eventRows);
    }

    private function reconstituteStoredEvents(array $eventRows)
    {
        return array_map(function ($eventRow) {
            return $this->reconstituteStoredEvent($eventRow);
        }, $eventRows);
    }

    private function reconstituteStoredEvent($eventRow)
    {
        $storedEvent = new StoredEvent(
            $eventRow['type_name'],
            new \DateTimeImmutable($eventRow['occurred_on']),
            $eventRow['event_body']
        );

        $storedEvent->setEventId($eventRow['id']);

        return $storedEvent;
    }
}
