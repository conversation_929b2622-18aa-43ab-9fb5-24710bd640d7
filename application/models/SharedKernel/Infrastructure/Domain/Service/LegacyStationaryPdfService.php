<?php

namespace SharedKernel\Infrastructure\Domain\Service;

use SharedKernel\Domain\Service\StationaryPdfService;

class LegacyStationaryPdfService implements StationaryPdfService
{
    public function mergePdfStringWithStationary($pdfString, $invoiceId)
    {
        $data = $this->fetchStationaryData($invoiceId);

        $pdf = new \Pdf();
        include 'application/views/scripts/partials/pdftemplates/styles.phtml';

        $pdf->addPageTemplateToCurrentPdfFromString($pdfString);
        $pdf->addStationery($data);

        return $pdf->zendpdf->render();
    }

    private function fetchStationaryData($invoiceId, $userId = false)
    {
        $invoice = $this->getCorporationDataForInvoiceTemplate($invoiceId, $userId);

        if (isset($invoice['no_stationary_on_pages'])) {
            $invoice['no_stationary_on_pages'] = json_decode($invoice['no_stationary_on_pages'], true);

            if (is_object($invoice['no_stationary_on_pages'])) {
                $invoice['no_stationary_on_pages'] = (array)$invoice['no_stationary_on_pages'];
            }
        }

        return $invoice;
    }

    private function getCorporationDataForInvoiceTemplate($invoiceId, $userId = false)
    {
        $invoiceRow = db()->select()
            ->from(['i' => 'invoices'], ['users_objects', 'object', 'customid'])
            ->where('i.id = ?', $invoiceId)
            ->query()
            ->fetch();

        $select = db()->select()
            // invoices
            ->from(['i' => 'invoices'], ['id', 'identifier', 'investor'])
            ->joinLeft(
                ['ig' => 'invoices_run'],
                'i.run = ig.id',
                ['run' => 'id', 'type', 'project', 'iridentifier' => 'identifier']
            )
            ->where('i.id = ?', $invoiceId);

        if ((is_numeric($invoiceRow['users_objects']) && $invoiceRow['users_objects'] != 0) || $invoiceRow['object'] > 0) {
            if ($invoiceRow['users_objects'] > 0) {
                $select
                    ->joinLeft(['uo' => 'users_objects'], 'uo.id = i.users_objects', ['uoid' => 'id'])
                    ->joinLeft(['u' => 'users'], 'u.id = uo.customer', ['u_language' => 'language'])
                    ->joinLeft(['o' => 'objects'], 'o.id = uo.object', false);
            } elseif ($invoiceRow['object'] > 0) {
                $select->joinLeft(['o' => 'objects'], 'o.id = ' . $invoiceRow['object'], false);
            }

            $select
                ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['investor_corporation'])
                ->joinLeft(['p' => 'projects'], 'p.id = og.project',
                    ['corporation', 'alternative_invoice_layout', 'manage_corporation' => 'investor_corporation'])
                ->joinLeft(['bao' => 'corporations_bank_accounts'], 'bao.id = o.bankaccount AND bao.is_active = 1',
                    [
                        'bankaccountoverrideid' => 'id',
                        'bankaccountoverridename' => 'bankaccountname',
                        'bankaccountoverride' => 'number',
                        'banktypeoverride' => 'type',
                        'bank_grouped_collectionoverride' => 'grouped_collection',
                        'ibanoverride' => 'iban',
                        'bicoverride' => 'bic'
                    ]);

            if ($userId !== false && $invoiceRow['users_objects'] > 0) {
                $select->where('uo.customer', $userId);
            }

            if (intval($invoiceRow['customid']) > 0) {
                $select
                    ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid',
                        ['ic_corporation' => 'corporation', 'no_stationary_on_pages', 'is_purchase']);
            }

        } elseif (is_numeric($invoiceRow['customid'])) {
            $select
                ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid',
                    ['corporation', 'no_stationary_on_pages', 'is_purchase']);
        } else {
            $select
                ->joinLeft(['io' => 'invoice_objects'], 'io.invoice = i.id', false)
                ->joinLeft(['o' => 'objects'], 'o.id = io.object', false)
                ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', false)
                ->joinLeft(['p' => 'projects'], 'p.id = og.project OR p.id = ig.project',
                    ['corporation', 'manage_corporation' => 'investor_corporation'])
                ->joinLeft(['bao' => 'corporations_bank_accounts'], 'bao.id = o.bankaccount AND bao.is_active = 1',
                    [
                        'bankaccountoverrideid' => 'id',
                        'bankaccountoverridename' => 'bankaccountname',
                        'bankaccountoverride' => 'number',
                        'banktypeoverride' => 'type',
                        'bank_grouped_collectionoverride' => 'grouped_collection',
                        'ibanoverride' => 'iban',
                        'bicoverride' => 'bic'
                    ]);
        }

        $invoice = db()->fetchRow($select);

        if ($invoice['investor_corporation'] > 0) {
            $invoice['corporation'] = $invoice['investor_corporation'];
        }

        if (intval($invoice['ic_corporation']) > 0) {
            $invoice['corporation'] = $invoice['ic_corporation'];
        }

        if (!empty($invoice['u_language'])) {
            $invoice['custom']['language'] = $invoice['u_language'];
        }

        $invoice = array_merge($invoice, $this->getCorporationData($invoice['corporation']));

        if (intval($invoice['manage_corporation']) > 0) {
            $managing_corporation = $this->getCorporationData($invoice['manage_corporation']);
            $invoice['manage_corporation'] = $managing_corporation['name'];
            $invoice['manage_kvk'] = $managing_corporation['kvk'];
        }

        if (empty($invoice)) {
            return;
        }

        // override the corporation name with with its default bankaccountname, if any
        $trimmedCorpName = trim($invoice['name']);
        $trimmedName = trim($invoice['corp_bankaccountname']);

        if (\Settings::get('invoice_corporation_override_normal_behaviour')) {
            $invoice['name'] = !empty($trimmedName) ? $invoice['corp_bankaccountname'] : $invoice['name'];
        }

        // if the default bankaccount is overridden, use the override values
        if (!empty($invoice['bankaccountoverrideid']) || $invoice['bankaccountoverrideid'] != 0) {
            $invoice['bank'] = $invoice['bankaccountoverride'];
            $invoice['iban'] = $invoice['ibanoverride'];
            $invoice['banktype'] = $invoice['banktypeoverride'];
            $invoice['bank_grouped_collection'] = $invoice['bank_grouped_collectionoverride'];
            $invoice['bic'] = $invoice['bicoverride'];

            $invoice['name'] = $invoice['bankaccountoverridename'] ?: $invoice['name'];
        }

        // if the name that ends up on the invoice is different than the corporation's,
        // do not show kvk/tax (as they do not corrospond to a corp. with that name)

        $trimmedName = trim($invoice['name']);
        if (!empty($trimmedName) && !empty($trimmedCorpName) && $trimmedCorpName != $trimmedName && !in_array(\Settings::get('general_company_shortname'),
                ['UMW'])) {
            $invoice['kvk'] = '';
            $invoice['tax'] = '';
        }

        $invoice['type'] = $invoice['investor'] > 0 && $invoice['type'] != 'custom' ? 'investor' : $invoice['type'];
        $invoice['bank'] = (string)new \StringFormat($invoice['bank'], 'bank');
        $invoice['fullidentifier'] = $invoice['iridentifier'] . '.' . str_pad($invoice['identifier'], 4, "0",
                STR_PAD_LEFT);

        return $invoice;
    }

    private function getCorporationData($corporationId)
    {
        $select = db()->select()
            ->from(['c' => 'corporations'], [
                'name',
                'number',
                'kvk',
                'tax',
                'logo',
                'upload_letterhead',
                'general_address_street',
                'general_address_number',
                'general_phone',
                'general_email',
                'general_website',
                'general_address_zipcode',
                'general_address_city',
                'general_fax'
            ])
            ->joinLeft(['ba' => 'corporations_bank_accounts'], 'ba.corporation_id = c.id AND ba.is_current = 1',
                ['corp_bankaccountname' => 'bankaccountname', 'bank' => 'number', 'iban', 'bic'])
            ->where('c.id = ?', $corporationId);

        $result = db()->fetchRow($select);

        return array_merge(
            $result,
            $this->getUploadedLetterheadDataForCorporation($corporationId)
        );
    }

    private function getUploadedLetterheadDataForCorporation($corporationId)
    {
        $result = db()->select()
            ->from(['c' => 'corporations'], false)
            ->joinLeft(['im' => 'images'], 'c.upload_letterhead = im.id',
                ['filename', 'positioning', 'width', 'height', 'left', 'top'])
            ->where('c.id = ?', $corporationId)
            ->query()
            ->fetch();

        if ($result['filename'] == '' && \Settings::get('upload_letterhead')) {

            $uploadedLetterhead = db()->select()
                ->from('images')
                ->where('id = ?', \Settings::get('upload_letterhead'))
                ->query()
                ->fetch();

            $result['filename'] = $uploadedLetterhead['filename'];
            $result['positioning'] = $uploadedLetterhead['positioning'];
            $result['width'] = $uploadedLetterhead['width'];
            $result['height'] = $uploadedLetterhead['height'];
            $result['left'] = $uploadedLetterhead['left'];
            $result['top'] = $uploadedLetterhead['top'];
        }

        return $result;
    }
}
