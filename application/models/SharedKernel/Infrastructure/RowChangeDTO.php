<?php

namespace SharedKernel\Infrastructure;

class RowChangeDTO {
	/** @var array */
	private $old;
	/** @var array */
	private $oldDiff;
	/** @var array */
	private $oldChecked;
	/** @var array */
	private $oldDiffChecked;
	/** @var array */
	private $new;
	/** @var array */
	private $newDiff;
	/** @var array */
	private $newChecked;
	/** @var array */
	private $newDiffChecked;
	/** @var boolean */
	private $newRow;

	/**
	 * @param array $old
	 * @param array $oldDiff
	 * @param array $oldChecked
	 * @param array $oldDiffChecked
	 * @param array $new
	 * @param array $newDiff
	 * @param array $newChecked
	 * @param array $newDiffChecked
	 * @param bool $isNewRow
	 */
	public function __construct(
		array $old,
		array $oldDiff,
		array $oldChecked,
		array $oldDiffChecked,
		array $new,
		array $newDiff,
		array $newChecked,
		array $newDiffChecked,
		$isNewRow
	) {
		$this->old = $old;
		$this->oldDiff = $oldDiff;
		$this->oldChecked = $oldChecked;
		$this->oldDiffChecked = $oldDiffChecked;
		$this->new = $new;
		$this->newDiff = $newDiff;
		$this->newChecked = $newChecked;
		$this->newRow = $isNewRow;
		$this->newDiffChecked = $newDiffChecked;
	}

	/**
	 * @return array
	 */
	public function getOld() {
		return $this->old;
	}

	/**
	 * Returns the old values of columns that changed
	 * @return array
	 */
	public function getOldDiff() {
		return $this->oldDiff;
	}

	/**
	 * Returns the old values of columns that were checked for changes
	 * @return array
	 */
	public function getOldChecked() {
		return $this->oldChecked;
	}

	/**
	 * Returns the old values of columns that were checked for changes and actually changed
	 * @return array
	 */
	public function getOldDiffChecked() {
		return $this->oldDiffChecked;
	}

	/**
	 * @return array
	 */
	public function getNew() {
		return $this->new;
	}

	/**
	 * @see RowChangeDTO::getOldDiff()
	 * @return array
	 */
	public function getNewDiff() {
		return $this->newDiff;
	}

	/**
	 * @see RowChangeDTO::getOldChecked()
	 * @return array
	 */
	public function getNewChecked() {
		return $this->newChecked;
	}

	/**
	 * @see RowChangeDTO::getOldDiffChecked()
	 * @return array
	 */
	public function getNewDiffChecked() {
		return $this->newDiffChecked;
	}

	/**
	 * @return bool
	 */
	public function isNewRow() {
		return $this->newRow;
	}
}
