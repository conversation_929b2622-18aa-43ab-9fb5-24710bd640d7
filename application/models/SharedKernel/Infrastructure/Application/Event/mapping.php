<?php

use TiaraWonen\Infrastructure\Event\Medium\PhotoDeletedDomainEventSubscriber;
use TiaraWonen\Infrastructure\Event\Medium\PhotoOrderChangedDomainEventSubscriber;
use TiaraWonen\Infrastructure\Event\Medium\PhotoUploadedDomainEventSubscriber;

$getMapping = static function () {
    return [
        PhotoDeletedDomainEventSubscriber::class,
        PhotoOrderChangedDomainEventSubscriber::class,
        PhotoUploadedDomainEventSubscriber::class,
    ];
};
