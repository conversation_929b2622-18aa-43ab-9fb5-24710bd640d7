<?php

namespace SharedKernel\Infrastructure;

class EnvironmentSingleton
{
    /**
     * @var EnvironmentSingleton
     */
    private static $instance;
    private $environment;

    public static function instance($environment = null)
    {
        if (null === static::$instance) {
            static::$instance = new self($environment);
        }

        return static::$instance;
    }

    private function __construct($environment)
    {
        $this->environment = $environment;
    }

    public function getEnvironment()
    {
        return $this->environment;
    }

    public function __clone()
    {
        throw new \BadMethodCallException('Clone is not supported');
    }
}
