<?php

namespace SharedKernel\Application\Service;

class EncryptionService
{
    private $key;
    private $iv;

    public function __construct()
    {
        global $omniboxx_base_dir;
        $configFilePath = $omniboxx_base_dir . '/encryption_config.ini';
        $config = @parse_ini_file($configFilePath);

        if ($config === false || empty($config['encryption_key']) || empty($config['initialization_vector'])) {
            throw new \RuntimeException('Failed to load encryption configuration');
        }

        $this->key = $config['encryption_key'];
        $this->iv = $config['initialization_vector'];
    }

    public function encrypt($data)
    {
        $encryptedData = openssl_encrypt($data, 'aes-256-cbc', $this->key, 0, $this->iv);

        if ($encryptedData === false) {
            throw new \RuntimeException('Failed to encrypt data');
        }

        return $encryptedData;
    }

    public function decrypt($data)
    {
        if (!$data) {
            return null;
        }

        $decryptedData = openssl_decrypt($data, 'aes-256-cbc', $this->key, 0, $this->iv);

        if ($decryptedData === false) {
            throw new \RuntimeException('Failed to decrypt data');
        }

        return $decryptedData;
    }

    public static function generateNewKeyFile($filename)
    {
        if (!$filename) {
            throw new \RuntimeException('Filename not provided');
        }

        global $site_base_dir;
        $configFilePath = $site_base_dir . '/' . $filename;

        $key = openssl_random_pseudo_bytes(32);
        $iv = openssl_random_pseudo_bytes(16);

        $config = [
            'encryption_key' =>$key,
            'initialization_vector' => $iv
        ];

        $fileContent = '';
        foreach ($config as $key => $value) {
            $fileContent .= "$key = \"$value\" \n";
        }

        file_put_contents($configFilePath, $fileContent);
    }
}
