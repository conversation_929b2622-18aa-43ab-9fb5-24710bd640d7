<?php

namespace SharedKernel\Application\Service;

use SharedKernel\Infrastructure\PIDLockClaimTimeoutException;

include 'library/PIDLock.php';

class PIDLockedApplicationService implements ApplicationService
{
	private $applicationService;
	private $PIDLockName;
	private $PIDLock;

	public function __construct(ApplicationService $applicationService, $PIDLockName)
	{
		$this->applicationService = $applicationService;
		$this->setPIDLockName($PIDLockName);
		$this->PIDLock = new \PIDLockStatus($PIDLockName);
	}

	private function setPIDLockName($PIDLockName)
	{
		if (!trim($PIDLockName)) {
			throw new \InvalidArgumentException("PIDLock name cannot be empty.");
		}
		$this->PIDLockName = $PIDLockName;
	}

	/**
	 * @param $request
	 * @return mixed
	 * @throws PIDLockClaimTimeoutException
	 */
	public function execute($request)
	{
		try {
			$this->claimPidLock();
			return $this->applicationService->execute($request);
		} catch (PIDLockClaimTimeoutException $e) {
			throw $e;
		} finally {
			$this->releasePidLock();
		}
	}

	/**
	 * @throws PIDLockClaimTimeoutException
	 */
	private function claimPidLock()
	{
		if (is_cli_call()) {
			$this->waitUntilPIDLockIsFree();

			$this->PIDLock->setLock();
		}
	}

	/**
	 * @throws PIDLockClaimTimeoutException
	 */
	private function waitUntilPIDLockIsFree()
	{
		$maxWakeupStamp = time() + (60 * 10);
		while ($this->PIDLock->isLocked() && (time() < $maxWakeupStamp)) {
			sleep(5);
		}

		if ($this->PIDLock->isLocked() && (time() >= $maxWakeupStamp)) {
			throw new PIDLockClaimTimeoutException('PIDlock was locked for > 10 minutes, 
				failed to update availability.');
		}
	}

	private function releasePidLock()
	{
		if (is_cli_call() && $this->PIDLock->isLocked()) {
			$this->PIDLock->remove();
		}
	}
}
