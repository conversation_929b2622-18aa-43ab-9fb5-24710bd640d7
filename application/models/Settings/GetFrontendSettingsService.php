<?php


namespace Settings;


class GetFrontendSettingsService
{

    /**
     * @var \Settings
     */
    private $settings;
    /**
     * @var SettingsToPublicSettingsFactory
     */
    private $settingsToPublicSettingsFactory;

    private $publicSettingsKeyList = [
        'autocomplete_url',
        'login_background_images',
        'general_company_shortname',
        'disable_iban_and_bic_validation',
        'modules_tenantLogin',
        'modules_tenantLogin_firstnamebasis',
        'modules_tenantLogin_hide_collection_type',
        'software_type',
        'extra_tentant_invoice_options',
        'modules_rental',
        'software_country',
        'modules_contracts_default_notice_period',
        'modules_contracts_disable_object_edit',
        'modules_rental_contract_deals',
        'modules_service_charges_enabled',
        'investor_provision_period_date',
        'financial_export_cost_carrier_input',
        'uo_disable_date_validation',
        'object_ratesheet_override_enabled',
        'object_deposit_enabled',
        'modules_app_alert',
        'inspection_new_version',
        'financial_export_system',
        'for_third_party',
        'invoice_post_amount',
        'ratesheet_amounts_enabled',
        'search_default_soundex',
        'dashboard_counters_until_deadline',
        'modules_rental_text_fields_wysiwyg',
        'financial_tax_half_rate_enabled',
        'modules_rental_workflow_deals',
        'default_make_payment_on_adding_purchase',
        'software_french_enabled'
    ];

    public function __construct()
    {
        $this->settings = new \Settings();
        $this->settingsToPublicSettingsFactory = new SettingsToPublicSettingsFactory();
    }

    public function execute()
    {
        $settingsData = $this->settings->getSessionStorage();
        
        $publicSettings = $this->settingsToPublicSettingsFactory->build($settingsData, $this->publicSettingsKeyList);

        return $publicSettings;
    }
}
