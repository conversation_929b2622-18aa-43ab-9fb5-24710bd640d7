<?php


namespace Settings;


class GetApiSettingsService
{
    /**
     * @var \Settings
     */
    private $settings;
    /**
     * @var SettingsToPublicSettingsFactory
     */
    private $settingsToPublicSettingsFactory;

    private $publicSettingsKeyList = [
        'end_end_inspection_nl',
        'end_end_inspection_en',
        'end_end_inspection_fr',
        'end_first_inspection_nl',
        'end_first_inspection_en',
        'end_first_inspection_fr',
        'intro_pre_end_inspection_nl',
        'intro_pre_end_inspection_en',
        'intro_pre_end_inspection_fr',
        //huurders-app:
        'modules_tenantLogin_hide_email_box',
        'modules_tenantLogin_hide_complaints',
        'general_company',
        'general_phone',
        'general_email',
        'general_website',
        'general_address_street',
        'general_address_number',
        'general_address_zipcode',
        'general_address_city'
    ];

    public function __construct()
    {
        $this->settings = new \Settings();
        $this->settingsToPublicSettingsFactory = new SettingsToPublicSettingsFactory();
    }

    public function execute()
    {
        $settingsData = $this->settings->getSessionStorage();

        $publicSettings = $this->settingsToPublicSettingsFactory->build($settingsData, $this->publicSettingsKeyList);

        return $publicSettings;
    }
}
