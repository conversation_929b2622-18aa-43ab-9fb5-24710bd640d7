<?php


namespace Settings;


class SettingsToPublicSettingsFactory
{

    public function build($settingsData, $publicSettingsKeyList)
    {
        $publicSettings = [];

        foreach ($publicSettingsKeyList as $settingsKey) {
            if (array_key_exists($settingsKey, $settingsData)) {
                $publicSettings[$settingsKey] = $settingsData[$settingsKey];
            }
        }

        return $publicSettings;
    }
}
