<?php

namespace Account;

use SharedKernel\Infrastructure\Environment;
use SharedKernel\Infrastructure\EnvironmentSingleton;

class NotifyUserLoginFromNewDeviceAndLocationService
{
    private $userLoginModel;
    private $userModel;

    public function __construct()
    {
        $this->userLoginModel = new \UserLogin();
        $this->userModel = new \Users();
    }

    public function execute($userId)
    {
        if (EnvironmentSingleton::instance()->getEnvironment() !== Environment::PRODUCTION) {
            return false;
        }

        $internalUsers = (new \Acl())->getInternalRights();
        if (!in_array(\loginManager::data()->rights, $internalUsers, true)) {
            return false; // this functionality is only for internal employees
        }

        $userLoginRow = $this->userLoginModel->fetchRow([
            'status = ?' => 'success',
            'user = ?' => $userId,
            'started >= ?' => (new \DateTime('-1 month'))->format('Y-m-d'),
            'ip = ?' => $_SERVER['REMOTE_ADDR'],
            'user_agent_hash = ?' => sha1($_SERVER['HTTP_USER_AGENT'])
        ]);

        if (null === $userLoginRow) {
            $userLoginRow = $this->userLoginModel->fetchRow([
                'status = ?' => 'success',
                'user = ?' => $userId,
                'started >= ?' => (new \DateTime('-1 month'))->format('Y-m-d'),
                'ip = ?' => $_SERVER['REMOTE_ADDR'],
                'user_agent = ?' => $_SERVER['HTTP_USER_AGENT'],
            ]);
        }

        if ($userLoginRow) {
            return false;
        }

        $userRow = $this->userModel->fetchRowById($userId);

        if (null === $userRow) {
            throw new \RuntimeException('User not found');
        }

        $name = $userRow->rendered_name ?: 'Klant';

        $emailOutgoing = new \EmailOutgoing([
            'to' => $userRow->id,
            'from' => ['name' => 'Omniboxx systeem mail', 'email' => '<EMAIL>' ],
            'subject' => 'Belangrijke beveiligingsmelding: Nieuwe inlogpoging op je account',
            'text' => "Beste {$name},

We willen je laten weten dat er zojuist is ingelogd op je account vanaf een nieuwe locatie en een nieuw apparaat. Hier zijn de details:

<b>Datum en tijd: {$userLoginRow->started}
IP-adres: {$_SERVER['REMOTE_ADDR']}
Apparaat: {$_SERVER['HTTP_USER_AGENT']}</b>

Als dit jouw inlogpoging was, kun je deze e-mail negeren. Als je deze inlogpoging niet hebt uitgevoerd, raden we je aan onmiddellijk je wachtwoord te wijzigen en contact met ons op te <NAME_EMAIL> om verdere stappen te bespreken.

Als tweefactorauthenticatie (2FA) nog niet is ingeschakeld, raden wij u aan dit te activeren om uw account beter te beveiligen. Ga naar uw account instellingen om 2FA in te stellen.

Bedankt voor je aandacht voor de beveiliging van je account.

Met vriendelijke groet,

Klantenservice Omniboxx",
        ]);
    }
}
