<?php

namespace HBSoftware\Application;

use HBSoftware\Domain\Model\Contact\ContactRepository;
use SharedKernel\Application\Service\ApplicationService;

/**
 * Class SendContactsToHbService
 * @package HBSoftware\Application
 */
class SendContactsToHbService implements ApplicationService
{
    /**
     * @var ContactRepository
     */
    private $contactRepository;

    public function __construct(ContactRepository $contactRepository) {
        $this->contactRepository = $contactRepository;
    }

    public function execute($request) {
        if (\Settings::get('general_company_shortname') === 'upkot') {
            error_log('SendContactsToHbService Disabled for Upgrade');
            return;
        }


        $this->contactRepository->addAllContacts();
    }
}
