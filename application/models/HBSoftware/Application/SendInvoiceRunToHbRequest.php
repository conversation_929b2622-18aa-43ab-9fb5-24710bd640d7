<?php

namespace HBSoftware\Application;

/**
 * Class SendInvoiceRunToHbRequest
 * @package HBSoftware\Application
 */
class SendInvoiceRunToHbRequest
{
    /**
     * @var int
     */
    private $invoiceRunId;

    /**
     * @var bool
     */
    private $invoiceIsCustomInvoice;

    /**
     * SendInvoiceRunToHbRequest constructor.
     * @param $invoiceRunId
     * @param bool $invoiceIsCustomInvoice
     */
    public function __construct($invoiceRunId, $invoiceIsCustomInvoice = false) {
        $this->invoiceRunId = $invoiceRunId;
        $this->invoiceIsCustomInvoice = $invoiceIsCustomInvoice;
    }

    /**
     * @return int
     */
    public function getInvoiceRunId() {
        return $this->invoiceRunId;
    }

    /**
     * @return bool
     */
    public function getInvoiceIsCustomInvoice() {
        return $this->invoiceIsCustomInvoice;
    }
}
