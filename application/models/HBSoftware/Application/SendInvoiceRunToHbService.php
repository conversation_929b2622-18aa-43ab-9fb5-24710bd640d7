<?php

namespace HBSoftware\Application;

use HBSoftware\Domain\Model\Invoice\InvoiceRepository;
use HBSoftware\Domain\Factory\InvoiceLine\InvoiceLineFactory;
use HBSoftware\Domain\Factory\Invoice\InvoiceFactory;
use HBSoftware\Domain\Model\Invoice\SendInvoiceLineFailedException;
use HBSoftware\Infrastructure\Service\Invoice\GetInvoiceDataService;
use HBSoftware\Infrastructure\Service\Invoice\HbInvoiceErrorLogger;
use HBSoftware\Infrastructure\Service\Invoice\SaveInvoiceFinancialIdService;
use Monolog\Logger;
use GuzzleHttp\Psr7\Response;
use Psr\Http\Message\ResponseInterface;

class SendInvoiceRunToHbService
{
    /**
     * @var InvoiceRepository
     */
    private $invoiceRepository;

    /**
     * @var InvoiceFactory
     */
    private $invoiceFactory;

    /**
     * @var GetInvoiceDataService
     */
    private $getInvoiceDataService;

    /**
     * @var InvoiceLineFactory
     */
    private $invoiceLineFactory;

    /**
     * @var SaveInvoiceFinancialIdService
     */
    private $saveInvoiceFinancialIdService;

    /**
     * @var HbInvoiceErrorLogger
     */
    private $hbInvoiceErrorLogger;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * SendInvoiceRunToHbService constructor.
     * @param InvoiceRepository $invoiceRepository
     * @param InvoiceFactory $invoiceFactory
     * @param GetInvoiceDataService $getInvoiceDataService
     * @param InvoiceLineFactory $invoiceLineFactory
     * @param SaveInvoiceFinancialIdService $saveInvoiceFinancialIdService
     * @param HbInvoiceErrorLogger $hbInvoiceErrorLogger
     * @param Logger $logger
     */
    public function __construct(
        InvoiceRepository $invoiceRepository,
        InvoiceFactory $invoiceFactory,
        GetInvoiceDataService $getInvoiceDataService,
        InvoiceLineFactory $invoiceLineFactory,
        SaveInvoiceFinancialIdService $saveInvoiceFinancialIdService,
        HbInvoiceErrorLogger $hbInvoiceErrorLogger,
        Logger $logger
    ) {
        $this->invoiceRepository = $invoiceRepository;
        $this->invoiceFactory = $invoiceFactory;
        $this->invoiceLineFactory = $invoiceLineFactory;
        $this->getInvoiceDataService = $getInvoiceDataService;
        $this->saveInvoiceFinancialIdService = $saveInvoiceFinancialIdService;
        $this->hbInvoiceErrorLogger = $hbInvoiceErrorLogger;
        $this->logger = $logger;
    }

    /**
     * @param SendInvoiceRunToHbRequest $request
     * @return ResponseInterface[]|false
     */
    public function execute(SendInvoiceRunToHbRequest $request) {

        if (\Settings::get('general_company_shortname') === 'upkot') {
            error_log('SendInvoiceRunToHbService Disabled for Upgrade');
            return;
        }

        $invoiceRunId = $request->getInvoiceRunId();
        $responses = [];
        try {
            $invoices = $this->getInvoiceDataService->getInvoicesByRun($invoiceRunId);
        } catch (\DomainException $exception) {
            $this->logger->addError($exception->getMessage());
            return false;
        }
        foreach ($invoices as $invoice) {
            $responses[] = $this->addInvoiceByInvoiceId($invoice['id'], $request->getInvoiceIsCustomInvoice());
        }
        return $responses;
    }

    /**
     * @param int|string $id
     * @param bool $invoiceIsCustomInvoice
     * @return Response[]|array|false
     */
    public function addInvoiceByInvoiceId($id, $invoiceIsCustomInvoice) {
        $responses = [];
        try {
            $invoiceData = $this->getInvoiceDataService->getById($id);
        } catch (\DomainException $e) {
            $this->logger->addError(
                'Domain exception for Invoice ID '.  $id . ': ' . $e->getMessage()
            );
            $this->hbInvoiceErrorLogger->logInvoiceError($id, $e->getMessage(), $invoiceIsCustomInvoice);
            return false;
        } catch (\Zend_Db_Statement_Exception $e) {
            $this->logger->addError(
                'Zend exception for Invoice ID '.  $id . ': ' . $e->getMessage()
            );
            $this->hbInvoiceErrorLogger->logInvoiceError($id, $e->getMessage(), $invoiceIsCustomInvoice);
            return false;
        }
        if (isset($invoiceData['invoice']['financial_invoice_id']) &&
            !empty($invoiceData['invoice']['financial_invoice_id'])) {
            return false;
        }

        $invoiceHeader = $this->invoiceFactory->build($invoiceData);
        $company = $invoiceData['corporation']['administration'];
        $response = $this->invoiceRepository->addInvoice($invoiceHeader, $company);
        if (!$response) {
            return false;
        }
        $responses[] = $response;

        $invoiceLines = [];
        $count = 1;
        foreach ($invoiceData['rows'] as $row) {
            $invoiceLine = $this->invoiceLineFactory->build($row);
            $invoiceLines[$count . '0000'] = $invoiceLine;
            $count++;
            try {
                $responses[] = $this->invoiceRepository->addInvoiceLine(
                    $invoiceHeader,
                    $invoiceLine,
                    $company,
                    $row['id']
                );

                $this->saveInvoiceFinancialIdService->saveFinancialIdToInvoice($id, $id);
            } catch (SendInvoiceLineFailedException $e) {
                $this->logger->addError(
                    'Invoice line ' . $row['id'] . ' failed to send, reverting invoice ' . $id
                );
                $responses[] = $this->invoiceRepository->removeInvoice($invoiceHeader, $invoiceLines, $company);
                $this->hbInvoiceErrorLogger->logInvoiceError($id, $e->getMessage(), $invoiceIsCustomInvoice);
                return $responses;
            }
        }

        return $responses;
    }
}
