<?php

namespace HBSoftware\Application\Domain;

use DI\Container;
use DI\DependencyException;
use DI\NotFoundException;
use HBSoftware\Infrastructure\Service\Helpers\GetHbSettingsService;
use HBSoftware\Infrastructure\Service\NavAPI;
use HBSoftware\Infrastructure\Service\NavAPIDebug;
use Monolog\Logger;

/**
 * Class NavApiService
 * @package Omniboxx\Services
 */
abstract class NavApiService
{
	/**
	 * @var NavAPI
	 */
	protected $api;

    /**
     * @var Logger
     */
	protected $logger;

	/**
	 * NavApiService constructor.
     *
     * @param Container $container
     *   Container used to get the logger.
     *
     * @throws \InvalidArgumentException
     * @throws DependencyException
     * @throws NotFoundException
	 */
	public function __construct(Container $container) {
	    $this->logger = $container->get('HBLogger');
	    global $config;
	    if ($config->get('app')->get('environment') === 'development') {
	        $this->api = new NavAPIDebug(new GetHbSettingsService());
        } else {
            $this->api = new NavAPI(new GetHbSettingsService());
        }
	}
}
