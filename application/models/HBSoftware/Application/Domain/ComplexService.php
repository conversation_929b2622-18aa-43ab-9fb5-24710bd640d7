<?php

namespace HBSoftware\Application\Domain;

use HBSoftware\Domain\Model\Complex\Complex;
use Psr\Http\Message\ResponseInterface;

/**
 * Class ComplexService
 */
class ComplexService extends NavApiService
{
    /**
     * Creates a database record for a Complex object.
     *
     * @param Complex $complex
     *   Complex object.
     * @param string $company
     *   HB Company ID
	 * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createComplex(Complex $complex, $company) {
        $complex->validate();
        return $this->api->insertRow($complex->jsonSerialize(), 'OmniboxxComplex', $company);
    }

    /**
     * Fetch the contents of the Complex table.
     * @param string $company
     *   HB Company ID
     * @return array|false
     *   The decoded JSON response.
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getComplexes($company) {
        return $this->api->getTable('OmniboxxComplex', $company);
    }

	/**
	 * @param Complex $complex
     * @param string $company
     *   HB Company ID
     * @return false|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
    public function updateComplex(Complex $complex, $company) {
    	if ($complex->getETag() === null) {
			$complex->setETag($this->getComplexEtag($complex, $company));
		}
    	return $this->api->updateEntity(
    		$complex->jsonSerialize(),
			'OmniboxxComplex',
			$company,
			$complex->getETag(),
			$complex->getNr()
		);
	}

	/**
	 * @param Complex $complex
     * @param string $company
     *   HB Company ID
     * @return mixed
	 * @throws \OutOfBoundsException
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
    public function getComplexEtag(Complex $complex, $company) {
    	$complexId = $complex->getNr();
    	$response = $this->api->getEntity($complexId, $company, 'OmniboxxComplex');
    	$responseArray = json_decode($response->getBody()->getContents(), true);
    	if (!isset($responseArray['ETag'])) {
    		throw new \OutOfBoundsException('ETag key not found in json response');
		}
    	return $responseArray['ETag'];
	}
}
