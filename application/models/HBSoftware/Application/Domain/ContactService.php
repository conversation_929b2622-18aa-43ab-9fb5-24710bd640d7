<?php

namespace HBSoftware\Application\Domain;

use GuzzleHttp\Exception\GuzzleException;
use HBSoftware\Domain\Model\Contact\Contact;
use Psr\Http\Message\ResponseInterface;

/**
 * Class ContactService
 * @package Omniboxx
 */
class ContactService extends NavApiService
{
    /**
     * @param Contact $contact
     * @param string $company
     *   HB Company ID
     * @return ResponseInterface|false
     */
    public function createContact(Contact $contact, $company) {
        $contact->validate();
        try {
            $response = $this->api->getEntity($contact->getNo(), $company, 'OmniboxxContact');
            if ($response->getStatusCode() === 404) {
                return $this->api->insertRow($contact->jsonSerialize(), 'OmniboxxContact', $company);
            }
            return $this->updateContact($contact, $company);
        } catch (GuzzleException $exception) {
            $this->logger->addError('Create Contact: API Exception occured for user ' . $contact->getNo() .
            ', message: ' . $exception->getMessage());
            $this->logger->addError('Error data ::' . var_export($contact->jsonSerialize(), true));
            throw $exception;
        }
    }

    /**
     * Fetch the contents of the Contact table.
     * @param string $company
     *   HB Company ID
     *
     * @return array|false
     *   The decoded JSON response string.
     */
    public function getContacts($company) {
        try {
            return $this->api->getTable('OmniboxxContact', $company);
        } catch (GuzzleException $exception) {
            $this->logger->addError('Get Contacts: API Exception occured for company ' . $company .
                ', message: ' . $exception->getMessage());
        }
    }

	/**
	 * @param Contact $contact
     * @param string $company
     *   HB Company ID
     * @return false|ResponseInterface
	 */
	public function updateContact(Contact $contact, $company) {
	    try {
            if ($contact->getETag() === null) {
                $contact->setETag($this->getContactETag($contact, $company));
            }
            return $this->api->updateEntity(
                $contact->jsonSerialize(),
                'OmniboxxContact',
                $company,
                $contact->getETag(),
                $contact->getNo()
            );
        } catch (GuzzleException $exception) {
            $this->logger->addError('Update Contact: API Exception occured for user ' . $contact->getNo() .
                ', message: ' . $exception->getMessage());
            $this->logger->addError('Error data ::' . var_export($contact->jsonSerialize(), true));
            return false;
        }
	}

	/**
	 * @param Contact $contact
     * @param string $company
     *   HB Company ID
     * @return mixed
	 * @throws \OutOfBoundsException
     * @throws GuzzleException
	 */
	protected function getContactETag(Contact $contact, $company) {
		$contactId = $contact->getNo();
		$response = $this->api->getEntity($contactId, $company, 'OmniboxxContact');
		$responseArray = json_decode($response->getBody()->getContents(), true);
		if (!isset($responseArray['ETag'])) {
			throw new \OutOfBoundsException('ETag key not found in json response');
		}
		return $responseArray['ETag'];
	}
}
