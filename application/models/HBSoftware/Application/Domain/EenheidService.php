<?php

namespace HBSoftware\Application\Domain;

use HBSoftware\Domain\Model\Eenheid\Eenheid;
use Psr\Http\Message\ResponseInterface;

/**
 * Class EenheidService
 * @package Omniboxx\Services
 */
class EenheidService extends NavApiService
{
    /**
     * Creates a database record for a Eenheid object.
     *
     * @param Eenheid $eenheid
     *   Eenheid object.
     * @param string $company
     *   HB Company ID
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createEenheid(Eenheid $eenheid, $company) {
        $eenheid->validate();
        $this->api->insertRow($eenheid->jsonSerialize(), 'OmniboxxEenheid', $company);
    }

    /**
     * Fetch the contents of the Eenheid table.
     * @param string $company
     *   HB Company ID
     *
     * @return array|false
     *   The decoded JSON response.
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getEenheiden($company) {
        return $this->api->getTable('OmniboxxComplex', $company);
    }

	/**
	 * @param Eenheid $eenheid
     * @param string $company
     *   HB Company ID
     * @return false|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function updateEenheid(Eenheid $eenheid, $company) {
		if ($eenheid->getETag() === null) {
			$eenheid->setETag($this->getEenheidETag($eenheid, $company));
		}
		return $this->api->updateEntity(
			$eenheid->jsonSerialize(),
			'OmniboxxEenheid',
			$company,
			$eenheid->getETag(),
			$eenheid->getNr()
		);
	}

	/**
	 * @param Eenheid $eenheid
     * @param string $company
     *   HB Company ID
     * @return mixed
	 * @throws \OutOfBoundsException
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function getEenheidETag(Eenheid $eenheid, $company) {
		$eenheidId = $eenheid->getNr();
		$response = $this->api->getEntity($eenheidId, $company, 'OmniboxxEenheid');
		$responseArray = json_decode($response->getBody()->getContents(), true);
		if (!isset($responseArray['ETag'])) {
			throw new \OutOfBoundsException('ETag key not found in json response');
		}
		return $responseArray['ETag'];
	}
}
