<?php

namespace HBSoftware\Application\Domain;

use HBSoftware\Domain\Model\Invoice\Invoice;
use Psr\Http\Message\ResponseInterface;

/**
 * Class InvoiceService
 * @package Omniboxx
 */
class InvoiceService extends NavApiService
{
    /**
     * @param Invoice $invoice
     * @param string $company
     *   HB Company ID
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createInvoice(Invoice $invoice, $company) {
        $invoice->validate();
        return $this->api->insertRow(
        	$invoice->jsonSerialize(),
			'OmniboxxInvoice',
			$company
		);
    }

    /**
     * Fetch the contents of the Invoice table.
     * @param string $company
     *   HB Company ID
     *
     * @return array|false
     *   The decoded JSON response string.
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getInvoices($company) {
        return $this->api->getTable('OmniboxxInvoice', $company);
    }

	/**
	 * @param Invoice $invoice
     * @param string $company
     *   HB Company ID
     * @return false|ResponseInterface
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function updateInvoice(Invoice $invoice, $company) {
		if ($invoice->getETag() === null) {
			$invoice->setETag($this->getInvoiceETag($invoice, $company));
		}
		return $this->api->updateEntity(
			$invoice->jsonSerialize(),
			'OmniboxxInvoice',
			$company,
			$invoice->getETag(),
			$invoice->getNo()
		);
	}

    /**
     * @param Invoice $invoice
     * @param $company
     * @return false|\GuzzleHttp\Psr7\Response|mixed|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
	public function deleteInvoice(Invoice $invoice, $company) {
	    $entityParams = 'Document_Type=\'Invoice\',No=\'' . $invoice->getNo() . '\'';
        return $this->api->deleteEntity(
            $company,
            'OmniboxxInvoice',
            $entityParams
        );
    }

	/**
	 * @param Invoice $invoice
     * @param string $company
     *   HB Company ID
     * @return mixed
	 * @throws \OutOfBoundsException
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function getInvoiceETag(Invoice $invoice, $company) {
		$invoiceId = $invoice->getNo();
		$response = $this->api->getEntity($invoiceId, $company, 'OmniboxxInvoice');
		$responseArray = json_decode($response->getBody()->getContents(), true);
		if (!isset($responseArray['ETag'])) {
			throw new \OutOfBoundsException('ETag key not found in json response');
		}
		return $responseArray['ETag'];
	}
}
