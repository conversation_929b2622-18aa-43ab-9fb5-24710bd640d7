<?php

namespace HBSoftware\Application\Domain;

use HBSoftware\Domain\Model\InvoiceLine\InvoiceLine;
use Psr\Http\Message\ResponseInterface;

/**
 * Class InvoiceLineService
 * @package Omniboxx
 */
class InvoiceLineService extends NavApiService
{
    /**
     * @param InvoiceLine $invoiceLine
     * @param string $company
     *   HB Company ID
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createInvoiceLine(InvoiceLine $invoiceLine, $company) {
        $invoiceLine->validate();
        return $this->api->insertRow($invoiceLine->jsonSerialize(), 'OmniboxxInvoiceLine', $company);
    }

    /**
     * Fetch the contents of the InvoiceLine table.
     * @param string $company
     *   HB Company ID
     *
     * @return array|false
     *   The decoded JSON response string.
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getInvoiceLines($company) {
        return $this->api->getTable('OmniboxxInvoiceLine', $company);
    }

	/**
	 * @param InvoiceLine $invoiceLine
     * @param string $company
     *   HB Company ID
     * @return false|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function updateInvoiceLine(InvoiceLine $invoiceLine, $company) {
		if ($invoiceLine->getETag() === null) {
			$invoiceLine->setETag($this->getInvoiceLineETag($invoiceLine, $company));
		}
		return $this->api->updateEntity(
			$invoiceLine->jsonSerialize(),
			'OmniboxxInvoiceLine',
			$company,
			$invoiceLine->getETag(),
			$invoiceLine->getNo()
		);
	}

    /**
     * @param InvoiceLine $invoiceLine
     * @param $company
     * @return false|\GuzzleHttp\Psr7\Response|mixed|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
	public function deleteInvoiceLine(InvoiceLine $invoiceLine, $company, $Line_No) {
	    $entityParams = 'Document_Type=\'Invoice\',' .
            'Document_No=\'' . $invoiceLine->getDocumentNo() . '\', ' .
            'Line_No=\'' . $Line_No . '\'';
	    return $this->api->deleteEntity(
            $company,
            'OmniboxxInvoiceLine',
            $entityParams
        );
    }

	/**
	 * @param InvoiceLine $invoiceLine
     * @param string $company
     *   HB Company ID
     * @return mixed
	 * @throws \OutOfBoundsException
     * @throws \GuzzleHttp\Exception\GuzzleException
	 */
	public function getInvoiceLineETag(InvoiceLine $invoiceLine, $company) {
		$invoiceLineId = $invoiceLine->getNo();
		$response = $this->api->getEntity($invoiceLineId, $company, 'OmniboxxInvoiceLine');
		$responseArray = json_decode($response->getBody()->getContents(), true);
		if (!isset($responseArray['ETag'])) {
			throw new \OutOfBoundsException('ETag key not found in json response');
		}
		return $responseArray['ETag'];
	}
}
