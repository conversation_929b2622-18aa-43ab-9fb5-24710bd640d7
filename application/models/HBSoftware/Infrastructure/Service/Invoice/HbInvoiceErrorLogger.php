<?php

namespace HBSoftware\Infrastructure\Service\Invoice;

/**
 * Class HbInvoiceErrorLogger
 * @package HBSoftware\Infrastructure\Service\Invoice
 */
class HbInvoiceErrorLogger
{
    /** @var \Invoices */
    private $invoices;

    /**
     * YukiErrorLogger constructor.
     * @param \Invoices $invoices
     */
    public function __construct(\Invoices $invoices)
    {
        $this->invoices = $invoices;
    }

    /**
     * Logs a financial export system error on given invoices to be displayed in the overview.
     *
     * @param int $invoiceId
     *   Invoice IDs to log the error on.
     * @param string $message
     *   Error message.
     * @param boolean $custom
     */
    public function logInvoiceError($invoiceId, $message, $custom = false)
    {
        $invoice = $this->invoices->getById($invoiceId);
        if ($invoice !== null) {
            $invoice->financial_error_message = json_encode(['message' => $message]);
            $invoice->save();
        }
    }
}
