<?php

namespace HBSoftware\Infrastructure\Service\Invoice;

use Invoices;
use InvoicesCustoms;

/**
 * Class SaveInvoiceFinancialIdService
 * @package HBSoftware\Infrastructure\Service\Invoice
 */
class SaveInvoiceFinancialIdService
{
    /**
     * @var Invoices
     */
    private $invoiceModel;

    /**
     * @var InvoicesCustoms
     */
    private $invoiceCustomModel;

    public function __construct(Invoices $invoiceModel, InvoicesCustoms $invoiceCustomModel)
    {
        $this->invoiceModel = $invoiceModel;
        $this->invoiceCustomModel = $invoiceCustomModel;
    }

    /**
     * @param $invoiceId
     * @param $financialInvoiceId
     */
    public function saveFinancialIdToInvoice($invoiceId, $financialInvoiceId) {
        $invoice = $this->invoiceModel->getById($invoiceId);
        if ($invoice === null) {
            throw new \InvalidArgumentException('Invoice with ID ' . $invoiceId . ' not found');

        }
        $invoice->financial_invoice_id = $financialInvoiceId;
        $invoice->save();

        if ($invoice->custom == 1) {
            $customInvoice = $this->invoiceCustomModel->getById($invoice->customid);
            if ($customInvoice !== null) {
                $customInvoice->financial_invoice_id = $financialInvoiceId;
                $customInvoice->save();
            }
        }
    }
}
