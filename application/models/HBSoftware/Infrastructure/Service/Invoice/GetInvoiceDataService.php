<?php

namespace HBSoftware\Infrastructure\Service\Invoice;

use Controllers\Invoice\FinancialExport;
use HBSoftware\Infrastructure\Service\Contact\GetUserDataService;
use Invoices;
use Invoice;
use InvoicesCustoms;
use InvoicesRuns;
use InvoiceRows;
use Objectusers;
use Objects;
use Corporations;
use Projects;
use Investors;
use User;

/**
 * Class GetInvoiceDataService
 */
class GetInvoiceDataService implements GetInvoiceDataServiceInterface
{
    const TYPE_CUSTOM = 1;
    const TYPE_LEDGER = 2;

    /**
     * @var Invoices
     */
    private $invoiceModel;

    /**
     * @var InvoicesCustoms
     */
    private $invoiceCustomModel;

    /**
     * @var InvoicesRuns
     */
    private $invoicesRunsModel;

    /**
     * @var Invoice
     */
    private $invoiceLibrary;

    /**
     * @var Objectusers
     */
    private $objectUsersModel;

    /**
     * @var Objects
     */
    private $objectsModel;

    /**
     * @var GetUserDataService
     */
    private $getUserDataService;

    /**
     * @var InvoiceRows
     */
    private $invoiceRowModel;

    /**
     * @var Corporations
     */
    private $corporationsModel;

    /**
     * @var Projects
     */
    private $projectsModel;

    /**
     * @var FinancialExport
     */
    private $financialExportModel;

    /**
     * @var Investors
     */
    private $investorModel;

    /**
     * @var User
     */
    private $userLibrary;

    /**
     * GetInvoiceDataService constructor.
     * @param Invoices $invoiceModel
     * @param InvoicesCustoms $invoiceCustomModel
     * @param InvoicesRuns $invoicesRunsModel
     * @param Invoice $invoiceLibrary
     * @param Objectusers $objectUsersModel
     * @param Objects $objectsModel
     * @param GetUserDataService $getUserDataService
     * @param InvoiceRows $invoiceRowModel
     * @param Corporations $corporationsModel
     * @param Projects $projectsModel
     * @param FinancialExport $financialExportModel
     * @param Investors $investorModel
     * @param User $userLibrary
     */
    public function __construct(
        Invoices $invoiceModel,
        InvoicesCustoms $invoiceCustomModel,
        InvoicesRuns $invoicesRunsModel,
        Invoice $invoiceLibrary,
        Objectusers $objectUsersModel,
        Objects $objectsModel,
        GetUserDataService $getUserDataService,
        InvoiceRows $invoiceRowModel,
        Corporations $corporationsModel,
        Projects $projectsModel,
        FinancialExport $financialExportModel,
        Investors $investorModel,
        User $userLibrary
    ) {
        $this->invoiceModel = $invoiceModel;
        $this->invoiceCustomModel = $invoiceCustomModel;
        $this->invoicesRunsModel = $invoicesRunsModel;
        $this->invoiceLibrary = $invoiceLibrary;
        $this->objectUsersModel = $objectUsersModel;
        $this->objectsModel = $objectsModel;
        $this->getUserDataService = $getUserDataService;
        $this->invoiceRowModel = $invoiceRowModel;
        $this->corporationsModel = $corporationsModel;
        $this->projectsModel = $projectsModel;
        $this->financialExportModel = $financialExportModel;
        $this->investorModel = $investorModel;
        $this->userLibrary = $userLibrary;
    }

    /**
     * @param $id
     *
     * @return array
     * @throws \Zend_Db_Statement_Exception
     */
    public function getById($id) {
        $invoice = [];
        $invoiceData = $this->invoiceModel->getById($id);

        if (!$invoiceData) {
            throw new \DomainException('Invoice with ID ' . $id . ' not found');
        }
        $runData = $this->invoicesRunsModel->getForInvoice($id);
        if (!$runData) {
            throw new \DomainException('Run not found for invoice with ID ' . $id);
        }

        $corporationData = $this->corporationsModel->getById($runData['corporation_id']);
        if (!$corporationData) {
            throw new \DomainException('Corporation not found for invoice with ID ' . $id);
        }

        if ($invoiceData->custom == 1) {
            $customInvoiceData = $this->invoiceCustomModel->getById($invoiceData->customid);

            if ($customInvoiceData === null) {
                throw new \DomainException('Custom invoice data not found for invoice ' . $id);
            }
            $customerId = $customInvoiceData->user ?: $this->investorModel->getUserId($customInvoiceData->investor);
            if ($customerId === null) {
                throw new \DomainException('Huurder of belegger niet gevonden bij handmatig factuur met intern nummer '
                    . $invoiceData->customid);
            }

            if ($customInvoiceData->object) {
                $objectData = $this->objectsModel->getById($customInvoiceData->object);
            } else {
                $objectData = null;
            }

            $project = $this->projectsModel->getById($customInvoiceData->project);
        } else {
            $objectUsersData = $this->objectUsersModel->get($invoiceData->users_objects);
            if (!$objectUsersData) {
                throw new \DomainException('Objectkoppeling niet gevonden voor factuur met intern ID ' . $id);
            }
            $customerId = $objectUsersData->customer;
            $objectData = $this->objectsModel->getById($objectUsersData->object);
            $projectId = $objectData->objectgroup->project;
            $project = $this->projectsModel->getById($projectId);
        }

        if ($project === null) {
            throw new \DomainException(
                'Project niet gevonden voor gebruiker ' . $customerId .
                ' (' . $objectUsersData->customer->name . '), invoice ' . $id
            );
        }
        if ($project->external_id === null) {
            throw new \DomainException(
                'Navision ID niet gevonden voor project ' . $project->id . ' (' . $project->name . ')'
            );
        }

        $invoice['project'] = $project->toArray();


//        if ($objectData === null) {
//            throw new \DomainException('Gegeven object niet gevonden voor factuur met intern factuurnummer ID ' . $id);
//        }

        if ($customerId === null) {
            if (!empty($invoiceData['investor'])) {
                $customerId = $this->investorModel->getUserId($invoiceData['investor']);
            } else {
                throw new \DomainException('Geen klantid of beleggerid gevonden voor factuur met intern nummer ' . $id);
            }
        }

        $invoice['invoiceType'] = $invoiceData->custom === self::TYPE_CUSTOM ? self::TYPE_CUSTOM : self::TYPE_LEDGER;
        $invoice['invoice'] = $invoiceData->toArray();
        $invoice['invoiceRun'] = $runData;
        $invoice['customer'] = $this->getUserDataService->getUserDataByUserId($customerId);
        $invoice['object'] = $objectData->toArray() ?: null;

        if ($invoiceData->custom) {
            $invoiceCustomData = $this->invoiceCustomModel->getById($invoiceData->customid);
            if ($invoiceCustomData === null) {
                throw new \DomainException(
                    'Geen handmatige factuurgegevens gevonden handmatig factuur met intern factuurnummer ' . $id .
                    ', en intern handmatig factuurnummer ' . $invoiceData->customid
                );
            }
            $invoice['invoice']['postingDescription'] = $invoiceCustomData->description;
        } else {
            $language = $invoice['customer']['general']['language'];

            $locale = setlocale(LC_TIME, 0);
            switch ($language) {
                case 'en':
                    setlocale(LC_TIME, ['en_US.utf8', 'en_US.utf-8']);
                    $postingDescription = 'Invoice ' . strftime('%B %Y', strtotime($runData['start']));
                    break;
                case 'fr':
                    setlocale(LC_TIME, ['fr_BE.utf8', 'fr_BE.utf-8']);
                    $postingDescription = 'Facture ' . strftime('%B %Y', strtotime($runData['start']));
                    break;
                default:
                    setlocale(LC_TIME, ['nl_NL.utf8', 'nl_NL.utf-8']);
                    $postingDescription = 'Factuur ' . strftime('%B %Y', strtotime($runData['start']));
                    break;
            }
            $invoice['invoice']['postingDescription'] = $postingDescription;
            setlocale(LC_TIME, $locale);
        }

        $this->financialExportModel->setRun($runData['id']);
        $financialIdentifierQuery = $this->financialExportModel->getQuery()->where('i.id = ?', $id);
        $financialIdentifierData = $financialIdentifierQuery->query()->fetch();

        if (empty($financialIdentifierData['identifier'])) {
            throw new \DomainException('Geen identifier gevonden voor factuur met intern factuurnummer ' . $id);
        }
        $invoice['invoice']['financialIdentifier'] = $financialIdentifierData['identifier'];
        
        $invoice['project'] = $project->toArray();

        $invoice['invoice']['description'] = $this->invoiceLibrary->createUnformattedStructuredMessageFromDebtorCode(
            $id
        );
        $invoice['corporation'] = $corporationData->toArray();
        if (!isset($invoice['corporation']['administration']) || empty($invoice['corporation']['administration'])) {
            throw new \DomainException('Juridische entiteit heeft geen administratie-code voor factuur ' . $id);
        }
        $invoice['rows'] = $this->invoiceRowModel->genMatchSelect(['invoice' => $id])->query()->fetchAll();
        foreach ($invoice['rows'] as $key => $row) {
            if (!empty($invoice['rows'][$key]['object'])) {
                $rowObject = $this->objectsModel->getById($invoice['rows'][$key]['object']);
            } else {
                $rowObject = $this->objectsModel->getById($invoice['object']['id']);
            }
            if ($rowObject === null) {
                throw new \DomainException('Invoice object not found for invoice row ' . $row['id']);
            }
            if ($rowObject->lot === null) {
                throw new \DomainException(
                    'Kavelnummer not found for invoice row ' . $row['id'] . ', object ' . $rowObject->id
                );
            }
            $invoice['rows'][$key]['objectData'] = $rowObject->toArray();
            $invoice['rows'][$key]['external_id'] = $project->external_id;
            $invoice['rows'][$key]['financialIdentifier'] = $invoice['invoice']['financialIdentifier'];
        }
        if ((int)$invoice['corporation']['id'] === 2) {
            $invoice['invoice']['paymentPreference'] = 'INCASSO';
        } else {
            $invoice['invoice']['paymentPreference'] = $this->getUserInvoicePreference($customerId);
        }
        return $invoice;
    }

    private function getUserInvoicePreference($userId) {
        $preferences = $this->userLibrary->getInvoicePreferences($userId);
        return $preferences['type'] === 'collection' ? 'INCASSO' : 'ISABEL-BE';
    }

    /**
     * @param $runId
     * @return array
     */
    public function getInvoicesByRun($runId) {
        return $this->invoiceModel->genMatchSelect(['run' => $runId])->query()->fetchAll();
    }
}
