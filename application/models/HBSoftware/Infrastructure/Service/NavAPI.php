<?php

namespace HBSoftware\Infrastructure\Service;

use GuzzleHttp;
use HBSoftware\Infrastructure\Service\Helpers\GetHbSettingsService;

/**
 * Class NavAPI
 * @package Omniboxx
 */
class NavAPI implements NavAPIInterface
{
    const BASE_URI = 'http://194.78.245.178:7448';
    const BASE_PATH = '/UE2017-TEST/ODataV4';

    protected $basePath;
    protected $baseUri;
    protected $username;
    protected $password;

    /**
     * NavAPI constructor.
     * @param GetHbSettingsService $getHbSettingsService
     */
    public function __construct(GetHbSettingsService $getHbSettingsService) {
        $this->basePath = $getHbSettingsService->getBasePath();
        $this->baseUri = 'http://' . $getHbSettingsService->getBaseUri();
        $this->username = $getHbSettingsService->getUsername();
        $this->password = $getHbSettingsService->getPassword();
    }

    /**
     * {@inheritdoc}
     */
    public function getTable($tableName, $companyName)
	{
        $response = $this->call(
        	'GET',
			$this->basePath . '/Company(\'' . $companyName . '\')/' . $tableName,
			[]
		);
        if ($response->getStatusCode() === 200) {
            $contents = $response->getBody()->getContents();
            return json_decode($contents);
        }
        return false;
    }

    /**
     * @throws GuzzleHttp\Exception\GuzzleException
     */
    public function getCompanies() {
        $response = $this->call(
            'GET',
            $this->basePath . '/Company',
            []
        );
        if ($response->getStatusCode() === 200) {
            $contents = $response->getBody()->getContents();
            return json_decode($contents);
        }
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function insertRow($body, $tableName, $companyName)
	{
        $params = [
        	'body' => json_encode($body),
			'headers' => [
				'Content-Type' => 'application/json'
			]
		];
        $response = $this->call(
        	'POST',
			$this->basePath . '/Company(\'' . $companyName . '\')/' . $tableName,
			$params
		);
        return $response;
    }

    /**
     * {@inheritdoc}
     */
    public function getEntity($entityId, $companyName, $tableName) {
    	$params = [
    		'Content-Type' => 'application/json'
		];

		return $this->call(
			'GET',
			$this->basePath . '/Company(\'' . $companyName . '\')/' . $tableName . '(\'' . $entityId . '\')',
			$params
		);
	}

    /**
     * {@inheritdoc}
     */
    public function updateEntity($body, $tableName, $companyName, $entityEtag, $entityId) {
    	$encodedEtag = base64_encode('\'' . $entityEtag . '\'');
    	$params = [
    		'body' => json_encode($body),
			'headers' => [
				'Accept' => 'application/json',
				'Content-Type' => 'application/json',
				'If-Match' => 'W/"' . $encodedEtag . '"'
			]
		];

    	return $this->call(
    		'PATCH',
            $this->basePath . '/Company(\'' . $companyName . '\')/' . $tableName . '(\'' . $entityId . '\')',
			$params
		);
	}

    /**
     * {@inheritdoc}
     */
	public function deleteEntity($companyName, $tableName, $entityParameters) {
        $params = [
            'Content-Type' => 'application/json'
        ];

        return $this->call(
            'DELETE',
            $this->basePath . '/Company(\'' . $companyName . '\')/' . $tableName . '(' . $entityParameters . ')',
            $params
        );
    }

    /**
     * {@inheritdoc}
     */
    public function call($method, $path, $params)
	{
    	$handler = new GuzzleHttp\Handler\CurlHandler();
        $client = new GuzzleHttp\Client([
        	'base_uri' => $this->baseUri,
			'handler' => GuzzleHttp\HandlerStack::create($handler),
		]);

        if (!in_array($method, ['PUT', 'PATCH', 'GET', 'POST'], true)) {
            exit;
        }
        try {
        	$params['auth'] = [$this->username, $this->password, 'ntlm'];
            return $client->request($method, $path, $params);
        } catch (GuzzleHttp\Exception\GuzzleException $e) {
            if ($e->getCode() !== 404) {
                throw $e;
            }
            return new GuzzleHttp\Psr7\Response($e->getCode());
        }
    }
}
