<?php

namespace HBSoftware\Infrastructure\Service\Helpers;

use Assert\Assert;
use Assert\LazyAssertion;

/**
 * Class AssertHelper
 * @package Omniboxx
 */
class AssertHelper
{
    /**
     * Helper function for maximum length asserts.
     *
     * @param array $values
     *   Key-Value pairs in the format of [MaxLength => [FieldName => Value]]
     * @param LazyAssertion $assert
     *   Optional previous lazy assertion to add this assertion to.
     *
     * @return LazyAssertion
     *   Returns a lazy assertion object that more assertions can be added on to before verifying.
     */
    public function assertMaxLength($values, $assert = null) {
        $assert = $assert ?: Assert::lazy()->tryAll();
        foreach ($values as $maxLength => $fields) {
            foreach ($fields as $name => $field) {
                $assert->that($field, 'Field ' . $name . ' max length ' . $maxLength)->nullOr()->maxLength($maxLength);
            }
        }
        return $assert;
    }

    /**
     * Helper function to verify date formats. Defaults to iso 8061 (ATOM).
     *
     * @param $dates
     *   Key-Value pairs in the format of [FieldName => Date]]
     * @param LazyAssertion $assert
     *   Optional previous lazy assertion to add this assertion to.
     * @param string $format
     *   The date format to verify against. Defaults to ATOM.
     *
     * @return LazyAssertion|null
     *   Returns a lazy assertion object that more assertions can be added on to before verifying.
     */
    public function assertIsoDateFormat($dates, $assert = null, $format = \DateTime::ATOM) {
        $assert = $assert ?: Assert::lazy()->tryAll();
        foreach ($dates as $field => $date) {
            $assert->that($date, $field)->nullOr()->date($format);
        }
        return $assert;
    }
}
