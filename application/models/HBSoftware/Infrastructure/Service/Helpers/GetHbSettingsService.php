<?php

namespace HBSoftware\Infrastructure\Service\Helpers;

use Settings;

/**
 * Class SettingsHelper
 * @package HBSoftware\Helpers
 */
class GetHbSettingsService
{

    /**
     * @return string|null
     */
    public function getBaseUri() {
        return Settings::get('financial_export_hb_base_uri');
    }

    /**
     * @return string|null
     */
    public function getBasePath() {
        return Settings::get('financial_export_hb_base_path');
    }

    /**
     * @return string|null
     */
    public function getUsername() {
        return Settings::get('financial_export_hb_auth_user');
    }

    /**
     * @return string|null
     */
    public function getPassword() {
        return Settings::get('financial_export_hb_auth_password');
    }
}
