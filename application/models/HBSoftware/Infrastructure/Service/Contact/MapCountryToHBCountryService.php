<?php

namespace HBSoftware\Infrastructure\Service\Contact;

/**
 * Class MapCountryToHBCountryService
 * @package HBSoftware\Infrastructure\Service\Contact\
 */
class MapCountryToHBCountryService
{
    public static $map = [
        'Nederland' => 'NL',
        'Netherlands' => 'NL',
        'Belgium' => 'BE',
        'France Metropolitan' => 'FR',
        'France' => 'FR',
        'Spain' => 'ES',
        'Germany' => 'DE',
        'Luxembourg' => 'LU',
        'Italy' => 'IT',
        'India' => 'IN',
        'China' => 'CN',
    ];

    public function mapCountryToHBCountryCode($country) {
        return isset(self::$map[$country]) ? self::$map[$country] : '';
    }
}
