<?php

namespace HBSoftware\Infrastructure\Service\Contact;

interface GetAddressDataServiceInterface
{
    /**
     * @param $id
     * @return array|null
     */
    public function getAddressDataByUserId($id);

    /**
     * @param $id
     * @return array|null
     */
    public function getObjectAddressDataByUserId($id);

    /**
     * @param $id
     * @return array|null
     */
    public function getObjectAddressDataByObjectUsers($id);
}