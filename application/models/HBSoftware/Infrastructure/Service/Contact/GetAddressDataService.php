<?php

namespace HBSoftware\Infrastructure\Service\Contact;

use Address;
use Objects;
use Objectusers;

class GetAddressDataService implements GetAddressDataServiceInterface
{

    /**
     * @var Address
     */
    private $addressModel;

    /**
     * @var Objects
     */
    private $objectsModel;

    /**
     * @var Objectusers
     */
    private $objectUsersModel;

    /**
     * GetUserDataService constructor.
     * @param Address $addressModel
     * @param Objects $objectsModel
     * @param Objectusers $objectUsersModel
     */
    public function __construct(
        Address $addressModel,
        Objects $objectsModel,
        Objectusers $objectUsersModel
    ) {
        $this->addressModel = $addressModel;
        $this->objectsModel = $objectsModel;
        $this->objectUsersModel = $objectUsersModel;
    }

    /**
     * @param $id
     * @return array|null
     */
    public function getAddressDataByUserId($id) {
        $address = $this->addressModel->get($id);
        if ($address !== null) {
            return $address->toArray();
        }
        return null;
    }

    /**
     * @param $id
     * @return array|null
     */
    public function getObjectAddressDataByUserId($id) {
        $object = $this->objectsModel->fetchRowByUserId($id);
        if ($object === null) {
            return null;
        }
        $address = $this->addressModel->get($object->id, 'object');
        if ($address !== null) {
            return $address->toArray();
        }
        return null;
    }

    /**
     * @param $id
     * @return array|null
     */
    public function getObjectAddressDataByObjectUsers($id) {
        $objectUsers = $this->objectUsersModel->getById($id);
        if ($objectUsers === null) {
            return null;
        }
        $address = $this->addressModel->get($objectUsers->object, 'object');
        if ($address !== null) {
            return $address->toArray();
        }
        return null;
    }
}
