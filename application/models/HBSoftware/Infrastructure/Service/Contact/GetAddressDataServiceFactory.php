<?php

namespace HBSoftware\Infrastructure\Service\Contact;

use Address;
use Objects;
use Objectusers;

/**
 * Class GetAddressDataServiceFactory
 * @package HBSoftware\Infrastructure\Service\Contact
 */
class GetAddressDataServiceFactory
{
    /**
     * @var Address
     */
    private $addressModel;

    /**
     * @var Objects
     */
    private $objectsModel;

    /**
     * @var Objectusers
     */
    private $objectUsersModel;

    /**
     * GetAddressDataServiceFactory constructor.
     * @param Address $addressModel
     * @param Objects $objectsModel
     * @param Objectusers $objectUsersModel
     */
    public function __construct(Address $addressModel, Objects $objectsModel, Objectusers $objectUsersModel) {
        $this->addressModel = $addressModel;
        $this->objectsModel = $objectsModel;
        $this->objectUsersModel = $objectUsersModel;
    }

    /**
     * @return GetAddressDataService
     */
    public function build() {
        return new GetAddressDataService($this->addressModel, $this->objectsModel, $this->objectUsersModel);
    }
}
