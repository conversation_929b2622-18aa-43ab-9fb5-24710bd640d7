<?php

namespace HBSoftware\Infrastructure\Service\Contact;

use CRMEmailAddress;

class GetEmailAddressDataService
{
    /**
     * @var CRMEmailAddress
     */
    private $crmEmailAddressModel;

    /**
     * GetEmailAddressDataService constructor.
     * @param CRMEmailAddress $crmEmailAddressModel
     */
    public function __construct(CRMEmailAddress $crmEmailAddressModel) {
        $this->crmEmailAddressModel = $crmEmailAddressModel;
    }

    /**
     * @param $userId
     * @return array
     */
    public function getEmailAddressesByUserId($userId) {
        $emailAddresses = [];
        $rawAddresses = $this->crmEmailAddressModel->genMatchSelect(['map_to' => $userId, 'type' => 'user'])->query()->fetchAll();
        if (empty($rawAddresses)) {
            throw new \DomainException('No e-mailaddress found for user ' . $userId);
        }

        foreach ($rawAddresses as $addressData) {
            if (empty($addressData['address_type'])) {
                $emailAddresses['default'] = $addressData;
            } else {
                $emailAddresses[$addressData['address_type']] = $addressData;
            }
        }
        return $emailAddresses;
    }
}
