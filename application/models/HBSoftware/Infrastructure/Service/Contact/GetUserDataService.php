<?php

namespace HBSoftware\Infrastructure\Service\Contact;

use Users;
use EmailUser;

/**
 * Class GetUserDataService
 * @package HBSoftware\Infrastructure\Service\Contact
 */
class GetUserDataService implements GetUserDataServiceInterface
{
	/**
	 * @var Users
	 */
    private $userModel;

	/**
	 * GetUserDataService constructor.
	 * @param Users $userModel
	 */
	public function __construct(
	    Users $userModel
    ) {
		$this->userModel = $userModel;
	}

	/**
	 * @param $id
	 *
	 * @return array
     * @throws \DomainException
	 */
	public function getUserDataByUserId($id) {
		$user = [];
		$userData = $this->userModel->getById($id);

		if (null !== $userData) {
			$user['general'] = $userData->toArray();
		} else {
            throw new \DomainException('No user-data found for user with id ' . $id);
        }
		return $user;
	}

    /**
     * @return int[]
     */
	public function getUserIdsToSend() {
        $select = db()->select()
            ->from(['u' => 'users'], ['id'])
//            ->joinInner(
//                ['uo' => 'users_objects'],
//                'uo.customer = u.id',
//                false
//            )
//            ->where('uo.till IS NULL OR uo.till >= NOW()')
            ->where('u.`type` = "tenant"');
//            ->group('u.id');
        return $select->query()->fetchAll();
    }

    /**
     * @param $userId
     * @param $oldDebtorCode
     */
    public function saveOldDebtorCodeToUser($userId, $oldDebtorCode) {
        // @TODO Move to application layer
	    $user = $this->userModel->getById($userId);
	    if (null !== $user) {
            $user->olddebtorcode = $oldDebtorCode;
            $user->save();
        }
    }

    /**
     * @param $userId
     * @return bool
     */
    public function getProjectByUserId($userId) {
        return $this->userModel->getProject($userId);
    }
}
