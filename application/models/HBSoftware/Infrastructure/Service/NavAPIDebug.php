<?php

namespace HBSoftware\Infrastructure\Service;

use GuzzleHttp\Psr7\Response;
use HBSoftware\Infrastructure\Service\Helpers\GetHbSettingsService;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

class NavAPIDebug extends NavAPI
{
    /**
     * @var Logger
     */
    private $logger;

    public function __construct(GetHbSettingsService $getHbSettingsService) {
        parent::__construct($getHbSettingsService);
        global $site_base_dir;
        $this->logger = new Logger('NavAPI Debug Logger');
        $this->logger->pushHandler(new StreamHandler($site_base_dir . '/debug/hb.log'));
    }

    /**
     * {@inheritdoc}
     */
    public function call($method, $path, $params)
    {
        $this->logger->addInfo('NAVAPI Debug call to method ' . $method . ', path ' . $path);
        $this->logger->addInfo(json_encode($params));
        // Some calls expect a response with an ETag key
        $params['ETag'] = 'test';
        return new Response(418, [], json_encode($params));
    }
}
