<?php

namespace HBSoftware\Infrastructure\Service;

/**
 * Class NavAPI
 * @package Omniboxx
 */
interface NavAPIInterface
{
    /**
     * Get all records from the given table for the given company.
     *
     * @param string $tableName
     *   Table name.
     * @param string $companyName
     *   Company name.
     *
     * @return array|false
     *   Returns the requested table contents.
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getTable($tableName, $companyName);

    /**
     * Insert a row into the given table for the given company table.
     *
     * @param string $body
     *   Request body as JSON string.
     * @param string $tableName
     *   Table name.
     * @param string $companyName
     *   Company name. Not relevant for Contact entities.
     *
     * @return \Psr\Http\Message\ResponseInterface
     *   For a POST only the status code needs to be returned.
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function insertRow($body, $tableName, $companyName);

    /**
     * @param $entityId
     *   ID of the entity to fetch an ETAG for.
     * @param $companyName
     *   Company name. Not relevant for Contact entities.
     * @param $tableName
     *   Name of the table to fetch.
     *
     * @return \Psr\Http\Message\ResponseInterface|false
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getEntity($entityId, $companyName, $tableName);

    /**
     * @param $body
     *   Request body as JSON string.
     * @param $tableName
     *   Table name.
     * @param $companyName
     *   Company name. Not relevant for Contact entities.
     * @param $entityEtag
     *   The entity's ETAG (used to ensure multiple calls in a short space of time don't overwrite each other)
     * @param $entityId
     *   Entity ID.
     * @return \Psr\Http\Message\ResponseInterface|false
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateEntity($body, $tableName, $companyName, $entityEtag, $entityId);

    /**
     * @param $companyName
     *   Company name. Not relevant for Contact entities.
     * @param $tableName
     *   Name of the table to fetch.
     * @param $entityParameters
     *
     * @return \Psr\Http\Message\ResponseInterface|false
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function deleteEntity($companyName, $tableName, $entityParameters);

    /**
     * @param string $method
     *   The HTTP method of the call. Can be either POST or GET.
     *
     * @param string $path
     *   The path of the API call.
     *
     * @param array $params
     *   The parameters of the API call.
     *
     * @return \Psr\Http\Message\ResponseInterface|false
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function call($method, $path, $params);
}