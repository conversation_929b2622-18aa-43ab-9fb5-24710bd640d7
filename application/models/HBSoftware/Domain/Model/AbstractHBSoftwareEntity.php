<?php

namespace HBSoftware\Domain\Model;

abstract class AbstractHBSoftwareEntity implements HbDomainEntity
{
	/**
	 * @var string
	 */
	protected $ETag;

	/**
	 * @param string $key
	 * @param int|string|float $value
	 */
	abstract public function set($key, $value);

	/**
	 * {@inheritdoc}
	 */
	public function jsonSerialize() {
		$data = get_object_vars($this);
		foreach ($data as $key => $value) {
			if ($value === null) {
				unset($data[$key]);
			}
		}
		return $data;
	}

	/**
	 * @return string
	 */
	public function getETag() {
		return $this->ETag;
	}

	/**
	 * @param string $ETag
	 */
	public function setETag($ETag) {
		$this->ETag = $ETag;
	}
}
