<?php

namespace HBSoftware\Domain\Model;

/**
 * Class Complex
 * @package Omniboxx
 */
interface HbDomainEntity extends \JsonSerializable
{
	/**
	 * @param $key
	 * @param $value
	 * @return void
	 */
    public function set($key, $value);

    /**
     * {@inheritdoc}
     */
    public function jsonSerialize();

    /**
     * Validate the object. Validation failures should cause exceptions here, nothing is returned.
     */
    public function validate();
}
