<?php

namespace HBSoftware\Domain\Model\InvoiceLine;

use HBSoftware\Infrastructure\Service\Helpers\AssertHelper;
use HBSoftware\Domain\Model\AbstractHBSoftwareEntity;

/**
 * Class Verkoopregel
 * @package Omniboxx\Types
 */
class InvoiceLine extends AbstractHBSoftwareEntity
{
    /**
     * @var string
     */
    protected $Document_No;
    /**
     * @var string
     */
    protected $Type;
    /**
     * @var string
     */
    protected $No;
    /**
     * @var string
     */
    protected $DescriptionOmniboxx;
    /**
     * @var int
     */
    protected $VAT;
    /**
     * @var float
     */
    protected $Amount;
    /**
     * @var float
     */
    protected $AmountInclVAT;
    /**
     * @var string
     */
    protected $ComplexNr;
    /**
     * @var string
     */
    protected $EenheidNr;

    /**
     * Verkoopregel constructor.
     * @param string $No
     */
    public function __construct(
        $No
    ) {
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getDocumentNo()
    {
        return $this->Document_No;
    }

    /**
     * @param string $Document_No
     */
    public function setDocumentNo($Document_No)
    {
        $this->Document_No = $Document_No;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->Type;
    }

    /**
     * @param string $Type
     */
    public function setType($Type)
    {
        $this->Type = $Type;
    }

    /**
     * @return string
     */
    public function getNo()
    {
        return $this->No;
    }

    /**
     * @param string $No
     */
    public function setNo($No)
    {
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getDescriptionOmniboxx()
    {
        return $this->DescriptionOmniboxx;
    }

    /**
     * @param string $DescriptionOmniboxx
     */
    public function setDescriptionOmniboxx($DescriptionOmniboxx)
    {
        $this->DescriptionOmniboxx = $DescriptionOmniboxx;
    }

    /**
     * @return int
     */
    public function getVAT()
    {
        return $this->VAT;
    }

    /**
     * @param int $VAT
     */
    public function setVAT($VAT)
    {
        $this->VAT = $VAT;
    }

    /**
     * @return float
     */
    public function getAmount()
    {
        return $this->Amount;
    }

    /**
     * @param float $Amount
     */
    public function setAmount($Amount)
    {
        $this->Amount = $Amount;
    }

    /**
     * @return float
     */
    public function getAmountInclVAT()
    {
        return $this->AmountInclVAT;
    }

    /**
     * @param float $AmountInclVAT
     */
    public function setAmountInclVAT($AmountInclVAT)
    {
        $this->AmountInclVAT = $AmountInclVAT;
    }

    /**
     * @return string
     */
    public function getComplexNr()
    {
        return $this->ComplexNr;
    }

    /**
     * @param string $ComplexNr
     */
    public function setComplexNr($ComplexNr)
    {
        $this->ComplexNr = $ComplexNr;
    }

    /**
     * @return string
     */
    public function getEenheidNr()
    {
        return $this->EenheidNr;
    }

    /**
     * @param string $EenheidNr
     */
    public function setEenheidNr($EenheidNr)
    {
        $this->EenheidNr = $EenheidNr;
    }

	/**
	 * {@inheritdoc}
	 */
	public function set($key, $value) {
		$keys = array_keys(get_object_vars($this));
		if (in_array($key, $keys, true)) {
			$this->$key = $value;
		} else {
			throw new \InvalidArgumentException('Invalid parameter key for type InvoiceLine: ' . $key);
		}
	}

    /**
     * {@inheritdoc}
     */
    public function validate() {
        $assertHelper = new AssertHelper();
        $assert = $assertHelper->assertMaxLength([
            20 => [
                'No' => $this->getNo(),
                'ComplexNr' => $this->getComplexNr(),
                'EenheidNr' => $this->getEenheidNr(),
            ],
        ]);
        $assert->that($this->getType(), 'Type')->nullOr()->between(0, 2);
        $assert->verifyNow();
    }
}
