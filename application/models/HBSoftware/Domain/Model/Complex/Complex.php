<?php

namespace HBSoftware\Domain\Model\Complex;

use HBSoftware\Infrastructure\Service\Helpers\AssertHelper;
use HBSoftware\Domain\Model\AbstractHBSoftwareEntity;

/**
 * Class Complex
 * @package Omniboxx
 */
class Complex extends AbstractHBSoftwareEntity
{
    /**
     * @var string
     */
    protected $Nr;

    /**
     * @var string
     */
    protected $Omschrijving;

    /**
     * @var
     */
    protected $Postcode;

    /**
     * @var string
     */
    protected $Plaats;

    /**
     * @var string
     */
    protected $Locatie;

    /**
     * @var string
     */
    protected $DatumOplevering;

    /**
     * @var string
     */
    protected $GemaaktOp;

    /**
     * @var string
     */
    protected $Regiocode;

    /**
     * @var string
     */
    protected $Administratie;

    /**
     * @var float
     */
    protected $TotaalComplex;

    /**
     * @var float
     */
    protected $AankoopbedragExclBtw;

    /**
     * @var string
     */
    protected $Provincie;

    /**
     * @var string
     */
    protected $OmniboxxID;

    /**
     * @var string
     */
    protected $Status;

	/**
	 * @var string
	 */
    protected $ETag;

    /**
     * Complex constructor.
     * @param string $Nr
     */
    public function __construct($Nr)
    {
        $this->Nr = $Nr;
    }

    /**
     * @return string
     */
    public function getNr()
    {
        return $this->Nr;
    }

    /**
     * @param string $Nr
     */
    public function setNr($Nr)
    {
        $this->Nr = $Nr;
    }

    /**
     * @return string
     */
    public function getOmschrijving()
    {
        return $this->Omschrijving;
    }

    /**
     * @param string $Omschrijving
     */
    public function setOmschrijving($Omschrijving)
    {
        $this->Omschrijving = $Omschrijving;
    }

    /**
     * @return mixed
     */
    public function getPostcode()
    {
        return $this->Postcode;
    }

    /**
     * @param mixed $Postcode
     */
    public function setPostcode($Postcode)
    {
        $this->Postcode = $Postcode;
    }

    /**
     * @return string
     */
    public function getPlaats()
    {
        return $this->Plaats;
    }

    /**
     * @param string $Plaats
     */
    public function setPlaats($Plaats)
    {
        $this->Plaats = $Plaats;
    }

    /**
     * @return string
     */
    public function getLocatie()
    {
        return $this->Locatie;
    }

    /**
     * @param string $Locatie
     */
    public function setLocatie($Locatie)
    {
        $this->Locatie = $Locatie;
    }

    /**
     * @return string
     */
    public function getDatumOplevering()
    {
        return $this->DatumOplevering;
    }

    /**
     * @param string $DatumOplevering
     */
    public function setDatumOplevering($DatumOplevering)
    {
        $this->DatumOplevering = $DatumOplevering;
    }

    /**
     * @return string
     */
    public function getGemaaktOp()
    {
        return $this->GemaaktOp;
    }

    /**
     * @param string $GemaaktOp
     */
    public function setGemaaktOp($GemaaktOp)
    {
        $this->GemaaktOp = $GemaaktOp;
    }

    /**
     * @return string
     */
    public function getRegiocode()
    {
        return $this->Regiocode;
    }

    /**
     * @param string $Regiocode
     */
    public function setRegiocode($Regiocode)
    {
        $this->Regiocode = $Regiocode;
    }

    /**
     * @return string
     */
    public function getAdministratie()
    {
        return $this->Administratie;
    }

    /**
     * @param string $Administratie
     */
    public function setAdministratie($Administratie)
    {
        $this->Administratie = $Administratie;
    }

    /**
     * @return float
     */
    public function getTotaalComplex()
    {
        return $this->TotaalComplex;
    }

    /**
     * @param float $TotaalComplex
     */
    public function setTotaalComplex($TotaalComplex)
    {
        $this->TotaalComplex = $TotaalComplex;
    }

    /**
     * @return float
     */
    public function getAankoopbedragExclBtw()
    {
        return $this->AankoopbedragExclBtw;
    }

    /**
     * @param float $AankoopbedragExclBtw
     */
    public function setAankoopbedragExclBtw($AankoopbedragExclBtw)
    {
        $this->AankoopbedragExclBtw = $AankoopbedragExclBtw;
    }

    /**
     * @return string
     */
    public function getProvincie()
    {
        return $this->Provincie;
    }

    /**
     * @param string $Provincie
     */
    public function setProvincie($Provincie)
    {
        $this->Provincie = $Provincie;
    }

    /**
     * @return string
     */
    public function getOmniboxxID()
    {
        return $this->OmniboxxID;
    }

    /**
     * @param string $OmniboxxID
     */
    public function setOmniboxxID($OmniboxxID)
    {
        $this->OmniboxxID = $OmniboxxID;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->Status;
    }

    /**
     * @param string $Status
     */
    public function setStatus($Status)
    {
        $this->Status = $Status;
    }

	/**
	 * {@inheritdoc}
	 */
	public function set($key, $value) {
		$keys = array_keys(get_object_vars($this));
		if (in_array($key, $keys, true)) {
			$this->$key = $value;
		} else {
			throw new \InvalidArgumentException('Invalid parameter key for type Contact: ' . $key);
		}
	}

    /**
     * {@inheritdoc}
     */
    public function validate() {
        $assertHelper = new AssertHelper();
        $assert = $assertHelper->assertMaxLength([
            20 => [
                'Nr' => $this->getNr(),
                'Postcode' => $this->getPostcode(),
            ],
            30 => [
                'Plaats' => $this->getPlaats(),
                'Administratie' => $this->getAdministratie(),
                'Provincie' => $this->getProvincie(),
            ],
            50 => [
                'Omschrijving' => $this->getOmschrijving(),
                'Locatie' => $this->getLocatie(),
            ],
        ]);
        $assert->that($this->getStatus(), 'Status')->between(0, 5);
        $assert = $assertHelper->assertIsoDateFormat([
            'DatumOplevering' => $this->getDatumOplevering(),
            'GemaaktOp' => $this->getGemaaktOp(),
        ], $assert);
        $assert->verifyNow();
    }
}
