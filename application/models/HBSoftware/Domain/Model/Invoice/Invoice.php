<?php

namespace HBSoftware\Domain\Model\Invoice;

use HBSoftware\Infrastructure\Service\Helpers\AssertHelper;
use HBSoftware\Domain\Model\AbstractHBSoftwareEntity;

/**
 * Class Verkoopfactuur
 * @package Omniboxx\Types
 */
class Invoice extends AbstractHBSoftwareEntity
{
    /**
     * @var string
     */
    protected $No;
    /**
     * @var string
     */
    protected $CustomerNo;
    /**
     * @var string
     */
    protected $PostingDate;
    /**
     * @var string
     */
    protected $PostingDescription;
    /**
     * @var string
     */
    protected $DueDate;
    /**
     * @var string
     */
    protected $DocumentDate;
    /**
     * @var string
     */
    protected $Status;
    /**
     * @var string
     */
    protected $ComplexNr;
    /**
     * @var string
     */
    protected $EenheidNr;
    /**
     * @var string
     */
    protected $FactuurSoort;

    /**
     * @var string
     */
    protected $Betaalwijze;

    /**
     * @var string
     */
    protected $StructMed;

    /**
     * @var string
     */
    protected $CustomerName;

    /**
     * Verkoopfactuur constructor.
     * @param string $No
     */
    public function __construct($No)
	{
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getNo()
    {
        return $this->No;
    }

    /**
     * @param string $No
     */
    public function setNo($No)
    {
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getCustomerNo()
    {
        return $this->CustomerNo;
    }

    /**
     * @param string $CustomerNo
     */
    public function setCustomerNo($CustomerNo)
    {
        $this->CustomerNo = $CustomerNo;
    }

    /**
     * @return string
     */
    public function getPostingDate()
    {
        return $this->PostingDate;
    }

    /**
     * @param string $PostingDate
     */
    public function setPostingDate($PostingDate)
    {
        $this->PostingDate = $PostingDate;
    }

    /**
     * @return string
     */
    public function getPostingDescription()
    {
        return $this->PostingDescription;
    }

    /**
     * @param string $PostingDescription
     */
    public function setPostingDescription($PostingDescription)
    {
        $this->PostingDescription = $PostingDescription;
    }

    /**
     * @return string
     */
    public function getDueDate()
    {
        return $this->DueDate;
    }

    /**
     * @param string $DueDate
     */
    public function setDueDate($DueDate)
    {
        $this->DueDate = $DueDate;
    }

    /**
     * @return string
     */
    public function getDocumentDate()
    {
        return $this->DocumentDate;
    }

    /**
     * @param string $DocumentDate
     */
    public function setDocumentDate($DocumentDate)
    {
        $this->DocumentDate = $DocumentDate;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->Status;
    }

    /**
     * @param string $Status
     */
    public function setStatus($Status)
    {
        $this->Status = $Status;
    }

    /**
     * @return string
     */
    public function getComplexNr()
    {
        return $this->ComplexNr;
    }

    /**
     * @param string $ComplexNr
     */
    public function setComplexNr($ComplexNr)
    {
        $this->ComplexNr = $ComplexNr;
    }

    /**
     * @return string
     */
    public function getEenheidNr()
    {
        return $this->EenheidNr;
    }

    /**
     * @param string $EenheidNr
     */
    public function setEenheidNr($EenheidNr)
    {
        $this->EenheidNr = $EenheidNr;
    }

    /**
     * @return string
     */
    public function getFactuurSoort()
    {
        return $this->FactuurSoort;
    }

    /**
     * @param string $FactuurSoort
     */
    public function setFactuurSoort($FactuurSoort)
    {
        $this->FactuurSoort = $FactuurSoort;
    }
    /**
     * @return string
     */
    public function getStructMed()
    {
        return $this->StructMed;
    }

    /**
     * @inheritDoc
     */
    public function setStructMed($StructMed)
    {
        $this->StructMed = $StructMed;
    }

    /**
     * @return string
     */
    public function getCustomerName()
    {
        return $this->CustomerName;
    }

    /**
     * @param string $CustomerName
     */
    public function setCustomerName($CustomerName)
    {
        $this->CustomerName = $CustomerName;
    }

	/**
	 * {@inheritdoc}
	 */
	public function set($key, $value) {
		$keys = array_keys(get_object_vars($this));
		if (in_array($key, $keys, true)) {
			$this->$key = $value;
		} else {
			throw new \InvalidArgumentException('Invalid parameter key for type Invoice: ' . $key);
		}
	}

    /**
     * {@inheritdoc}
     */
    public function validate() {
        $assertHelper = new AssertHelper();
        $assert = $assertHelper->assertMaxLength([
            20 => [
                'No' => $this->getNo(),
                'CustomerNo' => $this->getCustomerNo(),
                'ComplexNr' => $this->getComplexNr(),
                'EenheidNr' => $this->getEenheidNr(),
            ],
            50 => [
                'PostingDescription' => $this->getPostingDescription(),
            ],
        ]);
        $assert->that($this->getStatus(), 'Status')->nullOr()->between(0, 4);
        $assertHelper->assertIsoDateFormat([
            'PostingDate' => $this->getPostingDate(),
            'DueDate' => $this->getDueDate(),
            'DocumentDate' => $this->getDocumentDate(),
        ], $assert);
        $assert->verifyNow();
    }
}
