<?php

namespace HBSoftware\Domain\Model\Invoice;

use Psr\Http\Message\ResponseInterface;
use HBSoftware\Domain\Model\InvoiceLine\InvoiceLine;
use HBSoftware\Application\Domain\InvoiceLineService;
use HBSoftware\Application\Domain\InvoiceService;
use Monolog\Logger;

class InvoiceRepository
{
    /**
     * @var InvoiceService
     */
    private $apiInvoiceService;

    /**
     * @var InvoiceLineService
     */
    private $invoiceLineService;

    /**
     * @var Logger
     */
    private $logger;

    public function __construct(
        InvoiceService $invoiceService,
        InvoiceLineService $invoiceLineService,
        Logger $logger
    ) {
        $this->apiInvoiceService = $invoiceService;
        $this->invoiceLineService = $invoiceLineService;
        $this->logger = $logger;
    }

    /**
     * @param Invoice $invoice
     * @param $company
     * @return ResponseInterface[]|false
     */
    public function addInvoice(Invoice $invoice, $company) {
        try {
            $responses[] = $this->apiInvoiceService->createInvoice($invoice, $company);
            return $responses;
        } catch (\DomainException $exception) {
            $this->logger->addError($exception->getMessage());
            return false;
        } catch (\InvalidArgumentException $e) {
            $this->logger->addError(
                'Invalid argument exception for Invoice, invoice ID ' . $invoice->getNo() . ': ' . $e->getMessage()
            );
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->addError(
                'Guzzle Exception for Invoice, invoice ID ' . $invoice->getNo() . ': ' . $e->getMessage()
            );
        }
        return false;
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceLine $invoiceLine
     * @param string $company
     * @param string $rowId
     * @throws SendInvoiceLineFailedException
     * @return ResponseInterface
     */
    public function addInvoiceLine(Invoice $invoice, InvoiceLine $invoiceLine, $company, $rowId) {
        try {
            return $this->invoiceLineService->createInvoiceLine(
                $invoiceLine,
                $company
            );
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->addError(
                'Guzzle Exception for InvoiceLine, invoice ID ' . $invoice->getNo() .
                ', row ID ' . $rowId . ': ' . $e->getMessage()
            );
        } catch (\InvalidArgumentException $e) {
            $this->logger->addError(
                'Invalid argument exception for InvoiceLine ' . $rowId .
                ', invoice ID ' . $rowId . ': ' . $e->getMessage()
            );
        }
        throw new SendInvoiceLineFailedException(
            'Sending invoice line ' . $rowId . ' for invoice ' . $invoice->getNo() . ' failed'
        );
    }

    /**
     * @param Invoice $invoiceHeader
     * @param InvoiceLine[] $invoiceLines
     * @param string $company
     * @return ResponseInterface[]|false
     */
    public function removeInvoice(Invoice $invoiceHeader, array $invoiceLines, $company) {
        $responses = [];
        try {
            foreach ($invoiceLines as $Line_No => $invoiceLine) {
                $responses[] = $this->invoiceLineService->deleteInvoiceLine($invoiceLine, $company, $Line_No);
            }
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->addCritical(
                'Could not delete invoice row ' . $Line_No .
                ' of invoice ' . $invoiceHeader->getNo() . ': ' . $e->getMessage()
            );
        }
        try {
            $responses[] = $this->apiInvoiceService->deleteInvoice($invoiceHeader, $company);
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->addCritical(
                'Could not delete invoice ' . $invoiceHeader->getNo() . ': ' . $e->getMessage()
            );
        }
        return $responses;
    }
}
