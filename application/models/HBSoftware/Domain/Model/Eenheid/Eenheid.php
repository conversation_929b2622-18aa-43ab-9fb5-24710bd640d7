<?php

namespace HBSoftware\Domain\Model\Eenheid;

use HBSoftware\Infrastructure\Service\Helpers\AssertHelper;
use HBSoftware\Domain\Model\AbstractHBSoftwareEntity;

/**
 * Class Eenheid
 * @package Omniboxx\Types
 */
class Eenheid extends AbstractHBSoftwareEntity
{
	/**
	 * @var string
	 */
	protected $Nr;
    /**
     * @var string
     */
    protected $Omschrijving;
    /**
     * @var string
     */
    protected $Adres;
    /**
     * @var string
     */
    protected $Woonplaats;
    /**
     * @var string
     */
    protected $Aanmaakdatum;
	/**
	 * @var string
	 */
    protected $LaatsteWijziging;
    /**
     * @var string
     */
    protected $RegionCode;
    /**
     * @var string
     */
    protected $Objectsoort;
    /**
     * @var string
     */
    protected $Postcode;
    /**
     * @var float
     */
    protected $VVOm2;
    /**
     * @var string
     */
    protected $ComplexNr;
    /**
     * @var string
     */
    protected $Status;
    /**
     * @var string
     */
    protected $Huisnummer;
    /**
     * @var float
     */
    protected $BVO;
    /**
     * @var string
     */
    protected $Energielabel;
    /**
     * @var string
     */
    protected $ObjectgroepOmniboxx;
    /**
     * @var int
     */
    protected $OmniboxxID;

    /**
     * Eenheid constructor.
	 * @param string $Nr
     */
    public function __construct($Nr) {
    	$this->Nr = $Nr;
    }

	/**
	 * @return string
	 */
	public function getNr()
	{
		return $this->Nr;
	}

	/**
	 * @param string $Nr
	 */
	public function setNr($Nr)
	{
		$this->Nr = $Nr;
	}

    /**
     * @return string
     */
    public function getOmschrijving()
    {
        return $this->Omschrijving;
    }

    /**
     * @param string $Omschrijving
     */
    public function setOmschrijving($Omschrijving)
    {
        $this->Omschrijving = $Omschrijving;
    }

    /**
     * @return string
     */
    public function getAdres()
    {
        return $this->Adres;
    }

    /**
     * @param string $Adres
     */
    public function setAdres($Adres)
    {
        $this->Adres = $Adres;
    }

    /**
     * @return string
     */
    public function getWoonplaats()
    {
        return $this->Woonplaats;
    }

    /**
     * @param string $Woonplaats
     */
    public function setWoonplaats($Woonplaats)
    {
        $this->Woonplaats = $Woonplaats;
    }

    /**
     * @return string
     */
    public function getAanmaakdatum()
    {
        return $this->Aanmaakdatum;
    }

    /**
     * @param string $Aanmaakdatum
     */
    public function setAanmaakdatum($Aanmaakdatum)
    {
        $this->Aanmaakdatum = $Aanmaakdatum;
    }

    /**
     * @return string
     */
    public function getRegionCode()
    {
        return $this->RegionCode;
    }

    /**
     * @param string $RegionCode
     */
    public function setRegionCode($RegionCode)
    {
        $this->RegionCode = $RegionCode;
    }

    /**
     * @return string
     */
    public function getObjectsoort()
    {
        return $this->Objectsoort;
    }

    /**
     * @param string $Objectsoort
     */
    public function setObjectsoort($Objectsoort)
    {
        $this->Objectsoort = $Objectsoort;
    }

    /**
     * @return string
     */
    public function getPostcode()
    {
        return $this->Postcode;
    }

    /**
     * @param string $Postcode
     */
    public function setPostcode($Postcode)
    {
        $this->Postcode = $Postcode;
    }

    /**
     * @return float
     */
    public function getVVOm2()
    {
        return $this->VVOm2;
    }

    /**
     * @param float $VVOm2
     */
    public function setVVOm2($VVOm2)
    {
        $this->VVOm2 = $VVOm2;
    }

    /**
     * @return string
     */
    public function getComplexNr()
    {
        return $this->ComplexNr;
    }

    /**
     * @param string $ComplexNr
     */
    public function setComplexNr($ComplexNr)
    {
        $this->ComplexNr = $ComplexNr;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->Status;
    }

    /**
     * @param string $Status
     */
    public function setStatus($Status)
    {
        $this->Status = $Status;
    }

    /**
     * @return string
     */
    public function getHuisnummer()
    {
        return $this->Huisnummer;
    }

    /**
     * @param string $Huisnummer
     */
    public function setHuisnummer($Huisnummer)
    {
        $this->Huisnummer = $Huisnummer;
    }

    /**
     * @return float
     */
    public function getBVO()
    {
        return $this->BVO;
    }

    /**
     * @param float $BVO
     */
    public function setBVO($BVO)
    {
        $this->BVO = $BVO;
    }

    /**
     * @return string
     */
    public function getEnergielabel()
    {
        return $this->Energielabel;
    }

    /**
     * @param string $Energielabel
     */
    public function setEnergielabel($Energielabel)
    {
        $this->Energielabel = $Energielabel;
    }

    /**
     * @return string
     */
    public function getObjectgroepOmniboxx()
    {
        return $this->ObjectgroepOmniboxx;
    }

    /**
     * @param string $ObjectgroepOmniboxx
     */
    public function setObjectgroepOmniboxx($ObjectgroepOmniboxx)
    {
        $this->ObjectgroepOmniboxx = $ObjectgroepOmniboxx;
    }

    /**
     * @return int
     */
    public function getOmniboxxID()
    {
        return $this->OmniboxxID;
    }

    /**
     * @param int $OmniboxxID
     */
    public function setOmniboxxID($OmniboxxID)
    {
        $this->OmniboxxID = $OmniboxxID;
    }

	/**
	 * {@inheritdoc}
	 */
	public function set($key, $value) {
		$keys = array_keys(get_object_vars($this));
		if (in_array($key, $keys, true)) {
			$this->$key = $value;
		} else {
			throw new \InvalidArgumentException('Invalid parameter key for type Eenheid: ' . $key);
		}
	}

    /**
     * {@inheritdoc}
     */
    public function validate() {
        $assertHelper = new AssertHelper();
        $assert = $assertHelper->assertMaxLength([
            10 => [
                'RegionCode' => $this->getRegionCode(),
                'Objectsoort' => $this->getObjectsoort(),
                'Huisnummer' => $this->getHuisnummer(),
                'Energielabel' => $this->getEnergielabel(),
            ],
            20 => [
                'Postcode' => $this->getPostcode(),
                'ComplexNr' => $this->getComplexNr(),
            ],
            50 => [
                'Omschrijving' => $this->getOmschrijving(),
                'Woonplaats' => $this->getWoonplaats(),
            ],
            60 => [
                'Adres' => $this->getAdres(),
            ],
            100 => [
                'ObjectgroepOmniboxx' => $this->getObjectgroepOmniboxx(),
            ],
        ]);
        $assert->that($this->getStatus(), 'Status')->between(0, 4);
        $assert = $assertHelper->assertIsoDateFormat([
            'Aanmaakdatum' => $this->getAanmaakdatum(),
        ], $assert);
        $assert->verifyNow();
    }
}
