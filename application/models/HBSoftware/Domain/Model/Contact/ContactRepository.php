<?php

namespace HBSoftware\Domain\Model\Contact;

use Assert\LazyAssertionException;
use Financial\Application\Contact\AddFinancialContactErrorService;
use Financial\Application\Contact\AddFinancialContactIdentifierService;
use Financial\Application\Contact\LogStartSyncContactService;
use Financial\Infrastructure\Models\FinancialSystems;
use HBSoftware\Domain\Factory\Contact\ContactFactory;
use HBSoftware\Infrastructure\Cache\ContactDataHash;
use HBSoftware\Infrastructure\Service\Contact\GetEmailAddressDataService;
use HBSoftware\Infrastructure\Service\Contact\GetUserDataService;
use HBSoftware\Infrastructure\Service\Contact\GetAddressDataService;
use HBSoftware\Infrastructure\Service\Contact\MapCountryToHBCountryService;
use HBSoftware\Application\Domain\ContactService;
use Monolog\Logger;

class ContactRepository
{
	/**
	 * @var ContactFactory
	 */
	private $contactFactory;

	/**
	 * @var GetUserDataService
	 */
	private $getUserDataService;

    /**
     * @var GetAddressDataService
     */
	private $getAddressDataService;

    /**
     * @var MapCountryToHBCountryService
     */
	private $mapCountryToHBCountryService;

    /**
     * @var GetEmailAddressDataService
     */
	private $getEmailAddressDataService;

	/**
	 * @var ContactService
	 */
	private $contactService;

    /**
     * @var Logger
     */
    private $logger;
    /**
     * @var LogStartSyncContactService
     */
    private $logStartSyncContactService;
    /**
     * @var AddFinancialContactErrorService
     */
    private $addFinancialContactErrorService;
    /**
     * @var AddFinancialContactIdentifierService
     */
    private $addFinancialContactIdentifierService;

    /**
     * ContactRepository constructor.
     * @param ContactFactory $contactFactory
     * @param GetUserDataService $getUserDataService
     * @param GetAddressDataService $getAddressDataService
     * @param GetEmailAddressDataService $getEmailAddressDataService
     * @param ContactService $contactService
     * @param Logger $logger
     * @param MapCountryToHBCountryService $mapCountryToHBCountryService
     */
	public function __construct(
	    ContactFactory $contactFactory,
        GetUserDataService $getUserDataService,
        GetAddressDataService $getAddressDataService,
        GetEmailAddressDataService $getEmailAddressDataService,
        ContactService $contactService,
        Logger $logger,
        MapCountryToHBCountryService $mapCountryToHBCountryService,
        LogStartSyncContactService $logStartSyncContactService,
        AddFinancialContactErrorService $addFinancialContactErrorService,
        AddFinancialContactIdentifierService $addFinancialContactIdentifierService
    ) {
		$this->contactFactory = $contactFactory;
		$this->getUserDataService = $getUserDataService;
		$this->getAddressDataService = $getAddressDataService;
		$this->getEmailAddressDataService = $getEmailAddressDataService;
		$this->contactService = $contactService;
		$this->logger = $logger;
		$this->mapCountryToHBCountryService = $mapCountryToHBCountryService;
        $this->logStartSyncContactService = $logStartSyncContactService;
        $this->addFinancialContactErrorService = $addFinancialContactErrorService;
        $this->addFinancialContactIdentifierService = $addFinancialContactIdentifierService;
    }
	/**
	 * @param $id
     * @return mixed
	 */
	public function addContactByUserId($id) {

        if (\Settings::get('general_company_shortname') === 'testwfc') {
            $companyName = '416 WFC Vastgoed B.V.';
        } else {
            $companyName = '201 - Upgrade Estate Group';
        }

        $financialContact  = $this->logStartSyncContactService->execute(FinancialSystems::REF_HB_SOFTWARE, $id, $companyName);
        
	    try {
            $userData = $this->getUserDataService->getUserDataByUserId($id);
            $userData['address'] = $this->getAddressData($id);
            $this->addCountryCodeToAddressData($userData['address']);
            try {
                $userData['emailData'] = $this->getEmailAddressDataService->getEmailAddressesByUserId($id);
            } catch (\DomainException $exception) {
                $userData['emailData'] = false;
            }

            if (!$this->isContactDataChanges($userData)) {
                $this->logger->addError(
                    'Contact data not changes, user ID ' . $id
                );

                return false;
            }

            $contact = $this->contactFactory->build($userData);
            try {
                $response = $this->contactService->createContact($contact, $companyName);
            } catch (\Exception $exception) {
                $this->addFinancialContactErrorService->execute($financialContact, $exception->getMessage());
                $response = false;
            }

            if ($response !== false) {
                $this->getUserDataService->saveOldDebtorCodeToUser($id, $contact->getNo());

                try {
                    $this->addFinancialContactIdentifierService->execute($financialContact, $contact->getNo());
                } catch (\Exception $e) {
                    error_log($e->getMessage());
                }
            }

            return $response;
        } catch (\DomainException $exception) {
	        $this->logger->addInfo(
	            'Domain exception for Contact, user ID ' . $id . ': ' . $exception->getMessage()
            );

            $this->addFinancialContactErrorService->execute($financialContact, $exception->getMessage());

        } catch (LazyAssertionException $exception) {
	        $this->logger->addError(
	            'Assertion error when validating Contact, user ID ' . $id . ': ' . $exception->getMessage()
            );

            $this->addFinancialContactErrorService->execute($financialContact, $exception->getMessage());
        } catch (\InvalidArgumentException $exception) {
	        $this->logger->addError(
	            'Invalid argument exception for Contact, user ID ' . $id . ': ' . $exception->getMessage()
            );

            $this->addFinancialContactErrorService->execute($financialContact, $exception->getMessage());
        }
        return false;
	}


    private function isContactDataChanges($contactArr)
    {

        $contactString = json_encode($contactArr);
        $contactHash = hash('sha256', $contactString);
        $contactId = $contactArr['general']['id'];
        $contactDataHashCash = new  ContactDataHash();
        $contactDataHashList = $contactDataHashCash->load();

        if (!array_key_exists($contactId, $contactDataHashList)) {
            $contactDataHashList[$contactId] = $contactHash;
            $contactDataHashCash->save($contactDataHashList);

            return true;
        }

        $cashContactDataHash = $contactDataHashList[$contactId];

        if ($cashContactDataHash !== $contactHash) {
            $contactDataHashList[$contactId] = $contactHash;
            $contactDataHashCash->save($contactDataHashList);

            return true;
        }

        return false;
    }



    /**
     * @return mixed
     */
	public function addAllContacts() {
	    $responses = [];
	    $users = $this->getUserDataService->getUserIdsToSend();
	    foreach ($users as $user) {
	        $responses[] = $this->addContactByUserId($user['id']);
        }
	    return $responses;
	}

    /**
     * @param $id
     * @return mixed
     */
	private function getAddressData($id) {
	    $addressData = $this->getAddressDataService->getAddressDataByUserId($id);
	    if ($addressData === null) {
	        return $this->getAddressDataService->getObjectAddressDataByUserId($id) ?: null;
        }
	    return $addressData;
    }

    private function addCountryCodeToAddressData(&$addressData) {
	    $countryCode = $addressData['country'];
	    $addressData['countryCode'] = $this->mapCountryToHBCountryService->mapCountryToHBCountryCode($countryCode);
    }
}
