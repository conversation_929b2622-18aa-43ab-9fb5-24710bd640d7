<?php

namespace HBSoftware\Domain\Model\Contact;

use HBSoftware\Infrastructure\Service\Helpers\AssertHelper;
use HBSoftware\Domain\Model\AbstractHBSoftwareEntity;

/**
 * Class Contact
 * @package Omniboxx
 */
class Contact extends AbstractHBSoftwareEntity
{
    /**
     * @var string
     */
    protected $No;
    /**
     * @var string
     */
    protected $Name;
    /**
     * @var string
     */
    protected $Name2;
    /**
     * @var string
     */
    protected $Address;
    /**
     * @var string
     */
    protected $City;
    /**
     * @var string
     */
    protected $PhoneNo;
    /**
     * @var string
     */
    protected $CurrencyCode;
    /**
     * @var string
     */
    protected $LanguageCode;
    /**
     * @var string
     */
    protected $RegionCode;
    /**
     * @var string
     */
    protected $VATRegNo;
    /**
     * @var string
     */
    protected $PostCode;
    /**
     * @var string
     */
    protected $Email;
    /**
     * @var string
     */
    protected $FirstName;
    /**
     * @var string
     */
    protected $MiddleName;
    /**
     * @var string
     */
    protected $Surname;
    /**
     * @var string
     */
    protected $MobPhoneNo;
    /**
     * @var string
     */
    protected $CorrType;
    /**
     * @var string
     */
    protected $EnterpriseNo;
    /**
     * @var string
     */
    protected $HuurderOmniboxx;
    /**
     * @var string
     */
    protected $EigenaarHBV;
    /**
     * @var string
     */
    protected $KVK;
	/**
	 * @var string
	 */
    protected $LastDateModified;
    /**
     * @var string
     */
    protected $EmailOuders;

    /**
     * Contact constructor.
     * @param string $No
     */
    public function __construct($No)
	{
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getNo()
    {
        return $this->No;
    }

    /**
     * @param string $No
     */
    public function setNo($No)
    {
        $this->No = $No;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->Name;
    }

    /**
     * @param string $Name
     */
    public function setName($Name)
    {
        $this->Name = $Name;
    }

    /**
     * @return string
     */
    public function getName2()
    {
        return $this->Name2;
    }

    /**
     * @param string $Name2
     */
    public function setName2($Name2)
    {
        $this->Name2 = $Name2;
    }

    /**
     * @return string
     */
    public function getAddress()
    {
        return $this->Address;
    }

    /**
     * @param string $Address
     */
    public function setAddress($Address)
    {
        $this->Address = $Address;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->City;
    }

    /**
     * @param string $City
     */
    public function setCity($City)
    {
        $this->City = $City;
    }

    /**
     * @return string
     */
    public function getPhoneNo()
    {
        return $this->PhoneNo;
    }

    /**
     * @param string $PhoneNo
     */
    public function setPhoneNo($PhoneNo)
    {
        $this->PhoneNo = $PhoneNo;
    }

    /**
     * @return string
     */
    public function getCurrencyCode()
    {
        return $this->CurrencyCode;
    }

    /**
     * @param string $CurrencyCode
     */
    public function setCurrencyCode($CurrencyCode)
    {
        $this->CurrencyCode = $CurrencyCode;
    }

    /**
     * @return string
     */
    public function getLanguageCode()
    {
        return $this->LanguageCode;
    }

    /**
     * @param string $LanguageCode
     */
    public function setLanguageCode($LanguageCode)
    {
        $this->LanguageCode = $LanguageCode;
    }

    /**
     * @return string
     */
    public function getRegionCode()
    {
        return $this->RegionCode;
    }

    /**
     * @param string $RegionCode
     */
    public function setRegionCode($RegionCode)
    {
        $this->RegionCode = $RegionCode;
    }

    /**
     * @return string
     */
    public function getVATRegNo()
    {
        return $this->VATRegNo;
    }

    /**
     * @param string $VATRegNo
     */
    public function setVATRegNo($VATRegNo)
    {
        $this->VATRegNo = $VATRegNo;
    }

    /**
     * @return string
     */
    public function getPostCode()
    {
        return $this->PostCode;
    }

    /**
     * @param string $PostCode
     */
    public function setPostCode($PostCode)
    {
        $this->PostCode = $PostCode;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->Email;
    }

    /**
     * @param string $Email
     */
    public function setEmail($Email)
    {
        $this->Email = $Email;
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->FirstName;
    }

    /**
     * @param string $FirstName
     */
    public function setFirstName($FirstName)
    {
        $this->FirstName = $FirstName;
    }

    /**
     * @return string
     */
    public function getMiddleName()
    {
        return $this->MiddleName;
    }

    /**
     * @param string $MiddleName
     */
    public function setMiddleName($MiddleName)
    {
        $this->MiddleName = $MiddleName;
    }

    /**
     * @return string
     */
    public function getSurname()
    {
        return $this->Surname;
    }

    /**
     * @param string $Surname
     */
    public function setSurname($Surname)
    {
        $this->Surname = $Surname;
    }

    /**
     * @return string
     */
    public function getMobPhoneNo()
    {
        return $this->MobPhoneNo;
    }

    /**
     * @param string $MobPhoneNo
     */
    public function setMobPhoneNo($MobPhoneNo)
    {
        $this->MobPhoneNo = $MobPhoneNo;
    }

    /**
     * @return string
     */
    public function getCorrType()
    {
        return $this->CorrType;
    }

    /**
     * @param string $CorrType
     */
    public function setCorrType($CorrType)
    {
        $this->CorrType = $CorrType;
    }

    /**
     * @return string
     */
    public function getEnterpriseNo()
    {
        return $this->EnterpriseNo;
    }

    /**
     * @param string $EnterpriseNo
     */
    public function setEnterpriseNo($EnterpriseNo)
    {
        $this->EnterpriseNo = $EnterpriseNo;
    }

    /**
     * @return string
     */
    public function getHuurderOmniboxx()
    {
        return $this->HuurderOmniboxx;
    }

    /**
     * @param string $HuurderOmniboxx
     */
    public function setHuurderOmniboxx($HuurderOmniboxx)
    {
        $this->HuurderOmniboxx = $HuurderOmniboxx;
    }

    /**
     * @return string
     */
    public function getEigenaarHBV()
    {
        return $this->EigenaarHBV;
    }

    /**
     * @param string $EigenaarHBV
     */
    public function setEigenaarHBV($EigenaarHBV)
    {
        $this->EigenaarHBV = $EigenaarHBV;
    }

    /**
     * @return string
     */
    public function getKVK()
    {
        return $this->KVK;
    }

    /**
     * @param string $KVK
     */
    public function setKVK($KVK)
    {
        $this->KVK = $KVK;
    }

	/**
	 * @return string
	 */
	public function getLastDateModified()
	{
		return $this->LastDateModified;
	}

	/**
	 * @param string $LastDateModified
	 */
	public function setLastDateModified($LastDateModified)
	{
		$this->LastDateModified = $LastDateModified;
	}

    /**
     * @inheritDoc
     */
    public function getEmailOuders()
    {
        return $this->EmailOuders;
    }

    /**
     * @inheritDoc
     */
    public function setEmailOuders($EmailOuders)
    {
        $this->EmailOuders = $EmailOuders;
    }

	/**
	 * {@inheritdoc}
	 */
	public function set($key, $value) {
		$keys = array_keys(get_object_vars($this));
		if (in_array($key, $keys, true)) {
			$this->$key = $value;
		} else {
			throw new \InvalidArgumentException('Invalid parameter key for type Contact: ' . $key);
		}
	}

    /**
     * {@inheritdoc}
     */
    public function validate() {
        $assertHelper = new AssertHelper();
        $assertHelper->assertMaxLength([
            10 => [
                'CurrencyCode' => $this->getCurrencyCode(),
                'LanguageCode' => $this->getLanguageCode(),
                'RegionCode' => $this->getRegionCode(),
            ],
            20 => [
                'No' => $this->getNo(),
                'VATRegNo' => $this->getVATRegNo(),
                'Postcode' => $this->getPostCode(),
            ],
            30 => [
                'City' => $this->getCity(),
                'PhoneNo' =>$this->getPhoneNo(),
                'FirstName' => $this->getFirstName(),
                'MiddleName' => $this->getMiddleName(),
                'Surname' => $this->getSurname(),
                'MobPhoneNo' => $this->getMobPhoneNo(),
                'KVK' => $this->getKVK(),
            ],
            50 => [
                'Name' => $this->getName(),
                'Name2' => $this->getName2(),
                'EnterpriseNo' => $this->getEnterpriseNo(),
            ],
            80 => [
                'Email' => $this->getEmail(),
            ],
        ])
            ->that($this->getCorrType(), 'CorrType')->nullOr()->inArray([
            	'',
				0,
				1,
				2,
				3,
				' ',
				'Hard Copy',
				'Email',
				'Fax'
			])
            ->verifyNow();
    }
}
