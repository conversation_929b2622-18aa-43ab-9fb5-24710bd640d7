<?php

namespace HBSoftware\Domain\Factory\Eenheid;

use HBSoftware\Domain\Model\Eenheid\Eenheid;

class EenheidFactory
{
    /**
     * @param $params
     *   Input parameters from the Omniboxx DB.
     *
     * @return Eenheid
     *   Returns an Eenheid object.
     */
    public function build($params) {
        /*
         * @TODO
         *
         * Replace placeholder eenheid with functionality to create the appropriate Eenheid object from the Omniboxx DB
         */
        $eenheid = [
            'Nr' => 'ABCDEF_00_01',
            'Omschrijving' => 'Test',
            'Adres' => '',
            'Woonplaats' => 'asdf',
            'Aanmaakdatum' => '2019-06-11T12:28:47+02:00',
            'LaatsteWijziging' => '2019-06-11T12:28:47+02:00',
            'RegionCode' => '',
            'Objectsoort' => '999',
            'Postcode' => '',
			'VVOm2' => 0,
            'ComplexNr' => 'P_MOLEN_00.00',
            'Status' => ' ',
            'Huisnummer' => '',
            'BVO' => 0,
            'Energielabel' => '',
            'ObjectgroepOmniboxx' => '',
			'OmniboxxID' => 0
		];
        return $this->createEenheidFromArray($eenheid);
    }

	/**
	 * Static function to create a Eenheid-object from an array of parameters.
	 *
	 * @param array $data
	 * @return Eenheid
	 */
	private function createEenheidFromArray(array $data) {
		if (!isset($data['Nr'])) {
			throw new \InvalidArgumentException('Missing Nr parameter for type Eenheid');
		}
		$eenheid = new Eenheid(
			$data['Nr']
		);
		foreach ($data as $key => $value) {
			$eenheid->set($key, $value);
		}
		return $eenheid;
	}
}
