<?php

namespace HBSoftware\Domain\Factory\Contact;

use HBSoftware\Domain\Model\Contact\Contact;

class ContactFactory
{
    /**
     * @param array $params
     *   Input parameters from the Omniboxx DB.
     *
     * @return Contact
     *   Returns a Contact object.
     */
    public function build($params) {
        $emailAddress = !empty($params['emailData']['default']['address']) ?
            $params['emailData']['default']['address'] :
            $params['emailData']['private']['address'];
        $address = $params['address'] ? $params['address']['address'] . ' ' . $params['address']['number'] : '';
        $data = [
			'No' => $params['general']['olddebtorcode'] ?: ('CO' . $params['general']['id']),
			'Name' => ($params['general']['firstname'] ?: '') . ' ' . ($params['general']['name'] ?: ''),
			'Address' => $address,
			'City' => $params['address']['city'] ?: '',
			'PhoneNo' => $params['address']['phone1'] ?: '',
			'CurrencyCode' => '',
			'LanguageCode' => $this->convertLanguage($params['general']['language']),
            'RegionCode' => $params['address']['countryCode'] ?: '',
			'PostCode' => $params['address']['zipcode'] ?: '',
			'Email' => $emailAddress ?: '',
            'EmailOuders' => $params['emailData']['parent']['address'] ?: '',
			'FirstName' => $params['general']['firstname'] ?: '',
			'MiddleName' => $params['general']['middlename'] ?: '',
			'Surname' => $params['general']['name'] ?: '',
			'MobPhoneNo' => $params['address']['phone2'] ?: '',
			'HuurderOmniboxx' => $params['general']['type'] === 'tenant',
			'EigenaarHBV' => $params['general']['type'] === 'investor',
			'KVK' => $params['general']['kvk'] ?: ''
		];

        $data['Name'] = convertToUtf8($data['Name']);
        $data['Address'] = convertToUtf8($data['Address']);
        $data['City'] = convertToUtf8($data['City']);
        $data['FirstName'] = convertToUtf8($data['FirstName']);
        $data['MiddleName'] = convertToUtf8($data['MiddleName']);
        $data['Surname'] = convertToUtf8($data['Surname']);

        $data['No'] = $this->trimStringByLength($data['No'], 20);
        $data['Name'] = $this->trimStringByLength($data['Name'],50);
        $data['Address'] = $this->trimStringByLength($data['Address'], 50);
        $data['City'] = $this->trimStringByLength($data['City'], 30);
        $data['PhoneNo'] = $this->trimStringByLength($data['PhoneNo'], 30);
        $data['CurrencyCode'] = $this->trimStringByLength($data['CurrencyCode'], 10);
        $data['LanguageCode'] = $this->trimStringByLength($data['LanguageCode'], 10);
        $data['RegionCode'] = $this->trimStringByLength($data['RegionCode'], 10);
        $data['PostCode'] = $this->trimStringByLength($data['PostCode'], 20);
        $data['Email'] = $this->trimStringByLength($data['Email'], 80);
        $data['EmailOuders'] = $this->trimStringByLength($data['EmailOuders'], 80);
        $data['FirstName'] = $this->trimStringByLength($data['FirstName'], 30);
        $data['MiddleName'] = $this->trimStringByLength($data['MiddleName'], 30);
        $data['Surname'] = $this->trimStringByLength($data['Surname'], 30);
        $data['MobPhoneNo'] = $this->trimStringByLength($data['MobPhoneNo'], 30);
        $data['KVK'] = $this->trimStringByLength($data['KVK'],30);

		return $this->createContactFromArray($data);
    }

    private function trimStringByLength($value, $width)
    {
        return mb_strimwidth($value, 0, $width, '...');
    }

	/**
	 * Static function to create a Contact-object from an array of parameters.
	 *
	 * @param array $data
	 * @return Contact
	 */
	private function createContactFromArray(array $data) {
		if (!isset($data['No'])) {
			throw new \InvalidArgumentException('Missing "No" parameter for type Contact');
		}
		$contact = new Contact($data['No']);

		foreach ($data as $key => $value) {
			$contact->set($key, $value);
		}
		return $contact;
	}

    /**
     * Convert Omniboxx language code to HB language code
     *
     * @param $languageCode
     * @return mixed|string
     */
    private function convertLanguage($languageCode)
    {
        $languages = [
            'nl' => 'NLB', //Nederlands - België
            'fr' => 'FRB', //Frans - België
            'en' => 'ENB', //Engels
        ];
        return isset($languages[$languageCode]) ? $languages[$languageCode] : $languages['nl'];
    }
}
