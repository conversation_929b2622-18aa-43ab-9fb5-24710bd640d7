<?php

namespace HBSoftware\Domain\Factory\Contact;

use DI\Container;
use HBSoftware\Domain\Model\Contact\ContactRepositoryFactoryInterface;
use HBSoftware\Domain\Model\Contact\ContactRepository;
use HBSoftware\Infrastructure\Service\Contact\GetEmailAddressDataService;
use HBSoftware\Infrastructure\Service\Contact\GetUserDataServiceFactory;
use HBSoftware\Infrastructure\Service\Contact\GetAddressDataServiceFactory;
use HBSoftware\Application\Domain\ContactService;
use HBSoftware\Infrastructure\Service\Contact\MapCountryToHBCountryService;

class ContactRepositoryFactory implements ContactRepositoryFactoryInterface
{
    /**
     * @var ContactFactory
     */
    private $contactFactory;
    /**
     * @var GetUserDataServiceFactory
     */
    private $getUserDataServiceFactory;
    /**
     * @var GetAddressDataServiceFactory
     */
    private $getAddressDataServiceFactory;

    /**
     * @var GetEmailAddressDataService $getEmailAddressDataService
     */
    private $getEmailAddressDataService;
    /**
     * @var ContactService
     */
    private $contactService;

    /**
     * @var Container
     */
    private $container;

    /**
     * @var MapCountryToHBCountryService
     */
    private $mapCountryToHBCountryService;

    public function __construct(
        ContactFactory $contactFactory,
        GetUserDataServiceFactory $getUserDataServiceFactory,
        GetAddressDataServiceFactory $getAddressDataServiceFactory,
        GetEmailAddressDataService $getEmailAddressDataService,
        ContactService $contactService,
        Container $container,
        MapCountryToHBCountryService $mapCountryToHBCountryService
    ) {
        $this->contactFactory = $contactFactory;
        $this->getUserDataServiceFactory = $getUserDataServiceFactory;
        $this->getAddressDataServiceFactory = $getAddressDataServiceFactory;
        $this->getEmailAddressDataService = $getEmailAddressDataService;
        $this->contactService = $contactService;
        $this->container = $container;
        $this->mapCountryToHBCountryService = $mapCountryToHBCountryService;
    }
    /**
     * {@inheritDoc}
     */
    public function build()
    {
        return new ContactRepository(
            $this->contactFactory,
            $this->getUserDataServiceFactory->build(),
            $this->getAddressDataServiceFactory->build(),
            $this->getEmailAddressDataService,
            $this->contactService,
            $this->container,
            $this->mapCountryToHBCountryService
        );
    }
}
