<?php

namespace HBSoftware\Domain\Factory\Invoice;

use HBSoftware\Domain\Model\Invoice\Invoice;

class InvoiceFactory
{
    /**
     * @param $params
     *   Input parameters from the Omniboxx DB.
     *
     * @return Invoice
     *   Returns an Invoice object.
     */
    public function build($params) {
        $postingDate = $params['invoiceRun']['date'];
        $dueDate = $params['invoiceRun']['expire_date'];
        $identifier = $params['invoiceRun']['identifier'] ?: '';
        $postingDescription = $params['invoice']['postingDescription'] ?: $identifier;
        $postingDescription = mb_strimwidth($postingDescription, 0, 50, '...');
        $data = [
        	'No' => $params['invoice']['financialIdentifier'],
            'CustomerNo' => $params['customer']['general']['olddebtorcode'] ?: '',
            'PostingDate' => $postingDate ? $this->formatDate($postingDate) : '',
            'PostingDescription' => $postingDescription,
            'DueDate' => $dueDate ? $this->formatDate($dueDate) : '',
            'DocumentDate' => $postingDate ? $this->formatDate($postingDate) : '',
            'ComplexNr' => $params['project']['external_id'] ?: '',
            'EenheidNr' => $params['object']['lot'] ?: '',
            'StructMed' => $params['invoice']['description'] ?: '',
            'CustomerName' => $this->trimStringByLength($params['customer']['general']['rendered_name'], 80)
		];
        if ($params['invoice']['paymentPreference'] === 'INCASSO') {
            $data['Betaalwijze'] = 'INCASSO'; // max length 10
        }

        $data['No'] = $this->trimStringByLength($data['No'], 20);
        $data['CustomerNo'] = $this->trimStringByLength($data['CustomerNo'], 20);
        $data['PostingDescription'] = $this->trimStringByLength($data['PostingDescription'], 50);
        $data['ComplexNr'] = $this->trimStringByLength($data['ComplexNr'], 20);
        $data['EenheidNr'] = $this->trimStringByLength($data['EenheidNr'], 20);
        $data['StructMed'] = $this->trimStringByLength($data['StructMed'], 30);

        return $this->createInvoiceFromArray($data);
    }

    private function trimStringByLength($value, $width)
    {
        return mb_strimwidth($value, 0, $width, '...');
    }

	/**
	 * Static function to create a Invoice-object from an array of parameters.
	 *
	 * @param array $data
	 * @return Invoice
	 */
	private function createInvoiceFromArray(array $data) {
		if (!isset($data['No'])) {
			throw new \InvalidArgumentException('Missing "No" parameter for type Invoice');
		}
		$invoice = new Invoice($data['No']);
		foreach ($data as $key => $value) {
			$invoice->set($key, $value);
		}
		return $invoice;
	}

	private function formatDate($date) {
	    return date('Y-m-d\TH:i:sP', strtotime($date . ' + 12 hours'));
    }
}
