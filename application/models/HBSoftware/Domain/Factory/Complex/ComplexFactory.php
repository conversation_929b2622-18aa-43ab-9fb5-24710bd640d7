<?php

namespace HBSoftware\Domain\Factory\Complex;

use HBSoftware\Domain\Model\Complex\Complex;

class ComplexFactory
{
    /**
     * @param $params
     *   Input parameters from the Omniboxx DB.
     *
     * @return Complex
     *   Returns a Complex object.
     */
    public function build($params) {
        /*
         * @TODO
         *
         * Replace placeholder complex with functionality to create the appropriate Complex object from the Omniboxx DB
         */

		$data = [
			'Nr' => 'TEST123',
			'Omschrijving' => 'Achiel Apartetf',
			'Postcode' => '9000',
			'Plaats' => 'Gent',
			'Locatie' => '',
			'DatumOplevering' => '2019-06-11T12:28:47+02:00',
			'GemaaktOp' => '2019-06-11T12:28:47+02:00',
			'Regiocode' => '',
			'Administratie' => '',
			'TotaalComplex' => 0,
			'AankoopbedragExclBtw' => 0,
			'Provincie' => 'Oost-Vlaanderen',
			'OmniboxxID' => 0,
			'Status' => 'Lead'
		];

        return $this->createComplexFromArray($data);
    }

	/**
	 * Static function to create a Complex-object from an array of parameters.
	 *
	 * @param array $data
	 * @return Complex
	 */
	private function createComplexFromArray(array $data) {
		if (!isset($data['Nr'])) {
			throw new \InvalidArgumentException('Missing Nr parameter for type Complex');
		}
		$complex = new Complex($data['Nr']);
		foreach ($data as $key => $value) {
			$complex->set($key, $value);
		}
		return $complex;
	}
}
