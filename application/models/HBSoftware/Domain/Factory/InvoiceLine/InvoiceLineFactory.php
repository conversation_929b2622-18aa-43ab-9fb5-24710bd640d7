<?php

namespace HBSoftware\Domain\Factory\InvoiceLine;

use HBSoftware\Domain\Model\InvoiceLine\InvoiceLine;

class InvoiceLineFactory
{
    /**
     * @param $params
     *   Input parameters from the Omniboxx DB.
     *
     * @return InvoiceLine
     *   Returns an InvoiceLine object.
     */
    public function build($params) {
        $data = [
            'Document_No' => $params['financialIdentifier'] ?: '',
            'Type' => '1',
            'No' => $params['ledger'],
            'DescriptionOmniboxx' => $params['description'],
            'VAT' => $params['vat_percentage'] ? (int) $params['vat_percentage'] : 0,
            'Amount' => $params['amount_ex_vat'] ? (float) $params['amount_ex_vat'] : 0,
            'AmountInclVAT' => $params['amount_total'] ? (float) $params['amount_total'] : 0,
            'ComplexNr' => $params['external_id'],
            'EenheidNr' => $params['objectData']['lot'] ?: '',
        ];

        return $this->createInvoiceLineFromArray($data);
    }
	/**
	 * Static function to create an InvoiceLine-object from an array of parameters.
	 *
	 * @param array $data
	 * @return InvoiceLine
	 */
	private function createInvoiceLineFromArray(array $data) {
		if (!isset($data['No'])) {
			throw new \InvalidArgumentException('Missing "No" parameter for type InvoiceLine');
		}
		$invoiceLine = new InvoiceLine(
			$data['No']
		);
		foreach ($data as $key => $value) {
			$invoiceLine->set($key, $value);
		}
		return $invoiceLine;
	}
}
