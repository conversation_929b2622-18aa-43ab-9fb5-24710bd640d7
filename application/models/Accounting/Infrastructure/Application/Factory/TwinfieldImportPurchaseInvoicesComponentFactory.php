<?php

namespace Accounting\Infrastructure\Application\Factory;

use Accounting\Application\Factory\AbstractImportPurchaseInvoicesComponentFactory;
use Accounting\Infrastructure\Twinfield\Domain\Factory\PurchaseInvoiceFactory;
use Accounting\Infrastructure\Twinfield\Domain\Factory\PurchaseInvoiceLineFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\DetailLineFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\HeaderFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\PurchaseInvoiceFactory as TwinfieldPurchaseInvoiceFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\TotalLineFactory;
use Accounting\Infrastructure\Twinfield\Factory\PurchaseInvoice\VatLineFactory;
use Accounting\Infrastructure\Twinfield\Gateway\PurchaseInvoiceGateway;
use Accounting\Infrastructure\Twinfield\Service\DownloadAttachmentService;
use Accounting\Infrastructure\Twinfield\Service\RetrievePurchaseInvoiceService;

class TwinfieldImportPurchaseInvoicesComponentFactory extends AbstractImportPurchaseInvoicesComponentFactory
{
    public function retrieveThirdPartyPurchaseInvoicesService()
    {
        $gateway = new PurchaseInvoiceGateway();
        $purchaseInvoiceFactory = $this->makeTwinfieldPurchaseInvoiceFactory();
        return new RetrievePurchaseInvoiceService($gateway, $purchaseInvoiceFactory);
    }

    public function newThirdPartyPurchaseInvoiceFilter($administrationCode = null)
    {
        $settings = [
            'service_cost_filter' => \Settings::get('import_third_party_purchase_invoices_service_cost_filter')
        ];

        return $this->newThirdPartyPurchaseInvoiceFilterFactory->build($settings);
    }

    public function purchaseInvoiceFactory()
    {
        return new PurchaseInvoiceFactory(
            new PurchaseInvoiceLineFactory(),
            new DownloadAttachmentService()
        );
    }

    private function makeTwinfieldPurchaseInvoiceFactory()
    {
        return new TwinfieldPurchaseInvoiceFactory(
            new HeaderFactory(),
            new TotalLineFactory(),
            new DetailLineFactory(),
            new VatLineFactory()
        );
    }
}
