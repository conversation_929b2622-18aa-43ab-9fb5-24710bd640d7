<?php

namespace Accounting\Infrastructure\Application\Factory;

use Accounting\Application\Factory\AbstractImportPurchaseInvoicesComponentFactory;
use Accounting\Infrastructure\ExactOnline\Domain\Factory\EntryPurchaseInvoiceFactory;
use Accounting\Infrastructure\ExactOnline\Domain\Factory\EntryPurchaseInvoiceLineFactory;
use Accounting\Infrastructure\ExactOnline\Factory\AccountFactory;
use Accounting\Infrastructure\ExactOnline\Factory\Document\DocumentFactory;
use Accounting\Infrastructure\ExactOnline\Factory\PurchaseEntry\PurchaseEntryFactory;
use Accounting\Infrastructure\ExactOnline\Factory\PurchaseEntry\PurchaseEntryLineFactory;
use Accounting\Infrastructure\ExactOnline\Gateway\ExactOnlineConnection;
use Accounting\Infrastructure\ExactOnline\Gateway\PurchaseEntryGateway;
use Accounting\Infrastructure\ExactOnline\Service\RetrievePurchaseEntriesService;

class EntryExactOnlineImportPurchaseInvoicesComponentFactory extends AbstractImportPurchaseInvoicesComponentFactory
{
    public function retrieveThirdPartyPurchaseInvoicesService()
    {
        return new RetrievePurchaseEntriesService(
            $this->makeGateway(),
            $this->makeFactory()
        );
    }

    public function newThirdPartyPurchaseInvoiceFilter($administrationCode = null)
    {
        if (\Settings::get('general_company_shortname') === 'segesta') {
            // service cost filter is applied to any other administration than 22
            $settings = [
                'service_cost_filter' => $administrationCode != 22
            ];
        } else {
            $settings = [
                'service_cost_filter' => \Settings::get('import_third_party_purchase_invoices_service_cost_filter')
            ];
        }

        return $this->newThirdPartyPurchaseInvoiceFilterFactory->build($settings);
    }

    public function purchaseInvoiceFactory()
    {
        return new EntryPurchaseInvoiceFactory(
            new EntryPurchaseInvoiceLineFactory()
        );
    }

    private function makeGateway()
    {
        return new PurchaseEntryGateway(
            new ExactOnlineConnection()
        );
    }

    private function makeFactory()
    {
        return new PurchaseEntryFactory(
            new PurchaseEntryLineFactory(),
            new DocumentFactory(),
            new AccountFactory()
        );
    }
}
