<?php

namespace Accounting\Infrastructure\Application\Factory;

use Accounting\Application\Factory\AbstractImportPurchaseInvoicesComponentFactory;
use Accounting\Infrastructure\ExactOnline\Domain\Factory\PurchaseInvoiceFactory as DomainPurchaseInvoiceFactory;
use Accounting\Infrastructure\ExactOnline\Domain\Factory\PurchaseInvoiceLineFactory as DomainPurchaseInvoiceLineFactory;
use Accounting\Infrastructure\ExactOnline\Factory\Document\DocumentFactory;
use Accounting\Infrastructure\ExactOnline\Factory\PurchaseInvoice\PurchaseInvoiceFactory;
use Accounting\Infrastructure\ExactOnline\Factory\PurchaseInvoice\PurchaseInvoiceLineFactory;
use Accounting\Infrastructure\ExactOnline\Gateway\ExactOnlineConnection;
use Accounting\Infrastructure\ExactOnline\Gateway\PurchaseInvoiceGateway;
use Accounting\Infrastructure\ExactOnline\Service\RetrievePurchaseInvoiceService;

class ExactOnlineImportPurchaseInvoicesComponentFactory extends AbstractImportPurchaseInvoicesComponentFactory
{
    public function retrieveThirdPartyPurchaseInvoicesService()
    {
        return new RetrievePurchaseInvoiceService(
            $this->makeGateway(),
            $this->makeFactory()
        );
    }

    public function newThirdPartyPurchaseInvoiceFilter($administrationCode = null)
    {
        $settings = [
            'service_cost_filter' => \Settings::get('import_third_party_purchase_invoices_service_cost_filter')
        ];

        return $this->newThirdPartyPurchaseInvoiceFilterFactory->build($settings);
    }

    public function purchaseInvoiceFactory()
    {
        return new DomainPurchaseInvoiceFactory(
            new DomainPurchaseInvoiceLineFactory()
        );
    }

    private function makeGateway()
    {
        return new PurchaseInvoiceGateway(
            new ExactOnlineConnection()
        );
    }

    private function makeFactory()
    {
        return new PurchaseInvoiceFactory(
            new DocumentFactory(),
            new PurchaseInvoiceLineFactory()
        );
    }
}
