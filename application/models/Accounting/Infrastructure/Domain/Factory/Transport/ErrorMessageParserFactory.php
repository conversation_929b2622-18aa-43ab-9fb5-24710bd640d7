<?php

namespace Accounting\Infrastructure\Domain\Factory\Transport;

use Accounting\Domain\Factory\Transport\ErrorMessageParserFactory as ErrorMessageParserFactoryInterface;
use Accounting\Infrastructure\Domain\Model\Transport\Afas_ApiTransport;
use Accounting\Infrastructure\Domain\Model\Transport\PassthroughErrorMessageParser;
use ExactXml\ExactOnlineErrorMessageParser;
use Accounting\Infrastructure\Domain\Model\Transport\JsonErrorMessageParser;
use Yuki\Infrastructure\Service\YukiErrorMessageParser;

class ErrorMessageParserFactory implements ErrorMessageParserFactoryInterface {

	/**
	 * @inheritdoc
	 */
	public function build($activeFinancialExport) {
		switch ($activeFinancialExport) {
			case ('twinfield'):
				return new \Twinfield();
				break;

			case ('afas'):
				return new Afas_ApiTransport();
				break;

			case ('exactonline'):
				return new ExactOnlineErrorMessageParser();
				break;

			case ('yuki'):
				return new YukiErrorMessageParser();
				break;

            case ('hb'):
                return new JsonErrorMessageParser();
                break;

			default:
				return new PassthroughErrorMessageParser();
		}
	}
}
