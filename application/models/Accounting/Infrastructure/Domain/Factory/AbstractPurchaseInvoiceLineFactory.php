<?php

namespace Accounting\Infrastructure\Domain\Factory;

abstract class AbstractPurchaseInvoiceLineFactory
{
    protected $notifications = [];

    protected function tryFindServiceCharge($componentId = null)
    {
        if (null === $componentId) {
            return null;
        }

        $component = db()->select()
            ->from('components', 'service_charge')
            ->where('id = ?', $componentId)
            ->query()
            ->fetch();

        return $component['service_charge'];
    }

    protected function tryFindComponentOrNull($ledger = null, $percentage = null)
    {
        if (null === $ledger || null === $percentage) {
            $this->addNotification('- Component voor factuurregel niet gevonden; Grootboeknummer en of btw percentage ontbreekt');
            return null;
        }

        $taxRate = $this->convertTaxPercentageToTaxRate($percentage);

        if (null === $taxRate) {
            $this->addNotification('- Component voor factuurregel niet gevonden; btw-percentage onbekend.');
            return null;
        }

        $components = db()->select()
            ->from('components', 'id')
            ->where('ledger = ?', $ledger)
            ->where('tax_rate = ?', $taxRate)
            ->where('deleted = ?', 0)
            ->query()
            ->fetchAll();

        if (count($components) > 1) {
            $this->addNotification('- Component voor factuurregel niet gevonden; Meerdere resultaten op basis van het grootboeknummer ' . $ledger . ' en het btw-percentage ' . $percentage);
            return null;
        }

        if (empty($components)) {
            $this->addNotification('- Component voor factuurregel niet gevonden; Geen component gevonden op basis van grootboeknummer ' . $ledger . ' en btw-percentage ' . $percentage);
            return null;
        }

        return $components[0]['id'];
    }

    protected function convertTaxPercentageToTaxRate($percentage)
    {
        $taxRates = [
            0 => 0,
            6 => 1,
            21 => 2,
            9 => 3
        ];

        return array_key_exists($percentage, $taxRates) ? $taxRates[$percentage] : null;
    }

    protected function tryFindCostObjects($costCenter, $corporationId)
    {
        if (!$costCenter) {
            $this->addNotification('- Geen objecten voor factuurregel gevonden; Kostenplaats ontbreekt' );
            return [];
        }

        if (!$corporationId) {
            $this->addNotification('- Geen objecten voor factuurregel gevonden; Juridische entiteit ontbreekt' );
            return [];
        }

        $select = db()->select()
            ->from(['o' => 'objects'], ['id'])
            ->joinLeft(['og' => 'objectgroup'], 'o.objectgroup = og.id', false)
            ->joinLeft(['p' => 'projects'], 'og.project = p.id', false)
            ->joinLeft(['c' => 'corporations'], 'p.corporation = c.id', false)
            ->where('c.id = ?', $corporationId)
            ->where(new \Zend_Db_Expr('(
                o.cost_center = ' . db()->quote($costCenter) . ' OR 
                og.cost_center = ' . db()->quote($costCenter) . ' OR 
                p.cost_center = ' . db()->quote($costCenter) . '
            )'))
            ->where('p.sales_status = ?', 'closed')
            ->where('p.exploitation = ?', 1);

        $objectIds = db()->fetchAll($select);

        if (empty($objectIds)) {
            $this->addNotification('- Geen objecten voor factuurregel gevonden; Op basis van kostenplaats' );
            return [];
        }

        return array_column($objectIds, 'id');
    }

    protected function addNotification($notification)
    {
        $this->notifications[] = $notification;
    }

    public function makeImportNotificationsAndClear()
    {
        $notifications = implode("<br>", $this->notifications);
        $this->notifications = []; // reset

        return $notifications;
    }
}
