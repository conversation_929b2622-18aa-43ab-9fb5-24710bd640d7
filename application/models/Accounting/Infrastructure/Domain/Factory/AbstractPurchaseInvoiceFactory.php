<?php

namespace Accounting\Infrastructure\Domain\Factory;

use Accounting\Domain\Factory\PurchaseInvoiceFactory;

abstract class AbstractPurchaseInvoiceFactory implements PurchaseInvoiceFactory
{
    protected $notifications;

    protected function findCompanyOrNull($financialRelationCode)
    {
        if (!$financialRelationCode) {
            return null;
        }

        if (\Settings::get('general_company_shortname') === 'segesta') {
            $financialRelationCode = ltrim($financialRelationCode, '0');
        }

        $usersRow = (new \Users())->fetchRow(['olddebtorcode = ?' => $financialRelationCode]);

        if (null === $usersRow) {
            $this->addImportNotification('- Leverancier niet gevonden op basis van relatiecode. ' . $financialRelationCode);
            return null;
        }

        $companyRow = (new \Company())->fetchRow(['dummy_user = ?' => $usersRow->id]);

        if (null === $companyRow) {
            $this->addImportNotification('- Leverancier niet gevonden op basis van relatiecode. ' . $financialRelationCode);
            return null;
        }

        return $companyRow->id;
    }

    protected function findCorporationIdOrNull($corporationIds, $projectId = null)
    {
        if (count($corporationIds) === 1) {
            return $corporationIds[0];
        }

        if ($projectId) {
            return db()->select()
                ->from('projects', 'corporation')
                ->where('id = ?', $projectId)
                ->query()
                ->fetchColumn();
        }

        $this->addImportNotification("- Meerdere juridische entiteiten bij administratie; vereist handmatige controle.");

        return null;
    }

    protected function addImportNotification($notification)
    {
        $this->notifications[] = $notification;
    }

    protected function makeImportNotificationsAndClear()
    {
        $notifications = implode("\n\n", $this->notifications);
        $this->notifications = []; // reset

        return $notifications;
    }

    protected function findProjectIds($costCenter, $corporationIds)
    {
        if (!$costCenter || empty($corporationIds)) {
            return null;
        }

        $projectRows = db()->select()
            ->from(['o' => 'objects'], false)
            ->joinLeft(['og' => 'objectgroup'], 'o.objectgroup = og.id', false)
            ->joinLeft(['p' => 'projects'], 'og.project = p.id', ['id'])
            ->joinLeft(['c' => 'corporations'], 'p.corporation = c.id', false)
            ->where(new \Zend_Db_Expr('(
                o.cost_center = ' . db()->quote($costCenter) . ' OR 
                og.cost_center = ' . db()->quote($costCenter) . ' OR 
                p.cost_center = ' . db()->quote($costCenter) . '
            )'))
            ->where('p.corporation IN(?)', $corporationIds)
            ->where('p.sales_status = ?', 'closed')
            ->where('p.exploitation = ?', 1)
            ->group('p.id')
            ->query()
            ->fetchAll();

        return array_column($projectRows, 'id');
    }
}
