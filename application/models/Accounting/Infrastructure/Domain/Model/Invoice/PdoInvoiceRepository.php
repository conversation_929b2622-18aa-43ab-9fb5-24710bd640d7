<?php

namespace Accounting\Infrastructure\Domain\Model\Invoice;

use Accounting\Domain\Model\Invoice\InvoiceRepository;

class PdoInvoiceRepository implements InvoiceRepository
{
	public function findAllByRunId($aRunId)
    {
		$invoiceIds = db()->select()
			->from('invoices', ['id'])
			->where('run = ?', $aRunId)
			->where('financial_no_sync != ?', 1)
			->where('financial_invoice_id IS NULL')
			->query()
			->fetchAll();

		return array_column($invoiceIds, 'id');
    }

    public function findAllDebugByRunId($aRunId)
    {
        $invoiceIds = db()->select()
            ->from('invoices', ['id'])
            ->where('run = ?', $aRunId)
            ->query()
            ->fetchAll();

        return array_column($invoiceIds, 'id');
    }


}
