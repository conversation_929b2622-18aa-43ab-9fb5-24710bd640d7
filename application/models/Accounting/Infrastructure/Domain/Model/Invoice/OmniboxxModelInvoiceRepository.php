<?php

namespace Accounting\Infrastructure\Domain\Model\Invoice;

use Invoices;

/**
 * Class OmniboxxModelInvoiceRepository
 * @package Accounting\Infrastructure\Domain\Model\Invoice
 */
class OmniboxxModelInvoiceRepository
{
    /**
     * @var Invoices
     */
    private $invoiceModel;

    public function __construct(Invoices $invoiceModel) {
        $this->invoiceModel = $invoiceModel;
    }

    public function findByFinancialId($financialInvoiceId) {
        return $this->invoiceModel->genMatchSelect(['financial_invoice_id' => $financialInvoiceId])
            ->query()
            ->fetchObject();
    }

    public function findById($invoiceId) {
        return $this->invoiceModel->fetchRowById($invoiceId);
    }

    public function persist(\Zend_Db_Table_Row_Abstract $row) {
        return $row->save();
    }
}
