<?php

namespace Accounting\Infrastructure\Domain\Model\Customer;

use Accounting\Domain\Model\Customer\CustomerRepository;

class PdoCustomerRepository implements CustomerRepository
{
	public function findByInvoices($invoiceIds) {

		$customers = db()->select()
			->from(['i' => 'invoices'], false)
			->joinLeft(['uo' => 'users_objects'], 'uo.id = i.users_objects', ['customer'])
			->where('i.id IN(' . implode_for_where_in($invoiceIds) . ')')
			->query()
			->fetchAll();

		return array_column($customers, 'customer');
    }
}
