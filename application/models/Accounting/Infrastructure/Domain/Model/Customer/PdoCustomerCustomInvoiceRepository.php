<?php

namespace Accounting\Infrastructure\Domain\Model\Customer;

use Accounting\Domain\Model\Customer\CustomerRepository;

class PdoCustomerCustomInvoiceRepository implements CustomerRepository {

	/**
	 * @param $invoiceIds array
	 * @return mixed
	 */
	public function findByInvoices($invoiceIds) {

		$customers = db()->select()
			->from(['i' => 'invoices'], false)
			->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', ['ic_user' => 'ic.user'])
			->joinLeft(['in' => 'investor'], 'in.id = i.investor', ['in_user' => 'in.user'])
			->joinLeft(['c' => 'company'], 'c.id = ic.contact', ['c_user' => 'c.dummy_user'])
			->where('i.id IN(' . implode_for_where_in($invoiceIds) . ')')
			->group('ic.id')
			->query()
			->fetchAll();

		$output = [];
		foreach ($customers as $customer) {
			if (intval($customer['ic_user']) > 0) {
				$output[] = $customer['ic_user'];
			} else if (intval($customer['in_user']) > 0) {
				$output[] = $customer['in_user'];
			} else if (intval($customer['c_user']) > 0) {
				$output[] = $customer['c_user'];
			}
		}

		return $output;
	}
}
