<?php

namespace Accounting\Application\Factory;

use Accounting\Domain\Factory\NewThirdPartyPurchaseInvoiceFilterFactory;
use Accounting\Domain\Factory\PurchaseInvoiceFactory;

use Accounting\Domain\Service\NewThirdPartyPurchaseInvoiceFilter;
use Accounting\Domain\Service\RetrieveThirdPartyPurchaseInvoiceService;
use Accounting\Infrastructure\Domain\Model\PurchaseInvoice\PurchaseInvoiceRepository;

abstract class AbstractImportPurchaseInvoicesComponentFactory
{
    protected $newThirdPartyPurchaseInvoiceFilterFactory;

    public function __construct(
        NewThirdPartyPurchaseInvoiceFilterFactory $newThirdPartyPurchaseInvoiceFilterFactory
    ) {
        $this->newThirdPartyPurchaseInvoiceFilterFactory = $newThirdPartyPurchaseInvoiceFilterFactory;
    }

    /**
     * @return RetrieveThirdPartyPurchaseInvoiceService
     */
    abstract public function retrieveThirdPartyPurchaseInvoicesService();

    /**
     * @return NewThirdPartyPurchaseInvoiceFilter
     */
    abstract public function newThirdPartyPurchaseInvoiceFilter($administrationCode = null);

    /**
     * @return PurchaseInvoiceFactory
     */
    abstract public function purchaseInvoiceFactory();

    /**
     * @return PurchaseInvoiceRepository
     */
    public function purchaseInvoiceRepository()
    {
        return new PurchaseInvoiceRepository();
    }
}
