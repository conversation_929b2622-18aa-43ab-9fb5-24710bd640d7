<?php

namespace Accounting\Application\Factory;

use Accounting\Application\Service\ImportThirdPartyPurchaseInvoicesService;
use Accounting\Infrastructure\Application\Factory\EntryExactOnlineImportPurchaseInvoicesComponentFactory;
use Accounting\Infrastructure\Application\Factory\TwinfieldImportPurchaseInvoicesComponentFactory;
use Accounting\Infrastructure\ExactOnline\Domain\Factory\ExactOnlineNewThirdPartyPurchaseInvoiceFilterFactory;
use Accounting\Infrastructure\Twinfield\Domain\Factory\TwinfieldNewThirdPartyPurchaseInvoiceFilterFactory;

class ImportThirdPartyPurchaseInvoicesServiceFactory
{
    public function build($financialExportSystem, $administrationCode = null)
    {
        switch ($financialExportSystem) {
            case 'exactonline':
                $newThirdPartyPurchaseInvoiceFilterFactory = new ExactOnlineNewThirdPartyPurchaseInvoiceFilterFactory();
                $componentFactory = new EntryExactOnlineImportPurchaseInvoicesComponentFactory(
                    $newThirdPartyPurchaseInvoiceFilterFactory
                );
                break;
            case 'twinfield':
                $newThirdPartyPurchaseInvoiceFilterFactory = new TwinfieldNewThirdPartyPurchaseInvoiceFilterFactory();
                $componentFactory = new TwinfieldImportPurchaseInvoicesComponentFactory(
                    $newThirdPartyPurchaseInvoiceFilterFactory
                );
                break;
            default:
                throw new \Exception('Invalid financial export system');
        }

        return new ImportThirdPartyPurchaseInvoicesService(
            $componentFactory->retrieveThirdPartyPurchaseInvoicesService(),
            $componentFactory->newThirdPartyPurchaseInvoiceFilter($administrationCode),
            $componentFactory->purchaseInvoiceFactory(),
            $componentFactory->purchaseInvoiceRepository()
        );
    }
}
