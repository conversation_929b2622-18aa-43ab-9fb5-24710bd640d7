<?php

namespace Accounting\Application\Service;

use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveThirdPartyOutstandingItemsService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveOutstandingItemsService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactFindInvoicesForSyncingService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactSyncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
use Accounting\Infrastructure\Domain\Model\Invoice\OmniboxxModelInvoiceRepository;
use Accounting\Infrastructure\Domain\Model\Corporation\CorporationRepository;
use Dompdf\Exception;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\Exception\NoThirdPartyTransactionLinesException;

class SyncOutStandingItemsService
{

    private $retrieveThirdPartyOutstandingItemsService;
    private $retrieveOutstandingItemsService;
    private $findInvoicesForSyncingService;
    private $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
    /**
     * @var OmniboxxModelInvoiceRepository
     */
    private $invoiceRepository;
    /**
     * @var CorporationRepository
     */
    private $corporationRepository;

    public function __construct(
        ExactRetrieveThirdPartyOutstandingItemsService $retrieveThirdPartyOutstandingItemsService,
        ExactRetrieveOutstandingItemsService $retrieveOutstandingItemsService,
        ExactFindInvoicesForSyncingService $findInvoicesForSyncingService,
        ExactSyncInvoicePaymentsWithThirdPartyInvoicePaymentsService $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService,
        OmniboxxModelInvoiceRepository $invoiceRepository,
        CorporationRepository $corporationRepository
    ) {
        $this->retrieveThirdPartyOutstandingItemsService = $retrieveThirdPartyOutstandingItemsService;
        $this->retrieveOutstandingItemsService = $retrieveOutstandingItemsService;
        $this->findInvoicesForSyncingService = $findInvoicesForSyncingService;
        $this->syncInvoicePaymentsWithThirdPartyInvoicePaymentsService =
            $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
        $this->invoiceRepository = $invoiceRepository;
        $this->corporationRepository = $corporationRepository;
    }

    public function execute($corporationId)
    {
        try {
            $corporation = $this->corporationRepository->findByIdOrFail($corporationId);
        } catch (\Exception $exception) {
            throw new \Exception('Can not sync this corporation by follow error: '. $exception->getMessage());
        }

        if (!$corporation['administration']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "administration" set');
        }
        if ('yes' !== $corporation['financial_export_enabled']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_enabled" set');
        }
        if ('yes' !== $corporation['financial_export_invoices']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_invoices" set');
        }
        if ('yes' !== $corporation['financial_import_transactions']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_import_transactions" set');
        }

        $exactAdministrationId  = $corporation['administration'];
        $corporationsId = $corporation['id'];

        $tokenKey = $corporation['financial_link_map_to'];
        $thirdPartyOutstandingItems = $this->retrieveThirdPartyOutstandingItemsService->retrieve($exactAdministrationId, $tokenKey);

        $thirdPartyOutstandingItemsOmniboxxInvoiceRows = $this->findMatchingInvocesByThirdPartyInvoice(
            $thirdPartyOutstandingItems
        );

        $outstandingItems = $this->retrieveOutstandingItemsService->retrieve($corporationsId);

        $invoicesMarkedForSync = $this->convertToAUniqueListOfInvoiceId(
            $outstandingItems,
            $thirdPartyOutstandingItemsOmniboxxInvoiceRows
        );

        foreach ($invoicesMarkedForSync as $invoiceId) {
            try {
                $this->syncInvoicePaymentsWithThirdPartyInvoicePaymentsService->sync($invoiceId);
            } catch (NoThirdPartyTransactionLinesException $exception) {
                if ($this->activateNoSyncForInvoice($invoiceId)) {
                    $this->notifierOfInvoiceNotFountFinancialReportUsers($invoiceId);
                }

                error_log("syncInvoicePaymentsWithThirdPartyInvoicePaymentsService error: ". $exception->getMessage());
            } catch (\Exception $exception) {
                error_log("syncInvoicePaymentsWithThirdPartyInvoicePaymentsService error: ". $exception->getMessage());
            }
        }
    }

    private function findMatchingInvocesByThirdPartyInvoice(array $thirdPartyOutstandingItems)
    {
        $thirdPartyOutstandingItemsOmniboxxInvoiceRows = [];
        foreach ($thirdPartyOutstandingItems as $thirdPartyOutstandingItem) {
            $invoiceRow = $this->invoiceRepository->findByFinancialId($thirdPartyOutstandingItem->getEntryNumber());
            if (!$invoiceRow) {
                continue;
            }

            $thirdPartyOutstandingItemsOmniboxxInvoiceRows[] = $invoiceRow;
        }

        return $thirdPartyOutstandingItemsOmniboxxInvoiceRows;
    }

    private function convertToAUniqueListOfInvoiceId(array $outstandingItems, array $thirdPartyOutstandingItemsOmniboxxInvoiceRows)
    {
        $invoicesMarkedForSync = [];
        foreach ($thirdPartyOutstandingItemsOmniboxxInvoiceRows as $thirdPartyOutstandingItemsOmniboxxInvoiceRow) {
            $invoicesMarkedForSync[
                $thirdPartyOutstandingItemsOmniboxxInvoiceRow->id
            ] = $thirdPartyOutstandingItemsOmniboxxInvoiceRow->id;
        }

        foreach ($outstandingItems as $outstandingItem) {
            $invoicesMarkedForSync[$outstandingItem['id']] = $outstandingItem['id'];
        }

        return  $invoicesMarkedForSync;
    }

    private function activateNoSyncForInvoice($invoiceId)
    {
        $invoiceRow = $this->invoiceRepository->findById($invoiceId);
        $invoiceRow->financial_no_sync = 1;
        return $this->invoiceRepository->persist($invoiceRow);
    }

    private function notifierFinancialReportUsers($subject, $note)
    {
        $eMailTo = \Settings::get('bankimport_report_users');

        $emailOutgoing = new \EmailOutgoing(array(
            'to' =>				$eMailTo,
            'subject' =>		$subject,
            'template' =>		'notifier_financial_report_users.phtml',
            'note' => $note
        ));
//        debug !!!
//        $preview = new \EmailPreview($emailOutgoing->id);
//        $preview->display();
//        die();

    }

    private function getInvoiceIdentifier($invoiceId)
    {
        $select = db()->select()
            ->from(['i' => 'invoices'], [
                'id',
                'customid',
                'run',
                'identifier' => 'GROUP_CONCAT(ir.identifier, ".", COALESCE(IF(CHAR_LENGTH(i.identifier) < 4, LPAD(i.identifier,4,"0"), i.identifier), "[xxxx]"))',
            ])
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', [])
            ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', [])
            ->where('i.id = ?', $invoiceId);

        $row = db()->fetchRow($select);
        if (!$row) {
            throw new \Exception('Not ebel to create a identifier for invoice ::', $invoiceId);
        }

        return $row['identifier'];
    }

    private function notifierOfInvoiceNotFountFinancialReportUsers($invoiceId)
    {
        $invoiceIdentifier = $this->getInvoiceIdentifier($invoiceId);
        $invoiceRow = $this->invoiceRepository->findById($invoiceId);

        $omniboxxUrl = $this->getOmniboxxBaseUrl();

        $financial_system = [
            'none' => 'Geen',
            'exactonline' => 'Exact Online',
            'exactglobe' => 'Exact Globe',
            'accountview' => 'Accountview',
            'mamut' => 'Mamut',
            'accountview_energy' => 'Accountview energie',
            'twinfield' => 'Twinfield'
        ];

        $subject = sprintf('Factuur %s niet gevonden in %s',
            $invoiceIdentifier,
            $financial_system[\Settings::get('financial_export_system')]
        );

        $note = sprintf('De Factuur %s met het boekstuknummer %s is in Exact Online niet gevonden. De synchronisatie voor deze factuur is uitgezet.
        Zie ook hier voor het <a href="%s/financial-export-system-comparison/invoices">Financieel -> %s -> Saldo verschillen</a>
        ',
            $invoiceIdentifier,
            $invoiceRow->financial_invoice_id,
            $omniboxxUrl,
            $financial_system[\Settings::get('financial_export_system')]
        );

        $this->notifierFinancialReportUsers($subject, $note);
    }

    private function getOmniboxxBaseUrl()
    {
        $controller = \Zend_Controller_Front::getInstance();
        if($config = $controller->getParam('bootstrap')){
            $config = $config->getOptions();
        } else {
            $config = $controller->getParam('config')->toArray();
        }
        $protocol = ($config['app']['secure'] ? 'https' : 'http');
        $omniboxxUrl = $protocol . '://' . $config['app']['server'];

        return $omniboxxUrl;
    }
}
