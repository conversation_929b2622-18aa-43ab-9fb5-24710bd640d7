<?php

namespace Accounting\Application\Service;

use SharedKernel\Domain\Model\Money;

class ViewEditCustomInvoiceService
{
    private $invoiceCustomModel;
    private $invoiceCustomRowsModel;
    private $componentsModel;
    private $invoiceCustomLineRows;
    private $invoiceCustomIdentifier;
    private $invoiceCustomLink;

    public function __construct()
    {
        $this->invoiceCustomModel = new \InvoicesCustoms();
        $this->invoiceCustomRowsModel = new \InvoicesCustomsRows();
        $this->componentsModel = new \Components();
    }

    public function execute($customInvoiceId)
    {
        $this->invoiceCustomIdentifier = $this->invoiceCustomModel->getIdentifier($customInvoiceId);
        $this->invoiceCustomLink = $this->makeLinkToInvoice($customInvoiceId, $this->invoiceCustomIdentifier);
        $invoiceCustomLineRows = $this->invoiceCustomRowsModel->findByCustomInvoiceId($customInvoiceId);
        $invoiceCustomLineRows = $invoiceCustomLineRows->toArray();
        $invoiceCustomLineRows = $this->retrieveComponentRows($invoiceCustomLineRows);
        $invoiceCustomLineRows = $this->formatMoneyValues($invoiceCustomLineRows);

        $this->invoiceCustomLineRows = $invoiceCustomLineRows;
    }

    public function getInvoiceCustomLineRows()
    {
        return $this->invoiceCustomLineRows;
    }

    public function getInvoiceCustomIdentifier()
    {
        return $this->invoiceCustomIdentifier;
    }

    public function getInvoiceCustomLink()
    {
        return $this->invoiceCustomLink;
    }

    private function retrieveComponentRows($invoiceCustomLineRows)
    {
        foreach ($invoiceCustomLineRows as &$invoiceCustomLineRow) {
            if (empty($invoiceCustomLineRow['component'])) {
                $invoiceCustomLineRow['component_row'] = [];
                continue;
            }

            $componentRow = $this->componentsModel->fetchRowById($invoiceCustomLineRow['component']);

            if (null === $componentRow) {
                throw new \RuntimeException('Could not find component');
            }

            $invoiceCustomLineRow['component_row'] = $componentRow->toArray();
        }

        return $invoiceCustomLineRows;
    }

    private function formatMoneyValues($invoiceCustomLineRows)
    {
        foreach ($invoiceCustomLineRows as &$invoiceCustomLineRow) {
            $invoiceCustomLineRow['price'] = (string)Money::makeFromEuroInput($invoiceCustomLineRow['price']);
            $invoiceCustomLineRow['taxprice'] = (string)Money::makeFromEuroInput($invoiceCustomLineRow['taxprice']);
        }

        return $invoiceCustomLineRows;
    }

    private function makeLinkToInvoice($invoiceCustomId, $invoiceCustomIdentifier)
    {
        return "<a href='invoice/edit-custom/id/{$invoiceCustomId}' target='_blank'>
                    <i class='fa fa-file-pdf-o' aria-hidden='true'></i>{$invoiceCustomIdentifier}
                 </a>";
    }
}
