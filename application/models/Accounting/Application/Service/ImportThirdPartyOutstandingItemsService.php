<?php


namespace Accounting\Application\Service;


use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveThirdPartyOutstandingItemsService;
use Accounting\Infrastructure\Domain\Model\Corporation\CorporationRepository;
use Accounting\Infrastructure\Domain\Model\ThirdPartyFinanceOutstandingInvoices\ThirdPartyFinanceOutstandingInvoicesRepository;
use Accounting\Infrastructure\ExactOnline\Model\OutstandingInvoice;

class ImportThirdPartyOutstandingItemsService
{
    /**
     * @var ExactRetrieveThirdPartyOutstandingItemsService
     */
    private $retrieveThirdPartyOutstandingItemsService;
    /**
     * @var CorporationRepository
     */
    private $corporationRepository;
    /**
     * @var ThirdPartyFinanceOutstandingInvoicesRepository
     */
    private $thirdPartyFinanceOutstandingInvoicesRepository;

    public function __construct(
        ExactRetrieveThirdPartyOutstandingItemsService $retrieveThirdPartyOutstandingItemsService,
        CorporationRepository $corporationRepository,
        ThirdPartyFinanceOutstandingInvoicesRepository $thirdPartyFinanceOutstandingInvoicesRepository
    ) {

        $this->retrieveThirdPartyOutstandingItemsService = $retrieveThirdPartyOutstandingItemsService;
        $this->corporationRepository = $corporationRepository;
        $this->thirdPartyFinanceOutstandingInvoicesRepository = $thirdPartyFinanceOutstandingInvoicesRepository;
    }

    public function execute($corporationId)
    {
        try {
            $corporation = $this->corporationRepository->findByIdOrFail($corporationId);
        } catch (\Exception $exception) {
            throw new \RuntimeException('Can not sync this corporation by follow error: '. $exception->getMessage());
        }

        if (!$corporation['administration']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "administration" set');
        }
        if (!$corporation['financial_export_enabled']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_enabled" set');
        }
        if (!$corporation['financial_export_invoices']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_invoices" set');
        }
        if (!$corporation['financial_import_transactions']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_import_transactions" set');
        }

        $exactAdministrationId  = $corporation['administration'];
        $corporationsId = $corporation['id'];
        $tokenKey = $corporation['financial_link_map_to'];

        $thirdPartyOutstandingItems = $this->retrieveThirdPartyOutstandingItemsService->retrieve($exactAdministrationId, $tokenKey);

        $this->removeOutstandingItemByAdministration($exactAdministrationId);

        foreach ($thirdPartyOutstandingItems as $thirdPartyOutstandingItem) {
            $this->saveOutstandingItem($thirdPartyOutstandingItem, $exactAdministrationId, $corporationsId);
        }
    }

    private function removeOutstandingItemByAdministration($administrationId)
    {
        $thirdPartyFinanceOutstandingInvoicesRows = $this->thirdPartyFinanceOutstandingInvoicesRepository
            ->findByAdministration($administrationId);

        foreach ($thirdPartyFinanceOutstandingInvoicesRows AS $thirdPartyFinanceOutstandingInvoicesRow) {
            $this->thirdPartyFinanceOutstandingInvoicesRepository->remove($thirdPartyFinanceOutstandingInvoicesRow->id);
        }
    }



    private function saveOutstandingItem(OutstandingInvoice $outstandingItem, $exactAdministrationId, $corporationsId)
    {
        $data = [
            'financial_invoice_id' => $outstandingItem->getInvoiceNumber(),
            'amount_cents' => $outstandingItem->getAmount()->getAmountInCents(), // Outstanding amount_cents
            'administration' => $exactAdministrationId,
            'corporation_id' => $corporationsId
        ];

        return $this->thirdPartyFinanceOutstandingInvoicesRepository->insert($data);
    }
}
