<?php

namespace Accounting\Application\Service;

use Accounting\Domain\Factory\PurchaseInvoiceFactory;
use Accounting\Domain\Model\PurchaseInvoice\PurchaseInvoiceRepository;
use Accounting\Domain\Service\NewThirdPartyPurchaseInvoiceFilter;
use Accounting\Domain\Service\NoNewThirdPartyPurchaseInvoicesFoundException;
use Accounting\Domain\Service\NoThirdPartyPurchaseInvoicesFoundException;
use Accounting\Domain\Service\RetrieveThirdPartyPurchaseInvoiceService;
use Accounting\Infrastructure\Logger\AccountingLogger;

class ImportThirdPartyPurchaseInvoicesService
{
    private $retrieveThirdPartyPurchaseInvoices;
    private $newThirdPartyPurchaseInvoiceFilter;
    private $purchaseInvoiceFactory;
    private $purchaseInvoiceRepository;

    public function __construct(
        RetrieveThirdPartyPurchaseInvoiceService $retrieveThirdPartyPurchaseInvoices,
        NewThirdPartyPurchaseInvoiceFilter $newThirdPartyPurchaseInvoiceFilter,
        PurchaseInvoiceFactory $purchaseInvoiceFactory,
        PurchaseInvoiceRepository $purchaseInvoiceRepository
    ) {
        $this->retrieveThirdPartyPurchaseInvoices = $retrieveThirdPartyPurchaseInvoices;
        $this->newThirdPartyPurchaseInvoiceFilter = $newThirdPartyPurchaseInvoiceFilter;
        $this->purchaseInvoiceFactory = $purchaseInvoiceFactory;
        $this->purchaseInvoiceRepository = $purchaseInvoiceRepository;
    }

    public function execute(ImportThirdPartyPurchaseInvoicesRequest $request)
    {
        $thirdPartyPurchaseInvoices = $this->retrieveThirdPartyPurchaseInvoices->retrieve(
            $request->getAdministration(),
            $request->getFromDate(),
            $request->getFinancialLinkMapTo()
        );

        AccountingLogger::i()->info('Totaal gevonden inkoopfacturen: ' . count($thirdPartyPurchaseInvoices));

        if (empty($thirdPartyPurchaseInvoices)) {
            throw new NoThirdPartyPurchaseInvoicesFoundException();
        }

        $newThirdPartyPurchaseInvoices = $this->newThirdPartyPurchaseInvoiceFilter->runFilters(
            $thirdPartyPurchaseInvoices
        );

        if (empty($newThirdPartyPurchaseInvoices)) {
            throw new NoNewThirdPartyPurchaseInvoicesFoundException();
        }

        $purchaseInvoices = $this->purchaseInvoiceFactory->buildAll(
            $newThirdPartyPurchaseInvoices,
            $request->getCorporationIds()
        );

        $this->purchaseInvoiceRepository->saveAll($purchaseInvoices);
    }
}
