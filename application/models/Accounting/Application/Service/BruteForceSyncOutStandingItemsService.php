<?php


namespace Accounting\Application\Service;


use Accounting\Infrastructure\Domain\Model\Invoice\OmniboxxModelInvoiceRepository;
use Accounting\Infrastructure\Domain\Model\Corporation\CorporationRepository;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveThirdPartyOutstandingItemsService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactRetrieveOutstandingItemsService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactFindInvoicesForSyncingService;
use Accounting\Infrastructure\ExactOnline\Service\SyncOutstandingItems\ExactSyncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
use Accounting\Infrastructure\ExactOnline\Model\OutstandingInvoice;
use SharedKernel\Domain\Model\Money;
use Accounting\Infrastructure\Domain\Model\Transactions\TransactionsRepository;
use Accounting\Domain\Model\TransactionsPayments\TransactionPayment;
use Accounting\Infrastructure\Domain\Model\TransactionsPayments\TransactionsPaymentsRepository;
use application\models\Accounting\Infrastructure\Service\RecalculateTransactionByTransactionPaymentService;

class BruteForceSyncOutStandingItemsService
{
    private $retrieveThirdPartyOutstandingItemsService;
    private $retrieveOutstandingItemsService;
    private $findInvoicesForSyncingService;
    private $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
    /**
     * @var OmniboxxModelInvoiceRepository
     */
    private $invoiceRepository;
    /**Complaint Budget Authorization
     * @var CorporationRepository
     */
    private $corporationRepository;
    /**
     * @var TransactionsRepository
     */
    private $transactionsRepository;
    /**
     * @var TransactionsPaymentsRepository
     */
    private $transactionsPaymentsRepository;
    /**
     * @var RecalculateTransactionByTransactionPaymentService
     */
    private $recalculateTransactionByTransactionPaymentService;

    public function __construct(
        ExactRetrieveThirdPartyOutstandingItemsService $retrieveThirdPartyOutstandingItemsService,
        ExactRetrieveOutstandingItemsService $retrieveOutstandingItemsService,
        ExactFindInvoicesForSyncingService $findInvoicesForSyncingService,
        ExactSyncInvoicePaymentsWithThirdPartyInvoicePaymentsService $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService,
        OmniboxxModelInvoiceRepository $invoiceRepository,
        CorporationRepository $corporationRepository,
        TransactionsRepository $transactionsRepository,
        TransactionsPaymentsRepository $transactionsPaymentsRepository,
        RecalculateTransactionByTransactionPaymentService $recalculateTransactionByTransactionPaymentService
    ) {
        $this->retrieveThirdPartyOutstandingItemsService = $retrieveThirdPartyOutstandingItemsService;
        $this->retrieveOutstandingItemsService = $retrieveOutstandingItemsService;
        $this->findInvoicesForSyncingService = $findInvoicesForSyncingService;
        $this->syncInvoicePaymentsWithThirdPartyInvoicePaymentsService =
            $syncInvoicePaymentsWithThirdPartyInvoicePaymentsService;
        $this->invoiceRepository = $invoiceRepository;
        $this->corporationRepository = $corporationRepository;
        $this->transactionsRepository = $transactionsRepository;
        $this->transactionsPaymentsRepository = $transactionsPaymentsRepository;
        $this->recalculateTransactionByTransactionPaymentService = $recalculateTransactionByTransactionPaymentService;
    }

    public function execute($corporationId)
    {

        try {
            $corporation = $this->corporationRepository->findByIdOrFail($corporationId);
        } catch (\Exception $exception) {
            throw new \RuntimeException('Can not sync this corporation by follow error: '. $exception->getMessage());
        }

        if (!$corporation['administration']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "administration" set');
        }
        if (!$corporation['financial_export_enabled']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_enabled" set');
        }
        if (!$corporation['financial_export_invoices']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_export_invoices" set');
        }
        if (!$corporation['financial_import_transactions']) {
            throw new \Exception('this corporation id:'. $corporation['id'] .' have no "financial_import_transactions" set');
        }

        $exactAdministrationId  = $corporation['administration'];
        $corporationsId = $corporation['id'];
        $tokenKey = $corporation['financial_link_map_to'];

        $thirdPartyOutstandingItems = $this->retrieveThirdPartyOutstandingItemsService->retrieve($exactAdministrationId, $tokenKey);

        foreach ($thirdPartyOutstandingItems as $thirdPartyOutstandingItem) {

            $this->syncThirdPartyOutstandingItemsInToOmniboxx($thirdPartyOutstandingItem);
        }

        $outstandingItems = $this->retrieveOutstandingItemsService->retrieve($corporationsId);

        foreach ($outstandingItems as $outstandingItem) {
            if (!$this->isInThirdPartyOutstandingItems($thirdPartyOutstandingItems, $outstandingItem)) {
                continue;
            }

            $transactionsRow = $this->transactionsRepository->retrieveByInvoiceId($outstandingItem['id']);
            if (!$transactionsRow) {
                continue;
            }

            $transactionId = $transactionsRow['id'];
            $transactionType = $transactionsRow['type'];
            $transactionAmount = new Money($transactionsRow['amount']);
            $transactionPayed = new Money($transactionsRow['payed']);

            if ("c" == $transactionType) {
                $diffAmount = $transactionPayed->subtract($transactionAmount);
            } else if ("d" == $transactionType) {
                $diffAmount = $transactionAmount->subtract($transactionPayed);
            }

            if (0 != $diffAmount->getAmountInCents()) {
                $description = 'Staat niet meer in de open staan de posten list van exact';

                $transactionPayment = $this->makeTransactionPayment($diffAmount, $transactionId, $description);

                $this->transactionsPaymentsRepository->insert($transactionPayment);
            }

            $this->recalculateTransactionByTransactionPaymentService->execute($transactionId);
        }

    }

    private function isInThirdPartyOutstandingItems($thirdPartyOutstandingItems, $outstandingItems)
    {
        foreach ($thirdPartyOutstandingItems as $thirdPartyOutstandingItem) {
            if ($thirdPartyOutstandingItem->getInvoiceNumber() == $outstandingItems['financial_invoice_id']) {
                return false;
            }
        }

        return true;
    }

    private function syncThirdPartyOutstandingItemsInToOmniboxx(OutstandingInvoice $thirdPartyOutstandingItem)
    {
        $invoiceRow = $this->invoiceRepository->findByFinancialId($thirdPartyOutstandingItem->getInvoiceNumber());
        if (!$invoiceRow) {
            return;
        }
        if (1 == $invoiceRow->financial_no_sync) {
            return;
        }

        $corporationRow = $this->corporationRepository->findByInvoiceId($invoiceRow->id);
        if ('yes' !== $corporationRow->financial_export_enabled) {
            return;
        }
        if ('yes' !== $corporationRow->financial_import_transactions) {
            return;
        }

        $transactionsRow = $this->transactionsRepository->retrieveByInvoiceId($invoiceRow->id);
        if (!$transactionsRow) {
            return;
        }
        $invoiceTransactionPayed = Money::makeFromEuroInput($transactionsRow['payed']);
        if ($thirdPartyOutstandingItem->getAmount() == $invoiceTransactionPayed) {
            return;
        }
        $transactionsType = $transactionsRow['type'];
        $transactionAmount = new Money($transactionsRow['amount']);
        $transactionPayed = new Money($transactionsRow['payed']);

        $thirdPartyOutstandingAmount  = new Money(abs($thirdPartyOutstandingItem->getAmount()->getAmountInCents()));
        $thirdPartyAmount = $transactionAmount->subtract($thirdPartyOutstandingAmount);

        if ("c" == $transactionsType) {
            $diffAmount = $transactionPayed->subtract($thirdPartyAmount);
        } else if ("d" == $transactionsType) {
            $diffAmount = $thirdPartyAmount->subtract($transactionPayed);
        }

        if (0 != $diffAmount->getAmountInCents()) {
            //betaling omniboxx wijkt af tegen over openstaan de posten omniboxx
            $description = 'Er was een verschil met de open staande  posten in exact';
            $transactionPayment = $this->makeTransactionPayment($diffAmount, $transactionsRow['id'], $description);

            $this->transactionsPaymentsRepository->insert($transactionPayment);
        }

        $this->recalculateTransactionByTransactionPaymentService->execute($transactionsRow['id']);
    }

    private function makeTransactionPayment(Money $amount, $transactionId, $description) {
        $amountCents = $amount->getAmountInCents();
        $transactionDate = (new \DateTime())->format('Y-m-d H:i:s');
        $externalId = null;

        return new TransactionPayment(
            $transactionId,
            $transactionDate,
            $amountCents,
            $description,
            $externalId
        );
    }

}
