<?
namespace Accounting\Application\Service;


use Accounting\Domain\Factory\TransactionsPaymentsFactory;
use Accounting\Domain\Model\RowNotFoundException;
use Accounting\Domain\Model\Transactions\PaymentAmountInvalidException;
use Accounting\Domain\Model\Transactions\TransactionsRepository;
use Accounting\Domain\Model\Transactions\TransactionsUpdateService;
use Accounting\Domain\Model\TransactionsPayments\TransactionsPaymentsRepository;
use Accounting\Domain\Service\BuildInvoiceIdentifierService;
use Accounting\Domain\Service\ExtractImportableTransactionsService;
use Accounting\Domain\Service\ImportTransactionsService as InfrastructureImportTransactionsService;
use Accounting\Domain\Service\TransactionsImportReportService;

class ImportTransactionsService {

	/** @var InfrastructureImportTransactionsService $infrastructureTransactionImportService */
	private $infrastructureTransactionImportService;

	/** @var ExtractImportableTransactionsService $extractImportableTransactionsService */
	private $extractImportableTransactionsService;

	/** @var TransactionsPaymentsFactory $transactionsPaymentsFactory */
	private $transactionsPaymentsFactory;

	/** @var TransactionsPaymentsRepository $transactionsPaymentsRepository */
	private $transactionsPaymentsRepository;

	/** @var TransactionsRepository $transactionsRepository */
	private $transactionsRepository;

	/** @var TransactionsUpdateService $transactionsUpdateService */
	private $transactionsUpdateService;

	/** @var TransactionsImportReportService $transactionsImportReportService */
	private $transactionsImportReportService;

	/** @var BuildInvoiceIdentifierService $buildInvoiceIdentifierService */
	private $buildInvoiceIdentifierService;

	/**
	 * ImportTransactionsService constructor.
	 * @param InfrastructureImportTransactionsService $infrastructureTransactionImportService
	 * @param ExtractImportableTransactionsService $extractImportableTransactionsService
	 * @param TransactionsPaymentsFactory $transactionsPaymentsFactory
	 * @param TransactionsPaymentsRepository $transactionsPaymentsRepository
	 * @param TransactionsRepository $transactionsRepository
	 * @param TransactionsUpdateService $transactionsUpdateService
	 * @param TransactionsImportReportService $transactionsImportReportService
	 * @param BuildInvoiceIdentifierService $buildInvoiceIdentifierService
	 */
	public function __construct(
		InfrastructureImportTransactionsService $infrastructureTransactionImportService,
		ExtractImportableTransactionsService $extractImportableTransactionsService,
		TransactionsPaymentsFactory $transactionsPaymentsFactory,
		TransactionsPaymentsRepository $transactionsPaymentsRepository,
		TransactionsRepository $transactionsRepository,
		TransactionsUpdateService $transactionsUpdateService,
		TransactionsImportReportService $transactionsImportReportService,
		BuildInvoiceIdentifierService $buildInvoiceIdentifierService
	) {
		$this->infrastructureTransactionImportService = $infrastructureTransactionImportService;
		$this->extractImportableTransactionsService = $extractImportableTransactionsService;
		$this->transactionsPaymentsFactory = $transactionsPaymentsFactory;
		$this->transactionsPaymentsRepository = $transactionsPaymentsRepository;
		$this->transactionsRepository = $transactionsRepository;
		$this->transactionsUpdateService = $transactionsUpdateService;
		$this->transactionsImportReportService = $transactionsImportReportService;
		$this->buildInvoiceIdentifierService = $buildInvoiceIdentifierService;
	}

	public function execute() {
		if (!(\Settings::get('financial_export_exactonline_invoices_using_api') && \Settings::get('bankimport_exact_enabled'))) {
            return [];
        }

		$transactionsData = $this->infrastructureTransactionImportService->execute();
		$transactionsData = $this->extractImportableTransactionsService->execute($transactionsData);
		$transactionsPayments = $this->transactionsPaymentsFactory->buildAll($transactionsData);
		list($transactions, $transactionsPayments) = $this->buildUpdatedTransactionRows($transactionsPayments);

		$this->transactionsPaymentsRepository->persistAll($transactionsPayments);
		$this->transactionsRepository->persistAll($transactions);

		$this->transmitImportSuccessfulEvent($transactionsData);

		return $this->transactionsImportReportService->getReportData();
	}

	private function transmitImportSuccessfulEvent($transactionsData) {
		$this->infrastructureTransactionImportService->handleImportSuccessfulEvent($transactionsData);
	}

	/**
	 * @param $transactionsPayments
	 * @return array
	 */
	private function buildUpdatedTransactionRows($transactionsPayments) {
		$transactions = [];

		foreach ($transactionsPayments as $index => $transactionsPayment) {
			try {
				$transaction = $this->retrieveTransaction($transactions, $transactionsPayment);

				if (!is_object($transaction)) {
					throw new RowNotFoundException('No transaction row found');
				}

				$transactions[ $transactionsPayment->transaction ] =
					$this->transactionsUpdateService->updateTransactionBasedOnTransactionsPayment(
						$transaction,
						$transactionsPayment
					);
			} catch (PaymentAmountInvalidException $paie) {
				$transaction = $this->retrieveTransaction($transactions, $transactionsPayment);
				$invoiceIdentifier = $this->buildInvoiceIdentifierService->execute($transaction->invoice);

				unset($transactionsPayments[ $index ]);

				$this->transactionsImportReportService->addLine([
					'account'     => null,
					'amount'      => ($transactionsPayment->amount / 100),
					'entryDate'   => $transactionsPayment->date_created,
					'date'        => $transactionsPayment->date,
					'debetCredit' => ($transactionsPayment->direction == 'incoming' ? '+' : '-'),
					'description' => "Bedrag kon niet afgeletterd worden op $invoiceIdentifier",
					'notified'    => '0',
					'result'      => 'possible_duplicate',
				]);
			} catch (RowNotFoundException $rnfe) {
				unset($transactionsPayments[ $index ]);

				$this->transactionsImportReportService->addLine([
					'account'     => null,
					'amount'      => ($transactionsPayment->amount / 100),
					'entryDate'   => $transactionsPayment->date_created,
					'date'        => $transactionsPayment->date,
					'debetCredit' => ($transactionsPayment->direction == 'incoming' ? '+' : '-'),
					'description' => 'Geen factuur gevonden voor deze transactie',
					'notified'    => '0',
					'result'      => 'ignored',
				]);
			}
		}

		foreach ($transactionsPayments as $transactionsPayment) {
			$transaction = $this->retrieveTransaction($transactions, $transactionsPayment);
			$invoiceIdentifier = $this->buildInvoiceIdentifierService->execute($transaction->invoice);

			$this->transactionsImportReportService->addLine([
				'account'     => null,
				'amount'      => ($transactionsPayment->amount / 100),
				'entryDate'   => $transactionsPayment->date_created,
				'date'        => $transactionsPayment->date,
				'debetCredit' => ($transactionsPayment->direction == 'incoming' ? '+' : '-'),
				'description' => "Bedrag afgeletterd op $invoiceIdentifier",
				'notified'    => '0',
				'result'      => 'matched',
			]);
		}

		return [$transactions, $transactionsPayments];
	}

	/**
	 * @param array $transactions
	 * @param \Zend_Db_Table_Row_Abstract $transactionsPayment
	 * @return \Zend_Db_Table_Row_Abstract
	 */
	private function retrieveTransaction(array $transactions, \Zend_Db_Table_Row_Abstract $transactionsPayment) {
		if (is_object($transactions[$transactionsPayment->transaction])) {
			$transaction = $transactions[$transactionsPayment->transaction];
		} else {
			$transaction = $this->transactionsRepository->retrieve($transactionsPayment->transaction);
		}

		return $transaction;
	}
}
