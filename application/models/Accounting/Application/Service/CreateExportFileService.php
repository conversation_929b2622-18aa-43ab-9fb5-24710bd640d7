<?php

namespace Accounting\Application\Service;

use Accounting\Domain\Model\Customer\CustomerRepository;
use Accounting\Domain\Model\Customer\NoCustomersFoundException;
use Accounting\Domain\Model\Invoice\InvoiceRepository;
use Accounting\Domain\Model\Invoice\NoInvoicesFoundException;
use Accounting\Domain\Service\CreateExportFileService as CreateExportFileDomainService;

class CreateExportFileService
{
    private $invoiceRepository;
    private $customerRepository;
    private $createExportFileService;

    public function __construct(
        InvoiceRepository $invoiceRepository,
        CustomerRepository $customerRepository,
        CreateExportFileDomainService $createExportFileService
    ) {
        $this->invoiceRepository = $invoiceRepository;
        $this->customerRepository = $customerRepository;
        $this->createExportFileService = $createExportFileService;
    }

    public function execute(CreateExportFileRequest $request)
    {
        $invoiceIds = $this->invoiceRepository->findAllByRunId($request->getRunId());

        if (empty($invoiceIds)) {
            throw new NoInvoicesFoundException();
        }

        $customers = $this->customerRepository->findByInvoices($invoiceIds);

        if (empty($customers)) {
            throw new NoCustomersFoundException();
        }

        return $this->createExportFileService->create($request->getRunId(), $invoiceIds, $customers);
    }
}
