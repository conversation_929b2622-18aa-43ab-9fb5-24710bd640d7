<?php

namespace Accounting\Application\Service;

class RetrieveAdministrationsService
{
    public function execute($corporationId = null)
    {
        $select = db()->select()
            ->from('corporations', ['id', 'administration', 'financial_link_map_to'])
            ->where('administration IS NOT NULL AND financial_export_enabled = ?', 'yes')
            ->where('financial_import_third_party_purchase_invoices = ?', 'yes');

        if ($corporationId) {
            $administrationCode = db()->select()
                ->from('corporations', 'administration')
                ->where('id = ?', $corporationId)
                ->query()
                ->fetchColumn();

            if ($administrationCode) {
                $select->where('administration = ?', $administrationCode);
            }
        }

        if ((new \DateTime())->format('j') % 2 === 0) {
            $select->order('id DESC');
        } else {
            $select->order('id ASC');
        }

        $corporations = $select->query()
            ->fetchAll();

        $administrations = [];
        foreach ($corporations as $corporation) {
            $administrations[\Settings::get('financial_export_system')][$corporation['financial_link_map_to']][$corporation['administration']][] = $corporation['id'];
        }

        return $administrations;
    }
}
