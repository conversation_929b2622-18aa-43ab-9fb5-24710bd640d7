<?php

namespace Accounting\Application\Service;

class UpdateCustomInvoiceLineService
{
    private $invoiceModel;
    private $invoiceCustomModel;
    private $invoiceCustomLineModel;

    public function __construct()
    {
        $this->invoiceModel = new \Invoices();
        $this->invoiceCustomModel = new \InvoicesCustoms();
        $this->invoiceCustomLineModel = new \InvoicesCustomsRows();
    }

    public function execute(UpdateCustomInvoiceLineRequest $request)
    {
        $invoiceCustomLineRow = $this->findInvoiceCustomLineRowOrFail($request->getCustomInvoiceLineId());
        $invoiceCustomRow = $this->findInvoiceCustomRowOrFail($invoiceCustomLineRow->custom);
        $this->guardAgainstInvoiceAlreadyPushedToThirdPartySystem($invoiceCustomRow);

        $invoiceCustomLineRow->ledger = $request->getLedger();
        $invoiceCustomLineRow->cost_center = $request->getCostCenter();
        $invoiceCustomLineRow->save();

        return $invoiceCustomRow->id;
    }

    /**
     * @param $customInvoiceId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findInvoiceCustomRowOrFail($customInvoiceId)
    {
        $invoiceCustomRow = $this->invoiceCustomModel->fetchRowById($customInvoiceId);

        if (null === $invoiceCustomRow) {
            throw new \RuntimeException('Custom invoice row not found');
        }

        return $invoiceCustomRow;
    }

    private function guardAgainstInvoiceAlreadyPushedToThirdPartySystem($invoiceCustomRow)
    {
        $invoiceRow = $this->findInvoiceRowByCustomInvoiceRowOrFail($invoiceCustomRow);

        if ($invoiceRow->financial_invoice_id) {
            throw new \RuntimeException('It seems the custom invoice has already been pushed to third party system');
        }
    }

    /**
     * @param $customInvoiceLineId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findInvoiceCustomLineRowOrFail($customInvoiceLineId)
    {
        $invoiceCustomLineRow = $this->invoiceCustomLineModel->fetchRowById($customInvoiceLineId);

        if (null === $invoiceCustomLineRow) {
            throw new \InvalidArgumentException('Custom invoice line row not found');
        }

        return $invoiceCustomLineRow;
    }

    /**
     * @param $invoiceCustomRow
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findInvoiceRowByCustomInvoiceRowOrFail($invoiceCustomRow)
    {
        $invoiceRow = $this->invoiceModel->fetchRow([
            'custom = ?' => 1,
            'customid = ?' => $invoiceCustomRow->id
        ]);

        if (null === $invoiceRow) {
            throw new \RuntimeException('Invoice row not found for custom invoice');
        }

        return $invoiceRow;
    }
}
