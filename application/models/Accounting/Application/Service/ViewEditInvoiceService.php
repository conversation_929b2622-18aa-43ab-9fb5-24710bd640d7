<?php

namespace Accounting\Application\Service;

use SharedKernel\Domain\Model\Money;

class ViewEditInvoiceService
{
    private $invoiceModel;
    private $invoiceRowsModel;
    private $componentsModel;
    private $invoiceLineRows;
    private $invoiceLink;

    public function __construct()
    {
        $this->invoiceModel = new \Invoices();
        $this->invoiceRowsModel = new \InvoiceRows();
        $this->componentsModel = new \Components();
    }

    public function execute($invoiceId)
    {
        $invoiceIdentifier = $this->invoiceModel->getIdentifier($invoiceId);
        $this->invoiceLink = $this->makeLinkToInvoice($invoiceId, $invoiceIdentifier);
        $invoiceLineRows = $this->invoiceRowsModel->findByInvoiceId($invoiceId);
        $invoiceLineRows = $invoiceLineRows->toArray();
        $invoiceLineRows = $this->retrieveComponentRows($invoiceLineRows);
        $invoiceLineRows = $this->calculateTaxRate($invoiceLineRows);
        $invoiceLineRows = $this->formatMoneyValues($invoiceLineRows);

        $this->invoiceLineRows = $invoiceLineRows;
    }

    public function getInvoiceLineRows()
    {
        return $this->invoiceLineRows;
    }

    public function getInvoiceLink()
    {
        return $this->invoiceLink;
    }

    private function retrieveComponentRows($invoiceLineRows)
    {
        foreach ($invoiceLineRows as &$invoiceLineRow) {
            if (empty($invoiceLineRow['component_id'])) {
                $invoiceLineRow['component_row'] = [];
                continue;
            }

            $componentRow = $this->componentsModel->fetchRowById($invoiceLineRow['component_id']);

            if (null === $componentRow) {
                throw new \RuntimeException('Could not find component');
            }

            $invoiceLineRow['component_row'] = $componentRow->toArray();
        }

        return $invoiceLineRows;
    }

    private function formatMoneyValues($invoiceLineRows)
    {
        foreach ($invoiceLineRows as &$invoiceLineRow) {
            $invoiceLineRow['amount_ex_vat'] = (string)Money::makeFromEuroInput($invoiceLineRow['amount_ex_vat']);
            $invoiceLineRow['amount_total'] = (string)Money::makeFromEuroInput($invoiceLineRow['amount_total']);
        }

        return $invoiceLineRows;
    }

    private function makeLinkToInvoice($invoiceId, $invoiceIdentifier)
    {
        return "<a href='invoice/export/id/{$invoiceId}' target='_blank'>
                    <i class='fa fa-file-pdf-o' aria-hidden='true'></i>{$invoiceIdentifier}
                 </a>";
    }

    private function calculateTaxRate($invoiceLineRows)
    {
        foreach ($invoiceLineRows as &$invoiceLineRow) {
            $amountInclVat = Money::makeFromEuroInput($invoiceLineRow['amount_vat']);
            $amountExclVat = Money::makeFromEuroInput($invoiceLineRow['amount_ex_vat']);
            $invoiceLineRow['tax_rate'] = ($amountInclVat->getAmountInCents() / $amountExclVat->getAmountInCents()) * 100;
        }

        return $invoiceLineRows;
    }
}
