<?php

namespace Accounting\Application\Service;

class ImportThirdPartyPurchaseInvoicesRequest
{
    private $administration;
    private $fromDate;
    private $corporationIds;
    private $financialLinkMapTo;

    public function __construct($administration, \DateTimeImmutable $fromDate, array $corporationIds, $financialLinkMapTo)
    {
        $this->administration = $administration;
        $this->fromDate = $fromDate;
        $this->corporationIds = $corporationIds;
        $this->financialLinkMapTo = $financialLinkMapTo;
    }

    public function getAdministration()
    {
        return $this->administration;
    }

    public function getFromDate()
    {
        return $this->fromDate;
    }

    public function getCorporationIds()
    {
        return $this->corporationIds;
    }

    public function getFinancialLinkMapTo()
    {
        return $this->financialLinkMapTo;
    }
}
