<?php

namespace Accounting\Application\Service;

class UpdateCustomInvoiceLineRequest
{
    private $customInvoiceLineId;
    private $ledger;
    private $costCenter;

    public function __construct($customInvoiceLineId, $ledger, $costCenter)
    {
        $this->customInvoiceLineId = $customInvoiceLineId;
        $this->ledger = $ledger;
        $this->costCenter = $costCenter;
    }

    public function getCustomInvoiceLineId()
    {
        return $this->customInvoiceLineId;
    }

    public function getLedger()
    {
        return $this->ledger;
    }

    public function getCostCenter()
    {
        return $this->costCenter;
    }
}
