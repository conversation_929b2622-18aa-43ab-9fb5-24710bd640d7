<?php

namespace Accounting\Application\Service;

class UpdateInvoiceLineRequest
{
    private $invoiceLineId;
    private $ledger;
    private $costCenter;

    public function __construct($invoiceLineId, $ledger, $costCenter)
    {
        $this->invoiceLineId = $invoiceLineId;
        $this->ledger = $ledger;
        $this->costCenter = $costCenter;
    }

    public function getInvoiceLineId()
    {
        return $this->invoiceLineId;
    }

    public function getLedger()
    {
        return $this->ledger;
    }

    public function getCostCenter()
    {
        return $this->costCenter;
    }
}
