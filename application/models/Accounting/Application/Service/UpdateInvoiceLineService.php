<?php

namespace Accounting\Application\Service;

class UpdateInvoiceLineService
{
    private $invoiceModel;
    private $invoiceLineModel;

    public function __construct()
    {
        $this->invoiceModel = new \Invoices();
        $this->invoiceLineModel = new \InvoiceRows();
    }

    public function execute(UpdateInvoiceLineRequest $request)
    {
        $invoiceLineRow = $this->findInvoiceLineRowOrFail($request->getInvoiceLineId());
        $invoiceRow = $this->findInvoiceRowOrFail($invoiceLineRow->invoice->id);
        $this->guardAgainstInvoiceAlreadyPushedToThirdPartySystem($invoiceRow);

        $invoiceLineRow->ledger = $request->getLedger();
        $invoiceLineRow->cost_center = $request->getCostCenter();
        $invoiceLineRow->save();

        return $invoiceRow->id;
    }

    /**
     * @param $invoiceLineId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findInvoiceLineRowOrFail($invoiceLineId)
    {
        $invoiceLineRow = $this->invoiceLineModel->fetchRowById($invoiceLineId);

        if (null === $invoiceLineRow) {
            throw new \RuntimeException('Invoice row not found');
        }

        return $invoiceLineRow;
    }

    /**
     * @param $invoiceId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findInvoiceRowOrFail($invoiceId)
    {
        $invoiceRow = $this->invoiceModel->fetchRowById($invoiceId);

        if (null === $invoiceRow) {
            throw new \InvalidArgumentException('Invoice row not found');
        }

        return $invoiceRow;
    }

    private function guardAgainstInvoiceAlreadyPushedToThirdPartySystem(\Zend_Db_Table_Row_Abstract $invoiceRow)
    {
        if ($invoiceRow->financial_invoice_id) {
            throw new \RuntimeException('It seems the invoice has already been pushed to third party system');
        }
    }
}
