<?php

namespace Accounting\Domain\Model\Transactions;

interface TransactionsUpdateService
{
	/**
	 * @param \Zend_Db_Table_Row_Abstract $transaction
	 * @param \Zend_Db_Table_Row_Abstract $transactionsPayment
	 * @throws PaymentAmountInvalidException
	 * @return mixed
	 */
	public function updateTransactionBasedOnTransactionsPayment(
		\Zend_Db_Table_Row_Abstract $transaction,
		\Zend_Db_Table_Row_Abstract $transactionsPayment
	);
}
