<?php

namespace Accounting\Domain\Service;

use Accounting\Infrastructure\Filter\AbstractUnknownThirdPartyPurchaseInvoiceFilter;

class NewThirdPartyPurchaseInvoiceFilter
{
    private $filters = [];

    public function __construct(AbstractUnknownThirdPartyPurchaseInvoiceFilter $unknownThirdPartyPurchaseInvoiceFilter)
    {
        $this->filters[] = $unknownThirdPartyPurchaseInvoiceFilter;
    }

    public function addFilter(ThirdPartyPurchaseInvoiceFilter $filter)
    {
        $this->filters[] = $filter;
    }

    public function runFilters($thirdPartyPurchaseInvoices)
    {
        foreach ($this->filters as $filter) {
            $thirdPartyPurchaseInvoices = $filter->filter($thirdPartyPurchaseInvoices);
        }

        return $thirdPartyPurchaseInvoices;
    }
}
