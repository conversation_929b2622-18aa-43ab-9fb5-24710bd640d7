<?php

namespace Accounting\Domain\Service\SyncOutstandingItems;

use Accounting\Infrastructure\ExactOnline\Model\OutstandingInvoice;

interface FindInvoicesForSyncingService
{
    /**
     * @param OutstandingInvoice[] $thirdPartyOutstandingItems
     * @param OutstandingInvoice[] $outstandingItems
     * @return OutstandingInvoice[]
     */
    public function find(array $thirdPartyOutstandingItems, array $outstandingItems);
}
