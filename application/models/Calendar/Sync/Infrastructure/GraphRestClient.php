<?php

namespace Calendar\Sync\Infrastructure;

/**
 * Class GraphRestClient
 * @package Calendar\Sync\Infrastructure
 */
class GraphRestClient
{
    const BASE_URL = 'https://graph.microsoft.com/';
    const BASE_PATH = 'v1.0/me/events/';
    const POST_PATH = 'v1.0/me/calendar/events';

    /**
     * @param $accessToken
     * @param $graphUserId = false
     * @return string
     */
    public function getCurrentUserData($accessToken, $graphUserId = false)
    {
        $params = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);
        $uri = $graphUserId ? 'v1.0/users/' . $graphUserId : 'v1.0/me';
        $response = $client->request('GET', $uri, $params);
        return $response->getBody()->getContents();
    }

    /**
     * @param $accessToken
     * @return string
     */
    public function getCalendarEvents($accessToken)
    {
        $params = [
            'query' => [
                '$select' => 'subject,body,bodyPreview,organizer,attendees,start,end,location',
                '$filter' => 'start/datetime ge \'' . date('c') . '\'',
            ],
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);

        $response = $client->request('GET', self::BASE_PATH, $params);
        return $response->getBody()->getContents();
    }

    /**
     * @param $accessToken
     * @param $graphUserId
     * @return string
     */
    public function getCalendarEventsByGraphUserId($accessToken, $graphUserId)
    {
        $path = $this->buildUserCalendarPath($graphUserId);
        $params = [
            'query' => [
                '$select' => 'subject,body,bodyPreview,organizer,attendees,start,end,location',
                '$filter' => 'start/datetime ge \'' . date('c') . '\'',
            ],
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Prefer' => 'outlook.timezone="Central European Standard Time"'
            ]
        ];
        return $this->callCalendarEvents($path, $params, 'GET');
    }

    /**
     * @param $accessToken
     * @param $graphCalendarEvent
     * @param string|null $externalId
     * @return string
     */
    public function postCalendarEvent($accessToken, $graphCalendarEvent, $externalId = null)
    {
        $params = [
            'json' => $graphCalendarEvent,
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);

        $method = $externalId !== null ? 'PATCH' : 'POST';
        $path = $externalId ? self::BASE_PATH . $externalId : self::POST_PATH;
        $response = $client->request($method, $path, $params);
        return $response->getBody()->getContents();
    }

    /**
     * @param $accessToken
     * @param $graphCalendarEvent
     * @param $graphUserId
     * @param null $externalId
     * @return string
     */
    public function postCalendarEventByGraphUserId(
        $accessToken,
        $graphCalendarEvent,
        $graphUserId,
        $externalId = null
    ) {
        $params = [
            'json' => $graphCalendarEvent,
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];
        $method = $externalId !== null ? 'PATCH' : 'POST';
        $basePath = $this->buildUserCalendarPath($graphUserId);
        $path = $externalId ? $basePath . '/' . $externalId : $basePath;
        return $this->callCalendarEvents($path, $params, $method);
    }

    /**
     * @param $accessToken
     * @param $graphUserId
     * @param $externalId
     * @return string
     */
    public function getCalendarEventByGraphId($accessToken, $graphUserId, $externalId) {
        $path = $this->buildUserCalendarPath($graphUserId) . '/' . $externalId;
        $params = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];
        return $this->callCalendarEvents($path, $params, 'GET');
    }

    /**
     * @param $accessToken
     * @param $graphUserId
     * @param $externalId
     * @return string
     */
    public function deleteCalendarEventByGraphId($accessToken, $graphUserId, $externalId) {
        $path = $this->buildUserCalendarPath($graphUserId) . '/' . $externalId;
        $params = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken
            ]
        ];
        return $this->callCalendarEvents($path, $params, 'DELETE');
    }

    /**
     * @param $graphUserId
     * @return string
     */
    private function buildUserCalendarPath($graphUserId)
    {
        return 'v1.0/users/' . $graphUserId . '/calendar/events';
    }

    /**
     * @param $path
     * @param $params
     * @param $method
     * @return string
     */
    private function callCalendarEvents($path, $params, $method)
    {

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);

        $response = $client->request($method, $path, $params);
        return $response->getBody()->getContents();
    }
}
