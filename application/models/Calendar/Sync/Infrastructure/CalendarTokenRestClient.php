<?php

namespace Calendar\Sync\Infrastructure;

/**
 * Class CalendarRestClient
 * @package Calendar\Sync\Infrastructure
 */
class CalendarTokenRestClient
{
    const BASE_URL = 'https://login.microsoftonline.com/';
    const TOKEN_PATH = 'common/oauth2/v2.0/token';

    /**
     * @return string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getAccessToken($tokenCode)
    {
        $params = [
            'form_params' => [
                'client_id' => GraphCalendarSettings::getClientId(),
                'client_secret' => GraphCalendarSettings::getClientSecret(),
                'redirect_uri' => GraphCalendarSettings::getRedirectUrl(),
                'grant_type' => 'authorization_code',
                'scope' => 'Calendars.ReadWrite',
                'code' => $tokenCode
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]
        ];

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);

        $response = $client->request('POST', self::TOKEN_PATH, $params);
        return $response->getBody()->getContents();
    }

    /**
     * @param $refreshToken
     * @return string
     */
    public function getAccessTokenByRefreshToken($refreshToken) {
        $params = [
            'form_params' => [
                'client_id' => GraphCalendarSettings::getClientId(),
                'client_secret' => GraphCalendarSettings::getClientSecret(),
                'redirect_uri' => GraphCalendarSettings::getRedirectUrl(),
                'grant_type' => 'refresh_token',
                'scope' => 'Calendars.ReadWrite',
                'refresh_token' => $refreshToken
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]
        ];

        $handler = new \GuzzleHttp\Handler\CurlHandler();
        $client = new \GuzzleHttp\Client([
            'base_uri' => self::BASE_URL,
            'handler' => \GuzzleHttp\HandlerStack::create($handler),
        ]);

        $response = $client->request('POST', self::TOKEN_PATH, $params);
        return $response->getBody()->getContents();
    }
}
