<?php

namespace Calendar\Sync\Domain\GraphCalendarEvent;

/**
 * Class GraphCalendarEventFactory
 * @package Calendar\Sync\Domain\GraphCalendarEvent
 */
class GraphCalendarEventFactory
{
    public function build($omniboxxCalendarEventRowData) {
        return [
            'subject' => $omniboxxCalendarEventRowData['summary'],
            'body' => [
                'contentType' => 'html',
                'content' => $omniboxxCalendarEventRowData['remark']
            ],
            'start' => [
                'dateTime' => date('Y-m-d\TH:i:s', strtotime($omniboxxCalendarEventRowData['start'])),
                'timeZone' => 'W. Europe Standard Time'
            ],
            'end' => [
                'dateTime' => date('Y-m-d\TH:i:s', strtotime($omniboxxCalendarEventRowData['end'])),
                'timeZone' => 'W. Europe Standard Time'
            ]
        ];
    }
}
