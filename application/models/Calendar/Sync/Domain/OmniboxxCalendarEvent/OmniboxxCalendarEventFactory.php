<?php

namespace Calendar\Sync\Domain\OmniboxxCalendarEvent;

/**
 * Class OmniboxxCalendarEventFactory
 * @package Calendar\Sync\Domain\OmniboxxCalendarEvent
 */
class OmniboxxCalendarEventFactory
{
    public function build($graphCalendarEvent, $userId) {
        return [
            'calendar_type' => 'user',
            'calendar_link' => $userId,
            'map_to_type' => false,
            'map_to_id' => 0,
            'summary' => $graphCalendarEvent['subject'],
            'remark' => $graphCalendarEvent['bodyPreview'],
            'start' => $graphCalendarEvent['start']['dateTime'],
            'end' => $graphCalendarEvent['end']['dateTime'],
            'external_id' => $graphCalendarEvent['id']
        ];
    }
}
