<?php

namespace Calendar\Sync\Application;

use Calendar\Sync\Domain\GraphCalendarEvent\GraphCalendarEventFactory;
use Calendar\Sync\Infrastructure\GraphRestClient;
use CalendarEvents;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Class PostCalendarEventByGraphIdService
 * @package Calendar\Sync\Application
 */
class PostCalendarEventByGraphIdService
{
    /**
     * @var GraphCalendarEventFactory
     */
    private $graphCalendarEventFactory;
    /**
     * @var GraphRestClient
     */
    private $graphRestClient;
    /**
     * @var CalendarEvents
     */
    private $calendarEventsModel;

    /**
     * PostCalendarEventByGraphIdService constructor.
     * @param GraphCalendarEventFactory $graphCalendarEventFactory
     * @param GraphRestClient $graphRestClient
     * @param CalendarEvents $calendarEventsModel
     */
    public function __construct(
        GraphCalendarEventFactory $graphCalendarEventFactory,
        GraphRestClient $graphRestClient,
        CalendarEvents $calendarEventsModel
    ) {
        $this->graphCalendarEventFactory = $graphCalendarEventFactory;
        $this->graphRestClient = $graphRestClient;
        $this->calendarEventsModel = $calendarEventsModel;
    }

    public function post($calendarEventRowData, $accessToken, $currentUserGraphId) {
        $graphCalendarEvent = $this->graphCalendarEventFactory->build($calendarEventRowData);
        try {
            $this->exportCalendarEvent(
                $accessToken,
                $graphCalendarEvent,
                $currentUserGraphId,
                $calendarEventRowData['id'],
                $calendarEventRowData['external_id']
            );
        } catch (GuzzleException $e) {
            error_log($e->getResponse()->getBody()->getContents());
        } catch (\Exception $e) {
            error_log($e->getMessage());
        }
    }

    /**
     * @param string $accessToken
     * @param array $graphCalendarEvent
     * @param string $currentUserGraphId
     * @param string $omniboxxEventId
     * @param string $graphEventId
     */
    private function exportCalendarEvent(
        $accessToken,
        $graphCalendarEvent,
        $currentUserGraphId,
        $omniboxxEventId,
        $graphEventId
    ) {
        $response = $this->graphRestClient->postCalendarEventByGraphUserId(
            $accessToken,
            $graphCalendarEvent,
            $currentUserGraphId,
            $graphEventId
        );
        $responseData = json_decode($response, true);
        $calendarEventRow = $this->calendarEventsModel->getById($omniboxxEventId);
        $calendarEventRow->external_id = $responseData['id'];
        $calendarEventRow->save();
    }
}
