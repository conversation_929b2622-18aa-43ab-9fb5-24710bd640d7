<?php

namespace Calendar\Sync\Application;

use Calendar\Sync\Infrastructure\GraphRestClient;

class DeleteCalendarEventByGraphIdService
{
    /**
     * @var GraphRestClient
     */
    private $graphRestClient;

    /**
     * DeleteCalendarEventByGraphIdService constructor.
     * @param GraphRestClient $graphRestClient
     */
    public function __construct(GraphRestClient $graphRestClient) {
        $this->graphRestClient = $graphRestClient;
    }

    /**
     * @param $eventGraphId
     * @param $accessToken
     * @param $calendarId
     */
    public function delete($eventGraphId, $accessToken, $calendarId) {
        $this->graphRestClient->deleteCalendarEventByGraphId($accessToken, $calendarId, $eventGraphId);
    }
}
