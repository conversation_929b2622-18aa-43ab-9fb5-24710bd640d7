<?php

namespace Calendar\Sync\Application;

use Calendar\Sync\Infrastructure\CalendarTokenRestClient;
use Calendar\Sync\Infrastructure\GraphCalendarSettings;
use OAuth\Common\Storage\OmniboxxCacheDir;
use Users;
use GuzzleHttp\Exception\GuzzleException;
use Calendar\Sync\Infrastructure\GraphRestClient;

/**
 * Class SyncCalendarService
 * @package Calendar\Sync\Application
 */
class AuthenticateCalendarApiService
{
    //@see https://docs.microsoft.com/en-us/azure/active-directory/develop/howto-convert-app-to-be-multi-tenant
    const BASE_URL = 'https://login.microsoftonline.com';
    const URL_PATH = 'auth2/v2.0/authorize';
    const DEAFAULT_OPEN_ID = 'common';

    /**
     * @var int
     */
    private $currentUserId;
    /**
     * @var Users
     */
    private $userModel;
    /**
     * @var CalendarTokenRestClient
     */
    private $calendarTokenRestClient;
    /**
     * @var GraphRestClient
     */
    private $graphRestClient;

    /**
     * AuthenticateCalendarApiService constructor.
     * @param \loginManager $loginManager
     * @param Users $userModel
     * @param CalendarTokenRestClient $calendarTokenRestClient
     * @param GraphRestClient $graphRestClient
     */
    public function __construct(
        \loginManager $loginManager,
        Users $userModel,
        CalendarTokenRestClient $calendarTokenRestClient,
        GraphRestClient $graphRestClient
    ) {
        $this->calendarTokenRestClient = $calendarTokenRestClient;
        $this->userModel = $userModel;
        $currentUserData = $loginManager::data();
        $this->currentUserId = $currentUserData->id;
        $this->graphRestClient = $graphRestClient;
    }

    public function authenticate($urlParams = null)
    {
        $currentUserRow = $this->userModel->getById($this->currentUserId);
        if (!$this->isCurrentUserLinked($currentUserRow)) {
            if ($urlParams === null) {
                $state = [
                    'state' => 2,
                ];
                $params = [
                    'client_id' => GraphCalendarSettings::getClientId(),
                    'response_type' => 'code',
                    'redirect_uri' => GraphCalendarSettings::getRedirectUrl(),
                    'response_mode' => 'query',
                    'scope' => 'offline_access user.read calendars.readwrite',
                    'state' => json_encode($state)
                ];
                $url = $this->getEndpointUrl() . '?' . http_build_query($params);
                header('Location: ' . $url);
                exit();
            }
            $code = $urlParams['code'];
            $authenticationTokens = $this->getAuthTokens($code);
            $graphUserData = json_decode(
                $this->graphRestClient->getCurrentUserData($authenticationTokens['accessToken']),
                true
            );
            $this->linkOmniboxxUserToGraphUser(
                $currentUserRow,
                $authenticationTokens['refreshToken'],
                $graphUserData['id']
            );
        } else {
            $authenticationTokens = $this->getAccessTokenByRefreshToken($currentUserRow->calendar_token);
        }
        return $authenticationTokens;
    }

    private function getEndpointUrl()
    {
        $tenantedId = self::DEAFAULT_OPEN_ID;
        if (GraphCalendarSettings::getClientTenantedId()) {
            $tenantedId = GraphCalendarSettings::getClientTenantedId();
        }

        return sprintf('%s/%s/%s',
            self::BASE_URL,
            $tenantedId,
            self::URL_PATH
        );
    }

    private function isCurrentUserLinked($currentUser)
    {
        return $currentUser->calendar_token !== null;
    }

    /**
     * @param string $code
     * @return mixed
     * @throws GuzzleException
     */
    private function getAuthTokens($code)
    {
        $response = json_decode($this->calendarTokenRestClient->getAccessToken($code), true);
        return [
            'accessToken' => $response['access_token'],
            'refreshToken' => $response['refresh_token']
        ];
    }

    private function getAccessTokenByRefreshToken($refreshToken) {
        $response = json_decode($this->calendarTokenRestClient->getAccessTokenByRefreshToken($refreshToken), true);
        return [
            'accessToken' => $response['access_token'],
        ];
    }

    /**
     * @param $omniboxxUserRow
     * @param $refreshToken
     * @param $graphUserId
     */
    private function linkOmniboxxUserToGraphUser($omniboxxUserRow, $refreshToken, $graphUserId) {
        $omniboxxUserRow->calendar_token = $refreshToken;
        $omniboxxUserRow->calendar_id = $graphUserId;
        $omniboxxUserRow->save();
    }
}
