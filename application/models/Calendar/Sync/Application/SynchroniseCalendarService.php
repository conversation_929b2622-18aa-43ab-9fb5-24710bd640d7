<?php

namespace Calendar\Sync\Application;

use Calendar\Sync\Domain\GraphCalendarEvent\GraphCalendarEventFactory;
use Calendar\Sync\Domain\OmniboxxCalendarEvent\OmniboxxCalendarEventFactory;
use Calendar\Sync\Infrastructure\CalendarTokenRestClient;
use Calendar\Sync\Infrastructure\GraphRestClient;
use GuzzleHttp\Exception\GuzzleException;
use CalendarEvents;

class SynchroniseCalendarService
{
    /**
     * @var CalendarEvents
     */
    protected $calendarEventsModel;
    /**
     * @var OmniboxxCalendarEventFactory
     */
    protected $omniboxxCalendarEventFactory;
    /**
     * @var GraphCalendarEventFactory
     */
    protected $graphCalendarEventFactory;
    /**
     * @var GraphRestClient
     */
    protected $graphRestClient;
    /**
     * @var CalendarTokenRestClient
     */
    protected $calendarTokenRestClient;
    /**
     * @var int
     */
    private $currentUserId;

    /**
     * SynchroniseCalendarService constructor.
     */
    public function __construct()
    {
        $currentUserData = \loginManager::data();
        $this->currentUserId = $currentUserData->id;
        $this->calendarEventsModel = new CalendarEvents();
        $this->omniboxxCalendarEventFactory = new OmniboxxCalendarEventFactory();
        $this->graphCalendarEventFactory = new GraphCalendarEventFactory();
        $this->graphRestClient = new GraphRestClient();
        $this->calendarTokenRestClient = new CalendarTokenRestClient();
    }

    public function execute($accessToken, $userId = null, $graphUserId = null)
    {
        try {
            if ($graphUserId === null) {
                $graphUserId = $this->getCurrentUserId($accessToken);
            }
            $this->removeDeletedCalendarEvents($accessToken, $graphUserId, $userId);
            $this->saveThirdPartyEvents($accessToken, $graphUserId, $userId);
        } catch (GuzzleException $e) {
            error_log($e->getResponse()->getBody()->getContents());
        } catch (\Exception $e) {
            error_log($e->getMessage());
        }
    }

    private function getCurrentUserId($accessToken)
    {
        $graphUserData = json_decode($this->graphRestClient->getCurrentUserData($accessToken), true);
        return $graphUserData['id'];
    }

    /**
     * @param $accessToken
     * @param $currentUserGraphId
     * @param string|null $userId
     * @throws \Zend_Db_Statement_Exception
     */
    private function saveThirdPartyEvents($accessToken, $currentUserGraphId, $userId = null)
    {
        $thirdPartyEvents = json_decode(
            $this->graphRestClient->getCalendarEventsByGraphUserId($accessToken, $currentUserGraphId),
            true
        );
        foreach ($thirdPartyEvents['value'] as $thirdPartyEvent) {
            $this->saveThirdPartyEvent($thirdPartyEvent, $userId ?: $this->currentUserId);
        }
    }

    /**
     * @param array $thirdPartyEvent
     * @param string $userId
     * @throws \Zend_Db_Statement_Exception
     */
    private function saveThirdPartyEvent($thirdPartyEvent, $userId)
    {
        $omniboxxCalendarEventData = $this->omniboxxCalendarEventFactory->build(
            $thirdPartyEvent,
            $userId
        );
        $calendarEventRowData = $this->calendarEventsModel->genMatchSelect(
            ['external_id' => $thirdPartyEvent['id']]
        )->query()->fetch();
        if ($calendarEventRowData === false) {
            $this->calendarEventsModel->insert($omniboxxCalendarEventData);
        } else {
            $calendarEventRow = $this->calendarEventsModel->getById($calendarEventRowData['id']);
            $calendarEventRow->setFromArray($omniboxxCalendarEventData);
            $calendarEventRow->save();
        }
    }

    /**
     * @param $accessToken
     * @param $currentUserGraphId
     * @param null $userId
     * @throws \Zend_Db_Table_Row_Exception
     */
    private function removeDeletedCalendarEvents($accessToken, $currentUserGraphId, $userId = null)
    {
        $calendarEventRowsData = $this->calendarEventsModel
            ->select()
            ->where('start >= ?', date('Y-m-d H:i'))
            ->where('calendar_link = ?', $userId ?: $this->currentUserId)
            ->query()
            ->fetchAll();
        foreach ($calendarEventRowsData as $calendarEventRowData) {
            if ($this->eventShouldBeDeleted(
                $accessToken,
                $currentUserGraphId,
                $calendarEventRowData['external_id']
            )) {
                $calendarEventRow = $this->calendarEventsModel->getById($calendarEventRowData['id']);
                $calendarEventRow->delete();
            }
        }
    }

    /**
     * @param $accessToken
     * @param $currentUserGraphId
     * @param $eventExternalId
     * @return bool
     */
    private function eventShouldBeDeleted($accessToken, $currentUserGraphId, $eventExternalId)
    {
        try {
            $response = json_decode(
                $this->graphRestClient->getCalendarEventByGraphId($accessToken, $currentUserGraphId, $eventExternalId),
                true
            );
            return $response['id'] !== $eventExternalId;
        } catch (GuzzleException $e) {
            // Guzzle throws a response as an error if the http response code is an error code
            return true;
        }
    }
}
