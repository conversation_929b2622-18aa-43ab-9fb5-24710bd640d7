<?php

namespace Calendar\Events\Contacts;

class CreateRowsFromUrlParameterService
{

    public function execute($urlParameterValue)
    {
        if (is_null($urlParameterValue)) {
            return [];
        }

        $userIds = $this->getUserIdsFromUrlParameterValue($urlParameterValue);
        return $this->formatUserIdsAsCalendarEventContactRows($userIds);
    }

    private function getUserIdsFromUrlParameterValue($urlParameterValue)
    {
        $userIds = [];

        if (is_numeric($urlParameterValue)) {
            $userIds = [$urlParameterValue];
        } elseif (strpos($urlParameterValue, ',') !== false) {
            $userIds = explode(',', $urlParameterValue);
        }

        return $userIds;
    }

    private function formatUserIdsAsCalendarEventContactRows($userIds)
    {
        $calendarEventsContactsRows = [];
        foreach ($userIds as $userId) {
            $calendarEventsContactsRows[] = [
                'user_id' => $userId
            ];
        }

        return $calendarEventsContactsRows;
    }
}
