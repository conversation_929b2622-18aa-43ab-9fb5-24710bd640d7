<?php

namespace Calendar\Events\Contacts;

class AutoCompleterDataFormatterService
{

    private $calendarEventsContactsModel;
    private $usersModel;

    public function __construct(
        \CalendarEventsContacts $calendarEventsContactsModel,
        \Users $usersModel
    ) {
        $this->calendarEventsContactsModel = $calendarEventsContactsModel;
        $this->usersModel = $usersModel;
    }

    public function execute($eventsContactRows)
    {
        $autoCompleterFormattedData = $this->createAutoCompleterFormattedData($eventsContactRows);
        $encodedData = $this->encodeAutoCompleterFormattedData($autoCompleterFormattedData);

        return $encodedData;
    }

    private function createAutoCompleterFormattedData($eventsContactRows)
    {
        $formattedData = [];

        if (count($eventsContactRows) === 0) {
            return $formattedData;
        }

        $contactNames = $this->fetchNamesForAllCalenderEventContactRows($eventsContactRows);

        foreach ($eventsContactRows as $eventsContactRow) {
            $formattedValue = '-';
            if (isset($contactNames[$eventsContactRow['user_id']])) {
                $formattedValue = $contactNames[$eventsContactRow['user_id']];
            }

            $formattedData[] = [
                'id' => $eventsContactRow['id'],
                'user_id' => $eventsContactRow['user_id'],
                'value' => $formattedValue,
            ];
        }

        return $formattedData;
    }

    private function fetchNamesForAllCalenderEventContactRows($eventsContactRows)
    {

        $userIds = $this->getAllContactUserIdsFromEventContactRows($eventsContactRows);

        $select = $this->usersModel->select()
            ->from($this->usersModel->getTableName(), ['id', 'rendered_name'])
            ->where('id IN (' . implode_for_where_in($userIds) . ')');

        return db()->fetchPairs($select);
    }

    private function getAllContactUserIdsFromEventContactRows($eventsContactRows)
    {
        $userIdColumnValues = array_column($eventsContactRows, 'user_id');
        return array_unique($userIdColumnValues);
    }

    private function encodeAutoCompleterFormattedData($autoCompleterFormattedData)
    {
        return json_encode($autoCompleterFormattedData);
    }
}
