<?php

namespace Fixes\InvestorProvision;

use InvestorProvisionsSendCosts as investorProvisionSendCostModel;

class UpdateSendCostAmountsService
{
    /**
     * @var investorProvisionSendCostModel $investorProvisionSendCostModel
     */
    private $investorProvisionSendCostModel;

    public function __construct(investorProvisionSendCostModel $investorProvisionSendCostModel)
    {
        $this->investorProvisionSendCostModel = $investorProvisionSendCostModel;
    }


    public function execute()
    {
        $sendCosts = $this->getInvestorProvisionCostPayments();
        $sendIds = $this->getInvestorProvisionSendIds($sendCosts);

        if (!$sendIds || count($sendIds) === 0) {
            die('geen data gevonden');
        }

        $sendData = $this->getInvestorProvisionSendData($sendIds);

        if (!$sendData || count($sendData) === 0) {
            die('geen data gevonden');
        }

        $sendData = $this->decodeSendData($sendData);

        $amounts = [];
        foreach ($sendCosts as $sendCostItem) {
            if (!isset($sendData[$sendCostItem['investor_provision_send_id']])) {
                continue;
            }

            $sendDataItem = $sendData[$sendCostItem['investor_provision_send_id']];

            $costItem = $this->matchCostToSendDataItem($sendCostItem, $sendDataItem);

            if (!$costItem) {
                continue;
            }

            $amounts[$sendCostItem['id']] = $costItem['shared_amount'];
        }

        $this->persistAmounts($amounts);
    }

    private function persistAmounts($amounts)
    {
        try {
            db()->beginTransaction();

            foreach ($amounts as $id => $amount) {
                $this->investorProvisionSendCostModel
                    ->getById($id)
                    ->setFromArray(['amount' => $amount])
                    ->save();
            }

            db()->commit();
        } catch (\Exception $exception) {
            db()->rollBack();
            p($exception);
        }
    }

    private function getInvestorProvisionCostPayments()
    {
        $select = db()->select()
            ->from(
                ['ipsc' => 'investor_provision_send_costs'],
                ['id', 'cost_object_id', 'investor_provision_send_id']
            )
            ->where('ipsc.amount IS NULL')
        ;

        return db()->fetchAll($select);
    }

    private function getInvestorProvisionSendIds($sendCosts)
    {
        return array_unique(array_filter(array_column($sendCosts, 'investor_provision_send_id')));
    }

    private function getInvestorProvisionSendData($sendIds)
    {
        $dataSelect = db()->select()
            ->from(['ips' => 'investor_provision_send'], ['id', 'data'])
            ->where('ips.data IS NOT NULL')
            ->where('ips.id IN (?)', $sendIds);

        return db()->fetchPairs($dataSelect);
    }

    private function decodeSendData($sendData)
    {

        foreach ($sendData as $sendDataKey => $sendDataItem) {
            $sendData[$sendDataKey] = json_decode($sendDataItem, true);
        }
        return $sendData;
    }

    private function matchCostToSendDataItem($cost, $sendDataItem)
    {
        foreach ($sendDataItem['costs_ungrouped'] as $costItem) {

            if(!isset($costItem['shared_amount']) && isset($costItem['subtract_amount'])){
                $costItem['shared_amount'] = $costItem['subtract_amount'];
            }

            if (
                !isset($costItem['co_id']) ||
                !$costItem['co_id'] ||
                $costItem['co_id'] != $cost['cost_object_id'] ||
                !isset($costItem['shared_amount'])
            ) {
                continue;
            }

            return $costItem;
        }

        return null;
    }
}
