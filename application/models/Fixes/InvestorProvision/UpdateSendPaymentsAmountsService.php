<?php

namespace Fixes\InvestorProvision;

use InvestorProvisionsSendPayments as investorProvisionSendPaymentModel;

class UpdateSendPaymentsAmountsService
{
    /**
     * @var investorProvisionSendPaymentModel $investorProvisionSendPaymentModel
     */
    private $investorProvisionSendPaymentModel;

    public function __construct(investorProvisionSendPaymentModel $investorProvisionSendPaymentModel)
    {
        $this->investorProvisionSendPaymentModel = $investorProvisionSendPaymentModel;
    }


    public function execute()
    {
        $sendPayments = $this->getInvestorProvisionSendPayments();
        $sendIds = $this->getInvestorProvisionSendIds($sendPayments);

        if (!$sendIds || count($sendIds) === 0) {
            die('geen data gevonden');
        }

        $sendData = $this->getInvestorProvisionSendData($sendIds);

        if (!$sendData || count($sendData) === 0) {
            die('geen data gevonden');
        }

        $sendData = $this->decodeSendData($sendData);

        $transactionPaymentAmounts = $this->getTransactionPaymentAmounts($sendPayments);
        $invoiceRowsAmounts = $this->getInvoiceRowsAmounts($sendPayments);

        $amounts = [];
        foreach ($sendPayments as $payment) {
            if (!isset($sendData[$payment['investor_provision_send_id']])) {
                continue;
            }

            $sendDataItem = $sendData[$payment['investor_provision_send_id']];
            $payment['transaction_payment_amount'] = $transactionPaymentAmounts[$payment['transaction_payment_id']];

            $paymentItem = $this->matchPaymentToSendDataItem($payment, $sendDataItem);

            if (!$paymentItem) {
                continue;
            }

            $paymentItem['amount'] = $this->validateAmountUsingInvoiceRowsAndTransactionPayments(
                $paymentItem['shared_amount'],
                $payment['transaction_payment_id'],
                $transactionPaymentAmounts,
                $payment['invoice_row_id'],
                $invoiceRowsAmounts
            );

            $amounts[$payment['id']] = $paymentItem['amount'];
        }

        $this->persistAmounts($amounts);
    }

    private function persistAmounts($amounts)
    {
        try {
            db()->beginTransaction();

            foreach ($amounts as $id => $amount) {
                $this->investorProvisionSendPaymentModel
                    ->getById($id)
                    ->setFromArray(['amount' => $amount])
                    ->save();
            }

            db()->commit();
        } catch (\Exception $exception) {
            db()->rollBack();
            p($exception);
        }
    }

    private function validateAmountUsingInvoiceRowsAndTransactionPayments(
        $amount,
        $transactionPaymentId,
        $transactionPaymentAmounts,
        $invoiceRowId,
        $invoiceRowsAmounts
    ) {
        $amount = $this->setAmountUsingTransactionPaymentAmounts(
            $transactionPaymentId,
            $transactionPaymentAmounts,
            $amount
        );

        $amount = $this->setAmountUsingInvoiceRowAmounts(
            $invoiceRowId,
            $invoiceRowsAmounts,
            $amount
        );

        return $amount;
    }

    private function getInvestorProvisionSendPayments()
    {
        $select = db()->select()
            ->from(
                ['ipsp' => 'investor_provision_send_payments'],
                ['id', 'transaction_payment_id', 'investor_provision_send_id', 'invoice_row_id']
            )
            ->where('ipsp.amount IS NULL')
        ;

        return db()->fetchAll($select);
    }

    private function getInvestorProvisionSendIds($sendPayments)
    {
        return array_unique(array_filter(array_column($sendPayments, 'investor_provision_send_id')));
    }

    private function getInvestorProvisionSendData($sendIds)
    {
        $dataSelect = db()->select()
            ->from(['ips' => 'investor_provision_send'], ['id', 'data'])
            ->where('ips.data IS NOT NULL')
            ->where('ips.id IN (?)', $sendIds);

        return db()->fetchPairs($dataSelect);
    }

    private function decodeSendData($sendData)
    {

        foreach ($sendData as $sendDataKey => $sendDataItem) {
            $sendData[$sendDataKey] = json_decode($sendDataItem, true);
        }
        return $sendData;
    }

    private function getTransactionPaymentAmounts($sendPayments)
    {
        $transactionPaymentAmountsSelect = db()->select()
            ->from(['tp' => 'transactions_payments'],
                ['id', 'amount' => 'IF(direction = "incoming", amount, amount * -1)'])
            ->where('tp.id IN (?)', array_column($sendPayments, 'transaction_payment_id'));;

        return db()->fetchPairs($transactionPaymentAmountsSelect);
    }

    private function getInvoiceRowsAmounts($sendPayments)
    {
        $invoiceRowsSelect = db()->select()
            ->from(['irows' => 'invoice_rows'], ['id', 'amount' => 'ROUND(amount_total*100, 0)'])
            ->where('irows.id IN (?)', array_column($sendPayments, 'invoice_row_id'));;

        return db()->fetchPairs($invoiceRowsSelect);
    }

    private function matchPaymentToSendDataItem($payment, $sendDataItem)
    {
        foreach ($sendDataItem['payments'] as $objectData) {
            foreach ($objectData['payments'] as $paymentItem) {

                $transactionPaymentIds = [];

                if (isset($paymentItem['payment_id'])) {
                    $transactionPaymentIds[] = $paymentItem['payment_id'];
                }

                if (isset($paymentItem['other_tp_ids'])) {
                    $transactionPaymentIds = array_merge($transactionPaymentIds, $paymentItem['other_tp_ids']);
                }

                if (
                    !isset($paymentItem['id']) ||
                    !$paymentItem['id'] ||
                    !in_array($payment['transaction_payment_id'], $transactionPaymentIds) ||
                    !isset($paymentItem['amount'])
                ) {
                    continue;
                }

                // how much of the payout is contributed by this tp
                $transactionPaymentRatio = $payment['transaction_payment_amount'] / $paymentItem['amount_for_payout'];;

                $payoutRowTotal = 0;
                foreach($paymentItem['payout_rows'] as $payoutRow){
                    if($payoutRow['id'] == $payment['invoice_row_id']) {
                        $payoutRowTotal = $payoutRow['total'];
                        break;
                    }
                }

                // how much is on this component
                $invoiceRowRatio = $payoutRowTotal / $paymentItem['amount_for_payout'];;

                $paymentItem['shared_amount'] = round($paymentItem['shared_amount'] * $transactionPaymentRatio * $invoiceRowRatio);

                return $paymentItem;
            }
        }

        return null;
    }

    private function setAmountUsingTransactionPaymentAmounts($transactionPaymentId, $transactionPaymentAmounts, $amount)
    {
        if (!isset($transactionPaymentAmounts[$transactionPaymentId])) {
            return $amount;
        }

        $transactionPaymentAmount = $transactionPaymentAmounts[$transactionPaymentId];

        if ($transactionPaymentAmount > 0) {
            return min(
                $amount,
                $transactionPaymentAmount
            );
        }
        return max(
            0 - abs($amount),
            $transactionPaymentAmount
        );
    }

    private function setAmountUsingInvoiceRowAmounts($invoiceRowId, $invoiceRowAmounts, $amount)
    {
        if (!isset($invoiceRowAmounts[$invoiceRowId])) {
            return $amount;
        }

        $invoiceRowAmount = (int) $invoiceRowAmounts[$invoiceRowId];

        return min(
            $amount,
            abs($invoiceRowAmount)
        );
    }
}
