<?php

namespace Fixes\InvestorProvision;

use Exception as Exception;
use Investors as InvestorModel;
use Invoice as InvoiceLibrary;
use InvoiceCustom as InvoiceCustomLibrary;
use InvoiceRows as InvoiceRowsModel;
use InvoicesCustoms as InvoiceCustomModel;
use InvoicesCustomsRows as InvoiceCustomRowsModel;
use Main;
use Pdf as PdfLibrary;
use SharedKernel\Domain\Model\Money;

class RebuildInvoiceRowsAndPdfService
{

    private $investorModel;
    private $pdflLibrary;
    private $invoiceCustomModel;
    private $invoiceCustomLibrary;
    private $invoiceLibrary;
    private $invoiceCustomRowsModel;
    private $invoiceRowsModel;

    public function __construct(
        InvestorModel $investorModel,
        PdfLibrary $pdflLibrary,
        InvoiceCustomModel $invoiceCustomModel,
        InvoiceCustomLibrary $invoiceCustomLibrary,
        InvoiceLibrary $invoiceLibrary,
        InvoiceCustomRowsModel $invoiceCustomRowsModel,
        InvoiceRowsModel $invoiceRowsModel
    ) {
        $this->investorModel = $investorModel;
        $this->pdflLibrary = $pdflLibrary;
        $this->invoiceCustomModel = $invoiceCustomModel;
        $this->invoiceCustomLibrary = $invoiceCustomLibrary;
        $this->invoiceLibrary = $invoiceLibrary;
        $this->invoiceCustomRowsModel = $invoiceCustomRowsModel;
        $this->invoiceRowsModel = $invoiceRowsModel;
    }

    /**
     * @param $investorProvisionId
     * @throws Exception
     */
    public function execute($investorProvisionId)
    {
        $provisionData = $this->fetchProvisionData($investorProvisionId);

//        $invoiceFilename = $this->getInvoiceFilename($provisionData['invoice_id']);
//        $this->backupOriginalPdf($invoiceFilename);

        $periodOffset = $this->getPeriodOffset($provisionData['period']);

        $tempInvoiceCustomId = $this->getTempInvoiceCustomId(
            $provisionData['investor'],
            json_decode($provisionData['data'], true),
            $periodOffset,
            $this->pdflLibrary
        );

        if (!$tempInvoiceCustomId) {
            throw new Exception('Empty investor provision invoice');
        }

        $this->correctRoundingDifferences($provisionData['custom_id'], $tempInvoiceCustomId);

        $this->replaceInvoiceRows($tempInvoiceCustomId, $provisionData['invoice_id']);
        $this->replaceInvoiceCustomRows($provisionData['custom_id'], $tempInvoiceCustomId);

        $this->deleteTempInvoiceCustom($tempInvoiceCustomId);

//        $specificationPdfString = $this->getSpecificationPdf($investorProvisionId, $provisionData['investor']);
//        $pdf = $this->buildNewInvoicePdf($provisionData, $specificationPdfString);
//        $this->savePdf($pdf, $invoiceFilename);
    }

    private function fetchProvisionData($investorProvisionId)
    {
        $data_select = db()->select()
            ->from(['ips' => 'investor_provision_send'], ['data', 'investor', 'custom_id', 'period'])
            ->joinLeft(['i' => 'invoices'], 'i.customid = ips.custom_id', ['invoice_id' => 'id'])
            ->where('ips.id = ?', $investorProvisionId);

        return db()->fetchRow($data_select);
    }

    /**
     * @param $period
     * @return false|float|int|string
     */
    private function getPeriodOffset($period)
    {
        $start = strtotime($period);
        $periodOffset = $this->investorModel->dateToPeriodeOffset(date('m', $start), date('Y', $start));
        return $periodOffset;
    }

    private function getSpecificationPdf($investorProvisionId, $investorId)
    {
        $filename = Main::app()->getDir('investor_provision') . $investorId . '/' . $investorProvisionId . '.pdf';
        return file_get_contents($filename);
    }

    /**
     * @param $provisionData
     * @param $periodOffset
     * @param PdfLibrary $specificationPdf
     * @return false|string
     */
    private function getTempInvoiceCustomId($investorId, $provisionData, $periodOffset, PdfLibrary $specificationPdf)
    {
        return $this->investorModel->makeProvisionInvoice(
            $investorId,
            $periodOffset,
            $specificationPdf,
            $provisionData
        );
    }

    private function backupOriginalPdf($invoiceFilename)
    {
        $newFilename = $invoiceFilename . '.original';

        if (file_exists($newFilename)) {
            return;
        }

        copy($invoiceFilename, $newFilename);
    }

    /**
     * @param $originalCustomId
     * @param $newTempCustomId
     * @throws Exception
     */
    private function correctRoundingDifferences($originalCustomId, $newTempCustomId)
    {
        $oldInvoiceTotalSelect = db()->select()
            ->from(['i' => 'invoices'], false)
            ->joinLeft(['t' => 'transactions'], 't.invoice = i.id', ['total' => '(t.amount/100)'])
            ->where('i.customid = ?', $originalCustomId);

        $oldInvoiceTotal = Money::makeFromEuroInput(db()->fetchOne($oldInvoiceTotalSelect));


        $newTotalRowsSelect = $this->invoiceCustomRowsModel->select()
            ->where('custom = ?', $newTempCustomId)
            ->where('is_input = ?', true)
            ->order('description');

        $newTotalRows = $this->invoiceCustomRowsModel->fetchAll($newTotalRowsSelect);

        $newInvoiceTotal = Money::makeFromEuroInput(array_sum(array_column($newTotalRows->toArray(), 'taxprice')));

//        p($oldInvoiceTotal->equals($newInvoiceTotal));


        $oldCostRowsSelect = $this->invoiceCustomRowsModel->select()
            ->where('custom = ?', $originalCustomId)
            ->where('is_input = ?', true)
            ->where('description LIKE ?', 'Kosten %')
            ->order('description')
        ;

        $newCostRowsSelect = $this->invoiceCustomRowsModel->select()
            ->where('custom = ?', $newTempCustomId)
            ->where('is_input = ?', true)
            ->where('description LIKE ?', 'Kosten %')
            ->order('description');

        $oldCostRows = $this->invoiceCustomRowsModel->fetchAll($oldCostRowsSelect);
        $newCostRows = $this->invoiceCustomRowsModel->fetchAll($newCostRowsSelect);

        $oldCostTotal = Money::makeFromEuroInput(array_sum(array_column($oldCostRows->toArray(), 'taxprice')));
        $newCostTotal = Money::makeFromEuroInput(array_sum(array_column($newCostRows->toArray(), 'taxprice')));

        // temo disabled corrections on costs
        if(false && !$oldCostTotal->equals($newCostTotal)) {
            $differenceInCents = $oldCostRows->subtract($newCostRows);

            if ($differenceInCents > 2) {
                throw new \Exception('Cost total invalid ' . $originalCustomId);
            }

            $correctionRow = null;
            foreach ($newCostRows as $newCostRow) {
                if ($newCostRow->tax_rate > 0) {
                    $correctionRow = $newCostRow;
                    break;
                }
            }

            if (!$correctionRow) {
                throw new \Exception('Cost total invalid ' . $originalCustomId);
            }

            $correctedTaxPriceInCents = Money::makeFromEuroInput($correctionRow->taxprice)
                ->add($differenceInCents)
                ->getAmountInCents();

            $correctionRow->taxprice = formatMoneyInputForDatabase($correctedTaxPriceInCents / 100);
            $correctionRow->save();
        }

        $oldTroughputRowsSelect = $this->invoiceCustomRowsModel->select()
            ->where('custom = ?', $originalCustomId)
            ->where('is_input = ?', true)
            ->where('description NOT LIKE ?', '%Beheervergoeding%')
            ->where('description NOT LIKE ?', '%Management fee%')
            ->where('description NOT LIKE ?', 'Kosten %')
            ->order('description')
        ;

        $newTroughputRowsSelect = $this->invoiceCustomRowsModel->select()
            ->where('custom = ?', $newTempCustomId)
            ->where('is_input = ?', true)
            ->where('description NOT LIKE ?', '%Beheervergoeding%')
            ->where('description NOT LIKE ?', '%Management fee%')
            ->where('description NOT LIKE ?', 'Kosten %')
            ->order('description');

        $oldTroughputRows = $this->invoiceCustomRowsModel->fetchAll($oldTroughputRowsSelect);
        $newTroughputRows = $this->invoiceCustomRowsModel->fetchAll($newTroughputRowsSelect);

        $oldTroughputTotal = Money::makeFromEuroInput(array_sum(array_column($oldTroughputRows->toArray(), 'taxprice')));
        $newTroughputTotal = Money::makeFromEuroInput(array_sum(array_column($newTroughputRows->toArray(), 'taxprice')));

        if(!$oldTroughputTotal->equals($newTroughputTotal)) {
            $differenceInCents = $oldTroughputTotal->subtract($newTroughputTotal);

            if ($differenceInCents > 2) {
                throw new \Exception('Troughput total invalid ' . $originalCustomId);
            }

            $correctionRow = null;
            foreach ($newTroughputRows as $newTroughputRow) {
                if ($newTroughputRow->tax_rate > 0) {
                    $correctionRow = $newTroughputRow;
                    break;
                }
            }

            if (!$correctionRow) {
                throw new \Exception('Troughput total invalid ' . $originalCustomId);
            }

            $correctedTaxPriceInCents = Money::makeFromEuroInput($correctionRow->taxprice)
                ->add($differenceInCents)
                ->getAmountInCents();

            $correctionRow->taxprice = formatMoneyInputForDatabase($correctedTaxPriceInCents / 100);
            $correctionRow->save();
        }

    }

    private function replaceInvoiceCustomRows($originalCustomId, $newTempCustomId)
    {

        $this->invoiceCustomRowsModel->delete('custom = ' . $originalCustomId);

        $newRows = $this->invoiceCustomRowsModel->matchAll(['custom' => $newTempCustomId]);

        foreach ($newRows as $newRow) {
            $newRow
                ->setFromArray([
                    'custom' => $originalCustomId
                ])
                ->save();
        }
    }

    private function replaceInvoiceRows($tempInvoiceCustomId, $originalInvoiceId)
    {
        $tempInvoiceCustomData = $this->invoiceCustomLibrary->get($tempInvoiceCustomId);

        $rows = $this->invoiceCustomLibrary->customRowsDataToInvoiceRowsData(
            $tempInvoiceCustomData['rows'],
            $tempInvoiceCustomData['ledger'],
            $tempInvoiceCustomData['cost_center']
        );

        $this->invoiceRowsModel->delete('invoice = ' . $originalInvoiceId);

        foreach ($rows as $row) {
            $this->invoiceRowsModel
                ->createRow(array_merge(['invoice' => $originalInvoiceId], $row))
                ->save();
        }
    }

    private function buildNewInvoicePdf($provisionData, $specificationPdf)
    {
        $this->invoiceCustomModel
            ->getById($provisionData['custom_id'])
            ->setFromArray([
                'attachments' => serialize([$specificationPdf]),
            ])
            ->save();

        $pdf = @$this->invoiceCustomLibrary->buildpdf(
            $provisionData['custom_id'],
            $provisionData['invoice_id']
        );

        $this->invoiceCustomModel
            ->getById($provisionData['custom_id'])
            ->setFromArray([
                'attachments' => nullValue()
            ])
            ->save();

        return $pdf;
    }

    /**
     * @param $tempInvoiceCustomId
     */
    private function deleteTempInvoiceCustom($tempInvoiceCustomId)
    {
        $this->invoiceCustomLibrary->delete($tempInvoiceCustomId);
    }

    /**
     * @param PdfLibrary $pdf
     * @param $invoiceFilename
     */
    private function savePdf(PdfLibrary $pdf, $invoiceFilename)
    {
        file_put_contents($invoiceFilename, $pdf->zendpdf->render());
    }

    private function getInvoiceFilename($invoiceId)
    {
        return $this->invoiceLibrary->getPdfFileFromStorageUsingInvoiceId($invoiceId);
    }
}
