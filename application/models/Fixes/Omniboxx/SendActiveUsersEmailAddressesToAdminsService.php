<?php

namespace Fixes\Omniboxx;

use Acl as AclLibrary;
use EmailOutgoing as EmailOutgoing;
use QueryAllDatabasesService as QueryAllDatabasesService;

class SendActiveUsersEmailAddressesToAdminsService
{
    private $aclLibrary;

    public function __construct(AclLibrary $aclLibrary)
    {
        $this->aclLibrary = $aclLibrary;
    }

    public function execute($adminEmailAddresses = [])
    {
        $activeUsersWithEmailAddressSelect = $this->getActiveUsersWithEmailAddressSelect();

        $results = $this->getResultRows($activeUsersWithEmailAddressSelect);

        $emailAddresses = $this->getUniqueEmailAddressesFromResultRows($results);
        $exportFile = $this->createFormattedExportFileString($emailAddresses);
        $this->sendFileToAdmins($exportFile, $adminEmailAddresses);
    }

    private function getActiveUsersWithEmailAddressSelect()
    {
        return db()->select()
            ->from(
                ['u' => 'users'],
                ['id']
            )
            ->joinLeft(
                ['cea' => 'crm_email_address'],
                'cea.map_to = u.id AND cea.type = "user"',
                ['address']
            )
            ->where('u.type IN (?)', $this->getInternalUserTypes())
            ->where('cea.address LIKE ?', '%@%')
            ->where('cea.address NOT LIKE ?', '%@wennet%')
            ->where('cea.address NOT LIKE ?', '%@omniboxx%')
            ->where('u.lastlogin IS NOT NULL')
            ->where('u.id != ?', 568)
            ->where('u.enddate IS NULL');
    }

    private function getInternalUserTypes()
    {
        return $this->aclLibrary->getInternalRights();
    }

    private function getResultRows($activeUsersWithEmailAddressSelect)
    {
        return (new QueryAllDatabasesService())->execute($activeUsersWithEmailAddressSelect);
    }

    private function getUniqueEmailAddressesFromResultRows($resultRows)
    {
        $results = [];
        foreach ($resultRows as $resultRowsPerDatabase) {
            foreach ($resultRowsPerDatabase as $resultRow) {
                $results[$resultRow['address']] = $resultRow;
            }
        }

        return $results;
    }

    private function createFormattedExportFileString($emailAddresses)
    {
        $formatFileString = '';
        foreach ($emailAddresses as $emailAddress) {
            $formatFileString .= $emailAddress['address'] . "\n";
        }
        return $formatFileString;
    }

    private function sendFileToAdmins($exportFile, $adminEmailAddresses)
    {
        foreach ($adminEmailAddresses as $adminEmailAddress) {
            new EmailOutgoing([
                'to' => ['email' => $adminEmailAddress],
                'subject' => 'Export Omniboxx E-mailadressen tbv. Update Journal',
                'attachments' => [['data' => $exportFile, 'file' => 'Email addressen.txt']],
                'text' => '',
            ]);
        }
    }
}
