<?php

namespace Fixes\Transactions;

class DoubleCronjobCorrectionService
{

    public function execute()
    {
        /*
        * In the case that the double transaction payments rows have a sum of 200% of the transaction.payed, so on a exact doubling of a full payment, one of the doubling-pair can be safely removed
        * This is what we call the 'simple' case
        */
        $this->correctSimpleCompletePayed();
        
        /*
        * In the case that the double transaction payments rows are part of an invoice that has many small transaction_payments, we will try to find combinations of transaction_payments that will
        * bring the sum of the transaction_payments to the total of transaction.payed. The transaction_payments are ordered by prefered delete order, so that this alogoritm will prefer deleting
        * transaction_payments without investor_payed, and it will prefer deleting transaction_payments with higher ids. 
        */        
        $this->correctPartialPayments();
        
        /*
        * In the case that the double transaction payments rows are part of an invoice where the sum of the transaction_payments is higher then the transaction.payed, 
        * we try to see which of the doubling-pairs items we can remove whilst remaining above the sum of transaction.payed.
        * For example, if the sum of transaction payments is 120 and the transaction.payed is 100, we can safely remove a pair of double transactions payment rows with the amount of 10 each.
        */        
        $this->correctTransactionPaymentsWhereSumIsHigherThenPayed();

        /*
        * additionally, we also found two cases of doublings where we found triplets, to be manually resolved
        * these can be found by using the query from the note of this ticket;
        * https://klantenservice.omniboxx.nl/a/tickets/244158
        * and applying the having count = 3
        */
    }

    private function correctTransactionPaymentsWhereSumIsHigherThenPayed(){

        $paymentsSelect = $this->createBaseSelect();

        $paymentsSelect
            ->distinct()
            ->reset(\Zend_Db_Select::COLUMNS)
            ->columns(['tp.transaction'])
            ->where('tp.amount != t.amount')
            ->where('t.type = ?', 'c')
        ;

        $transactionIds = db()->fetchCol($paymentsSelect);
        $transactionPayedAmounts = $this->getTransactionPayedAmounts($transactionIds);
        $transactionPaymentAmounts = $this->getTransactionPaymentAmounts($transactionIds);

        $transactionTypeStrings = $this->fetchTransactionTypeStrings($transactionIds);

        $validatedCorrectionIds = [];
        foreach ($transactionIds as $transactionId) {
            $transactionPaymentAmount = $transactionPaymentAmounts[$transactionId];
            $transactionPayedAmount = $transactionPayedAmounts[$transactionId];

            if ($transactionPaymentAmount <= $transactionPayedAmount) {
                continue;
            }

            $correctPartialPaymentPairsSelect = $this->createBaseSelect();
            $correctPartialPaymentPairsSelect
                ->where('tp.transaction = ?', $transactionId)
                ->order('tp.date DESC');

            $correctionPairs = db()->fetchCol($correctPartialPaymentPairsSelect);

            $correctionIds = $this->determineBestCorrectionIdsForPairs($correctionPairs);

            $transactionPaymentsDetails = $this->getTransactionPaymentDetails($correctionIds);

            foreach ($correctionIds as $correctionId) {
                $transactionPaymentDetails = $transactionPaymentsDetails[$correctionId];

                if ($transactionPaymentDetails['direction'] === 'incoming') {
                    $correctionAmount = $transactionPaymentDetails['amount'];
                } else {
                    $correctionAmount = 0 - $transactionPaymentDetails['amount'];
                }

                $transactionPaymentAmount -= $correctionAmount;

                if ($transactionPaymentAmount < $transactionPayedAmount) {
                    continue;
                }

                $validatedCorrectionIds[] = $correctionId;
            }

        }

        $this->deleteCorrectionIdItems($validatedCorrectionIds);
    }

    private function correctPartialPayments()
    {
        $correctPartialTransactionsSelect = $this->createBaseSelect();

        $correctPartialTransactionsSelect
            ->distinct()
            ->reset(\Zend_Db_Select::COLUMNS)
            ->columns(['tp.transaction'])
//            ->where('t.id = ?', 5419)
//            ->where('t.invoice = ?', 151030)
//                ->where('t.invoice = ?', 55248)
            ->where('tp.amount != t.amount')
            ->where('t.type = ?', 'c')

            // this seems to be more then 100% matched in Twinfield, correction wont make it correct
//            ->where('tp.transaction NOT IN (?)', [5820, 7046])
//            ->where('tp.date != ?', '2023-06-06 00:00:00');

            ;

        $correctPartialTransactions = db()->fetchCol($correctPartialTransactionsSelect);
        $transactionPayedAmounts = $this->getTransactionPayedAmounts($correctPartialTransactions);
        $transactionPaymentAmounts = $this->getTransactionPaymentAmounts($correctPartialTransactions);

        $transactionTypeStrings = $this->fetchTransactionTypeStrings($correctPartialTransactions);

        foreach ($correctPartialTransactions as $correctPartialTransaction) {
            $correctPartialPaymentPairsSelect = $this->createBaseSelect();
            $correctPartialPaymentPairsSelect
                ->where('tp.transaction = ?', $correctPartialTransaction)
                ->order('tp.date DESC');

            $correctionPairs = db()->fetchCol($correctPartialPaymentPairsSelect);
            $correctionPairs = $this->splitCorrectionPairsToArray($correctionPairs);

            $correctionList = $this->splitCorrectionPairsToList($correctionPairs);

            $transactionPaymentIds = $this->getTransactionPaymentIdsFromCorrectionPairs($correctionPairs);

            $transactionPaymentAmount = $transactionPaymentAmounts[$correctPartialTransaction];
            $transactionPayedAmount = $transactionPayedAmounts[$correctPartialTransaction];



            $otherTransactionPaymentSum = $this->calculateTransactionPaymentTotalAmountExcludingPaymentIds(
                $correctPartialTransaction,
                $transactionTypeStrings[$correctPartialTransaction],
                $transactionPaymentIds
            );

            $transactionPayedAmount -= $otherTransactionPaymentSum;

            if ($transactionPaymentAmount <= $transactionPayedAmount) {
                continue;
            }

            $allTransactionPaymentDetails = $this->getTransactionPaymentDetails($transactionPaymentIds);

            $correctionList = $this->addAmountsToCorrectionList($correctionList, $allTransactionPaymentDetails);
            $correctionList = $this->sortCorrectionList($correctionList, $allTransactionPaymentDetails);

            $preserveList = $this->fitOrDie(
                $correctionList,
                $transactionPayedAmount
            );

            if (!$preserveList) {
                $select = db()->select()
                    ->from(['t' => 'transactions'], ['user', 'invoice'])
                    ->joinLeft(['i' => 'invoices'], 'i.id = t.invoice', false)
                    ->joinLeft(['ir' => 'invoices_run'], 'ir.id = i.run', ['identifier' => 'CONCAT(ir.identifier, ".", i.identifier)'])
                    ->where('t.id = ?', $correctPartialTransaction);

                p('geen optie gevonden: ', $correctPartialTransaction, db()->fetchRow($select));
                continue;
            }
            $correctionList = array_diff_key($correctionList, $preserveList);

            if (!$correctionList || count($correctionList) === 0) {
                p('geen correctie nodig');
                continue;
            }

            $this->deleteCorrectionIdItems(array_keys($correctionList));
        }
    }

    /*
     * 1, 2, 3, 4
     *
     * 1, 2, 3, 4
     * 1, 3, 4
     * 1, 4
     * 1
     *
     * 2, 3, 4
     * 2, 3
     * 2
     *
     * 3, 4
     * 4
     *
     */

    private function fitOrDie2(
        $correctionList,
        $transactionPayedAmount
    ){
        $test = $correctionList;
//        p($correctionList);
//        $correctionList = [1 => 1, 2 => 2, 3 => 3, 4 => 4];
        $combinations = [];
        foreach($correctionList as $correctionKey => $correctionItem){
            $combinations[$correctionKey] = [];

            $correctionSubList = array_keys($correctionList);

            foreach($correctionList as $correctionKey2 => $correctionItem2){

                $attempt = array_unique(array_merge([$correctionKey], $correctionSubList));
                $combinations[$correctionKey][md5(implode($attempt, ','))] =  $attempt;

                array_shift($correctionSubList);
            }

            $correctionList = array_slice($correctionList, 1, null, true);
        }

        foreach($combinations as $combination){
            foreach($combination as $combinationList) {
                $combinationTotal = 0;
                $combinationKeys = [];
                foreach ($combinationList as $correctionKey) {
                    $combinationTotal += $test[$correctionKey];
                    $combinationKeys[] = $correctionKey;
                }

                if ($combinationTotal == $transactionPayedAmount) {
                    return $combinationKeys;
                }
            }
        }

        return false;
    }

    private function sortCorrectionList($correctionList, $allTransactionPaymentDetails)
    {
        $sortArray = [];
        foreach ($correctionList as $correctionListKey => $correctionListItem) {
            $investor_payed = $allTransactionPaymentDetails[$correctionListKey]['investor_payed'] == '1';
            $key = $investor_payed . '_' . $correctionListItem . '_' . $correctionListKey;
            $sortArray[$key] = [$correctionListKey, $correctionListItem];
        }
        ksort($sortArray);

        $correctionList = [];
        foreach ($sortArray as $sortListItem) {
            $correctionList[$sortListItem[0]] = $sortListItem[1];
        }

        return $correctionList;
    }

    private function addAmountsToCorrectionList($correctionList, $allTransactionPaymentDetails){
        $correctionListAmounts = [];
        foreach($correctionList as $correctionListId){
            $transactionPaymentDetails = $allTransactionPaymentDetails[$correctionListId];

            if ($transactionPaymentDetails['direction'] === 'incoming') {
                $correctionAmount = $transactionPaymentDetails['amount'];
            } else {
                $correctionAmount = 0 - $transactionPaymentDetails['amount'];
            }

            $correctionListAmounts[$correctionListId] = $correctionAmount;
        }

        return $correctionListAmounts;
    }

    private function fitOrDie(
        $correctionList,
        $transactionPayedAmount,
        $correctionTotalAmount = 0,
        $correctionIds = []
    ) {
        $correctionListSup = $correctionList;
        foreach ($correctionList as $correctionId => $correctionAmount) {

            $correctionIds[$correctionId] = $correctionId;
            $correctionTotalAmount += $correctionAmount;

            if ($correctionTotalAmount == $transactionPayedAmount) {
                return $correctionIds;
            }

            if ($correctionTotalAmount < $transactionPayedAmount) {
                $correctionListSup = array_slice($correctionListSup, 1, null, true);

                $value = $this->fitOrDie($correctionListSup, $transactionPayedAmount, $correctionTotalAmount, $correctionIds);

                if($value === false){
                    $correctionTotalAmount -= $correctionAmount;
                    unset($correctionIds[$correctionId]);
                }
            }

            if ($correctionTotalAmount > $transactionPayedAmount) {
                $correctionTotalAmount -= $correctionAmount;
                unset($correctionIds[$correctionId]);
            }

        }
        return false;
    }

    private function getTransactionPayedAmounts($transactionIds)
    {
        $select = db()->select()
            ->from('transactions', ['id', 'payed'])
            ->where('id IN (?)', $transactionIds);

        return db()->fetchPairs($select);
    }

    private function getTransactionPaymentAmounts($transactionIds)
    {
        $transactionPaymentAmounts = [];

        $transactionTypes = $this->fetchTransactionTypeStrings($transactionIds);

        foreach ($transactionIds as $transactionId) {
            $transactionPaymentAmount = $this->calculateTransactionPaymentTotalAmount(
                $transactionId,
                $transactionTypes[$transactionId]
            );

            $transactionPaymentAmounts[$transactionId] = $transactionPaymentAmount;
        }

        return $transactionPaymentAmounts;
    }

    private function fetchTransactionTypeStrings($transactionIds)
    {
        $select = db()->select()
            ->from(['transactions'], ['id', 'type'])
            ->where('id IN (?)', $transactionIds);

        return db()->fetchPairs($select);
    }

    private function calculateTransactionPaymentTotalAmountExcludingPaymentIds($transactionId, $transactionType, $excludePaymentIds)
    {
        $transactionsPaymentRows = (new \TransactionsPayments())->getByTransactionId(
            $transactionId
        );

        $skipStatusForCalculate = [
            'penalty_payment',
            'penalty_absolve',
            'penalty_manual',
        ];

        $totalAmountTransactionsPayment = 0;
        foreach ($transactionsPaymentRows as $transactionsPaymentRow) {

            if (in_array($transactionsPaymentRow['id'], $excludePaymentIds)) {
                continue;
            }

            if (in_array($transactionsPaymentRow['status'], $skipStatusForCalculate)) {
                continue;
            }

            $transactionsPaymentRowAmountCents = abs((int)$transactionsPaymentRow['amount']);
            if ('c' == $transactionType) {
                if (\TransactionsPayments::DIRECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                } else {
                    if (\TransactionsPayments::DIRECTION_INCOMING == $transactionsPaymentRow['direction']) {
                        $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                    }
                }
            } else {
                if ('d' == $transactionType) {
                    if (\TransactionsPayments::DIRECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                        $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                    } else {
                        if (\TransactionsPayments::DIRECTION_INCOMING == $transactionsPaymentRow['direction']) {
                            $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                        }
                    }
                }
            }
        }
        return $totalAmountTransactionsPayment;
    }

    private function calculateTransactionPaymentTotalAmount($transactionId, $transactionType)
    {
        $transactionsPaymentRows = (new \TransactionsPayments())->getByTransactionId(
            $transactionId
        );

        $skipStatusForCalculate = [
            'penalty_payment',
            'penalty_absolve',
            'penalty_manual',
        ];

        $totalAmountTransactionsPayment = 0;
        foreach ($transactionsPaymentRows as $transactionsPaymentRow) {

            if (in_array($transactionsPaymentRow['status'], $skipStatusForCalculate)) {
                continue;
            }

            $transactionsPaymentRowAmountCents = abs((int)$transactionsPaymentRow['amount']);
            if ('c' == $transactionType) {
                if (\TransactionsPayments::DIRECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                    $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                } else {
                    if (\TransactionsPayments::DIRECTION_INCOMING == $transactionsPaymentRow['direction']) {
                        $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                    }
                }
            } else {
                if ('d' == $transactionType) {
                    if (\TransactionsPayments::DIRECTION_OUTGOING == $transactionsPaymentRow['direction']) {
                        $totalAmountTransactionsPayment += $transactionsPaymentRowAmountCents;
                    } else {
                        if (\TransactionsPayments::DIRECTION_INCOMING == $transactionsPaymentRow['direction']) {
                            $totalAmountTransactionsPayment -= $transactionsPaymentRowAmountCents;
                        }
                    }
                }
            }
        }
        return $totalAmountTransactionsPayment;
    }

    private function correctSimpleCompletePayed()
    {
        $simpleCompletePayedNonInvestorSelect = $this->createBaseSelect();

        $simpleCompletePayedNonInvestorSelect
            ->where('tp.amount = t.amount')
            ->having('COUNT(tp.`id`) = 2');

        $correctionPairs = db()->fetchCol($simpleCompletePayedNonInvestorSelect);

        $correctionIds = $this->determineBestCorrectionIdsForPairs($correctionPairs);
        $this->deleteCorrectionIdItems($correctionIds);
    }

    private function determineBestCorrectionIdsForPairs($correctionPairs)
    {
        $correctionPairs = $this->splitCorrectionPairsToArray($correctionPairs);
        $transactionPaymentIds = $this->getTransactionPaymentIdsFromCorrectionPairs($correctionPairs);

        $transactionPaymentDetails = $this->getTransactionPaymentDetails($transactionPaymentIds);

        $correctionIds = [];
        foreach ($correctionPairs as $correctionPair) {
            $correctionIds[] = $this->findBestCorrectionItemCandidateId($correctionPair, $transactionPaymentDetails);
        }

        return $correctionIds;

    }

    private function deleteCorrectionIdItems($correctionIds)
    {
        db()->delete(
            'transactions_payments',
            'id IN (' . implode_for_where_in($correctionIds) . ' )'
        );
    }

    private function findBestCorrectionItemCandidateId($correctionPair, $transactionPaymentDetails)
    {
        $firstCorrectionItem = $correctionPair[0];
        $firstCorrectionItemDetails = $transactionPaymentDetails[$firstCorrectionItem];
        $secondCorrectionItem = $correctionPair[1];
        $secondCorrectionItemDetails = $transactionPaymentDetails[$secondCorrectionItem];

        if (
            $firstCorrectionItemDetails['investor_payed'] == '1' &&
            $secondCorrectionItemDetails['investor_payed'] == '0'
        ) {
            return $secondCorrectionItem;
        }

        if (
            $firstCorrectionItemDetails['investor_payed'] == '0' &&
            $secondCorrectionItemDetails['investor_payed'] == '1'
        ) {
            return $firstCorrectionItem;
        }

        return max($firstCorrectionItem, $secondCorrectionItem);
    }

    private function splitCorrectionPairsToArray($correctionPairs)
    {
        foreach ($correctionPairs as &$correctionPair) {
            $correctionPair = explode(',', $correctionPair);
        }

        return $correctionPairs;
    }
    private function splitCorrectionPairsToList($correctionPairs)
    {
        $correctionList = [];
        foreach ($correctionPairs as $correctionPair) {
            $correctionList[] = $correctionPair[0];
            $correctionList[] = $correctionPair[1];
        }
        return $correctionList;
    }

    private function getTransactionPaymentDetails($transactionPaymentIds)
    {
        $select = db()->select()
            ->from('transactions_payments', ['*'])
            ->where('id IN (?)', $transactionPaymentIds);

        return db()->fetchAssoc($select);
    }

    private function getTransactionPaymentIdsFromCorrectionPairs($correctionPairs)
    {
        $transactionPaymentIds = [];

        foreach ($correctionPairs as $correctionPair) {
            $transactionPaymentIds = array_merge($transactionPaymentIds, $correctionPair);
        }

        return $transactionPaymentIds;
    }

    private function createBaseSelect()
    {
        $baseSelect = db()->select()
            ->from(
                ['tp' => 'transactions_payments'],
                [
                    'GROUP_CONCAT(tp.id)',
                ]
            )
            ->joinLeft(
                ['t' => 'transactions'],
                't.id = tp.transaction',
                null
            )
            ->where('tp.description = ?', 'Betaling gematched in Twinfield')
            ->group([
                'DATE_FORMAT(tp.date_created, "%Y-%m%-%d")',
                'tp.date',
                'tp.amount',
                'tp.direction',
                'tp.transaction',
            ])
//            ->where('tp.id NOT IN (8903, 9259)')
            ->having('COUNT(tp.`id`) = 2');

        return $baseSelect;
    }

}
