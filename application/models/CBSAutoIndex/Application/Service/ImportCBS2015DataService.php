<?php

namespace CBSAutoIndex\Application\Service;

use CBSAutoIndex\Application\Service\AutoFillCBSIndexRowsService;
use DbTable\CBSIndexDataSet;
use DbTable\CBSIndexPeriod;
use DbTable\CBSIndexType;
use GuzzleHttp\Client;

class ImportCBS2015DataService
{
    const BASE_URL = 'https://opendata.cbs.nl/odataapi/odata';
    const NUMBER = '83131NED';

    private $guzzleClient;
    private $cbsIndexTypeModel;
    private $cbsIndexPeriodModel;
    private $cbsIndexDataSetModel;

    public function __construct()
    {
        $this->guzzleClient = new Client();
        $this->cbsIndexTypeModel = new CBSIndexType();
        $this->cbsIndexPeriodModel = new CBSIndexPeriod();
        $this->cbsIndexDataSetModel = new CBSIndexDataSet();
    }

    public function execute()
    {
        $cbsIndexTypeRow = $this->findOrFailCbsIndexTypeRow();
        $data = $this->retrievePeriodDataFromCBS();

        if (empty($data['value'])) {
            throw new \RuntimeException('Something went wrong, no data found');
        }

        foreach ($data['value'] as $periodData) {
            $this->createOrUpdateCbsIndexPeriodRow($periodData, $cbsIndexTypeRow);
        }

        $data = $this->retrieveDataSetFromCBS();

        if (empty($data['value'])) {
            throw new \RuntimeException('Something went wrong, no data found');
        }

        foreach ($data['value'] as $dataSet) {
            $this->createOrUpdateCbsIndexDataSetRow($cbsIndexTypeRow, $dataSet);
        }

        (new AutoFillCBSIndexRowsService())->execute();

        $cbsIndexTypeRow->updated_at = (new \DateTime())->format('Y-m-d H:i:s');
        $cbsIndexTypeRow->save();
    }

    private function findOrFailCbsIndexTypeRow()
    {
        $cbsIndexTypeRow = $this->cbsIndexTypeModel->fetchRow(['number = ?' => self::NUMBER]);

        if (null === $cbsIndexTypeRow) {
            throw new \RuntimeException('Could not find CBS Index Type Row by number: ' . self::NUMBER);
        }

        return $cbsIndexTypeRow;
    }

    private function retrievePeriodDataFromCBS()
    {
        $url = self::BASE_URL . "/" . self::NUMBER . "/Perioden";
        $response = $this->guzzleClient->get($url);
        $data = $response->getBody()->getContents();

        return json_decode($data, true);
    }

    private function createOrUpdateCbsIndexPeriodRow($periodData, \Zend_Db_Table_Row_Abstract $cbsIndexTypeRow)
    {
        $indexPeriodRow = $this->cbsIndexPeriodModel->fetchRow([
            'cbs_index_type_id = ?' => $cbsIndexTypeRow->id,
            '`key` = ?' => $periodData['Key']
        ]);

        if (null === $indexPeriodRow) {
            $indexPeriodRow = $this->cbsIndexPeriodModel->createRow();
        }

        $indexPeriodRow->cbs_index_type_id = $cbsIndexTypeRow->id;
        $indexPeriodRow->key = $periodData['Key'];
        $indexPeriodRow->title = $periodData['Title'];
        $indexPeriodRow->description = $periodData['Description'];
        $indexPeriodRow->status = $periodData['Status'];
        $indexPeriodRow->save();
    }

    private function retrieveDATASETFromCbs()
    {
        $type = 'UntypedDataSet';

        $bestedingsCategorie = 'T001112%20%20'; // Alle bestedingen
        $filters = "Bestedingscategorieen%20eq%20%27$bestedingsCategorie%27";
        $filter = '$filter=(' . $filters . ')';
        $url = self::BASE_URL . "/" . self::NUMBER . "/$type?$filter";

        $response = $this->guzzleClient->get($url);

        $data = $response->getBody()->getContents();

        return json_decode($data, true);
    }

    private function createOrUpdateCbsIndexDataSetRow(\Zend_Db_Table_Row_Abstract $cbsIndexTypeRow, $dataSet)
    {
        $indexDataSetRow = $this->cbsIndexDataSetModel->fetchRow([
            'cbs_index_type_id = ?' => $cbsIndexTypeRow->id,
            'perioden = ?' => $dataSet['Perioden']
        ]);

        if (null === $indexDataSetRow) {
            $indexDataSetRow = $this->cbsIndexDataSetModel->createRow();
        }

        $indexDataSetRow->cbs_index_type_id = $cbsIndexTypeRow->id;
        $indexDataSetRow->bestedingscategorieen = $dataSet['Bestedingscategorieen'];
        $indexDataSetRow->perioden = $dataSet['Perioden'];
        $indexDataSetRow->cpi_1 = $dataSet['CPI_1'];
        $indexDataSetRow->cpi_afgeleid_2 = $dataSet['CPIAfgeleid_2'];
        $indexDataSetRow->maandmutatie_cpi_3 = $dataSet['MaandmutatieCPI_3'];
        $indexDataSetRow->maandmutatie_cpi_afgeleid_4 = $dataSet['MaandmutatieCPIAfgeleid_4'];
        $indexDataSetRow->jaarmutatie_cpi_5 = $dataSet['JaarmutatieCPI_5'];
        $indexDataSetRow->jaarmutatie_cpi_afgeleid_6 = $dataSet['JaarmutatieCPIAfgeleid_6'];
        $indexDataSetRow->wegingscoefficient_7 = $dataSet['Wegingscoefficient_7'];
        $indexDataSetRow->save();
    }
}
