<?php

namespace CBSAutoIndex\Application\Service;

use CBSAutoIndex\Domain\Model\CBSDataSetRepository;
use CBSAutoIndex\Domain\Service\AutoFillMonthlyIndexTypeService;
use CBSAutoIndex\Domain\Service\AutoFillYearlyIndexTypeService;

class AutoFillCBSIndexRowsService
{
    private $indexesTypesModel;
    private $autoFillMonthlyIndexTypeService;
    private $autoFillYearlyIndexTypeService;
    private $cbsDataSetRepository;
    private $importCbs2015DataService;


    public function __construct()
    {
        $this->indexesTypesModel = new \IndexesTypesTable();
        $this->autoFillMonthlyIndexTypeService = new AutoFillMonthlyIndexTypeService();
        $this->autoFillYearlyIndexTypeService = new AutoFillYearlyIndexTypeService();
        $this->cbsDataSetRepository = new CBSDataSetRepository();
        $this->importCbs2015DataService = new ImportCBS2015DataService();
    }

    public function execute($indexesTypeId = null)
    {
        // This is needed in the case if the import function has never been called before
        if (!$this->cbsDataSetRepository->hasData()) {
            $this->importCbs2015DataService->execute();
        }

        $where = [
            'auto_fill_cbs_data != ?' => 'no',
            'period_type IS NOT NULL',
            'type = ?' => 'commercial'
        ];

        if ($indexesTypeId) {
            $where['id = ?'] = $indexesTypeId;
        }

        $indexesTypesRows = $this->indexesTypesModel->fetchAll($where);

        foreach ($indexesTypesRows as $indexesTypesRow) {
            if ($indexesTypesRow->period_type === 'yearly') {
                $this->autoFillYearlyIndexTypeService->execute($indexesTypesRow);
            } else if ($indexesTypesRow->period_type === 'monthly') {
                $this->autoFillMonthlyIndexTypeService->execute($indexesTypesRow);
            }
        }
    }
}
