<?php

namespace CBSAutoIndex\Domain\Model;

class CBSDataSetRepository
{
    public function hasData($type = '2015_100')
    {
        return db()->select()
            ->from(['cip' => 'cbs_index_period'], ['*'])
            ->joinLeft(['cit' => 'cbs_index_type'], 'cit.id = cip.cbs_index_type_id', false)
            ->where('cit.type = ?', $type)
            ->joinLeft(['cids' => 'cbs_index_data_set'], 'cids.cbs_index_type_id = cit.id AND cids.perioden = cip.key', ['*'])
            ->query()
            ->rowCount();
    }

    public function getYearlySet($type, $concept = false)
    {
        $cbsDataQuery = db()->select()
            ->from(['cip' => 'cbs_index_period'], ['cip_id' => 'cip.id', 'key'])
            ->joinLeft(['cit' => 'cbs_index_type'], 'cit.id = cip.cbs_index_type_id', false)
            ->where('cit.type = ?', $type)
            ->joinLeft(['cids' => 'cbs_index_data_set'], 'cids.cbs_index_type_id = cit.id AND cids.perioden = cip.key', ['*']);
             if (!$concept) {
                 $cbsDataQuery->where('cip.status = ?', 'Definitief');
             }
            $cbsData = db()->fetchAll($cbsDataQuery);

        $data = [];
        foreach ($cbsData as $cbsLine) {
            if (strpos($cbsLine['key'], 'JJ') === false) {
                continue;
            }

            $year = substr($cbsLine['key'], 0, 4);
            $data[$year] = $cbsLine;
        }

        return $data;
    }

    public function getMonthlySet($type, $concept = false )
    {

        $cbsDataQuery = db()->select()
            ->from(['cip' => 'cbs_index_period'], ['cip_id' => 'cip.id', 'key'])
            ->joinLeft(['cit' => 'cbs_index_type'], 'cit.id = cip.cbs_index_type_id', false)
            ->where('cit.type = ?', $type)
            ->joinLeft(['cids' => 'cbs_index_data_set'], 'cids.cbs_index_type_id = cit.id AND cids.perioden = cip.key', ['*']);
            if (!$concept) {
                $cbsDataQuery->where('cip.status = ?', 'Definitief');
            }
            $cbsData = db()->fetchAll($cbsDataQuery);

        $data = [];
        foreach ($cbsData as $cbsLine) {
            if (strpos($cbsLine['key'], 'JJ') !== false) {
                continue;
            }

            $year = substr($cbsLine['key'], 0, 4);
            $month = (int)substr($cbsLine['key'], -2);
            $data[$year][$month] = $cbsLine;
        }

        return $data;
    }
}
