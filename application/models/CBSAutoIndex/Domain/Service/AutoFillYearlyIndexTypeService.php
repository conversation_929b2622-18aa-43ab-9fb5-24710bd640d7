<?php

namespace CBSAutoIndex\Domain\Service;

use CBSAutoIndex\Domain\Model\CBSDataSetRepository;

class AutoFillYearlyIndexTypeService
{
    private $indexesTypesIndexesModel;
    private $cbsDataSetRepository;

    public function __construct()
    {
        $this->indexesTypesIndexesModel = new \IndexesTypesIndexesTable();
        $this->cbsDataSetRepository = new CBSDataSetRepository();
    }

    public function execute(\Zend_Db_Table_Row_Abstract $indexesTypesRow)
    {
        $cbsDataSet = $this->cbsDataSetRepository->getYearlySet($indexesTypesRow->auto_fill_cbs_data, $indexesTypesRow->allow_concept_cbs_data);

        $twoYearsAgo = new \DateTime('-2 years');
        $now = new \DateTime();
        $interval = new \DateInterval('P1Y');
        $dateRange = new \DatePeriod($twoYearsAgo, $interval, $now);

        foreach ($dateRange as $intervalYear) {
            $year = $intervalYear->format('Y');

            // skip when cbs data unknown is for the period
            if (!isset($cbsDataSet[$year])) {
                continue;
            }

            $indexesTypesIndexesRow = $this->indexesTypesIndexesModel->fetchRow([
                'type = ?' => $indexesTypesRow->id,
                'year = ?' => $year
            ]);

            // skip every manual previous input
            if ($indexesTypesIndexesRow && !$indexesTypesIndexesRow->cbs_index_period_id) {
                continue;
            }

            if (null === $indexesTypesIndexesRow) {
                $indexesTypesIndexesRow = $this->indexesTypesIndexesModel->createRow();
            }

            $indexesTypesIndexesRow->type = $indexesTypesRow->id;
            $indexesTypesIndexesRow->start = "$year-01-01";

            $nextYear = $year + 1;
            $indexesTypesIndexesRow->end = "$nextYear-01-01";
            $indexesTypesIndexesRow->value = $cbsDataSet[$year]['cpi_1'];
            $indexesTypesIndexesRow->year = $year;
            $indexesTypesIndexesRow->cbs_index_period_id = $cbsDataSet[$year]['cip_id'];
            $indexesTypesIndexesRow->save();
        }
    }
}
