<?php

namespace CBSAutoIndex\Domain\Service;

use CBSAutoIndex\Domain\Model\CBSDataSetRepository;

class AutoFillMonthlyIndexTypeService
{
    private $indexesTypesIndexesModel;
    private $cbsDataSetRepository;

    public function __construct()
    {
        $this->indexesTypesIndexesModel = new \IndexesTypesIndexesTable();
        $this->cbsDataSetRepository = new CBSDataSetRepository();
    }

    public function execute(\Zend_Db_Table_Row_Abstract $indexesTypesRow)
    {
        $cbsDataSet = $this->cbsDataSetRepository->getMonthlySet($indexesTypesRow->auto_fill_cbs_data, $indexesTypesRow->allow_concept_cbs_data);

        $twoYearsAgo = new \DateTime('-2 years');
        $now = new \DateTime();
        $interval = new \DateInterval('P1M');
        $dateRange = new \DatePeriod($twoYearsAgo, $interval, $now);

        foreach ($dateRange as $intervalMonth) {
            $year = $intervalMonth->format('Y');
            $month = $intervalMonth->format('n');

            // skip when cbs data is unknown for the period
            if (!isset($cbsDataSet[$year][$month])) {
                continue;
            }

            $indexesTypesIndexesRow = $this->indexesTypesIndexesModel->fetchRow([
                'type = ?' => $indexesTypesRow->id,
                'year = ?' => $year,
                'month = ?' => $month
            ]);

            // skip every manual previous input
            if ($indexesTypesIndexesRow && !$indexesTypesIndexesRow->cbs_index_period_id) {
                continue;
            }

            if (null === $indexesTypesIndexesRow) {
                $indexesTypesIndexesRow = $this->indexesTypesIndexesModel->createRow();
            }

            $indexesTypesIndexesRow->type = $indexesTypesRow->id;
            $indexesTypesIndexesRow->value = $cbsDataSet[$year][$month]['cpi_1'];
            $indexesTypesIndexesRow->year = $year;
            $indexesTypesIndexesRow->month = $month;
            $indexesTypesIndexesRow->cbs_index_period_id = $cbsDataSet[$year][$month]['cip_id'];
            $indexesTypesIndexesRow->save();
        }
    }
}
