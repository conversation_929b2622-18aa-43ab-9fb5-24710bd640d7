<?php

namespace RoomSelector\Upload;

class UploadCacheService
{
    private $credential;

    public function __construct(Credentials $credential)
    {
        $this->credential = $credential;
    }

    public function execute($tmpObjects)
    {
        $company = \Settings::get('general_company_shortname');
        try {
            $sftp = new SFTPConnection($this->credential->getHost(), 22);
            $sftp->login($this->credential->getUsername(), $this->credential->getPassword());
            $sftp->uploadFile($tmpObjects, $this->credential->getUploadDirectory().$company.'/objects.json');
        } catch (FileNotSendException $e) {
            $this->sendEmailToSupport($e);
        } catch (\Exception $e) {
            $this->sendEmailToSupport($e);
        }
    }

    private function sendEmailToSupport(\Exception $e)
    {
        new \EmailOutgoing([
            'to'		=> '<EMAIL>',
            'from'		=> '<EMAIL>',
            'subject'	=> 'Kon geen verbinding gemaakt worden met de Roomselector',
            'text'		=> "<p>Het is niet gelukt om het cache bestand te versturen naar de Roomselector voor " . \Settings::get('company_name') . "</p> <p>" . $e->getMessage(). "</p>"
        ]);
    }
}
