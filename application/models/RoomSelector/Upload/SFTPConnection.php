<?php

namespace RoomSelector\Upload;


class SFTPConnection
{
    private $connection;
    private $sftp;

    public function __construct($host, $port = 22)
    {
        $this->connection = ssh2_connect($host, $port);
        if (!$this->connection)
            throw new \Exception("Could not connect to $host on port $port.");
    }

    public function login($username, $password)
    {
        if (!@ssh2_auth_password($this->connection, $username, $password))
            throw new \Exception("Could not authenticate with username $username " .
                "and password $password.");

        $this->sftp = ssh2_sftp($this->connection);
        if (!$this->sftp)
            throw new \Exception("Could not initialize SFTP subsystem.");
    }

    public function uploadFile($local_file, $remote_file)
    {
        $isSend = ssh2_scp_send($this->connection, $local_file, $remote_file, 0644);

        if (!$isSend)
            throw new FileNotSendException("Could not send data from file: $local_file.");
    }
}