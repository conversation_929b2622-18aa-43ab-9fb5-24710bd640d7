<?php

namespace RoomSelector\ObjectData;


class GetAllObjectsForWebsiteService extends GetForWebsiteService
{
    public function execute()
    {
        $objects = $this->getAllObjects();

        $data = [];
        foreach ($objects as $object) {
            $objectFromCache = $this->loadObjectFromCache($object['id']);

            if ($objectFromCache) {
                $data[$object['objectgroup_id'] . '_' . $object['build']] = $objectFromCache;
                continue;
            }

            list($object, $cacheTags) = $this->getObjectAdditionalData($object);

            if (!$object || !$cacheTags)
                continue;

            $this->putObjectInCache($object, $cacheTags);
            $data[$object['objectgroup_id'] . '_' . $object['build']] = $object;
        }

        return $data;
    }

    private function getAllObjects()
    {
        return db()->select()
            ->from(['o' => 'objects'], ['id', 'build', 'm2'])
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['objectgroup_id' => 'id'])
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['project_id' => 'p.id'])
            ->joinLeft(['ot' => 'object_type'], 'ot.id = o.type', 'name')
            ->joinLeft(['a' => 'address'], 'a.type_id = o.id AND a.type ="object"', ['number'])
            ->where('p.vidii_enabled = ? ' . $this->publishedToRoomSelector(), 'yes')
            ->query()
            ->fetchAll();
    }

    private function publishedToRoomSelector()
    {
        return "OR p.roomselector_projectname IS NOT NULL OR p.roomselector_url IS NOT NULL OR p.roomselector_locationmeeting IS NOT NULL OR p.roomselector_contractcosts IS NOT NULL";
    }
}