<?php

namespace RoomSelector\ObjectData;

abstract class GetForWebsiteService
{
    private $components;
    private $ocversion_model;
    private $ocvalues_model;
    private $uo_model;

    public function __construct()
    {
        $this->components = $this->getComponents();
        $this->ocversion_model = new \ObjectsComponentsVersions();
        $this->ocvalues_model = new \ObjectsComponentsValues();
        $this->invoicerun = new \InvoiceRun();
        $this->uo_model = new \Objectusers();
    }

    protected function loadObjectFromCache($objectId)
    {
        $cacheIndent = $this->makeCacheIndent($objectId);
        return \Cache_RoomselectorObjectsAvailable::getInstance($cacheIndent)->load();
    }

    private function makeCacheIndent($objectId)
    {
        return "object_$objectId";
    }

    protected function getObjectAdditionalData($object)
    {
        $cacheTags = [
            'objectgroup_' . $object['objectgroup_id'],
            'object_' . $object['id']
        ];

        $ocv_version = $this->ocversion_model->getHighestForObject($object['id']);

        if (!$ocv_version)
            return [false, false];

        $ocv_values = $this->ocvalues_model->getArrayList($object['id'], $ocv_version['id']);

        $object['furnature'] = false;

        $rental_labels = ['Huur'];

        if (\Settings::get('general_company_shortname') == 'ravel')
            $rental_labels[] = 'Meubels';

        if (\Settings::get('general_company_shortname') == 'MVGM') {
            $rental_labels[] = 'Huur onbelast';
            $rental_labels[] = 'Huur belast';
        }

        foreach ($ocv_values as $ocv_value) {

            $component = $this->components[$ocv_value['component']];

            if ($component['name'] == 'Meubels' && $ocv_value['value_excl'] > 0)
                $object['furnature'] = true;

            foreach (['excl', 'incl'] as $vat_type)
                $object['price'][in_array($component['name'], $rental_labels) ? 'rent' : 'service'][$vat_type] += ($vat_type == 'excl' ? $ocv_value['value_excl'] : $ocv_value['value_incl']);
        }


        $this->invoicerun->object = $object['id'];
        $object['status'] = 'empty';
        $object['name'] = str_replace(' ', '', $object['name']);

        $user_object = $this->uo_model->getCurrentForObject($object['id']);

        if ($user_object) {

            $cacheTags[] = 'users_objects_' . $user_object->id;
            if ($user_object->customer > 0) {
                $u = new \User();
                $hascontract = $u->getCurrentContract($user_object->customer);
                $object['status'] = $hascontract ? 'occupied' : 'optional';
                if ($object['status'] == 'optional') {
                    $object['status'] = $user_object->contract_id ? 'occupied' : 'optional';
                }
            } else {

                if (\Settings::get('modules_tenantLogin_roomselector_manually_control_availability')) {

                    $alert_status = \ObjectsAlert::currentStatus($object['id']);

                    if ($alert_status == 'available') {
                        $object['status'] = 'empty';
                    } elseif ($alert_status == 'reserved') {
                        $object['status'] = 'optional';
                    } else {
                        $object['status'] = 'occupied';
                    }
                }

                $object['futuredate'] = $user_object->from;
            }
        }

        return [$object, $cacheTags];
    }

    protected function getComponents()
    {
        $rows = db()->select()
            ->from('components')
            ->query()
            ->fetchAll();

        $components = [];
        foreach ($rows as $row)
            $components[$row['id']] = $row;

        return $components;
    }

    protected function putObjectInCache($object, $cacheTags)
    {
        $cacheTags = array_values(array_unique($cacheTags));
        $cacheIndent = $this->makeCacheIndent($object['id']);
        \Cache_RoomselectorObjectsAvailable::getInstance($cacheIndent)->save($object, $cacheTags);
    }
}