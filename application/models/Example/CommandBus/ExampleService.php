<?php

namespace Example\CommandBus;

use Example\Entities\Example;
use SharedKernel\Application\Service\ApplicationService;

class ExampleService implements ApplicationService
{

    /**
     * @param ExampleRequest $request
     * @return \Example\Entities\Example
     * @throws \InvalidArgumentException
     */
    public function execute($request)
    {
        $this->guardAgainstInvalidInput($request);
        $content = $this->doStuffWithContent($request->getContent());
        return new Example($content);
    }

    /**
     * @param ExampleRequest $request
     * @throws \InvalidArgumentException
     */
    private function guardAgainstInvalidInput(ExampleRequest $request)
    {
        if (trim($request->getContent()) === '') {
            throw new \InvalidArgumentException('Content parameter may not be empty');
        }
    }

    /**
     * @param string $content
     * @return string
     */
    private function doStuffWithContent($content)
    {
        return strrev(strrev($content));
    }
}
