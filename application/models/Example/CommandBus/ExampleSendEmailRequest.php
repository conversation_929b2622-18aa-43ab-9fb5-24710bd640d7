<?php

namespace Example\CommandBus;

class ExampleSendEmailRequest
{
    private $fromName;
    private $fromEmail;
    private $toName;
    private $toEmail;
    private $subject;
    private $content;

    /**
     * @param string $fromName
     * @param string $fromEmail
     * @param string $toName
     * @param string $toEmail
     * @param string $subject
     * @param string $content
     */
    public function __construct($fromName, $fromEmail, $toName, $toEmail, $subject, $content)
    {
        $this->fromName = $fromName;
        $this->fromEmail = $fromEmail;
        $this->toName = $toName;
        $this->toEmail = $toEmail;
        $this->subject = $subject;
        $this->content = $content;
    }

    /**
     * @return string
     */
    public function getFromName()
    {
        return $this->fromName;
    }

    /**
     * @return string
     */
    public function getFromEmail()
    {
        return $this->fromEmail;
    }

    /**
     * @return string
     */
    public function getToName()
    {
        return $this->toName;
    }

    /**
     * @return string
     */
    public function getToEmail()
    {
        return $this->toEmail;
    }

    /**
     * @return string
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }
}
