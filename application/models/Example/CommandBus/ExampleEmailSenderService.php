<?php

namespace Example\CommandBus;

use EmailOutgoing;

// The name of this class intentionally is not resolvable by the AutoFindApplicationServiceLocatorDecorator from its
// request (ExampleSendEmailRequest) to provide an example of how to handle such situations via the mapping file.
class ExampleEmailSenderService
{
    /**
     * @param ExampleSendEmailRequest $request
     * @return void
     */
    public function execute($request)
    {
        // note: EmailOutgoing moet eigenlijk geinject worden
        new EmailOutgoing([
            'from' => [
                'name' => $request->getFromName(),
                'email' => $request->getFromEmail()
            ],
            'to' => [
                'name' => $request->getToName(),
                'email' => $request->getToEmail()
            ],
            'subject' => $request->getSubject(),
            'notemplate' => $request->getContent(),
        ]);
    }
}
