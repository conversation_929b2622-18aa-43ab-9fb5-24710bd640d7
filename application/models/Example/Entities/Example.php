<?php

namespace Example\Entities;

use Example\DomainEvents\ExampleCreatedEvent;
use SharedKernel\Domain\DomainEventPublisher;

class Example
{
    /** @var int */
    private $id;
    /** @var string */
    private $content;

    /**
     * @param $content string
     */
    public function __construct($content)
    {
        $this->content = $content;

        DomainEventPublisher::instance()->publish(
            new ExampleCreatedEvent()
        );
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * @param string $content
     */
    public function setContent($content)
    {
        $this->content = $content;
    }
}
