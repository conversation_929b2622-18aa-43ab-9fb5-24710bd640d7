<?php

namespace Example\DomainEvents;

use EmailOutgoing;
use Example\CommandBus\ExampleSendEmailRequest;
use League\Tactician\CommandBus;
use SharedKernel\Application\Service\AsyncWorkerRequest;
use SharedKernel\Domain\DomainEvent;
use Shared<PERSON>ernel\Domain\DomainEventSubscriber;

class ExampleCreatedEventSubscriber implements DomainEventSubscriber
{
    private $commandBus;

    const SEND_DIRECTLY = 0;
    const SEND_VIA_COMMAND_BUS = 1;
    const SEND_VIA_COMMAND_BUS_ASYNC = 2;

    public function __construct(CommandBus $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    /**
     * @param DomainEvent $aDomainEvent
     */
    public function handle($aDomainEvent)
    {
        // in normal situations you choose one of these, this switch is purely for example purposes
        $method = self::SEND_VIA_COMMAND_BUS_ASYNC;

        switch ($method) {
            case (self::SEND_DIRECTLY):
                $this->sendDirectly($aDomainEvent);
                break;

            case (self::SEND_VIA_COMMAND_BUS):
                $this->sendViaCommandBus($aDomainEvent);
                break;

            case (self::SEND_VIA_COMMAND_BUS_ASYNC):
                $this->sendViaCommandBusAsync($aDomainEvent);
                break;
        }
    }

    /**
     * @param DomainEvent $aDomainEvent
     * @return bool
     */
    public function isSubscribedTo($aDomainEvent)
    {
        return get_class($aDomainEvent) === ExampleCreatedEvent::class;
    }

    private function sendDirectly($aDomainEvent)
    {
        new EmailOutgoing([
            'from' => [
                'name' => 'FromExampleName',
                'email' => '<EMAIL>',
            ],
            'to' => [
                'name' => 'ToExampleName',
                'email' => '<EMAIL>',
            ],
            'subject' => 'Er is een Example aangemaakt!',
            'notemplate' => sprintf('Event: %s', get_class($aDomainEvent)),
        ]);
    }

    private function sendViaCommandBus($aDomainEvent)
    {
        $this->commandBus->handle(
            new ExampleSendEmailRequest(
                'FromExampleName',
                '<EMAIL>',
                'ToExampleName',
                '<EMAIL>',
                'Er is een Example aangemaakt!',
                sprintf('Event: %s', get_class($aDomainEvent))
            )
        );
    }

    private function sendViaCommandBusAsync($aDomainEvent)
    {
        $this->commandBus->handle(
            new AsyncWorkerRequest(
                new ExampleSendEmailRequest(
                    'FromExampleName',
                    '<EMAIL>',
                    'ToExampleName',
                    '<EMAIL>',
                    'Er is een Example aangemaakt!',
                    sprintf('Event: %s', get_class($aDomainEvent))
                )
            )
        );
    }
}
