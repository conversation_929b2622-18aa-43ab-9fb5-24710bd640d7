<?

namespace Complaint\Infrastructure\DTO;

class NoLoginUrlDTO {
	private $complaintId;
	private $complaintType;
	private $preview;
	private $valid = false;

	/**
	 * NoLoginUrlDTO constructor.
	 *
	 * @param int $complaintId
	 * @param string $complaintType
	 * @param bool $preview
	 *
	 */
	public function __construct( $complaintId, $complaintType, $preview = false ) {
		$this->complaintId   = $complaintId;
		$this->complaintType = $complaintType;
		$this->preview       = $preview;

		if ( is_numeric( $complaintId ) && intval( $complaintId ) > 0 && trim( $complaintType ) != false ) {
			$this->valid = true;
		}
	}

	/**
	 * @return boolean
	 */
	public function isPreview() {
		return $this->preview;
	}

	/**
	 * @return boolean
	 */
	public function isValid() {
		return $this->valid;
	}

	/**
	 * @return int
	 */
	public function getComplaintId() {
		return $this->complaintId;
	}

	/**
	 * @return string
	 */
	public function getComplaintType() {
		return $this->complaintType;
	}

}