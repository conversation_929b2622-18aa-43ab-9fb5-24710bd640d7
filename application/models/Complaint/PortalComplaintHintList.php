<?php

namespace Complaint;

class PortalComplaintHintList
{

    public function getList()
    {
        $select = db()->select()
            ->from(
                'support_complaints_categories',
                ['id', 'attentionfield', 'attentionfield_en', 'attentionfield_fr']
            )
            ->where('COALESCE(attentionfield, attentionfield_en, attentionfield_fr) IS NOT NULL');

        $categoryHintList = db()->fetchAssoc($select);

        foreach ($categoryHintList as $categoryHintListKey => $categoryHintListItem) {
            $categoryHintListItem['attentionfield'] = trim($categoryHintListItem['attentionfield']);
            $categoryHintListItem['attentionfield_en'] = trim($categoryHintListItem['attentionfield_en']);
            $categoryHintListItem['attentionfield_fr'] = trim($categoryHintListItem['attentionfield_fr']);            
            $categoryHintList[$categoryHintListKey]['attentionfield'] = nl2br($categoryHintListItem['attentionfield']);
            $categoryHintList[$categoryHintListKey]['attentionfield_en'] = nl2br($categoryHintListItem['attentionfield_en']);
            $categoryHintList[$categoryHintListKey]['attentionfield_fr'] = nl2br($categoryHintListItem['attentionfield_fr']);
        }


        $select = db()->select()
            ->from(
                'support_complaints_subcategories',
                ['id', 'attentionfield', 'attentionfield_en', 'attentionfield_fr']
            )
            ->where('COALESCE(attentionfield, attentionfield_en, attentionfield_fr) IS NOT NULL');

        $subCategoryHintList = db()->fetchAssoc($select);

        foreach ($subCategoryHintList as $subCategoryHintListKey => $subCategoryHintListItem) {
            $subCategoryHintListItem['attentionfield'] = trim($subCategoryHintListItem['attentionfield']);        
            $subCategoryHintListItem['attentionfield_en'] = trim($subCategoryHintListItem['attentionfield_en']);        
            $subCategoryHintListItem['attentionfield_fr'] = trim($subCategoryHintListItem['attentionfield_fr']);        
            $subCategoryHintList[$subCategoryHintListKey]['attentionfield'] = nl2br($subCategoryHintListItem['attentionfield']);
            $subCategoryHintList[$subCategoryHintListKey]['attentionfield_en'] = nl2br($subCategoryHintListItem['attentionfield_en']);
            $subCategoryHintList[$subCategoryHintListKey]['attentionfield_fr'] = nl2br($subCategoryHintListItem['attentionfield_fr']);
        }

        return [
            'categoryHintList' => $categoryHintList,
            'subCategoryHintList' => $subCategoryHintList,
        ];
    }
}
