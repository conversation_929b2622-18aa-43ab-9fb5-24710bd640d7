<?php

namespace Hospitality;


class Projects extends \GlobalDbClass
{
    protected $_name = 'hospitality_projects';

    public $attributes = [
        'category_id' => [
            'title' => 'Categorie',
            'width' => 'xlarge truncate',
        ],
        'products' => [
            'title' => 'Producten',
            'width' => 'medium truncate',
        ]
    ];

    public function save($data, $params)
    {
        $data['general']['products'] = $this->serializeProducts($data['general']['products']);

        parent::save($data, $params);
    }

    private function serializeProducts($products)
    {
        return $products ? implode(',', $products) : '';
    }

    public function toFormArray($form, $params, $data = [])
    {
        $output = parent::toFormArray($form, $params, $data);
        $output['general']['products'] = explode(',', $output['general']['products']);

        return $output;
    }

    public function getList($params = [], $select = null)
    {
        $params = $this->unsetParams(['type'], $params);
        $projects = parent::getList($params)->toArray();
        return $this->getCategoriesByProjects($projects);
    }

    private function unsetParams($keys = [], $params)
    {
        foreach ($keys as $key)
            if (isset($params[$key]))
                unset($params[$key]);

        return $params;
    }

    private function getCategoriesByProjects($projects)
    {
        $ids = [];
        foreach ($projects as $project)
            $ids[] = $project['category_id'];

        $categories = $this->getCategories($ids);

        foreach ($projects as &$project)
            $project['category_name'] = $categories[$project['category_id']];

        return $projects;
    }

    private function getCategories($ids)
    {
        $select = db()->select()
            ->from('hospitality_categories', ['id', 'name'])
            ->where('id IN (' . implode_for_where_in($ids) . ')');

        $categories = db()->fetchAll($select);
        return array_column($categories, 'name', 'id');
    }
}