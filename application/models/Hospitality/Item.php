<?php

namespace Hospitality;

use DbTable\ItemProjectModel;

class Item extends \GlobalDbClass
{
    protected $_name = 'hospitality_item';

    public function save($data, $params)
    {
        $data = $this->formatData($data);
        $primaryKey = parent::save($data, $params);

        $this->saveProjects($data['item'], $primaryKey);

        return $primaryKey;
    }

    public function getById($id)
    {
        $data = parent::getById($id);

        $data['publish_date'] = (new \DateTime($data['publish_date']))->format('d-m-Y');
        $data['period_from'] = (new \DateTime($data['period_from']))->format('d-m-Y');
        $data['period_till'] = (new \DateTime($data['period_till']))->format('d-m-Y');

        return $data;
    }

    private function formatData($data)
    {
        $data['item']['publish_date'] = (new \DateTime($data['item']['publish_date']))->format('Y-m-d');
        $data['item']['period_from'] = (new \DateTime($data['item']['period_from']))->format('Y-m-d');
        $data['item']['period_till'] = (new \DateTime($data['item']['period_till']))->format('Y-m-d');

        return $data;
    }

    private function saveProjects($data, $itemId)
    {
        $itemProjectModel = new ItemProjectModel();
        if (empty($data['projects'])) {
            $itemProjectModel->delete("item_id = '" . $itemId . "'");
        } else {
            $itemProjectModel->delete("item_id = '" . $itemId . "' AND project_id NOT IN(" . implode_for_where_in($data['projects']) . ")");
        }

        foreach ($data['projects'] as $projectId) {
            $where = ['item_id = ?' => $itemId, 'project_id = ?' => $projectId];
            $data = ['item_id' => $itemId, 'project_id' => $projectId];
            $row = $itemProjectModel->fetchRow($where);

            if (!$row) {
                $row2 = $itemProjectModel->createRow($data);
                $row2->save();
            }
        }
    }
}