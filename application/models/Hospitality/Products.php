<?php

namespace Hospitality;


class Products extends \GlobalDbClass
{
    protected $_name = 'hospitality_products';

    public $attributes = [
        'title' => [
            'title' => 'Titel',
            'width' => 'medium truncate',
        ],
        'company_id' => [
            'title' => 'Bedrijf',
            'width' => 'medium truncate',
        ],

        'short_description' => [
            'title' => 'Korte omschrijving',
            'width' => 'medium truncate',
        ],
        'rate' => [
            'title' => 'Tarief',
            'width' => 'xxsmall',
        ],
        'category' => [
            'title' => 'Categorie',
            'width' => 'xsmall',
        ],
        'active' => [
            'title' => 'Actief',
            'width' => 'xxxsmall',
        ]
    ];

    public function getList($params = [], $select = null)
    {
        $params = $this->unsetParams(['type'], $params);
        $products = parent::getList($params)->toArray();
        return $this->getCategoriesByProducts($products);
    }

    public function save($data, $params = [])
    {
        $data['general']['rate'] = $this->formatMoneyInputToDecimal($data['general']['rate']);
        return parent::save($data, $params);
    }

    private function unsetParams($keys = [], $params)
    {
        foreach ($keys as $key)
            if (isset($params[$key]))
                unset($params[$key]);

        return $params;
    }

    private function getCategoriesByProducts($products)
    {
        $ids = [];
        foreach ($products as $product)
            $ids[] = $product['category'];

        $categories = $this->getCategories($ids);

        foreach ($products as &$product)
            $product['category_name'] = $categories[$product['category']];

        return $products;
    }

    private function getCategories($ids)
    {
        $select = db()->select()
            ->from('hospitality_categories', ['id', 'name'])
            ->where('id IN (' . implode_for_where_in($ids) . ')');

        $categories = db()->fetchAll($select);
        return array_column($categories, 'name', 'id');
    }

    private function formatMoneyInputToDecimal($value)
    {
        $value = ((string)new \StringFormat($value, 'money_db')) / 100;
        $value = str_replace(',', '.', $value);
        return $value;
    }
}