<?php

namespace application\models\Surveys\Application\Request;

class SendSurveyInviteRequest
{
    private $id;
    private $userId;
    private $type;
    private $mapToEventId;
    private $eventType;
    private $dateCreated;

    public function __construct($userId, $type, $mapToEventId, $eventType, $id = null, $dateCreated = null)
    {
        $this->id = $id;
        $this->userId = $userId;
        $this->type = $type;
        $this->mapToEventId = $mapToEventId;
        $this->eventType = $eventType;
        $this->dateCreated = $dateCreated;
    }

    public function id()
    {
        return $this->id;
    }

    public function userId()
    {
        return $this->userId;
    }

    public function type()
    {
        return $this->type;
    }

    public function mapToEventId()
    {
        return $this->mapToEventId;
    }

    public function eventType()
    {
        return $this->eventType;
    }

    public function dateCreated()
    {
        return $this->dateCreated;
    }
}
