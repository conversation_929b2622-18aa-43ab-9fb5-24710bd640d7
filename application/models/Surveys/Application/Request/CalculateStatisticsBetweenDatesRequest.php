<?php

namespace application\models\Surveys\Application\Request;

class CalculateStatisticsBetweenDatesRequest
{
    private $from;
    private $till;
    private $event;
    private $technicalContact;

    public function __construct($from, $till, $event, $technicalContact)
    {
        $this->from = $from;
        $this->till = $till;
        $this->event = $event;
        $this->technicalContact = $technicalContact;
    }

    public function getFrom()
    {
        return $this->from;
    }

    public function getTill()
    {
        return $this->till;
    }

    public function getEvent()
    {
        return $this->event;
    }

    public function getTechnicalContact()
    {
        return $this->technicalContact;
    }
}
