<?php

namespace application\models\Surveys\Application\Request;

class WebHookEventRequest
{
    private $name;
    private $eventType;
    private $eventId;
    private $objectType;
    private $objectId;
    private $eventDateTime;

    public function __construct($name, $eventType, $eventId, $objectType, $objectId, $eventDateTime)
    {
        $this->name = $name;
        $this->eventType = $eventType;
        $this->eventId = $eventId;
        $this->objectType = $objectType;
        $this->objectId = $objectId;
        $this->eventDateTime = $eventDateTime;
    }

    public function name()
    {
        return $this->name;
    }

    public function eventType()
    {
        return $this->eventType;
    }

    public function eventId()
    {
        return $this->eventId;
    }

    public function objectType()
    {
        return $this->objectType;
    }

    public function objectId()
    {
        return $this->objectId;
    }

    public function eventDateTime()
    {
        return $this->eventDateTime;
    }
}
