<?php

namespace application\models\Surveys\Application\Request;

class CalculateGeneralScoresRequest
{
    private $from;
    private $till;
    private $userId;
    private $technicalContactId;

    public function getFrom()
    {
        return $this->from;
    }

    public function setFrom($from)
    {
        $this->from = $from;
    }

    public function getTill()
    {
        return $this->till;
    }

    public function setTill($till)
    {
        $this->till = $till;
    }

    public function getUserId()
    {
        return $this->userId;
    }

    public function setUserId($userId)
    {
        $this->userId = $userId;
    }

    public function getTechnicalContactId()
    {
        return $this->technicalContactId;
    }

    public function setTechnicalContactId($technicalContactId)
    {
        $this->technicalContactId = $technicalContactId;
    }
}
