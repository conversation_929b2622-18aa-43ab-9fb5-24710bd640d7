<?php

namespace application\models\Surveys\Application\Service;

use application\models\Surveys\Application\Request\CalculateStatisticsBetweenDatesRequest;

class CalculateStatisticsBetweenDatesService
{
    private $from;
    private $till;
    private $event;
    private $technicalContact;

    public function execute(CalculateStatisticsBetweenDatesRequest $request)
    {
        $this->from = $request->getFrom();
        $this->till = $request->getTill();
        $this->event = $request->getEvent();
        $this->technicalContact = $request->getTechnicalContact();

        $questions = $this->getQuestions();
        $questions = $this->getChoices($questions);
        $questions = $this->getRows($questions);
        $questions = $this->getAnswersStatistics($questions);

        return $questions;
    }

    private function getQuestions()
    {
        $select = db()->select()
            ->from(['st' => 'surveys_templates'], false)
            ->joinLeft(['sm' => 'survey_monkey'], 'sm.id = st.external_id', false)
            ->joinLeft(['sq' => 'surveys_questions'], 'sq.template_id = sm.external_id', ['sq.external_id', 'sq.heading', 'sq.family', 'sq.sub_type'])
            ->where('st.id = ?', $this->event);

        return db()->fetchAll($select);
    }

    private function getChoices($questions)
    {
        foreach ($questions as $key => $question) {
            $select = db()->select()
                ->from(['sc' => 'surveys_choices'], ['choice_id', 'text', 'position', 'description', 'weight'])
                ->where('sc.question_id = ?', $question['external_id'])
                ->where('sc.type = ?', 'choice');

            $questions[$key]['choices'] = db()->fetchAll($select);
        }

        return $questions;
    }

    private function getRows($questions)
    {
        foreach ($questions as $key => $question) {
            $select = db()->select()
                ->from(['sc' => 'surveys_choices'], ['row_id'])
                ->where('sc.question_id = ?', $question['external_id'])
                ->where('sc.type = ?', 'row');

            $questions[$key]['rows'] = db()->fetchAll($select);
        }

        return $questions;
    }

    private function getAnswersStatistics($questions)
    {
        foreach ($questions as $key => $question) {
            if (count($question['rows'])) {
                foreach ($question['rows'] as $row) {
                    $questions[$key]['answers']['rows'][$row['row_id']] = $this->getAnswerStatistic(
                        $question['external_id'],
                        $row['row_id']
                    );
                }
            } else {
                $questions[$key]['answers'] = $this->getAnswerStatistic($question['external_id']);
            }
        }

        return $questions;
    }

    private function getAnswerStatistic($questionId, $rowId = false)
    {
        $select = db()->select()
            ->from(['sa' => 'surveys_answers'], ['choice_id', 'COUNT(choice_id)'])
            ->joinLeft(['si' => 'surveys_invites'], 'si.id = sa.invite_id', false)
            ->where('sa.question_id = ?', $questionId)
            ->where('si.sent_date >= ?', $this->from)
            ->where('si.sent_date <= ?', $this->till)
            ->group('choice_id')
        ;


        if ($this->technicalContact) {
            $select->joinLeft(['sc' => 'support_complaints'], 'sc.id = si.map_to', false)
                ->where('sc.inform_contact', $this->technicalContact);
        }

        if ($rowId) {
            $select->where('sa.row_id = ?', $rowId);
        }

        return db()->fetchPairs($select);
    }
}
