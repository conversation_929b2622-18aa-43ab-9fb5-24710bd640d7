<?php

namespace application\models\Surveys\Application\Service\Setting;

use application\models\Surveys\Application\Contract\ISettingsRepository;
use application\models\Surveys\Application\Response\ViewSettingsResponse;
use application\models\Surveys\Infrastructure\Persistence\Repositories\SQLSettingsRepository;

class ViewSettingsService
{
    private $settingsRepository;

    public function __construct(ISettingsRepository $settingsRepository = null)
    {
        $this->settingsRepository = $settingsRepository ?: new SQLSettingsRepository();
    }

    public function execute()
    {
        $settingsData = $this->settingsRepository->findViewSettingsData();

        $availableTemplates = $this->getAvailableTemplates();

        $data = [];

        foreach ($settingsData as $settingData) {
            $from = $settingData['from'] !== null ? date('d-m-Y', strtotime($settingData['from'])) : null;

            $data[] = new ViewSettingsResponse(
                $settingData['event_id'],
                $settingData['event_name'],
                $from,
                $settingData['template_id'],
                $availableTemplates,
                $settingData['frequency'],
                $this->getActiveTemplate($settingData),
                $this->getLastRetrieveDate(),
                $settingData['send_threshold'],
                $settingData['event_type']
            );
        }

        return $data;
    }

    private function getAvailableTemplates()
    {
        return $this->settingsRepository->findAvailableTemplates();
    }

    private function getActiveTemplate($settingData)
    {
        return [
            'name' => $settingData['name'],
            'template_id' => $settingData['template_id'],
            'external_id' => $settingData['external_id']
        ];
    }

    public function getLastRetrieveDate()
    {
        $select = db()->select()
            ->from(['sm' => 'survey_monkey'], ['last_retrieve_date' =>'MAX(date_created)']);

        $row = db()->fetchRow($select);

        return $row['last_retrieve_date'];
    }
}
