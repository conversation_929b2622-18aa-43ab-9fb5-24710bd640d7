<?php

namespace application\models\Surveys\Application\Service\Sync;

use library\SurveyMonkey\services\CollectorsService;

class CreateNewSurveyCollectorService
{
    public function execute($surveyId)
    {
        $response = $this->createCollector($surveyId);
        $this->saveCollector($surveyId, $response);
    }

    private function createCollector($surveyId)
    {
        $redirectUrl = 'http://vaanster.nl/bedankt';

        $body = [
            'type' => 'weblink',
            'redirect_url' => $redirectUrl
        ];

        $service = new CollectorsService();
        return $service->createASurveyCollector($surveyId, $body);
    }

    private function saveCollector($surveyId, $response)
    {
        $surveyMonkey = new \SurveyMonkey();

        $data = [
            'collector_id' => $response['id'],
            'url' => $response['url'] . '?inviteId='
        ];

        $where = [
            'external_id = ?' => $surveyId
        ];

        $surveyMonkey->update($data, $where);
    }
}
