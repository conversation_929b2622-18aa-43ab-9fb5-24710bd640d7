<?php

namespace application\models\Surveys\Application\Service;

use application\models\Surveys\Application\Request\OverviewTechnicalContactsRequest;

class OverviewTechnicalContactsService
{
    /**
     * @var OverviewTechnicalContactsRequest
     */
    private $request;

    public function execute(OverviewTechnicalContactsRequest $request)
    {
        $this->request = $request;

        $select = db()->select()
            ->from(['si' => 'surveys_invites'], ['general_avg_score' => 'AVG(general_score)', 'project_id', 'sent_date', 'invite_count' => 'COUNT(general_score)'])
            ->joinLeft(['sc' => 'support_complaints'], 'sc.id = si.map_to', false)
            ->joinLeft(['c' => 'company'], 'c.id = sc.inform_contact', 'dummy_user')
            ->where('si.sent_date >= ?', $request->getFrom())
            ->where('si.sent_date <= ?', $request->getTill())
            ->where('si.type = ?', 'complaint')
            ->where('si.general_score IS NOT NULL')
            ->group('si.project_id')
            ->group('sc.inform_contact')
        ;

        $rows = db()->fetchAll($select);
        $rows = $this->correctAVGScore($rows);

        return $rows;
    }

    private function correctAVGScore($rows)
    {
        foreach ($rows as $key => $row) {
            $row['from'] = $this->request->getFrom();
            $row['till'] = $this->request->getTill();
            $row['general_avg_score'] = round($row['general_avg_score'] * 2) / 2; // round

            $rows[$key] = $row;
        }

        return $rows;
    }
}
