<?php

namespace application\models\HafDocument\Application\Service;

use HafDocument\Domain\Model\SupportDocument\SupportDocument;
use Pdf;

class ImageToPDFService implements CreatePdfService
{

	/**
	 * @param SupportDocument $supportDocument
	 * @return string
	 * @throws \Zend_Pdf_Exception
	 */
    public function execute(SupportDocument $supportDocument)
    {
		$pdf = new Pdf();

        $pdf = $this->addImageToPdf($pdf, $supportDocument->getFilePath());
		
		return $pdf->zendpdf->render();
    }

    private function addImageToPdf(Pdf $pdf, $imageFilePath)
    {
        $imagedimensions = getimagesize($imageFilePath);
        $imagedWidth = $imagedimensions[0];
        $imagedHeight = $imagedimensions[1];

        // pdf page portrait or landscape determined by image is portrait or landscape
        if ($imagedWidth >= $imagedHeight) {
            $pdf->newPage(\Zend_Pdf_Page::SIZE_A4_LANDSCAPE);
        } else {
            $pdf->newPage(\Zend_Pdf_Page::SIZE_A4);
        }

        $pdfPageWidth = $pdf->currentpage->getWidth();
        $pdfPageHeight = $pdf->currentpage->getHeight();

        // calculate maximum of image that fits in pdf A4 format
        if ($imagedWidth >= $imagedHeight) {
            $scaleDiff = $imagedWidth / $pdfPageWidth;
            $newImagedWidth = $imagedWidth / $scaleDiff;
            $newImagedHeight = $imagedHeight / $scaleDiff;
        } else {
            $scaleDiff = $imagedHeight / $pdfPageHeight;
            $newImagedWidth = $imagedWidth / $scaleDiff;
            $newImagedHeight = $imagedHeight / $scaleDiff;
        }

        $dimensions = $pdf->convertDimensions(0, 0, $newImagedHeight, $newImagedWidth);

        $pdf->currentpage->drawImage(
            \Zend_Pdf_Image::imageWithPath($imageFilePath),
            $dimensions['left'],
            $dimensions['bottom'],
            $dimensions['right'],
            $dimensions['top']
        );

        $pdf->savePage();

        return $pdf;
    }
}
