<?php

namespace HafDocument\Application\Service;

use Zend_Pdf;
use Zend_Pdf_Page;

class CreateHafDocPDF
{

	private $zendPdf;

	public function __construct()
	{
		$this->zendPdf = new Zend_Pdf();
	}

	/**
	 * @param $frontPagePDFStrings
	 * @param array $supportDocumentPDFStrings array with pdf strings
	 * @return null
	 */
	public function create($frontPagePDFStrings, array $supportDocumentPDFStrings)
	{
		$this->stringToPdf($frontPagePDFStrings);

		foreach ($supportDocumentPDFStrings as $string) {
			$this->stringToPdf($string);
		}

		return $this->zendPdf->pages;
	}


	private function stringToPdf($string)
	{
		$string = Zend_Pdf::parse($string);
		foreach ($string->pages as $page) {
			$templatePage = clone $page;
			$pdfMerge = new Zend_Pdf_Page($templatePage);
			$this->zendPdf->pages [] = $pdfMerge;
		}

		return null;
	}
}
