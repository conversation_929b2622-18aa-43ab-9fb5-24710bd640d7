<?php

namespace HafDocument\Application\Service;

use application\models\HafDocument\Application\Service\CreatePdfServiceFactory;
use application\models\HafDocument\Application\Service\PDFToImageToPDFService;

class CreateSupportDocumentsPDFS
{

	private $createPdfServiceFactory;

	public function __construct(
		CreatePdfServiceFactory $createPdfServiceFactory
	) {
		$this->createPdfServiceFactory = $createPdfServiceFactory;
	}


	public function buildAll(array $supportDocuments)
	{
		$supportDocumentPDFStrings =[];
		foreach ($supportDocuments as $supportDocument) {
			$createPdfService = $this->createPdfServiceFactory->build($supportDocument);

            try {
                $supportDocumentPDFStrings[] = $createPdfService->execute($supportDocument);
            } catch (\Exception $exception) {

                $createPdfService = new PDFToImageToPDFService();
                $supportDocumentPDFStrings = array_merge($supportDocumentPDFStrings, $createPdfService->execute($supportDocument));
            }
		}

		return $supportDocumentPDFStrings;
	}
}
