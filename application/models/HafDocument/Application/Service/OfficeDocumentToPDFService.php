<?php


namespace application\models\HafDocument\Application\Service;


use CURLFile;
use HafDocument\Domain\Model\SupportDocument\SupportDocument;
use Pdf;
use http\Exception\RuntimeException;

class OfficeDocumentToPDFService implements CreatePdfService
{

    /**
     * @param SupportDocument $supportDocument
     * @return PFD string
     */
    public function execute(SupportDocument $supportDocument)
    {
        $variables = [];
        $variables['file_contents'] =  $this->supportDocumentToCURLFileFFactory($supportDocument);

        $curl = $this->getSetupConectionToDocServer($variables);

        $documentTmpPath = $this->sendToDocServer($curl);

        $curlError = $this->getErrors($curl);

        if (strlen($curlError) != 0) {
            throw new RuntimeException("Document server error: ". $curlError);
        }

        return $this->getPDFStringFromFile($documentTmpPath);
    }

    private function getPDFStringFromFile($path)
    {
        return file_get_contents($path);
    }

    private function getSetupConectionToDocServer($variables)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, 'http://phpdocx.omniboxx.nl/transformToPdf.php');
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);

        curl_setopt($curl, CURLOPT_POSTFIELDS, $variables);

        return $curl;
    }

    private function sendToDocServer($curl)
    {
        // get a temp file for PHPDocX to render the file to
        $tmp = array_search('uri', @array_flip(stream_get_meta_data($GLOBALS[mt_rand()]=tmpfile())));
        //local
        //$tmp = tempnam('/tmp', 'docx_');
        file_put_contents($tmp, curl_exec($curl));

        return $tmp;
    }

    private function getErrors($curl)
    {
        $curlError = curl_error($curl);
        return $curlError;
    }

    private function supportDocumentToCURLFileFFactory(SupportDocument $supportDocument)
    {
        $supportDocumentRealpath = realpath($supportDocument->getFilePath());
        return new CURLFile($supportDocumentRealpath, $supportDocument->getFileMimeType());
    }
}
