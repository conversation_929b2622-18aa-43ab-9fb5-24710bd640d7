<?php

namespace application\models\HafDocument\Application\Service;

use HafDocument\Domain\Model\SupportDocument\SupportDocument;
use Zend_Pdf;
use Zend_Pdf_Page;

class PDFService implements CreatePdfService
{


	/**
	 * @param SupportDocument $supportDocument
	 * @return string
	 * @throws \Zend_Pdf_Exception
	 */
	public function execute(SupportDocument $supportDocument) {
		$pdfMerged = new Zend_Pdf();
		$src = new Zend_Pdf($supportDocument->getFilePath(), null, true);
		foreach ($src->pages as $page) {
			$templatePage = clone $page;
			$pdfMerge = new Zend_Pdf_Page($templatePage);
			$pdfMerged->pages [] = $pdfMerge;
		}

		return $pdfMerged->render();
	}
}
