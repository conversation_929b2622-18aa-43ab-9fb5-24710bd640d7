<?php

namespace application\models\HafDocument\Application\Service;

use HafDocument\Domain\Model\SupportDocument\SupportDocument;

class CreatePdfServiceFactory
{
    public function build(SupportDocument $supportDocument)
    {
        switch ($supportDocument->getFileMimeType()) {
            case 'application/pdf':
            	return new PDFService();
                break;
            case 'image/jpeg':
            case 'image/png':
            case 'image/gif':
                return new ImageToPDFService();
                break;
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            case 'application/msword':
                return new OfficeDocumentToPDFService();
                break;

            default:
            	throw new \RuntimeException('Unsupported document type');
            	break;
        }
    }
}
