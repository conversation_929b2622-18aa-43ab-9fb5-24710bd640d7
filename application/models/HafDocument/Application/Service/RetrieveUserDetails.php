<?php

namespace HafDocument\Application\Service;

use HafDocument\Domain\Model\FrontPage\UserDetails;
use loginManager;

class RetrieveUserDetails
{

	public function retrieve($userId) {

		//Zend query get user by id
		$userRow = $this->retrieveUserInfoOrFail($userId);
		$userEmail = $this->retrieveUserEmail($userId);
		$ipAddress = $this->retrieveIp();
		$companyLogo = $this->retrieveCompanyLogo();

		return new UserDetails(
			$userRow->rendered_name,
			$userEmail->address,
			$ipAddress,
			$companyLogo
		);
	}

	private function retrieveUserInfoOrFail($userId)
	{
		$userModel = new \Users();
		$usersRow = $userModel->getById($userId);

		if (null === $usersRow) {
			throw new \InvalidArgumentException(sprintf('No user found for id %s', $userId));
		}

		return $usersRow;
	}

	private function retrieveIp()
	{
		if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
			$ip = $_SERVER['HTTP_CLIENT_IP'];
		} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
			$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} else {
			$ip = $_SERVER['REMOTE_ADDR'];
		}
		return $ip;
	}

	private function retrieveCompanyLogo() {
		return loginManager::getLogo('portal');
	}

	private function retrieveUserEmail($userId) {
		$email =  (new \EmailUsers)->getById($userId);

		return $email;
	}

}
