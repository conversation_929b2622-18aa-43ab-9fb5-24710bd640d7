<?php

namespace HafDocument\Application\Service;

use HafDocument\Domain\Model\SupportDocument\SupportDocument;

class RetrieveSupportDocuments
{

	private function retrieve($supportDocumentId) {

		$documentData = $this->getSupportDocumentData($supportDocumentId);

		return new SupportDocument(
			$documentData->title,
			$documentData->date,
			$documentData->category,
			$documentData->user,
			$this->getFileMimeType($documentData),
			$documentData->filePath
		);
	}


	private function getSupportDocumentData($supportDocumentId)
	{
		$select = db()->select()
			->from(['sd' => 'support_documents'], ['id', 'date', 'title'])
			->joinLeft(['sdc' => 'support_documents_categories'], 'sdc.id = sd.category', ['sdc.category'])
			->joinLeft(
				['sdv' => 'support_documents_versions'],
				'sd.id = sdv.support_documents',
				['filePath' => 'document']
			)
			->joinLeft(['u' => 'users'], 'u.id = sd.user', ['user' => 'rendered_name'])
			->where('sd.id = ?', $supportDocumentId);

		return $select->query()->fetchObject();
	}

	private function getFileMimeType($documentData)
	{
		return mime_content_type($documentData->filePath);
	}

	public function retrieveAll($upportDocumentIds) {
		$supportDocuments =  [];
		foreach ($upportDocumentIds as $supportDocumentId) {
			$supportDocuments[] = $this->retrieve($supportDocumentId);
		}

		return $supportDocuments;
	}
}
