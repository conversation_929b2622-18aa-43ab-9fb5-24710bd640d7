<?php

namespace HafDocument\Application\Service;

use HafDocument\Domain\Model\FrontPage\FrontPageFactory;
use HafDocument\Domain\Service\CreateFrontPagePdf;

class CreateHafDocumentService
{
	private $retrieveFrontPageData;
	private $frontPageFactory;
	private $createFrontPagePdf;
	private $retrieveSupportDocuments;
	private $createHafDocPDF;
	private $createSupportDocumentsPDFS;

	public function __construct(
		RetrieveFrontPageData $retrieveFrontPageData,
		FrontPageFactory $frontPageFactory,
		CreateFrontPagePdf $createFrontPagePdf,
		RetrieveSupportDocuments $retrieveSupportDocuments,
		CreateHafDocPDF $createHafDocPDF,
		CreateSupportDocumentsPDFS $createSupportDocumentsPDFS
	) {
		$this->retrieveFrontPageData = $retrieveFrontPageData;
		$this->frontPageFactory = $frontPageFactory;
		$this->createFrontPagePdf = $createFrontPagePdf;
		$this->retrieveSupportDocuments = $retrieveSupportDocuments;
		$this->createHafDocPDF = $createHafDocPDF;
		$this->createSupportDocumentsPDFS = $createSupportDocumentsPDFS;
	}


	public function execute($userId, $supportDocumentIds)
	{

		$frontPageData = $this->retrieveFrontPageData->retrieve($userId, $supportDocumentIds);

		$frontPage = $this->frontPageFactory->build($frontPageData);

		$frontPagePDFString = $this->createFrontPagePdf->create($frontPage);

		$supportDocuments = $this->retrieveSupportDocuments->retrieveAll($supportDocumentIds);

		$supportDocumentsStrings= $this->createSupportDocumentsPDFS->buildAll($supportDocuments);
		
		$generatedPdf = $this->createHafDocPDF->create($frontPagePDFString, $supportDocumentsStrings);

		return $generatedPdf;
	}
}
