<?php

namespace HafDocument\Application\Service;

class RetrieveFrontPageData
{
	private $retrieveSupportDocuments;
	private $retrieveUserDetails;

	public function __construct(
		RetrieveSupportDocuments $retrieveSupportDocuments,
		RetrieveUserDetails $retrieveUserDetails
	) {
		$this->retrieveSupportDocuments = $retrieveSupportDocuments;
		$this->retrieveUserDetails = $retrieveUserDetails;
	}


	/**
	 * @param $userId
	 * @param $supportDocumentIds
	 * @return array (
	 *                'userDetails' => \HafDocument\Domain\Model\FrontPage\UserDetails,
	 *                'supportDocuments' => \HafDocument\Domain\Model\SupportDocument\SupportDocument
	 * )
	 */
	public function retrieve($userId, $supportDocumentIds)
	{
		$userDetails = $this->retrieveUserDetails->retrieve($userId);

		$supportDocuments = $this->retrieveSupportDocuments->retrieveAll($supportDocumentIds);

		return [
			'userDetails' => $userDetails,
			'supportDocuments' => $supportDocuments
		];
	}
}
