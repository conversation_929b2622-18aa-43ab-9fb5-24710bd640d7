<?php


namespace application\models\HafDocument\Application\Service;

use HafDocument\Domain\Model\SupportDocument\SupportDocument;
use Pdf;

class PDFToImageToPDFService implements CreatePdfService
{

    /**
     * @param SupportDocument $supportDocument
     * @return string
     * @throws \Zend_Pdf_Exception
     */
    public function execute(SupportDocument $supportDocument)
    {
        $tempFolder = $this->PDFToImage($supportDocument->getFilePath());

        $filePathList = $this->scanForFeles($tempFolder);

        $documentPDFStrings = $this->imageToPDF($supportDocument, $filePathList);

        $this->cleanUp($tempFolder);

        return $documentPDFStrings;
    }

    private function cleanUp($folderPath)
    {
        rmdir($folderPath);
    }

    private function PDFToImage($PDFFilePath)
    {
        $tempFolder = '_cache/temp-document-PDF-to-image-merge/' . md5($PDFFilePath) . time() . '/';
        $tempFilePath = $tempFolder . md5($PDFFilePath) . time() . '.jpg';

        createFolder($tempFilePath);

        // create Imagick object
        $imagick = new \Imagick();

        // set init canvas size
        $imagick->setResolution(200, 200); //Warning Increase the file size and performance!

        // Read image from PDF
        $imagick->readImage($PDFFilePath);

        $imagick->setImageCompressionQuality(100);
        $imagick->setImageAlphaChannel(12);

        $imagick->setBackgroundColor('#ffffff');

        // Writes an image
        $imagick->writeImages($tempFilePath, false);

        return $tempFolder;
    }

    private function scanForFeles($folderPath)
    {
        $filePathList = [];

        foreach (scandir($folderPath) as $file) {
            if (in_array($file, ['.', '..'])) {
                continue;
            }

            $filePath = $folderPath . $file;

            if (!is_file($filePath)) {
                continue;
            }

            $filePathList[] = $filePath;
        }

        return $filePathList;
    }

    private function imageToPDFBySupportDocument(SupportDocument $supportDocument)
    {
        $createPdfService = new ImageToPDFService();
        return $createPdfService->execute($supportDocument);
    }

    private function imageToPDF(SupportDocument $supportDocument, $filePathList = [])
    {
        $documentPDFStrings = [];
        foreach ($filePathList as $filePath) {

            $documentPDFStrings[] = $this->imageToPDFBySupportDocument(new SupportDocument(
                $supportDocument->gettitle(),
                $supportDocument->getCreateDate(),
                $supportDocument->getCategory(),
                $supportDocument->getUser(),
                $supportDocument->getFileMimeType(),
                $filePath
            ));
        }

        return $documentPDFStrings;
    }

}
