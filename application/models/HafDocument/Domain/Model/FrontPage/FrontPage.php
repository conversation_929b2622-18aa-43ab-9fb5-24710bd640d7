<?php

namespace HafDocument\Domain\Model\FrontPage;

class FrontPage
{
	private $userDetails;
	private $supportDocuments;
	private $createdOn;

	public function __construct(UserDetails $userDetails, array $supportDocuments)
	{
		$this->userDetails = $userDetails;
		$this->supportDocuments = $supportDocuments;
		$this->createdOn = (new \DateTime())->format('l j-F Y H:i:s');
	}

	/**
	 * @return \DateTime|string
	 */
	public function getCreatedOn()
	{
		return $this->createdOn;
	}

	/**
	 * @return mixed
	 */
	public function getSupportDocuments()
	{
		return $this->supportDocuments;
	}

	/**
	 * @return mixed
	 */
	public function getUserDetails()
	{
		return $this->userDetails;
	}
}
