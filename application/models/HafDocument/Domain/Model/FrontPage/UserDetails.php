<?php

namespace HafDocument\Domain\Model\FrontPage;

class UserDetails {

	private $renderedName;
	private $userEmail;
	private $ipAddress;
	private $companyLogo;

	public function __construct(
		$renderedName,
		$userEmail,
		$ipAddress,
		$companyLogo
	) {
		$this->setRenderedName($renderedName);
		$this->setUserEmail($userEmail);
		$this->setIpAddress($ipAddress);
		$this->setCompanyLogo($companyLogo);
	}

	/**
	 * @return string
	 */
	public function getRenderedName()
	{
		return $this->renderedName;
	}

	/**
	 * @param string $renderedName
	 * @throws \Exception
	 */
	private function setRenderedName($renderedName)
	{
		if (null) {
			throw new \RuntimeException('FOUTJU', '400');
		}

		$this->renderedName = $renderedName;
	}

	/**
	 * @return mixed
	 */
	public function getIpAddress()
	{
		return $this->ipAddress;
	}

	/**
	 * @param mixed $ipAddress
	 * @throws \Exception
	 */
	private function setIpAddress($ipAddress)
	{
		if (null) {
			throw new \RuntimeException('FOUTJU 2.0', '400');
		}

		$this->ipAddress = $ipAddress;
	}

	/**
	 * @return mixed
	 */
	public function getCompanyLogo() {
		return $this->companyLogo;
	}

	/**
	 * @param mixed $companyLogo
	 */
	public function setCompanyLogo($companyLogo) {
		$this->companyLogo = $companyLogo;
	}

	/**
	 * @return mixed
	 */
	public function getUserEmail() {
		return $this->userEmail;
	}

	/**
	 * @param mixed $userEmail
	 */
	public function setUserEmail($userEmail) {
		$this->userEmail = $userEmail;
	}
}
