<?php

namespace HafDocument\Domain\Model\SupportDocument;

class SupportDocument
{
	public $title;
	public $createDate;
	public $category;
	public $user;
	public $fileMimeType;
	private $filePath;

	public function __construct(
        $title,
        $createDate,
        $category,
        $documentUser,
        $fileMimeType,
        $filePath
	) {
		$this->title = $title;
		$this->createDate = $createDate;
		$this->category = $category;
		$this->user = $documentUser;
		$this->fileMimeType = $fileMimeType;
		$this->filePath = $filePath;
	}

	/**
	 * @return mixed
	 */
	public function gettitle()
	{
		return $this->title;
	}

	/**
	 * @return mixed
	 */
	public function getCreateDate()
	{
		return $this->createDate;
	}

	/**
	 * @return mixed
	 */
	public function getCategory()
	{
		return $this->category;
	}

	/**
	 * @return mixed
	 */
	public function getUser()
	{
		return $this->user;
	}

	/**
	 * @return mixed
	 */
	public function getFilePath()
	{
		return $this->filePath;
	}

	/**
	 * @return mixed
	 */
	public function getFileMimeType()
	{
		return $this->fileMimeType;
	}
}
