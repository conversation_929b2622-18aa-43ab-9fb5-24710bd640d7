<?php

namespace HafDocument\Domain\Service;

use Dompdf\Dompdf;
use HafDocument\Domain\Model\FrontPage\FrontPage;
use Zend_View;

class CreateFrontPagePdf
{

	public function create(FrontPage $frontPage)
	{

		$pdf = new Dompdf();

		$content = $this->createContentHtml($frontPage);

		$pdf->loadHtml($content);

		$pdf->setPaper('A4', 'landscape');

		$pdf->render();

		return $pdf->output();
	}

	private function createContentHtml(FrontPage $frontPage)
	{
		$view = new Zend_View();

		global $omniboxx_base_dir;
		$dir = $omniboxx_base_dir . '/application/views/scripts/haf-document/pdf';

		$view->setScriptPath($dir);

		$view->user = $frontPage->getUserDetails()->getRenderedName();
		$view->userEmail = $frontPage->getUserDetails()->getUserEmail();
		$view->createdOn = $frontPage->getCreatedOn();
		$view->ip = $frontPage->getUserDetails()->getIpAddress();
		$view->document = $frontPage->getSupportDocuments();
		$view->companyLogo = $frontPage->getUserDetails()->getCompanyLogo();

		return $view->render('haf-document-front-page.phtml');
	}
}
