<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 12:15
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromUserRequest
{
    private $userId;
    private $emailAddress;

    /**
     * UnlinkEmailAddressFromUserRequest constructor.
     * @param $userId
     * @param $emailAddress
     */
    public function __construct($userId, $emailAddress)
    {
        $this->userId = $userId;
        $this->emailAddress = $emailAddress;
    }

    /**
     * @return mixed
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @return mixed
     */
    public function getEmailAddress()
    {
        return $this->emailAddress;
    }
}