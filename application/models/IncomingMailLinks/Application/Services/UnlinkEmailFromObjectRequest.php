<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 12:15
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromObjectRequest
{
    private $emailId;
    private $objectId;

    /**
     * UnlinkEmailFromObjectRequest constructor.
     * @param $emailId
     * @param $objectId
     */
    public function __construct($emailId, $objectId)
    {
        $this->emailId = $emailId;
        $this->objectId = $objectId;
    }

    /**
     * @return mixed
     */
    public function getEmailId()
    {
        return $this->emailId;
    }

    /**
     * @return mixed
     */
    public function getObjectId()
    {
        return $this->objectId;
    }
}