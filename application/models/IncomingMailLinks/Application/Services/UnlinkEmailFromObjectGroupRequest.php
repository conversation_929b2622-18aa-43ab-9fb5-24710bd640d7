<?php

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromObjectGroupRequest
{
    private $emailId;
    private $objectGroupId;

    public function __construct($emailId, $objectGroupId)
    {
        $this->emailId = $emailId;
        $this->objectGroupId = $objectGroupId;
    }

    public function getEmailId()
    {
        return $this->emailId;
    }

    public function getObjectGroupId()
    {
        return $this->objectGroupId;
    }
}