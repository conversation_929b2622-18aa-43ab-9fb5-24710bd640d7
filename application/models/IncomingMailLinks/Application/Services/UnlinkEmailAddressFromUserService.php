<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 10:37
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromUserService
{
    public function execute(UnlinkEmailAddressFromUserRequest $request)
    {
        $this->updateEmailUsersTable($request->getEmailAddress(), $request->getUserId());
        $this->deleteFromEmailAddressTable($request->getEmailAddress(), $request->getUserId());
    }

    private function updateEmailUsersTable($emailAddress, $userId)
    {
        $emailUsers = new \EmailUsers();

        $data = ['matched_user_id' => null];
        $where = [
            'address = ?' => $emailAddress,
            'matched_user_id = ?' => $userId
        ];

        $emailUsers->update($data, $where);
    }

    private function deleteFromEmailAddressTable($email, $userId)
    {
        $emailAddress = new \EmailAddress();

        $ids = $this->getAddressTypeIdsForUser();

        $where = [
            'address = ?' => $email,
            'map_to = ?' => $userId,
            'type IN(' . implode_for_where_in($ids) . ') OR type IS NULL'
        ];

        $emailAddress->delete($where);
    }

    private function getAddressTypeIdsForUser()
    {
        $emailAddressTypes = new \EmailAddressTypes();

        $where = [
            'map_to = ?' => 'user'
        ];

        $rows = $emailAddressTypes->fetchAll($where);

        $ids = [];

        foreach ($rows as $row) {
            $ids[] = $row['id'];
        }

        return $ids;
    }
}