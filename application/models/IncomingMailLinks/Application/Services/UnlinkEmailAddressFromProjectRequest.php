<?php

namespace IncomingMailLinks\Application\Services;

class UnlinkEmailAddressFromProjectRequest
{
    private $projectId;
    private $emailAddress;

    public function __construct($projectId, $emailAddress)
    {
        $this->projectId = $projectId;
        $this->emailAddress = $emailAddress;
    }

    public function getProjectId()
    {
        return $this->projectId;
    }

    public function getEmailAddress()
    {
        return $this->emailAddress;
    }
}
