<?php

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromObjectGroupService
{
    public function execute(UnlinkEmailAddressFromObjectGroupRequest $request)
    {
        $this->updateEmailUsersTable($request->getEmailAddress(), $request->getObjectGroupId());
        $this->deleteEmailAddressFromTable($request->getEmailAddress(), $request->getObjectGroupId());
    }

    private function updateEmailUsersTable($emailAddress, $objectGroupId)
    {
        $emailUsers = new \EmailUsers();

        $data = ['matched_objectgroup_id' => null];
        $where = [
            'address = ?' => $emailAddress,
            'matched_objectgroup_id = ?' => $objectGroupId
        ];

        $emailUsers->update($data, $where);
    }

    private function deleteEmailAddressFromTable($email, $objectGroupId)
    {
        $emailAddress = new \EmailAddress();

        $ids = $this->getAddressTypeIdsForObjectGroup();

        $where = [
            'address = ?' => $email,
            'map_to = ?' => $objectGroupId,
            'type IN(' . implode_for_where_in($ids) .') OR type IS NULL'
        ];

        $emailAddress->delete($where);
    }

    private function getAddressTypeIdsForObjectGroup()
    {
        $emailAddressTypes = new \EmailAddressTypes();

        $where = [
            'map_to = ?' => 'objectgroup'
        ];

        $rows = $emailAddressTypes->fetchAll($where);

        $ids = [];

        foreach ($rows as $row) {
            $ids[] = $row['id'];
        }

        return $ids;
    }
}