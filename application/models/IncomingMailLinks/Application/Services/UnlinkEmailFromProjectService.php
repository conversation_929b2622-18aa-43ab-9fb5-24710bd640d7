<?php

namespace IncomingMailLinks\Application\Services;

class UnlinkEmailFromProjectService
{
    public function execute(UnlinkEmailFromProjectRequest $request)
    {
        $data = ['matched_once_project_id' => null];
        $where = [
            'email = ?' => $request->getEmailId(),
            'matched_once_project_id' => $request->getProject(),
            'type = ?' => 'from'
        ];

        $emailUsers = new \EmailUsers();
        $emailUsers->update($data, $where);
    }
}
