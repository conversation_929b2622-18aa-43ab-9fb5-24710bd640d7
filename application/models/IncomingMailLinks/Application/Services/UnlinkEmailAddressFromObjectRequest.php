<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 12:14
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromObjectRequest
{
    private $objectId;
    private $emailAddress;

    /**
     * UnlinkEmailAddressFromObjectRequest constructor.
     * @param $objectId
     * @param $emailAddress
     */
    public function __construct($objectId, $emailAddress)
    {
        $this->objectId = $objectId;
        $this->emailAddress = $emailAddress;
    }

    /**
     * @return mixed
     */
    public function getObjectId()
    {
        return $this->objectId;
    }

    /**
     * @return mixed
     */
    public function getEmailAddress()
    {
        return $this->emailAddress;
    }
}