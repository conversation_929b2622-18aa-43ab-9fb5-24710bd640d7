<?php

namespace application\models\IncomingMailLinks\Application\Services;

class ViewLinkedEmailsService
{
    public function execute()
    {
        $userData = $this->getUserData();
		$investorData = $this->getInvestorData();
        $objectData = $this->getObjectData();
        $objectGroupData = $this->getObjectGroupData();
        $projectData = $this->getProjectData();

        return array_merge($userData, $investorData, $objectData, $objectGroupData, $projectData);
    }

    private function getUserData()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['email_id' => 'eu.email', 'user_id' => 'eu.matched_once_user_id', 'email_from' => 'eu.name', 'type' => "('Huurder')"])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
            ->joinLeft(['u' => 'users'], 'u.id = eu.matched_once_user_id', ['user_name' => 'u.rendered_name'])
			->where('eu.matched_once_user_id IS NOT NULL AND eu.matched_once_user_id > 0')
			->where('u.type != ?', 'investor');

        return db()->fetchAll($select);
    }

	private function getInvestorData()
	{
		$select = db()->select()
			->from(['eu' => 'email_users'], ['email_id' => 'eu.email', 'user_id' => 'eu.matched_once_user_id', 'email_from' => 'eu.name', 'type' => "('Belegger')"])
			->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
			->joinLeft(['u' => 'users'], 'u.id = eu.matched_once_user_id', ['user_name' => 'u.rendered_name'])
			->joinLeft(['i' => 'investor'], 'u.type = "investor" AND i.user = u.id', ['investor_id' => 'i.id', 'investor_name' => 'i.name',])
			->where('eu.matched_once_user_id IS NOT NULL AND eu.matched_once_user_id > 0')
			->where('u.type = ?', 'investor');

		$rows = db()->fetchAll($select);

		return $rows;
	}

    private function getObjectData()
    {
        $select = db()->select()
            ->from(['e' => 'email'], ['email_id' => 'e.id', 'email_subject' => 'e.subject', 'object_id' => 'e.object_once', 'type' => "('Object')"])
            ->joinLeft(['eu' => 'email_users'], 'eu.email = e.id', ['email_from' => 'eu.name'])
            ->joinLeft(['o' => 'objects'], 'o.id = e.object_once', ['object_name' => 'o.rendered_address'])
            ->where('eu.type = ?', 'from')
            ->where('e.object_once IS NOT NULL AND e.object_once > 0');

        return db()->fetchAll($select);
    }

    private function getObjectGroupData()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['email_id' => 'eu.email', 'objectgroup_id' => 'eu.matched_once_objectgroup_id', 'email_from' => 'eu.name', 'type' => "('Objectgroep')"])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
            ->joinLeft(['og' => 'objectgroup'], 'og.id = eu.matched_once_objectgroup_id', false)
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['objectgroup_description' => 'CONCAT_WS(" - ", p.name, og.description, og.type)'])
            ->where('eu.matched_once_objectgroup_id IS NOT NULL AND eu.matched_once_objectgroup_id > 0');

        return db()->fetchAll($select);
    }

    private function getProjectData()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['email_id' => 'eu.email', 'project_id' => 'eu.matched_once_project_id', 'email_from' => 'eu.name', 'type' => "('Project')"])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
            ->joinLeft(['p' => 'projects'], 'p.id = eu.matched_once_project_id', ['project_description' => 'CONCAT_WS(" - ", p.name, p.city)'])
            ->where('eu.matched_once_project_id IS NOT NULL AND eu.matched_once_project_id > 0');

        return db()->fetchAll($select);
    }
}