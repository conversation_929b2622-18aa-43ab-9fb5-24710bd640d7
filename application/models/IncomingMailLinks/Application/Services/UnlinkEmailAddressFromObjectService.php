<?php
/**
 * Created by Php<PERSON>torm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 10:38
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromObjectService
{
    public function execute(UnlinkEmailAddressFromObjectRequest $request)
    {
        $this->updateEmailTable($request->getEmailAddress(), $request->getObjectId());
        $this->deleteEmailAddressFromTable($request->getEmailAddress(), $request->getObjectId());
    }

    private function updateEmailTable($emailAddress, $objectId)
    {
        $emails = new \Emails();
        $rows = $this->getEmailIdByEmailAddressObjectIdCombination($emailAddress, $objectId);

        foreach ($rows as $row) {
            $data = ['object' => null];
            $where = ['id = ?' => $row['email_id']];
            $emails->update($data, $where);
        }
    }

    private function getEmailIdByEmailAddressObjectIdCombination($emailAddress, $objectId)
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['email_id' => 'eu.email'])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', ['e.object'])
            ->where('eu.address = ?', $emailAddress)
            ->where('eu.type = ?', 'from')
            ->where('e.object = ?', $objectId)
            ->order('e.object desc');

        return db()->fetchAll($select);
    }

    private function deleteEmailAddressFromTable($email, $objectId)
    {
        $emailAddress = new \EmailAddress();

        $ids = $this->getAddressTypeIdsForObject();

        $where = [
            'address = ?' => $email,
            'map_to = ?' => $objectId,
            'type IN(' . implode_for_where_in($ids) .') OR type IS NULL'
        ];

        $emailAddress->delete($where);
    }

    private function getAddressTypeIdsForObject()
    {
        $emailAddressTypes = new \EmailAddressTypes();

        $where = [
            'map_to = ?' => 'object'
        ];

        $rows = $emailAddressTypes->fetchAll($where);

        $ids = [];

        foreach ($rows as $row) {
            $ids[] = $row['id'];
        }

        return $ids;
    }
}