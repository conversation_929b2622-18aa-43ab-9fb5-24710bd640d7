<?php

namespace application\models\IncomingMailLinks\Application\Services;

class ViewLinkedEmailAddressesService
{
    public function execute()
    {
        $userData = $this->getUserData();
		$investorData = $this->getInvestorData();
        $objectData = $this->getObjectData();
        $objectGroupData = $this->getObjectGroupData();
        $projectData = $this->getProjectData();

        return array_merge($userData, $investorData, $objectData, $objectGroupData, $projectData);
    }

    private function getUserData()
    {
        $select = db()->select()
            ->from(['eu' => 'email_users'], ['email_address' => 'eu.address', 'email_address_type' => 'eu.address_type', 'type' => "('Huurder')"])
            ->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
            ->joinLeft(['u' => 'users'], 'u.id = eu.matched_user_id', ['user_id' => 'u.id', 'user_type' => 'u.type', 'user_name' => 'u.rendered_name'])
            ->where('eu.type = ?', 'from')
            ->where('eu.user_id IS NULL')
            ->where('eu.matched_user_id IS NOT NULL')
			->where('u.type != ?', 'investor')
            ->group('eu.address')
            ->group('u.id');

        return db()->fetchAll($select);
    }

	private function getInvestorData()
	{
		$select = db()->select()
			->from(['eu' => 'email_users'], ['email_address' => 'eu.address', 'email_address_type' => 'eu.address_type', 'type' => "('Belegger')"])
			->joinLeft(['e' => 'email'], 'e.id = eu.email', ['email_id' => 'e.id', 'email_subject' => 'e.subject'])
			->joinLeft(['u' => 'users'], 'u.id = eu.matched_user_id', ['user_id' => 'u.id', 'user_type' => 'u.type', 'user_name' => 'u.rendered_name'])
			->joinLeft(['i' => 'investor'], 'u.type = "investor" AND i.user = u.id', ['investor_id' => 'i.id', 'investor_name' => 'i.name',])
			->where('eu.type = ?', 'from')
			->where('eu.user_id IS NULL')
			->where('eu.matched_user_id IS NOT NULL')
			->where('u.type = ?', 'investor')
			->group('eu.address')
			->group('u.id');

		$rows = db()->fetchAll($select);

		return $rows;
	}

    private function getObjectData()
    {
        $select = db()->select()
            ->from(['e' => 'email'], ['object_id' => 'e.object', 'email_id' => 'e.id', 'email_subject' => 'e.subject', 'type' => "('Object')"])
            ->joinLeft(['eu' => 'email_users'], 'eu.email = e.id', ['email_address' => 'eu.address'])
            ->joinLeft(['o' => 'objects'], 'o.id = e.object', ['object_name' => 'o.rendered_address'])
            ->where('eu.type = ?', 'from')
            ->where('e.object IS NOT NULL')
            ->where('o.id IS NOT NULL')
            ->group('eu.address')
            ->group('e.object');

        return db()->fetchAll($select);
    }

    private function getObjectGroupData()
    {
        $eu = [
            'email_address' => 'eu.address',
            'objectgroup_id' => 'eu.matched_objectgroup_id',
            'type' => "('Objectgroep')"
        ];

        $select = db()->select()
            ->from(['eu' => 'email_users'], $eu)
            ->joinLeft(['og' => 'objectgroup'], 'og.id = eu.matched_objectgroup_id ', false)
            ->joinLeft(['p' => 'projects'], 'p.id = og.project', ['objectgroup_description' => 'CONCAT_WS(" - ", p.name, og.description, og.type)'])
            ->where('eu.matched_objectgroup_id IS NOT NULL')
            ->group('eu.address')
            ->group('og.id');

        $rows = db()->fetchAll($select);

        $select = db()->select()
            ->from(['ea' => 'email_address'], [
                'email_address' => 'ea.address',
                'objectgroup_id' => 'ea.map_to',
                'type' => "('Objectgroep')"
            ])
            ->joinLeft(['eat' => 'email_address_types'], 'eat.id = ea.type', false)
            ->where('eat.map_to = ?', 'objectgroup');

        $rows2 = db()->fetchAll($select);

        return array_merge($rows, $rows2);
    }

    private function getProjectData()
    {
        $eu = [
            'email_address' => 'eu.address',
            'project_id' => 'eu.matched_project_id',
            'type' => "('Project')"
        ];

        $select = db()->select()
            ->from(['eu' => 'email_users'], $eu)
            ->joinLeft(['p' => 'projects'], 'p.id = eu.matched_project_id', ['project_description' => 'CONCAT_WS(" - ", p.name, p.city)'])
            ->where('eu.matched_project_id IS NOT NULL')
            ->group('eu.address')
            ->group('p.id');

        $rows = db()->fetchAll($select);

        $select = db()->select()
            ->from(['ea' => 'email_address'], [
                'email_address' => 'ea.address',
                'project_id' => 'ea.map_to',
                'type' => "('Project')"
            ])
            ->joinLeft(['eat' => 'email_address_types'], 'eat.id = ea.type', false)
            ->where('eat.map_to = ?', 'project');

        $rows2 = db()->fetchAll($select);

        return array_merge($rows, $rows2);
    }
}