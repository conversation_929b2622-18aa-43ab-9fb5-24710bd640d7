<?php

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromObjectGroupService
{
    public function execute(UnlinkEmailFromObjectGroupRequest $request)
    {
        $data = ['matched_once_objectgroup_id' => null];
        $where = [
            'email = ?' => $request->getEmailId(),
            'matched_once_objectgroup_id' => $request->getObjectGroupId(),
            'type = ?' => 'from'
        ];

        $emailUsers = new \EmailUsers();
        $emailUsers->update($data, $where);
    }
}