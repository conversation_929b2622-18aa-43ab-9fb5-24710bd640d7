<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 10:40
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromObjectService
{
    public function execute(UnlinkEmailFromObjectRequest $request)
    {
        $data = ['object_once' => null];
        $where = [
            'id = ?' => $request->getEmailId(),
            'object_once = ?' => $request->getObjectId()
        ];

        $email = new \Emails();
        $email->update($data, $where);
    }
}