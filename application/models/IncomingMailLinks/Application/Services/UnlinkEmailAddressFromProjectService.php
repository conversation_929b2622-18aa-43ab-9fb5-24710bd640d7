<?php

namespace IncomingMailLinks\Application\Services;

class UnlinkEmailAddressFromProjectService
{
    public function execute(UnlinkEmailAddressFromProjectRequest $request)
    {
        $this->updateEmailUsersTable($request->getEmailAddress(), $request->getProjectId());
        $this->deleteEmailAddressFromTable($request->getEmailAddress(), $request->getProjectId());
    }

    private function updateEmailUsersTable($emailAddress, $projectId)
    {
        $emailUsers = new \EmailUsers();

        $data = [
            'matched_project_id' => null,
            'address_type' => null
        ];

        $where = [
            'address = ?' => $emailAddress,
            'matched_project_id = ?' => $projectId
        ];

        $emailUsers->update($data, $where);
    }

    private function deleteEmailAddressFromTable($email, $projectId)
    {
        $emailAddress = new \EmailAddress();

        $ids = $this->getAddressTypeIdsForProject();

        $where = [
            'address = ?' => $email,
            'map_to = ?' => $projectId,
            'type IN(' . implode_for_where_in($ids) .') OR type IS NULL'
        ];

        $emailAddress->delete($where);
    }

    private function getAddressTypeIdsForProject()
    {
        $emailAddressTypes = new \EmailAddressTypes();

        $where = [
            'map_to = ?' => 'project'
        ];

        $rows = $emailAddressTypes->fetchAll($where);

        $ids = [];

        foreach ($rows as $row) {
            $ids[] = $row['id'];
        }

        return $ids;
    }
}
