<?php

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailAddressFromObjectGroupRequest
{
    private $objectGroupId;
    private $emailAddress;

    public function __construct($objectGroupId, $emailAddress)
    {
        $this->objectGroupId = $objectGroupId;
        $this->emailAddress = $emailAddress;
    }

    public function getObjectGroupId()
    {
        return $this->objectGroupId;
    }

    public function getEmailAddress()
    {
        return $this->emailAddress;
    }
}