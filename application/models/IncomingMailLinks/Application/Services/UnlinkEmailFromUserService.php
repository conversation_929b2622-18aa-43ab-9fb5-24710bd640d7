<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 10:40
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromUserService
{
    public function execute(UnlinkEmailFromUserRequest $request)
    {
        $data = ['matched_once_user_id' => null];
        $where = [
            'email = ?' => $request->getEmailId(),
            'matched_once_user_id' => $request->getUserId(),
            'type = ?' => 'from'
        ];

        $emailUsers = new \EmailUsers();
        $emailUsers->update($data, $where);
    }
}