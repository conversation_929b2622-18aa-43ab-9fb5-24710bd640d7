<?php
/**
 * Created by PhpStorm.
 * User: mennohorde
 * Date: 26/09/16
 * Time: 12:15
 */

namespace application\models\IncomingMailLinks\Application\Services;


class UnlinkEmailFromUserRequest
{
    private $emailId;
    private $userId;

    /**
     * UnlinkEmailFromUserRequest constructor.
     * @param $emailId
     * @param $userId
     */
    public function __construct($emailId, $userId)
    {
        $this->emailId = $emailId;
        $this->userId = $userId;
    }

    /**
     * @return mixed
     */
    public function getEmailId()
    {
        return $this->emailId;
    }

    /**
     * @return mixed
     */
    public function getUserId()
    {
        return $this->userId;
    }
}