<?php


namespace Financial\Infrastructure;


use Financial\Domain\Contact\FinancialContact;
use Financial\Infrastructure\Models\FinancialContactErrors;
use Financial\Infrastructure\Models\FinancialContacts;
use Financial\Infrastructure\Models\FinancialSystems;

class FinancialContactErrorsRepository
{
    /**
     * @var FinancialContactErrors
     */
    private $financialContactErrors;

    public function __construct(FinancialContactErrors $financialContactErrors)
    {

        $this->financialContactErrors = $financialContactErrors;
    }

    public function getAllDataOrderByDate()
    {
        $select = db()->select()
            ->from(['fce' => $this->financialContactErrors->getTableName()],
                [
                    'error_id' => 'id',
                    'error_message' => 'message',
                    'error_created_at' => 'created_at'
                ]
            )
            ->joinLeft(
                ['fc' => (new FinancialContacts())->getTableName()],
                'fce.financial_contact_id = fc.id',
                [
                    'financial_contact_identifier' => 'financial_contact_identifier',
                    'administration' => 'administration',
                    'financial_contact_created_at' => 'created_at'
                ]
            )
            ->joinLeft(
                ['u' => (new \Users())->getTableName()],
                'fc.user_id = u.id',
                [
                    'user_rendered_name' => 'rendered_name'
                ]
            )
            ->joinLeft(
                ['fs' => (new FinancialSystems())->getTableName()],
                'fc.financial_system_id = fs.id',
                [
                    'financial_system_name' => 'name'
                ]
            )
        ;

        $rows = db()->fetchAll($select);
        return $rows;
    }

    public function create($financialContactId, $message)
    {
        $financialContactRow = $this->financialContactErrors->createRow([
            'financial_contact_id' => $financialContactId,
            'message' => $message,
            'created_at' => (new \DateTime())->format('Y-m-d H:i:s')
        ]);

        $financialContactRow->save();
    }
}

