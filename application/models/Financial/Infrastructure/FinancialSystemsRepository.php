<?php


namespace Financial\Infrastructure;


use Financial\Domain\FinancialSystem;
use Financial\Infrastructure\Models\FinancialSystems;

class FinancialSystemsRepository
{
    /**
     * @var FinancialSystems
     */
    private $financialSystems;

    public function __construct(FinancialSystems $financialSystems)
    {

        $this->financialSystems = $financialSystems;
    }


    public function getByFinancialSystemReference($financialSystemReference)
    {

        $select = db()->select()
            ->from(['fs' => $this->financialSystems->getTableName()], ['*'])
            ->where('fs.reference = ?', $financialSystemReference);

        $row = db()->fetchRow($select);

        if (!$row) {
            throw new \Exception(
                'Given financial system reference does not exist:' . $financialSystemReference
            );
        }

        return $this->buildDomainObject($row);

    }


    private function buildDomainObject(array $row)
    {
        return new FinancialSystem(
            $row['id'],
            $row['name'],
            $row['reference']
        );
    }
}
