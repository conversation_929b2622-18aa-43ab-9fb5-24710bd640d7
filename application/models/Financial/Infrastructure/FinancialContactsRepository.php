<?php


namespace Financial\Infrastructure;


use Financial\Domain\Contact\FinancialContact;
use Financial\Infrastructure\Models\FinancialContacts;

class FinancialContactsRepository
{
    /**
     * @var FinancialContacts
     */
    private $financialContacts;

    public function __construct(FinancialContacts $financialContacts)
    {

        $this->financialContacts = $financialContacts;
    }

    public function retrieveByFinancialSystemAndAdministrationAndUserIdOrFail($financialSystemId, $administration, $userId)
    {
        $select = db()->select()
            ->from(['fc' => $this->financialContacts->getTableName()])
            ->where('fc.financial_system_id = ?', $financialSystemId)
            ->where('fc.administration = ?', $administration)
            ->where('fc.user_id = ?', $userId);

        $row = db()->fetchRow($select);

        if (!$row) {
            throw new \RuntimeException('Financial contacts not found by:' . json_encode([
                    'financial_system_id' => $financialSystemId,
                    'administration' =>$administration,
                    'user_id' =>$userId
                ])
            );
        }

        return $this->buildDomainObject($row);
    }

    public function create($financialSystemId, $userId, $administration, $financialContactIdentifier = null)
    {
        $financialContactRow = $this->financialContacts->createRow([
            'user_id' => $userId,
            'financial_contact_identifier' => $financialContactIdentifier,
            'administration' => $administration,
            'financial_system_id' => $financialSystemId,
            'created_at' => (new \DateTime())->format('Y-m-d H:i:s')
        ]);

        $financialContactRow->save();
    }

    public function persist(\Zend_Db_Table_Row_Abstract $row)
    {
        return $row->save();
    }

    private function buildDomainObject(array $row)
    {
        return new FinancialContact(
            $row['id'],
            $row['user_id'],
            $row['financial_contact_identifier'],
            $row['administration'],
            $row['financial_system_id'],
            new \DateTime($row['created_at']),
            new \DateTime($row['updated_at']),
            new \DateTime($row['sync_at'])
        );
    }

    public function addFinancialContactIdentifier($id, $financialContactIdentifier)
    {
        $row = $this->financialContacts->getById($id);
        $row->financial_contact_identifier = $financialContactIdentifier;
        $row->updated_at = (new \DateTime())->format('Y-m-d H:i:s');
        $row->save();
    }

    public function updateSyncDate($id)
    {
        $row = $this->financialContacts->getById($id);
        $row->sync_at = (new \DateTime())->format('Y-m-d H:i:s');
        $row->save();
    }

    public function updateFinancialContactIdentifier($id, $financialContactIdentifier)
    {
        $row = $this->financialContacts->getById($id);
        if (!is_null($row->financial_contact_identifier) &&
            $row->financial_contact_identifier != $financialContactIdentifier
        ) {
            throw new \Exception(sprintf(
                'FinancialContact: %s have a identifier: %s. Try to set a others one: %s',
                $id,
                $row->financial_contact_identifier,
                $financialContactIdentifier
            ));
        }

        $row->financial_contact_identifier = $financialContactIdentifier;
        $row->updated_at = (new \DateTime())->format('Y-m-d H:i:s');
        $row->save();
    }
}
