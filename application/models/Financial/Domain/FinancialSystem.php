<?php


namespace Financial\Domain;


class FinancialSystem
{
    private $id;
    private $name;
    private $reference;

    public function __construct($id, $name, $reference)
    {
        $this->id = $id;
        $this->name = $name;
        $this->reference = $reference;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @return mixed
     */
    public function getReference()
    {
        return $this->reference;
    }
}
