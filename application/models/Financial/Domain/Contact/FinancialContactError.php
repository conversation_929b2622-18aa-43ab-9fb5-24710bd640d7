<?php


namespace Financial\Domain\Contact;


class FinancialContactError
{
    private $id;
    private $financialContactId;
    private $message;
    private $createdAt;

    public function __construct(
        $id,
        $financialContactId,
        $message,
        \DateTime $createdAt
    )
    {
        $this->id = $id;
        $this->financialContactId = $financialContactId;
        $this->message = $message;
        $this->createdAt = $createdAt;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getFinancialContactId()
    {
        return $this->financialContactId;
    }

    /**
     * @return mixed
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }
}
