<?php


namespace Financial\Domain\Contact;


class FinancialContact
{
    private $id;
    private $userId;
    private $financialContactIdentifier;
    private $administration;
    private $financialSystemId;
    /**
     * @var \DateTime
     */
    private $createdAt;
    /**
     * @var \DateTime
     */
    private $updatedAt;
    /**
     * @var \DateTime
     */
    private $syncAt;

    public function __construct(
        $id,
        $userId,
        $financialContactIdentifier,
        $administration,
        $financialSystemId,
        \DateTime $createdAt,
        \DateTime $updatedAt,
        \DateTime $syncAt
    ) {

        $this->id = $id;
        $this->userId = $userId;
        $this->financialContactIdentifier = $financialContactIdentifier;
        $this->administration = $administration;
        $this->financialSystemId = $financialSystemId;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
        $this->syncAt = $syncAt;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @return mixed
     */
    public function getFinancialContactIdentifier()
    {
        return $this->financialContactIdentifier;
    }

    /**
     * @return mixed
     */
    public function getAdministration()
    {
        return $this->administration;
    }

    /**
     * @return mixed
     */
    public function getFinancialSystemId()
    {
        return $this->financialSystemId;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @return \DateTime
     */
    public function getSyncAt()
    {
        return $this->syncAt;
    }
}
