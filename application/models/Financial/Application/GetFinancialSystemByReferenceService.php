<?php


namespace Financial\Application;


use Financial\Infrastructure\FinancialSystemsRepository;
use Financial\Infrastructure\Models\FinancialSystems;

class GetFinancialSystemByReferenceService
{
    /**
     * @var FinancialSystemsRepository
     */
    private $financialSystemsRepository;

    public function __construct(FinancialSystemsRepository $financialSystemsRepository)
    {
        $this->financialSystemsRepository = $financialSystemsRepository;
    }

    public function execute($financialSystemReference)
    {
        return $this->financialSystemsRepository
            ->getByFinancialSystemReference($financialSystemReference);
    }
}
