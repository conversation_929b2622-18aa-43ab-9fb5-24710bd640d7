<?php


namespace Financial\Application\Contact;


use Financial\Domain\Contact\FinancialContact;
use Financial\Domain\FinancialSystem;
use Financial\Infrastructure\FinancialContactsRepository;

class GetAndOrCreateFinancialContactService
{
    /**
     * @var FinancialContactsRepository
     */
    private $financialContactsRepository;

    public function __construct(
        FinancialContactsRepository $financialContactsRepository
    )
    {

        $this->financialContactsRepository = $financialContactsRepository;
    }

    public function execute(FinancialSystem $financialSystem, $userId, $administration)
    {
        try {
            $financialContacts = $this->financialContactsRepository->retrieveByFinancialSystemAndAdministrationAndUserIdOrFail(
                $financialSystem->getId(),
                $administration,
                $userId
            );
        } catch (\RuntimeException $exception) {
            $this->financialContactsRepository->create($financialSystem->getId(), $userId, $administration);

            $financialContacts = $this->financialContactsRepository->retrieveByFinancialSystemAndAdministrationAndUserIdOrFail(
                $financialSystem->getId(),
                $administration,
                $userId
            );
        }


        return $financialContacts;
    }
}
