<?php

namespace Financial\Application\Contact;

use Financial\Application\GetFinancialSystemByReferenceService;
use Financial\Domain\FinancialSystem;
use Financial\Infrastructure\Models\FinancialSystems;

class LogStartSyncContactService
{
    /**
     * @var GetFinancialSystemByReferenceService
     */
    private $getFinancialSystemByReferenceService;
    /**
     * @var GetAndOrCreateFinancialContactService
     */
    private $getAndOrCreateFinancialContactService;
    /**
     * @var UpdateSyncDateByUserIdService
     */
    private $updateSyncDateByUserIdService;

    public function __construct(
        GetFinancialSystemByReferenceService $getFinancialSystemByReferenceService,
        GetAndOrCreateFinancialContactService $getAndOrCreateFinancialContactService,
        UpdateSyncDateByUserIdService $updateSyncDateByUserIdService
    ) {

        $this->getFinancialSystemByReferenceService = $getFinancialSystemByReferenceService;
        $this->getAndOrCreateFinancialContactService = $getAndOrCreateFinancialContactService;
        $this->updateSyncDateByUserIdService = $updateSyncDateByUserIdService;
    }

    public function execute($financialSystemRef, $userId, $administration)
    {
        try {
            $financialSystem = $this->getFinancialSystemByReferenceService->execute($financialSystemRef);
        } catch (\Exception $exception) {
            throw $exception;
        }

        try {
            $financialContact = $this->getAndOrCreateFinancialContactService->execute(
                $financialSystem,
                $userId,
                $administration
            );
        } catch (\Exception $exception) {
            throw $exception;
        }

        try {
            $this->updateSyncDateByUserIdService->execute($financialContact);
        } catch (\Exception $exception) {
            throw $exception;
        }

        return $financialContact;
    }
}
