<?php


namespace Financial\Application\Contact;


use Financial\Domain\Contact\FinancialContact;
use Financial\Infrastructure\FinancialContactsRepository;

class AddFinancialContactIdentifierService
{

    /**
     * @var FinancialContactsRepository
     */
    private $financialContactsRepository;

    public function __construct(FinancialContactsRepository $financialContactsRepository)
    {
        $this->financialContactsRepository = $financialContactsRepository;
    }

    public function execute(FinancialContact $financialContact, $financialContactIdentifier)
    {
        $this->financialContactsRepository->updateFinancialContactIdentifier($financialContact->getId(), $financialContactIdentifier);
    }
}
