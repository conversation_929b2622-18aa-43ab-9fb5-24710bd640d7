<?php


namespace Financial\Application\Contact;


use Financial\Infrastructure\FinancialContactErrorsRepository;

class GetAllFinancialContactErrorLogService
{
    /**
     * @var FinancialContactErrorsRepository
     */
    private $financialContactErrorsRepository;

    public function __construct(FinancialContactErrorsRepository $financialContactErrorsRepository)
    {
        $this->financialContactErrorsRepository = $financialContactErrorsRepository;
    }


    public function execute()
    {
        $financialContactErrors = $this->financialContactErrorsRepository->getAllDataOrderByDate();

        return $financialContactErrors;
    }
}
