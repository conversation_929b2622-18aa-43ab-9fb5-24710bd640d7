<?php


namespace Financial\Application\Contact;


use Financial\Domain\Contact\FinancialContact;
use Financial\Domain\FinancialSystem;
use Financial\Infrastructure\FinancialContactErrorsRepository;

class AddFinancialContactErrorService
{
    /**
     * @var FinancialContactErrorsRepository
     */
    private $financialContactErrorsRepository;

    public function __construct(FinancialContactErrorsRepository $financialContactErrorsRepository)
    {
        $this->financialContactErrorsRepository = $financialContactErrorsRepository;
    }

    public function execute(FinancialContact $financialContact, $message)
    {
        $this->financialContactErrorsRepository->create($financialContact->getId(), $message);
    }
}
