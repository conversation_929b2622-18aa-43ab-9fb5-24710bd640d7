<?php

namespace Import\Purchase;

class Implementations_Wooncompany extends PurchaseBase {

	protected $contact_company_id = 63;
	protected $component_id = 945;
	protected $default_project_id = 29;
	protected $EAN_codes = [];

	public function getEANCodes()
	{
		return $this->EAN_codes;
	}

	public function setEANCodes($EAN_codes)
	{
		$this->EAN_codes = $EAN_codes;
	}

	public function __construct($source_files)
	{
		$this->fetchEANCodes();
		parent::__construct($source_files);
	}

	public function parseSourceData($data)
	{
		$parsed_data = [];

		$component_data = db()->fetchRow(db()->select()->from('components')->where('id = ?', $this->component_id));

		$data = $this->groupPerObject($data);

		if(count($data) == 0) return;

		$invoiceCustomData = $this->getInvoiceCustomDataForInvoice(end($data));

		$invoice_date = strtotime(end($data)['Nota datum']);

		$invoiceCustomData['rows'] = [];

		foreach ($data as $item) {
			$invoiceCustomData['rows'][] = [
				'description' => 'Gas en electra ' . date('m-Y', $invoice_date),
				'price' => $item['Bedrag excl BTW'],
				'taxprice' => $item['Bedrag excl BTW'] + $item['Bedrag BTW'],
				'taxrate' => $item['BTW percentage'],
				'component' => $this->component_id,
				'ledger' => $component_data['ledger'],
				'service_charge' =>  $component_data['service_charge'],
				'cost_objects' => json_encode($item['object_id'] > 0 ? [$item['object_id']] : [])
			];

			if(!($item['object_id'] > 0))
				p('Object niet gevonden voor EAN ' . $item['EAN code']);
		}

		$parsed_data[] = $invoiceCustomData;

		parent::parseSourceData($parsed_data);
	}

	protected function getProjectForObject($object_id){

		if(!($object_id > 0)) return $this->default_project_id;

		$select = db()->select()
			->from(['o' => 'objects'], false)
			->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup', ['project'])
			->where('o.id = ?', $object_id);

		$project_id = db()->fetchOne($select);

		return $project_id > 0 ? $project_id : $this->default_project_id;
	}

	protected function getCorporationIdForProject($project_id){
		$select = db()->select()
			->from('projects', ['corporation'])
			->where('id = ?', $project_id);

		return db()->fetchOne($select);
	}

	protected function fetchEANCodes(){

		$select = db()->select()
			->from(['objects_meters'], ['identifier', 'object']);

		$this->setEANCodes(db()->fetchPairs($select));
	}

	protected function getObjectIdUsingEANCode($ean_code){
		return isset($this->EAN_codes[$ean_code]) ? $this->EAN_codes[$ean_code] : false;
	}

	protected function groupPerObject($data)
	{
		$data_grouped = [];

		foreach ($data as $source_file)
			foreach ($source_file as $key => $item) {

				if (!$item['EAN code']) continue;

				$item['object_id'] = $this->getObjectIdUsingEANCode($item['EAN code']) ?: false;
				$object_grouping_key = $item['object_id'] ?: trim($item['Locatie']);

				if (!isset($data_grouped[$object_grouping_key])) {
					$data_grouped[$object_grouping_key] = $item;
				} else {
					$data_grouped[$object_grouping_key]['Bedrag excl BTW'] += $item['Bedrag excl BTW'];
					$data_grouped[$object_grouping_key]['Bedrag BTW'] += $item['Bedrag BTW'];
				}
			}

		return $data_grouped;
	}

	protected function getInvoiceCustomDataForInvoice($invoice_data){

		$invoice_date = strtotime($invoice_data['Nota datum']);
		$project_id = $this->getProjectForObject($invoice_data['object_id']);

		return [
			'select' => [
				'type' => 'contact',
				'attach' => 'contact_' . $this->contact_company_id,
				'project' => $project_id
			],

			'total' => [
				'collection' => false,
				'substract' => []
			],

			'general' => [
				'title' => $invoice_data['Nota nummer'],
				'description' => 'Nota ' . $invoice_data['Nota nummer'] . ' periode ' . $invoice_data['Periode'],
				'invoicedate' => date('Y-m-d', $invoice_date),
				'expiredate' =>  date('Y-m-d', strtotime('+5 days', $invoice_date)),
				'investor_provision_deduction' => true
			],

			'corporation' => [
				'corporation' => $this->getCorporationIdForProject($project_id)
			],

			'purchase_status' => [
				'validation_user' => \loginManager::data()->id,
				'validating' => false
			],

			'fiscal' => [
				'period' => 'm_' . date('m', $invoice_date),
				'year' => date('Y', $invoice_date)
			]
		];
	}

}