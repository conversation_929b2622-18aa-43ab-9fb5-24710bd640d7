<?php

namespace Import\Purchase;


class PurchaseBase extends \Import\Base {

	public function execute()
	{
		$this->encodeRows();
		$this->makeInvoices();
	}

	private function makeInvoices(){
		$ic_lib = new \InvoiceCustom();
		$ic_lib->is_purchase = true;

		foreach ($this->getSourceData() as $invoice) {
			$invoice['type'] = 'purchase';
			$ic_lib->add(false, $invoice);
		}
	}

	private function encodeRows()
	{
		$source_data = $this->getSourceData();

		foreach ($source_data as $invoice_key => $invoice)
			$source_data[$invoice_key]['rows'] = json_encode($invoice['rows']);

		$this->setSourceData($source_data);
	}
}