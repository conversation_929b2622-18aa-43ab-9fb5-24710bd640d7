<?php
namespace Import;

class Base {

	private $source_files;
	private $source_type = 'csv';
	private $source_data;
	private $parsed_data;

	public function __construct($source_files)
	{
		$this->setSourceFiles($source_files);
		$this->setSourceDataFromFiles();
	}

	public function getSourceFiles()
	{
		return $this->source_files;
	}

	public function setSourceFiles($source_files)
	{
		$this->source_files = $source_files;
	}

	public function setSourceDataFromFiles()
	{
		$data = [];
		foreach($this->getSourceFiles() as $source_file) {
			if ($this->source_type == 'csv')
				$data[] = $this->parseCsv($source_file);
		}

		$this->parseSourceData($data);
	}

	public function parseSourceData($data){
		$this->setSourceData($data);
	}

	public function getSourceData()
	{
		return $this->source_data;
	}

	public function setSourceData($source_data)
	{
		$this->source_data = $source_data;
	}

	private function parseCsv($csv_string)
	{
		$csv_lines = explode("\n", $csv_string);
		$header_line = str_getcsv(array_shift($csv_lines));

		$data = [];
		foreach($csv_lines as $csv_line)
			$data[] = array_combine($header_line, str_getcsv($csv_line));

		return $data;
	}

	public function execute(){}

}