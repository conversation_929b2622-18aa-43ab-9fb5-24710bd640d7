<?php

namespace Controllers\Api\RegisterLead;

use SharedKernel\Application\Service\ApplicationService;

class InformLeadService implements ApplicationService
{
	/** @var boolean */
	private $rentalWebsiteEnabledSetting;
	/** @var integer */
	private $rentalWebsiteDefaultEmailAccountSetting;
	/** @var \Users */
	private $usersModel;
	/** @var \EmailAccount */
	private $emailAccount;
	/** @var \CRMEmailAddress */
	private $crmEmailAddressModel;

	/**
	 * @param bool $rentalWebsiteSetting
	 * @param int $rentalWebsiteDefaultEmailAccountSetting
	 * @param \Users $usersModel
	 * @param \EmailAccount $emailAccount
	 * @param \CRMEmailAddress $crmEmailAddressModel
	 */
	public function __construct(
		$rentalWebsiteSetting,
		$rentalWebsiteDefaultEmailAccountSetting,
		\Users $usersModel,
		\EmailAccount $emailAccount,
		\CRMEmailAddress $crmEmailAddressModel
	) {
		$this->rentalWebsiteEnabledSetting = $rentalWebsiteSetting;
		$this->rentalWebsiteDefaultEmailAccountSetting = $rentalWebsiteDefaultEmailAccountSetting;
		$this->usersModel = $usersModel;
		$this->emailAccount = $emailAccount;
		$this->crmEmailAddressModel = $crmEmailAddressModel;
	}

	public function execute($request)
	{
		if (!$this->leadShouldBeInformed()
			|| !$this->inputIsValid($request)
		) {
			return;
		}

		try {
			$toAddress = $this->retrieveToAddress($request);
			$emailData = $this->retrieveEmailData($request);
			$fromAccount = $this->retrieveFromAccount($request);

			$this->sendEmailToLead($fromAccount, $toAddress, $emailData);
		} catch (\RuntimeException $e) {
			$this->informSupport($e);
		}
	}

	public function leadShouldBeInformed()
	{
		return $this->rentalWebsiteEnabledSetting;
	}

	private function inputIsValid($request)
	{
		return (int)$request['userId'] > 0 && (boolean)$this->usersModel->getById($request['userId']);
	}

	/**
	 * @param array $request
	 * @return \Zend_Db_Table_Row_Abstract
	 */
	private function retrieveToAddress($request)
	{
		$crmEmailAddressRow = $this->crmEmailAddressModel->matchRow([
			'type' => 'user',
			'map_to' => $request['userId'],
		]);

		if ($crmEmailAddressRow === null || !trim($crmEmailAddressRow->address)) {
			throw new \RuntimeException('Lead email address not found or invalid');
		}

		return $crmEmailAddressRow;
	}

	private function retrieveEmailData($request)
	{
		$userRow = $this->usersModel->getById($request['userId']);
		$emailData = [];
		$emailData['company_name'] = \Settings::get('general_company') == 'Maison Management' ? '070 Wonen' : \Settings::get('general_company');
		$emailData['language'] = $userRow? $userRow['language']: 'nl';
        $emailData['payed_account'] = $userRow['payed_account'];
		$emailData['name'] = \User::buildnameMail(false, $request['userId']);
        $emailData['username'] = $userRow['username'];

		return $emailData;
	}

	/**
	 * @param array $request
	 * @return \Zend_Db_Table_Row_Abstract
	 */
	private function retrieveFromAccount(array $request)
	{
		$fromAccount = null;

		if ($this->rentalWebsiteDefaultEmailAccountSetting > 0) {
			$fromAccount = $this->emailAccount->getById($this->rentalWebsiteDefaultEmailAccountSetting);
		}

		if ($fromAccount === null) {
			$fromAccount = $this->retrieveFromAccountForUser($request['userId']);
		}

		if ($fromAccount === null) {
			throw new \RuntimeException('No valid from account found');
		}

		return $fromAccount;
	}

	private function sendEmailToLead(
		\Zend_Db_Table_Row_Abstract $fromAccount,
		\Zend_Db_Table_Row_Abstract $toAddress,
		$emailData
	) {
		/** @noinspection PhpUndefinedMethodInspection */
		$welcomeString = ucfirst(translate()->_('welcome', $emailData['language']));

		$template = \Settings::get('modules_rental_website_subscription_template') ? : 'register-lead-success' ;

		new \EmailOutgoing([
			'to' => [
				'name' => $emailData['name'],
				'email' => $toAddress->address
			],
			'from' => [
				'name' => $fromAccount->name,
				'email' => $fromAccount->address
			],
			'subject' => "{$welcomeString}!",
			'data' => $emailData,
			'template' => 'rental/website/'.$template.'.phtml',
		]);
	}

	private function informSupport(\Exception $e)
	{
		$generalCompanySetting = \Settings::get('general_company');
		$generalCompanyShortnameSetting = \Settings::get('general_company_shortname');

		$supportEmailAddr = [
			'name' => 'Klantenservice Omniboxx',
			'email' => '<EMAIL>',
		];

		new \EmailOutgoing([
			'to' => $supportEmailAddr,
			'from' => $supportEmailAddr,
			'subject' => 'Lead inschrijving bevestigingsmail niet gelukt',
			'text' => "Bij $generalCompanySetting/$generalCompanyShortnameSetting is iets mis gegaan bij het "
				. 'informeren van een nieuwe lead dat de inschrijving via de rental website gelukt is.<br>'
				. "De exceptie gaf '{$e->getMessage()}'als bericht, een mogelijke reden is een missend/incorrect email "
				. 'adres voor verzender of ontvanger'
		]);
	}

	/**
	 * @param integer $userId
	 * @return null|\Zend_Db_Table_Row_Abstract
	 */
	private function retrieveFromAccountForUser($userId) {
		$project = $this->usersModel->getProject($userId);

		if ($project) {
			/** @noinspection StaticInvocationViaThisInspection */
			$fromAccount = $this->emailAccount->getForRental(
				$project['corporation'],
				$project['id']
			);
		} else {
			/** @noinspection StaticInvocationViaThisInspection */
			$fromAccount = $this->emailAccount->getDefault();
		}

		return $fromAccount;
	}
}
