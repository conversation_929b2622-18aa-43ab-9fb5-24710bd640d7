<?php

namespace Controllers\Api\Accounting\BusinessCentral;

use Exception as Exception;
use Invoices as InvoiceModel;
use SimpleXMLElement;

class ReadInvoicesIdService
{
    private $invoiceModel;

    public function __construct(InvoiceModel $invoiceModel)
    {
        $this->invoiceModel = $invoiceModel;
    }

    /**
     * @throws Exception
     */
    public function execute()
    {
        $postContent = $this->getPostContent();
        $simpleXmlObject = $this->getSimpleXmlObject($postContent);


        foreach ($simpleXmlObject as $simpleXmlItem) {

            $this->updateFinancialInvoiceIdOnInvoiceModelRow($simpleXmlItem);
        }
    }

    private function getPostContent()
    {
        return file_get_contents('php://input');
//        return file_get_contents('Voorbeeld invoice id.xml');
    }

    /**
     * @param $postContent
     * @return SimpleXMLElement
     * @throws Exception
     */
    private function getSimpleXmlObject($postContent)
    {
        return new SimpleXMLElement($postContent);
    }

    /**
     * @param $simpleXmlItem
     * @throws Exception
     */
    private function updateFinancialInvoiceIdOnInvoiceModelRow($simpleXmlItem)
    {
        $invoiceId = (int)$simpleXmlItem->omniboxx_invoice_id;
        $xmlItemString = htmlentities($simpleXmlItem->asXML());

        if (!$invoiceId || $invoiceId <= 0) {
            throw new Exception('Item does not contain an Omniboxx_invoice_id: ' . $xmlItemString);
        }

        $financialInvoiceId = $simpleXmlItem->BC_invoice_id;

        if (!$financialInvoiceId || $financialInvoiceId <= 0) {
            throw new Exception('Item does not contain an BC_invoice_id: ' . $xmlItemString);
        }


        $invoiceModelRow = $this->invoiceModel->getById($invoiceId);

        if(!$invoiceModelRow){
            throw new Exception('Invoice id not found: ' . $invoiceId);
        }

        $debtorId = $simpleXmlItem->BC_debtor_id;

        if ($debtorId != '' && is_numeric($invoiceModelRow['users_objects'])) {

            $usersObjectsRow = \Objectusers::get($invoiceModelRow['users_objects']);
            if (is_numeric($usersObjectsRow['customer'])) {
                $userModelRow = \Users::get($usersObjectsRow['customer']);
                $userModelRow
                    ->setFromArray([
                        'olddebtorcode' => $debtorId
                    ])
                    ->save();
            }

        }


        $invoiceModelRow
            ->setFromArray([
                'financial_invoice_id' => $financialInvoiceId
            ])
            ->save();



    }


}
