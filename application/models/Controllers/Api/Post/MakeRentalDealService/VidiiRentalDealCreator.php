<?php

namespace Controllers\Api\Post\MakeRentalDealService;

class VidiiRentalDealCreator implements RentalDealCreatorInterface
{
	/** @var \RentalDeals */
	private $rentalDealsModel;

	/**
	 * VidiiRentalDealCreator constructor.
	 * @param \RentalDeals $rentalDealsModel
	 */
	public function __construct(\RentalDeals $rentalDealsModel)
	{
		$this->rentalDealsModel = $rentalDealsModel;
	}

	/**
	 * @param int $createdUserId
	 * @param \Zend_Db_Table_Row_Abstract $object
	 * @param $tenant
	 * @param $order
	 * @return \Zend_Db_Table_Row_Abstract
	 */
	public function create($createdUserId, $object, $tenant, $order = null)
	{

        $vidii_deal = 'yes';
        $website_deal = 'no';

        $project_vidii_deal_type = \Objects::getVidiiProjectSetting($object->id);

	    if ($project_vidii_deal_type == 'as_rental_deal') {
            $vidii_deal = 'no';
            $website_deal = 'yes';
        }

	    $rentalDealId = $this->rentalDealsModel->insert([
            'status_template_id' => $this->getRentalDealTemplateId($object->id),
            'assigned_user_id' => $createdUserId,
			'created_user_id' => $createdUserId,
			'object_id' => $object->id,
			'user_id' => $tenant->id,
			'concept_deal' => 'yes',
			'vidii_deal' => $vidii_deal,
			'website_deal' => $website_deal,
			'order' => $order
		]);

		return $this->rentalDealsModel->getById($rentalDealId);
	}

    private function getRentalDealTemplateId($objectId){
        $select = db()->select()
            ->from(['p' => 'projects'], ['rental_deals_default_template'])
            ->joinLeft(['og' => 'objectgroup'], 'og.project = p.id', false)
            ->joinLeft(['o' => 'objects'], 'o.objectgroup = og.id', false)
            ->where('o.id = ?', $objectId)
        ;

        return db()->fetchOne($select) ?: 1;
    }
}
