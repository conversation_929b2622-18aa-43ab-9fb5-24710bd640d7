<?php

namespace Controllers\Api\Post\MakeRentalDealService;

class ShouldTenantBeRegisteredAsDealViaRentalWebsite implements ShouldTenantBeRegisteredAsDealInterface
{
	/** @var \ObjectsPublish */
	private $objectsPublishModel;

	/**
	 * ShouldTenantBeRegisteredAsDealViaRentalWebsite constructor.
	 * @param \ObjectsPublish $objectsPublishModel
	 */
	public function __construct(\ObjectsPublish $objectsPublishModel)
	{
		$this->objectsPublishModel = $objectsPublishModel;
	}

	public function check(array $data)
	{
		try {
			$output = (int)$data['object'] > 0
				&& $this->objectsPublishModel->isObjectCurrentlyPublished($data['object']);
		} catch (\Exception $e) {
			$output = false;
		}

		return $output;
	}
}
