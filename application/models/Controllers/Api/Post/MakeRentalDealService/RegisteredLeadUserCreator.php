<?php

namespace Controllers\Api\Post\MakeRentalDealService;

class RegisteredLeadUserC<PERSON> implements UserCreatorInterface
{
	/** @var \Users */
	private $usersModel;

	/**
	 * RegisteredLeadUserCreator constructor.
	 * @param \Users $usersModel
	 */
	public function __construct(\Users $usersModel)
	{
		$this->usersModel = $usersModel;
	}

	/**
	 * @param array $request
	 * @return boolean
	 */
	public function doesTenantExist(array $request)
	{
		return \loginManager::check() && in_array(\loginManager::data()->rights, ['tenant', 'registrant', ], true);
	}

	/**
	 * @param array $request
	 * @return null|\Zend_Db_Table_Row_Abstract
	 */
	public function retrieveTenant(array $request)
	{
		return $this->usersModel->getById(\loginManager::data()->id);
	}

	/**
	 * @param array $request
	 * @throws \Exception
	 */
	public function createTenant(array $request)
	{
		throw new \RuntimeException('Not logged in users should not reach this code');
	}
}
