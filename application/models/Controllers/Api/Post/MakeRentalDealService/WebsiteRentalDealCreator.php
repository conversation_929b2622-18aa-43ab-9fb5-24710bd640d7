<?php

namespace Controllers\Api\Post\MakeRentalDealService;

class WebsiteRentalDealCreator implements RentalDealCreatorInterface
{
	/** @var \RentalDeals */
	private $rentalDealsModel;
    /** @var \RentalDealsStatus */
    private $rentalDealsStatusModel;

	/**
	 * VidiiRentalDealCreator constructor.
	 * @param \RentalDeals $rentalDealsModel
     * @param \RentalDealsStatus $rentalDealsStatusModel
	 */
	public function __construct(\RentalDeals $rentalDealsModel, \RentalDealsStatus $rentalDealsStatusModel)
	{
		$this->rentalDealsModel = $rentalDealsModel;
        $this->rentalDealsStatusModel = $rentalDealsStatusModel;
	}

	/**
	 * @param int $createdUserId
	 * @param \Zend_Db_Table_Row_Abstract $object
	 * @param $tenant
	 * @param $order
	 * @return \Zend_Db_Table_Row_Abstract
	 */
	public function create($createdUserId, $object, $tenant, $order = null)
	{
        /**
         * add check  by $tenant->id, and $object->id,
         */

        if (!$this->needTenantToBeAPayingForThisDeal($tenant, $object)) {
            return false;
        }

        if ($this->isThereAlreadyAnActiveDeal($tenant->id, $object->id)) {
            return false;
        }

        $rentalDealId = $this->rentalDealsModel->insert([
            'status_template_id' => $this->getRentalDealTemplateId(),
            'assigned_user_id' => $createdUserId,
			'created_user_id' => $createdUserId,
			'object_id' => $object->id,
			'user_id' => $tenant->id,
			'concept_deal' => 'yes',
			'vidii_deal' => 'no',
			'website_deal' => 'yes',
            'contact-date' => date('Y-m-d', strtotime('+5 days'))
		]);

		return $this->rentalDealsModel->getById($rentalDealId);
	}

    private function isThereAlreadyAnActiveDeal($tenantUserId, $objectId)
    {
        $select = db()->select()
            ->from(['rd' => 'rental_deals'], ['id'])
            ->where('rd.user_id = ?', $tenantUserId)
            ->where('rd.object_id = ?', $objectId)
            ->where('rd.website_deal = ?', 'yes')
            ->where('rd.cancelled = ?', 0)
            ->where('rd.archived = ?', 0)
        ;

        return db()->fetchOne($select);
    }

    private function getRentalDealTemplateId(){
        $select = db()->select()
            ->from('rental_deals_status_templates', ['id'])
            ->where('finalized = ?', true)
            ->where('archived = ?', false)
            ->where('concept = ?', false)
            ->order('default DESC');

        return db()->fetchOne($select) ?: 1;
    }

    private function needTenantToBeAPayingForThisDeal($user, \Zend_Db_Table_Row_Abstract $object)
    {
        $objectPublishRow = (new \ObjectsPublish())->getCurrentObjectsPublish($object->id);
        if (!$objectPublishRow) {
            return false;
        }

        if (!$objectPublishRow['enabled']) {
            return true;
        }

        if (!$objectPublishRow['report_date']) {
            return true;
        }

        $reportAate = (new \DateTime($objectPublishRow['report_date']));
        $reportAate->modify('+60 hours');

        if ((new \DateTime())->getTimestamp() < $reportAate->getTimestamp() && !$user->payed_account) {
            return false;
        }

        return true;
    }
}
