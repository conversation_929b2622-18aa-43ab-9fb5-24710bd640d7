<?php /** @noinspection StaticInvocationViaThisInspection */

namespace Controllers\Api\Post\MakeRentalDealService;

use Controllers\Api\Post\AddPotentialTenant;

class DuringRegistrationUserCreator implements UserCreatorInterface
{

	/** @var \CRMEmailAddress */
	private $crmEmailAddressModel;

	/** @var \Users */
	private $usersModel;

	/** @var \User */
	private $userLib;

	/** @var AddPotentialTenant */
	private $addPotentialTenantControllerModel;

	/**
	 * DuringRegistrationUserCreator constructor.
	 * @param \CRMEmailAddress $crmEmailAddressModel
	 * @param \Users $usersModel
	 * @param \User $userLib
	 * @param AddPotentialTenant $addPotentialTenantControllerModel
	 */
	public function __construct(
		\CRMEmailAddress $crmEmailAddressModel,
		\Users $usersModel,
		\User $userLib,
		AddPotentialTenant $addPotentialTenantControllerModel
	) {
		$this->crmEmailAddressModel = $crmEmailAddressModel;
		$this->usersModel = $usersModel;
		$this->userLib = $userLib;
		$this->addPotentialTenantControllerModel = $addPotentialTenantControllerModel;
	}


	/**
	 * @param array $request
	 * @return boolean
	 */
	public function doesTenantExist(array $request)
	{
		if (!filter_var($request['email'], FILTER_VALIDATE_EMAIL)) {
			return false;
		}

		$emailAddressExists = function ($emailAddress) {
			$exists = false;

			$crmEmailAddressRow = $this->crmEmailAddressModel->matchRow([
				'address' => $emailAddress,
				'type' => 'user',
			]);

			if (null !== $crmEmailAddressRow && (int)$crmEmailAddressRow->map_to > 0) {
				$userRow = $this->usersModel->getById($crmEmailAddressRow->map_to);
				$exists = $userRow !== null && in_array($userRow->type, ['tenant', 'registrant',], true);
			}

			return $exists;
		};

		return (
			$emailAddressExists($request['email'])
			|| $emailAddressExists(trim($request['email']))
			|| $emailAddressExists(filter_var($request['email'], FILTER_SANITIZE_EMAIL))
		);
	}

	/**
	 * @param array $request
	 * @return null|\Zend_Db_Table_Row_Abstract
	 */
	public function retrieveTenant(array $request)
	{
		$plain = $request['email'];
		$trimmed = trim($request['email']);
		$sanitized = filter_var($request['email'], FILTER_SANITIZE_EMAIL);

		$crmEmailAddressRow = $this->crmEmailAddressModel->select()
			->where('type = ?', 'user')
			->where("address = '$plain' OR address = '$trimmed' OR address = '$sanitized'")
			->order('map_to DESC')
			->query()
			->fetchObject();

		return $this->usersModel->getById($crmEmailAddressRow->map_to);
	}

	/**
	 * @param array $request
	 * @return null|\Zend_Db_Table_Row_Abstract
	 */
	public function createTenant(array $request)
	{
		if (trim($request['phone1']) === false) {
			$request['phone1'] = $request['phone2'];
		}

		$request = $this->parsePartnerData($request);

		$userRow = $this->handleRentalModuleSpecificFields($request, function ($request) {
			$userId = $this->userLib->editUser($request);
			return $this->usersModel->getById($userId);
		});

		return $userRow;
	}

	private function parsePartnerData(array $request)
	{
		if (isset($request['partner']) && !is_array($request['partner'])) {
			$partnerData = [];
			parse_str($request['partner'], $partnerData);
			$request['partner'] = $partnerData;
		}
		return $request;
	}

	private function handleRentalModuleSpecificFields(array $request, callable $userSaveFunction)
	{
		list($request, $rentalFields)
			= $this->addPotentialTenantControllerModel->formatRentalSubscriptionFields($request);

		$userRow = $userSaveFunction($request);

		if ($userRow !== null && !empty($rentalFields)) {
			$this->addPotentialTenantControllerModel->saveRentalModuleSpecificFields($userRow->id, $rentalFields);
		}

		return $userRow;
	}
}
