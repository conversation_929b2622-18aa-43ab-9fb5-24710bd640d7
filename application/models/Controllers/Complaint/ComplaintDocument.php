<?

class Controllers_Complaint_ComplaintDocument {

	public $complaintId;

	public $supportDocumentsModel;

	public $supportDocumentVersionsModel;

	public $documentCategoriesModel;

	public $documentCategoryTitlesModel;

	private $defaultCategory = false;

	private $defaultTitle = false;

	function __construct( $complaintId ) {
		$this->complaintId                  = $complaintId;
		$this->supportDocumentsModel        = new SupportDocuments();
		$this->supportDocumentVersionsModel = new SupportDocumentsVersions();
		$this->documentCategoriesModel      = new SupportDocumentsCategories();
		$this->documentCategoryTitlesModel  = new SupportDocumentsCategoriesTitles();
	}

	function addDocument( $filename, $public = false, $title = false, $categoryId = false, $titleId = false , $visibleForUser = false ) {
 		if ( intval( $this->complaintId ) <= 0 ) {
			return false;
		}

		if ( intval( $categoryId ) <= 0 ) {
			$categoryId = $this->getDefaultCategoryId();
		}

		if ( intval( $titleId ) <= 0 ) {
			$titleId = $this->getDefaultTitleId( $categoryId );
		}

		$data = [
			'user'           => loginManager::data()->id,
			'category'       => $categoryId,
			'map_to'         => $this->complaintId,
			'type'           => 'complaint',
			'title'          => ( trim( $title ) != false ? $title : nullValue() ),
			'titleid'        => $titleId,
			'visibleForUser' => $visibleForUser == 1 ? 1 : 2,
			'originalDate'   => new Zend_Db_Expr( "NOW()" ),
		];

		$row = $this->supportDocumentsModel->createRow( $data );
		$row->save();

		$row->document = $this->buildPath( $filename, $row->id );
		$row->save();

		return $row;
	}

	private function getDefaultCategoryId() {
		if ( ! is_object( $this->defaultCategory ) ) {
			$this->defaultCategory = $this->supportDocumentsModel->getCategoryByName( 'Klacht/storing bijlagen' );
		}

		return $this->defaultCategory['id'];
	}

	private function getDefaultTitleId( $categoryId ) {
		if ( ! is_object( $this->defaultTitle ) ) {
			$this->defaultTitle = $this->supportDocumentsModel->getTitleByName( $categoryId, 'Klacht/storing bijlage' );
		}

		return $this->defaultTitle['id'];
	}

	function buildPath( $filename, $documentId ) {
		if ( intval( $documentId ) <= 0 ) {
			return false;
		}

		return Main::app()->getDir( 'document' ) . "complaint/{$this->complaintId}/$documentId/{$filename}";
	}

	function addVersion( Zend_Db_Table_Row_Abstract $document ) {

		$data = [
			'support_documents' => $document['id']
		];

		$row = $this->supportDocumentVersionsModel->matchRow( $data );

		if ( ! $row ) {
			$row = $this->supportDocumentVersionsModel->createRow( $data );
		}

		$row->title        = $document['title'];
		$row->description  = $document['description'];
		$row->document     = $document['document'];
		$row->originalDate = new Zend_Db_Expr( "NOW()" );

		$row->save();

		return $row;
	}

	function getDocuments() {
		$output = $this->supportDocumentsModel->matchAll( [
			'type'   => 'complaint',
			'map_to' => $this->complaintId,
		] );

		return $output;
	}

	function deleteDocument( $documentId ) {

		$supportDocument = $this->supportDocumentsModel->getById( $documentId );

		if ( empty( $supportDocument ) || ! is_object( $supportDocument ) ) {
			return false;
		}

		$this->complaintId = $supportDocument->map_to;

		$versions = $this->supportDocumentVersionsModel->matchAll( [
			'support_documents' => $documentId,
		] );

		foreach ( $versions as $version ) {
			$version->delete();
		}

		$dir = pathinfo( $supportDocument->document, PATHINFO_DIRNAME );
		@unlink( $supportDocument->document );
		@rmdir( $dir );

		$supportDocument->delete();

		return true;
	}

}
