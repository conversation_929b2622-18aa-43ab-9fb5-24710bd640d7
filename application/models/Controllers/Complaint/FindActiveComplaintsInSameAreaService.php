<?php

namespace application\models\Controllers\Complaint;

use InvalidArgumentException as InvalidArgumentException;
use Objectusers as UserObjectModel;

class FindActiveComplaintsInSameAreaService
{

    private $userObjectModel;

    public function __construct(UserObjectModel $userObjectModel)
    {
        $this->userObjectModel = $userObjectModel;
    }

    public function execute($zipcodes)
    {
        $this->guardAgainstInvalidArguments($zipcodes);

        $complaintIds = array_merge(
            (array)$this->getComplaintIdsWithMatchingZipcodesForType($zipcodes, 'project'),
            (array)$this->getComplaintIdsWithMatchingZipcodesForType($zipcodes, 'objectgroup'),
            (array)$this->getComplaintIdsWithMatchingZipcodesForType($zipcodes, 'object'),
            (array)$this->getComplaintIdsWithMatchingZipcodesForTenants($zipcodes)
        );

        if(count($complaintIds) === 0){
            return [];
        }

        return $this->getComplaintDataList($complaintIds);
    }

    private function getComplaintDataList($complaintIds){
        $select = db()->select()
            ->from(
                ['sc' => 'support_complaints'],
                [
                    'id',
                    'identifier',
                    'date',
                    'map_to_description' => 'COALESCE(o2.rendered_address,  o.rendered_address,  og.description, p.name, u.rendered_name)',
                    'status' => 'sst.status',
                    'description' ,
                    'type',
                    'for_object',
                    'map_to',
                ]
            )
            ->joinLeft(['scc' => 'support_complaints_categories'], 'scc.id = sc.category', ['name'])
            ->joinLeft(['scs' => 'support_complaints_subcategories'], 'scs.id = sc.subcategory', ['subname'=>'name'])
            ->joinLeft(['u' => 'users'], 'u.id = sc.map_to AND sc.type = "user"', false)
            ->joinLeft(['o' => 'objects'], 'o.id = sc.map_to AND sc.type = "object"', false)
            ->joinLeft(['o2' => 'objects'], 'o2.id = sc.for_object AND sc.type = "user"', false)
            ->joinLeft(['og' => 'objectgroup'], 'og.id = sc.map_to AND sc.type = "objectgroup"', false)
            ->joinLeft(['p' => 'projects'], 'p.id = sc.map_to AND sc.type = "project"', false)
            ->joinLeft(['sst' => 'support_status_types'], 'sst.id = sc.last_status', false)
            ->where('sc.id IN (' . implode_for_where_in($complaintIds) . ')')
            ->order('sc.date DESC')
            ;

        //p($select,'die');
        return db()->fetchAll($select);
    }

    private function guardAgainstInvalidArguments($zipcodes)
    {
        if (!$zipcodes || !is_array($zipcodes) || count($zipcodes) === 0) {
            throw new InvalidArgumentException('Invalid argument "type": ' . $zipcodes);
        }
    }

    private function getComplaintIdsWithMatchingZipcodesForType($zipcodes, $type)
    {

        $itemIds = $this->getItemIdsForZipcodes($zipcodes, $type);

        if (count($itemIds) === 0) {
            return [];
        }

        $select = $this->getSupportComplaintIdsBaseSelect()
            ->where('type = ?', $type)
            ->where('map_to IN (' . implode_for_where_in($itemIds) . ')');

        return db()->fetchCol($select);
    }

    private function getItemIdsForZipcodes($zipcodes, $type)
    {
        $select = db()->select()
            ->from('address', ['type_id'])
            ->where('type = ?', $type)
            ->where('zipcode IN (' . implode_for_where_in($zipcodes) . ')');

        return db()->fetchCol($select);
    }

    private function getSupportComplaintIdsBaseSelect()
    {
        return db()->select()
            ->from(['sc' => 'support_complaints'], ['id'])
            ->joinLeft(['sst' => 'support_status_types'], 'sst.id = sc.last_status', false)
            ->where('sc.date >= ?', date('Y-m-d 00:00:00', strtotime('-20 weeks')))
            ->where('sst.solved_status = ?', false)
            ->where('sst.enabled = ?', true);
    }

    private function getComplaintIdsWithMatchingZipcodesForTenants($zipcodes)
    {
        $objectIds = $this->getItemIdsForZipcodes($zipcodes, 'object');

        if (count($objectIds) === 0) {
            return [];
        }

        $userIds = $this->getUserIdsForObjectIds($objectIds);

        if (count($userIds) === 0) {
            return [];
        }

        $select = $this->getSupportComplaintIdsBaseSelect()
            ->where('type = ?', 'user')
            ->where('map_to IN (' . implode_for_where_in($userIds) . ')');

        return db()->fetchCol($select);
    }

    private function getUserIdsForObjectIds($objectIds)
    {
        $userObjects = $this->userObjectModel->getMostRecentForObjects($objectIds);

        $userObjects = array_filter($userObjects, function ($userObjects) {
            return $userObjects['customer'] > 0;
        });

        return array_filter(array_column($userObjects, 'customer'));
    }
}
