<?php

namespace application\models\Controllers\Complaint;

use InvalidArgumentException as InvalidArgumentException;
use Objectusers as UserObjectModel;

class FindZipcodesByTypeAndMapToService
{
    private $supportTypeArguments = ['project', 'objectgroup', 'object', 'user'];

    public function execute($type, $mapTo)
    {
        $this->guardAgainstInvalidArguments($type, $mapTo);

        switch ($type) {
            case 'project':
                $zipcodes = $this->getZipcodesForProject($mapTo);
                break;

            case 'objectgroup':
                $zipcodes = $this->getZipcodesForObjectgroup($mapTo);
                break;

            case 'object':
                $zipcodes = $this->getZipcodesForObject($mapTo);
                break;

            case 'user':
                $zipcodes = $this->getZipcodesForUser($mapTo);
                break;

            default:
                return [];
        }

        return $this->sanitizeZipcodes($zipcodes);
    }

    private function guardAgainstInvalidArguments($type, $mapTo)
    {
        if (!in_array($type, $this->supportTypeArguments)) {
            throw new InvalidArgumentException('Invalid argument "type": ' . $type);
        }

        if (!is_numeric($mapTo)) {
            throw new InvalidArgumentException('Invalid argument "mapTo": ' . $mapTo);
        }
    }

    private function getZipcodesForProject($projectId)
    {
        $projectZipcodeSelect = db()->select()
            ->from('address', ['zipcode'])
            ->where('type = ?', 'project')
            ->where('type_id = ?', $projectId);

        $projectZipcode = db()->fetchOne($projectZipcodeSelect);

        $objectgroupZipcodesSelect = db()->select()
            ->distinct()
            ->from(['p' => 'projects'], false)
            ->joinLeft(['og' => 'objectgroup'], 'og.project = p.id', false)
            ->joinLeft(['a' => 'address'], 'a.type = "objectgroup" AND a.type_id = og.id', ['zipcode'])
            ->where('p.id = ?', $projectId);

        $objectgroupZipcodes = db()->fetchCol($objectgroupZipcodesSelect);

        $objectZipcodesSelect = db()->select()
            ->distinct()
            ->from(['p' => 'projects'], false)
            ->joinLeft(['og' => 'objectgroup'], 'og.project = p.id', false)
            ->joinLeft(['o' => 'objects'], 'o.objectgroup = og.id', false)
            ->joinLeft(['a' => 'address'], 'a.type = "object" AND a.type_id = o.id', ['zipcode'])
            ->where('p.id = ?', $projectId);

        $objectZipcodes = db()->fetchCol($objectZipcodesSelect);

        return array_merge(
            [$projectZipcode],
            $objectgroupZipcodes,
            $objectZipcodes
        );
    }

    private function getZipcodesForObjectgroup($objectgroupId)
    {
        $objectgroupZipcodeSelect = db()->select()
            ->from('address', ['zipcode'])
            ->where('type = ?', 'objectgroup')
            ->where('type_id = ?', $objectgroupId);

        $objectgroupZipcode = db()->fetchOne($objectgroupZipcodeSelect);

        $objectZipcodesSelect = db()->select()
            ->distinct()
            ->from(['og' => 'objectgroup'], false)
            ->joinLeft(['o' => 'objects'], 'o.objectgroup = og.id', false)
            ->joinLeft(['a' => 'address'], 'a.type = "object" AND a.type_id = o.id', ['zipcode'])
            ->where('og.id = ?', $objectgroupId);

        $objectZipcodes = db()->fetchCol($objectZipcodesSelect);

        return array_merge(
            [$objectgroupZipcode],
            $objectZipcodes
        );
    }

    private function getZipcodesForObject($objectId)
    {
        $select = db()->select()
            ->from('address', ['zipcode'])
            ->where('type = ?', 'object')
            ->where('type_id = ?', $objectId);

        return [db()->fetchOne($select)];
    }

    private function getZipcodesForUser($userId)
    {
        $userObjectModelRow = UserObjectModel::getCurrentForUser($userId);

        if (!$userObjectModelRow) {
            return [];
        }

        $select = db()->select()
            ->from('address', ['zipcode'])
            ->where('type = ?', 'object')
            ->where('type_id = ?', $userObjectModelRow->object);

        return [db()->fetchOne($select)];
    }

    private function sanitizeZipcodes($zipcodes)
    {
        array_walk($zipcodes, function ($zipcode) {
            return strtoupper(str_replace(' ', '', $zipcode));
        });

        sort($zipcodes);

        return array_filter($zipcodes);
    }

}
