<?php

namespace application\models\Controllers\Complaint;

use application\models\Controllers\BaseModels\AbstractSendSurveyInvite;
use application\models\Surveys\Application\Request\SendSurveyInviteRequest;
use application\models\Surveys\Application\Service\SendSurveyInviteService;
use application\models\Surveys\Domain\Exceptions\FrequencyPolicyException;
use application\models\Surveys\Domain\Exceptions\NoActiveTemplateException;
use application\models\Surveys\GenericDomain\Exceptions\MissingEmailAddressException;
use application\models\Surveys\GenericInfrastructure\Exceptions\SendInviteFailureException;

class Edit extends AbstractSendSurveyInvite
{
    private $statusTypeId;
    private $solvedDate;
    private $type;
    private $mapTo;
    private $complaintId;

    public function __construct($statusTypeId, $solvedDate, $type, $mapTo, $complaintId)
    {
        $this->statusTypeId = $statusTypeId;
        $this->solvedDate = $solvedDate;
        $this->type = $type;
        $this->mapTo = $mapTo;
        $this->complaintId = $complaintId;
    }

    public function sendSurveyInvite()
    {
        if (!$this->hasSurveyModule()) return false;
        if (!$this->isAllowedKey($this->statusTypeId)) return false;
        if (!$this->isSolvedDateNotOlderThanXDays($this->solvedDate, $this->getSendThreshold())) {
            $this->logSurveyInviteNotSend();
            return false;
        }

        if (!$this->isAllowedType($this->type)) return false;

        $request = $this->buildRequest();
        $this->send($request);
    }

    private function getSendThreshold()
    {
        $where = ['type = ?' => 'technical_contact'];

        $surveysTemplates = new \SurveysEvents();
        $row = $surveysTemplates->fetchRow($where);

        return $row['send_threshold'];
    }

    private function send($request)
    {
        $sendInviteService = new SendSurveyInviteService();

        try {
            $sendInviteService->execute($request);
        } catch (MissingEmailAddressException $e) {
        } catch (FrequencyPolicyException $e) {
        } catch (SendInviteFailureException $e) {

            $uniqueId = $this->saveExceptionToErrorLog($e, 'ComplaintController');

            $data = [
                'error_type' => 'uitnodiging',
                'unique_id' => $uniqueId,
                'error_message' => 'Het versturen van de enquête uitnodiging is niet gelukt'
            ];

            $this->sendEmailToSupport($data);

        } catch (NoActiveTemplateException $e) {

            $data = [
                'error_type' => 'uitnodiging',
                'error_message' => 'Er is geen enquête template ingesteld voor deze gebeurtenis'
            ];

            $this->sendEmailToSupport($data);
        } catch (\Exception $e) {

            $uniqueId = $this->saveExceptionToErrorLog($e, 'ComplaintController');

            $data = [
                'error_type' => 'uitnodiging',
                'unique_id' => $uniqueId,
                'error_message' => 'Er is een algemene fout opgetreden.'
            ];

            $this->sendEmailToSupport($data);
        }
    }

    private function buildRequest()
    {
        $userId = $this->getCorrectUserId($this->type, $this->mapTo, $this->solvedDate);
        $type = 'complaint';
        $mapToEventId = $this->complaintId;
        $eventType = 'technical_contact';

        $request = new SendSurveyInviteRequest($userId, $type, $mapToEventId, $eventType);

        return $request;
    }

    private function isAllowedKey($statusTypeId)
    {
        $typeKey = $this->getSupportStatusTypeKey($statusTypeId);
        $allowedTypesKeys = ['solved', 'finished_technical'];

        return in_array($typeKey, $allowedTypesKeys);
    }


    private function getSupportStatusTypeKey($supportStatusTypeId)
    {
        $supportStatusTypes = new \SupportStatusTypes();
        $supportStatusType = $supportStatusTypes->fetchRowById($supportStatusTypeId);

        return $supportStatusType->key;
    }

    private function isSolvedDateNotOlderThanXDays($solvedDate, $xDays)
    {
        if ($xDays == 0) return true;

        $solvedDate = new \DateTime(date('Y-m-d', strtotime($solvedDate)));
        $currentDate = new \DateTime(date('Y-m-d'));
        $differenceInDays = $solvedDate->diff($currentDate)->days;

        return $differenceInDays <= $xDays ? true : false;
    }

    private function logSurveyInviteNotSend()
    {
        $comment = 'Klanttevredenheid - Technische partij: Enquête uitnodiging niet verzonden. Afhandelingsdatum valt buiten de ingestelde verzend drempel';

        \Journal::getForLinks([
            [
                'type' => 'complaint',
                'map_to' => $this->complaintId
            ]
        ])->record($comment);
    }
}