<?

namespace Controllers\Complaint {

	class AddEdit {

		/**
		 * @param $complaintForm \Form
		 * @param $isCommercialTenant boolean
		 * @param $hierarchy \Hierarchy
		 * @param $user \GlobalDbRowClass
		 * @param $complaintId int|null
		 *
		 */
		public function handleInformeeFormField( $complaintForm, $isCommercialTenant, $hierarchy, $user = null, $complaintId = null ) {
			$tenantCanBeInformed = false;

			if ( ! empty( $user ) ) {
				if ( $isCommercialTenant ) {
					$company   = \Company::getByDummyUserId( $hierarchy->_data['user'] );
					$informees = \Company::getPotentialComplaintInformees( $company );

					if ( count( $informees['all'] ) > 0 ) {
						$complaintForm->inform_select->setMultiOptions( $informees['all'] );
					}

					$complaintForm->inform_select->setValue(
						$this->determineDefaultInformees( $complaintId, $informees )
					);

					$tenantCanBeInformed = count( $informees['all'] ) > 0;

				} else {
					$tenantCanBeInformed = $user->has( 'email_address' )
					                       && filter_var( $user->email_address->address, FILTER_VALIDATE_EMAIL );
				}
			}

            if (in_array($hierarchy->type, ['project', 'objectgroup'])) {
                $tenantCanBeInformed = true;

                $type_name = $hierarchy->type === 'project' ? 'dit project' : 'deze objectgroep';
                $complaintForm->inform->setLabel('Informeer huurders binnen ' . $type_name);
            }

			if ( $tenantCanBeInformed === false && $complaintForm->inform ) {
				$complaintForm->inform->setValue( false );
				$complaintForm->inform->setAttrib( 'disabled', 'disabled' );
				$complaintForm->inform->setAttrib( 'title', false );

                if ($hierarchy->type === 'object') {
                    $complaintForm->inform->setDescription('Bij het aanmaken van meldingen op objectniveau is het niet mogelijk automatisch een huurder te informeren, maak hiervoor de melding aan op de pagina van de betreffende huurder.');
                } else {
                    $complaintForm->inform->setDescription('Deze huurder heeft geen e-mail
					adres waar informatie heen gestuurd zou kunnen worden.');
                }
			}
		}

		/**
		 * @param $complaintId
		 * @param $informees
		 *
		 * @return array
		 */
		private function determineDefaultInformees( $complaintId, $informees ) {
			$defaultInformees = [ ];

			/** @var \GlobalDbRowClass $complaint */
			if ( intval( $complaintId ) > 0 && $complaint = \SupportComplaints::get( $complaintId ) ) {
				if ( $complaint->has( 'informees' ) ) {
					foreach ( $complaint->informees as $informee ) {
						$defaultInformees[] = $informee['map_to'];
					}
				}

			} else {
				$defaultInformees = $informees['default'];
			}

			return $defaultInformees;
		}
	}
}
