<?php

namespace Controllers\Complaint;

class WhatIsDefaultComponentVisibilityService
{
	private $defaultOutput = [
		'visibility' => false,
		'component_id' => 0,
	];

	private $complaintCategoriesModel;
	private $complaintSubcategoriesModel;

	public function __construct(
		\Zend_Db_Table_Abstract $complaintCategoriesModel = null,
		\Zend_Db_Table_Abstract $complaintSubcategoriesModel = null
	) {
		$this->complaintCategoriesModel = $complaintCategoriesModel ?: new \ComplaintCategories();
		$this->complaintSubcategoriesModel = $complaintSubcategoriesModel ?: new \ComplaintSubcategories();
	}


	/**
	 * @param string|null $complaintType
	 * @param int|null $categoryId
	 * @param int|null $subcategoryId
	 * @return array
	 */
	public function execute($complaintType = null, $categoryId = null, $subcategoryId = null)
	{
		$output = $this->defaultOutput;

		if (!$this->inputIsValid($complaintType, $categoryId)) {
			throw new \InvalidArgumentException("Complaint type ('$complaintType') or 
				categoryId ('$categoryId') cannot both be empty");
		}

		$complaintType = $this->ensureComplaintType($complaintType, $categoryId);

		if ($this->componentsShouldBeVisibleForCustomer()
			&& $this->componentsShouldBeVisibleForThisType($complaintType)) {
			$output['visibility'] = true;
		}

		$output['component_id'] = $this->setComponentIdBasedOnCategory($categoryId, $output['component_id']);
		$output['component_id'] = $this->setComponentIdBasedOnSubCategory($subcategoryId, $output['component_id']);

		return $output;
	}

	private function inputIsValid($complaintType, $categoryId)
	{
		return trim($complaintType) ||
			((int) $categoryId > 0 && $this->complaintCategoriesModel->hasMatchingRow(['id' => $categoryId]));
	}

	private function ensureComplaintType($complaintType, $categoryId)
	{
		if (!$complaintType) {
			$category = $this->complaintCategoriesModel->getById($categoryId);
			if ($category) {
				$complaintType = $category->type;
			}
		}
		return $complaintType;
	}

	private function componentsShouldBeVisibleForCustomer()
	{

	    return \Settings::get('components_on_complaints');
	}

	private function componentsShouldBeVisibleForThisType($complaintType)
	{
		return in_array($complaintType, ['malfunction', 'request']);
	}

	private function setComponentIdBasedOnCategory($categoryId, $componentId)
	{
		if ($categoryId) {
			$category = $this->complaintCategoriesModel->getById($categoryId);

			if ($category && intval($category->default_component_id) > 0) {
				$componentId = $category->default_component_id;
			}
		}

		return $componentId;
	}

	private function setComponentIdBasedOnSubCategory($subcategoryId, $componentId)
	{
		if ($subcategoryId) {
			$subcategory = $this->complaintSubcategoriesModel->getById($subcategoryId);

			if ($subcategory && intval($subcategory->default_component_id) > 0) {
				$componentId = $subcategory->default_component_id;
			}
		}
		return $componentId;
	}
}
