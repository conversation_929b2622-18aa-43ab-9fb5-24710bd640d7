<?php

namespace application\models\Controllers\Complaint;

class SaveInformeesService
{
    private $complaint_id;
    private $complaint_type;
    private $complaint_map_to;
    private $inform_select_data = [];

    public function __construct(
        $complaint_id,
        $complaint_type,
        $complaint_map_to,
        $inform_select_data = []
    ) {
        $this->complaint_id = $complaint_id;
        $this->complaint_type = $complaint_type;
        $this->complaint_map_to = $complaint_map_to;
        $this->inform_select_data = $inform_select_data;
    }

    public function execute()
    {
        $this->determineInformees();
        $this->saveInformees();
    }

    private function determineInformees()
    {
        if (count($this->inform_select_data) > 0) {
            return;
        }

        if ($this->complaint_type === 'user') {
            $this->determineSingleUserComplaintInformees();
        } else {
            $this->determineMultiUserComplaintInformees();
        }
    }

    private function determineSingleUserComplaintInformees()
    {
        $userRow = \Users::get($this->complaint_map_to);

        if (!$userRow) {
            return;
        }

        if ($userRow->type !== 'tenant') {
            return;
        }

        $this->inform_select_data[] = $userRow->id;
    }

    private function determineMultiUserComplaintInformees()
    {
        if (!in_array($this->complaint_type, ['project', 'objectgroup'])) {
            return;
        }

        $hierarchy = new \Hierarchy(
            $this->complaint_map_to,
            $this->complaint_type,
            false
        );

        $this->inform_select_data = array_column(
            $hierarchy->getUserList(),
            'id'
        );
    }

    private function saveInformees()
    {
        $complaint_model = new \SupportComplaints();
        $complaint_model_row = $complaint_model->getById($this->complaint_id);

        foreach ($this->inform_select_data as $user_id) {
            if (intval($user_id) > 0) {
                $complaint_model_row->create('informees', [
                    'map_to' => $user_id,
                ])->save();
            }
        }
    }
}
