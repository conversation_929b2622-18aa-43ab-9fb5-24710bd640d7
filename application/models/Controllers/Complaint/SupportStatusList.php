<?

class Controllers_Complaint_SupportStatusList {

	function getStatuses( Zend_Db_Table_Row_Abstract $complaint ) {

		$ssModel = new SupportStatus();
		$ssRows  = $ssModel->fetchAll( $ssModel->select()->where( 'type = ?', 'complaint' )->where( 'map_to = ? ', $complaint->id )->order( 'date' ) );

		$scdModel = new SupportComplaintsDeadline();
		$scdRows  = $scdModel->fetchAll( $scdModel->select()->where( 'complaint = ?', $complaint->id )->order( 'date' ) );

		$scsModel = new SupportComplaintsSolutions();
		$scsRowsApp  = $scsModel->fetchAll( $scsModel->select()->where( 'complaint = ?', $complaint->id )->where('type = ?', 'appointment')->order( 'execution_date' ) );
		$scsRowsMan  = $scsModel->fetchAll( $scsModel->select()->where( 'complaint = ?', $complaint->id )->where('type = ?', 'manual')->order( 'date' ) );

		$output = [ ];
		// add supportStatus rows
		foreach ( $ssRows as $ssRow ) {
			$stamp = strtotime( $ssRow->date );
			while ( isset( $output[ $stamp ] ) ) {
				$stamp ++;
			}

			$output[ $stamp ]                = $ssRow->toArray();
			$output[ $stamp ]['status_type'] = 'status';
		}

		// add supportComplaintDeadline rows
		foreach ( $scdRows as $scdRow ) {
			$stamp = strtotime( $scdRow->date );
			while ( isset( $output[ $stamp ] ) ) {
				$stamp ++;
			}

			$output[ $stamp ]                = $scdRow->toArray();
			$output[ $stamp ]['status_type'] = 'deadline';
		}

		// add supportComplaintSolution rows of type appointment
		foreach ( $scsRowsApp as $scsRow ) {
			$stamp = strtotime( $scsRow->execution_date );
			while ( isset( $output[ $stamp ] ) ) {
				$stamp ++;
			}

			$output[ $stamp ]                = $scsRow->toArray();
			$output[ $stamp ]['user']        = false;
			$output[ $stamp ]['date']        = $scsRow->execution_date;
			$output[ $stamp ]['status_type'] = 'solution';

		}

		// add supportComplaintSolution rows of type manual
		foreach ( $scsRowsMan as $scsRow ) {
			$stamp = strtotime( $scsRow->date );
			while ( isset( $output[ $stamp ] ) ) {
				$stamp ++;
			}

			$output[ $stamp ]                = $scsRow->toArray();
			$output[ $stamp ]['user']        = $scsRow->user;
			$output[ $stamp ]['status_type'] = 'solution';

		}

		// add journal records for complaint
		$output = $this->journalRecord($complaint, $output);

		// add the current moment
		$output[ time() ] = [
			'date'        => date( 'Y-m-d H:i:s' ),
			'status'      => - 1,
			'user'        => false,
			'status_type' => 'now',
		];

		ksort( $output );
		$output = array_reverse( $output );

		foreach ( $output as $i => &$item ) {
			$item['order'] = ( count( $output ) - $i );
		}

		return array_values( $output );
	}

	private function journalRecord($complaint, $output)
	{
		$where = [
			['type' => 'complaint', 'map_to' => $complaint->id]
		];

		$journal = Journal::getForLinks($where);

		foreach ($journal->records as $record) {

			$output[time()-1] = [
				'date' => $record['created_on'],
				'status' => - 1,
				'user' => false,
				'status_type' => 'journal_record',
				'message' => $record['message']
			];
		}

		return $output;
	}

	function getStatusTypes() {
		$output         = db()->fetchPairs( db()->select()->from( 'support_status_types', [ 'id', 'status' ] ) );
		$output[ null ] = 'Deadline';
		$output[ - 1 ]  = 'Nu';

		return $output;
	}
}
