<?php

namespace Controllers\Broker;

class ObjectList {

    private $investor_id;

    public function __construct($investor_id)
    {
        $this->investor_id = $investor_id;
    }

    public function getObjectIds()
    {
        $investor_model = new \Investors();
        return array_column($investor_model->getObjects($this->investor_id), 'id');
    }

    public function getList($params = [])
    {

        $uo_data= [];
        $select = db()->select()
            ->from(['o' => 'objects'], [
                'id',
                'object' => 'id',
                'build',
                'object_deposit' => '(o.`deposit` / 100)',
                'inactive',
                'parking_spots',
                'size' => 'm2',
                'ledger'
            ])
            ->joinLeft(['og' => 'objectgroup'], 'og.id = o.objectgroup',
                ['og_description' => 'description', 'og_type' => 'type'])
            ->joinLeft(['ot' => 'object_type'], 'ot.id = o.type', ['ot_description' => 'name'])
            ->joinLeft(['p' => 'projects'], 'p.id = og.project',
                ['project' => 'id', 'project_name' => 'name'])
            ->joinLeft(['c' => 'corporations'], 'p.corporation = c.id', ['corporation_name' => 'name'])
            ->joinLeft(['a' => 'address'], 'a.type_id = o.id AND a.type = "object"', ['address', 'number', 'zipcode', 'city'])
            ->joinLeft(['i' => 'investor'],
                'i.id = IF(o.`investoroverride` > 0, o.`investoroverride`, og.investor)',
                ['investor' => 'id', 'investor_name' => 'name'])
            ->where('p.test_project = ? OR p.test_project IS NULL', false)
            ->where('p.sales_status = ?', 'Closed')
            ->where('p.deleted = ?', false)
            ->where('p.exploitation = ?', true)
            ->order("p.name ASC")
            ->order('a.address ASC')
            ->order('LENGTH(a.number) ASC')
            ->order('LENGTH(o.build) ASC')
            ->order('a.number ASC')
            ->order('o.build ASC')
        ;

        if($object_ids = $this->getObjectIds()){
            $select->where('o.id IN(' . implode_for_where_in($object_ids) . ')');
        } else {
            return $uo_data;
        }

        $object_data = db()->fetchAssoc($select);
        $object_ids = array_keys($object_data);

        $uo_data = $this->getUserObjectData($object_data);
        $component_data = $this->getComponentData($object_ids);

        foreach($uo_data as $uo_data_row_key => $uo_data_row){

            $uo_data_row['rate'] = $this->getInvoiceRateForUserObject($uo_data_row);
            $uo_data_row = $this->addComponentsToDataItem($uo_data_row, $component_data);

            $uo_data[$uo_data_row_key] = $uo_data_row;
        }

        return $uo_data;
    }

    private function mergeAdditionalDataToTenantListRow($data_item, $uo_data){
        $object_has_matched_uo = false;
        $merged_data = [];

        foreach($uo_data as $uo_data_key => $uo_data_item) {
            if($data_item['id'] != $uo_data_item['object']){
                continue;
            }

            $startDate = strtotime($uo_data_item['from']);
            $state = !is_null($uo_data_item['tenant']) ? 'tenant' : 'empty';

            $merged_data[] = $data_item + $uo_data_item + ['state' => $state];
            $object_has_matched_uo = true;
        }

        if($object_has_matched_uo === false){
            $merged_data[] = $data_item + ['state' => 'empty'];
        }

        return $merged_data;
    }

    private function getComponentData($object_ids){
        $ocv_model = new \ObjectsComponentsValues();

        $ocv_data = [];
        $components = [];
        foreach($ocv_model->getMostRecentVersionValuesForObjects($object_ids, true) as $value_row){
            if(!isset($ocv_data[$value_row['object']])){
                $ocv_data[$value_row['object']] = [];
            }


            if(!isset( $ocv_data[$value_row['object']][$value_row['component']])){
                $ocv_data[$value_row['object']][$value_row['component']] = [];
            }

            $ocv_data[$value_row['object']][$value_row['component']] = $value_row;

            if(!isset($components[$value_row['component']])){
                $components[$value_row['component']] = $value_row['component_name'];
            }
        }

        return ['components' => $components, 'ocv_data' => $ocv_data];
    }

    private function addComponentsToDataItem($data_item, $component_data) {

        $ocv_data_for_object = isset($component_data['ocv_data'][$data_item['id']]) ? $component_data['ocv_data'][$data_item['id']] : [];

        $data_item['amount'] = array_sum(
            array_column($ocv_data_for_object, 'value_incl')
        );

        return $data_item;
    }

    private function getInvoiceRateForUserObject($uo_data_row){

        $default_og_rate = 12;

        if($uo_data_row['uo_id'] > 0){
            $rate_from_uo_prefs_select = db()->select()
                ->from(['users_invoice_preferences'], ['rate'])
                ->where('user IS NULL AND users_objects = ?', $uo_data_row['uo_id']);

            if($rate = db()->fetchOne($rate_from_uo_prefs_select)){
                return $rate;
            }
        }

        if($uo_data_row['tenant'] > 0) {
            $rate_from_user_prefs_select = db()->select()
                ->from(['users_invoice_preferences'], ['rate'])
                ->where('user = ?', $uo_data_row['tenant']);

            if($rate = db()->fetchOne($rate_from_user_prefs_select)){
                return $rate;
            }
        }

        return $default_og_rate;
    }

    private function getUserObjectData($object_data)
    {
        $object_ids = array_keys($object_data);

        $uo_data = $this->fetchUoDataForObjects($object_ids);
        $future_uo_data = $this->fetchFutureUoDataForObjects($object_ids);
        $unpayedPerUserPerObject = $this->fetchUnpayedPerObjectForUsers(array_column($uo_data, 'tenant'));

        $merged_data = [];
        foreach($object_data as $data_item){
            $merged_uo_data_for_item = $this->mergeAdditionalDataToTenantListRow(
                $data_item,
                $uo_data
            );


            foreach($merged_uo_data_for_item as $merged_uo_data_row){
                if($next_uo_row = $future_uo_data[$merged_uo_data_row['object']]){
                    $merged_uo_data_row += $next_uo_row;
                }

                if (isset($unpayedPerUserPerObject[$merged_uo_data_row['tenant']][$merged_uo_data_row['object']])) {
                    $merged_uo_data_row['unpayed'] = $unpayedPerUserPerObject[$merged_uo_data_row['tenant']][$merged_uo_data_row['object']];
                }

                $merged_data[] = $merged_uo_data_row;
            }
        }

        return $merged_data;
    }

    private function fetchUoDataForObjects($object_ids){

        $uo_select = db()->select()

            ->from(['uo' => 'users_objects'],  [
                'uo_id' => 'id',
                'object',
                'from',
                'till'
            ])

            ->joinLeft(['u' => 'users'], 'u.id = uo.customer',  [
                'tenant' => 'id',
                'tenant_name' =>'rendered_name',
                'useridentifier' => 'number'
            ])

            ->where('uo.object IN (' . implode_for_where_in($object_ids) . ')')
            ->where('uo.`till` >= "' . date('Y-m-d') . '" || uo.`till` IS NULL')
            ->where('uo.`from` < ?', date('Y-m-d'))
            ->where('uo.role = ?', 'normal');
        ;

        return db()->fetchAssoc($uo_select);
    }

    private function fetchFutureUoDataForObjects($object_ids){
        $uo_select = db()->select()

            ->from(['uo' => 'users_objects'],  [
                'object',
                'future_uo_id' => 'id',
                'future_from' => 'from',
                'future_till' => 'till',
            ])

            ->joinLeft(['u' => 'users'], 'u.id = uo.customer',  [
                'future_tenant' => 'id',
                'future_tenant_name' =>'rendered_name'
            ])

            ->where('uo.object IN (' . implode_for_where_in($object_ids) . ')')
            ->where('uo.`from` > ?', date('Y-m-d'))
            ->where('uo.role = ?', 'normal')
            ->group('uo.object')
        ;

        return db()->fetchAssoc($uo_select);
    }

    private function fetchUnpayedPerObjectForUsers($user_ids){
        if(count($user_ids) <= 0){
            return [];
        }

        $due_select = db()->select()
            ->from(['t' => 'transactions'],	['user'])
            ->joinLeft(['ir' => 'invoices_run'], 't.invoicerun = ir.id', false)
            ->joinLeft(['i' => 'invoices'], 'i.id = t.invoice', ['id'])
            ->joinLeft(['ic' => 'invoice_custom'], 'ic.id = i.customid', false)
            ->joinLeft(
                ['irows' => 'invoice_rows'],
                'irows.invoice = i.id AND irows.is_input = "1"',
                [
                    'object' => 'COALESCE(ic.object, irows.object)',
                    'unpayed' => 'SUM(irows.amount_total - (irows.amount_total * (t.payed / t.amount)))',
                ]
            )
            ->where('t.closed_date IS NULL')
            ->where('ir.id IS NOT NULL')
            ->where('t.type = ?', 'c')
            ->where ('ir.expire_date < NOW()')
            ->where('ir.status = ?', 2)
            ->where('t.user IN (' . implode_for_where_in($user_ids) . ')')
            ->group('t.user, COALESCE(ic.object, irows.object)')
            ;

        if(isset(\loginManager::data()->info['investor_manage_start_date'])) {
            if (isValidTimeStamp(strtotime(\loginManager::data()->info['investor_manage_start_date']))) {
                $due_select->where(
                    'ir.start >= ?',
                    date('Y-m-d', strtotime(\loginManager::data()->info['investor_manage_start_date']))
                );
            }
        }

        if (\Settings::get('broker_page_filter_project_corporation_id')) {
            $due_select
                ->joinLeft(['p' => 'projects'], 'p.id = ir.project', false)
                ->where('ic.corporation = p.corporation OR ic.id IS NULL');
        }

        $unpayedAmounts = db()->fetchAll($due_select);

        $unpayedAmountsGroupedPerUserPerObject = [];

        foreach ($unpayedAmounts as $unpayedAmount) {
            if (!isset($unpayedAmountsGroupedPerUserPerObject[$unpayedAmount['user']])) {
                $unpayedAmountsGroupedPerUserPerObject[$unpayedAmount['user']] = [];
            }

            $unpayedAmountsGroupedPerUserPerObject[$unpayedAmount['user']][$unpayedAmount['object']] =
                $unpayedAmount['unpayed'];
        }

        return $unpayedAmountsGroupedPerUserPerObject;
    }

}
