<?php

namespace EmailImport\Domain\Service;

use DateTime;
use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\NewEmail\FromImportDatePolicyException;
use EmailImport\Domain\Model\NewEmail\LegacyCutOffDatePolicyException;
use EmailImport\Domain\Model\NewEmail\NewEmail;
use EmailImport\Domain\Model\NewEmail\NewEmailAlreadyExistException;
use EmailImport\Domain\Model\NewEmail\NewEmailImportedDomainEvent;
use EmailImport\Infrastructure\Domain\Model\NewEmail\ZendNewEmailRepository;
use Emails;
use SharedKernel\Domain\DomainEventPublisher;

class ImportNewEmailService
{
    const LEGACY_CUT_OFF_DATE = '2019-10-28';

    private $newEmailRepository;

    public function __construct(ZendNewEmailRepository $newEmailRepository)
    {
        $this->newEmailRepository = $newEmailRepository;
    }

    public function import(NewEmail $newEmail, EmailAccount $emailAccount)
    {
        if (!$this->isSatisfiedByLegacyCutOffDatePolicy($newEmail)) {
            throw new LegacyCutOffDatePolicyException();
        }

        if (!$this->isSatisfiedByFromImportDatePolicy($newEmail)) {
            throw new FromImportDatePolicyException();
        }

        if ($this->doesNewEmailAlreadyExist($newEmail, $emailAccount)) {
            throw new NewEmailAlreadyExistException();
        }

        $emailRowId = $this->newEmailRepository->add($newEmail);
        DomainEventPublisher::instance()->publish(new NewEmailImportedDomainEvent($emailRowId));
    }

    private function doesNewEmailAlreadyExist(NewEmail $newEmail, EmailAccount $emailAccount)
    {
        $emailRow = $this->findEmailRowBySubjectAndDateAndMessageId($newEmail, $emailAccount);

        if (null !== $emailRow) {
            return true;
        }

        // This is a temporary fix for #234416
        // the problem was, when you got 2 mails of the same date and subject the above check would pass, but the below one would fail
        // resulting in that the second email would not be imported
        // I think the below check is legacy fallback code, what might be obsolete now.
        // If this proves to be stable, then we can remove old legacy fallback
        if (in_array(\Settings::get('general_company_shortname'), ['briq', 'vanvliet'])) {
            return false;
        }

        $emailRows = $this->findEmailRowsBySubjectAndDate($newEmail, $emailAccount);

        // found one
        if ($emailRows && $emailRows->count() === 1) {
            return true;
        }

        // found multiple candidates
        if ($emailRows && $emailRows->count() > 1) {
            return true;
        }

        // found none
        return false;
    }

    private function findEmailRowsBySubjectAndDate(NewEmail $newEmail, EmailAccount $emailAccount)
    {
        if (!$newEmail->getSubject()) {
            return null;
        }

        $where = [
            'date = ?' => $newEmail->getSentDate()->format('Y-m-d H:i:s'),
            'direction = ?' => 'incoming',
            'account = ?' => $emailAccount->getId(),
            'subject = ?' => $newEmail->getSubject()
        ];

        return (new Emails())->fetchAll($where);
    }

    private function findEmailRowBySubjectAndDateAndMessageId(NewEmail $newEmail, EmailAccount $emailAccount)
    {
        if (!$newEmail->getMessageId()) {
            return null;
        }

        $where = [
            'date = ?' => $newEmail->getSentDate()->format('Y-m-d H:i:s'),
            'direction = ?' => 'incoming',
            'account = ?' => $emailAccount->getId(),
            'message_id = ?' => $newEmail->getMessageId()
        ];

        if ($newEmail->getSubject()) {
            $where['subject = ?'] = $newEmail->getSubject();
        }

        return (new Emails())->fetchRow($where);
    }

    private function isSatisfiedByFromImportDatePolicy(NewEmail $newEmail)
    {
        if (\Settings::has('mail_account_from_import_date') === false) {
            return true;
        }

        return $newEmail->getSentDate() > new DateTime(\Settings::get('mail_account_from_import_date'));
    }

    private function isSatisfiedByLegacyCutOffDatePolicy(NewEmail $newEmail)
    {
        return $newEmail->getSentDate() > new DateTime(self::LEGACY_CUT_OFF_DATE);
    }
}
