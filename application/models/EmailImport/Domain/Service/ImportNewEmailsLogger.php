<?php

namespace EmailImport\Domain\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;

interface ImportNewEmailsLogger
{
    public function logIncompatibleMailServer(\Exception $e, EmailAccount $emailAccount);

    public function logCouldNotImportNewEmailMessage(\Exception $e, EmailAccount $emailAccount, $emailMessage);

    public function logNewEmailAlreadyExist(\Exception $e, EmailAccount $emailAccount, $emailMessage);
}
