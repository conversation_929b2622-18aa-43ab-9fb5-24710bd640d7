<?php

namespace EmailImport\Domain\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;

interface MailStorageConnection
{
    public function makeConnection(EmailAccount $emailAccount, $folder = null);

    /**
     * @param $dirname
     * @return bool
     */
    public function doesMailStorageDirExist($dirname);

    public function createFolder($dirname);

    /**
     * @return MailStorageIterator
     */
    public function getEmailMessagesIterator();

    public function moveMessageToDirectory($messageId, $dirname);

    public function deleteMessage($emailMessageId);
}
