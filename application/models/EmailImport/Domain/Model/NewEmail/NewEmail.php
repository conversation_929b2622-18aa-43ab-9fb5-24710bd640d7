<?php

namespace EmailImport\Domain\Model\NewEmail;

class NewEmail
{
    private $messageId;
    private $emailAccountId;
    private $sentDate;
    private $subject;
    private $content;
    private $from;
    private $to = [];
    private $cc = [];
    private $bcc;

    /**
     * @var Attachment[]
     */
    private $attachments = [];

    public function __construct($messageId, $emailAccountId, \DateTime $sentDate, $subject, $content, $from)
    {
        $this->messageId = $messageId;
        $this->emailAccountId = $emailAccountId;
        $this->sentDate = $sentDate;
        $this->subject = $subject;
        $this->content = $content;
        $this->from = $from;
    }

    public function getMessageId()
    {
        return $this->messageId;
    }

    public function getEmailAccountId()
    {
        return $this->emailAccountId;
    }

    public function getSentDate()
    {
        return $this->sentDate;
    }

    public function getSubject()
    {
        return $this->subject;
    }

    public function getContent()
    {
        return $this->content;
    }

    public function getFrom()
    {
        return $this->from;
    }

    public function addTo(array $to)
    {
        foreach ($to as $toRecipient) {
            $this->to[] = $toRecipient;
        }
    }

    public function getTo()
    {
        return $this->to;
    }

    public function addCC(array $cc)
    {
        foreach ($cc as $ccRecipient) {
            $this->cc[] = $ccRecipient;
        }
    }

    public function getCc()
    {
        return $this->cc;
    }

    public function setBcc($bcc)
    {
        $this->bcc = $bcc;
    }

    public function getBcc()
    {
        return $this->bcc;
    }

    public function addAttachment(Attachment $attachment)
    {
        $this->attachments[] = $attachment;
    }

    public function getAttachments()
    {
        return $this->attachments;
    }
}
