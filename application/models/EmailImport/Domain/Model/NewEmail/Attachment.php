<?php

namespace EmailImport\Domain\Model\NewEmail;

class Attachment
{
    private $id;
    private $filename;
    private $content;
    private $inlineReference;

    public function __construct($filename, $content, $inlineReference)
    {
        $this->filename = $filename;
        $this->content = $content;
        $this->inlineReference = $inlineReference;
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
    }

    public function getFilename()
    {
        return $this->filename;
    }

    public function getContent()
    {
        return $this->content;
    }

    public function getInlineReference()
    {
        return $this->inlineReference;
    }
}
