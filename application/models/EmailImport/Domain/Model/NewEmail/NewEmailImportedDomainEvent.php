<?php

namespace EmailImport\Domain\Model\NewEmail;

use SharedKernel\Domain\DomainEvent;

class NewEmailImportedDomainEvent implements DomainEvent
{
    private $occurredOn;
    private $emailRowId;

    public function __construct($emailRowId)
    {
        $this->occurredOn = new \DateTime();
        $this->emailRowId = $emailRowId;
    }

    public function occurredOn()
    {
        return $this->occurredOn;
    }

    public function getEmailRowId()
    {
        return $this->emailRowId;
    }
}
