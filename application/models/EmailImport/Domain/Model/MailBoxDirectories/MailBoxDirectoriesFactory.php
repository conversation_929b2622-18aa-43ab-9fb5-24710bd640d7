<?php

namespace EmailImport\Domain\Model\MailBoxDirectories;

class MailBoxDirectoriesFactory
{
    public function build($emailAccountId)
    {
        $emailAccountRow = $this->findEmailAccountRowOrFail($emailAccountId);

        if (!$emailAccountRow->incoming) {
            return MailBoxDirectories::make();
        }

        $emailServerRow = (new \EmailServers())->fetchRowById($emailAccountRow->incoming);

        if (null === $emailServerRow) {
            return MailBoxDirectories::make();
        }

        switch ($emailServerRow->remote_folder_strategy) {
            case '1':
                $inboxDir = str_replace('.', '/', MailBoxDirectories::INBOX_DIR);
                $importedDir = str_replace('.', '/', MailBoxDirectories::IMPORTED_DIR);
                $errorDir = str_replace('.', '/', MailBoxDirectories::ERROR_DIR);
                $skippedDir = str_replace('.', '/', MailBoxDirectories::SKIPPED_DIR);

                return MailBoxDirectories::makeFrom(
                    $inboxDir,
                    $importedDir,
                    $errorDir,
                    $skippedDir
                );
                break;
            default:
                return MailBoxDirectories::make();
                break;
        }
    }

    /**
     * @param $emailAccountId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findEmailAccountRowOrFail($emailAccountId)
    {
        $emailAccountRow = (new \EmailAccount())->getById($emailAccountId);

        if (null === $emailAccountRow) {
            throw new \RuntimeException('could not find email account row');
        }

        return $emailAccountRow;
    }
}
