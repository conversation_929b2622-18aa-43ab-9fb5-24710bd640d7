<?php

namespace EmailImport\Domain\Model\MailBoxDirectories;

class MailBoxDirectories
{
    const INBOX_DIR = 'INBOX';
    const IMPORTED_DIR = 'INBOX.OMNIBOXXIMPORT';
    const ERROR_DIR = 'INBOX.OMNIBOXXERROR';
    const SKIPPED_DIR = 'INBOX.OMNIBOXXSKIPPED';

    private $inboxDir;
    private $importedDir;
    private $errorDir;
    private $skippedDir;

    public static function make()
    {
        return new self(self::INBOX_DIR, self::IMPORTED_DIR, self::ERROR_DIR, self::SKIPPED_DIR);
    }

    public static function makeFrom($inboxDir, $importedDir, $errorDir, $skippedDir)
    {
        return new self($inboxDir, $importedDir, $errorDir, $skippedDir);
    }

    private function __construct($inboxDir, $importedDir, $errorDir, $skippedDir)
    {
        $this->setInboxDir($inboxDir);
        $this->setImportedDir($importedDir);
        $this->setErrorDir($errorDir);
        $this->setSkippedDir($skippedDir);
    }

    public function getInboxDir()
    {
        return $this->inboxDir;
    }

    private function setInboxDir($inboxDir)
    {
        $inboxDir = trim($inboxDir);

        if (!$inboxDir) {
            throw new \InvalidArgumentException('Inbox directory name cannot be empty');
        }

        $this->inboxDir = $inboxDir;
    }

    public function getImportedDir()
    {
        return $this->importedDir;
    }

    private function setImportedDir($importedDir)
    {
        $importedDir = trim($importedDir);

        if (!$importedDir) {
            throw new \InvalidArgumentException('Imported directory name cannot be empty');
        }

        $this->importedDir = $importedDir;
    }

    public function getErrorDir()
    {
        return $this->errorDir;
    }

    private function setErrorDir($errorDir)
    {
        $errorDir = trim($errorDir);

        if (!$errorDir) {
            throw new \InvalidArgumentException('Error directory name cannot be empty');
        }

        $this->errorDir = $errorDir;
    }

    public function getSkippedDir()
    {
        return $this->skippedDir;
    }

    private function setSkippedDir($skippedDir)
    {
        $skippedDir = trim($skippedDir);

        if (!$skippedDir) {
            throw new \InvalidArgumentException('Skipped directory name cannot be empty');
        }

        $this->skippedDir = $skippedDir;
    }
}
