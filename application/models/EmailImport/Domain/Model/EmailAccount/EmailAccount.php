<?php

namespace EmailImport\Domain\Model\EmailAccount;

class EmailAccount
{
    private $id;
    private $name;
    private $emailAddress;
    private $username;
    private $password;
    private $host;
    private $ssl;
    private $token;

    public function __construct($id, $name, $emailAddress, $username, $password, $host, $ssl, $token)
    {
        $this->id = $id;
        $this->name = $name;
        $this->emailAddress = $emailAddress;
        $this->username = $username;
        $this->password = $password;
        $this->host = $host;
        $this->ssl = $ssl;
        $this->token = $token;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getName()
    {
        return $this->name;
    }

    public function getEmailAddress()
    {
        return $this->emailAddress;
    }

    public function getUsername()
    {
        return $this->username;
    }

    public function getPassword()
    {
        return $this->password;
    }

    public function getHost()
    {
        return $this->host;
    }

    public function getSsl()
    {
        return $this->ssl;
    }

    public function getToken()
    {
        return $this->token;
    }
}
