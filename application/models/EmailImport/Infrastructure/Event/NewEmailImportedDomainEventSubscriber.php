<?php

namespace EmailImport\Infrastructure\Event;

use Email\AutoParser\AutoParseEmailRequest;
use EmailImport\Application\Service\AddLabelsToNewEmailRequest;
use EmailImport\Application\Service\MatchFundaEmailToObjectRequest;
use EmailImport\Application\Service\MatchNewEmailToComplaintRequest;
use EmailImport\Application\Service\SendEmailAutoReplyRequest;
use EmailImport\Domain\Model\NewEmail\NewEmailImportedDomainEvent;
use League\Tactician\CommandBus;
use SharedKernel\Domain\DomainEvent;
use SharedKernel\Domain\DomainEventSubscriber;

class NewEmailImportedDomainEventSubscriber implements DomainEventSubscriber
{
    private $commandBus;

    public function __construct(CommandBus $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    /**
     * @param NewEmailImportedDomainEvent $aDomainEvent
     */
    public function handle($aDomainEvent)
    {
        try {
            $this->commandBus->handle(new MatchNewEmailToComplaintRequest($aDomainEvent->getEmailRowId()));
        } catch (\Exception $e) {
            error_log("NewEmailImportedDomainEventSubscriber: ".$e->getMessage());
        }

        try {
            $this->commandBus->handle(new SendEmailAutoReplyRequest($aDomainEvent->getEmailRowId()));
        } catch (\Exception $e) {
            error_log("NewEmailImportedDomainEventSubscriber: ".$e->getMessage());
        }

        try {
            $this->commandBus->handle(new AddLabelsToNewEmailRequest($aDomainEvent->getEmailRowId()));
        } catch (\Exception $e) {
            error_log("NewEmailImportedDomainEventSubscriber: ".$e->getMessage());
        }

        try {
            $this->commandBus->handle(new MatchFundaEmailToObjectRequest($aDomainEvent->getEmailRowId()));
        } catch (\Exception $e) {
            error_log("NewEmailImportedDomainEventSubscriber: ".$e->getMessage());
        }

        if (\Settings::get('general_company_shortname') === 'remaxleiden') {
            try {
                $this->commandBus->handle(new AutoParseEmailRequest($aDomainEvent->getEmailRowId()));
            } catch (\Exception $e) {
                error_log("NewEmailImportedDomainEventSubscriber: ".$e->getMessage());
            }
        }
    }

    /**
     * @param DomainEvent $aDomainEvent
     * @return bool
     */
    public function isSubscribedTo($aDomainEvent)
    {
        return get_class($aDomainEvent) === NewEmailImportedDomainEvent::class;
    }
}
