<?php

namespace EmailImport\Infrastructure\Domain\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Service\MailStorageConnection;
use MailServerConnection\Infrastructure\OAuthServiceFactory;
use Zend_Mail_Storage_Imap;

class ZendMailStorageConnection implements MailStorageConnection
{
    /**
     * @var Zend_Mail_Storage_Imap
     */
    private $mailStorageConnection;

    public function makeConnection(EmailAccount $emailAccount, $folder = null)
    {
        $options = [
            'user' => $emailAccount->getUsername(),
            'password' => $emailAccount->getPassword(),
            'host' => $emailAccount->getHost(),
            'ssl' => $emailAccount->getSsl(),
        ];

        if ($folder) {
            $options['folder'] = $folder;
        }

        $accessToken = $emailAccount->getToken();

        if ($emailAccount->getToken()) {
            $oAuthService = (new OAuthServiceFactory())->build($emailAccount->getId());

            if ($oAuthService->isAccessTokenExpired($emailAccount->getId())) {
                $accessToken = $oAuthService->refreshToken($emailAccount->getId());
            }

            $options = new \ZendImapOAuth($emailAccount->getHost(), null, $emailAccount->getSsl());
            $options->loginOauth($emailAccount->getUsername(), $accessToken);
        }

        $this->mailStorageConnection = new Zend_Mail_Storage_Imap($options);
    }

    public function doesMailStorageDirExist($dirname)
    {
        $folders = new \RecursiveIteratorIterator($this->mailStorageConnection->getFolders());

        foreach ($folders as $localName => $folder) {
            if ($folder->getGlobalName() === $dirname) {
                return true;
            }
        }

        return false;
    }

    public function createFolder($dirname)
    {
        $this->mailStorageConnection->createFolder($dirname);
    }

    public function getEmailMessagesIterator()
    {
        return new ZendMailStorageIterator($this->mailStorageConnection);
    }

    public function moveMessageToDirectory($messageId, $dirname)
    {
        $attemptCount = 0;
        do {
            try {
                $this->mailStorageConnection->moveMessage($messageId, $dirname);
                break;
            } catch (\Zend_Mail_Storage_Exception $e) {
                $attemptCount++;
                sleep(3);
            }
        } while ($attemptCount <= 2);
    }

    public function deleteMessage($emailMessageId)
    {
        $this->mailStorageConnection->removeMessage($emailMessageId);
    }
}
