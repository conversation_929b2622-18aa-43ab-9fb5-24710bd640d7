<?php

namespace EmailImport\Infrastructure\Domain\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Service\ImportNewEmailsLogger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Handler\SlackHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class ZendImportNewEmailsLogger implements ImportNewEmailsLogger
{
    /**
     * @var Logger
     */
    private $logger;

    public function __construct()
    {
        $this->logger = $this->buildLogger();
    }

    private function buildLogger()
    {
        $logger = new Logger('import-new-email');

        global $site_base_dir;
        $filePath = $site_base_dir . '/_data/log/email-import/email-import.log';

        createFolder($filePath);

        $slackHandler = new SlackHandler(
            '****************************************************************************',
            '#import-new-email',
            'Monologger',
            true,
            null,
            Logger::ERROR
        );

        $logger->pushHandler($slackHandler);

        $handler = new RotatingFileHandler($filePath, 90, Logger::INFO, true, 0664);
        $handler->setFilenameFormat('{date}-{filename}', 'Y-m-d');

        $logger->pushHandler($handler);

        return $logger;
    }

    public function logIncompatibleMailServer(\Exception $e, EmailAccount $emailAccount)
    {
        $data = [
            'klant_naam' => \Settings::get('general_company'),
            'exception' => $this->makeExceptionData($e),
            'email_account' => $this->makeEmailAccountData($emailAccount)
        ];

        $this->logger->critical(
            'De mogelijkheden van de mail server bij de klant: ' . $data['klant_naam'] . ' sluiten niet aan bij de strategie om de nieuwe email te importeren! - ' . $e->getMessage(),
            $data
        );
    }

    /**
     * @param \Exception $e
     * @param EmailAccount $emailAccount
     * @param \Zend_Mail_Message $emailMessage
     */
    public function logCouldNotImportNewEmailMessage(\Exception $e, EmailAccount $emailAccount, $emailMessage)
    {
        $data = [
            'klant_naam' => \Settings::get('general_company'),
            'exception' => $this->makeExceptionData($e),
            'email_account' => $this->makeEmailAccountData($emailAccount),
            'email_message_headers' => $emailMessage->getHeaders()
        ];

        $this->logger->error(
            'Er is een exception opgetreden tijdens het email importeren van een email bericht bij de klant: ' . $data['klant_naam'] . '. Het email bericht is verplaats naar de Omniboxx error directory op de remote mail server. - ' . $e->getMessage(),
            $data
        );
    }

    /**
     * @param \Exception $e
     * @param EmailAccount $emailAccount
     * @param \Zend_Mail_Message $emailMessage
     */
    public function logNewEmailAlreadyExist(\Exception $e, EmailAccount $emailAccount, $emailMessage)
    {
        $data = [
            'klant_naam' => \Settings::get('general_company'),
            'exception' => $this->makeExceptionData($e),
            'email_account' => $this->makeEmailAccountData($emailAccount),
            'email_message_headers' => $emailMessage->getHeaders()
        ];

        $this->logger->warning(
            'Email al lijkt al eerder te zijn geimporteerd bij de klant: ' . $data['klant_naam'] . '. Het email bericht is verplaats naar de Omniboxx ERROR directory op de remote mail server.',
            $data
        );
    }

    private function makeExceptionData(\Exception $e)
    {
        return [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace_string' => $e->getTraceAsString()
        ];
    }

    private function makeEmailAccountData(EmailAccount $emailAccount)
    {
        return [
            'name' => $emailAccount->getName(),
            'id' => $emailAccount->getId(),
            'email_address' => $emailAccount->getEmailAddress()
        ];
    }
}
