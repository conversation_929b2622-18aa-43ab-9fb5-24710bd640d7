<?php

namespace EmailImport\Infrastructure\Domain\Service;

class FindHeaderService
{
    public function find($headerName, \Zend_Mail_Message $emailMessage)
    {
        if ($emailMessage->headerExists(strtolower($headerName))) {
            return $emailMessage->getHeader(strtolower($headerName));
        }

        if ($emailMessage->headerExists(ucfirst($headerName))) {
            return $emailMessage->getHeader(ucfirst($headerName));
        }

        if ($emailMessage->headerExists(strtoupper($headerName))) {
            return $emailMessage->getHeader(strtoupper($headerName));
        }

        if ($emailMessage->headerExists(ucwords($headerName))) {
            return $emailMessage->getHeader(ucwords($headerName));
        }

        return null;
    }
}
