<?php

namespace EmailImport\Infrastructure\Domain\Service;

use EmailImport\Domain\Service\MailStorageIterator;
use Zend_Mail_Storage_Imap;

class ZendMailStorageIterator implements MailStorageIterator
{
    private $zendMailStorageImap;
    private $currentIndex;
    private $currentEmailMessageId;

    public function __construct(Zend_Mail_Storage_Imap $zendMailStorageImap)
    {
        $this->zendMailStorageImap = $zendMailStorageImap;
        $this->currentIndex = $this->zendMailStorageImap->count();
    }

    /**
     * @important the decrementing is deliberate, because of the problem of the shifting pointer in a loop when
     * moving/removing an email message.
     */
    public function getNextEmailMessage()
    {
        $emailMessage = $this->zendMailStorageImap->getMessage($this->currentIndex);
        $this->currentEmailMessageId = $this->currentIndex;
        --$this->currentIndex;

        return $emailMessage;
    }

    public function getCurrentEmailMessageId()
    {
        return $this->currentEmailMessageId;
    }

    public function hasNextEmailMessage()
    {
        return $this->currentIndex > 0;
    }
}
