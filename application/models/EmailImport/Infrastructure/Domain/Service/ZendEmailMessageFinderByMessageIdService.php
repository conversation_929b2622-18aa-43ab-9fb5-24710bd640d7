<?php

namespace EmailImport\Infrastructure\Domain\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\MailBoxDirectories\MailBoxDirectories;
use EmailImport\Domain\Model\MailBoxDirectories\MailBoxDirectoriesFactory;
use EmailImport\Domain\Service\EmailMessageFinderService;
use MailServerConnection\Infrastructure\OAuthServiceFactory;
use Zend_Mail_Storage_Imap;

class ZendEmailMessageFinderByMessageIdService
{
    private $mailDirectoriesFactory;
    /**
     * @var Zend_Mail_Storage_Imap
     */
    private $mailStorageConnection;

    private $findHeaderService;

    public function __construct(
        MailBoxDirectoriesFactory $mailBoxDirectoriesFactory,
        FindHeaderService $findHeaderService
    ) {
        $this->mailDirectoriesFactory = $mailBoxDirectoriesFactory;
        $this->findHeaderService = $findHeaderService;
    }

    /**
     * @param EmailAccount $emailAccount
     * @param $messageId
     * @param $dateString
     * @param $subject
     * @return \Zend_Mail_Message|null
     */
    public function find(EmailAccount $emailAccount, $messageId)
    {
        $mailDirectories = $this->mailDirectoriesFactory->build($emailAccount->getId());

        $directory =  $mailDirectories->getImportedDir();

        $emailMessage = $this->findEmailMessageInDirectory(
            $directory,
            $emailAccount,
            $messageId
        );

        if (null == $emailMessage) {
            return null;
        }

        return $emailMessage;
    }

    private function findEmailMessageInDirectory(
        $directory,
        EmailAccount $emailAccount,
        $providedMessageId
    ) {
        $this->connectToMailStorage($emailAccount, $directory);

        $foundEmailMessage =  $this->mailStorageConnection->searchMessageID($providedMessageId);

        return $foundEmailMessage;
    }

    private function connectToMailStorage(EmailAccount $emailAccount, $directory)
    {

        $options = [
            'user' => $emailAccount->getUsername(),
            'password' => $emailAccount->getPassword(),
            'host' => $emailAccount->getHost(),
            'ssl' => $emailAccount->getSsl()
        ];

        $accessToken = $emailAccount->getToken();

        if ($emailAccount->getToken()) {
            $oAuthService = (new OAuthServiceFactory())->build($emailAccount->getId());

            if ($oAuthService->isAccessTokenExpired($emailAccount->getId())) {
                $accessToken = $oAuthService->refreshToken($emailAccount->getId());
            }

            $options = new \ZendImapOAuth($emailAccount->getHost(), null, $emailAccount->getSsl());
            $options->loginOauth($emailAccount->getUsername(), $accessToken);
        }

        $this->mailStorageConnection = new Zend_Mail_Storage_Imap($options);
        $this->mailStorageConnection->selectFolder($directory);
    }

    private function searchByMessageId(\Zend_Mail_Message $emailMessage, $providedMessageId)
    {
        if (null === $providedMessageId) {
            return null;
        }

        $messageIdHeaderField = $this->findHeaderService->find('message-id', $emailMessage);

        if ($providedMessageId === $messageIdHeaderField) {
            return $emailMessage;
        }

        return null;
    }

    private function searchByDateAndSubject(\Zend_Mail_Message $emailMessage, $providedDate, $subject)
    {
        if (null === $providedDate || null === $subject) {
            return null;
        }

        $providedDate = (new \DateTime($providedDate))->format('Y-m-d H:i:s');
        $subjectHeaderField = $this->findHeaderService->find('subject', $emailMessage);
        $dateHeaderField = $this->findHeaderService->find('date', $emailMessage);

        preg_match_all("/.*\+([\d+]{4})/", $dateHeaderField, $matchDateHeaderField);
        if (is_array($matchDateHeaderField)) {
            $dateHeaderField = $matchDateHeaderField[0][0];
        }
        $dateHeaderField = (new \DateTime($dateHeaderField))->format('Y-m-d H:i:s');

        if ($subjectHeaderField === $subject) {
            return $emailMessage;
        }

        return null;
    }
}
