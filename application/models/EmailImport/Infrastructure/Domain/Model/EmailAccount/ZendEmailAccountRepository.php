<?php

namespace EmailImport\Infrastructure\Domain\Model\EmailAccount;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\EmailAccount\EmailAccountRepository;
use SharedKernel\Application\Service\EncryptionService;

class ZendEmailAccountRepository implements EmailAccountRepository
{
    /**
     * @param $emailAccountId
     * @return EmailAccount|null
     * @throws \Zend_Db_Statement_Exception
     */
    public function ofId($emailAccountId)
    {
        $emailAccountRow = db()->select()
            ->from('email_accounts', ['id', 'name', 'address', 'username', 'password', 'incoming', 'token'])
            ->where('id = ?', $emailAccountId)
            ->query()
            ->fetch();

        if (empty($emailAccountRow)) {
            return null;
        }

        if (!is_numeric($emailAccountRow['incoming']) || $emailAccountRow['incoming'] === '0') {
            return null;
        }

        $emailServerRow = db()->select()
            ->from('email_servers', ['host', 'ssl'])
            ->where('id = ?', $emailAccountRow['incoming'])
            ->query()
            ->fetch();

        $data = array_merge($emailAccountRow, $emailServerRow);

        if ($data['token']) {
            $tokenData = json_decode($data['token'], true);
            $data['token'] = isset($tokenData['access_token']) ? $tokenData['access_token'] : null;
        }

        $data['password'] = (new EncryptionService())->decrypt($data['password']);

        return new EmailAccount(
            $data['id'],
            $data['name'],
            $data['address'],
            $data['username'],
            $data['password'],
            $data['host'],
            $data['ssl'],
            $data['token']
        );
    }
}
