<?php

namespace EmailImport\Infrastructure\Domain\Model\NewEmail;

use Email\Parse;
use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\NewEmail\Attachment;
use EmailImport\Domain\Model\NewEmail\NewEmail;
use EmailImport\Domain\Model\NewEmail\NewEmailFactory;
use EmailImport\Infrastructure\Domain\Service\FindHeaderService;

class LegacyNewEmailFactory implements NewEmailFactory
{
    private $parseEmailMessagePartsService;
    private $findHeaderService;

    public function __construct(
        ParseEmailMessagePartsService $parseEmailMessagePartsService,
        FindHeaderService $findHeaderService
    ) {
        $this->parseEmailMessagePartsService = $parseEmailMessagePartsService;
        $this->findHeaderService = $findHeaderService;
    }

    /**
     * @param \Zend_Mail_Message $emailMessage
     * @param EmailAccount $emailAccount
     * @return NewEmail
     */
    public function build($emailMessage, EmailAccount $emailAccount)
    {
        mb_internal_encoding('UTF-8');
        header('Content-Type:text/html; charset=UTF-8');

        $emailMessagePartsData = $this->parseEmailMessagePartsService->execute($emailMessage, $emailAccount);

        $messageId = $this->findHeaderService->find('message-id', $emailMessage);
        $subject = $this->makeSubjectHeader($emailMessage);
        $from = $this->makeFromHeader($emailMessage);
        $date = $this->makeDateFieldOrFail($emailMessage);

        $newEmail = new NewEmail(
            $messageId,
            $emailAccount->getId(),
            $date,
            $subject,
            $emailMessagePartsData['content'],
            $from
        );

        $to = $this->makeToHeader($emailMessage);
        $cc = $this->makeCCHeader($emailMessage);
        $bcc = $this->makeBcc($emailMessage, $to, $cc);

        $newEmail->addTo($to);
        $newEmail->addCC($cc);
        $newEmail->setBcc($bcc);

        if (is_array($emailMessagePartsData['attachments'])) {
            foreach ($emailMessagePartsData['attachments'] as $attachment) {
                $attachment = new Attachment(
                    $attachment['filename'],
                    $attachment['data'],
                    $attachment['inline-reference']
                );

                $newEmail->addAttachment($attachment);
            }
        }

        return $newEmail;
    }

    private function makeFromHeader(\Zend_Mail_Message $emailMessage)
    {
        $from = $this->findHeaderService->find('from', $emailMessage);

        if (null === $from) {
            return [];
        }

        $result = Parse::getInstance()->parse($from);

        if (!$result['success']) {
            $emailAddress = $this->tryParseOnlyEmailAddressOrNull($from);
            $name = $this->tryParseOnlyNameOrNull($from, $emailAddress);

            return [
                'name' => $name,
                'email' => $emailAddress
            ];
        }

        $fromEmailAddress = $result['email_addresses'][0]['simple_address'];

        $hasDetectedEncoding = mb_detect_encoding($fromEmailAddress);

        if ($hasDetectedEncoding) {
            $fromEmailAddress = mb_convert_encoding($fromEmailAddress, 'UTF-8');
        }

        return [
            'name' => null,
            'email' => $fromEmailAddress
        ];
    }

    private function makeToHeader(\Zend_Mail_Message $emailMessage)
    {
        $toHeader = $this->findHeaderService->find('to', $emailMessage);

        if (null === $toHeader) {
            return [];
        }

        $result = Parse::getInstance()->parse($toHeader);

        if (!$result['success']) {
            $toHeader = $this->stripOutIllegalCharacters($toHeader);
            $result = Parse::getInstance()->parse($toHeader);
        }

        if (!$result['success']) {
            return [];
        }

        $toRecipients = [];
        foreach ($result['email_addresses'] as $emailAddress) {
            $toRecipients[] = $emailAddress['simple_address'];
        }

        foreach ($toRecipients as $id => $toRecipient) {
            $toRecipients[$id] = $this->tryConvertEncodingToUTF8($toRecipient);
        }

        return $toRecipients;
    }

    private function makeCCHeader(\Zend_Mail_Message $emailMessage)
    {
        $ccHeader = $this->findHeaderService->find('cc', $emailMessage);

        if (null === $ccHeader) {
            return [];
        }

        $result = Parse::getInstance()->parse($ccHeader);

        if (!$result['success']) {
            $ccHeader = $this->stripOutIllegalCharacters($ccHeader);
            $result = Parse::getInstance()->parse($ccHeader);
        }

        if (!$result['success']) {
            return [];
        }

        $ccRecipients = [];
        foreach ($result['email_addresses'] as $emailAddress) {
            $ccRecipients[] = [
                'name' => $emailAddress['name_parsed'],
                'email' => $emailAddress['simple_address']
            ];
        }

        foreach ($ccRecipients as $id => $ccRecipient) {
            $ccRecipients[$id] = $this->tryConvertEncodingToUTF8($ccRecipient);
        }

        return $ccRecipients;
    }

    private function tryConvertEncodingToUTF8($recipient)
    {
        if (is_array($recipient)) {
            foreach ($recipient as $key => $value) {
                $recipient[$key] = mb_detect_encoding($value) ? mb_convert_encoding($value, 'UTF-8') : $value;
            }

            return $recipient;
        }

        return mb_detect_encoding($recipient) ? mb_convert_encoding($recipient, 'UTF-8') : $recipient;
    }

    private function makeBcc(\Zend_Mail_Message $emailMessage, array $toRecipients, array $cc)
    {
        $xOrigTo = $this->findHeaderService->find('x-orig-to', $emailMessage);

        if (null === $xOrigTo) {
            return null;
        }

        $bcc = null;
        // the current E-mail account is not the recipient, so must be CC or BCC
        if (!in_array($xOrigTo, $toRecipients, false)) {
            if (!empty($cc)) {
                if (!in_array($xOrigTo, $cc, false)) {
                    $bcc = $xOrigTo;
                }
            } else {
                $bcc = $xOrigTo;
            }
        }

        if (null === $bcc) {
            return null;
        }

        $result = Parse::getInstance()->parse($bcc);

        if (!$result['success']) {
            return null;
        }

        $bccEmailAddress = $result['email_addresses'][0]['simple_address'];

        $hasDetectedEncoding = mb_detect_encoding($bccEmailAddress);

        if ($hasDetectedEncoding) {
            $bccEmailAddress = mb_convert_encoding($bccEmailAddress, 'UTF-8');
        }

        return [
            'name' => null,
            'email' => $bccEmailAddress
        ];
    }

    private function makeSubjectHeader(\Zend_Mail_Message $emailMessage)
    {
        $subject = $this->findHeaderService->find('subject', $emailMessage);

        if (null === $subject) {
            return null;
        }

        $subject = mb_decode_mimeheader($subject);
        $subject = mb_detect_encoding($subject) !== 'UTF-8' ? mb_convert_encoding($subject, 'UTF-8') : $subject;

        return $subject;
    }

    private function makeDateFieldOrFail(\Zend_Mail_Message $emailMessage)
    {
        $date = $this->findHeaderService->find('date', $emailMessage);

        if (null === $date) {
            $date = $this->tryFindDateInContentTypeHeader($emailMessage);
        }

        if ($date) {
            $date = $this->cleanUpDate($date);
        }

        if (null === $date) {
            throw new \RuntimeException('Could not parse the date header');
        }

        $date = new \DateTime($date);
        $this->tryConvertGivenTimeZoneToLocalTimeZone($date);

        return $date;
    }

    private function tryFindDateInContentTypeHeader(\Zend_Mail_Message $emailMessage)
    {
        $contentType = $this->findHeaderService->find('content-type', $emailMessage);

        if (null === $contentType) {
            return null;
        }

        $dateStringVariants = [
            'date:',
            'Date'
        ];

        $date = null;
        foreach ($dateStringVariants as $dateStringVariant) {
            $datePos = strpos($contentType, $dateStringVariant);

            if ($datePos !== false) {
                $date = substr($contentType, $datePos + 5);
            }
        }

        if (new \DateTime($date) === new \DateTime()) {
            throw new \RuntimeException('Date header can never be the same time as current time, something is wrong');
        }

        return $date;
    }

    private function cleanUpDate($date)
    {
        $date = $this->stripOfBoundary($date);
        $date = $this->stripOfDoubleTimeZone($date);

        // UT is not supported in PHP 5.6 and starts from PHP 7+
        if (strpos($date, 'UTC') === false) {
            $date = str_replace('UT', 'UTC', $date);
        }

        return $date;
    }

    private function tryParseOnlyEmailAddressOrNull($from)
    {
        preg_match_all("/[\._a-zA-Z0-9-]+@[\._a-zA-Z0-9-]+/i", $from, $matches);
        return !empty($matches) ? $matches[0][0] :  null;
    }

    private function tryParseOnlyNameOrNull($from, $emailAddress)
    {
        $name = str_replace([$emailAddress, '<', '>'], '', $from);
        $name = trim($name);
        return $name ?: null;
    }

    private function stripOfBoundary($date)
    {
        $pos = strpos($date, 'boundary=');

        if ($pos !== false) {
            $date = substr($date, 0, $pos);
        }

        return $date;
    }

    private function stripOfDoubleTimeZone($date)
    {
        if (strpos($date, '(') !== false && strrpos($date, ')') !== false) {
            $date = substr($date, 0, strpos($date, '('));
        }

        return $date;
    }

    private function tryConvertGivenTimeZoneToLocalTimeZone(\DateTime $date)
    {
        try {
            $date->setTimezone(new \DateTimeZone('Europe/Amsterdam'));
        } catch (\Exception $e) {
        }
    }

    private function stripOutIllegalCharacters($header)
    {
        // there are some unsupported characters in the name part of the To header for the following parser.
        $values = explode(',', $header);

        foreach ($values as $key => $value) {
            $parts = explode('<', $value);

            if (count($parts) === 2) {
                $parts[0] = str_replace(['|', '.'], '', $parts[0]);
                $value = implode('<', $parts);
                $values[$key] = $value;
            }
        }

        return implode(',', $values);
    }
}
