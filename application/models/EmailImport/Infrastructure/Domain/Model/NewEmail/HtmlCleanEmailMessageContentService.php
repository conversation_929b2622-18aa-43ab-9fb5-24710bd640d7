<?php

namespace EmailImport\Infrastructure\Domain\Model\NewEmail;

use Html2Text;

class HtmlCleanEmailMessageContentService
{
    public function clean($html)
    {

        $options = array(
            'remove_all_conditional_comments' => true,
            'only_not_mso'                    => true,
            'remove_entire_style_block'       => false,
            'first_style_block_only'          => false,
        );


        if (!empty($options['remove_all_conditional_comments'])) {
            $html = self::removeAllConditionalComments($html);
        } elseif (!empty($options['only_not_mso'])) {
            $html = self::removeNotMsoConditionalComments($html);
        }

        $html = self::stripHtmlNamespaces($html);
        $html = self::removeWordGeneratorMeta($html);
        $html = self::removeNamespacedTags($html);          // o:, v:, w:, m:
        $html = !empty($options['remove_entire_style_block'])
            ? self::removeAllStyleBlocks($html, !empty($options['first_style_block_only']))
            : self::cleanStyleBlocks($html, !empty($options['first_style_block_only']));
        $html = self::removeMsoInlineStyles($html);
        $html = self::normalizeMsoClasses($html);
        $html = self::cleanBodyAttributes($html);
        $html = self::removeEmptyAttributes($html);
        $html = self::removeAllNbsp($html);

        return $html;
    }

    private static function removeAllNbsp($html)
    {
        // &nbsp;
        $html = str_replace('Â&nbsp;', '', $html);
        $html = str_replace('&nbsp;', '', $html);

        return $html;
    }

    private static function removeAllConditionalComments($html)
    {
        return preg_replace('/<!--\[if.*?\]>.*?<!\[endif\]-->/is', '', $html);
    }

    private static function removeNotMsoConditionalComments($html)
    {
        return preg_replace('/<!--\[if\s+!mso\]>.*?<!\[endif\]-->/is', '', $html);
    }

    private static function stripHtmlNamespaces($html)
    {
        return preg_replace('/<html\b[^>]*>/i', '<html>', $html, 1);
    }

    private static function removeWordGeneratorMeta($html)
    {
        return preg_replace('/\s*<meta[^>]+name=["\']Generator["\'][^>]*>\s*/i', '', $html);
    }

    private static function removeNamespacedTags($html)
    {
        // Verwijdert tags met een namespace prefix, bijv. <o:p>, <v:rect>, </w:something>
        return preg_replace('/<\/?\w+:\w+[^>]*>/i', '', $html);
    }

    private static function removeAllStyleBlocks($html, $firstOnly)
    {
        if ($firstOnly) {
            return preg_replace('/<style\b[^>]*>.*?<\/style>/is', '', $html, 1);
        }
        return preg_replace('/<style\b[^>]*>.*?<\/style>/is', '', $html);
    }

    private static function cleanStyleBlocks($html, $firstOnly)
    {
        $limit = $firstOnly ? 1 : -1;
        return preg_replace_callback('/<style\b[^>]*>.*?<\/style>/is', function ($m)
        {
            $css = $m[0];
            $css = preg_replace('/@font-face\s*{[^}]*}\s*/i', '', $css);
            $css = preg_replace('/@page\s*[^}]*}\s*/i', '', $css);
            $css = preg_replace('/mso-[a-z-]+\s*:[^;}"\']+;?/i', '', $css);
            $css = preg_replace('/;\s*;/', ';', $css);
            return $css;
        }, $html, $limit);
    }

    public  function _cleanStyleBlockCb($m)
    {
        $css = $m[0];
        $css = preg_replace('/@font-face\s*{[^}]*}\s*/i', '', $css);
        $css = preg_replace('/@page\s*[^}]*}\s*/i', '', $css);
        $css = preg_replace('/mso-[a-z-]+\s*:[^;}"\']+;?/i', '', $css);
        $css = preg_replace('/;\s*;/', ';', $css);
        return $css;
    }

    private static function removeMsoInlineStyles($html)
    {
        return preg_replace_callback('/style="[^"]*"/i', function ($m)
        {
            $style = $m[0];
            $style = preg_replace('/mso-[a-z-]+\s*:[^;"]*;?/i', '', $style);
            $style = preg_replace('/;\s*;/', ';', $style);
            $style = preg_replace('/style="\s*;?\s*"/i', '', $style); // leeg style=""
            return $style;
        }, $html);
    }

    private static function _removeMsoInlineStylesCb($m)
    {
        $style = $m[0];
        $style = preg_replace('/mso-[a-z-]+\s*:[^;"]*;?/i', '', $style);
        $style = preg_replace('/;\s*;/', ';', $style);
        $style = preg_replace('/style="\s*;?\s*"/i', '', $style); // leeg style=""
        return $style;
    }

    private static function normalizeMsoClasses($html)
    {
        // p.MsoNormal -> p zonder class
        $html = preg_replace('/<p\b([^>]*)\bclass="[^"]*\bMsoNormal\b[^"]*"([^>]*)>/i', '<p$1$2>', $html);
        // overige Mso* classes weg
        $html = preg_replace('/\sclass="[^"]*\bMso\w+\b[^"]*"/i', '', $html);
        return $html;
    }

    private static function cleanBodyAttributes($html)
    {
        return preg_replace_callback('/<body\b([^>]*)>/i', function ($m)
        {
            $attrs = $m[1];
            $attrs = preg_replace('/\s(?:lang|link|vlink)="[^"]*"/i', '', $attrs);
            $attrs = preg_replace_callback('/\sstyle="[^"]*"/i', function ($n)
            {
                $sv = $n[0];
                $sv = preg_replace('/word-wrap\s*:\s*break-word;?/i', '', $sv);
                $sv = preg_replace('/style="\s*;?\s*"/i', '', $sv); // leeg style=""
                return $sv;
            }, $attrs);
            $attrs = preg_replace('/\s{2,}/', ' ', trim($attrs));
            return '<body' . ($attrs ? ' ' . $attrs : '') . '>';
        }, $html);
    }

    public static function _cleanBodyAttributesCb($m)
    {
        $attrs = $m[1];
        $attrs = preg_replace('/\s(?:lang|link|vlink)="[^"]*"/i', '', $attrs);
        $attrs = preg_replace_callback('/\sstyle="[^"]*"/i', function ($n)
        {
            $sv = $n[0];
            $sv = preg_replace('/word-wrap\s*:\s*break-word;?/i', '', $sv);
            $sv = preg_replace('/style="\s*;?\s*"/i', '', $sv); // leeg style=""
            return $sv;
        }, $attrs);
        $attrs = preg_replace('/\s{2,}/', ' ', trim($attrs));
        return '<body' . ($attrs ? ' ' . $attrs : '') . '>';
    }

    public static function _cleanBodyStyleCb($n)
    {
        $sv = $n[0];
        $sv = preg_replace('/word-wrap\s*:\s*break-word;?/i', '', $sv);
        $sv = preg_replace('/style="\s*;?\s*"/i', '', $sv); // leeg style=""
        return $sv;
    }

    public static function removeEmptyAttributes($html)
    {
        return preg_replace('/\s(?:class|style)="\s*"/i', '', $html);
    }
}
