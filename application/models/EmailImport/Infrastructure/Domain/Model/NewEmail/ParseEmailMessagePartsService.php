<?php

namespace EmailImport\Infrastructure\Domain\Model\NewEmail;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use RecursiveIteratorIterator;

class ParseEmailMessagePartsService
{
    private $cleanEmailMessageContentService;

    public function __construct(CleanEmailMessageContentService $cleanEmailMessageContentService)
    {
        $this->cleanEmailMessageContentService = $cleanEmailMessageContentService;
        $this->htmlCleanEmailMessageContentService = (new HtmlCleanEmailMessageContentService());
        require_once 'library/EmailQueue.php';
    }

    public function execute(\Zend_Mail_Message $emailMessage, EmailAccount $emailAccount)
    {
        $emailMessageData = [];
        if ($emailMessage->isMultipart()) {
            foreach (new RecursiveIteratorIterator($emailMessage) as $part) {
                unset($attachment);

                $headers = array_change_key_case((array)$part->getHeaders());

                try {
                    $part_content_type = strtolower($part->contentType);
                } catch (\Exception $e) {
                    $contentTypeString = 'Content-Type: ';
                    $microsoftHeader = $headers['x-microsoft-antispam-message-info'];
                    $position = strpos($microsoftHeader, $contentTypeString);

                    if ($position !== false) {
                        $part_content_type = substr($microsoftHeader, $position + strlen($contentTypeString));
                    } else {
                        $part_content_type = 'text/html; charset="us-ascii"';
                    }

                    $headers[trim($contentTypeString)] = $part_content_type;

                    error_log("[email_import_content_type_exception] Voor dit account: '{$emailAccount->getName()}', "
                        . "deze contentType gebruikt: {$part_content_type}, "
                        . "met deze message: '{$e->getMessage()}'. Stacktrace: {$e->getTraceAsString()}");
                }

                if (array_key_exists('content-disposition', $headers ) && stripos($headers['content-disposition'], 'filename*0=')) {
                    $headers['content-disposition'] = $this->handleTooLongFilenameCase($headers['content-disposition']);
                }

                if ((array_key_exists('content-disposition', $headers) && strpos($headers['content-disposition'],
                            'filename=') !== false && !array_key_exists('content-location',
                            $headers)) || (array_key_exists('content-type',
                            $headers) && stripos($headers['content-type'],
                            'name=') !== false && !array_key_exists('content-location', $headers))) {
                    if (strpos($headers['content-disposition'], 'filename=') !== false) {
                        $start = strpos($headers['content-disposition'], 'filename=') + strlen('filename=');
                        $filename = substr($headers['content-disposition'], $start);
                    } else {
                        $start = stripos($headers['content-type'], 'name=') + strlen('name=');
                        $filename = substr($headers['content-type'], $start);
                    }

                    if (strpos($filename, ';') !== false) {
                        $filename = substr($filename, 0, strpos($filename, ';'));
                    }

                    if (strtolower($headers['content-transfer-encoding']) === 'base64') {
                        $data = base64_decode($part->getContent());
                    } else {
                        $data = quoted_printable_decode($part->getContent());
                    }

                    $reference = false;
                    foreach (['x-attachment-id', 'content-id'] as $reference_field) {
                        if (array_key_exists($reference_field, $headers)) {
                            if ($headers[$reference_field] && $headers[$reference_field] !== '') {
                                $reference = str_replace(['<', '>'], '', $headers[$reference_field]);
                            }
                        }
                    }

                    $attachment = [
                        'filename' => str_replace('"', '', $filename),
                        'data' => $data
                    ];

                    if ($reference && stripos($part_content_type, '.pdf') === false) {
                        $attachment['inline-reference'] = $reference;
                    }

                    $emailMessageData['attachments'][] = $attachment;
                    unset($attachment);
                } elseif ((array_key_exists('x-attachment-id', $headers) || array_key_exists('content-id',
                            $headers)) && strpos($part_content_type,
                        'text/html') === false && !array_key_exists('x-microsoft-exchange-diagnostics', $headers)) {
                    $reference = false;
                    foreach (['x-attachment-id', 'content-id'] as $reference_field) {
                        if (array_key_exists($reference_field, $headers)) {
                            if ($headers[$reference_field] && $headers[$reference_field] !== '') {
                                $reference = str_replace(['<', '>'], '', $headers[$reference_field]);
                            }
                        }
                    }

                    if (!$reference) {
                        continue;
                    }

                    if (strtolower($headers['content-transfer-encoding']) === 'base64') {
                        $data = base64_decode($part->getContent());
                    } else {
                        $data = quoted_printable_decode($part->getContent());
                    }

                    $emailMessageData['attachments'][] = [
                        'filename' => $reference,
                        'data' => $data,
                        'inline-reference' => $reference
                    ];
                } elseif (strpos($part_content_type, 'text/html') !== false) {
                    $encoding = $this->getEncoding($part);
                    $content = $part->getContent();
                    $content = $this->decode($content, $encoding['encoding'], $encoding['charset']);

                    $newHtml =  $this->htmlCleanEmailMessageContentService->clean($content);
                    $content = $newHtml;


                    /**
                     * this wel be remove later !
                     *
                     */
                    libxml_use_internal_errors(true);
                    $doc = new \DOMDocument();
                    $result = $doc->loadHTML($newHtml);
                    $errors = libxml_get_errors();
                    libxml_clear_errors();

                    if ($result) {
                        echo "HTML is parseable.\n";
                    } else {
                        echo "HTML failed to parse.\n";
                    }
                    if ($errors) {

                        $unusableEmailError = [];
                        echo "Warnings/Errors:\n";
                        foreach ($errors as $error) {

                            switch (true) {
                                case strpos($error->message, 'Comment not terminated') !== false:
                                case strpos($error->message, 'Unexpected end of file') !== false:

                                    $unusableEmailError[] = $error->message;

                                    break;
                            }
                        }

                        if (count($unusableEmailError)) {

                            // use the old way
                            $content = $this->cleanEmailMessageContentService->clean($content, 'html');

                            global $config;
                            $msgId = $emailMessage->getHeader('message-id');
                            $subject = 'Email import error '. $config->app->server .' message-id: '.$msgId;



                            new \EmailSendDirect(
                                ['address' => '<EMAIL>'],
                                ['email' => '<EMAIL>'],
                                $subject,
                                implode(' ', [
                                    'Bij het importeren van de email is er iets mis gegaan waar een techniek medewerker naar moet kijken.'. "\n",
                                    $subject ."\n",
                                    'https://'.$config->app->server."\n",
                                    "\n",
                                    'Email: '. "\n",
                                    'Subject '. $emailMessage->getHeader('subject') ."\n",
                                    'From'. $emailMessage->getHeader('from') ."\n",
                                    'Date'. $emailMessage->getHeader('date') ."\n",
                                    "\n",
                                    'Errors: '. "\n",
                                    json_encode($unusableEmailError),
                                ]),
                                false
                            );
                        }
                    }



                    $emailMessageData['text/html']['content'] .= $content;
                } elseif (strpos($part_content_type, 'text/plain') !== false) {
                    $encoding = $this->getEncoding($part);
                    $content = $part->getContent();
                    $content = $this->decode($content, $encoding['encoding'], $encoding['charset']);
                    $content = $this->cleanEmailMessageContentService->clean($content, 'plain');

                    $content = nl2br($content);
                    $emailMessageData['text/plain']['content'] .= $content;
                } elseif (stripos($part_content_type, 'message/rfc822') !== false) {
                    // Email as attachment case

                    try {
                        $emailMessageAsAttachment = new \Zend_Mail_Message(['raw' => $part->getContent()]);

                        $filename = $emailMessageAsAttachment->headerExists('subject') ? $emailMessageAsAttachment->getHeader('subject') : uniqid('rfc822 message',
                            false);
                        $filename .= '.eml';

                        $emailMessageData['attachments'][] = [
                            'filename' => $filename,
                            'data' => $part->getContent(),
                            'inline-reference' => $filename
                        ];
                    } catch(\Exception $exception) {
                        error_log("ParseEmailMessagePartsService:  ". $exception->getMessage());
                    }
                }
            }
        } else {
            /**
             * NOT MULTIPART CASE
             */
            $encoding = $this->getEncoding($emailMessage);
            $emailMessageData['original'] = $emailMessage->getContent();

            if ($emailMessage->headerExists('content-type')) {
                $contentType = $emailMessage->getHeaderField('content-type');
            } else {
                $contentType = false;
            }

            $contentType = strpos($contentType, 'html') !== false ? 'html' : 'plain';
        }

        // Prefer HTML over Plain text
        if (isset($emailMessageData['text/html']['content'])) {
            $emailMessageData['content'] = $emailMessageData['text/html']['content'];
        } elseif (isset($emailMessageData['text/plain']['content'])) {
            $emailMessageData['content'] = $emailMessageData['text/plain']['content'];
        } else {
            $emailMessageData['content'] = $this->decode(
                $emailMessageData['original'],
                $encoding['encoding'],
                $encoding['charset']
            );

            if ($contentType === 'html') {

                $emailMessageData['content'] =  $this->htmlCleanEmailMessageContentService->clean($emailMessageData['content']);

            }else if ($contentType === 'plain') {
                $emailMessageData['content'] = $this->cleanEmailMessageContentService->clean(
                    $emailMessageData['content'],
                    $contentType
                );
                $emailMessageData['content'] = nl2br($emailMessageData['content']);
            } else {
                $emailMessageData['content'] = $this->cleanEmailMessageContentService->clean(
                    $emailMessageData['content'],
                    $contentType
                );
                $emailMessageData['content'] = nl2br($emailMessageData['content']);
            }
        }

        // The next lines make sure no attachments are marked as having an inline reference while not actually
        // having one. Some email clients will add 'x-attachment-id'/etc headers to attachments that are not
        // actually referenced inline, meaning these can't be used to determine if an attachment should be
        // listed seperately.
        if (!empty($emailMessageData['content'])) {
            if (is_array($emailMessageData['attachments'])) {
                foreach ($emailMessageData['attachments'] as &$attachment) {
                    if (!empty($attachment['inline-reference'])) {
                        if (strpos($emailMessageData['content'], 'src="cid:' . $attachment['inline-reference'] . '"') === false) {
                            unset($attachment['inline-reference']);
                        }
                    }
                }
            }
        }

        return $emailMessageData;
    }

    private function getEncoding(\Zend_Mail_Part $part)
    {
        if ($part->headerExists('content-transfer-encoding')) {
            $encoding = $part->getHeaderField('content-transfer-encoding');
        } else {
            false;
        }

        if ($part->headerExists('content-type') && strpos($part->getHeaderField('content-type'), 'charset')) {
            $charset = $part->getHeaderField('content-type', 'charset');
        } else {
            if ($part->headerExists('content-type') && strpos($part->getHeader('content-type'), 'charset')) {
                $charset = $part->getHeaderField('content-type', 'charset');
            } else {
                $charset = false;
            }
        }

        return compact('encoding', 'charset');
    }

    private function decode($content, $encoding, $charset)
    {
        switch (strtolower($encoding)) {
            case 'base64':
                $content = base64_decode($content);
                break;
            case 'quoted-printable':
                $content = quoted_printable_decode($content);
                break;
        }

        if (trim($charset) !== '' && mb_check_encoding($content, $charset)) {
            $content = iconv($charset, 'UTF-8', $content);
        } else {
            $content = mb_convert_encoding($content, 'UTF-8');
        }

        return $content;
    }

    private function handleTooLongFilenameCase($contentDisposition)
    {
        $headerContentDisposition = explode(";", $contentDisposition);

        $filenamePart = [];
        foreach ($headerContentDisposition as $key => $headerPart) {
            if (stripos($headerPart, 'filename*')) {
                $headerPart = substr($headerPart, strpos($headerPart, '=') + 1);
                $headerPart = substr($headerPart, 1, -1);
                $filenamePart[] = $headerPart;
                unset($headerContentDisposition[$key]);
            }
        }

        $filename = implode($filenamePart);
        $headerContentDisposition[] = 'filename="' . $filename . '"';

        return implode('; ', $headerContentDisposition);
    }
}
