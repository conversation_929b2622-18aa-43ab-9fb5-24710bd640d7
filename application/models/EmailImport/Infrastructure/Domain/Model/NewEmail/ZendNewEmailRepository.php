<?php

namespace EmailImport\Infrastructure\Domain\Model\NewEmail;

use EmailAttachment;
use EmailImport\Domain\Model\NewEmail\NewEmail;
use EmailImport\Domain\Model\NewEmail\NewEmailRepository;
use Emails;
use EmailUser;

class ZendNewEmailRepository implements NewEmailRepository
{
    /**
     * @param NewEmail $newEmail
     * @return int $emailRowId
     */
    public function add(NewEmail $newEmail)
    {
        $emailRowId = $this->saveEmail($newEmail);
        $this->saveEmailUsers($newEmail, $emailRowId);
        $this->saveAttachments($newEmail, $emailRowId);

        return $emailRowId;
    }

    private function saveEmail(NewEmail $newEmail)
    {
        $emails = new Emails();
        $emailRow = $emails->createRow();
        $emailRow->account = $newEmail->getEmailAccountId();
        $emailRow->direction = 'INCOMING';
        $emailRow->date = $newEmail->getSentDate()->format('Y-m-d H:i:s');
        $emailRow->extension_date = $newEmail->getSentDate()->format('Y-m-d H:i:s');
        $emailRow->subject = $newEmail->getSubject();
        $emailRow->message = $newEmail->getContent();
        $emailRow->message_id = $newEmail->getMessageId();

        return $emailRow->save();
    }

    private function saveEmailUsers(NewEmail $newEmail, $emailRowId)
    {
        $emailUser = new EmailUser('from', $newEmail->getFrom());
        $emailUser->save($emailRowId);

        if ($emailUser->isAccount() && $emailUser->isSystem) {
            $this->setSystem($emailRowId);
        }

        $this->saveToRecipients($newEmail, $emailRowId);
        $this->saveCCRecipients($newEmail, $emailRowId);
        $this->saveBccRecipients($newEmail, $emailRowId);
    }

    private function setSystem($emailRowId)
    {
        $emailRow = (new Emails())->fetchRowById($emailRowId);

        if (null === $emailRow) {
            throw new \RuntimeException('Could not find email row');
        }

        $emailRow->system = true;
        $emailRow->save();
    }

    private function saveToRecipients(NewEmail $newEmail, $emailRowId)
    {
        foreach ($newEmail->getTo() as $toRecipient) {
            $emailUser = new EmailUser('to', ['email' => $toRecipient]);
            $emailUser->save($emailRowId);
        }
    }

    private function saveCCRecipients(NewEmail $newEmail, $emailRowId)
    {
        foreach ($newEmail->getCc() as $ccRecipient) {
            $emailUser = new EmailUser('cc', ['name' => $ccRecipient['name'], 'email' => $ccRecipient['email']]);
            $emailUser->save($emailRowId);
        }
    }

    private function saveAttachments(NewEmail $newEmail, $emailRowId)
    {
        $emailAttachment = new EmailAttachment($emailRowId);

        if (!empty($newEmail->getAttachments())) {
            foreach ($newEmail->getAttachments() as $attachment) {
                $emailAttachment->add([
                    'file' => $attachment->getFilename(),
                    'data' => $attachment->getContent(),
                    'inline-reference' => $attachment->getInlineReference()
                ]);

                $attachment->setId($emailAttachment->row->id);
            }

            $this->replaceInlineImages($newEmail, $emailRowId);
        }
    }

    private function replaceInlineImages(NewEmail $newEmail, $emailRowId)
    {
        $inlineImages = [];

        if (is_array($newEmail->getAttachments())) {
            foreach ($newEmail->getAttachments() as $attachment) {
                if ($attachment->getInlineReference()) {
                    $inlineImages[$attachment->getInlineReference()] = $attachment;
                }
            }
        }

        $emailRow = (new Emails())->fetchRowById($emailRowId);

        if (null === $emailRow) {
            throw new \RuntimeException('Could not find email row');
        }

        foreach ($inlineImages as $reference => $attachment) {
            $src = 'src="cid:' . $reference . '"';

            if (strpos($emailRow->message, $src) !== false) {
                $emailRow->message = str_replace(
                    $src,
                    'src="email/get-attachment/id/' . $attachment->getId() . '/"',
                    $emailRow->message
                );
            }

            $emailRow->save();
        }
    }

    private function saveBccRecipients(NewEmail $newEmail, $emailRowId)
    {
        if (null !== $newEmail->getBcc()) {
            return;
        }

        $emailUser = new EmailUser('bcc', $newEmail->getBcc());
        $emailUser->save($emailRowId);
    }
}
