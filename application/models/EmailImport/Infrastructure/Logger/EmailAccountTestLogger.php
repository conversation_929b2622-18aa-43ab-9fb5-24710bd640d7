<?php

namespace EmailImport\Infrastructure\Logger;

class EmailAccountTestLogger
{
    private $isLoggingEnabled = false;
    private $lines = [];

    /**
     * @var EmailAccountTestLogger
     */
    private static $instance;

    public static function instance()
    {
        if (null === static::$instance) {
            static::$instance = new self();
        }
        return static::$instance;
    }

    private function __construct()
    {

    }

    public function __clone()
    {
        throw new \BadMethodCallException('Clone is not supported');
    }

    public function enableLogging()
    {
        $this->isLoggingEnabled = true;
    }

    public function disableLogging()
    {
        $this->isLoggingEnabled = false;
    }

    public function isLoggingEnabled()
    {
        return $this->isLoggingEnabled;
    }

    public function logLine($line)
    {
        $this->lines[] = $line;
    }

    public function getFormattedLogLines()
    {
        $formattedLines = "\r\n";
        foreach ($this->lines as $line) {
            $formattedLines .= "- " . $line;
        }

        return $formattedLines;
    }
}
