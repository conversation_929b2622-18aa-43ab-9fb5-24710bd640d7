<?php

namespace EmailImport\Application\Service;

class AddLabelsToNewEmailService
{
    public function execute(AddLabelsToNewEmailRequest $request)
    {
        if (\Settings::get('general_company_shortname') !== 'MVGM') {
            return;
        }

        $emailRow = $this->findEmailRowOrFail($request->getEmailRowId());

        if (stripos($emailRow->subject, 'funda') !== false) {
            $emailRow->label_id = 1; // Funda
            $emailRow->save();
        }
    }

    /**
     * @param $emailRowId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findEmailRowOrFail($emailRowId)
    {
        $emailRow = (new \Emails())->fetchRowById($emailRowId);

        if (null === $emailRow) {
            throw new \RuntimeException('Could not find email row');
        }

        return $emailRow;
    }
}
