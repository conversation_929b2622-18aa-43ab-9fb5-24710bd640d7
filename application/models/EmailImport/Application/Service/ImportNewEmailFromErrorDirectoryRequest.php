<?php

namespace EmailImport\Application\Service;

class ImportNewEmailFromErrorDirectoryRequest
{
    private $emailAccountId;
    private $runServiceTillDate;
    private $importMailFromDate;
    private $importMailTillDate;

    public function __construct(
        $emailAccountId,
        \DateTime $runServiceTillDate,
        \DateTime $importMailFromDate,
        \DateTime $importMailTillDate
    ) {
        $this->emailAccountId = $emailAccountId;
        $this->runServiceTillDate = $runServiceTillDate;
        $this->importMailFromDate = $importMailFromDate;
        $this->importMailTillDate = $importMailTillDate;
    }

    public function getEmailAccountId()
    {
        return $this->emailAccountId;
    }

    public function getRunServiceTillDate()
    {
        return $this->runServiceTillDate;
    }

    public function getImportMailFromDate()
    {
        return $this->importMailFromDate;
    }

    public function getImportMailTillDate()
    {
        return $this->importMailTillDate;
    }
}
