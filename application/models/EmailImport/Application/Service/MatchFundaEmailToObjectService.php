<?php

namespace EmailImport\Application\Service;

class MatchFundaEmailToObjectService
{
    public function execute(MatchFundaEmailToObjectRequest $request)
    {
        if (\Settings::get('general_company_shortname') !== 'MVGM') {
            return;
        }

        $emailRow = $this->findEmailRowOrFail($request->getEmailRowId());

        if (!$this->isFundaBasisHuurEmail($emailRow)) {
            return;
        }

        $productStageLevel = $this->parseProductStageLevel($emailRow->subject);

        switch ($productStageLevel) {
            case 'bestelling':
            case 'geleverd':
                $objectAddress = $this->parseAddressFromMessage($emailRow->message);
                break;
            case 'contract per direct stopgezet':
            case 'contract wordt verlengd':
            case 'contract verlengd':
            case 'contract geannuleerd':
                $objectAddress = $this->parseAddressFromSubject($emailRow->subject);
                break;
            default:
                return;
        }

        if (!$objectAddress) {
            return;
        }

        $objectId = $this->findObjectIdByAddress($objectAddress);

        if (!$objectId) {
            return;
        }

        $emailRow->object_once = $objectId;
        $emailRow->save();
    }

    /**
     * @param $emailRowId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findEmailRowOrFail($emailRowId)
    {
        $emailRow = (new \Emails())->fetchRowById($emailRowId);

        if (null === $emailRow) {
            throw new \RuntimeException('Could not find email row');
        }

        return $emailRow;
    }

    private function isFundaBasisHuurEmail(\Zend_Db_Table_Row_Abstract $emailRow)
    {
        return stripos($emailRow->subject, 'Funda Basis Huur') !== false;
    }

    private function parseProductStageLevel($subject)
    {
        // 6310M-3194777: bestelling 3194777 voor Funda Basis Huur -> adres in het bericht
        if (stripos($subject, 'bestelling') !== false) {
            return 'bestelling';
        }

        // 6310M-3194777: geleverd (Funda Basis Huur - 3194777). -> adres in het bericht
        if (stripos($subject, 'geleverd') !== false) {
            return 'geleverd';
        }

        // 6310M-3005687: contract per direct stopgezet (Steenstraat 77, Veendam - Funda Basis Huur)
        if (stripos($subject, 'contract per direct stopgezet') !== false) {
            return 'contract per direct stopgezet';
        }

        // 6310M-3028633: contract wordt verlengd (K R Poststraat 8 -2, Heerenveen - Funda Basis Huur)
        if (stripos($subject, 'contract wordt verlengd') !== false) {
            return 'contract wordt verlengd';
        }

        // 6310M-2961921: contract verlengd (Vismarkt 16 D, Groningen - Funda Basis Huur)
        if (stripos($subject, 'contract verlengd') !== false) {
            return 'contract verlengd';
        }

        // 6310M-3228417: contract geannuleerd (Hunsingolaan 6, Stadskanaal - Funda Basis Huur)
        if (stripos($subject, 'contract geannuleerd') !== false) {
            return 'contract geannuleerd';
        }

        return 'could not parse product stage';
    }

    private function parseAddressFromMessage($message)
    {
        $textLines = explode("\n", $message);

        $addressKeyIndex = null;
        foreach ($textLines as $key => $textLine) {
            if (strpos($textLine, 'Product:') === false) {
                continue;
            }

            $addressKeyIndex = $key + 1;
            break;
        }

        if (!$addressKeyIndex) {
            return false;
        }

        $addressLine = $textLines[$addressKeyIndex];

        $positionA = strpos($addressLine, 'Object:');
        if ($positionA !== false) {
            $positionA += strlen('Object:');
            $addressLine = substr($addressLine, $positionA);
        }

        $addressLine = trim($addressLine);

        return $this->makeAddressOfAddressLine($addressLine);
    }

    private function parseAddressFromSubject($subject)
    {
        $positionA = strpos($subject, '(');
        $positionB = strpos($subject, ' - Funda');

        if (!$positionA || !$positionB) {
            return false;
        }

        $addressLine = substr($subject, ($positionA + 1), ($positionB - $positionA));

        if (!$addressLine) {
            return false;
        }

        $addressLine = trim($addressLine);

        return $this->makeAddressOfAddressLine($addressLine);
    }

    private function makeAddressOfAddressLine($addressLine)
    {
        $street = $this->parseStreet($addressLine);
        $houseNumberAndAddition = $this->parseHouseNumberAndAddition($addressLine);
        $city = $this->parseCity($addressLine);

        if (!$street || !$houseNumberAndAddition || !$city) {
            return false;
        }

        return [
            'street' => $street,
            'house_number_and_addition' => $houseNumberAndAddition,
            'city' => $city,
        ];
    }

    private function parseStreet($addressLine)
    {
        preg_match('/^\D*(?=\d)/', $addressLine, $matches);
        $positionOfFirstNumber = isset($matches[0]) ? strlen($matches[0]) : false;

        if (!$positionOfFirstNumber) {
            return false;
        }

        $street = substr($addressLine, 0, $positionOfFirstNumber);
        return trim($street);
    }

    private function parseHouseNumberAndAddition($addressLine)
    {
        // 6310M-3028633: contract wordt verlengd (K R Poststraat 8 -2, Heerenveen - Funda Basis Huur)
        preg_match('/^\D*(?=\d)/', $addressLine, $matches);
        $positionOfFirstNumber = isset($matches[0]) ? strlen($matches[0]) : false;

        if (!$positionOfFirstNumber) {
            return false;
        }

        $positionB = strpos($addressLine, ',');

        if (!$positionB) {
            return false;
        }

        return substr($addressLine, $positionOfFirstNumber, ($positionB - $positionOfFirstNumber));
    }

    private function parseCity($addressLine)
    {
        $positionA = strpos($addressLine, ',');
        $city = substr($addressLine, ($positionA + 1));

        if (!$city) {
            return false;
        }

        return trim($city);
    }

    private function findObjectIdByAddress(array $objectAddress)
    {
        $objectData = db()->select()
            ->from(['o' => 'objects'], ['id'])
            ->joinLeft(['a' => 'address'], 'a.type_id = o.id', false)
            ->where('a.type = ?', 'object')
            ->where('a.city = ?', $objectAddress['city'])
            ->where('a.number = ?', $objectAddress['house_number_and_addition'])
            ->where('a.address = ?', $objectAddress['street'])
            ->query()
            ->fetch();

        return $objectData ? $objectData['id'] : false;
    }
}
