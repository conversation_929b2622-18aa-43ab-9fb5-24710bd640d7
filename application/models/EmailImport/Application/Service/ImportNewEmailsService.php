<?php

namespace EmailImport\Application\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\EmailAccount\EmailAccountNotFoundException;
use EmailImport\Domain\Model\MailBoxDirectories\MailBoxDirectoriesFactory;
use EmailImport\Domain\Model\NewEmail\FromImportDatePolicyException;
use EmailImport\Domain\Model\NewEmail\LegacyCutOffDatePolicyException;
use EmailImport\Domain\Model\NewEmail\NewEmailAlreadyExistException;
use EmailImport\Domain\Model\NewEmail\NewEmailFactory;
use EmailImport\Domain\Service\ImportNewEmailService;
use EmailImport\Domain\Service\ImportNewEmailsLogger;
use EmailImport\Domain\Service\MailStorageConnection;
use EmailImport\Infrastructure\Domain\Model\EmailAccount\ZendEmailAccountRepository;
use Zend_Db_Statement_Exception;

class ImportNewEmailsService
{
    const IMPORT_LIMIT = 100;

    private $emailAccountRepository;
    private $mailStorageConnection;
    private $mailBoxDirectoriesFactory;
    private $newEmailFactory;
    private $importNewEmailService;
    private $importedEmailMessageCount = 0;
    private $importLimitExceeded;
    private $importNewEmailsLogger;

    public function __construct(
        ZendEmailAccountRepository $emailAccountRepository,
        MailStorageConnection $mailStorageConnection,
        MailBoxDirectoriesFactory $mailBoxDirectoriesFactory,
        NewEmailFactory $newEmailFactory,
        ImportNewEmailService $importNewEmailService,
        ImportNewEmailsLogger $importNewEmailsLogger
    ) {
        $this->emailAccountRepository = $emailAccountRepository;
        $this->mailStorageConnection = $mailStorageConnection;
        $this->mailBoxDirectoriesFactory = $mailBoxDirectoriesFactory;
        $this->newEmailFactory = $newEmailFactory;
        $this->importNewEmailService = $importNewEmailService;
        $this->importNewEmailsLogger = $importNewEmailsLogger;
        $this->importLimitExceeded = false;
    }

    public function execute(ImportNewEmailsRequest $request)
    {
        $emailAccount = $this->findEmailAccountOrFail($request->getEmailAccountId());
        $this->mailStorageConnection->makeConnection($emailAccount);
        \Logger::add(['email', 'import'], 'Start import mail for :'. $emailAccount->getId() .' '. $emailAccount->getName());
        $mailBoxDirectories = $this->mailBoxDirectoriesFactory->build($emailAccount->getId());

        try {
            if (!$this->mailStorageConnection->doesMailStorageDirExist($mailBoxDirectories->getImportedDir())) {
                $this->mailStorageConnection->createFolder($mailBoxDirectories->getImportedDir());
            }

            if (!$this->mailStorageConnection->doesMailStorageDirExist($mailBoxDirectories->getErrorDir())) {
                $this->mailStorageConnection->createFolder($mailBoxDirectories->getErrorDir());
            }

            if (!$this->mailStorageConnection->doesMailStorageDirExist($mailBoxDirectories->getSkippedDir())) {
                $this->mailStorageConnection->createFolder($mailBoxDirectories->getSkippedDir());
            }
        } catch (\Exception $e) {
            $this->importNewEmailsLogger->logIncompatibleMailServer($e, $emailAccount);
            throw $e; // very important!
        }

        $emailMessagesIterator = $this->mailStorageConnection->getEmailMessagesIterator();

        while ($emailMessagesIterator->hasNextEmailMessage()) {
            $emailMessage = $emailMessagesIterator->getNextEmailMessage();
            $emailMessageId = $emailMessagesIterator->getCurrentEmailMessageId();

            try {
                $newEmail = $this->newEmailFactory->build($emailMessage, $emailAccount);
                $this->importNewEmailService->import($newEmail, $emailAccount);
            } catch (LegacyCutOffDatePolicyException $e) {
                $this->mailStorageConnection->moveMessageToDirectory(
                    $emailMessageId,
                    $mailBoxDirectories->getSkippedDir()
                );
                continue;
            } catch (FromImportDatePolicyException $e) {
                $this->mailStorageConnection->moveMessageToDirectory(
                    $emailMessageId,
                    $mailBoxDirectories->getSkippedDir()
                );
                continue;
            } catch (NewEmailAlreadyExistException $e) {
                $this->importNewEmailsLogger->logNewEmailAlreadyExist($e, $emailAccount, $emailMessage);
                $this->mailStorageConnection->moveMessageToDirectory(
                    $emailMessageId,
                    $mailBoxDirectories->getErrorDir()
                );
                continue;
            } catch (\Exception $e) {
                $this->importNewEmailsLogger->logCouldNotImportNewEmailMessage($e, $emailAccount, $emailMessage);
                $this->mailStorageConnection->moveMessageToDirectory(
                    $emailMessageId,
                    $mailBoxDirectories->getErrorDir()
                );
                continue;
            }

            if (\Settings::get('email_import_mark_as_imported_by_deleting')) {
                $this->mailStorageConnection->deleteMessage($emailMessageId);
            } else {
                $this->mailStorageConnection->moveMessageToDirectory(
                    $emailMessageId,
                    $mailBoxDirectories->getImportedDir()
                );
            }

            ++$this->importedEmailMessageCount;

            if ($this->importedEmailMessageCount >= self::IMPORT_LIMIT) {
                $this->importLimitExceeded = true;
                break;
            }
        }

        $this->clearPreviousErrors($emailAccount->getId());

        return $this->importLimitExceeded;
    }

    /**
     * @param $emailAccountId
     * @return EmailAccount
     * @throws EmailAccountNotFoundException
     * @throws Zend_Db_Statement_Exception
     */
    private function findEmailAccountOrFail($emailAccountId)
    {
        $emailAccount = $this->emailAccountRepository->ofId($emailAccountId);

        if (null === $emailAccount) {
            throw new EmailAccountNotFoundException();
        }

        return $emailAccount;
    }

    private function clearPreviousErrors($emailAccountId)
    {
        $emailAccountRow = (new \EmailAccounts())->fetchRowById($emailAccountId);

        if (null === $emailAccountRow) {
            throw new \RuntimeException('Could not find email account row by id: ' . $emailAccountId);
        }

        $emailAccountRow->error = '';
        $emailAccountRow->trace = '';
        $emailAccountRow->save();
    }
}
