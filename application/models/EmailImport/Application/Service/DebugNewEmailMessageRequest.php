<?php

namespace EmailImport\Application\Service;

class DebugNewEmailMessageRequest
{
    private $emailAccountId;
    private $messageId;
    private $dateString;
    private $subject;

    public function __construct($emailAccountId, $messageId, $dateString, $subject)
    {
        $this->emailAccountId = $emailAccountId;
        $this->messageId = $messageId;
        $this->dateString = $dateString;
        $this->subject = $subject;
    }

    public function getEmailAccountId()
    {
        return $this->emailAccountId;
    }

    public function getMessageId()
    {
        return $this->messageId;
    }

    public function getDateString()
    {
        return $this->dateString;
    }

    public function getSubject()
    {
        return $this->subject;
    }
}
