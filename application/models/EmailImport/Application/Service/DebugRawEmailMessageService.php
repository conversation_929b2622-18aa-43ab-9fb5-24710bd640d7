<?php

namespace EmailImport\Application\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Infrastructure\Domain\Model\NewEmail\CleanEmailMessageContentService;
use EmailImport\Infrastructure\Domain\Model\NewEmail\LegacyNewEmailFactory;
use EmailImport\Infrastructure\Domain\Model\NewEmail\ParseEmailMessagePartsService;
use EmailImport\Infrastructure\Domain\Service\FindHeaderService;

class DebugRawEmailMessageService
{
    private $emailFactory;

    public function __construct()
    {
        $this->emailFactory = $this->buildEmailFactory();
    }

    public function debug($rawEmail)
    {
        $params['raw'] = $rawEmail;
        $emailMessage = new \Zend_Mail_Message($params);
        $emailAccount = $this->makeDummyEmailAccount();
        $email = $this->emailFactory->build($emailMessage, $emailAccount);

        echo $email->getContent();
    }

    private function buildEmailFactory()
    {
        $cleanEmailMessageContentService = new CleanEmailMessageContentService();

        return new LegacyNewEmailFactory(
            new ParseEmailMessagePartsService($cleanEmailMessageContentService),
            new FindHeaderService()
        );
    }

    private function makeDummyEmailAccount()
    {
        return new EmailAccount(
            1,
            'name',
            'emailAddress',
            'username',
            'password',
            'host',
            'ssl'
        );
    }
}
