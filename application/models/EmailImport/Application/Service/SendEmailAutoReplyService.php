<?php

namespace EmailImport\Application\Service;

use EmailSendDirect;
use EmailView;

class SendEmailAutoReplyService
{
    public function execute(SendEmailAutoReplyRequest $request)
    {
        try {
            require_once 'library/EmailQueue.php';

            if (!$request->getEmailRowId()) {
                throw new \Exception('no email row id is set!');
            }

            $emailView = new EmailView(['id' => $request->getEmailRowId()]);
            $item = $emailView->getItem();

            $emailAccount = new \EmailAccount(['id' => $item['account']]);

            if (!$item['from']['email'] || !$emailAccount->isAccount()) {
                return;
            }

            if ($emailAccount->selected['auto_reply'] == null && empty($emailAccount->selected['auto_reply'])) {
                return;
            }

            $subject = 'E-mail ontvangen';

            if (strpos($item['subject'], $subject)) {
                return;
            }

            if ($this->countEmail2Day($item['to'][0]['email'], $item['from']['email']) > 5) {
                error_log_debug_backtrace('too many auto reply today for email '.
                    $request->getEmailRowId() . ' # '. $item['from']['email'] . ' to' . $item['to']['email']);
                return;
            }

            new EmailSendDirect(
                $emailAccount->get(),
                $item['from'],
                $subject,
                $emailAccount->selected['auto_reply'],
                false
            );
        } catch (\Exception $exception) {
            error_log_debug_backtrace("SendEmailAutoReplyService: ".$exception->getMessage() . " ## " . $exception->getTraceAsString());
        }
    }


    private function countEmail2Day($toEmailAaddress, $fromEmailAaddress)
    {
        $select = db()->select()
            ->from(['e' => 'email'], ['count' => 'COUNT(*)'])
            ->joinLeft(['euFrom' => 'email_users'], 'euFrom.email = e.id AND euFrom.`type` = "from"', null)
            ->joinLeft(['euTo' => 'email_users'], 'euTo.email = e.id AND euTo.`type` = "to"', null)
            ->where('e.date > CURDATE()')
            ->where('euFrom.address = ?', $fromEmailAaddress)
            ->where('euTo.address = ?', $toEmailAaddress)
        ;

        $aantalEmails = db()->fetchOne($select); // Geeft direct het getal terug
        return $aantalEmails;
    }
}
