<?php

namespace EmailImport\Application\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\EmailAccount\EmailAccountNotFoundException;
use EmailImport\Domain\Model\NewEmail\FromImportDatePolicyException;
use EmailImport\Domain\Model\NewEmail\LegacyCutOffDatePolicyException;
use EmailImport\Domain\Model\NewEmail\NewEmailAlreadyExistException;
use EmailImport\Domain\Model\NewEmail\NewEmailFactory;
use EmailImport\Domain\Service\EmailMessageFinderService;
use EmailImport\Domain\Service\ImportNewEmailService;
use EmailImport\Infrastructure\Domain\Model\EmailAccount\ZendEmailAccountRepository;
use EmailImport\Infrastructure\Domain\Service\ZendEmailMessageFinderByMessageIdService;

class FindEmailMessageService
{
    private $emailAccountRepository;
    private $newEmailFactory;
    private $importNewEmailService;
    private $emailMessageFinderService;

    public function __construct(
        ZendEmailAccountRepository $emailAccountRepository,
        NewEmailFactory $newEmailFactory,
        ImportNewEmailService $importNewEmailService,
        ZendEmailMessageFinderByMessageIdService $emailMessageFinderService
    ) {
        $this->emailAccountRepository = $emailAccountRepository;
        $this->newEmailFactory = $newEmailFactory;
        $this->importNewEmailService = $importNewEmailService;
        $this->emailMessageFinderService = $emailMessageFinderService;
    }

    public function execute($emailAccountId, $messageId)
    {


        $emailAccount = $this->findEmailAccountOrFail($emailAccountId);

        $emailMessage = $this->emailMessageFinderService->find(
            $emailAccount,
            $messageId
        );

        if (null === $emailMessage) {
            throw new \RuntimeException('Could not find the email message by the given search parameters');
        }

        try {
            $newEmail = $this->newEmailFactory->build($emailMessage, $emailAccount);
            
            return $newEmail;
        } catch (LegacyCutOffDatePolicyException $e) {
            p('die', $e->getMessage());
        } catch (FromImportDatePolicyException $e) {
            p('die', $e->getMessage());
        } catch (NewEmailAlreadyExistException $e) {
            p('die', $e->getMessage());
        } catch (\Exception $e) {
            p('die', $e->getMessage());
        }
    }

    private function guardAgainstNoSearchParametersProvided($messageId, $dateString, $subject)
    {
        if (!$messageId) {
            if (!$dateString || !$subject) {
                throw new \InvalidArgumentException(
                    'No correct search parameters provided, either the message-id or a combination of date and 
                    subject must be provided.'
                );
            }
        }
    }

    /**
     * @param $emailAccountId
     * @return EmailAccount
     * @throws EmailAccountNotFoundException
     * @throws \Zend_Db_Statement_Exception
     */
    private function findEmailAccountOrFail($emailAccountId)
    {
        $emailAccount = $this->emailAccountRepository->ofId($emailAccountId);

        if (null === $emailAccount) {
            throw new EmailAccountNotFoundException();
        }

        return $emailAccount;
    }
}
