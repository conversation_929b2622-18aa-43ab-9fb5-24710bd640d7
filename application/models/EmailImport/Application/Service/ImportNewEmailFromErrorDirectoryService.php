<?php

namespace EmailImport\Application\Service;

use EmailImport\Domain\Model\EmailAccount\EmailAccount;
use EmailImport\Domain\Model\EmailAccount\EmailAccountNotFoundException;
use EmailImport\Domain\Model\MailBoxDirectories\MailBoxDirectoriesFactory;
use EmailImport\Domain\Model\NewEmail\NewEmailAlreadyExistException;
use EmailImport\Domain\Service\ImportNewEmailService;
use EmailImport\Domain\Service\MailStorageConnection;
use EmailImport\Infrastructure\Domain\Model\EmailAccount\ZendEmailAccountRepository;
use EmailImport\Infrastructure\Domain\Model\NewEmail\LegacyNewEmailFactory;
use Zend_Db_Statement_Exception;

class ImportNewEmailFromErrorDirectoryService
{
    private $emailAccountRepository;
    private $mailBoxDirectoriesFactory;
    private $newEmailFactory;
    private $importNewEmailService;
    private $mailStorageConnection;

    public function __construct(
        ZendEmailAccountRepository $emailAccountRepository,
        MailBoxDirectoriesFactory $mailBoxDirectoriesFactory,
        MailStorageConnection $mailStorageConnection,
        LegacyNewEmailFactory $newEmailFactory,
        ImportNewEmailService $importNewEmailService
    ) {
        $this->emailAccountRepository = $emailAccountRepository;
        $this->mailBoxDirectoriesFactory = $mailBoxDirectoriesFactory;
        $this->mailStorageConnection = $mailStorageConnection;
        $this->newEmailFactory = $newEmailFactory;
        $this->importNewEmailService = $importNewEmailService;
    }

    public function execute(ImportNewEmailFromErrorDirectoryRequest $request)
    {
        if ($request->getRunServiceTillDate() <= new \DateTime()) {
            throw new \RuntimeException('Run service till date expired');
        }

        $this->guardAgainstInvalidImportDatePeriod(
            $request->getImportMailFromDate(),
            $request->getImportMailTillDate()
        );

        $emailAccount = $this->findEmailAccountOrFail($request->getEmailAccountId());

        $mailBoxDirectories = $this->mailBoxDirectoriesFactory->build($emailAccount->getId());

        $this->mailStorageConnection->makeConnection($emailAccount, $mailBoxDirectories->getErrorDir());

        $emailMessagesIterator = $this->mailStorageConnection->getEmailMessagesIterator();

        while ($emailMessagesIterator->hasNextEmailMessage()) {
            $emailMessage = $emailMessagesIterator->getNextEmailMessage();
            $emailMessageId = $emailMessagesIterator->getCurrentEmailMessageId();

            try {
                $newEmail = $this->newEmailFactory->build($emailMessage, $emailAccount);

                $isSentDateBetweenImportDatePeriod = $this->isSentDateBetweenImportDatePeriod(
                    $newEmail->getSentDate(),
                    $request->getImportMailFromDate(),
                    $request->getImportMailTillDate()
                );

                if (!$isSentDateBetweenImportDatePeriod) {
                    continue;
                }

                $this->importNewEmailService->import($newEmail, $emailAccount);
            } catch (NewEmailAlreadyExistException $e) {
                continue;
            } catch (\Exception $e) {
                continue;
            }

            $this->mailStorageConnection->moveMessageToDirectory(
                $emailMessageId,
                $mailBoxDirectories->getImportedDir()
            );
        }
    }

    /**
     * @param $emailAccountId
     * @return EmailAccount
     * @throws EmailAccountNotFoundException
     * @throws Zend_Db_Statement_Exception
     */
    private function findEmailAccountOrFail($emailAccountId)
    {
        $emailAccount = $this->emailAccountRepository->ofId($emailAccountId);

        if (null === $emailAccount) {
            throw new EmailAccountNotFoundException();
        }

        return $emailAccount;
    }

    private function guardAgainstInvalidImportDatePeriod(\DateTime $importMailFromDate, \DateTime $importMailTillDate)
    {
        if ($importMailTillDate < $importMailFromDate) {
            throw new \InvalidArgumentException('Till date can not be less than the from date');
        }
    }

    private function isSentDateBetweenImportDatePeriod(
        \DateTime $sentDate,
        \DateTime $importMailFromDate,
        \DateTime $importMailTillDate
    ) {
        return $sentDate >= $importMailFromDate && $sentDate <= $importMailTillDate;
    }
}
