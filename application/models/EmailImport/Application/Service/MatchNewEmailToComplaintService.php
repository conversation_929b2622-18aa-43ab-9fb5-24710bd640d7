<?php

namespace EmailImport\Application\Service;

use SupportComplaints;

class MatchNewEmailToComplaintService
{
    public function execute(MatchNewEmailToComplaintRequest $request)
    {
        $emailRow = $this->findEmailRowOrFail($request->getEmailRowId());
        $complaintIdentifier = $this->searchSubjectForComplaintIdentifier($emailRow->subject);

        if (null === $complaintIdentifier) {
            return;
        }

        $supportComplaintRow = SupportComplaints::getByIdentifier($complaintIdentifier);

        if (null === $supportComplaintRow) {
            throw new \RuntimeException('Could not find complaint by identifier');
        }

        $emailRow->complaint = $supportComplaintRow->id;
        $emailRow->save();
    }

    /**
     * @param $emailRowId
     * @return \Zend_Db_Table_Row_Abstract
     */
    private function findEmailRowOrFail($emailRowId)
    {
        $emailRow = (new \Emails())->fetchRowById($emailRowId);

        if (null === $emailRow) {
            throw new \RuntimeException('Could not find email row');
        }

        return $emailRow;
    }

    /**
     * Look for [#12345] in the subject and return 12345
     * @param $subject
     * @return int|null
     */
    private function searchSubjectForComplaintIdentifier($subject)
    {
        $matches = [];
        if (preg_match('/\[#(\d{5})\]/', $subject, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
